import React from 'react';

interface DRDOLogoProps {
  className?: string;
  size?: number;
}

const DRDOLogo: React.FC<DRDOLogoProps> = ({ className = "", size = 24 }) => {
  return (
    <svg 
      width={size} 
      height={size} 
      viewBox="0 0 100 100" 
      className={className}
      fill="currentColor"
    >
      {/* DRDO Shield Design */}
      <defs>
        <linearGradient id="shieldGradient" x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" stopColor="currentColor" stopOpacity="1" />
          <stop offset="100%" stopColor="currentColor" stopOpacity="0.7" />
        </linearGradient>
      </defs>
      
      {/* Main Shield */}
      <path 
        d="M50 5 L15 20 L15 45 C15 70 30 85 50 95 C70 85 85 70 85 45 L85 20 Z" 
        fill="url(#shieldGradient)" 
        stroke="currentColor" 
        strokeWidth="2"
      />
      
      {/* Inner Design - Trident/Missile */}
      <path 
        d="M50 25 L50 75 M40 35 L50 25 L60 35 M45 45 L55 45 M42 55 L58 55" 
        stroke="white" 
        strokeWidth="3" 
        fill="none" 
        strokeLinecap="round"
      />
      
      {/* DRDO Text Representation */}
      <circle cx="50" cy="65" r="3" fill="white" />
      <circle cx="45" cy="70" r="2" fill="white" />
      <circle cx="55" cy="70" r="2" fill="white" />
    </svg>
  );
};

export default DRDOLogo;
