var zm=Object.defineProperty;var Bm=(t,e,n)=>e in t?zm(t,e,{enumerable:!0,configurable:!0,writable:!0,value:n}):t[e]=n;var D=(t,e,n)=>Bm(t,typeof e!="symbol"?e+"":e,n);function $m(t,e){for(var n=0;n<e.length;n++){const i=e[n];if(typeof i!="string"&&!Array.isArray(i)){for(const s in i)if(s!=="default"&&!(s in t)){const r=Object.getOwnPropertyDescriptor(i,s);r&&Object.defineProperty(t,s,r.get?r:{enumerable:!0,get:()=>i[s]})}}}return Object.freeze(Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}))}(function(){const e=document.createElement("link").relList;if(e&&e.supports&&e.supports("modulepreload"))return;for(const s of document.querySelectorAll('link[rel="modulepreload"]'))i(s);new MutationObserver(s=>{for(const r of s)if(r.type==="childList")for(const o of r.addedNodes)o.tagName==="LINK"&&o.rel==="modulepreload"&&i(o)}).observe(document,{childList:!0,subtree:!0});function n(s){const r={};return s.integrity&&(r.integrity=s.integrity),s.referrerPolicy&&(r.referrerPolicy=s.referrerPolicy),s.crossOrigin==="use-credentials"?r.credentials="include":s.crossOrigin==="anonymous"?r.credentials="omit":r.credentials="same-origin",r}function i(s){if(s.ep)return;s.ep=!0;const r=n(s);fetch(s.href,r)}})();function Vm(t){return t&&t.__esModule&&Object.prototype.hasOwnProperty.call(t,"default")?t.default:t}var gh={exports:{}},Fo={},mh={exports:{}},$={};/**
 * @license React
 * react.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Qs=Symbol.for("react.element"),Hm=Symbol.for("react.portal"),Wm=Symbol.for("react.fragment"),Um=Symbol.for("react.strict_mode"),Km=Symbol.for("react.profiler"),Ym=Symbol.for("react.provider"),Xm=Symbol.for("react.context"),Qm=Symbol.for("react.forward_ref"),qm=Symbol.for("react.suspense"),Gm=Symbol.for("react.memo"),Jm=Symbol.for("react.lazy"),xc=Symbol.iterator;function Zm(t){return t===null||typeof t!="object"?null:(t=xc&&t[xc]||t["@@iterator"],typeof t=="function"?t:null)}var yh={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},xh=Object.assign,vh={};function Ei(t,e,n){this.props=t,this.context=e,this.refs=vh,this.updater=n||yh}Ei.prototype.isReactComponent={};Ei.prototype.setState=function(t,e){if(typeof t!="object"&&typeof t!="function"&&t!=null)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,t,e,"setState")};Ei.prototype.forceUpdate=function(t){this.updater.enqueueForceUpdate(this,t,"forceUpdate")};function _h(){}_h.prototype=Ei.prototype;function ou(t,e,n){this.props=t,this.context=e,this.refs=vh,this.updater=n||yh}var au=ou.prototype=new _h;au.constructor=ou;xh(au,Ei.prototype);au.isPureReactComponent=!0;var vc=Array.isArray,wh=Object.prototype.hasOwnProperty,lu={current:null},Sh={key:!0,ref:!0,__self:!0,__source:!0};function bh(t,e,n){var i,s={},r=null,o=null;if(e!=null)for(i in e.ref!==void 0&&(o=e.ref),e.key!==void 0&&(r=""+e.key),e)wh.call(e,i)&&!Sh.hasOwnProperty(i)&&(s[i]=e[i]);var a=arguments.length-2;if(a===1)s.children=n;else if(1<a){for(var l=Array(a),u=0;u<a;u++)l[u]=arguments[u+2];s.children=l}if(t&&t.defaultProps)for(i in a=t.defaultProps,a)s[i]===void 0&&(s[i]=a[i]);return{$$typeof:Qs,type:t,key:r,ref:o,props:s,_owner:lu.current}}function ey(t,e){return{$$typeof:Qs,type:t.type,key:e,ref:t.ref,props:t.props,_owner:t._owner}}function uu(t){return typeof t=="object"&&t!==null&&t.$$typeof===Qs}function ty(t){var e={"=":"=0",":":"=2"};return"$"+t.replace(/[=:]/g,function(n){return e[n]})}var _c=/\/+/g;function ca(t,e){return typeof t=="object"&&t!==null&&t.key!=null?ty(""+t.key):e.toString(36)}function Ir(t,e,n,i,s){var r=typeof t;(r==="undefined"||r==="boolean")&&(t=null);var o=!1;if(t===null)o=!0;else switch(r){case"string":case"number":o=!0;break;case"object":switch(t.$$typeof){case Qs:case Hm:o=!0}}if(o)return o=t,s=s(o),t=i===""?"."+ca(o,0):i,vc(s)?(n="",t!=null&&(n=t.replace(_c,"$&/")+"/"),Ir(s,e,n,"",function(u){return u})):s!=null&&(uu(s)&&(s=ey(s,n+(!s.key||o&&o.key===s.key?"":(""+s.key).replace(_c,"$&/")+"/")+t)),e.push(s)),1;if(o=0,i=i===""?".":i+":",vc(t))for(var a=0;a<t.length;a++){r=t[a];var l=i+ca(r,a);o+=Ir(r,e,n,l,s)}else if(l=Zm(t),typeof l=="function")for(t=l.call(t),a=0;!(r=t.next()).done;)r=r.value,l=i+ca(r,a++),o+=Ir(r,e,n,l,s);else if(r==="object")throw e=String(t),Error("Objects are not valid as a React child (found: "+(e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e)+"). If you meant to render a collection of children, use an array instead.");return o}function rr(t,e,n){if(t==null)return t;var i=[],s=0;return Ir(t,i,"","",function(r){return e.call(n,r,s++)}),i}function ny(t){if(t._status===-1){var e=t._result;e=e(),e.then(function(n){(t._status===0||t._status===-1)&&(t._status=1,t._result=n)},function(n){(t._status===0||t._status===-1)&&(t._status=2,t._result=n)}),t._status===-1&&(t._status=0,t._result=e)}if(t._status===1)return t._result.default;throw t._result}var je={current:null},Fr={transition:null},iy={ReactCurrentDispatcher:je,ReactCurrentBatchConfig:Fr,ReactCurrentOwner:lu};function kh(){throw Error("act(...) is not supported in production builds of React.")}$.Children={map:rr,forEach:function(t,e,n){rr(t,function(){e.apply(this,arguments)},n)},count:function(t){var e=0;return rr(t,function(){e++}),e},toArray:function(t){return rr(t,function(e){return e})||[]},only:function(t){if(!uu(t))throw Error("React.Children.only expected to receive a single React element child.");return t}};$.Component=Ei;$.Fragment=Wm;$.Profiler=Km;$.PureComponent=ou;$.StrictMode=Um;$.Suspense=qm;$.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=iy;$.act=kh;$.cloneElement=function(t,e,n){if(t==null)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+t+".");var i=xh({},t.props),s=t.key,r=t.ref,o=t._owner;if(e!=null){if(e.ref!==void 0&&(r=e.ref,o=lu.current),e.key!==void 0&&(s=""+e.key),t.type&&t.type.defaultProps)var a=t.type.defaultProps;for(l in e)wh.call(e,l)&&!Sh.hasOwnProperty(l)&&(i[l]=e[l]===void 0&&a!==void 0?a[l]:e[l])}var l=arguments.length-2;if(l===1)i.children=n;else if(1<l){a=Array(l);for(var u=0;u<l;u++)a[u]=arguments[u+2];i.children=a}return{$$typeof:Qs,type:t.type,key:s,ref:r,props:i,_owner:o}};$.createContext=function(t){return t={$$typeof:Xm,_currentValue:t,_currentValue2:t,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null},t.Provider={$$typeof:Ym,_context:t},t.Consumer=t};$.createElement=bh;$.createFactory=function(t){var e=bh.bind(null,t);return e.type=t,e};$.createRef=function(){return{current:null}};$.forwardRef=function(t){return{$$typeof:Qm,render:t}};$.isValidElement=uu;$.lazy=function(t){return{$$typeof:Jm,_payload:{_status:-1,_result:t},_init:ny}};$.memo=function(t,e){return{$$typeof:Gm,type:t,compare:e===void 0?null:e}};$.startTransition=function(t){var e=Fr.transition;Fr.transition={};try{t()}finally{Fr.transition=e}};$.unstable_act=kh;$.useCallback=function(t,e){return je.current.useCallback(t,e)};$.useContext=function(t){return je.current.useContext(t)};$.useDebugValue=function(){};$.useDeferredValue=function(t){return je.current.useDeferredValue(t)};$.useEffect=function(t,e){return je.current.useEffect(t,e)};$.useId=function(){return je.current.useId()};$.useImperativeHandle=function(t,e,n){return je.current.useImperativeHandle(t,e,n)};$.useInsertionEffect=function(t,e){return je.current.useInsertionEffect(t,e)};$.useLayoutEffect=function(t,e){return je.current.useLayoutEffect(t,e)};$.useMemo=function(t,e){return je.current.useMemo(t,e)};$.useReducer=function(t,e,n){return je.current.useReducer(t,e,n)};$.useRef=function(t){return je.current.useRef(t)};$.useState=function(t){return je.current.useState(t)};$.useSyncExternalStore=function(t,e,n){return je.current.useSyncExternalStore(t,e,n)};$.useTransition=function(){return je.current.useTransition()};$.version="18.3.1";mh.exports=$;var O=mh.exports;const zo=Vm(O),sy=$m({__proto__:null,default:zo},[O]);/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var ry=O,oy=Symbol.for("react.element"),ay=Symbol.for("react.fragment"),ly=Object.prototype.hasOwnProperty,uy=ry.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,cy={key:!0,ref:!0,__self:!0,__source:!0};function Ch(t,e,n){var i,s={},r=null,o=null;n!==void 0&&(r=""+n),e.key!==void 0&&(r=""+e.key),e.ref!==void 0&&(o=e.ref);for(i in e)ly.call(e,i)&&!cy.hasOwnProperty(i)&&(s[i]=e[i]);if(t&&t.defaultProps)for(i in e=t.defaultProps,e)s[i]===void 0&&(s[i]=e[i]);return{$$typeof:oy,type:t,key:r,ref:o,props:s,_owner:uy.current}}Fo.Fragment=ay;Fo.jsx=Ch;Fo.jsxs=Ch;gh.exports=Fo;var _=gh.exports,qa={},Ph={exports:{}},Xe={},Mh={exports:{}},Eh={};/**
 * @license React
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */(function(t){function e(N,R){var j=N.length;N.push(R);e:for(;0<j;){var F=j-1>>>1,U=N[F];if(0<s(U,R))N[F]=R,N[j]=U,j=F;else break e}}function n(N){return N.length===0?null:N[0]}function i(N){if(N.length===0)return null;var R=N[0],j=N.pop();if(j!==R){N[0]=j;e:for(var F=0,U=N.length,be=U>>>1;F<be;){var ye=2*(F+1)-1,qe=N[ye],Te=ye+1,sr=N[Te];if(0>s(qe,j))Te<U&&0>s(sr,qe)?(N[F]=sr,N[Te]=j,F=Te):(N[F]=qe,N[ye]=j,F=ye);else if(Te<U&&0>s(sr,j))N[F]=sr,N[Te]=j,F=Te;else break e}}return R}function s(N,R){var j=N.sortIndex-R.sortIndex;return j!==0?j:N.id-R.id}if(typeof performance=="object"&&typeof performance.now=="function"){var r=performance;t.unstable_now=function(){return r.now()}}else{var o=Date,a=o.now();t.unstable_now=function(){return o.now()-a}}var l=[],u=[],c=1,d=null,f=3,h=!1,p=!1,y=!1,v=typeof setTimeout=="function"?setTimeout:null,m=typeof clearTimeout=="function"?clearTimeout:null,g=typeof setImmediate<"u"?setImmediate:null;typeof navigator<"u"&&navigator.scheduling!==void 0&&navigator.scheduling.isInputPending!==void 0&&navigator.scheduling.isInputPending.bind(navigator.scheduling);function x(N){for(var R=n(u);R!==null;){if(R.callback===null)i(u);else if(R.startTime<=N)i(u),R.sortIndex=R.expirationTime,e(l,R);else break;R=n(u)}}function w(N){if(y=!1,x(N),!p)if(n(l)!==null)p=!0,z(S);else{var R=n(u);R!==null&&V(w,R.startTime-N)}}function S(N,R){p=!1,y&&(y=!1,m(b),b=-1),h=!0;var j=f;try{for(x(R),d=n(l);d!==null&&(!(d.expirationTime>R)||N&&!T());){var F=d.callback;if(typeof F=="function"){d.callback=null,f=d.priorityLevel;var U=F(d.expirationTime<=R);R=t.unstable_now(),typeof U=="function"?d.callback=U:d===n(l)&&i(l),x(R)}else i(l);d=n(l)}if(d!==null)var be=!0;else{var ye=n(u);ye!==null&&V(w,ye.startTime-R),be=!1}return be}finally{d=null,f=j,h=!1}}var C=!1,k=null,b=-1,M=5,P=-1;function T(){return!(t.unstable_now()-P<M)}function I(){if(k!==null){var N=t.unstable_now();P=N;var R=!0;try{R=k(!0,N)}finally{R?H():(C=!1,k=null)}}else C=!1}var H;if(typeof g=="function")H=function(){g(I)};else if(typeof MessageChannel<"u"){var J=new MessageChannel,W=J.port2;J.port1.onmessage=I,H=function(){W.postMessage(null)}}else H=function(){v(I,0)};function z(N){k=N,C||(C=!0,H())}function V(N,R){b=v(function(){N(t.unstable_now())},R)}t.unstable_IdlePriority=5,t.unstable_ImmediatePriority=1,t.unstable_LowPriority=4,t.unstable_NormalPriority=3,t.unstable_Profiling=null,t.unstable_UserBlockingPriority=2,t.unstable_cancelCallback=function(N){N.callback=null},t.unstable_continueExecution=function(){p||h||(p=!0,z(S))},t.unstable_forceFrameRate=function(N){0>N||125<N?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):M=0<N?Math.floor(1e3/N):5},t.unstable_getCurrentPriorityLevel=function(){return f},t.unstable_getFirstCallbackNode=function(){return n(l)},t.unstable_next=function(N){switch(f){case 1:case 2:case 3:var R=3;break;default:R=f}var j=f;f=R;try{return N()}finally{f=j}},t.unstable_pauseExecution=function(){},t.unstable_requestPaint=function(){},t.unstable_runWithPriority=function(N,R){switch(N){case 1:case 2:case 3:case 4:case 5:break;default:N=3}var j=f;f=N;try{return R()}finally{f=j}},t.unstable_scheduleCallback=function(N,R,j){var F=t.unstable_now();switch(typeof j=="object"&&j!==null?(j=j.delay,j=typeof j=="number"&&0<j?F+j:F):j=F,N){case 1:var U=-1;break;case 2:U=250;break;case 5:U=**********;break;case 4:U=1e4;break;default:U=5e3}return U=j+U,N={id:c++,callback:R,priorityLevel:N,startTime:j,expirationTime:U,sortIndex:-1},j>F?(N.sortIndex=j,e(u,N),n(l)===null&&N===n(u)&&(y?(m(b),b=-1):y=!0,V(w,j-F))):(N.sortIndex=U,e(l,N),p||h||(p=!0,z(S))),N},t.unstable_shouldYield=T,t.unstable_wrapCallback=function(N){var R=f;return function(){var j=f;f=R;try{return N.apply(this,arguments)}finally{f=j}}}})(Eh);Mh.exports=Eh;var dy=Mh.exports;/**
 * @license React
 * react-dom.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var fy=O,Ye=dy;function E(t){for(var e="https://reactjs.org/docs/error-decoder.html?invariant="+t,n=1;n<arguments.length;n++)e+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+t+"; visit "+e+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var Oh=new Set,xs={};function Bn(t,e){xi(t,e),xi(t+"Capture",e)}function xi(t,e){for(xs[t]=e,t=0;t<e.length;t++)Oh.add(e[t])}var At=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),Ga=Object.prototype.hasOwnProperty,hy=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,wc={},Sc={};function py(t){return Ga.call(Sc,t)?!0:Ga.call(wc,t)?!1:hy.test(t)?Sc[t]=!0:(wc[t]=!0,!1)}function gy(t,e,n,i){if(n!==null&&n.type===0)return!1;switch(typeof e){case"function":case"symbol":return!0;case"boolean":return i?!1:n!==null?!n.acceptsBooleans:(t=t.toLowerCase().slice(0,5),t!=="data-"&&t!=="aria-");default:return!1}}function my(t,e,n,i){if(e===null||typeof e>"u"||gy(t,e,n,i))return!0;if(i)return!1;if(n!==null)switch(n.type){case 3:return!e;case 4:return e===!1;case 5:return isNaN(e);case 6:return isNaN(e)||1>e}return!1}function Ae(t,e,n,i,s,r,o){this.acceptsBooleans=e===2||e===3||e===4,this.attributeName=i,this.attributeNamespace=s,this.mustUseProperty=n,this.propertyName=t,this.type=e,this.sanitizeURL=r,this.removeEmptyString=o}var Se={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(t){Se[t]=new Ae(t,0,!1,t,null,!1,!1)});[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(t){var e=t[0];Se[e]=new Ae(e,1,!1,t[1],null,!1,!1)});["contentEditable","draggable","spellCheck","value"].forEach(function(t){Se[t]=new Ae(t,2,!1,t.toLowerCase(),null,!1,!1)});["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(t){Se[t]=new Ae(t,2,!1,t,null,!1,!1)});"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(t){Se[t]=new Ae(t,3,!1,t.toLowerCase(),null,!1,!1)});["checked","multiple","muted","selected"].forEach(function(t){Se[t]=new Ae(t,3,!0,t,null,!1,!1)});["capture","download"].forEach(function(t){Se[t]=new Ae(t,4,!1,t,null,!1,!1)});["cols","rows","size","span"].forEach(function(t){Se[t]=new Ae(t,6,!1,t,null,!1,!1)});["rowSpan","start"].forEach(function(t){Se[t]=new Ae(t,5,!1,t.toLowerCase(),null,!1,!1)});var cu=/[\-:]([a-z])/g;function du(t){return t[1].toUpperCase()}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(t){var e=t.replace(cu,du);Se[e]=new Ae(e,1,!1,t,null,!1,!1)});"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(t){var e=t.replace(cu,du);Se[e]=new Ae(e,1,!1,t,"http://www.w3.org/1999/xlink",!1,!1)});["xml:base","xml:lang","xml:space"].forEach(function(t){var e=t.replace(cu,du);Se[e]=new Ae(e,1,!1,t,"http://www.w3.org/XML/1998/namespace",!1,!1)});["tabIndex","crossOrigin"].forEach(function(t){Se[t]=new Ae(t,1,!1,t.toLowerCase(),null,!1,!1)});Se.xlinkHref=new Ae("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1);["src","href","action","formAction"].forEach(function(t){Se[t]=new Ae(t,1,!1,t.toLowerCase(),null,!0,!0)});function fu(t,e,n,i){var s=Se.hasOwnProperty(e)?Se[e]:null;(s!==null?s.type!==0:i||!(2<e.length)||e[0]!=="o"&&e[0]!=="O"||e[1]!=="n"&&e[1]!=="N")&&(my(e,n,s,i)&&(n=null),i||s===null?py(e)&&(n===null?t.removeAttribute(e):t.setAttribute(e,""+n)):s.mustUseProperty?t[s.propertyName]=n===null?s.type===3?!1:"":n:(e=s.attributeName,i=s.attributeNamespace,n===null?t.removeAttribute(e):(s=s.type,n=s===3||s===4&&n===!0?"":""+n,i?t.setAttributeNS(i,e,n):t.setAttribute(e,n))))}var Bt=fy.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,or=Symbol.for("react.element"),Jn=Symbol.for("react.portal"),Zn=Symbol.for("react.fragment"),hu=Symbol.for("react.strict_mode"),Ja=Symbol.for("react.profiler"),Nh=Symbol.for("react.provider"),Lh=Symbol.for("react.context"),pu=Symbol.for("react.forward_ref"),Za=Symbol.for("react.suspense"),el=Symbol.for("react.suspense_list"),gu=Symbol.for("react.memo"),Ht=Symbol.for("react.lazy"),Th=Symbol.for("react.offscreen"),bc=Symbol.iterator;function Ri(t){return t===null||typeof t!="object"?null:(t=bc&&t[bc]||t["@@iterator"],typeof t=="function"?t:null)}var oe=Object.assign,da;function Yi(t){if(da===void 0)try{throw Error()}catch(n){var e=n.stack.trim().match(/\n( *(at )?)/);da=e&&e[1]||""}return`
`+da+t}var fa=!1;function ha(t,e){if(!t||fa)return"";fa=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(e)if(e=function(){throw Error()},Object.defineProperty(e.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(e,[])}catch(u){var i=u}Reflect.construct(t,[],e)}else{try{e.call()}catch(u){i=u}t.call(e.prototype)}else{try{throw Error()}catch(u){i=u}t()}}catch(u){if(u&&i&&typeof u.stack=="string"){for(var s=u.stack.split(`
`),r=i.stack.split(`
`),o=s.length-1,a=r.length-1;1<=o&&0<=a&&s[o]!==r[a];)a--;for(;1<=o&&0<=a;o--,a--)if(s[o]!==r[a]){if(o!==1||a!==1)do if(o--,a--,0>a||s[o]!==r[a]){var l=`
`+s[o].replace(" at new "," at ");return t.displayName&&l.includes("<anonymous>")&&(l=l.replace("<anonymous>",t.displayName)),l}while(1<=o&&0<=a);break}}}finally{fa=!1,Error.prepareStackTrace=n}return(t=t?t.displayName||t.name:"")?Yi(t):""}function yy(t){switch(t.tag){case 5:return Yi(t.type);case 16:return Yi("Lazy");case 13:return Yi("Suspense");case 19:return Yi("SuspenseList");case 0:case 2:case 15:return t=ha(t.type,!1),t;case 11:return t=ha(t.type.render,!1),t;case 1:return t=ha(t.type,!0),t;default:return""}}function tl(t){if(t==null)return null;if(typeof t=="function")return t.displayName||t.name||null;if(typeof t=="string")return t;switch(t){case Zn:return"Fragment";case Jn:return"Portal";case Ja:return"Profiler";case hu:return"StrictMode";case Za:return"Suspense";case el:return"SuspenseList"}if(typeof t=="object")switch(t.$$typeof){case Lh:return(t.displayName||"Context")+".Consumer";case Nh:return(t._context.displayName||"Context")+".Provider";case pu:var e=t.render;return t=t.displayName,t||(t=e.displayName||e.name||"",t=t!==""?"ForwardRef("+t+")":"ForwardRef"),t;case gu:return e=t.displayName||null,e!==null?e:tl(t.type)||"Memo";case Ht:e=t._payload,t=t._init;try{return tl(t(e))}catch{}}return null}function xy(t){var e=t.type;switch(t.tag){case 24:return"Cache";case 9:return(e.displayName||"Context")+".Consumer";case 10:return(e._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return t=e.render,t=t.displayName||t.name||"",e.displayName||(t!==""?"ForwardRef("+t+")":"ForwardRef");case 7:return"Fragment";case 5:return e;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return tl(e);case 8:return e===hu?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if(typeof e=="function")return e.displayName||e.name||null;if(typeof e=="string")return e}return null}function cn(t){switch(typeof t){case"boolean":case"number":case"string":case"undefined":return t;case"object":return t;default:return""}}function Rh(t){var e=t.type;return(t=t.nodeName)&&t.toLowerCase()==="input"&&(e==="checkbox"||e==="radio")}function vy(t){var e=Rh(t)?"checked":"value",n=Object.getOwnPropertyDescriptor(t.constructor.prototype,e),i=""+t[e];if(!t.hasOwnProperty(e)&&typeof n<"u"&&typeof n.get=="function"&&typeof n.set=="function"){var s=n.get,r=n.set;return Object.defineProperty(t,e,{configurable:!0,get:function(){return s.call(this)},set:function(o){i=""+o,r.call(this,o)}}),Object.defineProperty(t,e,{enumerable:n.enumerable}),{getValue:function(){return i},setValue:function(o){i=""+o},stopTracking:function(){t._valueTracker=null,delete t[e]}}}}function ar(t){t._valueTracker||(t._valueTracker=vy(t))}function Dh(t){if(!t)return!1;var e=t._valueTracker;if(!e)return!0;var n=e.getValue(),i="";return t&&(i=Rh(t)?t.checked?"true":"false":t.value),t=i,t!==n?(e.setValue(t),!0):!1}function eo(t){if(t=t||(typeof document<"u"?document:void 0),typeof t>"u")return null;try{return t.activeElement||t.body}catch{return t.body}}function nl(t,e){var n=e.checked;return oe({},e,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:n??t._wrapperState.initialChecked})}function kc(t,e){var n=e.defaultValue==null?"":e.defaultValue,i=e.checked!=null?e.checked:e.defaultChecked;n=cn(e.value!=null?e.value:n),t._wrapperState={initialChecked:i,initialValue:n,controlled:e.type==="checkbox"||e.type==="radio"?e.checked!=null:e.value!=null}}function jh(t,e){e=e.checked,e!=null&&fu(t,"checked",e,!1)}function il(t,e){jh(t,e);var n=cn(e.value),i=e.type;if(n!=null)i==="number"?(n===0&&t.value===""||t.value!=n)&&(t.value=""+n):t.value!==""+n&&(t.value=""+n);else if(i==="submit"||i==="reset"){t.removeAttribute("value");return}e.hasOwnProperty("value")?sl(t,e.type,n):e.hasOwnProperty("defaultValue")&&sl(t,e.type,cn(e.defaultValue)),e.checked==null&&e.defaultChecked!=null&&(t.defaultChecked=!!e.defaultChecked)}function Cc(t,e,n){if(e.hasOwnProperty("value")||e.hasOwnProperty("defaultValue")){var i=e.type;if(!(i!=="submit"&&i!=="reset"||e.value!==void 0&&e.value!==null))return;e=""+t._wrapperState.initialValue,n||e===t.value||(t.value=e),t.defaultValue=e}n=t.name,n!==""&&(t.name=""),t.defaultChecked=!!t._wrapperState.initialChecked,n!==""&&(t.name=n)}function sl(t,e,n){(e!=="number"||eo(t.ownerDocument)!==t)&&(n==null?t.defaultValue=""+t._wrapperState.initialValue:t.defaultValue!==""+n&&(t.defaultValue=""+n))}var Xi=Array.isArray;function ci(t,e,n,i){if(t=t.options,e){e={};for(var s=0;s<n.length;s++)e["$"+n[s]]=!0;for(n=0;n<t.length;n++)s=e.hasOwnProperty("$"+t[n].value),t[n].selected!==s&&(t[n].selected=s),s&&i&&(t[n].defaultSelected=!0)}else{for(n=""+cn(n),e=null,s=0;s<t.length;s++){if(t[s].value===n){t[s].selected=!0,i&&(t[s].defaultSelected=!0);return}e!==null||t[s].disabled||(e=t[s])}e!==null&&(e.selected=!0)}}function rl(t,e){if(e.dangerouslySetInnerHTML!=null)throw Error(E(91));return oe({},e,{value:void 0,defaultValue:void 0,children:""+t._wrapperState.initialValue})}function Pc(t,e){var n=e.value;if(n==null){if(n=e.children,e=e.defaultValue,n!=null){if(e!=null)throw Error(E(92));if(Xi(n)){if(1<n.length)throw Error(E(93));n=n[0]}e=n}e==null&&(e=""),n=e}t._wrapperState={initialValue:cn(n)}}function Ah(t,e){var n=cn(e.value),i=cn(e.defaultValue);n!=null&&(n=""+n,n!==t.value&&(t.value=n),e.defaultValue==null&&t.defaultValue!==n&&(t.defaultValue=n)),i!=null&&(t.defaultValue=""+i)}function Mc(t){var e=t.textContent;e===t._wrapperState.initialValue&&e!==""&&e!==null&&(t.value=e)}function Ih(t){switch(t){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function ol(t,e){return t==null||t==="http://www.w3.org/1999/xhtml"?Ih(e):t==="http://www.w3.org/2000/svg"&&e==="foreignObject"?"http://www.w3.org/1999/xhtml":t}var lr,Fh=function(t){return typeof MSApp<"u"&&MSApp.execUnsafeLocalFunction?function(e,n,i,s){MSApp.execUnsafeLocalFunction(function(){return t(e,n,i,s)})}:t}(function(t,e){if(t.namespaceURI!=="http://www.w3.org/2000/svg"||"innerHTML"in t)t.innerHTML=e;else{for(lr=lr||document.createElement("div"),lr.innerHTML="<svg>"+e.valueOf().toString()+"</svg>",e=lr.firstChild;t.firstChild;)t.removeChild(t.firstChild);for(;e.firstChild;)t.appendChild(e.firstChild)}});function vs(t,e){if(e){var n=t.firstChild;if(n&&n===t.lastChild&&n.nodeType===3){n.nodeValue=e;return}}t.textContent=e}var is={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},_y=["Webkit","ms","Moz","O"];Object.keys(is).forEach(function(t){_y.forEach(function(e){e=e+t.charAt(0).toUpperCase()+t.substring(1),is[e]=is[t]})});function zh(t,e,n){return e==null||typeof e=="boolean"||e===""?"":n||typeof e!="number"||e===0||is.hasOwnProperty(t)&&is[t]?(""+e).trim():e+"px"}function Bh(t,e){t=t.style;for(var n in e)if(e.hasOwnProperty(n)){var i=n.indexOf("--")===0,s=zh(n,e[n],i);n==="float"&&(n="cssFloat"),i?t.setProperty(n,s):t[n]=s}}var wy=oe({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function al(t,e){if(e){if(wy[t]&&(e.children!=null||e.dangerouslySetInnerHTML!=null))throw Error(E(137,t));if(e.dangerouslySetInnerHTML!=null){if(e.children!=null)throw Error(E(60));if(typeof e.dangerouslySetInnerHTML!="object"||!("__html"in e.dangerouslySetInnerHTML))throw Error(E(61))}if(e.style!=null&&typeof e.style!="object")throw Error(E(62))}}function ll(t,e){if(t.indexOf("-")===-1)return typeof e.is=="string";switch(t){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var ul=null;function mu(t){return t=t.target||t.srcElement||window,t.correspondingUseElement&&(t=t.correspondingUseElement),t.nodeType===3?t.parentNode:t}var cl=null,di=null,fi=null;function Ec(t){if(t=Js(t)){if(typeof cl!="function")throw Error(E(280));var e=t.stateNode;e&&(e=Wo(e),cl(t.stateNode,t.type,e))}}function $h(t){di?fi?fi.push(t):fi=[t]:di=t}function Vh(){if(di){var t=di,e=fi;if(fi=di=null,Ec(t),e)for(t=0;t<e.length;t++)Ec(e[t])}}function Hh(t,e){return t(e)}function Wh(){}var pa=!1;function Uh(t,e,n){if(pa)return t(e,n);pa=!0;try{return Hh(t,e,n)}finally{pa=!1,(di!==null||fi!==null)&&(Wh(),Vh())}}function _s(t,e){var n=t.stateNode;if(n===null)return null;var i=Wo(n);if(i===null)return null;n=i[e];e:switch(e){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(i=!i.disabled)||(t=t.type,i=!(t==="button"||t==="input"||t==="select"||t==="textarea")),t=!i;break e;default:t=!1}if(t)return null;if(n&&typeof n!="function")throw Error(E(231,e,typeof n));return n}var dl=!1;if(At)try{var Di={};Object.defineProperty(Di,"passive",{get:function(){dl=!0}}),window.addEventListener("test",Di,Di),window.removeEventListener("test",Di,Di)}catch{dl=!1}function Sy(t,e,n,i,s,r,o,a,l){var u=Array.prototype.slice.call(arguments,3);try{e.apply(n,u)}catch(c){this.onError(c)}}var ss=!1,to=null,no=!1,fl=null,by={onError:function(t){ss=!0,to=t}};function ky(t,e,n,i,s,r,o,a,l){ss=!1,to=null,Sy.apply(by,arguments)}function Cy(t,e,n,i,s,r,o,a,l){if(ky.apply(this,arguments),ss){if(ss){var u=to;ss=!1,to=null}else throw Error(E(198));no||(no=!0,fl=u)}}function $n(t){var e=t,n=t;if(t.alternate)for(;e.return;)e=e.return;else{t=e;do e=t,e.flags&4098&&(n=e.return),t=e.return;while(t)}return e.tag===3?n:null}function Kh(t){if(t.tag===13){var e=t.memoizedState;if(e===null&&(t=t.alternate,t!==null&&(e=t.memoizedState)),e!==null)return e.dehydrated}return null}function Oc(t){if($n(t)!==t)throw Error(E(188))}function Py(t){var e=t.alternate;if(!e){if(e=$n(t),e===null)throw Error(E(188));return e!==t?null:t}for(var n=t,i=e;;){var s=n.return;if(s===null)break;var r=s.alternate;if(r===null){if(i=s.return,i!==null){n=i;continue}break}if(s.child===r.child){for(r=s.child;r;){if(r===n)return Oc(s),t;if(r===i)return Oc(s),e;r=r.sibling}throw Error(E(188))}if(n.return!==i.return)n=s,i=r;else{for(var o=!1,a=s.child;a;){if(a===n){o=!0,n=s,i=r;break}if(a===i){o=!0,i=s,n=r;break}a=a.sibling}if(!o){for(a=r.child;a;){if(a===n){o=!0,n=r,i=s;break}if(a===i){o=!0,i=r,n=s;break}a=a.sibling}if(!o)throw Error(E(189))}}if(n.alternate!==i)throw Error(E(190))}if(n.tag!==3)throw Error(E(188));return n.stateNode.current===n?t:e}function Yh(t){return t=Py(t),t!==null?Xh(t):null}function Xh(t){if(t.tag===5||t.tag===6)return t;for(t=t.child;t!==null;){var e=Xh(t);if(e!==null)return e;t=t.sibling}return null}var Qh=Ye.unstable_scheduleCallback,Nc=Ye.unstable_cancelCallback,My=Ye.unstable_shouldYield,Ey=Ye.unstable_requestPaint,ce=Ye.unstable_now,Oy=Ye.unstable_getCurrentPriorityLevel,yu=Ye.unstable_ImmediatePriority,qh=Ye.unstable_UserBlockingPriority,io=Ye.unstable_NormalPriority,Ny=Ye.unstable_LowPriority,Gh=Ye.unstable_IdlePriority,Bo=null,bt=null;function Ly(t){if(bt&&typeof bt.onCommitFiberRoot=="function")try{bt.onCommitFiberRoot(Bo,t,void 0,(t.current.flags&128)===128)}catch{}}var ft=Math.clz32?Math.clz32:Dy,Ty=Math.log,Ry=Math.LN2;function Dy(t){return t>>>=0,t===0?32:31-(Ty(t)/Ry|0)|0}var ur=64,cr=4194304;function Qi(t){switch(t&-t){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t&4194240;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return t&130023424;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return t}}function so(t,e){var n=t.pendingLanes;if(n===0)return 0;var i=0,s=t.suspendedLanes,r=t.pingedLanes,o=n&268435455;if(o!==0){var a=o&~s;a!==0?i=Qi(a):(r&=o,r!==0&&(i=Qi(r)))}else o=n&~s,o!==0?i=Qi(o):r!==0&&(i=Qi(r));if(i===0)return 0;if(e!==0&&e!==i&&!(e&s)&&(s=i&-i,r=e&-e,s>=r||s===16&&(r&4194240)!==0))return e;if(i&4&&(i|=n&16),e=t.entangledLanes,e!==0)for(t=t.entanglements,e&=i;0<e;)n=31-ft(e),s=1<<n,i|=t[n],e&=~s;return i}function jy(t,e){switch(t){case 1:case 2:case 4:return e+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e+5e3;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return-1;case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function Ay(t,e){for(var n=t.suspendedLanes,i=t.pingedLanes,s=t.expirationTimes,r=t.pendingLanes;0<r;){var o=31-ft(r),a=1<<o,l=s[o];l===-1?(!(a&n)||a&i)&&(s[o]=jy(a,e)):l<=e&&(t.expiredLanes|=a),r&=~a}}function hl(t){return t=t.pendingLanes&-1073741825,t!==0?t:t&1073741824?1073741824:0}function Jh(){var t=ur;return ur<<=1,!(ur&4194240)&&(ur=64),t}function ga(t){for(var e=[],n=0;31>n;n++)e.push(t);return e}function qs(t,e,n){t.pendingLanes|=e,e!==536870912&&(t.suspendedLanes=0,t.pingedLanes=0),t=t.eventTimes,e=31-ft(e),t[e]=n}function Iy(t,e){var n=t.pendingLanes&~e;t.pendingLanes=e,t.suspendedLanes=0,t.pingedLanes=0,t.expiredLanes&=e,t.mutableReadLanes&=e,t.entangledLanes&=e,e=t.entanglements;var i=t.eventTimes;for(t=t.expirationTimes;0<n;){var s=31-ft(n),r=1<<s;e[s]=0,i[s]=-1,t[s]=-1,n&=~r}}function xu(t,e){var n=t.entangledLanes|=e;for(t=t.entanglements;n;){var i=31-ft(n),s=1<<i;s&e|t[i]&e&&(t[i]|=e),n&=~s}}var G=0;function Zh(t){return t&=-t,1<t?4<t?t&268435455?16:536870912:4:1}var ep,vu,tp,np,ip,pl=!1,dr=[],Zt=null,en=null,tn=null,ws=new Map,Ss=new Map,Ut=[],Fy="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function Lc(t,e){switch(t){case"focusin":case"focusout":Zt=null;break;case"dragenter":case"dragleave":en=null;break;case"mouseover":case"mouseout":tn=null;break;case"pointerover":case"pointerout":ws.delete(e.pointerId);break;case"gotpointercapture":case"lostpointercapture":Ss.delete(e.pointerId)}}function ji(t,e,n,i,s,r){return t===null||t.nativeEvent!==r?(t={blockedOn:e,domEventName:n,eventSystemFlags:i,nativeEvent:r,targetContainers:[s]},e!==null&&(e=Js(e),e!==null&&vu(e)),t):(t.eventSystemFlags|=i,e=t.targetContainers,s!==null&&e.indexOf(s)===-1&&e.push(s),t)}function zy(t,e,n,i,s){switch(e){case"focusin":return Zt=ji(Zt,t,e,n,i,s),!0;case"dragenter":return en=ji(en,t,e,n,i,s),!0;case"mouseover":return tn=ji(tn,t,e,n,i,s),!0;case"pointerover":var r=s.pointerId;return ws.set(r,ji(ws.get(r)||null,t,e,n,i,s)),!0;case"gotpointercapture":return r=s.pointerId,Ss.set(r,ji(Ss.get(r)||null,t,e,n,i,s)),!0}return!1}function sp(t){var e=kn(t.target);if(e!==null){var n=$n(e);if(n!==null){if(e=n.tag,e===13){if(e=Kh(n),e!==null){t.blockedOn=e,ip(t.priority,function(){tp(n)});return}}else if(e===3&&n.stateNode.current.memoizedState.isDehydrated){t.blockedOn=n.tag===3?n.stateNode.containerInfo:null;return}}}t.blockedOn=null}function zr(t){if(t.blockedOn!==null)return!1;for(var e=t.targetContainers;0<e.length;){var n=gl(t.domEventName,t.eventSystemFlags,e[0],t.nativeEvent);if(n===null){n=t.nativeEvent;var i=new n.constructor(n.type,n);ul=i,n.target.dispatchEvent(i),ul=null}else return e=Js(n),e!==null&&vu(e),t.blockedOn=n,!1;e.shift()}return!0}function Tc(t,e,n){zr(t)&&n.delete(e)}function By(){pl=!1,Zt!==null&&zr(Zt)&&(Zt=null),en!==null&&zr(en)&&(en=null),tn!==null&&zr(tn)&&(tn=null),ws.forEach(Tc),Ss.forEach(Tc)}function Ai(t,e){t.blockedOn===e&&(t.blockedOn=null,pl||(pl=!0,Ye.unstable_scheduleCallback(Ye.unstable_NormalPriority,By)))}function bs(t){function e(s){return Ai(s,t)}if(0<dr.length){Ai(dr[0],t);for(var n=1;n<dr.length;n++){var i=dr[n];i.blockedOn===t&&(i.blockedOn=null)}}for(Zt!==null&&Ai(Zt,t),en!==null&&Ai(en,t),tn!==null&&Ai(tn,t),ws.forEach(e),Ss.forEach(e),n=0;n<Ut.length;n++)i=Ut[n],i.blockedOn===t&&(i.blockedOn=null);for(;0<Ut.length&&(n=Ut[0],n.blockedOn===null);)sp(n),n.blockedOn===null&&Ut.shift()}var hi=Bt.ReactCurrentBatchConfig,ro=!0;function $y(t,e,n,i){var s=G,r=hi.transition;hi.transition=null;try{G=1,_u(t,e,n,i)}finally{G=s,hi.transition=r}}function Vy(t,e,n,i){var s=G,r=hi.transition;hi.transition=null;try{G=4,_u(t,e,n,i)}finally{G=s,hi.transition=r}}function _u(t,e,n,i){if(ro){var s=gl(t,e,n,i);if(s===null)Ca(t,e,i,oo,n),Lc(t,i);else if(zy(s,t,e,n,i))i.stopPropagation();else if(Lc(t,i),e&4&&-1<Fy.indexOf(t)){for(;s!==null;){var r=Js(s);if(r!==null&&ep(r),r=gl(t,e,n,i),r===null&&Ca(t,e,i,oo,n),r===s)break;s=r}s!==null&&i.stopPropagation()}else Ca(t,e,i,null,n)}}var oo=null;function gl(t,e,n,i){if(oo=null,t=mu(i),t=kn(t),t!==null)if(e=$n(t),e===null)t=null;else if(n=e.tag,n===13){if(t=Kh(e),t!==null)return t;t=null}else if(n===3){if(e.stateNode.current.memoizedState.isDehydrated)return e.tag===3?e.stateNode.containerInfo:null;t=null}else e!==t&&(t=null);return oo=t,null}function rp(t){switch(t){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(Oy()){case yu:return 1;case qh:return 4;case io:case Ny:return 16;case Gh:return 536870912;default:return 16}default:return 16}}var Yt=null,wu=null,Br=null;function op(){if(Br)return Br;var t,e=wu,n=e.length,i,s="value"in Yt?Yt.value:Yt.textContent,r=s.length;for(t=0;t<n&&e[t]===s[t];t++);var o=n-t;for(i=1;i<=o&&e[n-i]===s[r-i];i++);return Br=s.slice(t,1<i?1-i:void 0)}function $r(t){var e=t.keyCode;return"charCode"in t?(t=t.charCode,t===0&&e===13&&(t=13)):t=e,t===10&&(t=13),32<=t||t===13?t:0}function fr(){return!0}function Rc(){return!1}function Qe(t){function e(n,i,s,r,o){this._reactName=n,this._targetInst=s,this.type=i,this.nativeEvent=r,this.target=o,this.currentTarget=null;for(var a in t)t.hasOwnProperty(a)&&(n=t[a],this[a]=n?n(r):r[a]);return this.isDefaultPrevented=(r.defaultPrevented!=null?r.defaultPrevented:r.returnValue===!1)?fr:Rc,this.isPropagationStopped=Rc,this}return oe(e.prototype,{preventDefault:function(){this.defaultPrevented=!0;var n=this.nativeEvent;n&&(n.preventDefault?n.preventDefault():typeof n.returnValue!="unknown"&&(n.returnValue=!1),this.isDefaultPrevented=fr)},stopPropagation:function(){var n=this.nativeEvent;n&&(n.stopPropagation?n.stopPropagation():typeof n.cancelBubble!="unknown"&&(n.cancelBubble=!0),this.isPropagationStopped=fr)},persist:function(){},isPersistent:fr}),e}var Oi={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(t){return t.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},Su=Qe(Oi),Gs=oe({},Oi,{view:0,detail:0}),Hy=Qe(Gs),ma,ya,Ii,$o=oe({},Gs,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:bu,button:0,buttons:0,relatedTarget:function(t){return t.relatedTarget===void 0?t.fromElement===t.srcElement?t.toElement:t.fromElement:t.relatedTarget},movementX:function(t){return"movementX"in t?t.movementX:(t!==Ii&&(Ii&&t.type==="mousemove"?(ma=t.screenX-Ii.screenX,ya=t.screenY-Ii.screenY):ya=ma=0,Ii=t),ma)},movementY:function(t){return"movementY"in t?t.movementY:ya}}),Dc=Qe($o),Wy=oe({},$o,{dataTransfer:0}),Uy=Qe(Wy),Ky=oe({},Gs,{relatedTarget:0}),xa=Qe(Ky),Yy=oe({},Oi,{animationName:0,elapsedTime:0,pseudoElement:0}),Xy=Qe(Yy),Qy=oe({},Oi,{clipboardData:function(t){return"clipboardData"in t?t.clipboardData:window.clipboardData}}),qy=Qe(Qy),Gy=oe({},Oi,{data:0}),jc=Qe(Gy),Jy={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},Zy={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},ex={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function tx(t){var e=this.nativeEvent;return e.getModifierState?e.getModifierState(t):(t=ex[t])?!!e[t]:!1}function bu(){return tx}var nx=oe({},Gs,{key:function(t){if(t.key){var e=Jy[t.key]||t.key;if(e!=="Unidentified")return e}return t.type==="keypress"?(t=$r(t),t===13?"Enter":String.fromCharCode(t)):t.type==="keydown"||t.type==="keyup"?Zy[t.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:bu,charCode:function(t){return t.type==="keypress"?$r(t):0},keyCode:function(t){return t.type==="keydown"||t.type==="keyup"?t.keyCode:0},which:function(t){return t.type==="keypress"?$r(t):t.type==="keydown"||t.type==="keyup"?t.keyCode:0}}),ix=Qe(nx),sx=oe({},$o,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),Ac=Qe(sx),rx=oe({},Gs,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:bu}),ox=Qe(rx),ax=oe({},Oi,{propertyName:0,elapsedTime:0,pseudoElement:0}),lx=Qe(ax),ux=oe({},$o,{deltaX:function(t){return"deltaX"in t?t.deltaX:"wheelDeltaX"in t?-t.wheelDeltaX:0},deltaY:function(t){return"deltaY"in t?t.deltaY:"wheelDeltaY"in t?-t.wheelDeltaY:"wheelDelta"in t?-t.wheelDelta:0},deltaZ:0,deltaMode:0}),cx=Qe(ux),dx=[9,13,27,32],ku=At&&"CompositionEvent"in window,rs=null;At&&"documentMode"in document&&(rs=document.documentMode);var fx=At&&"TextEvent"in window&&!rs,ap=At&&(!ku||rs&&8<rs&&11>=rs),Ic=" ",Fc=!1;function lp(t,e){switch(t){case"keyup":return dx.indexOf(e.keyCode)!==-1;case"keydown":return e.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function up(t){return t=t.detail,typeof t=="object"&&"data"in t?t.data:null}var ei=!1;function hx(t,e){switch(t){case"compositionend":return up(e);case"keypress":return e.which!==32?null:(Fc=!0,Ic);case"textInput":return t=e.data,t===Ic&&Fc?null:t;default:return null}}function px(t,e){if(ei)return t==="compositionend"||!ku&&lp(t,e)?(t=op(),Br=wu=Yt=null,ei=!1,t):null;switch(t){case"paste":return null;case"keypress":if(!(e.ctrlKey||e.altKey||e.metaKey)||e.ctrlKey&&e.altKey){if(e.char&&1<e.char.length)return e.char;if(e.which)return String.fromCharCode(e.which)}return null;case"compositionend":return ap&&e.locale!=="ko"?null:e.data;default:return null}}var gx={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function zc(t){var e=t&&t.nodeName&&t.nodeName.toLowerCase();return e==="input"?!!gx[t.type]:e==="textarea"}function cp(t,e,n,i){$h(i),e=ao(e,"onChange"),0<e.length&&(n=new Su("onChange","change",null,n,i),t.push({event:n,listeners:e}))}var os=null,ks=null;function mx(t){wp(t,0)}function Vo(t){var e=ii(t);if(Dh(e))return t}function yx(t,e){if(t==="change")return e}var dp=!1;if(At){var va;if(At){var _a="oninput"in document;if(!_a){var Bc=document.createElement("div");Bc.setAttribute("oninput","return;"),_a=typeof Bc.oninput=="function"}va=_a}else va=!1;dp=va&&(!document.documentMode||9<document.documentMode)}function $c(){os&&(os.detachEvent("onpropertychange",fp),ks=os=null)}function fp(t){if(t.propertyName==="value"&&Vo(ks)){var e=[];cp(e,ks,t,mu(t)),Uh(mx,e)}}function xx(t,e,n){t==="focusin"?($c(),os=e,ks=n,os.attachEvent("onpropertychange",fp)):t==="focusout"&&$c()}function vx(t){if(t==="selectionchange"||t==="keyup"||t==="keydown")return Vo(ks)}function _x(t,e){if(t==="click")return Vo(e)}function wx(t,e){if(t==="input"||t==="change")return Vo(e)}function Sx(t,e){return t===e&&(t!==0||1/t===1/e)||t!==t&&e!==e}var pt=typeof Object.is=="function"?Object.is:Sx;function Cs(t,e){if(pt(t,e))return!0;if(typeof t!="object"||t===null||typeof e!="object"||e===null)return!1;var n=Object.keys(t),i=Object.keys(e);if(n.length!==i.length)return!1;for(i=0;i<n.length;i++){var s=n[i];if(!Ga.call(e,s)||!pt(t[s],e[s]))return!1}return!0}function Vc(t){for(;t&&t.firstChild;)t=t.firstChild;return t}function Hc(t,e){var n=Vc(t);t=0;for(var i;n;){if(n.nodeType===3){if(i=t+n.textContent.length,t<=e&&i>=e)return{node:n,offset:e-t};t=i}e:{for(;n;){if(n.nextSibling){n=n.nextSibling;break e}n=n.parentNode}n=void 0}n=Vc(n)}}function hp(t,e){return t&&e?t===e?!0:t&&t.nodeType===3?!1:e&&e.nodeType===3?hp(t,e.parentNode):"contains"in t?t.contains(e):t.compareDocumentPosition?!!(t.compareDocumentPosition(e)&16):!1:!1}function pp(){for(var t=window,e=eo();e instanceof t.HTMLIFrameElement;){try{var n=typeof e.contentWindow.location.href=="string"}catch{n=!1}if(n)t=e.contentWindow;else break;e=eo(t.document)}return e}function Cu(t){var e=t&&t.nodeName&&t.nodeName.toLowerCase();return e&&(e==="input"&&(t.type==="text"||t.type==="search"||t.type==="tel"||t.type==="url"||t.type==="password")||e==="textarea"||t.contentEditable==="true")}function bx(t){var e=pp(),n=t.focusedElem,i=t.selectionRange;if(e!==n&&n&&n.ownerDocument&&hp(n.ownerDocument.documentElement,n)){if(i!==null&&Cu(n)){if(e=i.start,t=i.end,t===void 0&&(t=e),"selectionStart"in n)n.selectionStart=e,n.selectionEnd=Math.min(t,n.value.length);else if(t=(e=n.ownerDocument||document)&&e.defaultView||window,t.getSelection){t=t.getSelection();var s=n.textContent.length,r=Math.min(i.start,s);i=i.end===void 0?r:Math.min(i.end,s),!t.extend&&r>i&&(s=i,i=r,r=s),s=Hc(n,r);var o=Hc(n,i);s&&o&&(t.rangeCount!==1||t.anchorNode!==s.node||t.anchorOffset!==s.offset||t.focusNode!==o.node||t.focusOffset!==o.offset)&&(e=e.createRange(),e.setStart(s.node,s.offset),t.removeAllRanges(),r>i?(t.addRange(e),t.extend(o.node,o.offset)):(e.setEnd(o.node,o.offset),t.addRange(e)))}}for(e=[],t=n;t=t.parentNode;)t.nodeType===1&&e.push({element:t,left:t.scrollLeft,top:t.scrollTop});for(typeof n.focus=="function"&&n.focus(),n=0;n<e.length;n++)t=e[n],t.element.scrollLeft=t.left,t.element.scrollTop=t.top}}var kx=At&&"documentMode"in document&&11>=document.documentMode,ti=null,ml=null,as=null,yl=!1;function Wc(t,e,n){var i=n.window===n?n.document:n.nodeType===9?n:n.ownerDocument;yl||ti==null||ti!==eo(i)||(i=ti,"selectionStart"in i&&Cu(i)?i={start:i.selectionStart,end:i.selectionEnd}:(i=(i.ownerDocument&&i.ownerDocument.defaultView||window).getSelection(),i={anchorNode:i.anchorNode,anchorOffset:i.anchorOffset,focusNode:i.focusNode,focusOffset:i.focusOffset}),as&&Cs(as,i)||(as=i,i=ao(ml,"onSelect"),0<i.length&&(e=new Su("onSelect","select",null,e,n),t.push({event:e,listeners:i}),e.target=ti)))}function hr(t,e){var n={};return n[t.toLowerCase()]=e.toLowerCase(),n["Webkit"+t]="webkit"+e,n["Moz"+t]="moz"+e,n}var ni={animationend:hr("Animation","AnimationEnd"),animationiteration:hr("Animation","AnimationIteration"),animationstart:hr("Animation","AnimationStart"),transitionend:hr("Transition","TransitionEnd")},wa={},gp={};At&&(gp=document.createElement("div").style,"AnimationEvent"in window||(delete ni.animationend.animation,delete ni.animationiteration.animation,delete ni.animationstart.animation),"TransitionEvent"in window||delete ni.transitionend.transition);function Ho(t){if(wa[t])return wa[t];if(!ni[t])return t;var e=ni[t],n;for(n in e)if(e.hasOwnProperty(n)&&n in gp)return wa[t]=e[n];return t}var mp=Ho("animationend"),yp=Ho("animationiteration"),xp=Ho("animationstart"),vp=Ho("transitionend"),_p=new Map,Uc="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function hn(t,e){_p.set(t,e),Bn(e,[t])}for(var Sa=0;Sa<Uc.length;Sa++){var ba=Uc[Sa],Cx=ba.toLowerCase(),Px=ba[0].toUpperCase()+ba.slice(1);hn(Cx,"on"+Px)}hn(mp,"onAnimationEnd");hn(yp,"onAnimationIteration");hn(xp,"onAnimationStart");hn("dblclick","onDoubleClick");hn("focusin","onFocus");hn("focusout","onBlur");hn(vp,"onTransitionEnd");xi("onMouseEnter",["mouseout","mouseover"]);xi("onMouseLeave",["mouseout","mouseover"]);xi("onPointerEnter",["pointerout","pointerover"]);xi("onPointerLeave",["pointerout","pointerover"]);Bn("onChange","change click focusin focusout input keydown keyup selectionchange".split(" "));Bn("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" "));Bn("onBeforeInput",["compositionend","keypress","textInput","paste"]);Bn("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" "));Bn("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" "));Bn("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var qi="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),Mx=new Set("cancel close invalid load scroll toggle".split(" ").concat(qi));function Kc(t,e,n){var i=t.type||"unknown-event";t.currentTarget=n,Cy(i,e,void 0,t),t.currentTarget=null}function wp(t,e){e=(e&4)!==0;for(var n=0;n<t.length;n++){var i=t[n],s=i.event;i=i.listeners;e:{var r=void 0;if(e)for(var o=i.length-1;0<=o;o--){var a=i[o],l=a.instance,u=a.currentTarget;if(a=a.listener,l!==r&&s.isPropagationStopped())break e;Kc(s,a,u),r=l}else for(o=0;o<i.length;o++){if(a=i[o],l=a.instance,u=a.currentTarget,a=a.listener,l!==r&&s.isPropagationStopped())break e;Kc(s,a,u),r=l}}}if(no)throw t=fl,no=!1,fl=null,t}function ee(t,e){var n=e[Sl];n===void 0&&(n=e[Sl]=new Set);var i=t+"__bubble";n.has(i)||(Sp(e,t,2,!1),n.add(i))}function ka(t,e,n){var i=0;e&&(i|=4),Sp(n,t,i,e)}var pr="_reactListening"+Math.random().toString(36).slice(2);function Ps(t){if(!t[pr]){t[pr]=!0,Oh.forEach(function(n){n!=="selectionchange"&&(Mx.has(n)||ka(n,!1,t),ka(n,!0,t))});var e=t.nodeType===9?t:t.ownerDocument;e===null||e[pr]||(e[pr]=!0,ka("selectionchange",!1,e))}}function Sp(t,e,n,i){switch(rp(e)){case 1:var s=$y;break;case 4:s=Vy;break;default:s=_u}n=s.bind(null,e,n,t),s=void 0,!dl||e!=="touchstart"&&e!=="touchmove"&&e!=="wheel"||(s=!0),i?s!==void 0?t.addEventListener(e,n,{capture:!0,passive:s}):t.addEventListener(e,n,!0):s!==void 0?t.addEventListener(e,n,{passive:s}):t.addEventListener(e,n,!1)}function Ca(t,e,n,i,s){var r=i;if(!(e&1)&&!(e&2)&&i!==null)e:for(;;){if(i===null)return;var o=i.tag;if(o===3||o===4){var a=i.stateNode.containerInfo;if(a===s||a.nodeType===8&&a.parentNode===s)break;if(o===4)for(o=i.return;o!==null;){var l=o.tag;if((l===3||l===4)&&(l=o.stateNode.containerInfo,l===s||l.nodeType===8&&l.parentNode===s))return;o=o.return}for(;a!==null;){if(o=kn(a),o===null)return;if(l=o.tag,l===5||l===6){i=r=o;continue e}a=a.parentNode}}i=i.return}Uh(function(){var u=r,c=mu(n),d=[];e:{var f=_p.get(t);if(f!==void 0){var h=Su,p=t;switch(t){case"keypress":if($r(n)===0)break e;case"keydown":case"keyup":h=ix;break;case"focusin":p="focus",h=xa;break;case"focusout":p="blur",h=xa;break;case"beforeblur":case"afterblur":h=xa;break;case"click":if(n.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":h=Dc;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":h=Uy;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":h=ox;break;case mp:case yp:case xp:h=Xy;break;case vp:h=lx;break;case"scroll":h=Hy;break;case"wheel":h=cx;break;case"copy":case"cut":case"paste":h=qy;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":h=Ac}var y=(e&4)!==0,v=!y&&t==="scroll",m=y?f!==null?f+"Capture":null:f;y=[];for(var g=u,x;g!==null;){x=g;var w=x.stateNode;if(x.tag===5&&w!==null&&(x=w,m!==null&&(w=_s(g,m),w!=null&&y.push(Ms(g,w,x)))),v)break;g=g.return}0<y.length&&(f=new h(f,p,null,n,c),d.push({event:f,listeners:y}))}}if(!(e&7)){e:{if(f=t==="mouseover"||t==="pointerover",h=t==="mouseout"||t==="pointerout",f&&n!==ul&&(p=n.relatedTarget||n.fromElement)&&(kn(p)||p[It]))break e;if((h||f)&&(f=c.window===c?c:(f=c.ownerDocument)?f.defaultView||f.parentWindow:window,h?(p=n.relatedTarget||n.toElement,h=u,p=p?kn(p):null,p!==null&&(v=$n(p),p!==v||p.tag!==5&&p.tag!==6)&&(p=null)):(h=null,p=u),h!==p)){if(y=Dc,w="onMouseLeave",m="onMouseEnter",g="mouse",(t==="pointerout"||t==="pointerover")&&(y=Ac,w="onPointerLeave",m="onPointerEnter",g="pointer"),v=h==null?f:ii(h),x=p==null?f:ii(p),f=new y(w,g+"leave",h,n,c),f.target=v,f.relatedTarget=x,w=null,kn(c)===u&&(y=new y(m,g+"enter",p,n,c),y.target=x,y.relatedTarget=v,w=y),v=w,h&&p)t:{for(y=h,m=p,g=0,x=y;x;x=Un(x))g++;for(x=0,w=m;w;w=Un(w))x++;for(;0<g-x;)y=Un(y),g--;for(;0<x-g;)m=Un(m),x--;for(;g--;){if(y===m||m!==null&&y===m.alternate)break t;y=Un(y),m=Un(m)}y=null}else y=null;h!==null&&Yc(d,f,h,y,!1),p!==null&&v!==null&&Yc(d,v,p,y,!0)}}e:{if(f=u?ii(u):window,h=f.nodeName&&f.nodeName.toLowerCase(),h==="select"||h==="input"&&f.type==="file")var S=yx;else if(zc(f))if(dp)S=wx;else{S=vx;var C=xx}else(h=f.nodeName)&&h.toLowerCase()==="input"&&(f.type==="checkbox"||f.type==="radio")&&(S=_x);if(S&&(S=S(t,u))){cp(d,S,n,c);break e}C&&C(t,f,u),t==="focusout"&&(C=f._wrapperState)&&C.controlled&&f.type==="number"&&sl(f,"number",f.value)}switch(C=u?ii(u):window,t){case"focusin":(zc(C)||C.contentEditable==="true")&&(ti=C,ml=u,as=null);break;case"focusout":as=ml=ti=null;break;case"mousedown":yl=!0;break;case"contextmenu":case"mouseup":case"dragend":yl=!1,Wc(d,n,c);break;case"selectionchange":if(kx)break;case"keydown":case"keyup":Wc(d,n,c)}var k;if(ku)e:{switch(t){case"compositionstart":var b="onCompositionStart";break e;case"compositionend":b="onCompositionEnd";break e;case"compositionupdate":b="onCompositionUpdate";break e}b=void 0}else ei?lp(t,n)&&(b="onCompositionEnd"):t==="keydown"&&n.keyCode===229&&(b="onCompositionStart");b&&(ap&&n.locale!=="ko"&&(ei||b!=="onCompositionStart"?b==="onCompositionEnd"&&ei&&(k=op()):(Yt=c,wu="value"in Yt?Yt.value:Yt.textContent,ei=!0)),C=ao(u,b),0<C.length&&(b=new jc(b,t,null,n,c),d.push({event:b,listeners:C}),k?b.data=k:(k=up(n),k!==null&&(b.data=k)))),(k=fx?hx(t,n):px(t,n))&&(u=ao(u,"onBeforeInput"),0<u.length&&(c=new jc("onBeforeInput","beforeinput",null,n,c),d.push({event:c,listeners:u}),c.data=k))}wp(d,e)})}function Ms(t,e,n){return{instance:t,listener:e,currentTarget:n}}function ao(t,e){for(var n=e+"Capture",i=[];t!==null;){var s=t,r=s.stateNode;s.tag===5&&r!==null&&(s=r,r=_s(t,n),r!=null&&i.unshift(Ms(t,r,s)),r=_s(t,e),r!=null&&i.push(Ms(t,r,s))),t=t.return}return i}function Un(t){if(t===null)return null;do t=t.return;while(t&&t.tag!==5);return t||null}function Yc(t,e,n,i,s){for(var r=e._reactName,o=[];n!==null&&n!==i;){var a=n,l=a.alternate,u=a.stateNode;if(l!==null&&l===i)break;a.tag===5&&u!==null&&(a=u,s?(l=_s(n,r),l!=null&&o.unshift(Ms(n,l,a))):s||(l=_s(n,r),l!=null&&o.push(Ms(n,l,a)))),n=n.return}o.length!==0&&t.push({event:e,listeners:o})}var Ex=/\r\n?/g,Ox=/\u0000|\uFFFD/g;function Xc(t){return(typeof t=="string"?t:""+t).replace(Ex,`
`).replace(Ox,"")}function gr(t,e,n){if(e=Xc(e),Xc(t)!==e&&n)throw Error(E(425))}function lo(){}var xl=null,vl=null;function _l(t,e){return t==="textarea"||t==="noscript"||typeof e.children=="string"||typeof e.children=="number"||typeof e.dangerouslySetInnerHTML=="object"&&e.dangerouslySetInnerHTML!==null&&e.dangerouslySetInnerHTML.__html!=null}var wl=typeof setTimeout=="function"?setTimeout:void 0,Nx=typeof clearTimeout=="function"?clearTimeout:void 0,Qc=typeof Promise=="function"?Promise:void 0,Lx=typeof queueMicrotask=="function"?queueMicrotask:typeof Qc<"u"?function(t){return Qc.resolve(null).then(t).catch(Tx)}:wl;function Tx(t){setTimeout(function(){throw t})}function Pa(t,e){var n=e,i=0;do{var s=n.nextSibling;if(t.removeChild(n),s&&s.nodeType===8)if(n=s.data,n==="/$"){if(i===0){t.removeChild(s),bs(e);return}i--}else n!=="$"&&n!=="$?"&&n!=="$!"||i++;n=s}while(n);bs(e)}function nn(t){for(;t!=null;t=t.nextSibling){var e=t.nodeType;if(e===1||e===3)break;if(e===8){if(e=t.data,e==="$"||e==="$!"||e==="$?")break;if(e==="/$")return null}}return t}function qc(t){t=t.previousSibling;for(var e=0;t;){if(t.nodeType===8){var n=t.data;if(n==="$"||n==="$!"||n==="$?"){if(e===0)return t;e--}else n==="/$"&&e++}t=t.previousSibling}return null}var Ni=Math.random().toString(36).slice(2),wt="__reactFiber$"+Ni,Es="__reactProps$"+Ni,It="__reactContainer$"+Ni,Sl="__reactEvents$"+Ni,Rx="__reactListeners$"+Ni,Dx="__reactHandles$"+Ni;function kn(t){var e=t[wt];if(e)return e;for(var n=t.parentNode;n;){if(e=n[It]||n[wt]){if(n=e.alternate,e.child!==null||n!==null&&n.child!==null)for(t=qc(t);t!==null;){if(n=t[wt])return n;t=qc(t)}return e}t=n,n=t.parentNode}return null}function Js(t){return t=t[wt]||t[It],!t||t.tag!==5&&t.tag!==6&&t.tag!==13&&t.tag!==3?null:t}function ii(t){if(t.tag===5||t.tag===6)return t.stateNode;throw Error(E(33))}function Wo(t){return t[Es]||null}var bl=[],si=-1;function pn(t){return{current:t}}function ne(t){0>si||(t.current=bl[si],bl[si]=null,si--)}function Z(t,e){si++,bl[si]=t.current,t.current=e}var dn={},Ne=pn(dn),$e=pn(!1),Rn=dn;function vi(t,e){var n=t.type.contextTypes;if(!n)return dn;var i=t.stateNode;if(i&&i.__reactInternalMemoizedUnmaskedChildContext===e)return i.__reactInternalMemoizedMaskedChildContext;var s={},r;for(r in n)s[r]=e[r];return i&&(t=t.stateNode,t.__reactInternalMemoizedUnmaskedChildContext=e,t.__reactInternalMemoizedMaskedChildContext=s),s}function Ve(t){return t=t.childContextTypes,t!=null}function uo(){ne($e),ne(Ne)}function Gc(t,e,n){if(Ne.current!==dn)throw Error(E(168));Z(Ne,e),Z($e,n)}function bp(t,e,n){var i=t.stateNode;if(e=e.childContextTypes,typeof i.getChildContext!="function")return n;i=i.getChildContext();for(var s in i)if(!(s in e))throw Error(E(108,xy(t)||"Unknown",s));return oe({},n,i)}function co(t){return t=(t=t.stateNode)&&t.__reactInternalMemoizedMergedChildContext||dn,Rn=Ne.current,Z(Ne,t),Z($e,$e.current),!0}function Jc(t,e,n){var i=t.stateNode;if(!i)throw Error(E(169));n?(t=bp(t,e,Rn),i.__reactInternalMemoizedMergedChildContext=t,ne($e),ne(Ne),Z(Ne,t)):ne($e),Z($e,n)}var Nt=null,Uo=!1,Ma=!1;function kp(t){Nt===null?Nt=[t]:Nt.push(t)}function jx(t){Uo=!0,kp(t)}function gn(){if(!Ma&&Nt!==null){Ma=!0;var t=0,e=G;try{var n=Nt;for(G=1;t<n.length;t++){var i=n[t];do i=i(!0);while(i!==null)}Nt=null,Uo=!1}catch(s){throw Nt!==null&&(Nt=Nt.slice(t+1)),Qh(yu,gn),s}finally{G=e,Ma=!1}}return null}var ri=[],oi=0,fo=null,ho=0,Je=[],Ze=0,Dn=null,Tt=1,Rt="";function _n(t,e){ri[oi++]=ho,ri[oi++]=fo,fo=t,ho=e}function Cp(t,e,n){Je[Ze++]=Tt,Je[Ze++]=Rt,Je[Ze++]=Dn,Dn=t;var i=Tt;t=Rt;var s=32-ft(i)-1;i&=~(1<<s),n+=1;var r=32-ft(e)+s;if(30<r){var o=s-s%5;r=(i&(1<<o)-1).toString(32),i>>=o,s-=o,Tt=1<<32-ft(e)+s|n<<s|i,Rt=r+t}else Tt=1<<r|n<<s|i,Rt=t}function Pu(t){t.return!==null&&(_n(t,1),Cp(t,1,0))}function Mu(t){for(;t===fo;)fo=ri[--oi],ri[oi]=null,ho=ri[--oi],ri[oi]=null;for(;t===Dn;)Dn=Je[--Ze],Je[Ze]=null,Rt=Je[--Ze],Je[Ze]=null,Tt=Je[--Ze],Je[Ze]=null}var Ke=null,Ue=null,ie=!1,dt=null;function Pp(t,e){var n=et(5,null,null,0);n.elementType="DELETED",n.stateNode=e,n.return=t,e=t.deletions,e===null?(t.deletions=[n],t.flags|=16):e.push(n)}function Zc(t,e){switch(t.tag){case 5:var n=t.type;return e=e.nodeType!==1||n.toLowerCase()!==e.nodeName.toLowerCase()?null:e,e!==null?(t.stateNode=e,Ke=t,Ue=nn(e.firstChild),!0):!1;case 6:return e=t.pendingProps===""||e.nodeType!==3?null:e,e!==null?(t.stateNode=e,Ke=t,Ue=null,!0):!1;case 13:return e=e.nodeType!==8?null:e,e!==null?(n=Dn!==null?{id:Tt,overflow:Rt}:null,t.memoizedState={dehydrated:e,treeContext:n,retryLane:1073741824},n=et(18,null,null,0),n.stateNode=e,n.return=t,t.child=n,Ke=t,Ue=null,!0):!1;default:return!1}}function kl(t){return(t.mode&1)!==0&&(t.flags&128)===0}function Cl(t){if(ie){var e=Ue;if(e){var n=e;if(!Zc(t,e)){if(kl(t))throw Error(E(418));e=nn(n.nextSibling);var i=Ke;e&&Zc(t,e)?Pp(i,n):(t.flags=t.flags&-4097|2,ie=!1,Ke=t)}}else{if(kl(t))throw Error(E(418));t.flags=t.flags&-4097|2,ie=!1,Ke=t}}}function ed(t){for(t=t.return;t!==null&&t.tag!==5&&t.tag!==3&&t.tag!==13;)t=t.return;Ke=t}function mr(t){if(t!==Ke)return!1;if(!ie)return ed(t),ie=!0,!1;var e;if((e=t.tag!==3)&&!(e=t.tag!==5)&&(e=t.type,e=e!=="head"&&e!=="body"&&!_l(t.type,t.memoizedProps)),e&&(e=Ue)){if(kl(t))throw Mp(),Error(E(418));for(;e;)Pp(t,e),e=nn(e.nextSibling)}if(ed(t),t.tag===13){if(t=t.memoizedState,t=t!==null?t.dehydrated:null,!t)throw Error(E(317));e:{for(t=t.nextSibling,e=0;t;){if(t.nodeType===8){var n=t.data;if(n==="/$"){if(e===0){Ue=nn(t.nextSibling);break e}e--}else n!=="$"&&n!=="$!"&&n!=="$?"||e++}t=t.nextSibling}Ue=null}}else Ue=Ke?nn(t.stateNode.nextSibling):null;return!0}function Mp(){for(var t=Ue;t;)t=nn(t.nextSibling)}function _i(){Ue=Ke=null,ie=!1}function Eu(t){dt===null?dt=[t]:dt.push(t)}var Ax=Bt.ReactCurrentBatchConfig;function Fi(t,e,n){if(t=n.ref,t!==null&&typeof t!="function"&&typeof t!="object"){if(n._owner){if(n=n._owner,n){if(n.tag!==1)throw Error(E(309));var i=n.stateNode}if(!i)throw Error(E(147,t));var s=i,r=""+t;return e!==null&&e.ref!==null&&typeof e.ref=="function"&&e.ref._stringRef===r?e.ref:(e=function(o){var a=s.refs;o===null?delete a[r]:a[r]=o},e._stringRef=r,e)}if(typeof t!="string")throw Error(E(284));if(!n._owner)throw Error(E(290,t))}return t}function yr(t,e){throw t=Object.prototype.toString.call(e),Error(E(31,t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t))}function td(t){var e=t._init;return e(t._payload)}function Ep(t){function e(m,g){if(t){var x=m.deletions;x===null?(m.deletions=[g],m.flags|=16):x.push(g)}}function n(m,g){if(!t)return null;for(;g!==null;)e(m,g),g=g.sibling;return null}function i(m,g){for(m=new Map;g!==null;)g.key!==null?m.set(g.key,g):m.set(g.index,g),g=g.sibling;return m}function s(m,g){return m=an(m,g),m.index=0,m.sibling=null,m}function r(m,g,x){return m.index=x,t?(x=m.alternate,x!==null?(x=x.index,x<g?(m.flags|=2,g):x):(m.flags|=2,g)):(m.flags|=1048576,g)}function o(m){return t&&m.alternate===null&&(m.flags|=2),m}function a(m,g,x,w){return g===null||g.tag!==6?(g=Da(x,m.mode,w),g.return=m,g):(g=s(g,x),g.return=m,g)}function l(m,g,x,w){var S=x.type;return S===Zn?c(m,g,x.props.children,w,x.key):g!==null&&(g.elementType===S||typeof S=="object"&&S!==null&&S.$$typeof===Ht&&td(S)===g.type)?(w=s(g,x.props),w.ref=Fi(m,g,x),w.return=m,w):(w=Xr(x.type,x.key,x.props,null,m.mode,w),w.ref=Fi(m,g,x),w.return=m,w)}function u(m,g,x,w){return g===null||g.tag!==4||g.stateNode.containerInfo!==x.containerInfo||g.stateNode.implementation!==x.implementation?(g=ja(x,m.mode,w),g.return=m,g):(g=s(g,x.children||[]),g.return=m,g)}function c(m,g,x,w,S){return g===null||g.tag!==7?(g=On(x,m.mode,w,S),g.return=m,g):(g=s(g,x),g.return=m,g)}function d(m,g,x){if(typeof g=="string"&&g!==""||typeof g=="number")return g=Da(""+g,m.mode,x),g.return=m,g;if(typeof g=="object"&&g!==null){switch(g.$$typeof){case or:return x=Xr(g.type,g.key,g.props,null,m.mode,x),x.ref=Fi(m,null,g),x.return=m,x;case Jn:return g=ja(g,m.mode,x),g.return=m,g;case Ht:var w=g._init;return d(m,w(g._payload),x)}if(Xi(g)||Ri(g))return g=On(g,m.mode,x,null),g.return=m,g;yr(m,g)}return null}function f(m,g,x,w){var S=g!==null?g.key:null;if(typeof x=="string"&&x!==""||typeof x=="number")return S!==null?null:a(m,g,""+x,w);if(typeof x=="object"&&x!==null){switch(x.$$typeof){case or:return x.key===S?l(m,g,x,w):null;case Jn:return x.key===S?u(m,g,x,w):null;case Ht:return S=x._init,f(m,g,S(x._payload),w)}if(Xi(x)||Ri(x))return S!==null?null:c(m,g,x,w,null);yr(m,x)}return null}function h(m,g,x,w,S){if(typeof w=="string"&&w!==""||typeof w=="number")return m=m.get(x)||null,a(g,m,""+w,S);if(typeof w=="object"&&w!==null){switch(w.$$typeof){case or:return m=m.get(w.key===null?x:w.key)||null,l(g,m,w,S);case Jn:return m=m.get(w.key===null?x:w.key)||null,u(g,m,w,S);case Ht:var C=w._init;return h(m,g,x,C(w._payload),S)}if(Xi(w)||Ri(w))return m=m.get(x)||null,c(g,m,w,S,null);yr(g,w)}return null}function p(m,g,x,w){for(var S=null,C=null,k=g,b=g=0,M=null;k!==null&&b<x.length;b++){k.index>b?(M=k,k=null):M=k.sibling;var P=f(m,k,x[b],w);if(P===null){k===null&&(k=M);break}t&&k&&P.alternate===null&&e(m,k),g=r(P,g,b),C===null?S=P:C.sibling=P,C=P,k=M}if(b===x.length)return n(m,k),ie&&_n(m,b),S;if(k===null){for(;b<x.length;b++)k=d(m,x[b],w),k!==null&&(g=r(k,g,b),C===null?S=k:C.sibling=k,C=k);return ie&&_n(m,b),S}for(k=i(m,k);b<x.length;b++)M=h(k,m,b,x[b],w),M!==null&&(t&&M.alternate!==null&&k.delete(M.key===null?b:M.key),g=r(M,g,b),C===null?S=M:C.sibling=M,C=M);return t&&k.forEach(function(T){return e(m,T)}),ie&&_n(m,b),S}function y(m,g,x,w){var S=Ri(x);if(typeof S!="function")throw Error(E(150));if(x=S.call(x),x==null)throw Error(E(151));for(var C=S=null,k=g,b=g=0,M=null,P=x.next();k!==null&&!P.done;b++,P=x.next()){k.index>b?(M=k,k=null):M=k.sibling;var T=f(m,k,P.value,w);if(T===null){k===null&&(k=M);break}t&&k&&T.alternate===null&&e(m,k),g=r(T,g,b),C===null?S=T:C.sibling=T,C=T,k=M}if(P.done)return n(m,k),ie&&_n(m,b),S;if(k===null){for(;!P.done;b++,P=x.next())P=d(m,P.value,w),P!==null&&(g=r(P,g,b),C===null?S=P:C.sibling=P,C=P);return ie&&_n(m,b),S}for(k=i(m,k);!P.done;b++,P=x.next())P=h(k,m,b,P.value,w),P!==null&&(t&&P.alternate!==null&&k.delete(P.key===null?b:P.key),g=r(P,g,b),C===null?S=P:C.sibling=P,C=P);return t&&k.forEach(function(I){return e(m,I)}),ie&&_n(m,b),S}function v(m,g,x,w){if(typeof x=="object"&&x!==null&&x.type===Zn&&x.key===null&&(x=x.props.children),typeof x=="object"&&x!==null){switch(x.$$typeof){case or:e:{for(var S=x.key,C=g;C!==null;){if(C.key===S){if(S=x.type,S===Zn){if(C.tag===7){n(m,C.sibling),g=s(C,x.props.children),g.return=m,m=g;break e}}else if(C.elementType===S||typeof S=="object"&&S!==null&&S.$$typeof===Ht&&td(S)===C.type){n(m,C.sibling),g=s(C,x.props),g.ref=Fi(m,C,x),g.return=m,m=g;break e}n(m,C);break}else e(m,C);C=C.sibling}x.type===Zn?(g=On(x.props.children,m.mode,w,x.key),g.return=m,m=g):(w=Xr(x.type,x.key,x.props,null,m.mode,w),w.ref=Fi(m,g,x),w.return=m,m=w)}return o(m);case Jn:e:{for(C=x.key;g!==null;){if(g.key===C)if(g.tag===4&&g.stateNode.containerInfo===x.containerInfo&&g.stateNode.implementation===x.implementation){n(m,g.sibling),g=s(g,x.children||[]),g.return=m,m=g;break e}else{n(m,g);break}else e(m,g);g=g.sibling}g=ja(x,m.mode,w),g.return=m,m=g}return o(m);case Ht:return C=x._init,v(m,g,C(x._payload),w)}if(Xi(x))return p(m,g,x,w);if(Ri(x))return y(m,g,x,w);yr(m,x)}return typeof x=="string"&&x!==""||typeof x=="number"?(x=""+x,g!==null&&g.tag===6?(n(m,g.sibling),g=s(g,x),g.return=m,m=g):(n(m,g),g=Da(x,m.mode,w),g.return=m,m=g),o(m)):n(m,g)}return v}var wi=Ep(!0),Op=Ep(!1),po=pn(null),go=null,ai=null,Ou=null;function Nu(){Ou=ai=go=null}function Lu(t){var e=po.current;ne(po),t._currentValue=e}function Pl(t,e,n){for(;t!==null;){var i=t.alternate;if((t.childLanes&e)!==e?(t.childLanes|=e,i!==null&&(i.childLanes|=e)):i!==null&&(i.childLanes&e)!==e&&(i.childLanes|=e),t===n)break;t=t.return}}function pi(t,e){go=t,Ou=ai=null,t=t.dependencies,t!==null&&t.firstContext!==null&&(t.lanes&e&&(Be=!0),t.firstContext=null)}function it(t){var e=t._currentValue;if(Ou!==t)if(t={context:t,memoizedValue:e,next:null},ai===null){if(go===null)throw Error(E(308));ai=t,go.dependencies={lanes:0,firstContext:t}}else ai=ai.next=t;return e}var Cn=null;function Tu(t){Cn===null?Cn=[t]:Cn.push(t)}function Np(t,e,n,i){var s=e.interleaved;return s===null?(n.next=n,Tu(e)):(n.next=s.next,s.next=n),e.interleaved=n,Ft(t,i)}function Ft(t,e){t.lanes|=e;var n=t.alternate;for(n!==null&&(n.lanes|=e),n=t,t=t.return;t!==null;)t.childLanes|=e,n=t.alternate,n!==null&&(n.childLanes|=e),n=t,t=t.return;return n.tag===3?n.stateNode:null}var Wt=!1;function Ru(t){t.updateQueue={baseState:t.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function Lp(t,e){t=t.updateQueue,e.updateQueue===t&&(e.updateQueue={baseState:t.baseState,firstBaseUpdate:t.firstBaseUpdate,lastBaseUpdate:t.lastBaseUpdate,shared:t.shared,effects:t.effects})}function jt(t,e){return{eventTime:t,lane:e,tag:0,payload:null,callback:null,next:null}}function sn(t,e,n){var i=t.updateQueue;if(i===null)return null;if(i=i.shared,K&2){var s=i.pending;return s===null?e.next=e:(e.next=s.next,s.next=e),i.pending=e,Ft(t,n)}return s=i.interleaved,s===null?(e.next=e,Tu(i)):(e.next=s.next,s.next=e),i.interleaved=e,Ft(t,n)}function Vr(t,e,n){if(e=e.updateQueue,e!==null&&(e=e.shared,(n&4194240)!==0)){var i=e.lanes;i&=t.pendingLanes,n|=i,e.lanes=n,xu(t,n)}}function nd(t,e){var n=t.updateQueue,i=t.alternate;if(i!==null&&(i=i.updateQueue,n===i)){var s=null,r=null;if(n=n.firstBaseUpdate,n!==null){do{var o={eventTime:n.eventTime,lane:n.lane,tag:n.tag,payload:n.payload,callback:n.callback,next:null};r===null?s=r=o:r=r.next=o,n=n.next}while(n!==null);r===null?s=r=e:r=r.next=e}else s=r=e;n={baseState:i.baseState,firstBaseUpdate:s,lastBaseUpdate:r,shared:i.shared,effects:i.effects},t.updateQueue=n;return}t=n.lastBaseUpdate,t===null?n.firstBaseUpdate=e:t.next=e,n.lastBaseUpdate=e}function mo(t,e,n,i){var s=t.updateQueue;Wt=!1;var r=s.firstBaseUpdate,o=s.lastBaseUpdate,a=s.shared.pending;if(a!==null){s.shared.pending=null;var l=a,u=l.next;l.next=null,o===null?r=u:o.next=u,o=l;var c=t.alternate;c!==null&&(c=c.updateQueue,a=c.lastBaseUpdate,a!==o&&(a===null?c.firstBaseUpdate=u:a.next=u,c.lastBaseUpdate=l))}if(r!==null){var d=s.baseState;o=0,c=u=l=null,a=r;do{var f=a.lane,h=a.eventTime;if((i&f)===f){c!==null&&(c=c.next={eventTime:h,lane:0,tag:a.tag,payload:a.payload,callback:a.callback,next:null});e:{var p=t,y=a;switch(f=e,h=n,y.tag){case 1:if(p=y.payload,typeof p=="function"){d=p.call(h,d,f);break e}d=p;break e;case 3:p.flags=p.flags&-65537|128;case 0:if(p=y.payload,f=typeof p=="function"?p.call(h,d,f):p,f==null)break e;d=oe({},d,f);break e;case 2:Wt=!0}}a.callback!==null&&a.lane!==0&&(t.flags|=64,f=s.effects,f===null?s.effects=[a]:f.push(a))}else h={eventTime:h,lane:f,tag:a.tag,payload:a.payload,callback:a.callback,next:null},c===null?(u=c=h,l=d):c=c.next=h,o|=f;if(a=a.next,a===null){if(a=s.shared.pending,a===null)break;f=a,a=f.next,f.next=null,s.lastBaseUpdate=f,s.shared.pending=null}}while(!0);if(c===null&&(l=d),s.baseState=l,s.firstBaseUpdate=u,s.lastBaseUpdate=c,e=s.shared.interleaved,e!==null){s=e;do o|=s.lane,s=s.next;while(s!==e)}else r===null&&(s.shared.lanes=0);An|=o,t.lanes=o,t.memoizedState=d}}function id(t,e,n){if(t=e.effects,e.effects=null,t!==null)for(e=0;e<t.length;e++){var i=t[e],s=i.callback;if(s!==null){if(i.callback=null,i=n,typeof s!="function")throw Error(E(191,s));s.call(i)}}}var Zs={},kt=pn(Zs),Os=pn(Zs),Ns=pn(Zs);function Pn(t){if(t===Zs)throw Error(E(174));return t}function Du(t,e){switch(Z(Ns,e),Z(Os,t),Z(kt,Zs),t=e.nodeType,t){case 9:case 11:e=(e=e.documentElement)?e.namespaceURI:ol(null,"");break;default:t=t===8?e.parentNode:e,e=t.namespaceURI||null,t=t.tagName,e=ol(e,t)}ne(kt),Z(kt,e)}function Si(){ne(kt),ne(Os),ne(Ns)}function Tp(t){Pn(Ns.current);var e=Pn(kt.current),n=ol(e,t.type);e!==n&&(Z(Os,t),Z(kt,n))}function ju(t){Os.current===t&&(ne(kt),ne(Os))}var se=pn(0);function yo(t){for(var e=t;e!==null;){if(e.tag===13){var n=e.memoizedState;if(n!==null&&(n=n.dehydrated,n===null||n.data==="$?"||n.data==="$!"))return e}else if(e.tag===19&&e.memoizedProps.revealOrder!==void 0){if(e.flags&128)return e}else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break;for(;e.sibling===null;){if(e.return===null||e.return===t)return null;e=e.return}e.sibling.return=e.return,e=e.sibling}return null}var Ea=[];function Au(){for(var t=0;t<Ea.length;t++)Ea[t]._workInProgressVersionPrimary=null;Ea.length=0}var Hr=Bt.ReactCurrentDispatcher,Oa=Bt.ReactCurrentBatchConfig,jn=0,re=null,he=null,xe=null,xo=!1,ls=!1,Ls=0,Ix=0;function ke(){throw Error(E(321))}function Iu(t,e){if(e===null)return!1;for(var n=0;n<e.length&&n<t.length;n++)if(!pt(t[n],e[n]))return!1;return!0}function Fu(t,e,n,i,s,r){if(jn=r,re=e,e.memoizedState=null,e.updateQueue=null,e.lanes=0,Hr.current=t===null||t.memoizedState===null?$x:Vx,t=n(i,s),ls){r=0;do{if(ls=!1,Ls=0,25<=r)throw Error(E(301));r+=1,xe=he=null,e.updateQueue=null,Hr.current=Hx,t=n(i,s)}while(ls)}if(Hr.current=vo,e=he!==null&&he.next!==null,jn=0,xe=he=re=null,xo=!1,e)throw Error(E(300));return t}function zu(){var t=Ls!==0;return Ls=0,t}function vt(){var t={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return xe===null?re.memoizedState=xe=t:xe=xe.next=t,xe}function st(){if(he===null){var t=re.alternate;t=t!==null?t.memoizedState:null}else t=he.next;var e=xe===null?re.memoizedState:xe.next;if(e!==null)xe=e,he=t;else{if(t===null)throw Error(E(310));he=t,t={memoizedState:he.memoizedState,baseState:he.baseState,baseQueue:he.baseQueue,queue:he.queue,next:null},xe===null?re.memoizedState=xe=t:xe=xe.next=t}return xe}function Ts(t,e){return typeof e=="function"?e(t):e}function Na(t){var e=st(),n=e.queue;if(n===null)throw Error(E(311));n.lastRenderedReducer=t;var i=he,s=i.baseQueue,r=n.pending;if(r!==null){if(s!==null){var o=s.next;s.next=r.next,r.next=o}i.baseQueue=s=r,n.pending=null}if(s!==null){r=s.next,i=i.baseState;var a=o=null,l=null,u=r;do{var c=u.lane;if((jn&c)===c)l!==null&&(l=l.next={lane:0,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null}),i=u.hasEagerState?u.eagerState:t(i,u.action);else{var d={lane:c,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null};l===null?(a=l=d,o=i):l=l.next=d,re.lanes|=c,An|=c}u=u.next}while(u!==null&&u!==r);l===null?o=i:l.next=a,pt(i,e.memoizedState)||(Be=!0),e.memoizedState=i,e.baseState=o,e.baseQueue=l,n.lastRenderedState=i}if(t=n.interleaved,t!==null){s=t;do r=s.lane,re.lanes|=r,An|=r,s=s.next;while(s!==t)}else s===null&&(n.lanes=0);return[e.memoizedState,n.dispatch]}function La(t){var e=st(),n=e.queue;if(n===null)throw Error(E(311));n.lastRenderedReducer=t;var i=n.dispatch,s=n.pending,r=e.memoizedState;if(s!==null){n.pending=null;var o=s=s.next;do r=t(r,o.action),o=o.next;while(o!==s);pt(r,e.memoizedState)||(Be=!0),e.memoizedState=r,e.baseQueue===null&&(e.baseState=r),n.lastRenderedState=r}return[r,i]}function Rp(){}function Dp(t,e){var n=re,i=st(),s=e(),r=!pt(i.memoizedState,s);if(r&&(i.memoizedState=s,Be=!0),i=i.queue,Bu(Ip.bind(null,n,i,t),[t]),i.getSnapshot!==e||r||xe!==null&&xe.memoizedState.tag&1){if(n.flags|=2048,Rs(9,Ap.bind(null,n,i,s,e),void 0,null),ve===null)throw Error(E(349));jn&30||jp(n,e,s)}return s}function jp(t,e,n){t.flags|=16384,t={getSnapshot:e,value:n},e=re.updateQueue,e===null?(e={lastEffect:null,stores:null},re.updateQueue=e,e.stores=[t]):(n=e.stores,n===null?e.stores=[t]:n.push(t))}function Ap(t,e,n,i){e.value=n,e.getSnapshot=i,Fp(e)&&zp(t)}function Ip(t,e,n){return n(function(){Fp(e)&&zp(t)})}function Fp(t){var e=t.getSnapshot;t=t.value;try{var n=e();return!pt(t,n)}catch{return!0}}function zp(t){var e=Ft(t,1);e!==null&&ht(e,t,1,-1)}function sd(t){var e=vt();return typeof t=="function"&&(t=t()),e.memoizedState=e.baseState=t,t={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:Ts,lastRenderedState:t},e.queue=t,t=t.dispatch=Bx.bind(null,re,t),[e.memoizedState,t]}function Rs(t,e,n,i){return t={tag:t,create:e,destroy:n,deps:i,next:null},e=re.updateQueue,e===null?(e={lastEffect:null,stores:null},re.updateQueue=e,e.lastEffect=t.next=t):(n=e.lastEffect,n===null?e.lastEffect=t.next=t:(i=n.next,n.next=t,t.next=i,e.lastEffect=t)),t}function Bp(){return st().memoizedState}function Wr(t,e,n,i){var s=vt();re.flags|=t,s.memoizedState=Rs(1|e,n,void 0,i===void 0?null:i)}function Ko(t,e,n,i){var s=st();i=i===void 0?null:i;var r=void 0;if(he!==null){var o=he.memoizedState;if(r=o.destroy,i!==null&&Iu(i,o.deps)){s.memoizedState=Rs(e,n,r,i);return}}re.flags|=t,s.memoizedState=Rs(1|e,n,r,i)}function rd(t,e){return Wr(8390656,8,t,e)}function Bu(t,e){return Ko(2048,8,t,e)}function $p(t,e){return Ko(4,2,t,e)}function Vp(t,e){return Ko(4,4,t,e)}function Hp(t,e){if(typeof e=="function")return t=t(),e(t),function(){e(null)};if(e!=null)return t=t(),e.current=t,function(){e.current=null}}function Wp(t,e,n){return n=n!=null?n.concat([t]):null,Ko(4,4,Hp.bind(null,e,t),n)}function $u(){}function Up(t,e){var n=st();e=e===void 0?null:e;var i=n.memoizedState;return i!==null&&e!==null&&Iu(e,i[1])?i[0]:(n.memoizedState=[t,e],t)}function Kp(t,e){var n=st();e=e===void 0?null:e;var i=n.memoizedState;return i!==null&&e!==null&&Iu(e,i[1])?i[0]:(t=t(),n.memoizedState=[t,e],t)}function Yp(t,e,n){return jn&21?(pt(n,e)||(n=Jh(),re.lanes|=n,An|=n,t.baseState=!0),e):(t.baseState&&(t.baseState=!1,Be=!0),t.memoizedState=n)}function Fx(t,e){var n=G;G=n!==0&&4>n?n:4,t(!0);var i=Oa.transition;Oa.transition={};try{t(!1),e()}finally{G=n,Oa.transition=i}}function Xp(){return st().memoizedState}function zx(t,e,n){var i=on(t);if(n={lane:i,action:n,hasEagerState:!1,eagerState:null,next:null},Qp(t))qp(e,n);else if(n=Np(t,e,n,i),n!==null){var s=De();ht(n,t,i,s),Gp(n,e,i)}}function Bx(t,e,n){var i=on(t),s={lane:i,action:n,hasEagerState:!1,eagerState:null,next:null};if(Qp(t))qp(e,s);else{var r=t.alternate;if(t.lanes===0&&(r===null||r.lanes===0)&&(r=e.lastRenderedReducer,r!==null))try{var o=e.lastRenderedState,a=r(o,n);if(s.hasEagerState=!0,s.eagerState=a,pt(a,o)){var l=e.interleaved;l===null?(s.next=s,Tu(e)):(s.next=l.next,l.next=s),e.interleaved=s;return}}catch{}finally{}n=Np(t,e,s,i),n!==null&&(s=De(),ht(n,t,i,s),Gp(n,e,i))}}function Qp(t){var e=t.alternate;return t===re||e!==null&&e===re}function qp(t,e){ls=xo=!0;var n=t.pending;n===null?e.next=e:(e.next=n.next,n.next=e),t.pending=e}function Gp(t,e,n){if(n&4194240){var i=e.lanes;i&=t.pendingLanes,n|=i,e.lanes=n,xu(t,n)}}var vo={readContext:it,useCallback:ke,useContext:ke,useEffect:ke,useImperativeHandle:ke,useInsertionEffect:ke,useLayoutEffect:ke,useMemo:ke,useReducer:ke,useRef:ke,useState:ke,useDebugValue:ke,useDeferredValue:ke,useTransition:ke,useMutableSource:ke,useSyncExternalStore:ke,useId:ke,unstable_isNewReconciler:!1},$x={readContext:it,useCallback:function(t,e){return vt().memoizedState=[t,e===void 0?null:e],t},useContext:it,useEffect:rd,useImperativeHandle:function(t,e,n){return n=n!=null?n.concat([t]):null,Wr(4194308,4,Hp.bind(null,e,t),n)},useLayoutEffect:function(t,e){return Wr(4194308,4,t,e)},useInsertionEffect:function(t,e){return Wr(4,2,t,e)},useMemo:function(t,e){var n=vt();return e=e===void 0?null:e,t=t(),n.memoizedState=[t,e],t},useReducer:function(t,e,n){var i=vt();return e=n!==void 0?n(e):e,i.memoizedState=i.baseState=e,t={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:t,lastRenderedState:e},i.queue=t,t=t.dispatch=zx.bind(null,re,t),[i.memoizedState,t]},useRef:function(t){var e=vt();return t={current:t},e.memoizedState=t},useState:sd,useDebugValue:$u,useDeferredValue:function(t){return vt().memoizedState=t},useTransition:function(){var t=sd(!1),e=t[0];return t=Fx.bind(null,t[1]),vt().memoizedState=t,[e,t]},useMutableSource:function(){},useSyncExternalStore:function(t,e,n){var i=re,s=vt();if(ie){if(n===void 0)throw Error(E(407));n=n()}else{if(n=e(),ve===null)throw Error(E(349));jn&30||jp(i,e,n)}s.memoizedState=n;var r={value:n,getSnapshot:e};return s.queue=r,rd(Ip.bind(null,i,r,t),[t]),i.flags|=2048,Rs(9,Ap.bind(null,i,r,n,e),void 0,null),n},useId:function(){var t=vt(),e=ve.identifierPrefix;if(ie){var n=Rt,i=Tt;n=(i&~(1<<32-ft(i)-1)).toString(32)+n,e=":"+e+"R"+n,n=Ls++,0<n&&(e+="H"+n.toString(32)),e+=":"}else n=Ix++,e=":"+e+"r"+n.toString(32)+":";return t.memoizedState=e},unstable_isNewReconciler:!1},Vx={readContext:it,useCallback:Up,useContext:it,useEffect:Bu,useImperativeHandle:Wp,useInsertionEffect:$p,useLayoutEffect:Vp,useMemo:Kp,useReducer:Na,useRef:Bp,useState:function(){return Na(Ts)},useDebugValue:$u,useDeferredValue:function(t){var e=st();return Yp(e,he.memoizedState,t)},useTransition:function(){var t=Na(Ts)[0],e=st().memoizedState;return[t,e]},useMutableSource:Rp,useSyncExternalStore:Dp,useId:Xp,unstable_isNewReconciler:!1},Hx={readContext:it,useCallback:Up,useContext:it,useEffect:Bu,useImperativeHandle:Wp,useInsertionEffect:$p,useLayoutEffect:Vp,useMemo:Kp,useReducer:La,useRef:Bp,useState:function(){return La(Ts)},useDebugValue:$u,useDeferredValue:function(t){var e=st();return he===null?e.memoizedState=t:Yp(e,he.memoizedState,t)},useTransition:function(){var t=La(Ts)[0],e=st().memoizedState;return[t,e]},useMutableSource:Rp,useSyncExternalStore:Dp,useId:Xp,unstable_isNewReconciler:!1};function lt(t,e){if(t&&t.defaultProps){e=oe({},e),t=t.defaultProps;for(var n in t)e[n]===void 0&&(e[n]=t[n]);return e}return e}function Ml(t,e,n,i){e=t.memoizedState,n=n(i,e),n=n==null?e:oe({},e,n),t.memoizedState=n,t.lanes===0&&(t.updateQueue.baseState=n)}var Yo={isMounted:function(t){return(t=t._reactInternals)?$n(t)===t:!1},enqueueSetState:function(t,e,n){t=t._reactInternals;var i=De(),s=on(t),r=jt(i,s);r.payload=e,n!=null&&(r.callback=n),e=sn(t,r,s),e!==null&&(ht(e,t,s,i),Vr(e,t,s))},enqueueReplaceState:function(t,e,n){t=t._reactInternals;var i=De(),s=on(t),r=jt(i,s);r.tag=1,r.payload=e,n!=null&&(r.callback=n),e=sn(t,r,s),e!==null&&(ht(e,t,s,i),Vr(e,t,s))},enqueueForceUpdate:function(t,e){t=t._reactInternals;var n=De(),i=on(t),s=jt(n,i);s.tag=2,e!=null&&(s.callback=e),e=sn(t,s,i),e!==null&&(ht(e,t,i,n),Vr(e,t,i))}};function od(t,e,n,i,s,r,o){return t=t.stateNode,typeof t.shouldComponentUpdate=="function"?t.shouldComponentUpdate(i,r,o):e.prototype&&e.prototype.isPureReactComponent?!Cs(n,i)||!Cs(s,r):!0}function Jp(t,e,n){var i=!1,s=dn,r=e.contextType;return typeof r=="object"&&r!==null?r=it(r):(s=Ve(e)?Rn:Ne.current,i=e.contextTypes,r=(i=i!=null)?vi(t,s):dn),e=new e(n,r),t.memoizedState=e.state!==null&&e.state!==void 0?e.state:null,e.updater=Yo,t.stateNode=e,e._reactInternals=t,i&&(t=t.stateNode,t.__reactInternalMemoizedUnmaskedChildContext=s,t.__reactInternalMemoizedMaskedChildContext=r),e}function ad(t,e,n,i){t=e.state,typeof e.componentWillReceiveProps=="function"&&e.componentWillReceiveProps(n,i),typeof e.UNSAFE_componentWillReceiveProps=="function"&&e.UNSAFE_componentWillReceiveProps(n,i),e.state!==t&&Yo.enqueueReplaceState(e,e.state,null)}function El(t,e,n,i){var s=t.stateNode;s.props=n,s.state=t.memoizedState,s.refs={},Ru(t);var r=e.contextType;typeof r=="object"&&r!==null?s.context=it(r):(r=Ve(e)?Rn:Ne.current,s.context=vi(t,r)),s.state=t.memoizedState,r=e.getDerivedStateFromProps,typeof r=="function"&&(Ml(t,e,r,n),s.state=t.memoizedState),typeof e.getDerivedStateFromProps=="function"||typeof s.getSnapshotBeforeUpdate=="function"||typeof s.UNSAFE_componentWillMount!="function"&&typeof s.componentWillMount!="function"||(e=s.state,typeof s.componentWillMount=="function"&&s.componentWillMount(),typeof s.UNSAFE_componentWillMount=="function"&&s.UNSAFE_componentWillMount(),e!==s.state&&Yo.enqueueReplaceState(s,s.state,null),mo(t,n,s,i),s.state=t.memoizedState),typeof s.componentDidMount=="function"&&(t.flags|=4194308)}function bi(t,e){try{var n="",i=e;do n+=yy(i),i=i.return;while(i);var s=n}catch(r){s=`
Error generating stack: `+r.message+`
`+r.stack}return{value:t,source:e,stack:s,digest:null}}function Ta(t,e,n){return{value:t,source:null,stack:n??null,digest:e??null}}function Ol(t,e){try{console.error(e.value)}catch(n){setTimeout(function(){throw n})}}var Wx=typeof WeakMap=="function"?WeakMap:Map;function Zp(t,e,n){n=jt(-1,n),n.tag=3,n.payload={element:null};var i=e.value;return n.callback=function(){wo||(wo=!0,zl=i),Ol(t,e)},n}function eg(t,e,n){n=jt(-1,n),n.tag=3;var i=t.type.getDerivedStateFromError;if(typeof i=="function"){var s=e.value;n.payload=function(){return i(s)},n.callback=function(){Ol(t,e)}}var r=t.stateNode;return r!==null&&typeof r.componentDidCatch=="function"&&(n.callback=function(){Ol(t,e),typeof i!="function"&&(rn===null?rn=new Set([this]):rn.add(this));var o=e.stack;this.componentDidCatch(e.value,{componentStack:o!==null?o:""})}),n}function ld(t,e,n){var i=t.pingCache;if(i===null){i=t.pingCache=new Wx;var s=new Set;i.set(e,s)}else s=i.get(e),s===void 0&&(s=new Set,i.set(e,s));s.has(n)||(s.add(n),t=sv.bind(null,t,e,n),e.then(t,t))}function ud(t){do{var e;if((e=t.tag===13)&&(e=t.memoizedState,e=e!==null?e.dehydrated!==null:!0),e)return t;t=t.return}while(t!==null);return null}function cd(t,e,n,i,s){return t.mode&1?(t.flags|=65536,t.lanes=s,t):(t===e?t.flags|=65536:(t.flags|=128,n.flags|=131072,n.flags&=-52805,n.tag===1&&(n.alternate===null?n.tag=17:(e=jt(-1,1),e.tag=2,sn(n,e,1))),n.lanes|=1),t)}var Ux=Bt.ReactCurrentOwner,Be=!1;function Re(t,e,n,i){e.child=t===null?Op(e,null,n,i):wi(e,t.child,n,i)}function dd(t,e,n,i,s){n=n.render;var r=e.ref;return pi(e,s),i=Fu(t,e,n,i,r,s),n=zu(),t!==null&&!Be?(e.updateQueue=t.updateQueue,e.flags&=-2053,t.lanes&=~s,zt(t,e,s)):(ie&&n&&Pu(e),e.flags|=1,Re(t,e,i,s),e.child)}function fd(t,e,n,i,s){if(t===null){var r=n.type;return typeof r=="function"&&!Qu(r)&&r.defaultProps===void 0&&n.compare===null&&n.defaultProps===void 0?(e.tag=15,e.type=r,tg(t,e,r,i,s)):(t=Xr(n.type,null,i,e,e.mode,s),t.ref=e.ref,t.return=e,e.child=t)}if(r=t.child,!(t.lanes&s)){var o=r.memoizedProps;if(n=n.compare,n=n!==null?n:Cs,n(o,i)&&t.ref===e.ref)return zt(t,e,s)}return e.flags|=1,t=an(r,i),t.ref=e.ref,t.return=e,e.child=t}function tg(t,e,n,i,s){if(t!==null){var r=t.memoizedProps;if(Cs(r,i)&&t.ref===e.ref)if(Be=!1,e.pendingProps=i=r,(t.lanes&s)!==0)t.flags&131072&&(Be=!0);else return e.lanes=t.lanes,zt(t,e,s)}return Nl(t,e,n,i,s)}function ng(t,e,n){var i=e.pendingProps,s=i.children,r=t!==null?t.memoizedState:null;if(i.mode==="hidden")if(!(e.mode&1))e.memoizedState={baseLanes:0,cachePool:null,transitions:null},Z(ui,We),We|=n;else{if(!(n&1073741824))return t=r!==null?r.baseLanes|n:n,e.lanes=e.childLanes=1073741824,e.memoizedState={baseLanes:t,cachePool:null,transitions:null},e.updateQueue=null,Z(ui,We),We|=t,null;e.memoizedState={baseLanes:0,cachePool:null,transitions:null},i=r!==null?r.baseLanes:n,Z(ui,We),We|=i}else r!==null?(i=r.baseLanes|n,e.memoizedState=null):i=n,Z(ui,We),We|=i;return Re(t,e,s,n),e.child}function ig(t,e){var n=e.ref;(t===null&&n!==null||t!==null&&t.ref!==n)&&(e.flags|=512,e.flags|=2097152)}function Nl(t,e,n,i,s){var r=Ve(n)?Rn:Ne.current;return r=vi(e,r),pi(e,s),n=Fu(t,e,n,i,r,s),i=zu(),t!==null&&!Be?(e.updateQueue=t.updateQueue,e.flags&=-2053,t.lanes&=~s,zt(t,e,s)):(ie&&i&&Pu(e),e.flags|=1,Re(t,e,n,s),e.child)}function hd(t,e,n,i,s){if(Ve(n)){var r=!0;co(e)}else r=!1;if(pi(e,s),e.stateNode===null)Ur(t,e),Jp(e,n,i),El(e,n,i,s),i=!0;else if(t===null){var o=e.stateNode,a=e.memoizedProps;o.props=a;var l=o.context,u=n.contextType;typeof u=="object"&&u!==null?u=it(u):(u=Ve(n)?Rn:Ne.current,u=vi(e,u));var c=n.getDerivedStateFromProps,d=typeof c=="function"||typeof o.getSnapshotBeforeUpdate=="function";d||typeof o.UNSAFE_componentWillReceiveProps!="function"&&typeof o.componentWillReceiveProps!="function"||(a!==i||l!==u)&&ad(e,o,i,u),Wt=!1;var f=e.memoizedState;o.state=f,mo(e,i,o,s),l=e.memoizedState,a!==i||f!==l||$e.current||Wt?(typeof c=="function"&&(Ml(e,n,c,i),l=e.memoizedState),(a=Wt||od(e,n,a,i,f,l,u))?(d||typeof o.UNSAFE_componentWillMount!="function"&&typeof o.componentWillMount!="function"||(typeof o.componentWillMount=="function"&&o.componentWillMount(),typeof o.UNSAFE_componentWillMount=="function"&&o.UNSAFE_componentWillMount()),typeof o.componentDidMount=="function"&&(e.flags|=4194308)):(typeof o.componentDidMount=="function"&&(e.flags|=4194308),e.memoizedProps=i,e.memoizedState=l),o.props=i,o.state=l,o.context=u,i=a):(typeof o.componentDidMount=="function"&&(e.flags|=4194308),i=!1)}else{o=e.stateNode,Lp(t,e),a=e.memoizedProps,u=e.type===e.elementType?a:lt(e.type,a),o.props=u,d=e.pendingProps,f=o.context,l=n.contextType,typeof l=="object"&&l!==null?l=it(l):(l=Ve(n)?Rn:Ne.current,l=vi(e,l));var h=n.getDerivedStateFromProps;(c=typeof h=="function"||typeof o.getSnapshotBeforeUpdate=="function")||typeof o.UNSAFE_componentWillReceiveProps!="function"&&typeof o.componentWillReceiveProps!="function"||(a!==d||f!==l)&&ad(e,o,i,l),Wt=!1,f=e.memoizedState,o.state=f,mo(e,i,o,s);var p=e.memoizedState;a!==d||f!==p||$e.current||Wt?(typeof h=="function"&&(Ml(e,n,h,i),p=e.memoizedState),(u=Wt||od(e,n,u,i,f,p,l)||!1)?(c||typeof o.UNSAFE_componentWillUpdate!="function"&&typeof o.componentWillUpdate!="function"||(typeof o.componentWillUpdate=="function"&&o.componentWillUpdate(i,p,l),typeof o.UNSAFE_componentWillUpdate=="function"&&o.UNSAFE_componentWillUpdate(i,p,l)),typeof o.componentDidUpdate=="function"&&(e.flags|=4),typeof o.getSnapshotBeforeUpdate=="function"&&(e.flags|=1024)):(typeof o.componentDidUpdate!="function"||a===t.memoizedProps&&f===t.memoizedState||(e.flags|=4),typeof o.getSnapshotBeforeUpdate!="function"||a===t.memoizedProps&&f===t.memoizedState||(e.flags|=1024),e.memoizedProps=i,e.memoizedState=p),o.props=i,o.state=p,o.context=l,i=u):(typeof o.componentDidUpdate!="function"||a===t.memoizedProps&&f===t.memoizedState||(e.flags|=4),typeof o.getSnapshotBeforeUpdate!="function"||a===t.memoizedProps&&f===t.memoizedState||(e.flags|=1024),i=!1)}return Ll(t,e,n,i,r,s)}function Ll(t,e,n,i,s,r){ig(t,e);var o=(e.flags&128)!==0;if(!i&&!o)return s&&Jc(e,n,!1),zt(t,e,r);i=e.stateNode,Ux.current=e;var a=o&&typeof n.getDerivedStateFromError!="function"?null:i.render();return e.flags|=1,t!==null&&o?(e.child=wi(e,t.child,null,r),e.child=wi(e,null,a,r)):Re(t,e,a,r),e.memoizedState=i.state,s&&Jc(e,n,!0),e.child}function sg(t){var e=t.stateNode;e.pendingContext?Gc(t,e.pendingContext,e.pendingContext!==e.context):e.context&&Gc(t,e.context,!1),Du(t,e.containerInfo)}function pd(t,e,n,i,s){return _i(),Eu(s),e.flags|=256,Re(t,e,n,i),e.child}var Tl={dehydrated:null,treeContext:null,retryLane:0};function Rl(t){return{baseLanes:t,cachePool:null,transitions:null}}function rg(t,e,n){var i=e.pendingProps,s=se.current,r=!1,o=(e.flags&128)!==0,a;if((a=o)||(a=t!==null&&t.memoizedState===null?!1:(s&2)!==0),a?(r=!0,e.flags&=-129):(t===null||t.memoizedState!==null)&&(s|=1),Z(se,s&1),t===null)return Cl(e),t=e.memoizedState,t!==null&&(t=t.dehydrated,t!==null)?(e.mode&1?t.data==="$!"?e.lanes=8:e.lanes=1073741824:e.lanes=1,null):(o=i.children,t=i.fallback,r?(i=e.mode,r=e.child,o={mode:"hidden",children:o},!(i&1)&&r!==null?(r.childLanes=0,r.pendingProps=o):r=qo(o,i,0,null),t=On(t,i,n,null),r.return=e,t.return=e,r.sibling=t,e.child=r,e.child.memoizedState=Rl(n),e.memoizedState=Tl,t):Vu(e,o));if(s=t.memoizedState,s!==null&&(a=s.dehydrated,a!==null))return Kx(t,e,o,i,a,s,n);if(r){r=i.fallback,o=e.mode,s=t.child,a=s.sibling;var l={mode:"hidden",children:i.children};return!(o&1)&&e.child!==s?(i=e.child,i.childLanes=0,i.pendingProps=l,e.deletions=null):(i=an(s,l),i.subtreeFlags=s.subtreeFlags&14680064),a!==null?r=an(a,r):(r=On(r,o,n,null),r.flags|=2),r.return=e,i.return=e,i.sibling=r,e.child=i,i=r,r=e.child,o=t.child.memoizedState,o=o===null?Rl(n):{baseLanes:o.baseLanes|n,cachePool:null,transitions:o.transitions},r.memoizedState=o,r.childLanes=t.childLanes&~n,e.memoizedState=Tl,i}return r=t.child,t=r.sibling,i=an(r,{mode:"visible",children:i.children}),!(e.mode&1)&&(i.lanes=n),i.return=e,i.sibling=null,t!==null&&(n=e.deletions,n===null?(e.deletions=[t],e.flags|=16):n.push(t)),e.child=i,e.memoizedState=null,i}function Vu(t,e){return e=qo({mode:"visible",children:e},t.mode,0,null),e.return=t,t.child=e}function xr(t,e,n,i){return i!==null&&Eu(i),wi(e,t.child,null,n),t=Vu(e,e.pendingProps.children),t.flags|=2,e.memoizedState=null,t}function Kx(t,e,n,i,s,r,o){if(n)return e.flags&256?(e.flags&=-257,i=Ta(Error(E(422))),xr(t,e,o,i)):e.memoizedState!==null?(e.child=t.child,e.flags|=128,null):(r=i.fallback,s=e.mode,i=qo({mode:"visible",children:i.children},s,0,null),r=On(r,s,o,null),r.flags|=2,i.return=e,r.return=e,i.sibling=r,e.child=i,e.mode&1&&wi(e,t.child,null,o),e.child.memoizedState=Rl(o),e.memoizedState=Tl,r);if(!(e.mode&1))return xr(t,e,o,null);if(s.data==="$!"){if(i=s.nextSibling&&s.nextSibling.dataset,i)var a=i.dgst;return i=a,r=Error(E(419)),i=Ta(r,i,void 0),xr(t,e,o,i)}if(a=(o&t.childLanes)!==0,Be||a){if(i=ve,i!==null){switch(o&-o){case 4:s=2;break;case 16:s=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:s=32;break;case 536870912:s=268435456;break;default:s=0}s=s&(i.suspendedLanes|o)?0:s,s!==0&&s!==r.retryLane&&(r.retryLane=s,Ft(t,s),ht(i,t,s,-1))}return Xu(),i=Ta(Error(E(421))),xr(t,e,o,i)}return s.data==="$?"?(e.flags|=128,e.child=t.child,e=rv.bind(null,t),s._reactRetry=e,null):(t=r.treeContext,Ue=nn(s.nextSibling),Ke=e,ie=!0,dt=null,t!==null&&(Je[Ze++]=Tt,Je[Ze++]=Rt,Je[Ze++]=Dn,Tt=t.id,Rt=t.overflow,Dn=e),e=Vu(e,i.children),e.flags|=4096,e)}function gd(t,e,n){t.lanes|=e;var i=t.alternate;i!==null&&(i.lanes|=e),Pl(t.return,e,n)}function Ra(t,e,n,i,s){var r=t.memoizedState;r===null?t.memoizedState={isBackwards:e,rendering:null,renderingStartTime:0,last:i,tail:n,tailMode:s}:(r.isBackwards=e,r.rendering=null,r.renderingStartTime=0,r.last=i,r.tail=n,r.tailMode=s)}function og(t,e,n){var i=e.pendingProps,s=i.revealOrder,r=i.tail;if(Re(t,e,i.children,n),i=se.current,i&2)i=i&1|2,e.flags|=128;else{if(t!==null&&t.flags&128)e:for(t=e.child;t!==null;){if(t.tag===13)t.memoizedState!==null&&gd(t,n,e);else if(t.tag===19)gd(t,n,e);else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break e;for(;t.sibling===null;){if(t.return===null||t.return===e)break e;t=t.return}t.sibling.return=t.return,t=t.sibling}i&=1}if(Z(se,i),!(e.mode&1))e.memoizedState=null;else switch(s){case"forwards":for(n=e.child,s=null;n!==null;)t=n.alternate,t!==null&&yo(t)===null&&(s=n),n=n.sibling;n=s,n===null?(s=e.child,e.child=null):(s=n.sibling,n.sibling=null),Ra(e,!1,s,n,r);break;case"backwards":for(n=null,s=e.child,e.child=null;s!==null;){if(t=s.alternate,t!==null&&yo(t)===null){e.child=s;break}t=s.sibling,s.sibling=n,n=s,s=t}Ra(e,!0,n,null,r);break;case"together":Ra(e,!1,null,null,void 0);break;default:e.memoizedState=null}return e.child}function Ur(t,e){!(e.mode&1)&&t!==null&&(t.alternate=null,e.alternate=null,e.flags|=2)}function zt(t,e,n){if(t!==null&&(e.dependencies=t.dependencies),An|=e.lanes,!(n&e.childLanes))return null;if(t!==null&&e.child!==t.child)throw Error(E(153));if(e.child!==null){for(t=e.child,n=an(t,t.pendingProps),e.child=n,n.return=e;t.sibling!==null;)t=t.sibling,n=n.sibling=an(t,t.pendingProps),n.return=e;n.sibling=null}return e.child}function Yx(t,e,n){switch(e.tag){case 3:sg(e),_i();break;case 5:Tp(e);break;case 1:Ve(e.type)&&co(e);break;case 4:Du(e,e.stateNode.containerInfo);break;case 10:var i=e.type._context,s=e.memoizedProps.value;Z(po,i._currentValue),i._currentValue=s;break;case 13:if(i=e.memoizedState,i!==null)return i.dehydrated!==null?(Z(se,se.current&1),e.flags|=128,null):n&e.child.childLanes?rg(t,e,n):(Z(se,se.current&1),t=zt(t,e,n),t!==null?t.sibling:null);Z(se,se.current&1);break;case 19:if(i=(n&e.childLanes)!==0,t.flags&128){if(i)return og(t,e,n);e.flags|=128}if(s=e.memoizedState,s!==null&&(s.rendering=null,s.tail=null,s.lastEffect=null),Z(se,se.current),i)break;return null;case 22:case 23:return e.lanes=0,ng(t,e,n)}return zt(t,e,n)}var ag,Dl,lg,ug;ag=function(t,e){for(var n=e.child;n!==null;){if(n.tag===5||n.tag===6)t.appendChild(n.stateNode);else if(n.tag!==4&&n.child!==null){n.child.return=n,n=n.child;continue}if(n===e)break;for(;n.sibling===null;){if(n.return===null||n.return===e)return;n=n.return}n.sibling.return=n.return,n=n.sibling}};Dl=function(){};lg=function(t,e,n,i){var s=t.memoizedProps;if(s!==i){t=e.stateNode,Pn(kt.current);var r=null;switch(n){case"input":s=nl(t,s),i=nl(t,i),r=[];break;case"select":s=oe({},s,{value:void 0}),i=oe({},i,{value:void 0}),r=[];break;case"textarea":s=rl(t,s),i=rl(t,i),r=[];break;default:typeof s.onClick!="function"&&typeof i.onClick=="function"&&(t.onclick=lo)}al(n,i);var o;n=null;for(u in s)if(!i.hasOwnProperty(u)&&s.hasOwnProperty(u)&&s[u]!=null)if(u==="style"){var a=s[u];for(o in a)a.hasOwnProperty(o)&&(n||(n={}),n[o]="")}else u!=="dangerouslySetInnerHTML"&&u!=="children"&&u!=="suppressContentEditableWarning"&&u!=="suppressHydrationWarning"&&u!=="autoFocus"&&(xs.hasOwnProperty(u)?r||(r=[]):(r=r||[]).push(u,null));for(u in i){var l=i[u];if(a=s!=null?s[u]:void 0,i.hasOwnProperty(u)&&l!==a&&(l!=null||a!=null))if(u==="style")if(a){for(o in a)!a.hasOwnProperty(o)||l&&l.hasOwnProperty(o)||(n||(n={}),n[o]="");for(o in l)l.hasOwnProperty(o)&&a[o]!==l[o]&&(n||(n={}),n[o]=l[o])}else n||(r||(r=[]),r.push(u,n)),n=l;else u==="dangerouslySetInnerHTML"?(l=l?l.__html:void 0,a=a?a.__html:void 0,l!=null&&a!==l&&(r=r||[]).push(u,l)):u==="children"?typeof l!="string"&&typeof l!="number"||(r=r||[]).push(u,""+l):u!=="suppressContentEditableWarning"&&u!=="suppressHydrationWarning"&&(xs.hasOwnProperty(u)?(l!=null&&u==="onScroll"&&ee("scroll",t),r||a===l||(r=[])):(r=r||[]).push(u,l))}n&&(r=r||[]).push("style",n);var u=r;(e.updateQueue=u)&&(e.flags|=4)}};ug=function(t,e,n,i){n!==i&&(e.flags|=4)};function zi(t,e){if(!ie)switch(t.tailMode){case"hidden":e=t.tail;for(var n=null;e!==null;)e.alternate!==null&&(n=e),e=e.sibling;n===null?t.tail=null:n.sibling=null;break;case"collapsed":n=t.tail;for(var i=null;n!==null;)n.alternate!==null&&(i=n),n=n.sibling;i===null?e||t.tail===null?t.tail=null:t.tail.sibling=null:i.sibling=null}}function Ce(t){var e=t.alternate!==null&&t.alternate.child===t.child,n=0,i=0;if(e)for(var s=t.child;s!==null;)n|=s.lanes|s.childLanes,i|=s.subtreeFlags&14680064,i|=s.flags&14680064,s.return=t,s=s.sibling;else for(s=t.child;s!==null;)n|=s.lanes|s.childLanes,i|=s.subtreeFlags,i|=s.flags,s.return=t,s=s.sibling;return t.subtreeFlags|=i,t.childLanes=n,e}function Xx(t,e,n){var i=e.pendingProps;switch(Mu(e),e.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return Ce(e),null;case 1:return Ve(e.type)&&uo(),Ce(e),null;case 3:return i=e.stateNode,Si(),ne($e),ne(Ne),Au(),i.pendingContext&&(i.context=i.pendingContext,i.pendingContext=null),(t===null||t.child===null)&&(mr(e)?e.flags|=4:t===null||t.memoizedState.isDehydrated&&!(e.flags&256)||(e.flags|=1024,dt!==null&&(Vl(dt),dt=null))),Dl(t,e),Ce(e),null;case 5:ju(e);var s=Pn(Ns.current);if(n=e.type,t!==null&&e.stateNode!=null)lg(t,e,n,i,s),t.ref!==e.ref&&(e.flags|=512,e.flags|=2097152);else{if(!i){if(e.stateNode===null)throw Error(E(166));return Ce(e),null}if(t=Pn(kt.current),mr(e)){i=e.stateNode,n=e.type;var r=e.memoizedProps;switch(i[wt]=e,i[Es]=r,t=(e.mode&1)!==0,n){case"dialog":ee("cancel",i),ee("close",i);break;case"iframe":case"object":case"embed":ee("load",i);break;case"video":case"audio":for(s=0;s<qi.length;s++)ee(qi[s],i);break;case"source":ee("error",i);break;case"img":case"image":case"link":ee("error",i),ee("load",i);break;case"details":ee("toggle",i);break;case"input":kc(i,r),ee("invalid",i);break;case"select":i._wrapperState={wasMultiple:!!r.multiple},ee("invalid",i);break;case"textarea":Pc(i,r),ee("invalid",i)}al(n,r),s=null;for(var o in r)if(r.hasOwnProperty(o)){var a=r[o];o==="children"?typeof a=="string"?i.textContent!==a&&(r.suppressHydrationWarning!==!0&&gr(i.textContent,a,t),s=["children",a]):typeof a=="number"&&i.textContent!==""+a&&(r.suppressHydrationWarning!==!0&&gr(i.textContent,a,t),s=["children",""+a]):xs.hasOwnProperty(o)&&a!=null&&o==="onScroll"&&ee("scroll",i)}switch(n){case"input":ar(i),Cc(i,r,!0);break;case"textarea":ar(i),Mc(i);break;case"select":case"option":break;default:typeof r.onClick=="function"&&(i.onclick=lo)}i=s,e.updateQueue=i,i!==null&&(e.flags|=4)}else{o=s.nodeType===9?s:s.ownerDocument,t==="http://www.w3.org/1999/xhtml"&&(t=Ih(n)),t==="http://www.w3.org/1999/xhtml"?n==="script"?(t=o.createElement("div"),t.innerHTML="<script><\/script>",t=t.removeChild(t.firstChild)):typeof i.is=="string"?t=o.createElement(n,{is:i.is}):(t=o.createElement(n),n==="select"&&(o=t,i.multiple?o.multiple=!0:i.size&&(o.size=i.size))):t=o.createElementNS(t,n),t[wt]=e,t[Es]=i,ag(t,e,!1,!1),e.stateNode=t;e:{switch(o=ll(n,i),n){case"dialog":ee("cancel",t),ee("close",t),s=i;break;case"iframe":case"object":case"embed":ee("load",t),s=i;break;case"video":case"audio":for(s=0;s<qi.length;s++)ee(qi[s],t);s=i;break;case"source":ee("error",t),s=i;break;case"img":case"image":case"link":ee("error",t),ee("load",t),s=i;break;case"details":ee("toggle",t),s=i;break;case"input":kc(t,i),s=nl(t,i),ee("invalid",t);break;case"option":s=i;break;case"select":t._wrapperState={wasMultiple:!!i.multiple},s=oe({},i,{value:void 0}),ee("invalid",t);break;case"textarea":Pc(t,i),s=rl(t,i),ee("invalid",t);break;default:s=i}al(n,s),a=s;for(r in a)if(a.hasOwnProperty(r)){var l=a[r];r==="style"?Bh(t,l):r==="dangerouslySetInnerHTML"?(l=l?l.__html:void 0,l!=null&&Fh(t,l)):r==="children"?typeof l=="string"?(n!=="textarea"||l!=="")&&vs(t,l):typeof l=="number"&&vs(t,""+l):r!=="suppressContentEditableWarning"&&r!=="suppressHydrationWarning"&&r!=="autoFocus"&&(xs.hasOwnProperty(r)?l!=null&&r==="onScroll"&&ee("scroll",t):l!=null&&fu(t,r,l,o))}switch(n){case"input":ar(t),Cc(t,i,!1);break;case"textarea":ar(t),Mc(t);break;case"option":i.value!=null&&t.setAttribute("value",""+cn(i.value));break;case"select":t.multiple=!!i.multiple,r=i.value,r!=null?ci(t,!!i.multiple,r,!1):i.defaultValue!=null&&ci(t,!!i.multiple,i.defaultValue,!0);break;default:typeof s.onClick=="function"&&(t.onclick=lo)}switch(n){case"button":case"input":case"select":case"textarea":i=!!i.autoFocus;break e;case"img":i=!0;break e;default:i=!1}}i&&(e.flags|=4)}e.ref!==null&&(e.flags|=512,e.flags|=2097152)}return Ce(e),null;case 6:if(t&&e.stateNode!=null)ug(t,e,t.memoizedProps,i);else{if(typeof i!="string"&&e.stateNode===null)throw Error(E(166));if(n=Pn(Ns.current),Pn(kt.current),mr(e)){if(i=e.stateNode,n=e.memoizedProps,i[wt]=e,(r=i.nodeValue!==n)&&(t=Ke,t!==null))switch(t.tag){case 3:gr(i.nodeValue,n,(t.mode&1)!==0);break;case 5:t.memoizedProps.suppressHydrationWarning!==!0&&gr(i.nodeValue,n,(t.mode&1)!==0)}r&&(e.flags|=4)}else i=(n.nodeType===9?n:n.ownerDocument).createTextNode(i),i[wt]=e,e.stateNode=i}return Ce(e),null;case 13:if(ne(se),i=e.memoizedState,t===null||t.memoizedState!==null&&t.memoizedState.dehydrated!==null){if(ie&&Ue!==null&&e.mode&1&&!(e.flags&128))Mp(),_i(),e.flags|=98560,r=!1;else if(r=mr(e),i!==null&&i.dehydrated!==null){if(t===null){if(!r)throw Error(E(318));if(r=e.memoizedState,r=r!==null?r.dehydrated:null,!r)throw Error(E(317));r[wt]=e}else _i(),!(e.flags&128)&&(e.memoizedState=null),e.flags|=4;Ce(e),r=!1}else dt!==null&&(Vl(dt),dt=null),r=!0;if(!r)return e.flags&65536?e:null}return e.flags&128?(e.lanes=n,e):(i=i!==null,i!==(t!==null&&t.memoizedState!==null)&&i&&(e.child.flags|=8192,e.mode&1&&(t===null||se.current&1?ge===0&&(ge=3):Xu())),e.updateQueue!==null&&(e.flags|=4),Ce(e),null);case 4:return Si(),Dl(t,e),t===null&&Ps(e.stateNode.containerInfo),Ce(e),null;case 10:return Lu(e.type._context),Ce(e),null;case 17:return Ve(e.type)&&uo(),Ce(e),null;case 19:if(ne(se),r=e.memoizedState,r===null)return Ce(e),null;if(i=(e.flags&128)!==0,o=r.rendering,o===null)if(i)zi(r,!1);else{if(ge!==0||t!==null&&t.flags&128)for(t=e.child;t!==null;){if(o=yo(t),o!==null){for(e.flags|=128,zi(r,!1),i=o.updateQueue,i!==null&&(e.updateQueue=i,e.flags|=4),e.subtreeFlags=0,i=n,n=e.child;n!==null;)r=n,t=i,r.flags&=14680066,o=r.alternate,o===null?(r.childLanes=0,r.lanes=t,r.child=null,r.subtreeFlags=0,r.memoizedProps=null,r.memoizedState=null,r.updateQueue=null,r.dependencies=null,r.stateNode=null):(r.childLanes=o.childLanes,r.lanes=o.lanes,r.child=o.child,r.subtreeFlags=0,r.deletions=null,r.memoizedProps=o.memoizedProps,r.memoizedState=o.memoizedState,r.updateQueue=o.updateQueue,r.type=o.type,t=o.dependencies,r.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext}),n=n.sibling;return Z(se,se.current&1|2),e.child}t=t.sibling}r.tail!==null&&ce()>ki&&(e.flags|=128,i=!0,zi(r,!1),e.lanes=4194304)}else{if(!i)if(t=yo(o),t!==null){if(e.flags|=128,i=!0,n=t.updateQueue,n!==null&&(e.updateQueue=n,e.flags|=4),zi(r,!0),r.tail===null&&r.tailMode==="hidden"&&!o.alternate&&!ie)return Ce(e),null}else 2*ce()-r.renderingStartTime>ki&&n!==1073741824&&(e.flags|=128,i=!0,zi(r,!1),e.lanes=4194304);r.isBackwards?(o.sibling=e.child,e.child=o):(n=r.last,n!==null?n.sibling=o:e.child=o,r.last=o)}return r.tail!==null?(e=r.tail,r.rendering=e,r.tail=e.sibling,r.renderingStartTime=ce(),e.sibling=null,n=se.current,Z(se,i?n&1|2:n&1),e):(Ce(e),null);case 22:case 23:return Yu(),i=e.memoizedState!==null,t!==null&&t.memoizedState!==null!==i&&(e.flags|=8192),i&&e.mode&1?We&1073741824&&(Ce(e),e.subtreeFlags&6&&(e.flags|=8192)):Ce(e),null;case 24:return null;case 25:return null}throw Error(E(156,e.tag))}function Qx(t,e){switch(Mu(e),e.tag){case 1:return Ve(e.type)&&uo(),t=e.flags,t&65536?(e.flags=t&-65537|128,e):null;case 3:return Si(),ne($e),ne(Ne),Au(),t=e.flags,t&65536&&!(t&128)?(e.flags=t&-65537|128,e):null;case 5:return ju(e),null;case 13:if(ne(se),t=e.memoizedState,t!==null&&t.dehydrated!==null){if(e.alternate===null)throw Error(E(340));_i()}return t=e.flags,t&65536?(e.flags=t&-65537|128,e):null;case 19:return ne(se),null;case 4:return Si(),null;case 10:return Lu(e.type._context),null;case 22:case 23:return Yu(),null;case 24:return null;default:return null}}var vr=!1,Me=!1,qx=typeof WeakSet=="function"?WeakSet:Set,L=null;function li(t,e){var n=t.ref;if(n!==null)if(typeof n=="function")try{n(null)}catch(i){ae(t,e,i)}else n.current=null}function jl(t,e,n){try{n()}catch(i){ae(t,e,i)}}var md=!1;function Gx(t,e){if(xl=ro,t=pp(),Cu(t)){if("selectionStart"in t)var n={start:t.selectionStart,end:t.selectionEnd};else e:{n=(n=t.ownerDocument)&&n.defaultView||window;var i=n.getSelection&&n.getSelection();if(i&&i.rangeCount!==0){n=i.anchorNode;var s=i.anchorOffset,r=i.focusNode;i=i.focusOffset;try{n.nodeType,r.nodeType}catch{n=null;break e}var o=0,a=-1,l=-1,u=0,c=0,d=t,f=null;t:for(;;){for(var h;d!==n||s!==0&&d.nodeType!==3||(a=o+s),d!==r||i!==0&&d.nodeType!==3||(l=o+i),d.nodeType===3&&(o+=d.nodeValue.length),(h=d.firstChild)!==null;)f=d,d=h;for(;;){if(d===t)break t;if(f===n&&++u===s&&(a=o),f===r&&++c===i&&(l=o),(h=d.nextSibling)!==null)break;d=f,f=d.parentNode}d=h}n=a===-1||l===-1?null:{start:a,end:l}}else n=null}n=n||{start:0,end:0}}else n=null;for(vl={focusedElem:t,selectionRange:n},ro=!1,L=e;L!==null;)if(e=L,t=e.child,(e.subtreeFlags&1028)!==0&&t!==null)t.return=e,L=t;else for(;L!==null;){e=L;try{var p=e.alternate;if(e.flags&1024)switch(e.tag){case 0:case 11:case 15:break;case 1:if(p!==null){var y=p.memoizedProps,v=p.memoizedState,m=e.stateNode,g=m.getSnapshotBeforeUpdate(e.elementType===e.type?y:lt(e.type,y),v);m.__reactInternalSnapshotBeforeUpdate=g}break;case 3:var x=e.stateNode.containerInfo;x.nodeType===1?x.textContent="":x.nodeType===9&&x.documentElement&&x.removeChild(x.documentElement);break;case 5:case 6:case 4:case 17:break;default:throw Error(E(163))}}catch(w){ae(e,e.return,w)}if(t=e.sibling,t!==null){t.return=e.return,L=t;break}L=e.return}return p=md,md=!1,p}function us(t,e,n){var i=e.updateQueue;if(i=i!==null?i.lastEffect:null,i!==null){var s=i=i.next;do{if((s.tag&t)===t){var r=s.destroy;s.destroy=void 0,r!==void 0&&jl(e,n,r)}s=s.next}while(s!==i)}}function Xo(t,e){if(e=e.updateQueue,e=e!==null?e.lastEffect:null,e!==null){var n=e=e.next;do{if((n.tag&t)===t){var i=n.create;n.destroy=i()}n=n.next}while(n!==e)}}function Al(t){var e=t.ref;if(e!==null){var n=t.stateNode;switch(t.tag){case 5:t=n;break;default:t=n}typeof e=="function"?e(t):e.current=t}}function cg(t){var e=t.alternate;e!==null&&(t.alternate=null,cg(e)),t.child=null,t.deletions=null,t.sibling=null,t.tag===5&&(e=t.stateNode,e!==null&&(delete e[wt],delete e[Es],delete e[Sl],delete e[Rx],delete e[Dx])),t.stateNode=null,t.return=null,t.dependencies=null,t.memoizedProps=null,t.memoizedState=null,t.pendingProps=null,t.stateNode=null,t.updateQueue=null}function dg(t){return t.tag===5||t.tag===3||t.tag===4}function yd(t){e:for(;;){for(;t.sibling===null;){if(t.return===null||dg(t.return))return null;t=t.return}for(t.sibling.return=t.return,t=t.sibling;t.tag!==5&&t.tag!==6&&t.tag!==18;){if(t.flags&2||t.child===null||t.tag===4)continue e;t.child.return=t,t=t.child}if(!(t.flags&2))return t.stateNode}}function Il(t,e,n){var i=t.tag;if(i===5||i===6)t=t.stateNode,e?n.nodeType===8?n.parentNode.insertBefore(t,e):n.insertBefore(t,e):(n.nodeType===8?(e=n.parentNode,e.insertBefore(t,n)):(e=n,e.appendChild(t)),n=n._reactRootContainer,n!=null||e.onclick!==null||(e.onclick=lo));else if(i!==4&&(t=t.child,t!==null))for(Il(t,e,n),t=t.sibling;t!==null;)Il(t,e,n),t=t.sibling}function Fl(t,e,n){var i=t.tag;if(i===5||i===6)t=t.stateNode,e?n.insertBefore(t,e):n.appendChild(t);else if(i!==4&&(t=t.child,t!==null))for(Fl(t,e,n),t=t.sibling;t!==null;)Fl(t,e,n),t=t.sibling}var _e=null,ut=!1;function $t(t,e,n){for(n=n.child;n!==null;)fg(t,e,n),n=n.sibling}function fg(t,e,n){if(bt&&typeof bt.onCommitFiberUnmount=="function")try{bt.onCommitFiberUnmount(Bo,n)}catch{}switch(n.tag){case 5:Me||li(n,e);case 6:var i=_e,s=ut;_e=null,$t(t,e,n),_e=i,ut=s,_e!==null&&(ut?(t=_e,n=n.stateNode,t.nodeType===8?t.parentNode.removeChild(n):t.removeChild(n)):_e.removeChild(n.stateNode));break;case 18:_e!==null&&(ut?(t=_e,n=n.stateNode,t.nodeType===8?Pa(t.parentNode,n):t.nodeType===1&&Pa(t,n),bs(t)):Pa(_e,n.stateNode));break;case 4:i=_e,s=ut,_e=n.stateNode.containerInfo,ut=!0,$t(t,e,n),_e=i,ut=s;break;case 0:case 11:case 14:case 15:if(!Me&&(i=n.updateQueue,i!==null&&(i=i.lastEffect,i!==null))){s=i=i.next;do{var r=s,o=r.destroy;r=r.tag,o!==void 0&&(r&2||r&4)&&jl(n,e,o),s=s.next}while(s!==i)}$t(t,e,n);break;case 1:if(!Me&&(li(n,e),i=n.stateNode,typeof i.componentWillUnmount=="function"))try{i.props=n.memoizedProps,i.state=n.memoizedState,i.componentWillUnmount()}catch(a){ae(n,e,a)}$t(t,e,n);break;case 21:$t(t,e,n);break;case 22:n.mode&1?(Me=(i=Me)||n.memoizedState!==null,$t(t,e,n),Me=i):$t(t,e,n);break;default:$t(t,e,n)}}function xd(t){var e=t.updateQueue;if(e!==null){t.updateQueue=null;var n=t.stateNode;n===null&&(n=t.stateNode=new qx),e.forEach(function(i){var s=ov.bind(null,t,i);n.has(i)||(n.add(i),i.then(s,s))})}}function at(t,e){var n=e.deletions;if(n!==null)for(var i=0;i<n.length;i++){var s=n[i];try{var r=t,o=e,a=o;e:for(;a!==null;){switch(a.tag){case 5:_e=a.stateNode,ut=!1;break e;case 3:_e=a.stateNode.containerInfo,ut=!0;break e;case 4:_e=a.stateNode.containerInfo,ut=!0;break e}a=a.return}if(_e===null)throw Error(E(160));fg(r,o,s),_e=null,ut=!1;var l=s.alternate;l!==null&&(l.return=null),s.return=null}catch(u){ae(s,e,u)}}if(e.subtreeFlags&12854)for(e=e.child;e!==null;)hg(e,t),e=e.sibling}function hg(t,e){var n=t.alternate,i=t.flags;switch(t.tag){case 0:case 11:case 14:case 15:if(at(e,t),mt(t),i&4){try{us(3,t,t.return),Xo(3,t)}catch(y){ae(t,t.return,y)}try{us(5,t,t.return)}catch(y){ae(t,t.return,y)}}break;case 1:at(e,t),mt(t),i&512&&n!==null&&li(n,n.return);break;case 5:if(at(e,t),mt(t),i&512&&n!==null&&li(n,n.return),t.flags&32){var s=t.stateNode;try{vs(s,"")}catch(y){ae(t,t.return,y)}}if(i&4&&(s=t.stateNode,s!=null)){var r=t.memoizedProps,o=n!==null?n.memoizedProps:r,a=t.type,l=t.updateQueue;if(t.updateQueue=null,l!==null)try{a==="input"&&r.type==="radio"&&r.name!=null&&jh(s,r),ll(a,o);var u=ll(a,r);for(o=0;o<l.length;o+=2){var c=l[o],d=l[o+1];c==="style"?Bh(s,d):c==="dangerouslySetInnerHTML"?Fh(s,d):c==="children"?vs(s,d):fu(s,c,d,u)}switch(a){case"input":il(s,r);break;case"textarea":Ah(s,r);break;case"select":var f=s._wrapperState.wasMultiple;s._wrapperState.wasMultiple=!!r.multiple;var h=r.value;h!=null?ci(s,!!r.multiple,h,!1):f!==!!r.multiple&&(r.defaultValue!=null?ci(s,!!r.multiple,r.defaultValue,!0):ci(s,!!r.multiple,r.multiple?[]:"",!1))}s[Es]=r}catch(y){ae(t,t.return,y)}}break;case 6:if(at(e,t),mt(t),i&4){if(t.stateNode===null)throw Error(E(162));s=t.stateNode,r=t.memoizedProps;try{s.nodeValue=r}catch(y){ae(t,t.return,y)}}break;case 3:if(at(e,t),mt(t),i&4&&n!==null&&n.memoizedState.isDehydrated)try{bs(e.containerInfo)}catch(y){ae(t,t.return,y)}break;case 4:at(e,t),mt(t);break;case 13:at(e,t),mt(t),s=t.child,s.flags&8192&&(r=s.memoizedState!==null,s.stateNode.isHidden=r,!r||s.alternate!==null&&s.alternate.memoizedState!==null||(Uu=ce())),i&4&&xd(t);break;case 22:if(c=n!==null&&n.memoizedState!==null,t.mode&1?(Me=(u=Me)||c,at(e,t),Me=u):at(e,t),mt(t),i&8192){if(u=t.memoizedState!==null,(t.stateNode.isHidden=u)&&!c&&t.mode&1)for(L=t,c=t.child;c!==null;){for(d=L=c;L!==null;){switch(f=L,h=f.child,f.tag){case 0:case 11:case 14:case 15:us(4,f,f.return);break;case 1:li(f,f.return);var p=f.stateNode;if(typeof p.componentWillUnmount=="function"){i=f,n=f.return;try{e=i,p.props=e.memoizedProps,p.state=e.memoizedState,p.componentWillUnmount()}catch(y){ae(i,n,y)}}break;case 5:li(f,f.return);break;case 22:if(f.memoizedState!==null){_d(d);continue}}h!==null?(h.return=f,L=h):_d(d)}c=c.sibling}e:for(c=null,d=t;;){if(d.tag===5){if(c===null){c=d;try{s=d.stateNode,u?(r=s.style,typeof r.setProperty=="function"?r.setProperty("display","none","important"):r.display="none"):(a=d.stateNode,l=d.memoizedProps.style,o=l!=null&&l.hasOwnProperty("display")?l.display:null,a.style.display=zh("display",o))}catch(y){ae(t,t.return,y)}}}else if(d.tag===6){if(c===null)try{d.stateNode.nodeValue=u?"":d.memoizedProps}catch(y){ae(t,t.return,y)}}else if((d.tag!==22&&d.tag!==23||d.memoizedState===null||d===t)&&d.child!==null){d.child.return=d,d=d.child;continue}if(d===t)break e;for(;d.sibling===null;){if(d.return===null||d.return===t)break e;c===d&&(c=null),d=d.return}c===d&&(c=null),d.sibling.return=d.return,d=d.sibling}}break;case 19:at(e,t),mt(t),i&4&&xd(t);break;case 21:break;default:at(e,t),mt(t)}}function mt(t){var e=t.flags;if(e&2){try{e:{for(var n=t.return;n!==null;){if(dg(n)){var i=n;break e}n=n.return}throw Error(E(160))}switch(i.tag){case 5:var s=i.stateNode;i.flags&32&&(vs(s,""),i.flags&=-33);var r=yd(t);Fl(t,r,s);break;case 3:case 4:var o=i.stateNode.containerInfo,a=yd(t);Il(t,a,o);break;default:throw Error(E(161))}}catch(l){ae(t,t.return,l)}t.flags&=-3}e&4096&&(t.flags&=-4097)}function Jx(t,e,n){L=t,pg(t)}function pg(t,e,n){for(var i=(t.mode&1)!==0;L!==null;){var s=L,r=s.child;if(s.tag===22&&i){var o=s.memoizedState!==null||vr;if(!o){var a=s.alternate,l=a!==null&&a.memoizedState!==null||Me;a=vr;var u=Me;if(vr=o,(Me=l)&&!u)for(L=s;L!==null;)o=L,l=o.child,o.tag===22&&o.memoizedState!==null?wd(s):l!==null?(l.return=o,L=l):wd(s);for(;r!==null;)L=r,pg(r),r=r.sibling;L=s,vr=a,Me=u}vd(t)}else s.subtreeFlags&8772&&r!==null?(r.return=s,L=r):vd(t)}}function vd(t){for(;L!==null;){var e=L;if(e.flags&8772){var n=e.alternate;try{if(e.flags&8772)switch(e.tag){case 0:case 11:case 15:Me||Xo(5,e);break;case 1:var i=e.stateNode;if(e.flags&4&&!Me)if(n===null)i.componentDidMount();else{var s=e.elementType===e.type?n.memoizedProps:lt(e.type,n.memoizedProps);i.componentDidUpdate(s,n.memoizedState,i.__reactInternalSnapshotBeforeUpdate)}var r=e.updateQueue;r!==null&&id(e,r,i);break;case 3:var o=e.updateQueue;if(o!==null){if(n=null,e.child!==null)switch(e.child.tag){case 5:n=e.child.stateNode;break;case 1:n=e.child.stateNode}id(e,o,n)}break;case 5:var a=e.stateNode;if(n===null&&e.flags&4){n=a;var l=e.memoizedProps;switch(e.type){case"button":case"input":case"select":case"textarea":l.autoFocus&&n.focus();break;case"img":l.src&&(n.src=l.src)}}break;case 6:break;case 4:break;case 12:break;case 13:if(e.memoizedState===null){var u=e.alternate;if(u!==null){var c=u.memoizedState;if(c!==null){var d=c.dehydrated;d!==null&&bs(d)}}}break;case 19:case 17:case 21:case 22:case 23:case 25:break;default:throw Error(E(163))}Me||e.flags&512&&Al(e)}catch(f){ae(e,e.return,f)}}if(e===t){L=null;break}if(n=e.sibling,n!==null){n.return=e.return,L=n;break}L=e.return}}function _d(t){for(;L!==null;){var e=L;if(e===t){L=null;break}var n=e.sibling;if(n!==null){n.return=e.return,L=n;break}L=e.return}}function wd(t){for(;L!==null;){var e=L;try{switch(e.tag){case 0:case 11:case 15:var n=e.return;try{Xo(4,e)}catch(l){ae(e,n,l)}break;case 1:var i=e.stateNode;if(typeof i.componentDidMount=="function"){var s=e.return;try{i.componentDidMount()}catch(l){ae(e,s,l)}}var r=e.return;try{Al(e)}catch(l){ae(e,r,l)}break;case 5:var o=e.return;try{Al(e)}catch(l){ae(e,o,l)}}}catch(l){ae(e,e.return,l)}if(e===t){L=null;break}var a=e.sibling;if(a!==null){a.return=e.return,L=a;break}L=e.return}}var Zx=Math.ceil,_o=Bt.ReactCurrentDispatcher,Hu=Bt.ReactCurrentOwner,nt=Bt.ReactCurrentBatchConfig,K=0,ve=null,fe=null,we=0,We=0,ui=pn(0),ge=0,Ds=null,An=0,Qo=0,Wu=0,cs=null,Fe=null,Uu=0,ki=1/0,Ot=null,wo=!1,zl=null,rn=null,_r=!1,Xt=null,So=0,ds=0,Bl=null,Kr=-1,Yr=0;function De(){return K&6?ce():Kr!==-1?Kr:Kr=ce()}function on(t){return t.mode&1?K&2&&we!==0?we&-we:Ax.transition!==null?(Yr===0&&(Yr=Jh()),Yr):(t=G,t!==0||(t=window.event,t=t===void 0?16:rp(t.type)),t):1}function ht(t,e,n,i){if(50<ds)throw ds=0,Bl=null,Error(E(185));qs(t,n,i),(!(K&2)||t!==ve)&&(t===ve&&(!(K&2)&&(Qo|=n),ge===4&&Kt(t,we)),He(t,i),n===1&&K===0&&!(e.mode&1)&&(ki=ce()+500,Uo&&gn()))}function He(t,e){var n=t.callbackNode;Ay(t,e);var i=so(t,t===ve?we:0);if(i===0)n!==null&&Nc(n),t.callbackNode=null,t.callbackPriority=0;else if(e=i&-i,t.callbackPriority!==e){if(n!=null&&Nc(n),e===1)t.tag===0?jx(Sd.bind(null,t)):kp(Sd.bind(null,t)),Lx(function(){!(K&6)&&gn()}),n=null;else{switch(Zh(i)){case 1:n=yu;break;case 4:n=qh;break;case 16:n=io;break;case 536870912:n=Gh;break;default:n=io}n=Sg(n,gg.bind(null,t))}t.callbackPriority=e,t.callbackNode=n}}function gg(t,e){if(Kr=-1,Yr=0,K&6)throw Error(E(327));var n=t.callbackNode;if(gi()&&t.callbackNode!==n)return null;var i=so(t,t===ve?we:0);if(i===0)return null;if(i&30||i&t.expiredLanes||e)e=bo(t,i);else{e=i;var s=K;K|=2;var r=yg();(ve!==t||we!==e)&&(Ot=null,ki=ce()+500,En(t,e));do try{nv();break}catch(a){mg(t,a)}while(!0);Nu(),_o.current=r,K=s,fe!==null?e=0:(ve=null,we=0,e=ge)}if(e!==0){if(e===2&&(s=hl(t),s!==0&&(i=s,e=$l(t,s))),e===1)throw n=Ds,En(t,0),Kt(t,i),He(t,ce()),n;if(e===6)Kt(t,i);else{if(s=t.current.alternate,!(i&30)&&!ev(s)&&(e=bo(t,i),e===2&&(r=hl(t),r!==0&&(i=r,e=$l(t,r))),e===1))throw n=Ds,En(t,0),Kt(t,i),He(t,ce()),n;switch(t.finishedWork=s,t.finishedLanes=i,e){case 0:case 1:throw Error(E(345));case 2:wn(t,Fe,Ot);break;case 3:if(Kt(t,i),(i&130023424)===i&&(e=Uu+500-ce(),10<e)){if(so(t,0)!==0)break;if(s=t.suspendedLanes,(s&i)!==i){De(),t.pingedLanes|=t.suspendedLanes&s;break}t.timeoutHandle=wl(wn.bind(null,t,Fe,Ot),e);break}wn(t,Fe,Ot);break;case 4:if(Kt(t,i),(i&4194240)===i)break;for(e=t.eventTimes,s=-1;0<i;){var o=31-ft(i);r=1<<o,o=e[o],o>s&&(s=o),i&=~r}if(i=s,i=ce()-i,i=(120>i?120:480>i?480:1080>i?1080:1920>i?1920:3e3>i?3e3:4320>i?4320:1960*Zx(i/1960))-i,10<i){t.timeoutHandle=wl(wn.bind(null,t,Fe,Ot),i);break}wn(t,Fe,Ot);break;case 5:wn(t,Fe,Ot);break;default:throw Error(E(329))}}}return He(t,ce()),t.callbackNode===n?gg.bind(null,t):null}function $l(t,e){var n=cs;return t.current.memoizedState.isDehydrated&&(En(t,e).flags|=256),t=bo(t,e),t!==2&&(e=Fe,Fe=n,e!==null&&Vl(e)),t}function Vl(t){Fe===null?Fe=t:Fe.push.apply(Fe,t)}function ev(t){for(var e=t;;){if(e.flags&16384){var n=e.updateQueue;if(n!==null&&(n=n.stores,n!==null))for(var i=0;i<n.length;i++){var s=n[i],r=s.getSnapshot;s=s.value;try{if(!pt(r(),s))return!1}catch{return!1}}}if(n=e.child,e.subtreeFlags&16384&&n!==null)n.return=e,e=n;else{if(e===t)break;for(;e.sibling===null;){if(e.return===null||e.return===t)return!0;e=e.return}e.sibling.return=e.return,e=e.sibling}}return!0}function Kt(t,e){for(e&=~Wu,e&=~Qo,t.suspendedLanes|=e,t.pingedLanes&=~e,t=t.expirationTimes;0<e;){var n=31-ft(e),i=1<<n;t[n]=-1,e&=~i}}function Sd(t){if(K&6)throw Error(E(327));gi();var e=so(t,0);if(!(e&1))return He(t,ce()),null;var n=bo(t,e);if(t.tag!==0&&n===2){var i=hl(t);i!==0&&(e=i,n=$l(t,i))}if(n===1)throw n=Ds,En(t,0),Kt(t,e),He(t,ce()),n;if(n===6)throw Error(E(345));return t.finishedWork=t.current.alternate,t.finishedLanes=e,wn(t,Fe,Ot),He(t,ce()),null}function Ku(t,e){var n=K;K|=1;try{return t(e)}finally{K=n,K===0&&(ki=ce()+500,Uo&&gn())}}function In(t){Xt!==null&&Xt.tag===0&&!(K&6)&&gi();var e=K;K|=1;var n=nt.transition,i=G;try{if(nt.transition=null,G=1,t)return t()}finally{G=i,nt.transition=n,K=e,!(K&6)&&gn()}}function Yu(){We=ui.current,ne(ui)}function En(t,e){t.finishedWork=null,t.finishedLanes=0;var n=t.timeoutHandle;if(n!==-1&&(t.timeoutHandle=-1,Nx(n)),fe!==null)for(n=fe.return;n!==null;){var i=n;switch(Mu(i),i.tag){case 1:i=i.type.childContextTypes,i!=null&&uo();break;case 3:Si(),ne($e),ne(Ne),Au();break;case 5:ju(i);break;case 4:Si();break;case 13:ne(se);break;case 19:ne(se);break;case 10:Lu(i.type._context);break;case 22:case 23:Yu()}n=n.return}if(ve=t,fe=t=an(t.current,null),we=We=e,ge=0,Ds=null,Wu=Qo=An=0,Fe=cs=null,Cn!==null){for(e=0;e<Cn.length;e++)if(n=Cn[e],i=n.interleaved,i!==null){n.interleaved=null;var s=i.next,r=n.pending;if(r!==null){var o=r.next;r.next=s,i.next=o}n.pending=i}Cn=null}return t}function mg(t,e){do{var n=fe;try{if(Nu(),Hr.current=vo,xo){for(var i=re.memoizedState;i!==null;){var s=i.queue;s!==null&&(s.pending=null),i=i.next}xo=!1}if(jn=0,xe=he=re=null,ls=!1,Ls=0,Hu.current=null,n===null||n.return===null){ge=1,Ds=e,fe=null;break}e:{var r=t,o=n.return,a=n,l=e;if(e=we,a.flags|=32768,l!==null&&typeof l=="object"&&typeof l.then=="function"){var u=l,c=a,d=c.tag;if(!(c.mode&1)&&(d===0||d===11||d===15)){var f=c.alternate;f?(c.updateQueue=f.updateQueue,c.memoizedState=f.memoizedState,c.lanes=f.lanes):(c.updateQueue=null,c.memoizedState=null)}var h=ud(o);if(h!==null){h.flags&=-257,cd(h,o,a,r,e),h.mode&1&&ld(r,u,e),e=h,l=u;var p=e.updateQueue;if(p===null){var y=new Set;y.add(l),e.updateQueue=y}else p.add(l);break e}else{if(!(e&1)){ld(r,u,e),Xu();break e}l=Error(E(426))}}else if(ie&&a.mode&1){var v=ud(o);if(v!==null){!(v.flags&65536)&&(v.flags|=256),cd(v,o,a,r,e),Eu(bi(l,a));break e}}r=l=bi(l,a),ge!==4&&(ge=2),cs===null?cs=[r]:cs.push(r),r=o;do{switch(r.tag){case 3:r.flags|=65536,e&=-e,r.lanes|=e;var m=Zp(r,l,e);nd(r,m);break e;case 1:a=l;var g=r.type,x=r.stateNode;if(!(r.flags&128)&&(typeof g.getDerivedStateFromError=="function"||x!==null&&typeof x.componentDidCatch=="function"&&(rn===null||!rn.has(x)))){r.flags|=65536,e&=-e,r.lanes|=e;var w=eg(r,a,e);nd(r,w);break e}}r=r.return}while(r!==null)}vg(n)}catch(S){e=S,fe===n&&n!==null&&(fe=n=n.return);continue}break}while(!0)}function yg(){var t=_o.current;return _o.current=vo,t===null?vo:t}function Xu(){(ge===0||ge===3||ge===2)&&(ge=4),ve===null||!(An&268435455)&&!(Qo&268435455)||Kt(ve,we)}function bo(t,e){var n=K;K|=2;var i=yg();(ve!==t||we!==e)&&(Ot=null,En(t,e));do try{tv();break}catch(s){mg(t,s)}while(!0);if(Nu(),K=n,_o.current=i,fe!==null)throw Error(E(261));return ve=null,we=0,ge}function tv(){for(;fe!==null;)xg(fe)}function nv(){for(;fe!==null&&!My();)xg(fe)}function xg(t){var e=wg(t.alternate,t,We);t.memoizedProps=t.pendingProps,e===null?vg(t):fe=e,Hu.current=null}function vg(t){var e=t;do{var n=e.alternate;if(t=e.return,e.flags&32768){if(n=Qx(n,e),n!==null){n.flags&=32767,fe=n;return}if(t!==null)t.flags|=32768,t.subtreeFlags=0,t.deletions=null;else{ge=6,fe=null;return}}else if(n=Xx(n,e,We),n!==null){fe=n;return}if(e=e.sibling,e!==null){fe=e;return}fe=e=t}while(e!==null);ge===0&&(ge=5)}function wn(t,e,n){var i=G,s=nt.transition;try{nt.transition=null,G=1,iv(t,e,n,i)}finally{nt.transition=s,G=i}return null}function iv(t,e,n,i){do gi();while(Xt!==null);if(K&6)throw Error(E(327));n=t.finishedWork;var s=t.finishedLanes;if(n===null)return null;if(t.finishedWork=null,t.finishedLanes=0,n===t.current)throw Error(E(177));t.callbackNode=null,t.callbackPriority=0;var r=n.lanes|n.childLanes;if(Iy(t,r),t===ve&&(fe=ve=null,we=0),!(n.subtreeFlags&2064)&&!(n.flags&2064)||_r||(_r=!0,Sg(io,function(){return gi(),null})),r=(n.flags&15990)!==0,n.subtreeFlags&15990||r){r=nt.transition,nt.transition=null;var o=G;G=1;var a=K;K|=4,Hu.current=null,Gx(t,n),hg(n,t),bx(vl),ro=!!xl,vl=xl=null,t.current=n,Jx(n),Ey(),K=a,G=o,nt.transition=r}else t.current=n;if(_r&&(_r=!1,Xt=t,So=s),r=t.pendingLanes,r===0&&(rn=null),Ly(n.stateNode),He(t,ce()),e!==null)for(i=t.onRecoverableError,n=0;n<e.length;n++)s=e[n],i(s.value,{componentStack:s.stack,digest:s.digest});if(wo)throw wo=!1,t=zl,zl=null,t;return So&1&&t.tag!==0&&gi(),r=t.pendingLanes,r&1?t===Bl?ds++:(ds=0,Bl=t):ds=0,gn(),null}function gi(){if(Xt!==null){var t=Zh(So),e=nt.transition,n=G;try{if(nt.transition=null,G=16>t?16:t,Xt===null)var i=!1;else{if(t=Xt,Xt=null,So=0,K&6)throw Error(E(331));var s=K;for(K|=4,L=t.current;L!==null;){var r=L,o=r.child;if(L.flags&16){var a=r.deletions;if(a!==null){for(var l=0;l<a.length;l++){var u=a[l];for(L=u;L!==null;){var c=L;switch(c.tag){case 0:case 11:case 15:us(8,c,r)}var d=c.child;if(d!==null)d.return=c,L=d;else for(;L!==null;){c=L;var f=c.sibling,h=c.return;if(cg(c),c===u){L=null;break}if(f!==null){f.return=h,L=f;break}L=h}}}var p=r.alternate;if(p!==null){var y=p.child;if(y!==null){p.child=null;do{var v=y.sibling;y.sibling=null,y=v}while(y!==null)}}L=r}}if(r.subtreeFlags&2064&&o!==null)o.return=r,L=o;else e:for(;L!==null;){if(r=L,r.flags&2048)switch(r.tag){case 0:case 11:case 15:us(9,r,r.return)}var m=r.sibling;if(m!==null){m.return=r.return,L=m;break e}L=r.return}}var g=t.current;for(L=g;L!==null;){o=L;var x=o.child;if(o.subtreeFlags&2064&&x!==null)x.return=o,L=x;else e:for(o=g;L!==null;){if(a=L,a.flags&2048)try{switch(a.tag){case 0:case 11:case 15:Xo(9,a)}}catch(S){ae(a,a.return,S)}if(a===o){L=null;break e}var w=a.sibling;if(w!==null){w.return=a.return,L=w;break e}L=a.return}}if(K=s,gn(),bt&&typeof bt.onPostCommitFiberRoot=="function")try{bt.onPostCommitFiberRoot(Bo,t)}catch{}i=!0}return i}finally{G=n,nt.transition=e}}return!1}function bd(t,e,n){e=bi(n,e),e=Zp(t,e,1),t=sn(t,e,1),e=De(),t!==null&&(qs(t,1,e),He(t,e))}function ae(t,e,n){if(t.tag===3)bd(t,t,n);else for(;e!==null;){if(e.tag===3){bd(e,t,n);break}else if(e.tag===1){var i=e.stateNode;if(typeof e.type.getDerivedStateFromError=="function"||typeof i.componentDidCatch=="function"&&(rn===null||!rn.has(i))){t=bi(n,t),t=eg(e,t,1),e=sn(e,t,1),t=De(),e!==null&&(qs(e,1,t),He(e,t));break}}e=e.return}}function sv(t,e,n){var i=t.pingCache;i!==null&&i.delete(e),e=De(),t.pingedLanes|=t.suspendedLanes&n,ve===t&&(we&n)===n&&(ge===4||ge===3&&(we&130023424)===we&&500>ce()-Uu?En(t,0):Wu|=n),He(t,e)}function _g(t,e){e===0&&(t.mode&1?(e=cr,cr<<=1,!(cr&130023424)&&(cr=4194304)):e=1);var n=De();t=Ft(t,e),t!==null&&(qs(t,e,n),He(t,n))}function rv(t){var e=t.memoizedState,n=0;e!==null&&(n=e.retryLane),_g(t,n)}function ov(t,e){var n=0;switch(t.tag){case 13:var i=t.stateNode,s=t.memoizedState;s!==null&&(n=s.retryLane);break;case 19:i=t.stateNode;break;default:throw Error(E(314))}i!==null&&i.delete(e),_g(t,n)}var wg;wg=function(t,e,n){if(t!==null)if(t.memoizedProps!==e.pendingProps||$e.current)Be=!0;else{if(!(t.lanes&n)&&!(e.flags&128))return Be=!1,Yx(t,e,n);Be=!!(t.flags&131072)}else Be=!1,ie&&e.flags&1048576&&Cp(e,ho,e.index);switch(e.lanes=0,e.tag){case 2:var i=e.type;Ur(t,e),t=e.pendingProps;var s=vi(e,Ne.current);pi(e,n),s=Fu(null,e,i,t,s,n);var r=zu();return e.flags|=1,typeof s=="object"&&s!==null&&typeof s.render=="function"&&s.$$typeof===void 0?(e.tag=1,e.memoizedState=null,e.updateQueue=null,Ve(i)?(r=!0,co(e)):r=!1,e.memoizedState=s.state!==null&&s.state!==void 0?s.state:null,Ru(e),s.updater=Yo,e.stateNode=s,s._reactInternals=e,El(e,i,t,n),e=Ll(null,e,i,!0,r,n)):(e.tag=0,ie&&r&&Pu(e),Re(null,e,s,n),e=e.child),e;case 16:i=e.elementType;e:{switch(Ur(t,e),t=e.pendingProps,s=i._init,i=s(i._payload),e.type=i,s=e.tag=lv(i),t=lt(i,t),s){case 0:e=Nl(null,e,i,t,n);break e;case 1:e=hd(null,e,i,t,n);break e;case 11:e=dd(null,e,i,t,n);break e;case 14:e=fd(null,e,i,lt(i.type,t),n);break e}throw Error(E(306,i,""))}return e;case 0:return i=e.type,s=e.pendingProps,s=e.elementType===i?s:lt(i,s),Nl(t,e,i,s,n);case 1:return i=e.type,s=e.pendingProps,s=e.elementType===i?s:lt(i,s),hd(t,e,i,s,n);case 3:e:{if(sg(e),t===null)throw Error(E(387));i=e.pendingProps,r=e.memoizedState,s=r.element,Lp(t,e),mo(e,i,null,n);var o=e.memoizedState;if(i=o.element,r.isDehydrated)if(r={element:i,isDehydrated:!1,cache:o.cache,pendingSuspenseBoundaries:o.pendingSuspenseBoundaries,transitions:o.transitions},e.updateQueue.baseState=r,e.memoizedState=r,e.flags&256){s=bi(Error(E(423)),e),e=pd(t,e,i,n,s);break e}else if(i!==s){s=bi(Error(E(424)),e),e=pd(t,e,i,n,s);break e}else for(Ue=nn(e.stateNode.containerInfo.firstChild),Ke=e,ie=!0,dt=null,n=Op(e,null,i,n),e.child=n;n;)n.flags=n.flags&-3|4096,n=n.sibling;else{if(_i(),i===s){e=zt(t,e,n);break e}Re(t,e,i,n)}e=e.child}return e;case 5:return Tp(e),t===null&&Cl(e),i=e.type,s=e.pendingProps,r=t!==null?t.memoizedProps:null,o=s.children,_l(i,s)?o=null:r!==null&&_l(i,r)&&(e.flags|=32),ig(t,e),Re(t,e,o,n),e.child;case 6:return t===null&&Cl(e),null;case 13:return rg(t,e,n);case 4:return Du(e,e.stateNode.containerInfo),i=e.pendingProps,t===null?e.child=wi(e,null,i,n):Re(t,e,i,n),e.child;case 11:return i=e.type,s=e.pendingProps,s=e.elementType===i?s:lt(i,s),dd(t,e,i,s,n);case 7:return Re(t,e,e.pendingProps,n),e.child;case 8:return Re(t,e,e.pendingProps.children,n),e.child;case 12:return Re(t,e,e.pendingProps.children,n),e.child;case 10:e:{if(i=e.type._context,s=e.pendingProps,r=e.memoizedProps,o=s.value,Z(po,i._currentValue),i._currentValue=o,r!==null)if(pt(r.value,o)){if(r.children===s.children&&!$e.current){e=zt(t,e,n);break e}}else for(r=e.child,r!==null&&(r.return=e);r!==null;){var a=r.dependencies;if(a!==null){o=r.child;for(var l=a.firstContext;l!==null;){if(l.context===i){if(r.tag===1){l=jt(-1,n&-n),l.tag=2;var u=r.updateQueue;if(u!==null){u=u.shared;var c=u.pending;c===null?l.next=l:(l.next=c.next,c.next=l),u.pending=l}}r.lanes|=n,l=r.alternate,l!==null&&(l.lanes|=n),Pl(r.return,n,e),a.lanes|=n;break}l=l.next}}else if(r.tag===10)o=r.type===e.type?null:r.child;else if(r.tag===18){if(o=r.return,o===null)throw Error(E(341));o.lanes|=n,a=o.alternate,a!==null&&(a.lanes|=n),Pl(o,n,e),o=r.sibling}else o=r.child;if(o!==null)o.return=r;else for(o=r;o!==null;){if(o===e){o=null;break}if(r=o.sibling,r!==null){r.return=o.return,o=r;break}o=o.return}r=o}Re(t,e,s.children,n),e=e.child}return e;case 9:return s=e.type,i=e.pendingProps.children,pi(e,n),s=it(s),i=i(s),e.flags|=1,Re(t,e,i,n),e.child;case 14:return i=e.type,s=lt(i,e.pendingProps),s=lt(i.type,s),fd(t,e,i,s,n);case 15:return tg(t,e,e.type,e.pendingProps,n);case 17:return i=e.type,s=e.pendingProps,s=e.elementType===i?s:lt(i,s),Ur(t,e),e.tag=1,Ve(i)?(t=!0,co(e)):t=!1,pi(e,n),Jp(e,i,s),El(e,i,s,n),Ll(null,e,i,!0,t,n);case 19:return og(t,e,n);case 22:return ng(t,e,n)}throw Error(E(156,e.tag))};function Sg(t,e){return Qh(t,e)}function av(t,e,n,i){this.tag=t,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=e,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=i,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function et(t,e,n,i){return new av(t,e,n,i)}function Qu(t){return t=t.prototype,!(!t||!t.isReactComponent)}function lv(t){if(typeof t=="function")return Qu(t)?1:0;if(t!=null){if(t=t.$$typeof,t===pu)return 11;if(t===gu)return 14}return 2}function an(t,e){var n=t.alternate;return n===null?(n=et(t.tag,e,t.key,t.mode),n.elementType=t.elementType,n.type=t.type,n.stateNode=t.stateNode,n.alternate=t,t.alternate=n):(n.pendingProps=e,n.type=t.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=t.flags&14680064,n.childLanes=t.childLanes,n.lanes=t.lanes,n.child=t.child,n.memoizedProps=t.memoizedProps,n.memoizedState=t.memoizedState,n.updateQueue=t.updateQueue,e=t.dependencies,n.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext},n.sibling=t.sibling,n.index=t.index,n.ref=t.ref,n}function Xr(t,e,n,i,s,r){var o=2;if(i=t,typeof t=="function")Qu(t)&&(o=1);else if(typeof t=="string")o=5;else e:switch(t){case Zn:return On(n.children,s,r,e);case hu:o=8,s|=8;break;case Ja:return t=et(12,n,e,s|2),t.elementType=Ja,t.lanes=r,t;case Za:return t=et(13,n,e,s),t.elementType=Za,t.lanes=r,t;case el:return t=et(19,n,e,s),t.elementType=el,t.lanes=r,t;case Th:return qo(n,s,r,e);default:if(typeof t=="object"&&t!==null)switch(t.$$typeof){case Nh:o=10;break e;case Lh:o=9;break e;case pu:o=11;break e;case gu:o=14;break e;case Ht:o=16,i=null;break e}throw Error(E(130,t==null?t:typeof t,""))}return e=et(o,n,e,s),e.elementType=t,e.type=i,e.lanes=r,e}function On(t,e,n,i){return t=et(7,t,i,e),t.lanes=n,t}function qo(t,e,n,i){return t=et(22,t,i,e),t.elementType=Th,t.lanes=n,t.stateNode={isHidden:!1},t}function Da(t,e,n){return t=et(6,t,null,e),t.lanes=n,t}function ja(t,e,n){return e=et(4,t.children!==null?t.children:[],t.key,e),e.lanes=n,e.stateNode={containerInfo:t.containerInfo,pendingChildren:null,implementation:t.implementation},e}function uv(t,e,n,i,s){this.tag=e,this.containerInfo=t,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=ga(0),this.expirationTimes=ga(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=ga(0),this.identifierPrefix=i,this.onRecoverableError=s,this.mutableSourceEagerHydrationData=null}function qu(t,e,n,i,s,r,o,a,l){return t=new uv(t,e,n,a,l),e===1?(e=1,r===!0&&(e|=8)):e=0,r=et(3,null,null,e),t.current=r,r.stateNode=t,r.memoizedState={element:i,isDehydrated:n,cache:null,transitions:null,pendingSuspenseBoundaries:null},Ru(r),t}function cv(t,e,n){var i=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:Jn,key:i==null?null:""+i,children:t,containerInfo:e,implementation:n}}function bg(t){if(!t)return dn;t=t._reactInternals;e:{if($n(t)!==t||t.tag!==1)throw Error(E(170));var e=t;do{switch(e.tag){case 3:e=e.stateNode.context;break e;case 1:if(Ve(e.type)){e=e.stateNode.__reactInternalMemoizedMergedChildContext;break e}}e=e.return}while(e!==null);throw Error(E(171))}if(t.tag===1){var n=t.type;if(Ve(n))return bp(t,n,e)}return e}function kg(t,e,n,i,s,r,o,a,l){return t=qu(n,i,!0,t,s,r,o,a,l),t.context=bg(null),n=t.current,i=De(),s=on(n),r=jt(i,s),r.callback=e??null,sn(n,r,s),t.current.lanes=s,qs(t,s,i),He(t,i),t}function Go(t,e,n,i){var s=e.current,r=De(),o=on(s);return n=bg(n),e.context===null?e.context=n:e.pendingContext=n,e=jt(r,o),e.payload={element:t},i=i===void 0?null:i,i!==null&&(e.callback=i),t=sn(s,e,o),t!==null&&(ht(t,s,o,r),Vr(t,s,o)),o}function ko(t){if(t=t.current,!t.child)return null;switch(t.child.tag){case 5:return t.child.stateNode;default:return t.child.stateNode}}function kd(t,e){if(t=t.memoizedState,t!==null&&t.dehydrated!==null){var n=t.retryLane;t.retryLane=n!==0&&n<e?n:e}}function Gu(t,e){kd(t,e),(t=t.alternate)&&kd(t,e)}function dv(){return null}var Cg=typeof reportError=="function"?reportError:function(t){console.error(t)};function Ju(t){this._internalRoot=t}Jo.prototype.render=Ju.prototype.render=function(t){var e=this._internalRoot;if(e===null)throw Error(E(409));Go(t,e,null,null)};Jo.prototype.unmount=Ju.prototype.unmount=function(){var t=this._internalRoot;if(t!==null){this._internalRoot=null;var e=t.containerInfo;In(function(){Go(null,t,null,null)}),e[It]=null}};function Jo(t){this._internalRoot=t}Jo.prototype.unstable_scheduleHydration=function(t){if(t){var e=np();t={blockedOn:null,target:t,priority:e};for(var n=0;n<Ut.length&&e!==0&&e<Ut[n].priority;n++);Ut.splice(n,0,t),n===0&&sp(t)}};function Zu(t){return!(!t||t.nodeType!==1&&t.nodeType!==9&&t.nodeType!==11)}function Zo(t){return!(!t||t.nodeType!==1&&t.nodeType!==9&&t.nodeType!==11&&(t.nodeType!==8||t.nodeValue!==" react-mount-point-unstable "))}function Cd(){}function fv(t,e,n,i,s){if(s){if(typeof i=="function"){var r=i;i=function(){var u=ko(o);r.call(u)}}var o=kg(e,i,t,0,null,!1,!1,"",Cd);return t._reactRootContainer=o,t[It]=o.current,Ps(t.nodeType===8?t.parentNode:t),In(),o}for(;s=t.lastChild;)t.removeChild(s);if(typeof i=="function"){var a=i;i=function(){var u=ko(l);a.call(u)}}var l=qu(t,0,!1,null,null,!1,!1,"",Cd);return t._reactRootContainer=l,t[It]=l.current,Ps(t.nodeType===8?t.parentNode:t),In(function(){Go(e,l,n,i)}),l}function ea(t,e,n,i,s){var r=n._reactRootContainer;if(r){var o=r;if(typeof s=="function"){var a=s;s=function(){var l=ko(o);a.call(l)}}Go(e,o,t,s)}else o=fv(n,e,t,s,i);return ko(o)}ep=function(t){switch(t.tag){case 3:var e=t.stateNode;if(e.current.memoizedState.isDehydrated){var n=Qi(e.pendingLanes);n!==0&&(xu(e,n|1),He(e,ce()),!(K&6)&&(ki=ce()+500,gn()))}break;case 13:In(function(){var i=Ft(t,1);if(i!==null){var s=De();ht(i,t,1,s)}}),Gu(t,1)}};vu=function(t){if(t.tag===13){var e=Ft(t,134217728);if(e!==null){var n=De();ht(e,t,134217728,n)}Gu(t,134217728)}};tp=function(t){if(t.tag===13){var e=on(t),n=Ft(t,e);if(n!==null){var i=De();ht(n,t,e,i)}Gu(t,e)}};np=function(){return G};ip=function(t,e){var n=G;try{return G=t,e()}finally{G=n}};cl=function(t,e,n){switch(e){case"input":if(il(t,n),e=n.name,n.type==="radio"&&e!=null){for(n=t;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll("input[name="+JSON.stringify(""+e)+'][type="radio"]'),e=0;e<n.length;e++){var i=n[e];if(i!==t&&i.form===t.form){var s=Wo(i);if(!s)throw Error(E(90));Dh(i),il(i,s)}}}break;case"textarea":Ah(t,n);break;case"select":e=n.value,e!=null&&ci(t,!!n.multiple,e,!1)}};Hh=Ku;Wh=In;var hv={usingClientEntryPoint:!1,Events:[Js,ii,Wo,$h,Vh,Ku]},Bi={findFiberByHostInstance:kn,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},pv={bundleType:Bi.bundleType,version:Bi.version,rendererPackageName:Bi.rendererPackageName,rendererConfig:Bi.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:Bt.ReactCurrentDispatcher,findHostInstanceByFiber:function(t){return t=Yh(t),t===null?null:t.stateNode},findFiberByHostInstance:Bi.findFiberByHostInstance||dv,findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var wr=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!wr.isDisabled&&wr.supportsFiber)try{Bo=wr.inject(pv),bt=wr}catch{}}Xe.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=hv;Xe.createPortal=function(t,e){var n=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!Zu(e))throw Error(E(200));return cv(t,e,null,n)};Xe.createRoot=function(t,e){if(!Zu(t))throw Error(E(299));var n=!1,i="",s=Cg;return e!=null&&(e.unstable_strictMode===!0&&(n=!0),e.identifierPrefix!==void 0&&(i=e.identifierPrefix),e.onRecoverableError!==void 0&&(s=e.onRecoverableError)),e=qu(t,1,!1,null,null,n,!1,i,s),t[It]=e.current,Ps(t.nodeType===8?t.parentNode:t),new Ju(e)};Xe.findDOMNode=function(t){if(t==null)return null;if(t.nodeType===1)return t;var e=t._reactInternals;if(e===void 0)throw typeof t.render=="function"?Error(E(188)):(t=Object.keys(t).join(","),Error(E(268,t)));return t=Yh(e),t=t===null?null:t.stateNode,t};Xe.flushSync=function(t){return In(t)};Xe.hydrate=function(t,e,n){if(!Zo(e))throw Error(E(200));return ea(null,t,e,!0,n)};Xe.hydrateRoot=function(t,e,n){if(!Zu(t))throw Error(E(405));var i=n!=null&&n.hydratedSources||null,s=!1,r="",o=Cg;if(n!=null&&(n.unstable_strictMode===!0&&(s=!0),n.identifierPrefix!==void 0&&(r=n.identifierPrefix),n.onRecoverableError!==void 0&&(o=n.onRecoverableError)),e=kg(e,null,t,1,n??null,s,!1,r,o),t[It]=e.current,Ps(t),i)for(t=0;t<i.length;t++)n=i[t],s=n._getVersion,s=s(n._source),e.mutableSourceEagerHydrationData==null?e.mutableSourceEagerHydrationData=[n,s]:e.mutableSourceEagerHydrationData.push(n,s);return new Jo(e)};Xe.render=function(t,e,n){if(!Zo(e))throw Error(E(200));return ea(null,t,e,!1,n)};Xe.unmountComponentAtNode=function(t){if(!Zo(t))throw Error(E(40));return t._reactRootContainer?(In(function(){ea(null,null,t,!1,function(){t._reactRootContainer=null,t[It]=null})}),!0):!1};Xe.unstable_batchedUpdates=Ku;Xe.unstable_renderSubtreeIntoContainer=function(t,e,n,i){if(!Zo(n))throw Error(E(200));if(t==null||t._reactInternals===void 0)throw Error(E(38));return ea(t,e,n,!1,i)};Xe.version="18.3.1-next-f1338f8080-20240426";function Pg(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(Pg)}catch(t){console.error(t)}}Pg(),Ph.exports=Xe;var gv=Ph.exports,Pd=gv;qa.createRoot=Pd.createRoot,qa.hydrateRoot=Pd.hydrateRoot;/**
 * @remix-run/router v1.23.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function js(){return js=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(t[i]=n[i])}return t},js.apply(this,arguments)}var Qt;(function(t){t.Pop="POP",t.Push="PUSH",t.Replace="REPLACE"})(Qt||(Qt={}));const Md="popstate";function mv(t){t===void 0&&(t={});function e(i,s){let{pathname:r,search:o,hash:a}=i.location;return Hl("",{pathname:r,search:o,hash:a},s.state&&s.state.usr||null,s.state&&s.state.key||"default")}function n(i,s){return typeof s=="string"?s:Co(s)}return xv(e,n,null,t)}function ue(t,e){if(t===!1||t===null||typeof t>"u")throw new Error(e)}function Mg(t,e){if(!t){typeof console<"u"&&console.warn(e);try{throw new Error(e)}catch{}}}function yv(){return Math.random().toString(36).substr(2,8)}function Ed(t,e){return{usr:t.state,key:t.key,idx:e}}function Hl(t,e,n,i){return n===void 0&&(n=null),js({pathname:typeof t=="string"?t:t.pathname,search:"",hash:""},typeof e=="string"?Li(e):e,{state:n,key:e&&e.key||i||yv()})}function Co(t){let{pathname:e="/",search:n="",hash:i=""}=t;return n&&n!=="?"&&(e+=n.charAt(0)==="?"?n:"?"+n),i&&i!=="#"&&(e+=i.charAt(0)==="#"?i:"#"+i),e}function Li(t){let e={};if(t){let n=t.indexOf("#");n>=0&&(e.hash=t.substr(n),t=t.substr(0,n));let i=t.indexOf("?");i>=0&&(e.search=t.substr(i),t=t.substr(0,i)),t&&(e.pathname=t)}return e}function xv(t,e,n,i){i===void 0&&(i={});let{window:s=document.defaultView,v5Compat:r=!1}=i,o=s.history,a=Qt.Pop,l=null,u=c();u==null&&(u=0,o.replaceState(js({},o.state,{idx:u}),""));function c(){return(o.state||{idx:null}).idx}function d(){a=Qt.Pop;let v=c(),m=v==null?null:v-u;u=v,l&&l({action:a,location:y.location,delta:m})}function f(v,m){a=Qt.Push;let g=Hl(y.location,v,m);u=c()+1;let x=Ed(g,u),w=y.createHref(g);try{o.pushState(x,"",w)}catch(S){if(S instanceof DOMException&&S.name==="DataCloneError")throw S;s.location.assign(w)}r&&l&&l({action:a,location:y.location,delta:1})}function h(v,m){a=Qt.Replace;let g=Hl(y.location,v,m);u=c();let x=Ed(g,u),w=y.createHref(g);o.replaceState(x,"",w),r&&l&&l({action:a,location:y.location,delta:0})}function p(v){let m=s.location.origin!=="null"?s.location.origin:s.location.href,g=typeof v=="string"?v:Co(v);return g=g.replace(/ $/,"%20"),ue(m,"No window.location.(origin|href) available to create URL for href: "+g),new URL(g,m)}let y={get action(){return a},get location(){return t(s,o)},listen(v){if(l)throw new Error("A history only accepts one active listener");return s.addEventListener(Md,d),l=v,()=>{s.removeEventListener(Md,d),l=null}},createHref(v){return e(s,v)},createURL:p,encodeLocation(v){let m=p(v);return{pathname:m.pathname,search:m.search,hash:m.hash}},push:f,replace:h,go(v){return o.go(v)}};return y}var Od;(function(t){t.data="data",t.deferred="deferred",t.redirect="redirect",t.error="error"})(Od||(Od={}));function vv(t,e,n){return n===void 0&&(n="/"),_v(t,e,n)}function _v(t,e,n,i){let s=typeof e=="string"?Li(e):e,r=Ci(s.pathname||"/",n);if(r==null)return null;let o=Eg(t);wv(o);let a=null;for(let l=0;a==null&&l<o.length;++l){let u=Tv(r);a=Nv(o[l],u)}return a}function Eg(t,e,n,i){e===void 0&&(e=[]),n===void 0&&(n=[]),i===void 0&&(i="");let s=(r,o,a)=>{let l={relativePath:a===void 0?r.path||"":a,caseSensitive:r.caseSensitive===!0,childrenIndex:o,route:r};l.relativePath.startsWith("/")&&(ue(l.relativePath.startsWith(i),'Absolute route path "'+l.relativePath+'" nested under path '+('"'+i+'" is not valid. An absolute child route path ')+"must start with the combined path of all its parent routes."),l.relativePath=l.relativePath.slice(i.length));let u=ln([i,l.relativePath]),c=n.concat(l);r.children&&r.children.length>0&&(ue(r.index!==!0,"Index routes must not have child routes. Please remove "+('all child routes from route path "'+u+'".')),Eg(r.children,e,c,u)),!(r.path==null&&!r.index)&&e.push({path:u,score:Ev(u,r.index),routesMeta:c})};return t.forEach((r,o)=>{var a;if(r.path===""||!((a=r.path)!=null&&a.includes("?")))s(r,o);else for(let l of Og(r.path))s(r,o,l)}),e}function Og(t){let e=t.split("/");if(e.length===0)return[];let[n,...i]=e,s=n.endsWith("?"),r=n.replace(/\?$/,"");if(i.length===0)return s?[r,""]:[r];let o=Og(i.join("/")),a=[];return a.push(...o.map(l=>l===""?r:[r,l].join("/"))),s&&a.push(...o),a.map(l=>t.startsWith("/")&&l===""?"/":l)}function wv(t){t.sort((e,n)=>e.score!==n.score?n.score-e.score:Ov(e.routesMeta.map(i=>i.childrenIndex),n.routesMeta.map(i=>i.childrenIndex)))}const Sv=/^:[\w-]+$/,bv=3,kv=2,Cv=1,Pv=10,Mv=-2,Nd=t=>t==="*";function Ev(t,e){let n=t.split("/"),i=n.length;return n.some(Nd)&&(i+=Mv),e&&(i+=kv),n.filter(s=>!Nd(s)).reduce((s,r)=>s+(Sv.test(r)?bv:r===""?Cv:Pv),i)}function Ov(t,e){return t.length===e.length&&t.slice(0,-1).every((i,s)=>i===e[s])?t[t.length-1]-e[e.length-1]:0}function Nv(t,e,n){let{routesMeta:i}=t,s={},r="/",o=[];for(let a=0;a<i.length;++a){let l=i[a],u=a===i.length-1,c=r==="/"?e:e.slice(r.length)||"/",d=Wl({path:l.relativePath,caseSensitive:l.caseSensitive,end:u},c),f=l.route;if(!d)return null;Object.assign(s,d.params),o.push({params:s,pathname:ln([r,d.pathname]),pathnameBase:Av(ln([r,d.pathnameBase])),route:f}),d.pathnameBase!=="/"&&(r=ln([r,d.pathnameBase]))}return o}function Wl(t,e){typeof t=="string"&&(t={path:t,caseSensitive:!1,end:!0});let[n,i]=Lv(t.path,t.caseSensitive,t.end),s=e.match(n);if(!s)return null;let r=s[0],o=r.replace(/(.)\/+$/,"$1"),a=s.slice(1);return{params:i.reduce((u,c,d)=>{let{paramName:f,isOptional:h}=c;if(f==="*"){let y=a[d]||"";o=r.slice(0,r.length-y.length).replace(/(.)\/+$/,"$1")}const p=a[d];return h&&!p?u[f]=void 0:u[f]=(p||"").replace(/%2F/g,"/"),u},{}),pathname:r,pathnameBase:o,pattern:t}}function Lv(t,e,n){e===void 0&&(e=!1),n===void 0&&(n=!0),Mg(t==="*"||!t.endsWith("*")||t.endsWith("/*"),'Route path "'+t+'" will be treated as if it were '+('"'+t.replace(/\*$/,"/*")+'" because the `*` character must ')+"always follow a `/` in the pattern. To get rid of this warning, "+('please change the route path to "'+t.replace(/\*$/,"/*")+'".'));let i=[],s="^"+t.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,(o,a,l)=>(i.push({paramName:a,isOptional:l!=null}),l?"/?([^\\/]+)?":"/([^\\/]+)"));return t.endsWith("*")?(i.push({paramName:"*"}),s+=t==="*"||t==="/*"?"(.*)$":"(?:\\/(.+)|\\/*)$"):n?s+="\\/*$":t!==""&&t!=="/"&&(s+="(?:(?=\\/|$))"),[new RegExp(s,e?void 0:"i"),i]}function Tv(t){try{return t.split("/").map(e=>decodeURIComponent(e).replace(/\//g,"%2F")).join("/")}catch(e){return Mg(!1,'The URL path "'+t+'" could not be decoded because it is is a malformed URL segment. This is probably due to a bad percent '+("encoding ("+e+").")),t}}function Ci(t,e){if(e==="/")return t;if(!t.toLowerCase().startsWith(e.toLowerCase()))return null;let n=e.endsWith("/")?e.length-1:e.length,i=t.charAt(n);return i&&i!=="/"?null:t.slice(n)||"/"}function Rv(t,e){e===void 0&&(e="/");let{pathname:n,search:i="",hash:s=""}=typeof t=="string"?Li(t):t;return{pathname:n?n.startsWith("/")?n:Dv(n,e):e,search:Iv(i),hash:Fv(s)}}function Dv(t,e){let n=e.replace(/\/+$/,"").split("/");return t.split("/").forEach(s=>{s===".."?n.length>1&&n.pop():s!=="."&&n.push(s)}),n.length>1?n.join("/"):"/"}function Aa(t,e,n,i){return"Cannot include a '"+t+"' character in a manually specified "+("`to."+e+"` field ["+JSON.stringify(i)+"].  Please separate it out to the ")+("`to."+n+"` field. Alternatively you may provide the full path as ")+'a string in <Link to="..."> and the router will parse it for you.'}function jv(t){return t.filter((e,n)=>n===0||e.route.path&&e.route.path.length>0)}function Ng(t,e){let n=jv(t);return e?n.map((i,s)=>s===n.length-1?i.pathname:i.pathnameBase):n.map(i=>i.pathnameBase)}function Lg(t,e,n,i){i===void 0&&(i=!1);let s;typeof t=="string"?s=Li(t):(s=js({},t),ue(!s.pathname||!s.pathname.includes("?"),Aa("?","pathname","search",s)),ue(!s.pathname||!s.pathname.includes("#"),Aa("#","pathname","hash",s)),ue(!s.search||!s.search.includes("#"),Aa("#","search","hash",s)));let r=t===""||s.pathname==="",o=r?"/":s.pathname,a;if(o==null)a=n;else{let d=e.length-1;if(!i&&o.startsWith("..")){let f=o.split("/");for(;f[0]==="..";)f.shift(),d-=1;s.pathname=f.join("/")}a=d>=0?e[d]:"/"}let l=Rv(s,a),u=o&&o!=="/"&&o.endsWith("/"),c=(r||o===".")&&n.endsWith("/");return!l.pathname.endsWith("/")&&(u||c)&&(l.pathname+="/"),l}const ln=t=>t.join("/").replace(/\/\/+/g,"/"),Av=t=>t.replace(/\/+$/,"").replace(/^\/*/,"/"),Iv=t=>!t||t==="?"?"":t.startsWith("?")?t:"?"+t,Fv=t=>!t||t==="#"?"":t.startsWith("#")?t:"#"+t;function zv(t){return t!=null&&typeof t.status=="number"&&typeof t.statusText=="string"&&typeof t.internal=="boolean"&&"data"in t}const Tg=["post","put","patch","delete"];new Set(Tg);const Bv=["get",...Tg];new Set(Bv);/**
 * React Router v6.30.1
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function As(){return As=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(t[i]=n[i])}return t},As.apply(this,arguments)}const ta=O.createContext(null),Rg=O.createContext(null),mn=O.createContext(null),na=O.createContext(null),Vn=O.createContext({outlet:null,matches:[],isDataRoute:!1}),Dg=O.createContext(null);function $v(t,e){let{relative:n}=e===void 0?{}:e;er()||ue(!1);let{basename:i,navigator:s}=O.useContext(mn),{hash:r,pathname:o,search:a}=ia(t,{relative:n}),l=o;return i!=="/"&&(l=o==="/"?i:ln([i,o])),s.createHref({pathname:l,search:a,hash:r})}function er(){return O.useContext(na)!=null}function tr(){return er()||ue(!1),O.useContext(na).location}function jg(t){O.useContext(mn).static||O.useLayoutEffect(t)}function Vv(){let{isDataRoute:t}=O.useContext(Vn);return t?t0():Hv()}function Hv(){er()||ue(!1);let t=O.useContext(ta),{basename:e,future:n,navigator:i}=O.useContext(mn),{matches:s}=O.useContext(Vn),{pathname:r}=tr(),o=JSON.stringify(Ng(s,n.v7_relativeSplatPath)),a=O.useRef(!1);return jg(()=>{a.current=!0}),O.useCallback(function(u,c){if(c===void 0&&(c={}),!a.current)return;if(typeof u=="number"){i.go(u);return}let d=Lg(u,JSON.parse(o),r,c.relative==="path");t==null&&e!=="/"&&(d.pathname=d.pathname==="/"?e:ln([e,d.pathname])),(c.replace?i.replace:i.push)(d,c.state,c)},[e,i,o,r,t])}function ia(t,e){let{relative:n}=e===void 0?{}:e,{future:i}=O.useContext(mn),{matches:s}=O.useContext(Vn),{pathname:r}=tr(),o=JSON.stringify(Ng(s,i.v7_relativeSplatPath));return O.useMemo(()=>Lg(t,JSON.parse(o),r,n==="path"),[t,o,r,n])}function Wv(t,e){return Uv(t,e)}function Uv(t,e,n,i){er()||ue(!1);let{navigator:s}=O.useContext(mn),{matches:r}=O.useContext(Vn),o=r[r.length-1],a=o?o.params:{};o&&o.pathname;let l=o?o.pathnameBase:"/";o&&o.route;let u=tr(),c;if(e){var d;let v=typeof e=="string"?Li(e):e;l==="/"||(d=v.pathname)!=null&&d.startsWith(l)||ue(!1),c=v}else c=u;let f=c.pathname||"/",h=f;if(l!=="/"){let v=l.replace(/^\//,"").split("/");h="/"+f.replace(/^\//,"").split("/").slice(v.length).join("/")}let p=vv(t,{pathname:h}),y=qv(p&&p.map(v=>Object.assign({},v,{params:Object.assign({},a,v.params),pathname:ln([l,s.encodeLocation?s.encodeLocation(v.pathname).pathname:v.pathname]),pathnameBase:v.pathnameBase==="/"?l:ln([l,s.encodeLocation?s.encodeLocation(v.pathnameBase).pathname:v.pathnameBase])})),r,n,i);return e&&y?O.createElement(na.Provider,{value:{location:As({pathname:"/",search:"",hash:"",state:null,key:"default"},c),navigationType:Qt.Pop}},y):y}function Kv(){let t=e0(),e=zv(t)?t.status+" "+t.statusText:t instanceof Error?t.message:JSON.stringify(t),n=t instanceof Error?t.stack:null,s={padding:"0.5rem",backgroundColor:"rgba(200,200,200, 0.5)"};return O.createElement(O.Fragment,null,O.createElement("h2",null,"Unexpected Application Error!"),O.createElement("h3",{style:{fontStyle:"italic"}},e),n?O.createElement("pre",{style:s},n):null,null)}const Yv=O.createElement(Kv,null);class Xv extends O.Component{constructor(e){super(e),this.state={location:e.location,revalidation:e.revalidation,error:e.error}}static getDerivedStateFromError(e){return{error:e}}static getDerivedStateFromProps(e,n){return n.location!==e.location||n.revalidation!=="idle"&&e.revalidation==="idle"?{error:e.error,location:e.location,revalidation:e.revalidation}:{error:e.error!==void 0?e.error:n.error,location:n.location,revalidation:e.revalidation||n.revalidation}}componentDidCatch(e,n){console.error("React Router caught the following error during render",e,n)}render(){return this.state.error!==void 0?O.createElement(Vn.Provider,{value:this.props.routeContext},O.createElement(Dg.Provider,{value:this.state.error,children:this.props.component})):this.props.children}}function Qv(t){let{routeContext:e,match:n,children:i}=t,s=O.useContext(ta);return s&&s.static&&s.staticContext&&(n.route.errorElement||n.route.ErrorBoundary)&&(s.staticContext._deepestRenderedBoundaryId=n.route.id),O.createElement(Vn.Provider,{value:e},i)}function qv(t,e,n,i){var s;if(e===void 0&&(e=[]),n===void 0&&(n=null),i===void 0&&(i=null),t==null){var r;if(!n)return null;if(n.errors)t=n.matches;else if((r=i)!=null&&r.v7_partialHydration&&e.length===0&&!n.initialized&&n.matches.length>0)t=n.matches;else return null}let o=t,a=(s=n)==null?void 0:s.errors;if(a!=null){let c=o.findIndex(d=>d.route.id&&(a==null?void 0:a[d.route.id])!==void 0);c>=0||ue(!1),o=o.slice(0,Math.min(o.length,c+1))}let l=!1,u=-1;if(n&&i&&i.v7_partialHydration)for(let c=0;c<o.length;c++){let d=o[c];if((d.route.HydrateFallback||d.route.hydrateFallbackElement)&&(u=c),d.route.id){let{loaderData:f,errors:h}=n,p=d.route.loader&&f[d.route.id]===void 0&&(!h||h[d.route.id]===void 0);if(d.route.lazy||p){l=!0,u>=0?o=o.slice(0,u+1):o=[o[0]];break}}}return o.reduceRight((c,d,f)=>{let h,p=!1,y=null,v=null;n&&(h=a&&d.route.id?a[d.route.id]:void 0,y=d.route.errorElement||Yv,l&&(u<0&&f===0?(n0("route-fallback"),p=!0,v=null):u===f&&(p=!0,v=d.route.hydrateFallbackElement||null)));let m=e.concat(o.slice(0,f+1)),g=()=>{let x;return h?x=y:p?x=v:d.route.Component?x=O.createElement(d.route.Component,null):d.route.element?x=d.route.element:x=c,O.createElement(Qv,{match:d,routeContext:{outlet:c,matches:m,isDataRoute:n!=null},children:x})};return n&&(d.route.ErrorBoundary||d.route.errorElement||f===0)?O.createElement(Xv,{location:n.location,revalidation:n.revalidation,component:y,error:h,children:g(),routeContext:{outlet:null,matches:m,isDataRoute:!0}}):g()},null)}var Ag=function(t){return t.UseBlocker="useBlocker",t.UseRevalidator="useRevalidator",t.UseNavigateStable="useNavigate",t}(Ag||{}),Ig=function(t){return t.UseBlocker="useBlocker",t.UseLoaderData="useLoaderData",t.UseActionData="useActionData",t.UseRouteError="useRouteError",t.UseNavigation="useNavigation",t.UseRouteLoaderData="useRouteLoaderData",t.UseMatches="useMatches",t.UseRevalidator="useRevalidator",t.UseNavigateStable="useNavigate",t.UseRouteId="useRouteId",t}(Ig||{});function Gv(t){let e=O.useContext(ta);return e||ue(!1),e}function Jv(t){let e=O.useContext(Rg);return e||ue(!1),e}function Zv(t){let e=O.useContext(Vn);return e||ue(!1),e}function Fg(t){let e=Zv(),n=e.matches[e.matches.length-1];return n.route.id||ue(!1),n.route.id}function e0(){var t;let e=O.useContext(Dg),n=Jv(),i=Fg();return e!==void 0?e:(t=n.errors)==null?void 0:t[i]}function t0(){let{router:t}=Gv(Ag.UseNavigateStable),e=Fg(Ig.UseNavigateStable),n=O.useRef(!1);return jg(()=>{n.current=!0}),O.useCallback(function(s,r){r===void 0&&(r={}),n.current&&(typeof s=="number"?t.navigate(s):t.navigate(s,As({fromRouteId:e},r)))},[t,e])}const Ld={};function n0(t,e,n){Ld[t]||(Ld[t]=!0)}function i0(t,e){t==null||t.v7_startTransition,t==null||t.v7_relativeSplatPath}function Gn(t){ue(!1)}function s0(t){let{basename:e="/",children:n=null,location:i,navigationType:s=Qt.Pop,navigator:r,static:o=!1,future:a}=t;er()&&ue(!1);let l=e.replace(/^\/*/,"/"),u=O.useMemo(()=>({basename:l,navigator:r,static:o,future:As({v7_relativeSplatPath:!1},a)}),[l,a,r,o]);typeof i=="string"&&(i=Li(i));let{pathname:c="/",search:d="",hash:f="",state:h=null,key:p="default"}=i,y=O.useMemo(()=>{let v=Ci(c,l);return v==null?null:{location:{pathname:v,search:d,hash:f,state:h,key:p},navigationType:s}},[l,c,d,f,h,p,s]);return y==null?null:O.createElement(mn.Provider,{value:u},O.createElement(na.Provider,{children:n,value:y}))}function r0(t){let{children:e,location:n}=t;return Wv(Ul(e),n)}new Promise(()=>{});function Ul(t,e){e===void 0&&(e=[]);let n=[];return O.Children.forEach(t,(i,s)=>{if(!O.isValidElement(i))return;let r=[...e,s];if(i.type===O.Fragment){n.push.apply(n,Ul(i.props.children,r));return}i.type!==Gn&&ue(!1),!i.props.index||!i.props.children||ue(!1);let o={id:i.props.id||r.join("-"),caseSensitive:i.props.caseSensitive,element:i.props.element,Component:i.props.Component,index:i.props.index,path:i.props.path,loader:i.props.loader,action:i.props.action,errorElement:i.props.errorElement,ErrorBoundary:i.props.ErrorBoundary,hasErrorBoundary:i.props.ErrorBoundary!=null||i.props.errorElement!=null,shouldRevalidate:i.props.shouldRevalidate,handle:i.props.handle,lazy:i.props.lazy};i.props.children&&(o.children=Ul(i.props.children,r)),n.push(o)}),n}/**
 * React Router DOM v6.30.1
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function Po(){return Po=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(t[i]=n[i])}return t},Po.apply(this,arguments)}function zg(t,e){if(t==null)return{};var n={},i=Object.keys(t),s,r;for(r=0;r<i.length;r++)s=i[r],!(e.indexOf(s)>=0)&&(n[s]=t[s]);return n}function o0(t){return!!(t.metaKey||t.altKey||t.ctrlKey||t.shiftKey)}function a0(t,e){return t.button===0&&(!e||e==="_self")&&!o0(t)}const l0=["onClick","relative","reloadDocument","replace","state","target","to","preventScrollReset","viewTransition"],u0=["aria-current","caseSensitive","className","end","style","to","viewTransition","children"],c0="6";try{window.__reactRouterVersion=c0}catch{}const d0=O.createContext({isTransitioning:!1}),f0="startTransition",Td=sy[f0];function h0(t){let{basename:e,children:n,future:i,window:s}=t,r=O.useRef();r.current==null&&(r.current=mv({window:s,v5Compat:!0}));let o=r.current,[a,l]=O.useState({action:o.action,location:o.location}),{v7_startTransition:u}=i||{},c=O.useCallback(d=>{u&&Td?Td(()=>l(d)):l(d)},[l,u]);return O.useLayoutEffect(()=>o.listen(c),[o,c]),O.useEffect(()=>i0(i),[i]),O.createElement(s0,{basename:e,children:n,location:a.location,navigationType:a.action,navigator:o,future:i})}const p0=typeof window<"u"&&typeof window.document<"u"&&typeof window.document.createElement<"u",g0=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,m0=O.forwardRef(function(e,n){let{onClick:i,relative:s,reloadDocument:r,replace:o,state:a,target:l,to:u,preventScrollReset:c,viewTransition:d}=e,f=zg(e,l0),{basename:h}=O.useContext(mn),p,y=!1;if(typeof u=="string"&&g0.test(u)&&(p=u,p0))try{let x=new URL(window.location.href),w=u.startsWith("//")?new URL(x.protocol+u):new URL(u),S=Ci(w.pathname,h);w.origin===x.origin&&S!=null?u=S+w.search+w.hash:y=!0}catch{}let v=$v(u,{relative:s}),m=v0(u,{replace:o,state:a,target:l,preventScrollReset:c,relative:s,viewTransition:d});function g(x){i&&i(x),x.defaultPrevented||m(x)}return O.createElement("a",Po({},f,{href:p||v,onClick:y||r?i:g,ref:n,target:l}))}),y0=O.forwardRef(function(e,n){let{"aria-current":i="page",caseSensitive:s=!1,className:r="",end:o=!1,style:a,to:l,viewTransition:u,children:c}=e,d=zg(e,u0),f=ia(l,{relative:d.relative}),h=tr(),p=O.useContext(Rg),{navigator:y,basename:v}=O.useContext(mn),m=p!=null&&_0(f)&&u===!0,g=y.encodeLocation?y.encodeLocation(f).pathname:f.pathname,x=h.pathname,w=p&&p.navigation&&p.navigation.location?p.navigation.location.pathname:null;s||(x=x.toLowerCase(),w=w?w.toLowerCase():null,g=g.toLowerCase()),w&&v&&(w=Ci(w,v)||w);const S=g!=="/"&&g.endsWith("/")?g.length-1:g.length;let C=x===g||!o&&x.startsWith(g)&&x.charAt(S)==="/",k=w!=null&&(w===g||!o&&w.startsWith(g)&&w.charAt(g.length)==="/"),b={isActive:C,isPending:k,isTransitioning:m},M=C?i:void 0,P;typeof r=="function"?P=r(b):P=[r,C?"active":null,k?"pending":null,m?"transitioning":null].filter(Boolean).join(" ");let T=typeof a=="function"?a(b):a;return O.createElement(m0,Po({},d,{"aria-current":M,className:P,ref:n,style:T,to:l,viewTransition:u}),typeof c=="function"?c(b):c)});var Kl;(function(t){t.UseScrollRestoration="useScrollRestoration",t.UseSubmit="useSubmit",t.UseSubmitFetcher="useSubmitFetcher",t.UseFetcher="useFetcher",t.useViewTransitionState="useViewTransitionState"})(Kl||(Kl={}));var Rd;(function(t){t.UseFetcher="useFetcher",t.UseFetchers="useFetchers",t.UseScrollRestoration="useScrollRestoration"})(Rd||(Rd={}));function x0(t){let e=O.useContext(ta);return e||ue(!1),e}function v0(t,e){let{target:n,replace:i,state:s,preventScrollReset:r,relative:o,viewTransition:a}=e===void 0?{}:e,l=Vv(),u=tr(),c=ia(t,{relative:o});return O.useCallback(d=>{if(a0(d,n)){d.preventDefault();let f=i!==void 0?i:Co(u)===Co(c);l(t,{replace:f,state:s,preventScrollReset:r,relative:o,viewTransition:a})}},[u,l,c,i,s,n,t,r,o,a])}function _0(t,e){e===void 0&&(e={});let n=O.useContext(d0);n==null&&ue(!1);let{basename:i}=x0(Kl.useViewTransitionState),s=ia(t,{relative:e.relative});if(!n.isTransitioning)return!1;let r=Ci(n.currentLocation.pathname,i)||n.currentLocation.pathname,o=Ci(n.nextLocation.pathname,i)||n.nextLocation.pathname;return Wl(s.pathname,o)!=null||Wl(s.pathname,r)!=null}const w0=(t,e,n,i)=>{var r,o,a,l;const s=[n,{code:e,...i||{}}];if((o=(r=t==null?void 0:t.services)==null?void 0:r.logger)!=null&&o.forward)return t.services.logger.forward(s,"warn","react-i18next::",!0);Nn(s[0])&&(s[0]=`react-i18next:: ${s[0]}`),(l=(a=t==null?void 0:t.services)==null?void 0:a.logger)!=null&&l.warn?t.services.logger.warn(...s):console!=null&&console.warn&&console.warn(...s)},Dd={},Yl=(t,e,n,i)=>{Nn(n)&&Dd[n]||(Nn(n)&&(Dd[n]=new Date),w0(t,e,n,i))},Bg=(t,e)=>()=>{if(t.isInitialized)e();else{const n=()=>{setTimeout(()=>{t.off("initialized",n)},0),e()};t.on("initialized",n)}},Xl=(t,e,n)=>{t.loadNamespaces(e,Bg(t,n))},jd=(t,e,n,i)=>{if(Nn(n)&&(n=[n]),t.options.preload&&t.options.preload.indexOf(e)>-1)return Xl(t,n,i);n.forEach(s=>{t.options.ns.indexOf(s)<0&&t.options.ns.push(s)}),t.loadLanguages(e,Bg(t,i))},S0=(t,e,n={})=>!e.languages||!e.languages.length?(Yl(e,"NO_LANGUAGES","i18n.languages were undefined or empty",{languages:e.languages}),!0):e.hasLoadedNamespace(t,{lng:n.lng,precheck:(i,s)=>{var r;if(((r=n.bindI18n)==null?void 0:r.indexOf("languageChanging"))>-1&&i.services.backendConnector.backend&&i.isLanguageChangingTo&&!s(i.isLanguageChangingTo,t))return!1}}),Nn=t=>typeof t=="string",b0=t=>typeof t=="object"&&t!==null,k0=/&(?:amp|#38|lt|#60|gt|#62|apos|#39|quot|#34|nbsp|#160|copy|#169|reg|#174|hellip|#8230|#x2F|#47);/g,C0={"&amp;":"&","&#38;":"&","&lt;":"<","&#60;":"<","&gt;":">","&#62;":">","&apos;":"'","&#39;":"'","&quot;":'"',"&#34;":'"',"&nbsp;":" ","&#160;":" ","&copy;":"©","&#169;":"©","&reg;":"®","&#174;":"®","&hellip;":"…","&#8230;":"…","&#x2F;":"/","&#47;":"/"},P0=t=>C0[t],M0=t=>t.replace(k0,P0);let Ql={bindI18n:"languageChanged",bindI18nStore:"",transEmptyNodeValue:"",transSupportBasicHtmlNodes:!0,transWrapTextNodes:"",transKeepBasicHtmlNodesFor:["br","strong","i","p"],useSuspense:!0,unescape:M0};const E0=(t={})=>{Ql={...Ql,...t}},O0=()=>Ql;let $g;const N0=t=>{$g=t},L0=()=>$g,T0={type:"3rdParty",init(t){E0(t.options.react),N0(t)}},R0=O.createContext();class D0{constructor(){this.usedNamespaces={}}addUsedNamespaces(e){e.forEach(n=>{this.usedNamespaces[n]||(this.usedNamespaces[n]=!0)})}getUsedNamespaces(){return Object.keys(this.usedNamespaces)}}const j0=(t,e)=>{const n=O.useRef();return O.useEffect(()=>{n.current=t},[t,e]),n.current},Vg=(t,e,n,i)=>t.getFixedT(e,n,i),A0=(t,e,n,i)=>O.useCallback(Vg(t,e,n,i),[t,e,n,i]),nr=(t,e={})=>{var w,S,C,k;const{i18n:n}=e,{i18n:i,defaultNS:s}=O.useContext(R0)||{},r=n||i||L0();if(r&&!r.reportNamespaces&&(r.reportNamespaces=new D0),!r){Yl(r,"NO_I18NEXT_INSTANCE","useTranslation: You will need to pass in an i18next instance by using initReactI18next");const b=(P,T)=>Nn(T)?T:b0(T)&&Nn(T.defaultValue)?T.defaultValue:Array.isArray(P)?P[P.length-1]:P,M=[b,{},!1];return M.t=b,M.i18n={},M.ready=!1,M}(w=r.options.react)!=null&&w.wait&&Yl(r,"DEPRECATED_OPTION","useTranslation: It seems you are still using the old wait option, you may migrate to the new useSuspense behaviour.");const o={...O0(),...r.options.react,...e},{useSuspense:a,keyPrefix:l}=o;let u=s||((S=r.options)==null?void 0:S.defaultNS);u=Nn(u)?[u]:u||["translation"],(k=(C=r.reportNamespaces).addUsedNamespaces)==null||k.call(C,u);const c=(r.isInitialized||r.initializedStoreOnce)&&u.every(b=>S0(b,r,o)),d=A0(r,e.lng||null,o.nsMode==="fallback"?u:u[0],l),f=()=>d,h=()=>Vg(r,e.lng||null,o.nsMode==="fallback"?u:u[0],l),[p,y]=O.useState(f);let v=u.join();e.lng&&(v=`${e.lng}${v}`);const m=j0(v),g=O.useRef(!0);O.useEffect(()=>{const{bindI18n:b,bindI18nStore:M}=o;g.current=!0,!c&&!a&&(e.lng?jd(r,e.lng,u,()=>{g.current&&y(h)}):Xl(r,u,()=>{g.current&&y(h)})),c&&m&&m!==v&&g.current&&y(h);const P=()=>{g.current&&y(h)};return b&&(r==null||r.on(b,P)),M&&(r==null||r.store.on(M,P)),()=>{g.current=!1,r&&(b==null||b.split(" ").forEach(T=>r.off(T,P))),M&&r&&M.split(" ").forEach(T=>r.store.off(T,P))}},[r,v]),O.useEffect(()=>{g.current&&c&&y(f)},[r,l,c]);const x=[p,r,c];if(x.t=p,x.i18n=r,x.ready=c,c||!c&&!a)return x;throw new Promise(b=>{e.lng?jd(r,e.lng,u,()=>b()):Xl(r,u,()=>b())})},I0=()=>{const{i18n:t}=nr(),e=[{code:"en",name:"EN",fullName:"English"},{code:"hi",name:"हिं",fullName:"हिंदी"}],n=i=>{t.changeLanguage(i)};return _.jsx("div",{className:"flex gap-2",children:e.map(i=>_.jsx("button",{onClick:()=>n(i.code),className:`px-3 py-1 rounded text-sm transition-colors ${t.language===i.code?"bg-accent text-accent-foreground font-medium":"bg-accent/20 text-accent hover:bg-accent/40"}`,title:i.fullName,children:i.name},i.code))})},F0=()=>{const{t}=nr();return _.jsxs("header",{className:"bg-primary text-primary-foreground p-4 flex items-center justify-between fixed w-[calc(100%-240px)] z-20 shadow-lg border-b border-border",children:[_.jsxs("div",{className:"flex items-center gap-4",children:[_.jsxs("div",{className:"flex items-center gap-2",children:[_.jsx("div",{className:"w-8 h-8 bg-primary-foreground text-primary rounded-full flex items-center justify-center font-bold text-sm",children:"IDX"}),_.jsxs("div",{children:[_.jsx("h1",{className:"text-lg font-semibold",children:t("dashboard.title")}),_.jsx("p",{className:"text-xs text-primary-foreground/70",children:t("dashboard.subtitle")})]})]}),_.jsx("div",{className:"bg-success text-success-foreground px-2 py-1 rounded text-xs font-medium animate-tactical-pulse",children:t("common.classified")})]}),_.jsxs("div",{className:"flex items-center gap-4",children:[_.jsxs("div",{className:"flex items-center gap-2",children:[_.jsx("div",{className:"w-2 h-2 bg-success rounded-full animate-pulse"}),_.jsx("span",{className:"text-sm",children:t("common.online")})]}),_.jsx("div",{className:"text-sm font-mono",children:new Date().toLocaleTimeString("en-IN")}),_.jsx(I0,{})]})]})},z0=()=>{const{t}=nr(),e=[{nameKey:"nav.dashboard",icon:"🏠",path:"/"},{nameKey:"nav.equipment",icon:"⚙️",path:"/equipment-status"},{nameKey:"nav.statistics",icon:"📊",path:"/mission-statistics"},{nameKey:"nav.results",icon:"🎯",path:"/simulation-results"},{nameKey:"nav.settings",icon:"⚙️",path:"/settings"}];return _.jsxs("nav",{className:"bg-card w-60 p-4 h-screen fixed z-10 shadow-lg border-r border-border",children:[_.jsxs("div",{className:"mb-6",children:[_.jsxs("div",{className:"flex items-center gap-2 mb-2",children:[_.jsx("div",{className:"w-8 h-8 bg-primary text-primary-foreground rounded-lg flex items-center justify-center font-bold text-sm",children:"D"}),_.jsxs("div",{children:[_.jsx("h2",{className:"text-lg font-bold text-card-foreground",children:"DRDO IDX"}),_.jsx("p",{className:"text-xs text-muted-foreground",children:t("footer.version")})]})]}),_.jsxs("div",{className:"bg-success/10 text-success px-2 py-1 rounded text-xs font-medium",children:["🔒 ",t("security.secureConnection")]})]}),_.jsx("ul",{className:"space-y-2",children:e.map(n=>_.jsx("li",{children:_.jsxs(y0,{to:n.path,className:({isActive:i})=>`flex items-center gap-3 p-3 rounded-lg transition-all duration-200 ${i?"bg-primary text-primary-foreground shadow-md":"text-card-foreground hover:bg-secondary hover:shadow-sm"}`,children:[_.jsx("span",{className:"text-lg",children:n.icon}),_.jsx("span",{className:"font-medium text-sm",children:t(n.nameKey)})]})},n.nameKey))}),_.jsx("div",{className:"absolute bottom-4 left-4 right-4",children:_.jsxs("div",{className:"bg-muted p-3 rounded-lg text-center",children:[_.jsx("div",{className:"text-xs text-muted-foreground",children:t("footer.copyright")}),_.jsxs("div",{className:"flex items-center justify-center mt-1 gap-1",children:[_.jsx("div",{className:"w-2 h-2 bg-success rounded-full animate-pulse"}),_.jsx("span",{className:"text-xs text-muted-foreground",children:t("common.encrypted")})]})]})})]})};/*!
 * @kurkle/color v0.3.4
 * https://github.com/kurkle/color#readme
 * (c) 2024 Jukka Kurkela
 * Released under the MIT License
 */function ir(t){return t+.5|0}const qt=(t,e,n)=>Math.max(Math.min(t,n),e);function Gi(t){return qt(ir(t*2.55),0,255)}function un(t){return qt(ir(t*255),0,255)}function Lt(t){return qt(ir(t/2.55)/100,0,1)}function Ad(t){return qt(ir(t*100),0,100)}const Ge={0:0,1:1,2:2,3:3,4:4,5:5,6:6,7:7,8:8,9:9,A:10,B:11,C:12,D:13,E:14,F:15,a:10,b:11,c:12,d:13,e:14,f:15},ql=[..."0123456789ABCDEF"],B0=t=>ql[t&15],$0=t=>ql[(t&240)>>4]+ql[t&15],Sr=t=>(t&240)>>4===(t&15),V0=t=>Sr(t.r)&&Sr(t.g)&&Sr(t.b)&&Sr(t.a);function H0(t){var e=t.length,n;return t[0]==="#"&&(e===4||e===5?n={r:255&Ge[t[1]]*17,g:255&Ge[t[2]]*17,b:255&Ge[t[3]]*17,a:e===5?Ge[t[4]]*17:255}:(e===7||e===9)&&(n={r:Ge[t[1]]<<4|Ge[t[2]],g:Ge[t[3]]<<4|Ge[t[4]],b:Ge[t[5]]<<4|Ge[t[6]],a:e===9?Ge[t[7]]<<4|Ge[t[8]]:255})),n}const W0=(t,e)=>t<255?e(t):"";function U0(t){var e=V0(t)?B0:$0;return t?"#"+e(t.r)+e(t.g)+e(t.b)+W0(t.a,e):void 0}const K0=/^(hsla?|hwb|hsv)\(\s*([-+.e\d]+)(?:deg)?[\s,]+([-+.e\d]+)%[\s,]+([-+.e\d]+)%(?:[\s,]+([-+.e\d]+)(%)?)?\s*\)$/;function Hg(t,e,n){const i=e*Math.min(n,1-n),s=(r,o=(r+t/30)%12)=>n-i*Math.max(Math.min(o-3,9-o,1),-1);return[s(0),s(8),s(4)]}function Y0(t,e,n){const i=(s,r=(s+t/60)%6)=>n-n*e*Math.max(Math.min(r,4-r,1),0);return[i(5),i(3),i(1)]}function X0(t,e,n){const i=Hg(t,1,.5);let s;for(e+n>1&&(s=1/(e+n),e*=s,n*=s),s=0;s<3;s++)i[s]*=1-e-n,i[s]+=e;return i}function Q0(t,e,n,i,s){return t===s?(e-n)/i+(e<n?6:0):e===s?(n-t)/i+2:(t-e)/i+4}function ec(t){const n=t.r/255,i=t.g/255,s=t.b/255,r=Math.max(n,i,s),o=Math.min(n,i,s),a=(r+o)/2;let l,u,c;return r!==o&&(c=r-o,u=a>.5?c/(2-r-o):c/(r+o),l=Q0(n,i,s,c,r),l=l*60+.5),[l|0,u||0,a]}function tc(t,e,n,i){return(Array.isArray(e)?t(e[0],e[1],e[2]):t(e,n,i)).map(un)}function nc(t,e,n){return tc(Hg,t,e,n)}function q0(t,e,n){return tc(X0,t,e,n)}function G0(t,e,n){return tc(Y0,t,e,n)}function Wg(t){return(t%360+360)%360}function J0(t){const e=K0.exec(t);let n=255,i;if(!e)return;e[5]!==i&&(n=e[6]?Gi(+e[5]):un(+e[5]));const s=Wg(+e[2]),r=+e[3]/100,o=+e[4]/100;return e[1]==="hwb"?i=q0(s,r,o):e[1]==="hsv"?i=G0(s,r,o):i=nc(s,r,o),{r:i[0],g:i[1],b:i[2],a:n}}function Z0(t,e){var n=ec(t);n[0]=Wg(n[0]+e),n=nc(n),t.r=n[0],t.g=n[1],t.b=n[2]}function e1(t){if(!t)return;const e=ec(t),n=e[0],i=Ad(e[1]),s=Ad(e[2]);return t.a<255?`hsla(${n}, ${i}%, ${s}%, ${Lt(t.a)})`:`hsl(${n}, ${i}%, ${s}%)`}const Id={x:"dark",Z:"light",Y:"re",X:"blu",W:"gr",V:"medium",U:"slate",A:"ee",T:"ol",S:"or",B:"ra",C:"lateg",D:"ights",R:"in",Q:"turquois",E:"hi",P:"ro",O:"al",N:"le",M:"de",L:"yello",F:"en",K:"ch",G:"arks",H:"ea",I:"ightg",J:"wh"},Fd={OiceXe:"f0f8ff",antiquewEte:"faebd7",aqua:"ffff",aquamarRe:"7fffd4",azuY:"f0ffff",beige:"f5f5dc",bisque:"ffe4c4",black:"0",blanKedOmond:"ffebcd",Xe:"ff",XeviTet:"8a2be2",bPwn:"a52a2a",burlywood:"deb887",caMtXe:"5f9ea0",KartYuse:"7fff00",KocTate:"d2691e",cSO:"ff7f50",cSnflowerXe:"6495ed",cSnsilk:"fff8dc",crimson:"dc143c",cyan:"ffff",xXe:"8b",xcyan:"8b8b",xgTMnPd:"b8860b",xWay:"a9a9a9",xgYF:"6400",xgYy:"a9a9a9",xkhaki:"bdb76b",xmagFta:"8b008b",xTivegYF:"556b2f",xSange:"ff8c00",xScEd:"9932cc",xYd:"8b0000",xsOmon:"e9967a",xsHgYF:"8fbc8f",xUXe:"483d8b",xUWay:"2f4f4f",xUgYy:"2f4f4f",xQe:"ced1",xviTet:"9400d3",dAppRk:"ff1493",dApskyXe:"bfff",dimWay:"696969",dimgYy:"696969",dodgerXe:"1e90ff",fiYbrick:"b22222",flSOwEte:"fffaf0",foYstWAn:"228b22",fuKsia:"ff00ff",gaRsbSo:"dcdcdc",ghostwEte:"f8f8ff",gTd:"ffd700",gTMnPd:"daa520",Way:"808080",gYF:"8000",gYFLw:"adff2f",gYy:"808080",honeyMw:"f0fff0",hotpRk:"ff69b4",RdianYd:"cd5c5c",Rdigo:"4b0082",ivSy:"fffff0",khaki:"f0e68c",lavFMr:"e6e6fa",lavFMrXsh:"fff0f5",lawngYF:"7cfc00",NmoncEffon:"fffacd",ZXe:"add8e6",ZcSO:"f08080",Zcyan:"e0ffff",ZgTMnPdLw:"fafad2",ZWay:"d3d3d3",ZgYF:"90ee90",ZgYy:"d3d3d3",ZpRk:"ffb6c1",ZsOmon:"ffa07a",ZsHgYF:"20b2aa",ZskyXe:"87cefa",ZUWay:"778899",ZUgYy:"778899",ZstAlXe:"b0c4de",ZLw:"ffffe0",lime:"ff00",limegYF:"32cd32",lRF:"faf0e6",magFta:"ff00ff",maPon:"800000",VaquamarRe:"66cdaa",VXe:"cd",VScEd:"ba55d3",VpurpN:"9370db",VsHgYF:"3cb371",VUXe:"7b68ee",VsprRggYF:"fa9a",VQe:"48d1cc",VviTetYd:"c71585",midnightXe:"191970",mRtcYam:"f5fffa",mistyPse:"ffe4e1",moccasR:"ffe4b5",navajowEte:"ffdead",navy:"80",Tdlace:"fdf5e6",Tive:"808000",TivedBb:"6b8e23",Sange:"ffa500",SangeYd:"ff4500",ScEd:"da70d6",pOegTMnPd:"eee8aa",pOegYF:"98fb98",pOeQe:"afeeee",pOeviTetYd:"db7093",papayawEp:"ffefd5",pHKpuff:"ffdab9",peru:"cd853f",pRk:"ffc0cb",plum:"dda0dd",powMrXe:"b0e0e6",purpN:"800080",YbeccapurpN:"663399",Yd:"ff0000",Psybrown:"bc8f8f",PyOXe:"4169e1",saddNbPwn:"8b4513",sOmon:"fa8072",sandybPwn:"f4a460",sHgYF:"2e8b57",sHshell:"fff5ee",siFna:"a0522d",silver:"c0c0c0",skyXe:"87ceeb",UXe:"6a5acd",UWay:"708090",UgYy:"708090",snow:"fffafa",sprRggYF:"ff7f",stAlXe:"4682b4",tan:"d2b48c",teO:"8080",tEstN:"d8bfd8",tomato:"ff6347",Qe:"40e0d0",viTet:"ee82ee",JHt:"f5deb3",wEte:"ffffff",wEtesmoke:"f5f5f5",Lw:"ffff00",LwgYF:"9acd32"};function t1(){const t={},e=Object.keys(Fd),n=Object.keys(Id);let i,s,r,o,a;for(i=0;i<e.length;i++){for(o=a=e[i],s=0;s<n.length;s++)r=n[s],a=a.replace(r,Id[r]);r=parseInt(Fd[o],16),t[a]=[r>>16&255,r>>8&255,r&255]}return t}let br;function n1(t){br||(br=t1(),br.transparent=[0,0,0,0]);const e=br[t.toLowerCase()];return e&&{r:e[0],g:e[1],b:e[2],a:e.length===4?e[3]:255}}const i1=/^rgba?\(\s*([-+.\d]+)(%)?[\s,]+([-+.e\d]+)(%)?[\s,]+([-+.e\d]+)(%)?(?:[\s,/]+([-+.e\d]+)(%)?)?\s*\)$/;function s1(t){const e=i1.exec(t);let n=255,i,s,r;if(e){if(e[7]!==i){const o=+e[7];n=e[8]?Gi(o):qt(o*255,0,255)}return i=+e[1],s=+e[3],r=+e[5],i=255&(e[2]?Gi(i):qt(i,0,255)),s=255&(e[4]?Gi(s):qt(s,0,255)),r=255&(e[6]?Gi(r):qt(r,0,255)),{r:i,g:s,b:r,a:n}}}function r1(t){return t&&(t.a<255?`rgba(${t.r}, ${t.g}, ${t.b}, ${Lt(t.a)})`:`rgb(${t.r}, ${t.g}, ${t.b})`)}const Ia=t=>t<=.0031308?t*12.92:Math.pow(t,1/2.4)*1.055-.055,Kn=t=>t<=.04045?t/12.92:Math.pow((t+.055)/1.055,2.4);function o1(t,e,n){const i=Kn(Lt(t.r)),s=Kn(Lt(t.g)),r=Kn(Lt(t.b));return{r:un(Ia(i+n*(Kn(Lt(e.r))-i))),g:un(Ia(s+n*(Kn(Lt(e.g))-s))),b:un(Ia(r+n*(Kn(Lt(e.b))-r))),a:t.a+n*(e.a-t.a)}}function kr(t,e,n){if(t){let i=ec(t);i[e]=Math.max(0,Math.min(i[e]+i[e]*n,e===0?360:1)),i=nc(i),t.r=i[0],t.g=i[1],t.b=i[2]}}function Ug(t,e){return t&&Object.assign(e||{},t)}function zd(t){var e={r:0,g:0,b:0,a:255};return Array.isArray(t)?t.length>=3&&(e={r:t[0],g:t[1],b:t[2],a:255},t.length>3&&(e.a=un(t[3]))):(e=Ug(t,{r:0,g:0,b:0,a:1}),e.a=un(e.a)),e}function a1(t){return t.charAt(0)==="r"?s1(t):J0(t)}class Is{constructor(e){if(e instanceof Is)return e;const n=typeof e;let i;n==="object"?i=zd(e):n==="string"&&(i=H0(e)||n1(e)||a1(e)),this._rgb=i,this._valid=!!i}get valid(){return this._valid}get rgb(){var e=Ug(this._rgb);return e&&(e.a=Lt(e.a)),e}set rgb(e){this._rgb=zd(e)}rgbString(){return this._valid?r1(this._rgb):void 0}hexString(){return this._valid?U0(this._rgb):void 0}hslString(){return this._valid?e1(this._rgb):void 0}mix(e,n){if(e){const i=this.rgb,s=e.rgb;let r;const o=n===r?.5:n,a=2*o-1,l=i.a-s.a,u=((a*l===-1?a:(a+l)/(1+a*l))+1)/2;r=1-u,i.r=255&u*i.r+r*s.r+.5,i.g=255&u*i.g+r*s.g+.5,i.b=255&u*i.b+r*s.b+.5,i.a=o*i.a+(1-o)*s.a,this.rgb=i}return this}interpolate(e,n){return e&&(this._rgb=o1(this._rgb,e._rgb,n)),this}clone(){return new Is(this.rgb)}alpha(e){return this._rgb.a=un(e),this}clearer(e){const n=this._rgb;return n.a*=1-e,this}greyscale(){const e=this._rgb,n=ir(e.r*.3+e.g*.59+e.b*.11);return e.r=e.g=e.b=n,this}opaquer(e){const n=this._rgb;return n.a*=1+e,this}negate(){const e=this._rgb;return e.r=255-e.r,e.g=255-e.g,e.b=255-e.b,this}lighten(e){return kr(this._rgb,2,e),this}darken(e){return kr(this._rgb,2,-e),this}saturate(e){return kr(this._rgb,1,e),this}desaturate(e){return kr(this._rgb,1,-e),this}rotate(e){return Z0(this._rgb,e),this}}/*!
 * Chart.js v4.5.0
 * https://www.chartjs.org
 * (c) 2025 Chart.js Contributors
 * Released under the MIT License
 */function Pt(){}const l1=(()=>{let t=0;return()=>t++})();function X(t){return t==null}function pe(t){if(Array.isArray&&Array.isArray(t))return!0;const e=Object.prototype.toString.call(t);return e.slice(0,7)==="[object"&&e.slice(-6)==="Array]"}function Y(t){return t!==null&&Object.prototype.toString.call(t)==="[object Object]"}function rt(t){return(typeof t=="number"||t instanceof Number)&&isFinite(+t)}function yt(t,e){return rt(t)?t:e}function B(t,e){return typeof t>"u"?e:t}const u1=(t,e)=>typeof t=="string"&&t.endsWith("%")?parseFloat(t)/100:+t/e,Kg=(t,e)=>typeof t=="string"&&t.endsWith("%")?parseFloat(t)/100*e:+t;function te(t,e,n){if(t&&typeof t.call=="function")return t.apply(n,e)}function Q(t,e,n,i){let s,r,o;if(pe(t))for(r=t.length,s=0;s<r;s++)e.call(n,t[s],s);else if(Y(t))for(o=Object.keys(t),r=o.length,s=0;s<r;s++)e.call(n,t[o[s]],o[s])}function Mo(t,e){let n,i,s,r;if(!t||!e||t.length!==e.length)return!1;for(n=0,i=t.length;n<i;++n)if(s=t[n],r=e[n],s.datasetIndex!==r.datasetIndex||s.index!==r.index)return!1;return!0}function Eo(t){if(pe(t))return t.map(Eo);if(Y(t)){const e=Object.create(null),n=Object.keys(t),i=n.length;let s=0;for(;s<i;++s)e[n[s]]=Eo(t[n[s]]);return e}return t}function Yg(t){return["__proto__","prototype","constructor"].indexOf(t)===-1}function c1(t,e,n,i){if(!Yg(t))return;const s=e[t],r=n[t];Y(s)&&Y(r)?Fs(s,r,i):e[t]=Eo(r)}function Fs(t,e,n){const i=pe(e)?e:[e],s=i.length;if(!Y(t))return t;n=n||{};const r=n.merger||c1;let o;for(let a=0;a<s;++a){if(o=i[a],!Y(o))continue;const l=Object.keys(o);for(let u=0,c=l.length;u<c;++u)r(l[u],t,o,n)}return t}function fs(t,e){return Fs(t,e,{merger:d1})}function d1(t,e,n){if(!Yg(t))return;const i=e[t],s=n[t];Y(i)&&Y(s)?fs(i,s):Object.prototype.hasOwnProperty.call(e,t)||(e[t]=Eo(s))}const Bd={"":t=>t,x:t=>t.x,y:t=>t.y};function f1(t){const e=t.split("."),n=[];let i="";for(const s of e)i+=s,i.endsWith("\\")?i=i.slice(0,-1)+".":(n.push(i),i="");return n}function h1(t){const e=f1(t);return n=>{for(const i of e){if(i==="")break;n=n&&n[i]}return n}}function Fn(t,e){return(Bd[e]||(Bd[e]=h1(e)))(t)}function ic(t){return t.charAt(0).toUpperCase()+t.slice(1)}const zs=t=>typeof t<"u",fn=t=>typeof t=="function",$d=(t,e)=>{if(t.size!==e.size)return!1;for(const n of t)if(!e.has(n))return!1;return!0};function p1(t){return t.type==="mouseup"||t.type==="click"||t.type==="contextmenu"}const q=Math.PI,le=2*q,g1=le+q,Oo=Number.POSITIVE_INFINITY,m1=q/180,me=q/2,yn=q/4,Vd=q*2/3,Xg=Math.log10,Ct=Math.sign;function hs(t,e,n){return Math.abs(t-e)<n}function Hd(t){const e=Math.round(t);t=hs(t,e,t/1e3)?e:t;const n=Math.pow(10,Math.floor(Xg(t))),i=t/n;return(i<=1?1:i<=2?2:i<=5?5:10)*n}function y1(t){const e=[],n=Math.sqrt(t);let i;for(i=1;i<n;i++)t%i===0&&(e.push(i),e.push(t/i));return n===(n|0)&&e.push(n),e.sort((s,r)=>s-r).pop(),e}function x1(t){return typeof t=="symbol"||typeof t=="object"&&t!==null&&!(Symbol.toPrimitive in t||"toString"in t||"valueOf"in t)}function Bs(t){return!x1(t)&&!isNaN(parseFloat(t))&&isFinite(t)}function v1(t,e){const n=Math.round(t);return n-e<=t&&n+e>=t}function _1(t,e,n){let i,s,r;for(i=0,s=t.length;i<s;i++)r=t[i][n],isNaN(r)||(e.min=Math.min(e.min,r),e.max=Math.max(e.max,r))}function Dt(t){return t*(q/180)}function w1(t){return t*(180/q)}function Wd(t){if(!rt(t))return;let e=1,n=0;for(;Math.round(t*e)/e!==t;)e*=10,n++;return n}function Qg(t,e){const n=e.x-t.x,i=e.y-t.y,s=Math.sqrt(n*n+i*i);let r=Math.atan2(i,n);return r<-.5*q&&(r+=le),{angle:r,distance:s}}function Gl(t,e){return Math.sqrt(Math.pow(e.x-t.x,2)+Math.pow(e.y-t.y,2))}function S1(t,e){return(t-e+g1)%le-q}function ct(t){return(t%le+le)%le}function $s(t,e,n,i){const s=ct(t),r=ct(e),o=ct(n),a=ct(r-s),l=ct(o-s),u=ct(s-r),c=ct(s-o);return s===r||s===o||i&&r===o||a>l&&u<c}function Ee(t,e,n){return Math.max(e,Math.min(n,t))}function b1(t){return Ee(t,-32768,32767)}function Gt(t,e,n,i=1e-6){return t>=Math.min(e,n)-i&&t<=Math.max(e,n)+i}function sc(t,e,n){n=n||(o=>t[o]<e);let i=t.length-1,s=0,r;for(;i-s>1;)r=s+i>>1,n(r)?s=r:i=r;return{lo:s,hi:i}}const Mn=(t,e,n,i)=>sc(t,n,i?s=>{const r=t[s][e];return r<n||r===n&&t[s+1][e]===n}:s=>t[s][e]<n),k1=(t,e,n)=>sc(t,n,i=>t[i][e]>=n);function C1(t,e,n){let i=0,s=t.length;for(;i<s&&t[i]<e;)i++;for(;s>i&&t[s-1]>n;)s--;return i>0||s<t.length?t.slice(i,s):t}const qg=["push","pop","shift","splice","unshift"];function P1(t,e){if(t._chartjs){t._chartjs.listeners.push(e);return}Object.defineProperty(t,"_chartjs",{configurable:!0,enumerable:!1,value:{listeners:[e]}}),qg.forEach(n=>{const i="_onData"+ic(n),s=t[n];Object.defineProperty(t,n,{configurable:!0,enumerable:!1,value(...r){const o=s.apply(this,r);return t._chartjs.listeners.forEach(a=>{typeof a[i]=="function"&&a[i](...r)}),o}})})}function Ud(t,e){const n=t._chartjs;if(!n)return;const i=n.listeners,s=i.indexOf(e);s!==-1&&i.splice(s,1),!(i.length>0)&&(qg.forEach(r=>{delete t[r]}),delete t._chartjs)}function Gg(t){const e=new Set(t);return e.size===t.length?t:Array.from(e)}const Jg=function(){return typeof window>"u"?function(t){return t()}:window.requestAnimationFrame}();function Zg(t,e){let n=[],i=!1;return function(...s){n=s,i||(i=!0,Jg.call(window,()=>{i=!1,t.apply(e,n)}))}}function M1(t,e){let n;return function(...i){return e?(clearTimeout(n),n=setTimeout(t,e,i)):t.apply(this,i),e}}const rc=t=>t==="start"?"left":t==="end"?"right":"center",Pe=(t,e,n)=>t==="start"?e:t==="end"?n:(e+n)/2,E1=(t,e,n,i)=>t===(i?"left":"right")?n:t==="center"?(e+n)/2:e;function O1(t,e,n){const i=e.length;let s=0,r=i;if(t._sorted){const{iScale:o,vScale:a,_parsed:l}=t,u=t.dataset&&t.dataset.options?t.dataset.options.spanGaps:null,c=o.axis,{min:d,max:f,minDefined:h,maxDefined:p}=o.getUserBounds();if(h){if(s=Math.min(Mn(l,c,d).lo,n?i:Mn(e,c,o.getPixelForValue(d)).lo),u){const y=l.slice(0,s+1).reverse().findIndex(v=>!X(v[a.axis]));s-=Math.max(0,y)}s=Ee(s,0,i-1)}if(p){let y=Math.max(Mn(l,o.axis,f,!0).hi+1,n?0:Mn(e,c,o.getPixelForValue(f),!0).hi+1);if(u){const v=l.slice(y-1).findIndex(m=>!X(m[a.axis]));y+=Math.max(0,v)}r=Ee(y,s,i)-s}else r=i-s}return{start:s,count:r}}function N1(t){const{xScale:e,yScale:n,_scaleRanges:i}=t,s={xmin:e.min,xmax:e.max,ymin:n.min,ymax:n.max};if(!i)return t._scaleRanges=s,!0;const r=i.xmin!==e.min||i.xmax!==e.max||i.ymin!==n.min||i.ymax!==n.max;return Object.assign(i,s),r}const Cr=t=>t===0||t===1,Kd=(t,e,n)=>-(Math.pow(2,10*(t-=1))*Math.sin((t-e)*le/n)),Yd=(t,e,n)=>Math.pow(2,-10*t)*Math.sin((t-e)*le/n)+1,ps={linear:t=>t,easeInQuad:t=>t*t,easeOutQuad:t=>-t*(t-2),easeInOutQuad:t=>(t/=.5)<1?.5*t*t:-.5*(--t*(t-2)-1),easeInCubic:t=>t*t*t,easeOutCubic:t=>(t-=1)*t*t+1,easeInOutCubic:t=>(t/=.5)<1?.5*t*t*t:.5*((t-=2)*t*t+2),easeInQuart:t=>t*t*t*t,easeOutQuart:t=>-((t-=1)*t*t*t-1),easeInOutQuart:t=>(t/=.5)<1?.5*t*t*t*t:-.5*((t-=2)*t*t*t-2),easeInQuint:t=>t*t*t*t*t,easeOutQuint:t=>(t-=1)*t*t*t*t+1,easeInOutQuint:t=>(t/=.5)<1?.5*t*t*t*t*t:.5*((t-=2)*t*t*t*t+2),easeInSine:t=>-Math.cos(t*me)+1,easeOutSine:t=>Math.sin(t*me),easeInOutSine:t=>-.5*(Math.cos(q*t)-1),easeInExpo:t=>t===0?0:Math.pow(2,10*(t-1)),easeOutExpo:t=>t===1?1:-Math.pow(2,-10*t)+1,easeInOutExpo:t=>Cr(t)?t:t<.5?.5*Math.pow(2,10*(t*2-1)):.5*(-Math.pow(2,-10*(t*2-1))+2),easeInCirc:t=>t>=1?t:-(Math.sqrt(1-t*t)-1),easeOutCirc:t=>Math.sqrt(1-(t-=1)*t),easeInOutCirc:t=>(t/=.5)<1?-.5*(Math.sqrt(1-t*t)-1):.5*(Math.sqrt(1-(t-=2)*t)+1),easeInElastic:t=>Cr(t)?t:Kd(t,.075,.3),easeOutElastic:t=>Cr(t)?t:Yd(t,.075,.3),easeInOutElastic(t){return Cr(t)?t:t<.5?.5*Kd(t*2,.1125,.45):.5+.5*Yd(t*2-1,.1125,.45)},easeInBack(t){return t*t*((1.70158+1)*t-1.70158)},easeOutBack(t){return(t-=1)*t*((1.70158+1)*t********)+1},easeInOutBack(t){let e=1.70158;return(t/=.5)<1?.5*(t*t*(((e*=1.525)+1)*t-e)):.5*((t-=2)*t*(((e*=1.525)+1)*t+e)+2)},easeInBounce:t=>1-ps.easeOutBounce(1-t),easeOutBounce(t){return t<1/2.75?7.5625*t*t:t<2/2.75?7.5625*(t-=1.5/2.75)*t+.75:t<2.5/2.75?7.5625*(t-=2.25/2.75)*t+.9375:7.5625*(t-=2.625/2.75)*t+.984375},easeInOutBounce:t=>t<.5?ps.easeInBounce(t*2)*.5:ps.easeOutBounce(t*2-1)*.5+.5};function oc(t){if(t&&typeof t=="object"){const e=t.toString();return e==="[object CanvasPattern]"||e==="[object CanvasGradient]"}return!1}function Xd(t){return oc(t)?t:new Is(t)}function Fa(t){return oc(t)?t:new Is(t).saturate(.5).darken(.1).hexString()}const L1=["x","y","borderWidth","radius","tension"],T1=["color","borderColor","backgroundColor"];function R1(t){t.set("animation",{delay:void 0,duration:1e3,easing:"easeOutQuart",fn:void 0,from:void 0,loop:void 0,to:void 0,type:void 0}),t.describe("animation",{_fallback:!1,_indexable:!1,_scriptable:e=>e!=="onProgress"&&e!=="onComplete"&&e!=="fn"}),t.set("animations",{colors:{type:"color",properties:T1},numbers:{type:"number",properties:L1}}),t.describe("animations",{_fallback:"animation"}),t.set("transitions",{active:{animation:{duration:400}},resize:{animation:{duration:0}},show:{animations:{colors:{from:"transparent"},visible:{type:"boolean",duration:0}}},hide:{animations:{colors:{to:"transparent"},visible:{type:"boolean",easing:"linear",fn:e=>e|0}}}})}function D1(t){t.set("layout",{autoPadding:!0,padding:{top:0,right:0,bottom:0,left:0}})}const Qd=new Map;function j1(t,e){e=e||{};const n=t+JSON.stringify(e);let i=Qd.get(n);return i||(i=new Intl.NumberFormat(t,e),Qd.set(n,i)),i}function ac(t,e,n){return j1(e,n).format(t)}const A1={values(t){return pe(t)?t:""+t},numeric(t,e,n){if(t===0)return"0";const i=this.chart.options.locale;let s,r=t;if(n.length>1){const u=Math.max(Math.abs(n[0].value),Math.abs(n[n.length-1].value));(u<1e-4||u>1e15)&&(s="scientific"),r=I1(t,n)}const o=Xg(Math.abs(r)),a=isNaN(o)?1:Math.max(Math.min(-1*Math.floor(o),20),0),l={notation:s,minimumFractionDigits:a,maximumFractionDigits:a};return Object.assign(l,this.options.ticks.format),ac(t,i,l)}};function I1(t,e){let n=e.length>3?e[2].value-e[1].value:e[1].value-e[0].value;return Math.abs(n)>=1&&t!==Math.floor(t)&&(n=t-Math.floor(t)),n}var em={formatters:A1};function F1(t){t.set("scale",{display:!0,offset:!1,reverse:!1,beginAtZero:!1,bounds:"ticks",clip:!0,grace:0,grid:{display:!0,lineWidth:1,drawOnChartArea:!0,drawTicks:!0,tickLength:8,tickWidth:(e,n)=>n.lineWidth,tickColor:(e,n)=>n.color,offset:!1},border:{display:!0,dash:[],dashOffset:0,width:1},title:{display:!1,text:"",padding:{top:4,bottom:4}},ticks:{minRotation:0,maxRotation:50,mirror:!1,textStrokeWidth:0,textStrokeColor:"",padding:3,display:!0,autoSkip:!0,autoSkipPadding:3,labelOffset:0,callback:em.formatters.values,minor:{},major:{},align:"center",crossAlign:"near",showLabelBackdrop:!1,backdropColor:"rgba(255, 255, 255, 0.75)",backdropPadding:2}}),t.route("scale.ticks","color","","color"),t.route("scale.grid","color","","borderColor"),t.route("scale.border","color","","borderColor"),t.route("scale.title","color","","color"),t.describe("scale",{_fallback:!1,_scriptable:e=>!e.startsWith("before")&&!e.startsWith("after")&&e!=="callback"&&e!=="parser",_indexable:e=>e!=="borderDash"&&e!=="tickBorderDash"&&e!=="dash"}),t.describe("scales",{_fallback:"scale"}),t.describe("scale.ticks",{_scriptable:e=>e!=="backdropPadding"&&e!=="callback",_indexable:e=>e!=="backdropPadding"})}const zn=Object.create(null),Jl=Object.create(null);function gs(t,e){if(!e)return t;const n=e.split(".");for(let i=0,s=n.length;i<s;++i){const r=n[i];t=t[r]||(t[r]=Object.create(null))}return t}function za(t,e,n){return typeof e=="string"?Fs(gs(t,e),n):Fs(gs(t,""),e)}class z1{constructor(e,n){this.animation=void 0,this.backgroundColor="rgba(0,0,0,0.1)",this.borderColor="rgba(0,0,0,0.1)",this.color="#666",this.datasets={},this.devicePixelRatio=i=>i.chart.platform.getDevicePixelRatio(),this.elements={},this.events=["mousemove","mouseout","click","touchstart","touchmove"],this.font={family:"'Helvetica Neue', 'Helvetica', 'Arial', sans-serif",size:12,style:"normal",lineHeight:1.2,weight:null},this.hover={},this.hoverBackgroundColor=(i,s)=>Fa(s.backgroundColor),this.hoverBorderColor=(i,s)=>Fa(s.borderColor),this.hoverColor=(i,s)=>Fa(s.color),this.indexAxis="x",this.interaction={mode:"nearest",intersect:!0,includeInvisible:!1},this.maintainAspectRatio=!0,this.onHover=null,this.onClick=null,this.parsing=!0,this.plugins={},this.responsive=!0,this.scale=void 0,this.scales={},this.showLine=!0,this.drawActiveElementsOnTop=!0,this.describe(e),this.apply(n)}set(e,n){return za(this,e,n)}get(e){return gs(this,e)}describe(e,n){return za(Jl,e,n)}override(e,n){return za(zn,e,n)}route(e,n,i,s){const r=gs(this,e),o=gs(this,i),a="_"+n;Object.defineProperties(r,{[a]:{value:r[n],writable:!0},[n]:{enumerable:!0,get(){const l=this[a],u=o[s];return Y(l)?Object.assign({},u,l):B(l,u)},set(l){this[a]=l}}})}apply(e){e.forEach(n=>n(this))}}var de=new z1({_scriptable:t=>!t.startsWith("on"),_indexable:t=>t!=="events",hover:{_fallback:"interaction"},interaction:{_scriptable:!1,_indexable:!1}},[R1,D1,F1]);function B1(t){return!t||X(t.size)||X(t.family)?null:(t.style?t.style+" ":"")+(t.weight?t.weight+" ":"")+t.size+"px "+t.family}function qd(t,e,n,i,s){let r=e[s];return r||(r=e[s]=t.measureText(s).width,n.push(s)),r>i&&(i=r),i}function xn(t,e,n){const i=t.currentDevicePixelRatio,s=n!==0?Math.max(n/2,.5):0;return Math.round((e-s)*i)/i+s}function Gd(t,e){!e&&!t||(e=e||t.getContext("2d"),e.save(),e.resetTransform(),e.clearRect(0,0,t.width,t.height),e.restore())}function Zl(t,e,n,i){tm(t,e,n,i,null)}function tm(t,e,n,i,s){let r,o,a,l,u,c,d,f;const h=e.pointStyle,p=e.rotation,y=e.radius;let v=(p||0)*m1;if(h&&typeof h=="object"&&(r=h.toString(),r==="[object HTMLImageElement]"||r==="[object HTMLCanvasElement]")){t.save(),t.translate(n,i),t.rotate(v),t.drawImage(h,-h.width/2,-h.height/2,h.width,h.height),t.restore();return}if(!(isNaN(y)||y<=0)){switch(t.beginPath(),h){default:s?t.ellipse(n,i,s/2,y,0,0,le):t.arc(n,i,y,0,le),t.closePath();break;case"triangle":c=s?s/2:y,t.moveTo(n+Math.sin(v)*c,i-Math.cos(v)*y),v+=Vd,t.lineTo(n+Math.sin(v)*c,i-Math.cos(v)*y),v+=Vd,t.lineTo(n+Math.sin(v)*c,i-Math.cos(v)*y),t.closePath();break;case"rectRounded":u=y*.516,l=y-u,o=Math.cos(v+yn)*l,d=Math.cos(v+yn)*(s?s/2-u:l),a=Math.sin(v+yn)*l,f=Math.sin(v+yn)*(s?s/2-u:l),t.arc(n-d,i-a,u,v-q,v-me),t.arc(n+f,i-o,u,v-me,v),t.arc(n+d,i+a,u,v,v+me),t.arc(n-f,i+o,u,v+me,v+q),t.closePath();break;case"rect":if(!p){l=Math.SQRT1_2*y,c=s?s/2:l,t.rect(n-c,i-l,2*c,2*l);break}v+=yn;case"rectRot":d=Math.cos(v)*(s?s/2:y),o=Math.cos(v)*y,a=Math.sin(v)*y,f=Math.sin(v)*(s?s/2:y),t.moveTo(n-d,i-a),t.lineTo(n+f,i-o),t.lineTo(n+d,i+a),t.lineTo(n-f,i+o),t.closePath();break;case"crossRot":v+=yn;case"cross":d=Math.cos(v)*(s?s/2:y),o=Math.cos(v)*y,a=Math.sin(v)*y,f=Math.sin(v)*(s?s/2:y),t.moveTo(n-d,i-a),t.lineTo(n+d,i+a),t.moveTo(n+f,i-o),t.lineTo(n-f,i+o);break;case"star":d=Math.cos(v)*(s?s/2:y),o=Math.cos(v)*y,a=Math.sin(v)*y,f=Math.sin(v)*(s?s/2:y),t.moveTo(n-d,i-a),t.lineTo(n+d,i+a),t.moveTo(n+f,i-o),t.lineTo(n-f,i+o),v+=yn,d=Math.cos(v)*(s?s/2:y),o=Math.cos(v)*y,a=Math.sin(v)*y,f=Math.sin(v)*(s?s/2:y),t.moveTo(n-d,i-a),t.lineTo(n+d,i+a),t.moveTo(n+f,i-o),t.lineTo(n-f,i+o);break;case"line":o=s?s/2:Math.cos(v)*y,a=Math.sin(v)*y,t.moveTo(n-o,i-a),t.lineTo(n+o,i+a);break;case"dash":t.moveTo(n,i),t.lineTo(n+Math.cos(v)*(s?s/2:y),i+Math.sin(v)*y);break;case!1:t.closePath();break}t.fill(),e.borderWidth>0&&t.stroke()}}function Vs(t,e,n){return n=n||.5,!e||t&&t.x>e.left-n&&t.x<e.right+n&&t.y>e.top-n&&t.y<e.bottom+n}function lc(t,e){t.save(),t.beginPath(),t.rect(e.left,e.top,e.right-e.left,e.bottom-e.top),t.clip()}function uc(t){t.restore()}function $1(t,e,n,i,s){if(!e)return t.lineTo(n.x,n.y);if(s==="middle"){const r=(e.x+n.x)/2;t.lineTo(r,e.y),t.lineTo(r,n.y)}else s==="after"!=!!i?t.lineTo(e.x,n.y):t.lineTo(n.x,e.y);t.lineTo(n.x,n.y)}function V1(t,e,n,i){if(!e)return t.lineTo(n.x,n.y);t.bezierCurveTo(i?e.cp1x:e.cp2x,i?e.cp1y:e.cp2y,i?n.cp2x:n.cp1x,i?n.cp2y:n.cp1y,n.x,n.y)}function H1(t,e){e.translation&&t.translate(e.translation[0],e.translation[1]),X(e.rotation)||t.rotate(e.rotation),e.color&&(t.fillStyle=e.color),e.textAlign&&(t.textAlign=e.textAlign),e.textBaseline&&(t.textBaseline=e.textBaseline)}function W1(t,e,n,i,s){if(s.strikethrough||s.underline){const r=t.measureText(i),o=e-r.actualBoundingBoxLeft,a=e+r.actualBoundingBoxRight,l=n-r.actualBoundingBoxAscent,u=n+r.actualBoundingBoxDescent,c=s.strikethrough?(l+u)/2:u;t.strokeStyle=t.fillStyle,t.beginPath(),t.lineWidth=s.decorationWidth||2,t.moveTo(o,c),t.lineTo(a,c),t.stroke()}}function U1(t,e){const n=t.fillStyle;t.fillStyle=e.color,t.fillRect(e.left,e.top,e.width,e.height),t.fillStyle=n}function Hs(t,e,n,i,s,r={}){const o=pe(e)?e:[e],a=r.strokeWidth>0&&r.strokeColor!=="";let l,u;for(t.save(),t.font=s.string,H1(t,r),l=0;l<o.length;++l)u=o[l],r.backdrop&&U1(t,r.backdrop),a&&(r.strokeColor&&(t.strokeStyle=r.strokeColor),X(r.strokeWidth)||(t.lineWidth=r.strokeWidth),t.strokeText(u,n,i,r.maxWidth)),t.fillText(u,n,i,r.maxWidth),W1(t,n,i,u,r),i+=Number(s.lineHeight);t.restore()}function No(t,e){const{x:n,y:i,w:s,h:r,radius:o}=e;t.arc(n+o.topLeft,i+o.topLeft,o.topLeft,1.5*q,q,!0),t.lineTo(n,i+r-o.bottomLeft),t.arc(n+o.bottomLeft,i+r-o.bottomLeft,o.bottomLeft,q,me,!0),t.lineTo(n+s-o.bottomRight,i+r),t.arc(n+s-o.bottomRight,i+r-o.bottomRight,o.bottomRight,me,0,!0),t.lineTo(n+s,i+o.topRight),t.arc(n+s-o.topRight,i+o.topRight,o.topRight,0,-me,!0),t.lineTo(n+o.topLeft,i)}const K1=/^(normal|(\d+(?:\.\d+)?)(px|em|%)?)$/,Y1=/^(normal|italic|initial|inherit|unset|(oblique( -?[0-9]?[0-9]deg)?))$/;function X1(t,e){const n=(""+t).match(K1);if(!n||n[1]==="normal")return e*1.2;switch(t=+n[2],n[3]){case"px":return t;case"%":t/=100;break}return e*t}const Q1=t=>+t||0;function cc(t,e){const n={},i=Y(e),s=i?Object.keys(e):e,r=Y(t)?i?o=>B(t[o],t[e[o]]):o=>t[o]:()=>t;for(const o of s)n[o]=Q1(r(o));return n}function nm(t){return cc(t,{top:"y",right:"x",bottom:"y",left:"x"})}function mi(t){return cc(t,["topLeft","topRight","bottomLeft","bottomRight"])}function ot(t){const e=nm(t);return e.width=e.left+e.right,e.height=e.top+e.bottom,e}function Oe(t,e){t=t||{},e=e||de.font;let n=B(t.size,e.size);typeof n=="string"&&(n=parseInt(n,10));let i=B(t.style,e.style);i&&!(""+i).match(Y1)&&(console.warn('Invalid font style specified: "'+i+'"'),i=void 0);const s={family:B(t.family,e.family),lineHeight:X1(B(t.lineHeight,e.lineHeight),n),size:n,style:i,weight:B(t.weight,e.weight),string:""};return s.string=B1(s),s}function Pr(t,e,n,i){let s,r,o;for(s=0,r=t.length;s<r;++s)if(o=t[s],o!==void 0&&o!==void 0)return o}function q1(t,e,n){const{min:i,max:s}=t,r=Kg(e,(s-i)/2),o=(a,l)=>n&&a===0?0:a+l;return{min:o(i,-Math.abs(r)),max:o(s,r)}}function Hn(t,e){return Object.assign(Object.create(t),e)}function dc(t,e=[""],n,i,s=()=>t[0]){const r=n||t;typeof i>"u"&&(i=om("_fallback",t));const o={[Symbol.toStringTag]:"Object",_cacheable:!0,_scopes:t,_rootScopes:r,_fallback:i,_getTarget:s,override:a=>dc([a,...t],e,r,i)};return new Proxy(o,{deleteProperty(a,l){return delete a[l],delete a._keys,delete t[0][l],!0},get(a,l){return sm(a,l,()=>s_(l,e,t,a))},getOwnPropertyDescriptor(a,l){return Reflect.getOwnPropertyDescriptor(a._scopes[0],l)},getPrototypeOf(){return Reflect.getPrototypeOf(t[0])},has(a,l){return Zd(a).includes(l)},ownKeys(a){return Zd(a)},set(a,l,u){const c=a._storage||(a._storage=s());return a[l]=c[l]=u,delete a._keys,!0}})}function Pi(t,e,n,i){const s={_cacheable:!1,_proxy:t,_context:e,_subProxy:n,_stack:new Set,_descriptors:im(t,i),setContext:r=>Pi(t,r,n,i),override:r=>Pi(t.override(r),e,n,i)};return new Proxy(s,{deleteProperty(r,o){return delete r[o],delete t[o],!0},get(r,o,a){return sm(r,o,()=>J1(r,o,a))},getOwnPropertyDescriptor(r,o){return r._descriptors.allKeys?Reflect.has(t,o)?{enumerable:!0,configurable:!0}:void 0:Reflect.getOwnPropertyDescriptor(t,o)},getPrototypeOf(){return Reflect.getPrototypeOf(t)},has(r,o){return Reflect.has(t,o)},ownKeys(){return Reflect.ownKeys(t)},set(r,o,a){return t[o]=a,delete r[o],!0}})}function im(t,e={scriptable:!0,indexable:!0}){const{_scriptable:n=e.scriptable,_indexable:i=e.indexable,_allKeys:s=e.allKeys}=t;return{allKeys:s,scriptable:n,indexable:i,isScriptable:fn(n)?n:()=>n,isIndexable:fn(i)?i:()=>i}}const G1=(t,e)=>t?t+ic(e):e,fc=(t,e)=>Y(e)&&t!=="adapters"&&(Object.getPrototypeOf(e)===null||e.constructor===Object);function sm(t,e,n){if(Object.prototype.hasOwnProperty.call(t,e)||e==="constructor")return t[e];const i=n();return t[e]=i,i}function J1(t,e,n){const{_proxy:i,_context:s,_subProxy:r,_descriptors:o}=t;let a=i[e];return fn(a)&&o.isScriptable(e)&&(a=Z1(e,a,t,n)),pe(a)&&a.length&&(a=e_(e,a,t,o.isIndexable)),fc(e,a)&&(a=Pi(a,s,r&&r[e],o)),a}function Z1(t,e,n,i){const{_proxy:s,_context:r,_subProxy:o,_stack:a}=n;if(a.has(t))throw new Error("Recursion detected: "+Array.from(a).join("->")+"->"+t);a.add(t);let l=e(r,o||i);return a.delete(t),fc(t,l)&&(l=hc(s._scopes,s,t,l)),l}function e_(t,e,n,i){const{_proxy:s,_context:r,_subProxy:o,_descriptors:a}=n;if(typeof r.index<"u"&&i(t))return e[r.index%e.length];if(Y(e[0])){const l=e,u=s._scopes.filter(c=>c!==l);e=[];for(const c of l){const d=hc(u,s,t,c);e.push(Pi(d,r,o&&o[t],a))}}return e}function rm(t,e,n){return fn(t)?t(e,n):t}const t_=(t,e)=>t===!0?e:typeof t=="string"?Fn(e,t):void 0;function n_(t,e,n,i,s){for(const r of e){const o=t_(n,r);if(o){t.add(o);const a=rm(o._fallback,n,s);if(typeof a<"u"&&a!==n&&a!==i)return a}else if(o===!1&&typeof i<"u"&&n!==i)return null}return!1}function hc(t,e,n,i){const s=e._rootScopes,r=rm(e._fallback,n,i),o=[...t,...s],a=new Set;a.add(i);let l=Jd(a,o,n,r||n,i);return l===null||typeof r<"u"&&r!==n&&(l=Jd(a,o,r,l,i),l===null)?!1:dc(Array.from(a),[""],s,r,()=>i_(e,n,i))}function Jd(t,e,n,i,s){for(;n;)n=n_(t,e,n,i,s);return n}function i_(t,e,n){const i=t._getTarget();e in i||(i[e]={});const s=i[e];return pe(s)&&Y(n)?n:s||{}}function s_(t,e,n,i){let s;for(const r of e)if(s=om(G1(r,t),n),typeof s<"u")return fc(t,s)?hc(n,i,t,s):s}function om(t,e){for(const n of e){if(!n)continue;const i=n[t];if(typeof i<"u")return i}}function Zd(t){let e=t._keys;return e||(e=t._keys=r_(t._scopes)),e}function r_(t){const e=new Set;for(const n of t)for(const i of Object.keys(n).filter(s=>!s.startsWith("_")))e.add(i);return Array.from(e)}const o_=Number.EPSILON||1e-14,Mi=(t,e)=>e<t.length&&!t[e].skip&&t[e],am=t=>t==="x"?"y":"x";function a_(t,e,n,i){const s=t.skip?e:t,r=e,o=n.skip?e:n,a=Gl(r,s),l=Gl(o,r);let u=a/(a+l),c=l/(a+l);u=isNaN(u)?0:u,c=isNaN(c)?0:c;const d=i*u,f=i*c;return{previous:{x:r.x-d*(o.x-s.x),y:r.y-d*(o.y-s.y)},next:{x:r.x+f*(o.x-s.x),y:r.y+f*(o.y-s.y)}}}function l_(t,e,n){const i=t.length;let s,r,o,a,l,u=Mi(t,0);for(let c=0;c<i-1;++c)if(l=u,u=Mi(t,c+1),!(!l||!u)){if(hs(e[c],0,o_)){n[c]=n[c+1]=0;continue}s=n[c]/e[c],r=n[c+1]/e[c],a=Math.pow(s,2)+Math.pow(r,2),!(a<=9)&&(o=3/Math.sqrt(a),n[c]=s*o*e[c],n[c+1]=r*o*e[c])}}function u_(t,e,n="x"){const i=am(n),s=t.length;let r,o,a,l=Mi(t,0);for(let u=0;u<s;++u){if(o=a,a=l,l=Mi(t,u+1),!a)continue;const c=a[n],d=a[i];o&&(r=(c-o[n])/3,a[`cp1${n}`]=c-r,a[`cp1${i}`]=d-r*e[u]),l&&(r=(l[n]-c)/3,a[`cp2${n}`]=c+r,a[`cp2${i}`]=d+r*e[u])}}function c_(t,e="x"){const n=am(e),i=t.length,s=Array(i).fill(0),r=Array(i);let o,a,l,u=Mi(t,0);for(o=0;o<i;++o)if(a=l,l=u,u=Mi(t,o+1),!!l){if(u){const c=u[e]-l[e];s[o]=c!==0?(u[n]-l[n])/c:0}r[o]=a?u?Ct(s[o-1])!==Ct(s[o])?0:(s[o-1]+s[o])/2:s[o-1]:s[o]}l_(t,s,r),u_(t,r,e)}function Mr(t,e,n){return Math.max(Math.min(t,n),e)}function d_(t,e){let n,i,s,r,o,a=Vs(t[0],e);for(n=0,i=t.length;n<i;++n)o=r,r=a,a=n<i-1&&Vs(t[n+1],e),r&&(s=t[n],o&&(s.cp1x=Mr(s.cp1x,e.left,e.right),s.cp1y=Mr(s.cp1y,e.top,e.bottom)),a&&(s.cp2x=Mr(s.cp2x,e.left,e.right),s.cp2y=Mr(s.cp2y,e.top,e.bottom)))}function f_(t,e,n,i,s){let r,o,a,l;if(e.spanGaps&&(t=t.filter(u=>!u.skip)),e.cubicInterpolationMode==="monotone")c_(t,s);else{let u=i?t[t.length-1]:t[0];for(r=0,o=t.length;r<o;++r)a=t[r],l=a_(u,a,t[Math.min(r+1,o-(i?0:1))%o],e.tension),a.cp1x=l.previous.x,a.cp1y=l.previous.y,a.cp2x=l.next.x,a.cp2y=l.next.y,u=a}e.capBezierPoints&&d_(t,n)}function pc(){return typeof window<"u"&&typeof document<"u"}function gc(t){let e=t.parentNode;return e&&e.toString()==="[object ShadowRoot]"&&(e=e.host),e}function Lo(t,e,n){let i;return typeof t=="string"?(i=parseInt(t,10),t.indexOf("%")!==-1&&(i=i/100*e.parentNode[n])):i=t,i}const sa=t=>t.ownerDocument.defaultView.getComputedStyle(t,null);function h_(t,e){return sa(t).getPropertyValue(e)}const p_=["top","right","bottom","left"];function Ln(t,e,n){const i={};n=n?"-"+n:"";for(let s=0;s<4;s++){const r=p_[s];i[r]=parseFloat(t[e+"-"+r+n])||0}return i.width=i.left+i.right,i.height=i.top+i.bottom,i}const g_=(t,e,n)=>(t>0||e>0)&&(!n||!n.shadowRoot);function m_(t,e){const n=t.touches,i=n&&n.length?n[0]:t,{offsetX:s,offsetY:r}=i;let o=!1,a,l;if(g_(s,r,t.target))a=s,l=r;else{const u=e.getBoundingClientRect();a=i.clientX-u.left,l=i.clientY-u.top,o=!0}return{x:a,y:l,box:o}}function Sn(t,e){if("native"in t)return t;const{canvas:n,currentDevicePixelRatio:i}=e,s=sa(n),r=s.boxSizing==="border-box",o=Ln(s,"padding"),a=Ln(s,"border","width"),{x:l,y:u,box:c}=m_(t,n),d=o.left+(c&&a.left),f=o.top+(c&&a.top);let{width:h,height:p}=e;return r&&(h-=o.width+a.width,p-=o.height+a.height),{x:Math.round((l-d)/h*n.width/i),y:Math.round((u-f)/p*n.height/i)}}function y_(t,e,n){let i,s;if(e===void 0||n===void 0){const r=t&&gc(t);if(!r)e=t.clientWidth,n=t.clientHeight;else{const o=r.getBoundingClientRect(),a=sa(r),l=Ln(a,"border","width"),u=Ln(a,"padding");e=o.width-u.width-l.width,n=o.height-u.height-l.height,i=Lo(a.maxWidth,r,"clientWidth"),s=Lo(a.maxHeight,r,"clientHeight")}}return{width:e,height:n,maxWidth:i||Oo,maxHeight:s||Oo}}const Er=t=>Math.round(t*10)/10;function x_(t,e,n,i){const s=sa(t),r=Ln(s,"margin"),o=Lo(s.maxWidth,t,"clientWidth")||Oo,a=Lo(s.maxHeight,t,"clientHeight")||Oo,l=y_(t,e,n);let{width:u,height:c}=l;if(s.boxSizing==="content-box"){const f=Ln(s,"border","width"),h=Ln(s,"padding");u-=h.width+f.width,c-=h.height+f.height}return u=Math.max(0,u-r.width),c=Math.max(0,i?u/i:c-r.height),u=Er(Math.min(u,o,l.maxWidth)),c=Er(Math.min(c,a,l.maxHeight)),u&&!c&&(c=Er(u/2)),(e!==void 0||n!==void 0)&&i&&l.height&&c>l.height&&(c=l.height,u=Er(Math.floor(c*i))),{width:u,height:c}}function ef(t,e,n){const i=e||1,s=Math.floor(t.height*i),r=Math.floor(t.width*i);t.height=Math.floor(t.height),t.width=Math.floor(t.width);const o=t.canvas;return o.style&&(n||!o.style.height&&!o.style.width)&&(o.style.height=`${t.height}px`,o.style.width=`${t.width}px`),t.currentDevicePixelRatio!==i||o.height!==s||o.width!==r?(t.currentDevicePixelRatio=i,o.height=s,o.width=r,t.ctx.setTransform(i,0,0,i,0,0),!0):!1}const v_=function(){let t=!1;try{const e={get passive(){return t=!0,!1}};pc()&&(window.addEventListener("test",null,e),window.removeEventListener("test",null,e))}catch{}return t}();function tf(t,e){const n=h_(t,e),i=n&&n.match(/^(\d+)(\.\d+)?px$/);return i?+i[1]:void 0}function bn(t,e,n,i){return{x:t.x+n*(e.x-t.x),y:t.y+n*(e.y-t.y)}}function __(t,e,n,i){return{x:t.x+n*(e.x-t.x),y:i==="middle"?n<.5?t.y:e.y:i==="after"?n<1?t.y:e.y:n>0?e.y:t.y}}function w_(t,e,n,i){const s={x:t.cp2x,y:t.cp2y},r={x:e.cp1x,y:e.cp1y},o=bn(t,s,n),a=bn(s,r,n),l=bn(r,e,n),u=bn(o,a,n),c=bn(a,l,n);return bn(u,c,n)}const S_=function(t,e){return{x(n){return t+t+e-n},setWidth(n){e=n},textAlign(n){return n==="center"?n:n==="right"?"left":"right"},xPlus(n,i){return n-i},leftForLtr(n,i){return n-i}}},b_=function(){return{x(t){return t},setWidth(t){},textAlign(t){return t},xPlus(t,e){return t+e},leftForLtr(t,e){return t}}};function yi(t,e,n){return t?S_(e,n):b_()}function lm(t,e){let n,i;(e==="ltr"||e==="rtl")&&(n=t.canvas.style,i=[n.getPropertyValue("direction"),n.getPropertyPriority("direction")],n.setProperty("direction",e,"important"),t.prevTextDirection=i)}function um(t,e){e!==void 0&&(delete t.prevTextDirection,t.canvas.style.setProperty("direction",e[0],e[1]))}function cm(t){return t==="angle"?{between:$s,compare:S1,normalize:ct}:{between:Gt,compare:(e,n)=>e-n,normalize:e=>e}}function nf({start:t,end:e,count:n,loop:i,style:s}){return{start:t%n,end:e%n,loop:i&&(e-t+1)%n===0,style:s}}function k_(t,e,n){const{property:i,start:s,end:r}=n,{between:o,normalize:a}=cm(i),l=e.length;let{start:u,end:c,loop:d}=t,f,h;if(d){for(u+=l,c+=l,f=0,h=l;f<h&&o(a(e[u%l][i]),s,r);++f)u--,c--;u%=l,c%=l}return c<u&&(c+=l),{start:u,end:c,loop:d,style:t.style}}function C_(t,e,n){if(!n)return[t];const{property:i,start:s,end:r}=n,o=e.length,{compare:a,between:l,normalize:u}=cm(i),{start:c,end:d,loop:f,style:h}=k_(t,e,n),p=[];let y=!1,v=null,m,g,x;const w=()=>l(s,x,m)&&a(s,x)!==0,S=()=>a(r,m)===0||l(r,x,m),C=()=>y||w(),k=()=>!y||S();for(let b=c,M=c;b<=d;++b)g=e[b%o],!g.skip&&(m=u(g[i]),m!==x&&(y=l(m,s,r),v===null&&C()&&(v=a(m,s)===0?b:M),v!==null&&k()&&(p.push(nf({start:v,end:b,loop:f,count:o,style:h})),v=null),M=b,x=m));return v!==null&&p.push(nf({start:v,end:d,loop:f,count:o,style:h})),p}function P_(t,e){const n=[],i=t.segments;for(let s=0;s<i.length;s++){const r=C_(i[s],t.points,e);r.length&&n.push(...r)}return n}function M_(t,e,n,i){let s=0,r=e-1;if(n&&!i)for(;s<e&&!t[s].skip;)s++;for(;s<e&&t[s].skip;)s++;for(s%=e,n&&(r+=s);r>s&&t[r%e].skip;)r--;return r%=e,{start:s,end:r}}function E_(t,e,n,i){const s=t.length,r=[];let o=e,a=t[e],l;for(l=e+1;l<=n;++l){const u=t[l%s];u.skip||u.stop?a.skip||(i=!1,r.push({start:e%s,end:(l-1)%s,loop:i}),e=o=u.stop?l:null):(o=l,a.skip&&(e=l)),a=u}return o!==null&&r.push({start:e%s,end:o%s,loop:i}),r}function O_(t,e){const n=t.points,i=t.options.spanGaps,s=n.length;if(!s)return[];const r=!!t._loop,{start:o,end:a}=M_(n,s,r,i);if(i===!0)return sf(t,[{start:o,end:a,loop:r}],n,e);const l=a<o?a+s:a,u=!!t._fullLoop&&o===0&&a===s-1;return sf(t,E_(n,o,l,u),n,e)}function sf(t,e,n,i){return!i||!i.setContext||!n?e:N_(t,e,n,i)}function N_(t,e,n,i){const s=t._chart.getContext(),r=rf(t.options),{_datasetIndex:o,options:{spanGaps:a}}=t,l=n.length,u=[];let c=r,d=e[0].start,f=d;function h(p,y,v,m){const g=a?-1:1;if(p!==y){for(p+=l;n[p%l].skip;)p-=g;for(;n[y%l].skip;)y+=g;p%l!==y%l&&(u.push({start:p%l,end:y%l,loop:v,style:m}),c=m,d=y%l)}}for(const p of e){d=a?d:p.start;let y=n[d%l],v;for(f=d+1;f<=p.end;f++){const m=n[f%l];v=rf(i.setContext(Hn(s,{type:"segment",p0:y,p1:m,p0DataIndex:(f-1)%l,p1DataIndex:f%l,datasetIndex:o}))),L_(v,c)&&h(d,f-1,p.loop,c),y=m,c=v}d<f-1&&h(d,f-1,p.loop,c)}return u}function rf(t){return{backgroundColor:t.backgroundColor,borderCapStyle:t.borderCapStyle,borderDash:t.borderDash,borderDashOffset:t.borderDashOffset,borderJoinStyle:t.borderJoinStyle,borderWidth:t.borderWidth,borderColor:t.borderColor}}function L_(t,e){if(!e)return!1;const n=[],i=function(s,r){return oc(r)?(n.includes(r)||n.push(r),n.indexOf(r)):r};return JSON.stringify(t,i)!==JSON.stringify(e,i)}function Or(t,e,n){return t.options.clip?t[n]:e[n]}function T_(t,e){const{xScale:n,yScale:i}=t;return n&&i?{left:Or(n,e,"left"),right:Or(n,e,"right"),top:Or(i,e,"top"),bottom:Or(i,e,"bottom")}:e}function R_(t,e){const n=e._clip;if(n.disabled)return!1;const i=T_(e,t.chartArea);return{left:n.left===!1?0:i.left-(n.left===!0?0:n.left),right:n.right===!1?t.width:i.right+(n.right===!0?0:n.right),top:n.top===!1?0:i.top-(n.top===!0?0:n.top),bottom:n.bottom===!1?t.height:i.bottom+(n.bottom===!0?0:n.bottom)}}/*!
 * Chart.js v4.5.0
 * https://www.chartjs.org
 * (c) 2025 Chart.js Contributors
 * Released under the MIT License
 */class D_{constructor(){this._request=null,this._charts=new Map,this._running=!1,this._lastDate=void 0}_notify(e,n,i,s){const r=n.listeners[s],o=n.duration;r.forEach(a=>a({chart:e,initial:n.initial,numSteps:o,currentStep:Math.min(i-n.start,o)}))}_refresh(){this._request||(this._running=!0,this._request=Jg.call(window,()=>{this._update(),this._request=null,this._running&&this._refresh()}))}_update(e=Date.now()){let n=0;this._charts.forEach((i,s)=>{if(!i.running||!i.items.length)return;const r=i.items;let o=r.length-1,a=!1,l;for(;o>=0;--o)l=r[o],l._active?(l._total>i.duration&&(i.duration=l._total),l.tick(e),a=!0):(r[o]=r[r.length-1],r.pop());a&&(s.draw(),this._notify(s,i,e,"progress")),r.length||(i.running=!1,this._notify(s,i,e,"complete"),i.initial=!1),n+=r.length}),this._lastDate=e,n===0&&(this._running=!1)}_getAnims(e){const n=this._charts;let i=n.get(e);return i||(i={running:!1,initial:!0,items:[],listeners:{complete:[],progress:[]}},n.set(e,i)),i}listen(e,n,i){this._getAnims(e).listeners[n].push(i)}add(e,n){!n||!n.length||this._getAnims(e).items.push(...n)}has(e){return this._getAnims(e).items.length>0}start(e){const n=this._charts.get(e);n&&(n.running=!0,n.start=Date.now(),n.duration=n.items.reduce((i,s)=>Math.max(i,s._duration),0),this._refresh())}running(e){if(!this._running)return!1;const n=this._charts.get(e);return!(!n||!n.running||!n.items.length)}stop(e){const n=this._charts.get(e);if(!n||!n.items.length)return;const i=n.items;let s=i.length-1;for(;s>=0;--s)i[s].cancel();n.items=[],this._notify(e,n,Date.now(),"complete")}remove(e){return this._charts.delete(e)}}var Mt=new D_;const of="transparent",j_={boolean(t,e,n){return n>.5?e:t},color(t,e,n){const i=Xd(t||of),s=i.valid&&Xd(e||of);return s&&s.valid?s.mix(i,n).hexString():e},number(t,e,n){return t+(e-t)*n}};class A_{constructor(e,n,i,s){const r=n[i];s=Pr([e.to,s,r,e.from]);const o=Pr([e.from,r,s]);this._active=!0,this._fn=e.fn||j_[e.type||typeof o],this._easing=ps[e.easing]||ps.linear,this._start=Math.floor(Date.now()+(e.delay||0)),this._duration=this._total=Math.floor(e.duration),this._loop=!!e.loop,this._target=n,this._prop=i,this._from=o,this._to=s,this._promises=void 0}active(){return this._active}update(e,n,i){if(this._active){this._notify(!1);const s=this._target[this._prop],r=i-this._start,o=this._duration-r;this._start=i,this._duration=Math.floor(Math.max(o,e.duration)),this._total+=r,this._loop=!!e.loop,this._to=Pr([e.to,n,s,e.from]),this._from=Pr([e.from,s,n])}}cancel(){this._active&&(this.tick(Date.now()),this._active=!1,this._notify(!1))}tick(e){const n=e-this._start,i=this._duration,s=this._prop,r=this._from,o=this._loop,a=this._to;let l;if(this._active=r!==a&&(o||n<i),!this._active){this._target[s]=a,this._notify(!0);return}if(n<0){this._target[s]=r;return}l=n/i%2,l=o&&l>1?2-l:l,l=this._easing(Math.min(1,Math.max(0,l))),this._target[s]=this._fn(r,a,l)}wait(){const e=this._promises||(this._promises=[]);return new Promise((n,i)=>{e.push({res:n,rej:i})})}_notify(e){const n=e?"res":"rej",i=this._promises||[];for(let s=0;s<i.length;s++)i[s][n]()}}class dm{constructor(e,n){this._chart=e,this._properties=new Map,this.configure(n)}configure(e){if(!Y(e))return;const n=Object.keys(de.animation),i=this._properties;Object.getOwnPropertyNames(e).forEach(s=>{const r=e[s];if(!Y(r))return;const o={};for(const a of n)o[a]=r[a];(pe(r.properties)&&r.properties||[s]).forEach(a=>{(a===s||!i.has(a))&&i.set(a,o)})})}_animateOptions(e,n){const i=n.options,s=F_(e,i);if(!s)return[];const r=this._createAnimations(s,i);return i.$shared&&I_(e.options.$animations,i).then(()=>{e.options=i},()=>{}),r}_createAnimations(e,n){const i=this._properties,s=[],r=e.$animations||(e.$animations={}),o=Object.keys(n),a=Date.now();let l;for(l=o.length-1;l>=0;--l){const u=o[l];if(u.charAt(0)==="$")continue;if(u==="options"){s.push(...this._animateOptions(e,n));continue}const c=n[u];let d=r[u];const f=i.get(u);if(d)if(f&&d.active()){d.update(f,c,a);continue}else d.cancel();if(!f||!f.duration){e[u]=c;continue}r[u]=d=new A_(f,e,u,c),s.push(d)}return s}update(e,n){if(this._properties.size===0){Object.assign(e,n);return}const i=this._createAnimations(e,n);if(i.length)return Mt.add(this._chart,i),!0}}function I_(t,e){const n=[],i=Object.keys(e);for(let s=0;s<i.length;s++){const r=t[i[s]];r&&r.active()&&n.push(r.wait())}return Promise.all(n)}function F_(t,e){if(!e)return;let n=t.options;if(!n){t.options=e;return}return n.$shared&&(t.options=n=Object.assign({},n,{$shared:!1,$animations:{}})),n}function af(t,e){const n=t&&t.options||{},i=n.reverse,s=n.min===void 0?e:0,r=n.max===void 0?e:0;return{start:i?r:s,end:i?s:r}}function z_(t,e,n){if(n===!1)return!1;const i=af(t,n),s=af(e,n);return{top:s.end,right:i.end,bottom:s.start,left:i.start}}function B_(t){let e,n,i,s;return Y(t)?(e=t.top,n=t.right,i=t.bottom,s=t.left):e=n=i=s=t,{top:e,right:n,bottom:i,left:s,disabled:t===!1}}function fm(t,e){const n=[],i=t._getSortedDatasetMetas(e);let s,r;for(s=0,r=i.length;s<r;++s)n.push(i[s].index);return n}function lf(t,e,n,i={}){const s=t.keys,r=i.mode==="single";let o,a,l,u;if(e===null)return;let c=!1;for(o=0,a=s.length;o<a;++o){if(l=+s[o],l===n){if(c=!0,i.all)continue;break}u=t.values[l],rt(u)&&(r||e===0||Ct(e)===Ct(u))&&(e+=u)}return!c&&!i.all?0:e}function $_(t,e){const{iScale:n,vScale:i}=e,s=n.axis==="x"?"x":"y",r=i.axis==="x"?"x":"y",o=Object.keys(t),a=new Array(o.length);let l,u,c;for(l=0,u=o.length;l<u;++l)c=o[l],a[l]={[s]:c,[r]:t[c]};return a}function Ba(t,e){const n=t&&t.options.stacked;return n||n===void 0&&e.stack!==void 0}function V_(t,e,n){return`${t.id}.${e.id}.${n.stack||n.type}`}function H_(t){const{min:e,max:n,minDefined:i,maxDefined:s}=t.getUserBounds();return{min:i?e:Number.NEGATIVE_INFINITY,max:s?n:Number.POSITIVE_INFINITY}}function W_(t,e,n){const i=t[e]||(t[e]={});return i[n]||(i[n]={})}function uf(t,e,n,i){for(const s of e.getMatchingVisibleMetas(i).reverse()){const r=t[s.index];if(n&&r>0||!n&&r<0)return s.index}return null}function cf(t,e){const{chart:n,_cachedMeta:i}=t,s=n._stacks||(n._stacks={}),{iScale:r,vScale:o,index:a}=i,l=r.axis,u=o.axis,c=V_(r,o,i),d=e.length;let f;for(let h=0;h<d;++h){const p=e[h],{[l]:y,[u]:v}=p,m=p._stacks||(p._stacks={});f=m[u]=W_(s,c,y),f[a]=v,f._top=uf(f,o,!0,i.type),f._bottom=uf(f,o,!1,i.type);const g=f._visualValues||(f._visualValues={});g[a]=v}}function $a(t,e){const n=t.scales;return Object.keys(n).filter(i=>n[i].axis===e).shift()}function U_(t,e){return Hn(t,{active:!1,dataset:void 0,datasetIndex:e,index:e,mode:"default",type:"dataset"})}function K_(t,e,n){return Hn(t,{active:!1,dataIndex:e,parsed:void 0,raw:void 0,element:n,index:e,mode:"default",type:"data"})}function $i(t,e){const n=t.controller.index,i=t.vScale&&t.vScale.axis;if(i){e=e||t._parsed;for(const s of e){const r=s._stacks;if(!r||r[i]===void 0||r[i][n]===void 0)return;delete r[i][n],r[i]._visualValues!==void 0&&r[i]._visualValues[n]!==void 0&&delete r[i]._visualValues[n]}}}const Va=t=>t==="reset"||t==="none",df=(t,e)=>e?t:Object.assign({},t),Y_=(t,e,n)=>t&&!e.hidden&&e._stacked&&{keys:fm(n,!0),values:null};class Tn{constructor(e,n){this.chart=e,this._ctx=e.ctx,this.index=n,this._cachedDataOpts={},this._cachedMeta=this.getMeta(),this._type=this._cachedMeta.type,this.options=void 0,this._parsing=!1,this._data=void 0,this._objectData=void 0,this._sharedOptions=void 0,this._drawStart=void 0,this._drawCount=void 0,this.enableOptionSharing=!1,this.supportsDecimation=!1,this.$context=void 0,this._syncList=[],this.datasetElementType=new.target.datasetElementType,this.dataElementType=new.target.dataElementType,this.initialize()}initialize(){const e=this._cachedMeta;this.configure(),this.linkScales(),e._stacked=Ba(e.vScale,e),this.addElements(),this.options.fill&&!this.chart.isPluginEnabled("filler")&&console.warn("Tried to use the 'fill' option without the 'Filler' plugin enabled. Please import and register the 'Filler' plugin and make sure it is not disabled in the options")}updateIndex(e){this.index!==e&&$i(this._cachedMeta),this.index=e}linkScales(){const e=this.chart,n=this._cachedMeta,i=this.getDataset(),s=(d,f,h,p)=>d==="x"?f:d==="r"?p:h,r=n.xAxisID=B(i.xAxisID,$a(e,"x")),o=n.yAxisID=B(i.yAxisID,$a(e,"y")),a=n.rAxisID=B(i.rAxisID,$a(e,"r")),l=n.indexAxis,u=n.iAxisID=s(l,r,o,a),c=n.vAxisID=s(l,o,r,a);n.xScale=this.getScaleForId(r),n.yScale=this.getScaleForId(o),n.rScale=this.getScaleForId(a),n.iScale=this.getScaleForId(u),n.vScale=this.getScaleForId(c)}getDataset(){return this.chart.data.datasets[this.index]}getMeta(){return this.chart.getDatasetMeta(this.index)}getScaleForId(e){return this.chart.scales[e]}_getOtherScale(e){const n=this._cachedMeta;return e===n.iScale?n.vScale:n.iScale}reset(){this._update("reset")}_destroy(){const e=this._cachedMeta;this._data&&Ud(this._data,this),e._stacked&&$i(e)}_dataCheck(){const e=this.getDataset(),n=e.data||(e.data=[]),i=this._data;if(Y(n)){const s=this._cachedMeta;this._data=$_(n,s)}else if(i!==n){if(i){Ud(i,this);const s=this._cachedMeta;$i(s),s._parsed=[]}n&&Object.isExtensible(n)&&P1(n,this),this._syncList=[],this._data=n}}addElements(){const e=this._cachedMeta;this._dataCheck(),this.datasetElementType&&(e.dataset=new this.datasetElementType)}buildOrUpdateElements(e){const n=this._cachedMeta,i=this.getDataset();let s=!1;this._dataCheck();const r=n._stacked;n._stacked=Ba(n.vScale,n),n.stack!==i.stack&&(s=!0,$i(n),n.stack=i.stack),this._resyncElements(e),(s||r!==n._stacked)&&(cf(this,n._parsed),n._stacked=Ba(n.vScale,n))}configure(){const e=this.chart.config,n=e.datasetScopeKeys(this._type),i=e.getOptionScopes(this.getDataset(),n,!0);this.options=e.createResolver(i,this.getContext()),this._parsing=this.options.parsing,this._cachedDataOpts={}}parse(e,n){const{_cachedMeta:i,_data:s}=this,{iScale:r,_stacked:o}=i,a=r.axis;let l=e===0&&n===s.length?!0:i._sorted,u=e>0&&i._parsed[e-1],c,d,f;if(this._parsing===!1)i._parsed=s,i._sorted=!0,f=s;else{pe(s[e])?f=this.parseArrayData(i,s,e,n):Y(s[e])?f=this.parseObjectData(i,s,e,n):f=this.parsePrimitiveData(i,s,e,n);const h=()=>d[a]===null||u&&d[a]<u[a];for(c=0;c<n;++c)i._parsed[c+e]=d=f[c],l&&(h()&&(l=!1),u=d);i._sorted=l}o&&cf(this,f)}parsePrimitiveData(e,n,i,s){const{iScale:r,vScale:o}=e,a=r.axis,l=o.axis,u=r.getLabels(),c=r===o,d=new Array(s);let f,h,p;for(f=0,h=s;f<h;++f)p=f+i,d[f]={[a]:c||r.parse(u[p],p),[l]:o.parse(n[p],p)};return d}parseArrayData(e,n,i,s){const{xScale:r,yScale:o}=e,a=new Array(s);let l,u,c,d;for(l=0,u=s;l<u;++l)c=l+i,d=n[c],a[l]={x:r.parse(d[0],c),y:o.parse(d[1],c)};return a}parseObjectData(e,n,i,s){const{xScale:r,yScale:o}=e,{xAxisKey:a="x",yAxisKey:l="y"}=this._parsing,u=new Array(s);let c,d,f,h;for(c=0,d=s;c<d;++c)f=c+i,h=n[f],u[c]={x:r.parse(Fn(h,a),f),y:o.parse(Fn(h,l),f)};return u}getParsed(e){return this._cachedMeta._parsed[e]}getDataElement(e){return this._cachedMeta.data[e]}applyStack(e,n,i){const s=this.chart,r=this._cachedMeta,o=n[e.axis],a={keys:fm(s,!0),values:n._stacks[e.axis]._visualValues};return lf(a,o,r.index,{mode:i})}updateRangeFromParsed(e,n,i,s){const r=i[n.axis];let o=r===null?NaN:r;const a=s&&i._stacks[n.axis];s&&a&&(s.values=a,o=lf(s,r,this._cachedMeta.index)),e.min=Math.min(e.min,o),e.max=Math.max(e.max,o)}getMinMax(e,n){const i=this._cachedMeta,s=i._parsed,r=i._sorted&&e===i.iScale,o=s.length,a=this._getOtherScale(e),l=Y_(n,i,this.chart),u={min:Number.POSITIVE_INFINITY,max:Number.NEGATIVE_INFINITY},{min:c,max:d}=H_(a);let f,h;function p(){h=s[f];const y=h[a.axis];return!rt(h[e.axis])||c>y||d<y}for(f=0;f<o&&!(!p()&&(this.updateRangeFromParsed(u,e,h,l),r));++f);if(r){for(f=o-1;f>=0;--f)if(!p()){this.updateRangeFromParsed(u,e,h,l);break}}return u}getAllParsedValues(e){const n=this._cachedMeta._parsed,i=[];let s,r,o;for(s=0,r=n.length;s<r;++s)o=n[s][e.axis],rt(o)&&i.push(o);return i}getMaxOverflow(){return!1}getLabelAndValue(e){const n=this._cachedMeta,i=n.iScale,s=n.vScale,r=this.getParsed(e);return{label:i?""+i.getLabelForValue(r[i.axis]):"",value:s?""+s.getLabelForValue(r[s.axis]):""}}_update(e){const n=this._cachedMeta;this.update(e||"default"),n._clip=B_(B(this.options.clip,z_(n.xScale,n.yScale,this.getMaxOverflow())))}update(e){}draw(){const e=this._ctx,n=this.chart,i=this._cachedMeta,s=i.data||[],r=n.chartArea,o=[],a=this._drawStart||0,l=this._drawCount||s.length-a,u=this.options.drawActiveElementsOnTop;let c;for(i.dataset&&i.dataset.draw(e,r,a,l),c=a;c<a+l;++c){const d=s[c];d.hidden||(d.active&&u?o.push(d):d.draw(e,r))}for(c=0;c<o.length;++c)o[c].draw(e,r)}getStyle(e,n){const i=n?"active":"default";return e===void 0&&this._cachedMeta.dataset?this.resolveDatasetElementOptions(i):this.resolveDataElementOptions(e||0,i)}getContext(e,n,i){const s=this.getDataset();let r;if(e>=0&&e<this._cachedMeta.data.length){const o=this._cachedMeta.data[e];r=o.$context||(o.$context=K_(this.getContext(),e,o)),r.parsed=this.getParsed(e),r.raw=s.data[e],r.index=r.dataIndex=e}else r=this.$context||(this.$context=U_(this.chart.getContext(),this.index)),r.dataset=s,r.index=r.datasetIndex=this.index;return r.active=!!n,r.mode=i,r}resolveDatasetElementOptions(e){return this._resolveElementOptions(this.datasetElementType.id,e)}resolveDataElementOptions(e,n){return this._resolveElementOptions(this.dataElementType.id,n,e)}_resolveElementOptions(e,n="default",i){const s=n==="active",r=this._cachedDataOpts,o=e+"-"+n,a=r[o],l=this.enableOptionSharing&&zs(i);if(a)return df(a,l);const u=this.chart.config,c=u.datasetElementScopeKeys(this._type,e),d=s?[`${e}Hover`,"hover",e,""]:[e,""],f=u.getOptionScopes(this.getDataset(),c),h=Object.keys(de.elements[e]),p=()=>this.getContext(i,s,n),y=u.resolveNamedOptions(f,h,p,d);return y.$shared&&(y.$shared=l,r[o]=Object.freeze(df(y,l))),y}_resolveAnimations(e,n,i){const s=this.chart,r=this._cachedDataOpts,o=`animation-${n}`,a=r[o];if(a)return a;let l;if(s.options.animation!==!1){const c=this.chart.config,d=c.datasetAnimationScopeKeys(this._type,n),f=c.getOptionScopes(this.getDataset(),d);l=c.createResolver(f,this.getContext(e,i,n))}const u=new dm(s,l&&l.animations);return l&&l._cacheable&&(r[o]=Object.freeze(u)),u}getSharedOptions(e){if(e.$shared)return this._sharedOptions||(this._sharedOptions=Object.assign({},e))}includeOptions(e,n){return!n||Va(e)||this.chart._animationsDisabled}_getSharedOptions(e,n){const i=this.resolveDataElementOptions(e,n),s=this._sharedOptions,r=this.getSharedOptions(i),o=this.includeOptions(n,r)||r!==s;return this.updateSharedOptions(r,n,i),{sharedOptions:r,includeOptions:o}}updateElement(e,n,i,s){Va(s)?Object.assign(e,i):this._resolveAnimations(n,s).update(e,i)}updateSharedOptions(e,n,i){e&&!Va(n)&&this._resolveAnimations(void 0,n).update(e,i)}_setStyle(e,n,i,s){e.active=s;const r=this.getStyle(n,s);this._resolveAnimations(n,i,s).update(e,{options:!s&&this.getSharedOptions(r)||r})}removeHoverStyle(e,n,i){this._setStyle(e,i,"active",!1)}setHoverStyle(e,n,i){this._setStyle(e,i,"active",!0)}_removeDatasetHoverStyle(){const e=this._cachedMeta.dataset;e&&this._setStyle(e,void 0,"active",!1)}_setDatasetHoverStyle(){const e=this._cachedMeta.dataset;e&&this._setStyle(e,void 0,"active",!0)}_resyncElements(e){const n=this._data,i=this._cachedMeta.data;for(const[a,l,u]of this._syncList)this[a](l,u);this._syncList=[];const s=i.length,r=n.length,o=Math.min(r,s);o&&this.parse(0,o),r>s?this._insertElements(s,r-s,e):r<s&&this._removeElements(r,s-r)}_insertElements(e,n,i=!0){const s=this._cachedMeta,r=s.data,o=e+n;let a;const l=u=>{for(u.length+=n,a=u.length-1;a>=o;a--)u[a]=u[a-n]};for(l(r),a=e;a<o;++a)r[a]=new this.dataElementType;this._parsing&&l(s._parsed),this.parse(e,n),i&&this.updateElements(r,e,n,"reset")}updateElements(e,n,i,s){}_removeElements(e,n){const i=this._cachedMeta;if(this._parsing){const s=i._parsed.splice(e,n);i._stacked&&$i(i,s)}i.data.splice(e,n)}_sync(e){if(this._parsing)this._syncList.push(e);else{const[n,i,s]=e;this[n](i,s)}this.chart._dataChanges.push([this.index,...e])}_onDataPush(){const e=arguments.length;this._sync(["_insertElements",this.getDataset().data.length-e,e])}_onDataPop(){this._sync(["_removeElements",this._cachedMeta.data.length-1,1])}_onDataShift(){this._sync(["_removeElements",0,1])}_onDataSplice(e,n){n&&this._sync(["_removeElements",e,n]);const i=arguments.length-2;i&&this._sync(["_insertElements",e,i])}_onDataUnshift(){this._sync(["_insertElements",0,arguments.length])}}D(Tn,"defaults",{}),D(Tn,"datasetElementType",null),D(Tn,"dataElementType",null);function X_(t,e){if(!t._cache.$bar){const n=t.getMatchingVisibleMetas(e);let i=[];for(let s=0,r=n.length;s<r;s++)i=i.concat(n[s].controller.getAllParsedValues(t));t._cache.$bar=Gg(i.sort((s,r)=>s-r))}return t._cache.$bar}function Q_(t){const e=t.iScale,n=X_(e,t.type);let i=e._length,s,r,o,a;const l=()=>{o===32767||o===-32768||(zs(a)&&(i=Math.min(i,Math.abs(o-a)||i)),a=o)};for(s=0,r=n.length;s<r;++s)o=e.getPixelForValue(n[s]),l();for(a=void 0,s=0,r=e.ticks.length;s<r;++s)o=e.getPixelForTick(s),l();return i}function q_(t,e,n,i){const s=n.barThickness;let r,o;return X(s)?(r=e.min*n.categoryPercentage,o=n.barPercentage):(r=s*i,o=1),{chunk:r/i,ratio:o,start:e.pixels[t]-r/2}}function G_(t,e,n,i){const s=e.pixels,r=s[t];let o=t>0?s[t-1]:null,a=t<s.length-1?s[t+1]:null;const l=n.categoryPercentage;o===null&&(o=r-(a===null?e.end-e.start:a-r)),a===null&&(a=r+r-o);const u=r-(r-Math.min(o,a))/2*l;return{chunk:Math.abs(a-o)/2*l/i,ratio:n.barPercentage,start:u}}function J_(t,e,n,i){const s=n.parse(t[0],i),r=n.parse(t[1],i),o=Math.min(s,r),a=Math.max(s,r);let l=o,u=a;Math.abs(o)>Math.abs(a)&&(l=a,u=o),e[n.axis]=u,e._custom={barStart:l,barEnd:u,start:s,end:r,min:o,max:a}}function hm(t,e,n,i){return pe(t)?J_(t,e,n,i):e[n.axis]=n.parse(t,i),e}function ff(t,e,n,i){const s=t.iScale,r=t.vScale,o=s.getLabels(),a=s===r,l=[];let u,c,d,f;for(u=n,c=n+i;u<c;++u)f=e[u],d={},d[s.axis]=a||s.parse(o[u],u),l.push(hm(f,d,r,u));return l}function Ha(t){return t&&t.barStart!==void 0&&t.barEnd!==void 0}function Z_(t,e,n){return t!==0?Ct(t):(e.isHorizontal()?1:-1)*(e.min>=n?1:-1)}function ew(t){let e,n,i,s,r;return t.horizontal?(e=t.base>t.x,n="left",i="right"):(e=t.base<t.y,n="bottom",i="top"),e?(s="end",r="start"):(s="start",r="end"),{start:n,end:i,reverse:e,top:s,bottom:r}}function tw(t,e,n,i){let s=e.borderSkipped;const r={};if(!s){t.borderSkipped=r;return}if(s===!0){t.borderSkipped={top:!0,right:!0,bottom:!0,left:!0};return}const{start:o,end:a,reverse:l,top:u,bottom:c}=ew(t);s==="middle"&&n&&(t.enableBorderRadius=!0,(n._top||0)===i?s=u:(n._bottom||0)===i?s=c:(r[hf(c,o,a,l)]=!0,s=u)),r[hf(s,o,a,l)]=!0,t.borderSkipped=r}function hf(t,e,n,i){return i?(t=nw(t,e,n),t=pf(t,n,e)):t=pf(t,e,n),t}function nw(t,e,n){return t===e?n:t===n?e:t}function pf(t,e,n){return t==="start"?e:t==="end"?n:t}function iw(t,{inflateAmount:e},n){t.inflateAmount=e==="auto"?n===1?.33:0:e}class Qr extends Tn{parsePrimitiveData(e,n,i,s){return ff(e,n,i,s)}parseArrayData(e,n,i,s){return ff(e,n,i,s)}parseObjectData(e,n,i,s){const{iScale:r,vScale:o}=e,{xAxisKey:a="x",yAxisKey:l="y"}=this._parsing,u=r.axis==="x"?a:l,c=o.axis==="x"?a:l,d=[];let f,h,p,y;for(f=i,h=i+s;f<h;++f)y=n[f],p={},p[r.axis]=r.parse(Fn(y,u),f),d.push(hm(Fn(y,c),p,o,f));return d}updateRangeFromParsed(e,n,i,s){super.updateRangeFromParsed(e,n,i,s);const r=i._custom;r&&n===this._cachedMeta.vScale&&(e.min=Math.min(e.min,r.min),e.max=Math.max(e.max,r.max))}getMaxOverflow(){return 0}getLabelAndValue(e){const n=this._cachedMeta,{iScale:i,vScale:s}=n,r=this.getParsed(e),o=r._custom,a=Ha(o)?"["+o.start+", "+o.end+"]":""+s.getLabelForValue(r[s.axis]);return{label:""+i.getLabelForValue(r[i.axis]),value:a}}initialize(){this.enableOptionSharing=!0,super.initialize();const e=this._cachedMeta;e.stack=this.getDataset().stack}update(e){const n=this._cachedMeta;this.updateElements(n.data,0,n.data.length,e)}updateElements(e,n,i,s){const r=s==="reset",{index:o,_cachedMeta:{vScale:a}}=this,l=a.getBasePixel(),u=a.isHorizontal(),c=this._getRuler(),{sharedOptions:d,includeOptions:f}=this._getSharedOptions(n,s);for(let h=n;h<n+i;h++){const p=this.getParsed(h),y=r||X(p[a.axis])?{base:l,head:l}:this._calculateBarValuePixels(h),v=this._calculateBarIndexPixels(h,c),m=(p._stacks||{})[a.axis],g={horizontal:u,base:y.base,enableBorderRadius:!m||Ha(p._custom)||o===m._top||o===m._bottom,x:u?y.head:v.center,y:u?v.center:y.head,height:u?v.size:Math.abs(y.size),width:u?Math.abs(y.size):v.size};f&&(g.options=d||this.resolveDataElementOptions(h,e[h].active?"active":s));const x=g.options||e[h].options;tw(g,x,m,o),iw(g,x,c.ratio),this.updateElement(e[h],h,g,s)}}_getStacks(e,n){const{iScale:i}=this._cachedMeta,s=i.getMatchingVisibleMetas(this._type).filter(c=>c.controller.options.grouped),r=i.options.stacked,o=[],a=this._cachedMeta.controller.getParsed(n),l=a&&a[i.axis],u=c=>{const d=c._parsed.find(h=>h[i.axis]===l),f=d&&d[c.vScale.axis];if(X(f)||isNaN(f))return!0};for(const c of s)if(!(n!==void 0&&u(c))&&((r===!1||o.indexOf(c.stack)===-1||r===void 0&&c.stack===void 0)&&o.push(c.stack),c.index===e))break;return o.length||o.push(void 0),o}_getStackCount(e){return this._getStacks(void 0,e).length}_getAxisCount(){return this._getAxis().length}getFirstScaleIdForIndexAxis(){const e=this.chart.scales,n=this.chart.options.indexAxis;return Object.keys(e).filter(i=>e[i].axis===n).shift()}_getAxis(){const e={},n=this.getFirstScaleIdForIndexAxis();for(const i of this.chart.data.datasets)e[B(this.chart.options.indexAxis==="x"?i.xAxisID:i.yAxisID,n)]=!0;return Object.keys(e)}_getStackIndex(e,n,i){const s=this._getStacks(e,i),r=n!==void 0?s.indexOf(n):-1;return r===-1?s.length-1:r}_getRuler(){const e=this.options,n=this._cachedMeta,i=n.iScale,s=[];let r,o;for(r=0,o=n.data.length;r<o;++r)s.push(i.getPixelForValue(this.getParsed(r)[i.axis],r));const a=e.barThickness;return{min:a||Q_(n),pixels:s,start:i._startPixel,end:i._endPixel,stackCount:this._getStackCount(),scale:i,grouped:e.grouped,ratio:a?1:e.categoryPercentage*e.barPercentage}}_calculateBarValuePixels(e){const{_cachedMeta:{vScale:n,_stacked:i,index:s},options:{base:r,minBarLength:o}}=this,a=r||0,l=this.getParsed(e),u=l._custom,c=Ha(u);let d=l[n.axis],f=0,h=i?this.applyStack(n,l,i):d,p,y;h!==d&&(f=h-d,h=d),c&&(d=u.barStart,h=u.barEnd-u.barStart,d!==0&&Ct(d)!==Ct(u.barEnd)&&(f=0),f+=d);const v=!X(r)&&!c?r:f;let m=n.getPixelForValue(v);if(this.chart.getDataVisibility(e)?p=n.getPixelForValue(f+h):p=m,y=p-m,Math.abs(y)<o){y=Z_(y,n,a)*o,d===a&&(m-=y/2);const g=n.getPixelForDecimal(0),x=n.getPixelForDecimal(1),w=Math.min(g,x),S=Math.max(g,x);m=Math.max(Math.min(m,S),w),p=m+y,i&&!c&&(l._stacks[n.axis]._visualValues[s]=n.getValueForPixel(p)-n.getValueForPixel(m))}if(m===n.getPixelForValue(a)){const g=Ct(y)*n.getLineWidthForValue(a)/2;m+=g,y-=g}return{size:y,base:m,head:p,center:p+y/2}}_calculateBarIndexPixels(e,n){const i=n.scale,s=this.options,r=s.skipNull,o=B(s.maxBarThickness,1/0);let a,l;const u=this._getAxisCount();if(n.grouped){const c=r?this._getStackCount(e):n.stackCount,d=s.barThickness==="flex"?G_(e,n,s,c*u):q_(e,n,s,c*u),f=this.chart.options.indexAxis==="x"?this.getDataset().xAxisID:this.getDataset().yAxisID,h=this._getAxis().indexOf(B(f,this.getFirstScaleIdForIndexAxis())),p=this._getStackIndex(this.index,this._cachedMeta.stack,r?e:void 0)+h;a=d.start+d.chunk*p+d.chunk/2,l=Math.min(o,d.chunk*d.ratio)}else a=i.getPixelForValue(this.getParsed(e)[i.axis],e),l=Math.min(o,n.min*n.ratio);return{base:a-l/2,head:a+l/2,center:a,size:l}}draw(){const e=this._cachedMeta,n=e.vScale,i=e.data,s=i.length;let r=0;for(;r<s;++r)this.getParsed(r)[n.axis]!==null&&!i[r].hidden&&i[r].draw(this._ctx)}}D(Qr,"id","bar"),D(Qr,"defaults",{datasetElementType:!1,dataElementType:"bar",categoryPercentage:.8,barPercentage:.9,grouped:!0,animations:{numbers:{type:"number",properties:["x","y","base","width","height"]}}}),D(Qr,"overrides",{scales:{_index_:{type:"category",offset:!0,grid:{offset:!0}},_value_:{type:"linear",beginAtZero:!0}}});function sw(t,e,n){let i=1,s=1,r=0,o=0;if(e<le){const a=t,l=a+e,u=Math.cos(a),c=Math.sin(a),d=Math.cos(l),f=Math.sin(l),h=(x,w,S)=>$s(x,a,l,!0)?1:Math.max(w,w*n,S,S*n),p=(x,w,S)=>$s(x,a,l,!0)?-1:Math.min(w,w*n,S,S*n),y=h(0,u,d),v=h(me,c,f),m=p(q,u,d),g=p(q+me,c,f);i=(y-m)/2,s=(v-g)/2,r=-(y+m)/2,o=-(v+g)/2}return{ratioX:i,ratioY:s,offsetX:r,offsetY:o}}class Ji extends Tn{constructor(e,n){super(e,n),this.enableOptionSharing=!0,this.innerRadius=void 0,this.outerRadius=void 0,this.offsetX=void 0,this.offsetY=void 0}linkScales(){}parse(e,n){const i=this.getDataset().data,s=this._cachedMeta;if(this._parsing===!1)s._parsed=i;else{let r=l=>+i[l];if(Y(i[e])){const{key:l="value"}=this._parsing;r=u=>+Fn(i[u],l)}let o,a;for(o=e,a=e+n;o<a;++o)s._parsed[o]=r(o)}}_getRotation(){return Dt(this.options.rotation-90)}_getCircumference(){return Dt(this.options.circumference)}_getRotationExtents(){let e=le,n=-le;for(let i=0;i<this.chart.data.datasets.length;++i)if(this.chart.isDatasetVisible(i)&&this.chart.getDatasetMeta(i).type===this._type){const s=this.chart.getDatasetMeta(i).controller,r=s._getRotation(),o=s._getCircumference();e=Math.min(e,r),n=Math.max(n,r+o)}return{rotation:e,circumference:n-e}}update(e){const n=this.chart,{chartArea:i}=n,s=this._cachedMeta,r=s.data,o=this.getMaxBorderWidth()+this.getMaxOffset(r)+this.options.spacing,a=Math.max((Math.min(i.width,i.height)-o)/2,0),l=Math.min(u1(this.options.cutout,a),1),u=this._getRingWeight(this.index),{circumference:c,rotation:d}=this._getRotationExtents(),{ratioX:f,ratioY:h,offsetX:p,offsetY:y}=sw(d,c,l),v=(i.width-o)/f,m=(i.height-o)/h,g=Math.max(Math.min(v,m)/2,0),x=Kg(this.options.radius,g),w=Math.max(x*l,0),S=(x-w)/this._getVisibleDatasetWeightTotal();this.offsetX=p*x,this.offsetY=y*x,s.total=this.calculateTotal(),this.outerRadius=x-S*this._getRingWeightOffset(this.index),this.innerRadius=Math.max(this.outerRadius-S*u,0),this.updateElements(r,0,r.length,e)}_circumference(e,n){const i=this.options,s=this._cachedMeta,r=this._getCircumference();return n&&i.animation.animateRotate||!this.chart.getDataVisibility(e)||s._parsed[e]===null||s.data[e].hidden?0:this.calculateCircumference(s._parsed[e]*r/le)}updateElements(e,n,i,s){const r=s==="reset",o=this.chart,a=o.chartArea,u=o.options.animation,c=(a.left+a.right)/2,d=(a.top+a.bottom)/2,f=r&&u.animateScale,h=f?0:this.innerRadius,p=f?0:this.outerRadius,{sharedOptions:y,includeOptions:v}=this._getSharedOptions(n,s);let m=this._getRotation(),g;for(g=0;g<n;++g)m+=this._circumference(g,r);for(g=n;g<n+i;++g){const x=this._circumference(g,r),w=e[g],S={x:c+this.offsetX,y:d+this.offsetY,startAngle:m,endAngle:m+x,circumference:x,outerRadius:p,innerRadius:h};v&&(S.options=y||this.resolveDataElementOptions(g,w.active?"active":s)),m+=x,this.updateElement(w,g,S,s)}}calculateTotal(){const e=this._cachedMeta,n=e.data;let i=0,s;for(s=0;s<n.length;s++){const r=e._parsed[s];r!==null&&!isNaN(r)&&this.chart.getDataVisibility(s)&&!n[s].hidden&&(i+=Math.abs(r))}return i}calculateCircumference(e){const n=this._cachedMeta.total;return n>0&&!isNaN(e)?le*(Math.abs(e)/n):0}getLabelAndValue(e){const n=this._cachedMeta,i=this.chart,s=i.data.labels||[],r=ac(n._parsed[e],i.options.locale);return{label:s[e]||"",value:r}}getMaxBorderWidth(e){let n=0;const i=this.chart;let s,r,o,a,l;if(!e){for(s=0,r=i.data.datasets.length;s<r;++s)if(i.isDatasetVisible(s)){o=i.getDatasetMeta(s),e=o.data,a=o.controller;break}}if(!e)return 0;for(s=0,r=e.length;s<r;++s)l=a.resolveDataElementOptions(s),l.borderAlign!=="inner"&&(n=Math.max(n,l.borderWidth||0,l.hoverBorderWidth||0));return n}getMaxOffset(e){let n=0;for(let i=0,s=e.length;i<s;++i){const r=this.resolveDataElementOptions(i);n=Math.max(n,r.offset||0,r.hoverOffset||0)}return n}_getRingWeightOffset(e){let n=0;for(let i=0;i<e;++i)this.chart.isDatasetVisible(i)&&(n+=this._getRingWeight(i));return n}_getRingWeight(e){return Math.max(B(this.chart.data.datasets[e].weight,1),0)}_getVisibleDatasetWeightTotal(){return this._getRingWeightOffset(this.chart.data.datasets.length)||1}}D(Ji,"id","doughnut"),D(Ji,"defaults",{datasetElementType:!1,dataElementType:"arc",animation:{animateRotate:!0,animateScale:!1},animations:{numbers:{type:"number",properties:["circumference","endAngle","innerRadius","outerRadius","startAngle","x","y","offset","borderWidth","spacing"]}},cutout:"50%",rotation:0,circumference:360,radius:"100%",spacing:0,indexAxis:"r"}),D(Ji,"descriptors",{_scriptable:e=>e!=="spacing",_indexable:e=>e!=="spacing"&&!e.startsWith("borderDash")&&!e.startsWith("hoverBorderDash")}),D(Ji,"overrides",{aspectRatio:1,plugins:{legend:{labels:{generateLabels(e){const n=e.data;if(n.labels.length&&n.datasets.length){const{labels:{pointStyle:i,color:s}}=e.legend.options;return n.labels.map((r,o)=>{const l=e.getDatasetMeta(0).controller.getStyle(o);return{text:r,fillStyle:l.backgroundColor,strokeStyle:l.borderColor,fontColor:s,lineWidth:l.borderWidth,pointStyle:i,hidden:!e.getDataVisibility(o),index:o}})}return[]}},onClick(e,n,i){i.chart.toggleDataVisibility(n.index),i.chart.update()}}}});class qr extends Tn{initialize(){this.enableOptionSharing=!0,this.supportsDecimation=!0,super.initialize()}update(e){const n=this._cachedMeta,{dataset:i,data:s=[],_dataset:r}=n,o=this.chart._animationsDisabled;let{start:a,count:l}=O1(n,s,o);this._drawStart=a,this._drawCount=l,N1(n)&&(a=0,l=s.length),i._chart=this.chart,i._datasetIndex=this.index,i._decimated=!!r._decimated,i.points=s;const u=this.resolveDatasetElementOptions(e);this.options.showLine||(u.borderWidth=0),u.segment=this.options.segment,this.updateElement(i,void 0,{animated:!o,options:u},e),this.updateElements(s,a,l,e)}updateElements(e,n,i,s){const r=s==="reset",{iScale:o,vScale:a,_stacked:l,_dataset:u}=this._cachedMeta,{sharedOptions:c,includeOptions:d}=this._getSharedOptions(n,s),f=o.axis,h=a.axis,{spanGaps:p,segment:y}=this.options,v=Bs(p)?p:Number.POSITIVE_INFINITY,m=this.chart._animationsDisabled||r||s==="none",g=n+i,x=e.length;let w=n>0&&this.getParsed(n-1);for(let S=0;S<x;++S){const C=e[S],k=m?C:{};if(S<n||S>=g){k.skip=!0;continue}const b=this.getParsed(S),M=X(b[h]),P=k[f]=o.getPixelForValue(b[f],S),T=k[h]=r||M?a.getBasePixel():a.getPixelForValue(l?this.applyStack(a,b,l):b[h],S);k.skip=isNaN(P)||isNaN(T)||M,k.stop=S>0&&Math.abs(b[f]-w[f])>v,y&&(k.parsed=b,k.raw=u.data[S]),d&&(k.options=c||this.resolveDataElementOptions(S,C.active?"active":s)),m||this.updateElement(C,S,k,s),w=b}}getMaxOverflow(){const e=this._cachedMeta,n=e.dataset,i=n.options&&n.options.borderWidth||0,s=e.data||[];if(!s.length)return i;const r=s[0].size(this.resolveDataElementOptions(0)),o=s[s.length-1].size(this.resolveDataElementOptions(s.length-1));return Math.max(i,r,o)/2}draw(){const e=this._cachedMeta;e.dataset.updateControlPoints(this.chart.chartArea,e.iScale.axis),super.draw()}}D(qr,"id","line"),D(qr,"defaults",{datasetElementType:"line",dataElementType:"point",showLine:!0,spanGaps:!1}),D(qr,"overrides",{scales:{_index_:{type:"category"},_value_:{type:"linear"}}});class eu extends Ji{}D(eu,"id","pie"),D(eu,"defaults",{cutout:0,rotation:0,circumference:360,radius:"100%"});function vn(){throw new Error("This method is not implemented: Check that a complete date adapter is provided.")}class mc{constructor(e){D(this,"options");this.options=e||{}}static override(e){Object.assign(mc.prototype,e)}init(){}formats(){return vn()}parse(){return vn()}format(){return vn()}add(){return vn()}diff(){return vn()}startOf(){return vn()}endOf(){return vn()}}var rw={_date:mc};function ow(t,e,n,i){const{controller:s,data:r,_sorted:o}=t,a=s._cachedMeta.iScale,l=t.dataset&&t.dataset.options?t.dataset.options.spanGaps:null;if(a&&e===a.axis&&e!=="r"&&o&&r.length){const u=a._reversePixels?k1:Mn;if(i){if(s._sharedOptions){const c=r[0],d=typeof c.getRange=="function"&&c.getRange(e);if(d){const f=u(r,e,n-d),h=u(r,e,n+d);return{lo:f.lo,hi:h.hi}}}}else{const c=u(r,e,n);if(l){const{vScale:d}=s._cachedMeta,{_parsed:f}=t,h=f.slice(0,c.lo+1).reverse().findIndex(y=>!X(y[d.axis]));c.lo-=Math.max(0,h);const p=f.slice(c.hi).findIndex(y=>!X(y[d.axis]));c.hi+=Math.max(0,p)}return c}}return{lo:0,hi:r.length-1}}function ra(t,e,n,i,s){const r=t.getSortedVisibleDatasetMetas(),o=n[e];for(let a=0,l=r.length;a<l;++a){const{index:u,data:c}=r[a],{lo:d,hi:f}=ow(r[a],e,o,s);for(let h=d;h<=f;++h){const p=c[h];p.skip||i(p,u,h)}}}function aw(t){const e=t.indexOf("x")!==-1,n=t.indexOf("y")!==-1;return function(i,s){const r=e?Math.abs(i.x-s.x):0,o=n?Math.abs(i.y-s.y):0;return Math.sqrt(Math.pow(r,2)+Math.pow(o,2))}}function Wa(t,e,n,i,s){const r=[];return!s&&!t.isPointInArea(e)||ra(t,n,e,function(a,l,u){!s&&!Vs(a,t.chartArea,0)||a.inRange(e.x,e.y,i)&&r.push({element:a,datasetIndex:l,index:u})},!0),r}function lw(t,e,n,i){let s=[];function r(o,a,l){const{startAngle:u,endAngle:c}=o.getProps(["startAngle","endAngle"],i),{angle:d}=Qg(o,{x:e.x,y:e.y});$s(d,u,c)&&s.push({element:o,datasetIndex:a,index:l})}return ra(t,n,e,r),s}function uw(t,e,n,i,s,r){let o=[];const a=aw(n);let l=Number.POSITIVE_INFINITY;function u(c,d,f){const h=c.inRange(e.x,e.y,s);if(i&&!h)return;const p=c.getCenterPoint(s);if(!(!!r||t.isPointInArea(p))&&!h)return;const v=a(e,p);v<l?(o=[{element:c,datasetIndex:d,index:f}],l=v):v===l&&o.push({element:c,datasetIndex:d,index:f})}return ra(t,n,e,u),o}function Ua(t,e,n,i,s,r){return!r&&!t.isPointInArea(e)?[]:n==="r"&&!i?lw(t,e,n,s):uw(t,e,n,i,s,r)}function gf(t,e,n,i,s){const r=[],o=n==="x"?"inXRange":"inYRange";let a=!1;return ra(t,n,e,(l,u,c)=>{l[o]&&l[o](e[n],s)&&(r.push({element:l,datasetIndex:u,index:c}),a=a||l.inRange(e.x,e.y,s))}),i&&!a?[]:r}var cw={modes:{index(t,e,n,i){const s=Sn(e,t),r=n.axis||"x",o=n.includeInvisible||!1,a=n.intersect?Wa(t,s,r,i,o):Ua(t,s,r,!1,i,o),l=[];return a.length?(t.getSortedVisibleDatasetMetas().forEach(u=>{const c=a[0].index,d=u.data[c];d&&!d.skip&&l.push({element:d,datasetIndex:u.index,index:c})}),l):[]},dataset(t,e,n,i){const s=Sn(e,t),r=n.axis||"xy",o=n.includeInvisible||!1;let a=n.intersect?Wa(t,s,r,i,o):Ua(t,s,r,!1,i,o);if(a.length>0){const l=a[0].datasetIndex,u=t.getDatasetMeta(l).data;a=[];for(let c=0;c<u.length;++c)a.push({element:u[c],datasetIndex:l,index:c})}return a},point(t,e,n,i){const s=Sn(e,t),r=n.axis||"xy",o=n.includeInvisible||!1;return Wa(t,s,r,i,o)},nearest(t,e,n,i){const s=Sn(e,t),r=n.axis||"xy",o=n.includeInvisible||!1;return Ua(t,s,r,n.intersect,i,o)},x(t,e,n,i){const s=Sn(e,t);return gf(t,s,"x",n.intersect,i)},y(t,e,n,i){const s=Sn(e,t);return gf(t,s,"y",n.intersect,i)}}};const pm=["left","top","right","bottom"];function Vi(t,e){return t.filter(n=>n.pos===e)}function mf(t,e){return t.filter(n=>pm.indexOf(n.pos)===-1&&n.box.axis===e)}function Hi(t,e){return t.sort((n,i)=>{const s=e?i:n,r=e?n:i;return s.weight===r.weight?s.index-r.index:s.weight-r.weight})}function dw(t){const e=[];let n,i,s,r,o,a;for(n=0,i=(t||[]).length;n<i;++n)s=t[n],{position:r,options:{stack:o,stackWeight:a=1}}=s,e.push({index:n,box:s,pos:r,horizontal:s.isHorizontal(),weight:s.weight,stack:o&&r+o,stackWeight:a});return e}function fw(t){const e={};for(const n of t){const{stack:i,pos:s,stackWeight:r}=n;if(!i||!pm.includes(s))continue;const o=e[i]||(e[i]={count:0,placed:0,weight:0,size:0});o.count++,o.weight+=r}return e}function hw(t,e){const n=fw(t),{vBoxMaxWidth:i,hBoxMaxHeight:s}=e;let r,o,a;for(r=0,o=t.length;r<o;++r){a=t[r];const{fullSize:l}=a.box,u=n[a.stack],c=u&&a.stackWeight/u.weight;a.horizontal?(a.width=c?c*i:l&&e.availableWidth,a.height=s):(a.width=i,a.height=c?c*s:l&&e.availableHeight)}return n}function pw(t){const e=dw(t),n=Hi(e.filter(u=>u.box.fullSize),!0),i=Hi(Vi(e,"left"),!0),s=Hi(Vi(e,"right")),r=Hi(Vi(e,"top"),!0),o=Hi(Vi(e,"bottom")),a=mf(e,"x"),l=mf(e,"y");return{fullSize:n,leftAndTop:i.concat(r),rightAndBottom:s.concat(l).concat(o).concat(a),chartArea:Vi(e,"chartArea"),vertical:i.concat(s).concat(l),horizontal:r.concat(o).concat(a)}}function yf(t,e,n,i){return Math.max(t[n],e[n])+Math.max(t[i],e[i])}function gm(t,e){t.top=Math.max(t.top,e.top),t.left=Math.max(t.left,e.left),t.bottom=Math.max(t.bottom,e.bottom),t.right=Math.max(t.right,e.right)}function gw(t,e,n,i){const{pos:s,box:r}=n,o=t.maxPadding;if(!Y(s)){n.size&&(t[s]-=n.size);const d=i[n.stack]||{size:0,count:1};d.size=Math.max(d.size,n.horizontal?r.height:r.width),n.size=d.size/d.count,t[s]+=n.size}r.getPadding&&gm(o,r.getPadding());const a=Math.max(0,e.outerWidth-yf(o,t,"left","right")),l=Math.max(0,e.outerHeight-yf(o,t,"top","bottom")),u=a!==t.w,c=l!==t.h;return t.w=a,t.h=l,n.horizontal?{same:u,other:c}:{same:c,other:u}}function mw(t){const e=t.maxPadding;function n(i){const s=Math.max(e[i]-t[i],0);return t[i]+=s,s}t.y+=n("top"),t.x+=n("left"),n("right"),n("bottom")}function yw(t,e){const n=e.maxPadding;function i(s){const r={left:0,top:0,right:0,bottom:0};return s.forEach(o=>{r[o]=Math.max(e[o],n[o])}),r}return i(t?["left","right"]:["top","bottom"])}function Zi(t,e,n,i){const s=[];let r,o,a,l,u,c;for(r=0,o=t.length,u=0;r<o;++r){a=t[r],l=a.box,l.update(a.width||e.w,a.height||e.h,yw(a.horizontal,e));const{same:d,other:f}=gw(e,n,a,i);u|=d&&s.length,c=c||f,l.fullSize||s.push(a)}return u&&Zi(s,e,n,i)||c}function Nr(t,e,n,i,s){t.top=n,t.left=e,t.right=e+i,t.bottom=n+s,t.width=i,t.height=s}function xf(t,e,n,i){const s=n.padding;let{x:r,y:o}=e;for(const a of t){const l=a.box,u=i[a.stack]||{placed:0,weight:1},c=a.stackWeight/u.weight||1;if(a.horizontal){const d=e.w*c,f=u.size||l.height;zs(u.start)&&(o=u.start),l.fullSize?Nr(l,s.left,o,n.outerWidth-s.right-s.left,f):Nr(l,e.left+u.placed,o,d,f),u.start=o,u.placed+=d,o=l.bottom}else{const d=e.h*c,f=u.size||l.width;zs(u.start)&&(r=u.start),l.fullSize?Nr(l,r,s.top,f,n.outerHeight-s.bottom-s.top):Nr(l,r,e.top+u.placed,f,d),u.start=r,u.placed+=d,r=l.right}}e.x=r,e.y=o}var tt={addBox(t,e){t.boxes||(t.boxes=[]),e.fullSize=e.fullSize||!1,e.position=e.position||"top",e.weight=e.weight||0,e._layers=e._layers||function(){return[{z:0,draw(n){e.draw(n)}}]},t.boxes.push(e)},removeBox(t,e){const n=t.boxes?t.boxes.indexOf(e):-1;n!==-1&&t.boxes.splice(n,1)},configure(t,e,n){e.fullSize=n.fullSize,e.position=n.position,e.weight=n.weight},update(t,e,n,i){if(!t)return;const s=ot(t.options.layout.padding),r=Math.max(e-s.width,0),o=Math.max(n-s.height,0),a=pw(t.boxes),l=a.vertical,u=a.horizontal;Q(t.boxes,y=>{typeof y.beforeLayout=="function"&&y.beforeLayout()});const c=l.reduce((y,v)=>v.box.options&&v.box.options.display===!1?y:y+1,0)||1,d=Object.freeze({outerWidth:e,outerHeight:n,padding:s,availableWidth:r,availableHeight:o,vBoxMaxWidth:r/2/c,hBoxMaxHeight:o/2}),f=Object.assign({},s);gm(f,ot(i));const h=Object.assign({maxPadding:f,w:r,h:o,x:s.left,y:s.top},s),p=hw(l.concat(u),d);Zi(a.fullSize,h,d,p),Zi(l,h,d,p),Zi(u,h,d,p)&&Zi(l,h,d,p),mw(h),xf(a.leftAndTop,h,d,p),h.x+=h.w,h.y+=h.h,xf(a.rightAndBottom,h,d,p),t.chartArea={left:h.left,top:h.top,right:h.left+h.w,bottom:h.top+h.h,height:h.h,width:h.w},Q(a.chartArea,y=>{const v=y.box;Object.assign(v,t.chartArea),v.update(h.w,h.h,{left:0,top:0,right:0,bottom:0})})}};class mm{acquireContext(e,n){}releaseContext(e){return!1}addEventListener(e,n,i){}removeEventListener(e,n,i){}getDevicePixelRatio(){return 1}getMaximumSize(e,n,i,s){return n=Math.max(0,n||e.width),i=i||e.height,{width:n,height:Math.max(0,s?Math.floor(n/s):i)}}isAttached(e){return!0}updateConfig(e){}}class xw extends mm{acquireContext(e){return e&&e.getContext&&e.getContext("2d")||null}updateConfig(e){e.options.animation=!1}}const Gr="$chartjs",vw={touchstart:"mousedown",touchmove:"mousemove",touchend:"mouseup",pointerenter:"mouseenter",pointerdown:"mousedown",pointermove:"mousemove",pointerup:"mouseup",pointerleave:"mouseout",pointerout:"mouseout"},vf=t=>t===null||t==="";function _w(t,e){const n=t.style,i=t.getAttribute("height"),s=t.getAttribute("width");if(t[Gr]={initial:{height:i,width:s,style:{display:n.display,height:n.height,width:n.width}}},n.display=n.display||"block",n.boxSizing=n.boxSizing||"border-box",vf(s)){const r=tf(t,"width");r!==void 0&&(t.width=r)}if(vf(i))if(t.style.height==="")t.height=t.width/(e||2);else{const r=tf(t,"height");r!==void 0&&(t.height=r)}return t}const ym=v_?{passive:!0}:!1;function ww(t,e,n){t&&t.addEventListener(e,n,ym)}function Sw(t,e,n){t&&t.canvas&&t.canvas.removeEventListener(e,n,ym)}function bw(t,e){const n=vw[t.type]||t.type,{x:i,y:s}=Sn(t,e);return{type:n,chart:e,native:t,x:i!==void 0?i:null,y:s!==void 0?s:null}}function To(t,e){for(const n of t)if(n===e||n.contains(e))return!0}function kw(t,e,n){const i=t.canvas,s=new MutationObserver(r=>{let o=!1;for(const a of r)o=o||To(a.addedNodes,i),o=o&&!To(a.removedNodes,i);o&&n()});return s.observe(document,{childList:!0,subtree:!0}),s}function Cw(t,e,n){const i=t.canvas,s=new MutationObserver(r=>{let o=!1;for(const a of r)o=o||To(a.removedNodes,i),o=o&&!To(a.addedNodes,i);o&&n()});return s.observe(document,{childList:!0,subtree:!0}),s}const Ws=new Map;let _f=0;function xm(){const t=window.devicePixelRatio;t!==_f&&(_f=t,Ws.forEach((e,n)=>{n.currentDevicePixelRatio!==t&&e()}))}function Pw(t,e){Ws.size||window.addEventListener("resize",xm),Ws.set(t,e)}function Mw(t){Ws.delete(t),Ws.size||window.removeEventListener("resize",xm)}function Ew(t,e,n){const i=t.canvas,s=i&&gc(i);if(!s)return;const r=Zg((a,l)=>{const u=s.clientWidth;n(a,l),u<s.clientWidth&&n()},window),o=new ResizeObserver(a=>{const l=a[0],u=l.contentRect.width,c=l.contentRect.height;u===0&&c===0||r(u,c)});return o.observe(s),Pw(t,r),o}function Ka(t,e,n){n&&n.disconnect(),e==="resize"&&Mw(t)}function Ow(t,e,n){const i=t.canvas,s=Zg(r=>{t.ctx!==null&&n(bw(r,t))},t);return ww(i,e,s),s}class Nw extends mm{acquireContext(e,n){const i=e&&e.getContext&&e.getContext("2d");return i&&i.canvas===e?(_w(e,n),i):null}releaseContext(e){const n=e.canvas;if(!n[Gr])return!1;const i=n[Gr].initial;["height","width"].forEach(r=>{const o=i[r];X(o)?n.removeAttribute(r):n.setAttribute(r,o)});const s=i.style||{};return Object.keys(s).forEach(r=>{n.style[r]=s[r]}),n.width=n.width,delete n[Gr],!0}addEventListener(e,n,i){this.removeEventListener(e,n);const s=e.$proxies||(e.$proxies={}),o={attach:kw,detach:Cw,resize:Ew}[n]||Ow;s[n]=o(e,n,i)}removeEventListener(e,n){const i=e.$proxies||(e.$proxies={}),s=i[n];if(!s)return;({attach:Ka,detach:Ka,resize:Ka}[n]||Sw)(e,n,s),i[n]=void 0}getDevicePixelRatio(){return window.devicePixelRatio}getMaximumSize(e,n,i,s){return x_(e,n,i,s)}isAttached(e){const n=e&&gc(e);return!!(n&&n.isConnected)}}function Lw(t){return!pc()||typeof OffscreenCanvas<"u"&&t instanceof OffscreenCanvas?xw:Nw}class gt{constructor(){D(this,"x");D(this,"y");D(this,"active",!1);D(this,"options");D(this,"$animations")}tooltipPosition(e){const{x:n,y:i}=this.getProps(["x","y"],e);return{x:n,y:i}}hasValue(){return Bs(this.x)&&Bs(this.y)}getProps(e,n){const i=this.$animations;if(!n||!i)return this;const s={};return e.forEach(r=>{s[r]=i[r]&&i[r].active()?i[r]._to:this[r]}),s}}D(gt,"defaults",{}),D(gt,"defaultRoutes");function Tw(t,e){const n=t.options.ticks,i=Rw(t),s=Math.min(n.maxTicksLimit||i,i),r=n.major.enabled?jw(e):[],o=r.length,a=r[0],l=r[o-1],u=[];if(o>s)return Aw(e,u,r,o/s),u;const c=Dw(r,e,s);if(o>0){let d,f;const h=o>1?Math.round((l-a)/(o-1)):null;for(Lr(e,u,c,X(h)?0:a-h,a),d=0,f=o-1;d<f;d++)Lr(e,u,c,r[d],r[d+1]);return Lr(e,u,c,l,X(h)?e.length:l+h),u}return Lr(e,u,c),u}function Rw(t){const e=t.options.offset,n=t._tickSize(),i=t._length/n+(e?0:1),s=t._maxLength/n;return Math.floor(Math.min(i,s))}function Dw(t,e,n){const i=Iw(t),s=e.length/n;if(!i)return Math.max(s,1);const r=y1(i);for(let o=0,a=r.length-1;o<a;o++){const l=r[o];if(l>s)return l}return Math.max(s,1)}function jw(t){const e=[];let n,i;for(n=0,i=t.length;n<i;n++)t[n].major&&e.push(n);return e}function Aw(t,e,n,i){let s=0,r=n[0],o;for(i=Math.ceil(i),o=0;o<t.length;o++)o===r&&(e.push(t[o]),s++,r=n[s*i])}function Lr(t,e,n,i,s){const r=B(i,0),o=Math.min(B(s,t.length),t.length);let a=0,l,u,c;for(n=Math.ceil(n),s&&(l=s-i,n=l/Math.floor(l/n)),c=r;c<0;)a++,c=Math.round(r+a*n);for(u=Math.max(r,0);u<o;u++)u===c&&(e.push(t[u]),a++,c=Math.round(r+a*n))}function Iw(t){const e=t.length;let n,i;if(e<2)return!1;for(i=t[0],n=1;n<e;++n)if(t[n]-t[n-1]!==i)return!1;return i}const Fw=t=>t==="left"?"right":t==="right"?"left":t,wf=(t,e,n)=>e==="top"||e==="left"?t[e]+n:t[e]-n,Sf=(t,e)=>Math.min(e||t,t);function bf(t,e){const n=[],i=t.length/e,s=t.length;let r=0;for(;r<s;r+=i)n.push(t[Math.floor(r)]);return n}function zw(t,e,n){const i=t.ticks.length,s=Math.min(e,i-1),r=t._startPixel,o=t._endPixel,a=1e-6;let l=t.getPixelForTick(s),u;if(!(n&&(i===1?u=Math.max(l-r,o-l):e===0?u=(t.getPixelForTick(1)-l)/2:u=(l-t.getPixelForTick(s-1))/2,l+=s<e?u:-u,l<r-a||l>o+a)))return l}function Bw(t,e){Q(t,n=>{const i=n.gc,s=i.length/2;let r;if(s>e){for(r=0;r<s;++r)delete n.data[i[r]];i.splice(0,s)}})}function Wi(t){return t.drawTicks?t.tickLength:0}function kf(t,e){if(!t.display)return 0;const n=Oe(t.font,e),i=ot(t.padding);return(pe(t.text)?t.text.length:1)*n.lineHeight+i.height}function $w(t,e){return Hn(t,{scale:e,type:"scale"})}function Vw(t,e,n){return Hn(t,{tick:n,index:e,type:"tick"})}function Hw(t,e,n){let i=rc(t);return(n&&e!=="right"||!n&&e==="right")&&(i=Fw(i)),i}function Ww(t,e,n,i){const{top:s,left:r,bottom:o,right:a,chart:l}=t,{chartArea:u,scales:c}=l;let d=0,f,h,p;const y=o-s,v=a-r;if(t.isHorizontal()){if(h=Pe(i,r,a),Y(n)){const m=Object.keys(n)[0],g=n[m];p=c[m].getPixelForValue(g)+y-e}else n==="center"?p=(u.bottom+u.top)/2+y-e:p=wf(t,n,e);f=a-r}else{if(Y(n)){const m=Object.keys(n)[0],g=n[m];h=c[m].getPixelForValue(g)-v+e}else n==="center"?h=(u.left+u.right)/2-v+e:h=wf(t,n,e);p=Pe(i,o,s),d=n==="left"?-me:me}return{titleX:h,titleY:p,maxWidth:f,rotation:d}}class Ti extends gt{constructor(e){super(),this.id=e.id,this.type=e.type,this.options=void 0,this.ctx=e.ctx,this.chart=e.chart,this.top=void 0,this.bottom=void 0,this.left=void 0,this.right=void 0,this.width=void 0,this.height=void 0,this._margins={left:0,right:0,top:0,bottom:0},this.maxWidth=void 0,this.maxHeight=void 0,this.paddingTop=void 0,this.paddingBottom=void 0,this.paddingLeft=void 0,this.paddingRight=void 0,this.axis=void 0,this.labelRotation=void 0,this.min=void 0,this.max=void 0,this._range=void 0,this.ticks=[],this._gridLineItems=null,this._labelItems=null,this._labelSizes=null,this._length=0,this._maxLength=0,this._longestTextCache={},this._startPixel=void 0,this._endPixel=void 0,this._reversePixels=!1,this._userMax=void 0,this._userMin=void 0,this._suggestedMax=void 0,this._suggestedMin=void 0,this._ticksLength=0,this._borderValue=0,this._cache={},this._dataLimitsCached=!1,this.$context=void 0}init(e){this.options=e.setContext(this.getContext()),this.axis=e.axis,this._userMin=this.parse(e.min),this._userMax=this.parse(e.max),this._suggestedMin=this.parse(e.suggestedMin),this._suggestedMax=this.parse(e.suggestedMax)}parse(e,n){return e}getUserBounds(){let{_userMin:e,_userMax:n,_suggestedMin:i,_suggestedMax:s}=this;return e=yt(e,Number.POSITIVE_INFINITY),n=yt(n,Number.NEGATIVE_INFINITY),i=yt(i,Number.POSITIVE_INFINITY),s=yt(s,Number.NEGATIVE_INFINITY),{min:yt(e,i),max:yt(n,s),minDefined:rt(e),maxDefined:rt(n)}}getMinMax(e){let{min:n,max:i,minDefined:s,maxDefined:r}=this.getUserBounds(),o;if(s&&r)return{min:n,max:i};const a=this.getMatchingVisibleMetas();for(let l=0,u=a.length;l<u;++l)o=a[l].controller.getMinMax(this,e),s||(n=Math.min(n,o.min)),r||(i=Math.max(i,o.max));return n=r&&n>i?i:n,i=s&&n>i?n:i,{min:yt(n,yt(i,n)),max:yt(i,yt(n,i))}}getPadding(){return{left:this.paddingLeft||0,top:this.paddingTop||0,right:this.paddingRight||0,bottom:this.paddingBottom||0}}getTicks(){return this.ticks}getLabels(){const e=this.chart.data;return this.options.labels||(this.isHorizontal()?e.xLabels:e.yLabels)||e.labels||[]}getLabelItems(e=this.chart.chartArea){return this._labelItems||(this._labelItems=this._computeLabelItems(e))}beforeLayout(){this._cache={},this._dataLimitsCached=!1}beforeUpdate(){te(this.options.beforeUpdate,[this])}update(e,n,i){const{beginAtZero:s,grace:r,ticks:o}=this.options,a=o.sampleSize;this.beforeUpdate(),this.maxWidth=e,this.maxHeight=n,this._margins=i=Object.assign({left:0,right:0,top:0,bottom:0},i),this.ticks=null,this._labelSizes=null,this._gridLineItems=null,this._labelItems=null,this.beforeSetDimensions(),this.setDimensions(),this.afterSetDimensions(),this._maxLength=this.isHorizontal()?this.width+i.left+i.right:this.height+i.top+i.bottom,this._dataLimitsCached||(this.beforeDataLimits(),this.determineDataLimits(),this.afterDataLimits(),this._range=q1(this,r,s),this._dataLimitsCached=!0),this.beforeBuildTicks(),this.ticks=this.buildTicks()||[],this.afterBuildTicks();const l=a<this.ticks.length;this._convertTicksToLabels(l?bf(this.ticks,a):this.ticks),this.configure(),this.beforeCalculateLabelRotation(),this.calculateLabelRotation(),this.afterCalculateLabelRotation(),o.display&&(o.autoSkip||o.source==="auto")&&(this.ticks=Tw(this,this.ticks),this._labelSizes=null,this.afterAutoSkip()),l&&this._convertTicksToLabels(this.ticks),this.beforeFit(),this.fit(),this.afterFit(),this.afterUpdate()}configure(){let e=this.options.reverse,n,i;this.isHorizontal()?(n=this.left,i=this.right):(n=this.top,i=this.bottom,e=!e),this._startPixel=n,this._endPixel=i,this._reversePixels=e,this._length=i-n,this._alignToPixels=this.options.alignToPixels}afterUpdate(){te(this.options.afterUpdate,[this])}beforeSetDimensions(){te(this.options.beforeSetDimensions,[this])}setDimensions(){this.isHorizontal()?(this.width=this.maxWidth,this.left=0,this.right=this.width):(this.height=this.maxHeight,this.top=0,this.bottom=this.height),this.paddingLeft=0,this.paddingTop=0,this.paddingRight=0,this.paddingBottom=0}afterSetDimensions(){te(this.options.afterSetDimensions,[this])}_callHooks(e){this.chart.notifyPlugins(e,this.getContext()),te(this.options[e],[this])}beforeDataLimits(){this._callHooks("beforeDataLimits")}determineDataLimits(){}afterDataLimits(){this._callHooks("afterDataLimits")}beforeBuildTicks(){this._callHooks("beforeBuildTicks")}buildTicks(){return[]}afterBuildTicks(){this._callHooks("afterBuildTicks")}beforeTickToLabelConversion(){te(this.options.beforeTickToLabelConversion,[this])}generateTickLabels(e){const n=this.options.ticks;let i,s,r;for(i=0,s=e.length;i<s;i++)r=e[i],r.label=te(n.callback,[r.value,i,e],this)}afterTickToLabelConversion(){te(this.options.afterTickToLabelConversion,[this])}beforeCalculateLabelRotation(){te(this.options.beforeCalculateLabelRotation,[this])}calculateLabelRotation(){const e=this.options,n=e.ticks,i=Sf(this.ticks.length,e.ticks.maxTicksLimit),s=n.minRotation||0,r=n.maxRotation;let o=s,a,l,u;if(!this._isVisible()||!n.display||s>=r||i<=1||!this.isHorizontal()){this.labelRotation=s;return}const c=this._getLabelSizes(),d=c.widest.width,f=c.highest.height,h=Ee(this.chart.width-d,0,this.maxWidth);a=e.offset?this.maxWidth/i:h/(i-1),d+6>a&&(a=h/(i-(e.offset?.5:1)),l=this.maxHeight-Wi(e.grid)-n.padding-kf(e.title,this.chart.options.font),u=Math.sqrt(d*d+f*f),o=w1(Math.min(Math.asin(Ee((c.highest.height+6)/a,-1,1)),Math.asin(Ee(l/u,-1,1))-Math.asin(Ee(f/u,-1,1)))),o=Math.max(s,Math.min(r,o))),this.labelRotation=o}afterCalculateLabelRotation(){te(this.options.afterCalculateLabelRotation,[this])}afterAutoSkip(){}beforeFit(){te(this.options.beforeFit,[this])}fit(){const e={width:0,height:0},{chart:n,options:{ticks:i,title:s,grid:r}}=this,o=this._isVisible(),a=this.isHorizontal();if(o){const l=kf(s,n.options.font);if(a?(e.width=this.maxWidth,e.height=Wi(r)+l):(e.height=this.maxHeight,e.width=Wi(r)+l),i.display&&this.ticks.length){const{first:u,last:c,widest:d,highest:f}=this._getLabelSizes(),h=i.padding*2,p=Dt(this.labelRotation),y=Math.cos(p),v=Math.sin(p);if(a){const m=i.mirror?0:v*d.width+y*f.height;e.height=Math.min(this.maxHeight,e.height+m+h)}else{const m=i.mirror?0:y*d.width+v*f.height;e.width=Math.min(this.maxWidth,e.width+m+h)}this._calculatePadding(u,c,v,y)}}this._handleMargins(),a?(this.width=this._length=n.width-this._margins.left-this._margins.right,this.height=e.height):(this.width=e.width,this.height=this._length=n.height-this._margins.top-this._margins.bottom)}_calculatePadding(e,n,i,s){const{ticks:{align:r,padding:o},position:a}=this.options,l=this.labelRotation!==0,u=a!=="top"&&this.axis==="x";if(this.isHorizontal()){const c=this.getPixelForTick(0)-this.left,d=this.right-this.getPixelForTick(this.ticks.length-1);let f=0,h=0;l?u?(f=s*e.width,h=i*n.height):(f=i*e.height,h=s*n.width):r==="start"?h=n.width:r==="end"?f=e.width:r!=="inner"&&(f=e.width/2,h=n.width/2),this.paddingLeft=Math.max((f-c+o)*this.width/(this.width-c),0),this.paddingRight=Math.max((h-d+o)*this.width/(this.width-d),0)}else{let c=n.height/2,d=e.height/2;r==="start"?(c=0,d=e.height):r==="end"&&(c=n.height,d=0),this.paddingTop=c+o,this.paddingBottom=d+o}}_handleMargins(){this._margins&&(this._margins.left=Math.max(this.paddingLeft,this._margins.left),this._margins.top=Math.max(this.paddingTop,this._margins.top),this._margins.right=Math.max(this.paddingRight,this._margins.right),this._margins.bottom=Math.max(this.paddingBottom,this._margins.bottom))}afterFit(){te(this.options.afterFit,[this])}isHorizontal(){const{axis:e,position:n}=this.options;return n==="top"||n==="bottom"||e==="x"}isFullSize(){return this.options.fullSize}_convertTicksToLabels(e){this.beforeTickToLabelConversion(),this.generateTickLabels(e);let n,i;for(n=0,i=e.length;n<i;n++)X(e[n].label)&&(e.splice(n,1),i--,n--);this.afterTickToLabelConversion()}_getLabelSizes(){let e=this._labelSizes;if(!e){const n=this.options.ticks.sampleSize;let i=this.ticks;n<i.length&&(i=bf(i,n)),this._labelSizes=e=this._computeLabelSizes(i,i.length,this.options.ticks.maxTicksLimit)}return e}_computeLabelSizes(e,n,i){const{ctx:s,_longestTextCache:r}=this,o=[],a=[],l=Math.floor(n/Sf(n,i));let u=0,c=0,d,f,h,p,y,v,m,g,x,w,S;for(d=0;d<n;d+=l){if(p=e[d].label,y=this._resolveTickFontOptions(d),s.font=v=y.string,m=r[v]=r[v]||{data:{},gc:[]},g=y.lineHeight,x=w=0,!X(p)&&!pe(p))x=qd(s,m.data,m.gc,x,p),w=g;else if(pe(p))for(f=0,h=p.length;f<h;++f)S=p[f],!X(S)&&!pe(S)&&(x=qd(s,m.data,m.gc,x,S),w+=g);o.push(x),a.push(w),u=Math.max(x,u),c=Math.max(w,c)}Bw(r,n);const C=o.indexOf(u),k=a.indexOf(c),b=M=>({width:o[M]||0,height:a[M]||0});return{first:b(0),last:b(n-1),widest:b(C),highest:b(k),widths:o,heights:a}}getLabelForValue(e){return e}getPixelForValue(e,n){return NaN}getValueForPixel(e){}getPixelForTick(e){const n=this.ticks;return e<0||e>n.length-1?null:this.getPixelForValue(n[e].value)}getPixelForDecimal(e){this._reversePixels&&(e=1-e);const n=this._startPixel+e*this._length;return b1(this._alignToPixels?xn(this.chart,n,0):n)}getDecimalForPixel(e){const n=(e-this._startPixel)/this._length;return this._reversePixels?1-n:n}getBasePixel(){return this.getPixelForValue(this.getBaseValue())}getBaseValue(){const{min:e,max:n}=this;return e<0&&n<0?n:e>0&&n>0?e:0}getContext(e){const n=this.ticks||[];if(e>=0&&e<n.length){const i=n[e];return i.$context||(i.$context=Vw(this.getContext(),e,i))}return this.$context||(this.$context=$w(this.chart.getContext(),this))}_tickSize(){const e=this.options.ticks,n=Dt(this.labelRotation),i=Math.abs(Math.cos(n)),s=Math.abs(Math.sin(n)),r=this._getLabelSizes(),o=e.autoSkipPadding||0,a=r?r.widest.width+o:0,l=r?r.highest.height+o:0;return this.isHorizontal()?l*i>a*s?a/i:l/s:l*s<a*i?l/i:a/s}_isVisible(){const e=this.options.display;return e!=="auto"?!!e:this.getMatchingVisibleMetas().length>0}_computeGridLineItems(e){const n=this.axis,i=this.chart,s=this.options,{grid:r,position:o,border:a}=s,l=r.offset,u=this.isHorizontal(),d=this.ticks.length+(l?1:0),f=Wi(r),h=[],p=a.setContext(this.getContext()),y=p.display?p.width:0,v=y/2,m=function(z){return xn(i,z,y)};let g,x,w,S,C,k,b,M,P,T,I,H;if(o==="top")g=m(this.bottom),k=this.bottom-f,M=g-v,T=m(e.top)+v,H=e.bottom;else if(o==="bottom")g=m(this.top),T=e.top,H=m(e.bottom)-v,k=g+v,M=this.top+f;else if(o==="left")g=m(this.right),C=this.right-f,b=g-v,P=m(e.left)+v,I=e.right;else if(o==="right")g=m(this.left),P=e.left,I=m(e.right)-v,C=g+v,b=this.left+f;else if(n==="x"){if(o==="center")g=m((e.top+e.bottom)/2+.5);else if(Y(o)){const z=Object.keys(o)[0],V=o[z];g=m(this.chart.scales[z].getPixelForValue(V))}T=e.top,H=e.bottom,k=g+v,M=k+f}else if(n==="y"){if(o==="center")g=m((e.left+e.right)/2);else if(Y(o)){const z=Object.keys(o)[0],V=o[z];g=m(this.chart.scales[z].getPixelForValue(V))}C=g-v,b=C-f,P=e.left,I=e.right}const J=B(s.ticks.maxTicksLimit,d),W=Math.max(1,Math.ceil(d/J));for(x=0;x<d;x+=W){const z=this.getContext(x),V=r.setContext(z),N=a.setContext(z),R=V.lineWidth,j=V.color,F=N.dash||[],U=N.dashOffset,be=V.tickWidth,ye=V.tickColor,qe=V.tickBorderDash||[],Te=V.tickBorderDashOffset;w=zw(this,x,l),w!==void 0&&(S=xn(i,w,R),u?C=b=P=I=S:k=M=T=H=S,h.push({tx1:C,ty1:k,tx2:b,ty2:M,x1:P,y1:T,x2:I,y2:H,width:R,color:j,borderDash:F,borderDashOffset:U,tickWidth:be,tickColor:ye,tickBorderDash:qe,tickBorderDashOffset:Te}))}return this._ticksLength=d,this._borderValue=g,h}_computeLabelItems(e){const n=this.axis,i=this.options,{position:s,ticks:r}=i,o=this.isHorizontal(),a=this.ticks,{align:l,crossAlign:u,padding:c,mirror:d}=r,f=Wi(i.grid),h=f+c,p=d?-c:h,y=-Dt(this.labelRotation),v=[];let m,g,x,w,S,C,k,b,M,P,T,I,H="middle";if(s==="top")C=this.bottom-p,k=this._getXAxisLabelAlignment();else if(s==="bottom")C=this.top+p,k=this._getXAxisLabelAlignment();else if(s==="left"){const W=this._getYAxisLabelAlignment(f);k=W.textAlign,S=W.x}else if(s==="right"){const W=this._getYAxisLabelAlignment(f);k=W.textAlign,S=W.x}else if(n==="x"){if(s==="center")C=(e.top+e.bottom)/2+h;else if(Y(s)){const W=Object.keys(s)[0],z=s[W];C=this.chart.scales[W].getPixelForValue(z)+h}k=this._getXAxisLabelAlignment()}else if(n==="y"){if(s==="center")S=(e.left+e.right)/2-h;else if(Y(s)){const W=Object.keys(s)[0],z=s[W];S=this.chart.scales[W].getPixelForValue(z)}k=this._getYAxisLabelAlignment(f).textAlign}n==="y"&&(l==="start"?H="top":l==="end"&&(H="bottom"));const J=this._getLabelSizes();for(m=0,g=a.length;m<g;++m){x=a[m],w=x.label;const W=r.setContext(this.getContext(m));b=this.getPixelForTick(m)+r.labelOffset,M=this._resolveTickFontOptions(m),P=M.lineHeight,T=pe(w)?w.length:1;const z=T/2,V=W.color,N=W.textStrokeColor,R=W.textStrokeWidth;let j=k;o?(S=b,k==="inner"&&(m===g-1?j=this.options.reverse?"left":"right":m===0?j=this.options.reverse?"right":"left":j="center"),s==="top"?u==="near"||y!==0?I=-T*P+P/2:u==="center"?I=-J.highest.height/2-z*P+P:I=-J.highest.height+P/2:u==="near"||y!==0?I=P/2:u==="center"?I=J.highest.height/2-z*P:I=J.highest.height-T*P,d&&(I*=-1),y!==0&&!W.showLabelBackdrop&&(S+=P/2*Math.sin(y))):(C=b,I=(1-T)*P/2);let F;if(W.showLabelBackdrop){const U=ot(W.backdropPadding),be=J.heights[m],ye=J.widths[m];let qe=I-U.top,Te=0-U.left;switch(H){case"middle":qe-=be/2;break;case"bottom":qe-=be;break}switch(k){case"center":Te-=ye/2;break;case"right":Te-=ye;break;case"inner":m===g-1?Te-=ye:m>0&&(Te-=ye/2);break}F={left:Te,top:qe,width:ye+U.width,height:be+U.height,color:W.backdropColor}}v.push({label:w,font:M,textOffset:I,options:{rotation:y,color:V,strokeColor:N,strokeWidth:R,textAlign:j,textBaseline:H,translation:[S,C],backdrop:F}})}return v}_getXAxisLabelAlignment(){const{position:e,ticks:n}=this.options;if(-Dt(this.labelRotation))return e==="top"?"left":"right";let s="center";return n.align==="start"?s="left":n.align==="end"?s="right":n.align==="inner"&&(s="inner"),s}_getYAxisLabelAlignment(e){const{position:n,ticks:{crossAlign:i,mirror:s,padding:r}}=this.options,o=this._getLabelSizes(),a=e+r,l=o.widest.width;let u,c;return n==="left"?s?(c=this.right+r,i==="near"?u="left":i==="center"?(u="center",c+=l/2):(u="right",c+=l)):(c=this.right-a,i==="near"?u="right":i==="center"?(u="center",c-=l/2):(u="left",c=this.left)):n==="right"?s?(c=this.left+r,i==="near"?u="right":i==="center"?(u="center",c-=l/2):(u="left",c-=l)):(c=this.left+a,i==="near"?u="left":i==="center"?(u="center",c+=l/2):(u="right",c=this.right)):u="right",{textAlign:u,x:c}}_computeLabelArea(){if(this.options.ticks.mirror)return;const e=this.chart,n=this.options.position;if(n==="left"||n==="right")return{top:0,left:this.left,bottom:e.height,right:this.right};if(n==="top"||n==="bottom")return{top:this.top,left:0,bottom:this.bottom,right:e.width}}drawBackground(){const{ctx:e,options:{backgroundColor:n},left:i,top:s,width:r,height:o}=this;n&&(e.save(),e.fillStyle=n,e.fillRect(i,s,r,o),e.restore())}getLineWidthForValue(e){const n=this.options.grid;if(!this._isVisible()||!n.display)return 0;const s=this.ticks.findIndex(r=>r.value===e);return s>=0?n.setContext(this.getContext(s)).lineWidth:0}drawGrid(e){const n=this.options.grid,i=this.ctx,s=this._gridLineItems||(this._gridLineItems=this._computeGridLineItems(e));let r,o;const a=(l,u,c)=>{!c.width||!c.color||(i.save(),i.lineWidth=c.width,i.strokeStyle=c.color,i.setLineDash(c.borderDash||[]),i.lineDashOffset=c.borderDashOffset,i.beginPath(),i.moveTo(l.x,l.y),i.lineTo(u.x,u.y),i.stroke(),i.restore())};if(n.display)for(r=0,o=s.length;r<o;++r){const l=s[r];n.drawOnChartArea&&a({x:l.x1,y:l.y1},{x:l.x2,y:l.y2},l),n.drawTicks&&a({x:l.tx1,y:l.ty1},{x:l.tx2,y:l.ty2},{color:l.tickColor,width:l.tickWidth,borderDash:l.tickBorderDash,borderDashOffset:l.tickBorderDashOffset})}}drawBorder(){const{chart:e,ctx:n,options:{border:i,grid:s}}=this,r=i.setContext(this.getContext()),o=i.display?r.width:0;if(!o)return;const a=s.setContext(this.getContext(0)).lineWidth,l=this._borderValue;let u,c,d,f;this.isHorizontal()?(u=xn(e,this.left,o)-o/2,c=xn(e,this.right,a)+a/2,d=f=l):(d=xn(e,this.top,o)-o/2,f=xn(e,this.bottom,a)+a/2,u=c=l),n.save(),n.lineWidth=r.width,n.strokeStyle=r.color,n.beginPath(),n.moveTo(u,d),n.lineTo(c,f),n.stroke(),n.restore()}drawLabels(e){if(!this.options.ticks.display)return;const i=this.ctx,s=this._computeLabelArea();s&&lc(i,s);const r=this.getLabelItems(e);for(const o of r){const a=o.options,l=o.font,u=o.label,c=o.textOffset;Hs(i,u,0,c,l,a)}s&&uc(i)}drawTitle(){const{ctx:e,options:{position:n,title:i,reverse:s}}=this;if(!i.display)return;const r=Oe(i.font),o=ot(i.padding),a=i.align;let l=r.lineHeight/2;n==="bottom"||n==="center"||Y(n)?(l+=o.bottom,pe(i.text)&&(l+=r.lineHeight*(i.text.length-1))):l+=o.top;const{titleX:u,titleY:c,maxWidth:d,rotation:f}=Ww(this,l,n,a);Hs(e,i.text,0,0,r,{color:i.color,maxWidth:d,rotation:f,textAlign:Hw(a,n,s),textBaseline:"middle",translation:[u,c]})}draw(e){this._isVisible()&&(this.drawBackground(),this.drawGrid(e),this.drawBorder(),this.drawTitle(),this.drawLabels(e))}_layers(){const e=this.options,n=e.ticks&&e.ticks.z||0,i=B(e.grid&&e.grid.z,-1),s=B(e.border&&e.border.z,0);return!this._isVisible()||this.draw!==Ti.prototype.draw?[{z:n,draw:r=>{this.draw(r)}}]:[{z:i,draw:r=>{this.drawBackground(),this.drawGrid(r),this.drawTitle()}},{z:s,draw:()=>{this.drawBorder()}},{z:n,draw:r=>{this.drawLabels(r)}}]}getMatchingVisibleMetas(e){const n=this.chart.getSortedVisibleDatasetMetas(),i=this.axis+"AxisID",s=[];let r,o;for(r=0,o=n.length;r<o;++r){const a=n[r];a[i]===this.id&&(!e||a.type===e)&&s.push(a)}return s}_resolveTickFontOptions(e){const n=this.options.ticks.setContext(this.getContext(e));return Oe(n.font)}_maxDigits(){const e=this._resolveTickFontOptions(0).lineHeight;return(this.isHorizontal()?this.width:this.height)/e}}class Tr{constructor(e,n,i){this.type=e,this.scope=n,this.override=i,this.items=Object.create(null)}isForType(e){return Object.prototype.isPrototypeOf.call(this.type.prototype,e.prototype)}register(e){const n=Object.getPrototypeOf(e);let i;Yw(n)&&(i=this.register(n));const s=this.items,r=e.id,o=this.scope+"."+r;if(!r)throw new Error("class does not have id: "+e);return r in s||(s[r]=e,Uw(e,o,i),this.override&&de.override(e.id,e.overrides)),o}get(e){return this.items[e]}unregister(e){const n=this.items,i=e.id,s=this.scope;i in n&&delete n[i],s&&i in de[s]&&(delete de[s][i],this.override&&delete zn[i])}}function Uw(t,e,n){const i=Fs(Object.create(null),[n?de.get(n):{},de.get(e),t.defaults]);de.set(e,i),t.defaultRoutes&&Kw(e,t.defaultRoutes),t.descriptors&&de.describe(e,t.descriptors)}function Kw(t,e){Object.keys(e).forEach(n=>{const i=n.split("."),s=i.pop(),r=[t].concat(i).join("."),o=e[n].split("."),a=o.pop(),l=o.join(".");de.route(r,s,l,a)})}function Yw(t){return"id"in t&&"defaults"in t}class Xw{constructor(){this.controllers=new Tr(Tn,"datasets",!0),this.elements=new Tr(gt,"elements"),this.plugins=new Tr(Object,"plugins"),this.scales=new Tr(Ti,"scales"),this._typedRegistries=[this.controllers,this.scales,this.elements]}add(...e){this._each("register",e)}remove(...e){this._each("unregister",e)}addControllers(...e){this._each("register",e,this.controllers)}addElements(...e){this._each("register",e,this.elements)}addPlugins(...e){this._each("register",e,this.plugins)}addScales(...e){this._each("register",e,this.scales)}getController(e){return this._get(e,this.controllers,"controller")}getElement(e){return this._get(e,this.elements,"element")}getPlugin(e){return this._get(e,this.plugins,"plugin")}getScale(e){return this._get(e,this.scales,"scale")}removeControllers(...e){this._each("unregister",e,this.controllers)}removeElements(...e){this._each("unregister",e,this.elements)}removePlugins(...e){this._each("unregister",e,this.plugins)}removeScales(...e){this._each("unregister",e,this.scales)}_each(e,n,i){[...n].forEach(s=>{const r=i||this._getRegistryForType(s);i||r.isForType(s)||r===this.plugins&&s.id?this._exec(e,r,s):Q(s,o=>{const a=i||this._getRegistryForType(o);this._exec(e,a,o)})})}_exec(e,n,i){const s=ic(e);te(i["before"+s],[],i),n[e](i),te(i["after"+s],[],i)}_getRegistryForType(e){for(let n=0;n<this._typedRegistries.length;n++){const i=this._typedRegistries[n];if(i.isForType(e))return i}return this.plugins}_get(e,n,i){const s=n.get(e);if(s===void 0)throw new Error('"'+e+'" is not a registered '+i+".");return s}}var _t=new Xw;class Qw{constructor(){this._init=[]}notify(e,n,i,s){n==="beforeInit"&&(this._init=this._createDescriptors(e,!0),this._notify(this._init,e,"install"));const r=s?this._descriptors(e).filter(s):this._descriptors(e),o=this._notify(r,e,n,i);return n==="afterDestroy"&&(this._notify(r,e,"stop"),this._notify(this._init,e,"uninstall")),o}_notify(e,n,i,s){s=s||{};for(const r of e){const o=r.plugin,a=o[i],l=[n,s,r.options];if(te(a,l,o)===!1&&s.cancelable)return!1}return!0}invalidate(){X(this._cache)||(this._oldCache=this._cache,this._cache=void 0)}_descriptors(e){if(this._cache)return this._cache;const n=this._cache=this._createDescriptors(e);return this._notifyStateChanges(e),n}_createDescriptors(e,n){const i=e&&e.config,s=B(i.options&&i.options.plugins,{}),r=qw(i);return s===!1&&!n?[]:Jw(e,r,s,n)}_notifyStateChanges(e){const n=this._oldCache||[],i=this._cache,s=(r,o)=>r.filter(a=>!o.some(l=>a.plugin.id===l.plugin.id));this._notify(s(n,i),e,"stop"),this._notify(s(i,n),e,"start")}}function qw(t){const e={},n=[],i=Object.keys(_t.plugins.items);for(let r=0;r<i.length;r++)n.push(_t.getPlugin(i[r]));const s=t.plugins||[];for(let r=0;r<s.length;r++){const o=s[r];n.indexOf(o)===-1&&(n.push(o),e[o.id]=!0)}return{plugins:n,localIds:e}}function Gw(t,e){return!e&&t===!1?null:t===!0?{}:t}function Jw(t,{plugins:e,localIds:n},i,s){const r=[],o=t.getContext();for(const a of e){const l=a.id,u=Gw(i[l],s);u!==null&&r.push({plugin:a,options:Zw(t.config,{plugin:a,local:n[l]},u,o)})}return r}function Zw(t,{plugin:e,local:n},i,s){const r=t.pluginScopeKeys(e),o=t.getOptionScopes(i,r);return n&&e.defaults&&o.push(e.defaults),t.createResolver(o,s,[""],{scriptable:!1,indexable:!1,allKeys:!0})}function tu(t,e){const n=de.datasets[t]||{};return((e.datasets||{})[t]||{}).indexAxis||e.indexAxis||n.indexAxis||"x"}function eS(t,e){let n=t;return t==="_index_"?n=e:t==="_value_"&&(n=e==="x"?"y":"x"),n}function tS(t,e){return t===e?"_index_":"_value_"}function Cf(t){if(t==="x"||t==="y"||t==="r")return t}function nS(t){if(t==="top"||t==="bottom")return"x";if(t==="left"||t==="right")return"y"}function nu(t,...e){if(Cf(t))return t;for(const n of e){const i=n.axis||nS(n.position)||t.length>1&&Cf(t[0].toLowerCase());if(i)return i}throw new Error(`Cannot determine type of '${t}' axis. Please provide 'axis' or 'position' option.`)}function Pf(t,e,n){if(n[e+"AxisID"]===t)return{axis:e}}function iS(t,e){if(e.data&&e.data.datasets){const n=e.data.datasets.filter(i=>i.xAxisID===t||i.yAxisID===t);if(n.length)return Pf(t,"x",n[0])||Pf(t,"y",n[0])}return{}}function sS(t,e){const n=zn[t.type]||{scales:{}},i=e.scales||{},s=tu(t.type,e),r=Object.create(null);return Object.keys(i).forEach(o=>{const a=i[o];if(!Y(a))return console.error(`Invalid scale configuration for scale: ${o}`);if(a._proxy)return console.warn(`Ignoring resolver passed as options for scale: ${o}`);const l=nu(o,a,iS(o,t),de.scales[a.type]),u=tS(l,s),c=n.scales||{};r[o]=fs(Object.create(null),[{axis:l},a,c[l],c[u]])}),t.data.datasets.forEach(o=>{const a=o.type||t.type,l=o.indexAxis||tu(a,e),c=(zn[a]||{}).scales||{};Object.keys(c).forEach(d=>{const f=eS(d,l),h=o[f+"AxisID"]||f;r[h]=r[h]||Object.create(null),fs(r[h],[{axis:f},i[h],c[d]])})}),Object.keys(r).forEach(o=>{const a=r[o];fs(a,[de.scales[a.type],de.scale])}),r}function vm(t){const e=t.options||(t.options={});e.plugins=B(e.plugins,{}),e.scales=sS(t,e)}function _m(t){return t=t||{},t.datasets=t.datasets||[],t.labels=t.labels||[],t}function rS(t){return t=t||{},t.data=_m(t.data),vm(t),t}const Mf=new Map,wm=new Set;function Rr(t,e){let n=Mf.get(t);return n||(n=e(),Mf.set(t,n),wm.add(n)),n}const Ui=(t,e,n)=>{const i=Fn(e,n);i!==void 0&&t.add(i)};class oS{constructor(e){this._config=rS(e),this._scopeCache=new Map,this._resolverCache=new Map}get platform(){return this._config.platform}get type(){return this._config.type}set type(e){this._config.type=e}get data(){return this._config.data}set data(e){this._config.data=_m(e)}get options(){return this._config.options}set options(e){this._config.options=e}get plugins(){return this._config.plugins}update(){const e=this._config;this.clearCache(),vm(e)}clearCache(){this._scopeCache.clear(),this._resolverCache.clear()}datasetScopeKeys(e){return Rr(e,()=>[[`datasets.${e}`,""]])}datasetAnimationScopeKeys(e,n){return Rr(`${e}.transition.${n}`,()=>[[`datasets.${e}.transitions.${n}`,`transitions.${n}`],[`datasets.${e}`,""]])}datasetElementScopeKeys(e,n){return Rr(`${e}-${n}`,()=>[[`datasets.${e}.elements.${n}`,`datasets.${e}`,`elements.${n}`,""]])}pluginScopeKeys(e){const n=e.id,i=this.type;return Rr(`${i}-plugin-${n}`,()=>[[`plugins.${n}`,...e.additionalOptionScopes||[]]])}_cachedScopes(e,n){const i=this._scopeCache;let s=i.get(e);return(!s||n)&&(s=new Map,i.set(e,s)),s}getOptionScopes(e,n,i){const{options:s,type:r}=this,o=this._cachedScopes(e,i),a=o.get(n);if(a)return a;const l=new Set;n.forEach(c=>{e&&(l.add(e),c.forEach(d=>Ui(l,e,d))),c.forEach(d=>Ui(l,s,d)),c.forEach(d=>Ui(l,zn[r]||{},d)),c.forEach(d=>Ui(l,de,d)),c.forEach(d=>Ui(l,Jl,d))});const u=Array.from(l);return u.length===0&&u.push(Object.create(null)),wm.has(n)&&o.set(n,u),u}chartOptionScopes(){const{options:e,type:n}=this;return[e,zn[n]||{},de.datasets[n]||{},{type:n},de,Jl]}resolveNamedOptions(e,n,i,s=[""]){const r={$shared:!0},{resolver:o,subPrefixes:a}=Ef(this._resolverCache,e,s);let l=o;if(lS(o,n)){r.$shared=!1,i=fn(i)?i():i;const u=this.createResolver(e,i,a);l=Pi(o,i,u)}for(const u of n)r[u]=l[u];return r}createResolver(e,n,i=[""],s){const{resolver:r}=Ef(this._resolverCache,e,i);return Y(n)?Pi(r,n,void 0,s):r}}function Ef(t,e,n){let i=t.get(e);i||(i=new Map,t.set(e,i));const s=n.join();let r=i.get(s);return r||(r={resolver:dc(e,n),subPrefixes:n.filter(a=>!a.toLowerCase().includes("hover"))},i.set(s,r)),r}const aS=t=>Y(t)&&Object.getOwnPropertyNames(t).some(e=>fn(t[e]));function lS(t,e){const{isScriptable:n,isIndexable:i}=im(t);for(const s of e){const r=n(s),o=i(s),a=(o||r)&&t[s];if(r&&(fn(a)||aS(a))||o&&pe(a))return!0}return!1}var uS="4.5.0";const cS=["top","bottom","left","right","chartArea"];function Of(t,e){return t==="top"||t==="bottom"||cS.indexOf(t)===-1&&e==="x"}function Nf(t,e){return function(n,i){return n[t]===i[t]?n[e]-i[e]:n[t]-i[t]}}function Lf(t){const e=t.chart,n=e.options.animation;e.notifyPlugins("afterRender"),te(n&&n.onComplete,[t],e)}function dS(t){const e=t.chart,n=e.options.animation;te(n&&n.onProgress,[t],e)}function Sm(t){return pc()&&typeof t=="string"?t=document.getElementById(t):t&&t.length&&(t=t[0]),t&&t.canvas&&(t=t.canvas),t}const Jr={},Tf=t=>{const e=Sm(t);return Object.values(Jr).filter(n=>n.canvas===e).pop()};function fS(t,e,n){const i=Object.keys(t);for(const s of i){const r=+s;if(r>=e){const o=t[s];delete t[s],(n>0||r>e)&&(t[r+n]=o)}}}function hS(t,e,n,i){return!n||t.type==="mouseout"?null:i?e:t}var Vt;let Wn=(Vt=class{static register(...e){_t.add(...e),Rf()}static unregister(...e){_t.remove(...e),Rf()}constructor(e,n){const i=this.config=new oS(n),s=Sm(e),r=Tf(s);if(r)throw new Error("Canvas is already in use. Chart with ID '"+r.id+"' must be destroyed before the canvas with ID '"+r.canvas.id+"' can be reused.");const o=i.createResolver(i.chartOptionScopes(),this.getContext());this.platform=new(i.platform||Lw(s)),this.platform.updateConfig(i);const a=this.platform.acquireContext(s,o.aspectRatio),l=a&&a.canvas,u=l&&l.height,c=l&&l.width;if(this.id=l1(),this.ctx=a,this.canvas=l,this.width=c,this.height=u,this._options=o,this._aspectRatio=this.aspectRatio,this._layers=[],this._metasets=[],this._stacks=void 0,this.boxes=[],this.currentDevicePixelRatio=void 0,this.chartArea=void 0,this._active=[],this._lastEvent=void 0,this._listeners={},this._responsiveListeners=void 0,this._sortedMetasets=[],this.scales={},this._plugins=new Qw,this.$proxies={},this._hiddenIndices={},this.attached=!1,this._animationsDisabled=void 0,this.$context=void 0,this._doResize=M1(d=>this.update(d),o.resizeDelay||0),this._dataChanges=[],Jr[this.id]=this,!a||!l){console.error("Failed to create chart: can't acquire context from the given item");return}Mt.listen(this,"complete",Lf),Mt.listen(this,"progress",dS),this._initialize(),this.attached&&this.update()}get aspectRatio(){const{options:{aspectRatio:e,maintainAspectRatio:n},width:i,height:s,_aspectRatio:r}=this;return X(e)?n&&r?r:s?i/s:null:e}get data(){return this.config.data}set data(e){this.config.data=e}get options(){return this._options}set options(e){this.config.options=e}get registry(){return _t}_initialize(){return this.notifyPlugins("beforeInit"),this.options.responsive?this.resize():ef(this,this.options.devicePixelRatio),this.bindEvents(),this.notifyPlugins("afterInit"),this}clear(){return Gd(this.canvas,this.ctx),this}stop(){return Mt.stop(this),this}resize(e,n){Mt.running(this)?this._resizeBeforeDraw={width:e,height:n}:this._resize(e,n)}_resize(e,n){const i=this.options,s=this.canvas,r=i.maintainAspectRatio&&this.aspectRatio,o=this.platform.getMaximumSize(s,e,n,r),a=i.devicePixelRatio||this.platform.getDevicePixelRatio(),l=this.width?"resize":"attach";this.width=o.width,this.height=o.height,this._aspectRatio=this.aspectRatio,ef(this,a,!0)&&(this.notifyPlugins("resize",{size:o}),te(i.onResize,[this,o],this),this.attached&&this._doResize(l)&&this.render())}ensureScalesHaveIDs(){const n=this.options.scales||{};Q(n,(i,s)=>{i.id=s})}buildOrUpdateScales(){const e=this.options,n=e.scales,i=this.scales,s=Object.keys(i).reduce((o,a)=>(o[a]=!1,o),{});let r=[];n&&(r=r.concat(Object.keys(n).map(o=>{const a=n[o],l=nu(o,a),u=l==="r",c=l==="x";return{options:a,dposition:u?"chartArea":c?"bottom":"left",dtype:u?"radialLinear":c?"category":"linear"}}))),Q(r,o=>{const a=o.options,l=a.id,u=nu(l,a),c=B(a.type,o.dtype);(a.position===void 0||Of(a.position,u)!==Of(o.dposition))&&(a.position=o.dposition),s[l]=!0;let d=null;if(l in i&&i[l].type===c)d=i[l];else{const f=_t.getScale(c);d=new f({id:l,type:c,ctx:this.ctx,chart:this}),i[d.id]=d}d.init(a,e)}),Q(s,(o,a)=>{o||delete i[a]}),Q(i,o=>{tt.configure(this,o,o.options),tt.addBox(this,o)})}_updateMetasets(){const e=this._metasets,n=this.data.datasets.length,i=e.length;if(e.sort((s,r)=>s.index-r.index),i>n){for(let s=n;s<i;++s)this._destroyDatasetMeta(s);e.splice(n,i-n)}this._sortedMetasets=e.slice(0).sort(Nf("order","index"))}_removeUnreferencedMetasets(){const{_metasets:e,data:{datasets:n}}=this;e.length>n.length&&delete this._stacks,e.forEach((i,s)=>{n.filter(r=>r===i._dataset).length===0&&this._destroyDatasetMeta(s)})}buildOrUpdateControllers(){const e=[],n=this.data.datasets;let i,s;for(this._removeUnreferencedMetasets(),i=0,s=n.length;i<s;i++){const r=n[i];let o=this.getDatasetMeta(i);const a=r.type||this.config.type;if(o.type&&o.type!==a&&(this._destroyDatasetMeta(i),o=this.getDatasetMeta(i)),o.type=a,o.indexAxis=r.indexAxis||tu(a,this.options),o.order=r.order||0,o.index=i,o.label=""+r.label,o.visible=this.isDatasetVisible(i),o.controller)o.controller.updateIndex(i),o.controller.linkScales();else{const l=_t.getController(a),{datasetElementType:u,dataElementType:c}=de.datasets[a];Object.assign(l,{dataElementType:_t.getElement(c),datasetElementType:u&&_t.getElement(u)}),o.controller=new l(this,i),e.push(o.controller)}}return this._updateMetasets(),e}_resetElements(){Q(this.data.datasets,(e,n)=>{this.getDatasetMeta(n).controller.reset()},this)}reset(){this._resetElements(),this.notifyPlugins("reset")}update(e){const n=this.config;n.update();const i=this._options=n.createResolver(n.chartOptionScopes(),this.getContext()),s=this._animationsDisabled=!i.animation;if(this._updateScales(),this._checkEventBindings(),this._updateHiddenIndices(),this._plugins.invalidate(),this.notifyPlugins("beforeUpdate",{mode:e,cancelable:!0})===!1)return;const r=this.buildOrUpdateControllers();this.notifyPlugins("beforeElementsUpdate");let o=0;for(let u=0,c=this.data.datasets.length;u<c;u++){const{controller:d}=this.getDatasetMeta(u),f=!s&&r.indexOf(d)===-1;d.buildOrUpdateElements(f),o=Math.max(+d.getMaxOverflow(),o)}o=this._minPadding=i.layout.autoPadding?o:0,this._updateLayout(o),s||Q(r,u=>{u.reset()}),this._updateDatasets(e),this.notifyPlugins("afterUpdate",{mode:e}),this._layers.sort(Nf("z","_idx"));const{_active:a,_lastEvent:l}=this;l?this._eventHandler(l,!0):a.length&&this._updateHoverStyles(a,a,!0),this.render()}_updateScales(){Q(this.scales,e=>{tt.removeBox(this,e)}),this.ensureScalesHaveIDs(),this.buildOrUpdateScales()}_checkEventBindings(){const e=this.options,n=new Set(Object.keys(this._listeners)),i=new Set(e.events);(!$d(n,i)||!!this._responsiveListeners!==e.responsive)&&(this.unbindEvents(),this.bindEvents())}_updateHiddenIndices(){const{_hiddenIndices:e}=this,n=this._getUniformDataChanges()||[];for(const{method:i,start:s,count:r}of n){const o=i==="_removeElements"?-r:r;fS(e,s,o)}}_getUniformDataChanges(){const e=this._dataChanges;if(!e||!e.length)return;this._dataChanges=[];const n=this.data.datasets.length,i=r=>new Set(e.filter(o=>o[0]===r).map((o,a)=>a+","+o.splice(1).join(","))),s=i(0);for(let r=1;r<n;r++)if(!$d(s,i(r)))return;return Array.from(s).map(r=>r.split(",")).map(r=>({method:r[1],start:+r[2],count:+r[3]}))}_updateLayout(e){if(this.notifyPlugins("beforeLayout",{cancelable:!0})===!1)return;tt.update(this,this.width,this.height,e);const n=this.chartArea,i=n.width<=0||n.height<=0;this._layers=[],Q(this.boxes,s=>{i&&s.position==="chartArea"||(s.configure&&s.configure(),this._layers.push(...s._layers()))},this),this._layers.forEach((s,r)=>{s._idx=r}),this.notifyPlugins("afterLayout")}_updateDatasets(e){if(this.notifyPlugins("beforeDatasetsUpdate",{mode:e,cancelable:!0})!==!1){for(let n=0,i=this.data.datasets.length;n<i;++n)this.getDatasetMeta(n).controller.configure();for(let n=0,i=this.data.datasets.length;n<i;++n)this._updateDataset(n,fn(e)?e({datasetIndex:n}):e);this.notifyPlugins("afterDatasetsUpdate",{mode:e})}}_updateDataset(e,n){const i=this.getDatasetMeta(e),s={meta:i,index:e,mode:n,cancelable:!0};this.notifyPlugins("beforeDatasetUpdate",s)!==!1&&(i.controller._update(n),s.cancelable=!1,this.notifyPlugins("afterDatasetUpdate",s))}render(){this.notifyPlugins("beforeRender",{cancelable:!0})!==!1&&(Mt.has(this)?this.attached&&!Mt.running(this)&&Mt.start(this):(this.draw(),Lf({chart:this})))}draw(){let e;if(this._resizeBeforeDraw){const{width:i,height:s}=this._resizeBeforeDraw;this._resizeBeforeDraw=null,this._resize(i,s)}if(this.clear(),this.width<=0||this.height<=0||this.notifyPlugins("beforeDraw",{cancelable:!0})===!1)return;const n=this._layers;for(e=0;e<n.length&&n[e].z<=0;++e)n[e].draw(this.chartArea);for(this._drawDatasets();e<n.length;++e)n[e].draw(this.chartArea);this.notifyPlugins("afterDraw")}_getSortedDatasetMetas(e){const n=this._sortedMetasets,i=[];let s,r;for(s=0,r=n.length;s<r;++s){const o=n[s];(!e||o.visible)&&i.push(o)}return i}getSortedVisibleDatasetMetas(){return this._getSortedDatasetMetas(!0)}_drawDatasets(){if(this.notifyPlugins("beforeDatasetsDraw",{cancelable:!0})===!1)return;const e=this.getSortedVisibleDatasetMetas();for(let n=e.length-1;n>=0;--n)this._drawDataset(e[n]);this.notifyPlugins("afterDatasetsDraw")}_drawDataset(e){const n=this.ctx,i={meta:e,index:e.index,cancelable:!0},s=R_(this,e);this.notifyPlugins("beforeDatasetDraw",i)!==!1&&(s&&lc(n,s),e.controller.draw(),s&&uc(n),i.cancelable=!1,this.notifyPlugins("afterDatasetDraw",i))}isPointInArea(e){return Vs(e,this.chartArea,this._minPadding)}getElementsAtEventForMode(e,n,i,s){const r=cw.modes[n];return typeof r=="function"?r(this,e,i,s):[]}getDatasetMeta(e){const n=this.data.datasets[e],i=this._metasets;let s=i.filter(r=>r&&r._dataset===n).pop();return s||(s={type:null,data:[],dataset:null,controller:null,hidden:null,xAxisID:null,yAxisID:null,order:n&&n.order||0,index:e,_dataset:n,_parsed:[],_sorted:!1},i.push(s)),s}getContext(){return this.$context||(this.$context=Hn(null,{chart:this,type:"chart"}))}getVisibleDatasetCount(){return this.getSortedVisibleDatasetMetas().length}isDatasetVisible(e){const n=this.data.datasets[e];if(!n)return!1;const i=this.getDatasetMeta(e);return typeof i.hidden=="boolean"?!i.hidden:!n.hidden}setDatasetVisibility(e,n){const i=this.getDatasetMeta(e);i.hidden=!n}toggleDataVisibility(e){this._hiddenIndices[e]=!this._hiddenIndices[e]}getDataVisibility(e){return!this._hiddenIndices[e]}_updateVisibility(e,n,i){const s=i?"show":"hide",r=this.getDatasetMeta(e),o=r.controller._resolveAnimations(void 0,s);zs(n)?(r.data[n].hidden=!i,this.update()):(this.setDatasetVisibility(e,i),o.update(r,{visible:i}),this.update(a=>a.datasetIndex===e?s:void 0))}hide(e,n){this._updateVisibility(e,n,!1)}show(e,n){this._updateVisibility(e,n,!0)}_destroyDatasetMeta(e){const n=this._metasets[e];n&&n.controller&&n.controller._destroy(),delete this._metasets[e]}_stop(){let e,n;for(this.stop(),Mt.remove(this),e=0,n=this.data.datasets.length;e<n;++e)this._destroyDatasetMeta(e)}destroy(){this.notifyPlugins("beforeDestroy");const{canvas:e,ctx:n}=this;this._stop(),this.config.clearCache(),e&&(this.unbindEvents(),Gd(e,n),this.platform.releaseContext(n),this.canvas=null,this.ctx=null),delete Jr[this.id],this.notifyPlugins("afterDestroy")}toBase64Image(...e){return this.canvas.toDataURL(...e)}bindEvents(){this.bindUserEvents(),this.options.responsive?this.bindResponsiveEvents():this.attached=!0}bindUserEvents(){const e=this._listeners,n=this.platform,i=(r,o)=>{n.addEventListener(this,r,o),e[r]=o},s=(r,o,a)=>{r.offsetX=o,r.offsetY=a,this._eventHandler(r)};Q(this.options.events,r=>i(r,s))}bindResponsiveEvents(){this._responsiveListeners||(this._responsiveListeners={});const e=this._responsiveListeners,n=this.platform,i=(l,u)=>{n.addEventListener(this,l,u),e[l]=u},s=(l,u)=>{e[l]&&(n.removeEventListener(this,l,u),delete e[l])},r=(l,u)=>{this.canvas&&this.resize(l,u)};let o;const a=()=>{s("attach",a),this.attached=!0,this.resize(),i("resize",r),i("detach",o)};o=()=>{this.attached=!1,s("resize",r),this._stop(),this._resize(0,0),i("attach",a)},n.isAttached(this.canvas)?a():o()}unbindEvents(){Q(this._listeners,(e,n)=>{this.platform.removeEventListener(this,n,e)}),this._listeners={},Q(this._responsiveListeners,(e,n)=>{this.platform.removeEventListener(this,n,e)}),this._responsiveListeners=void 0}updateHoverStyle(e,n,i){const s=i?"set":"remove";let r,o,a,l;for(n==="dataset"&&(r=this.getDatasetMeta(e[0].datasetIndex),r.controller["_"+s+"DatasetHoverStyle"]()),a=0,l=e.length;a<l;++a){o=e[a];const u=o&&this.getDatasetMeta(o.datasetIndex).controller;u&&u[s+"HoverStyle"](o.element,o.datasetIndex,o.index)}}getActiveElements(){return this._active||[]}setActiveElements(e){const n=this._active||[],i=e.map(({datasetIndex:r,index:o})=>{const a=this.getDatasetMeta(r);if(!a)throw new Error("No dataset found at index "+r);return{datasetIndex:r,element:a.data[o],index:o}});!Mo(i,n)&&(this._active=i,this._lastEvent=null,this._updateHoverStyles(i,n))}notifyPlugins(e,n,i){return this._plugins.notify(this,e,n,i)}isPluginEnabled(e){return this._plugins._cache.filter(n=>n.plugin.id===e).length===1}_updateHoverStyles(e,n,i){const s=this.options.hover,r=(l,u)=>l.filter(c=>!u.some(d=>c.datasetIndex===d.datasetIndex&&c.index===d.index)),o=r(n,e),a=i?e:r(e,n);o.length&&this.updateHoverStyle(o,s.mode,!1),a.length&&s.mode&&this.updateHoverStyle(a,s.mode,!0)}_eventHandler(e,n){const i={event:e,replay:n,cancelable:!0,inChartArea:this.isPointInArea(e)},s=o=>(o.options.events||this.options.events).includes(e.native.type);if(this.notifyPlugins("beforeEvent",i,s)===!1)return;const r=this._handleEvent(e,n,i.inChartArea);return i.cancelable=!1,this.notifyPlugins("afterEvent",i,s),(r||i.changed)&&this.render(),this}_handleEvent(e,n,i){const{_active:s=[],options:r}=this,o=n,a=this._getActiveElements(e,s,i,o),l=p1(e),u=hS(e,this._lastEvent,i,l);i&&(this._lastEvent=null,te(r.onHover,[e,a,this],this),l&&te(r.onClick,[e,a,this],this));const c=!Mo(a,s);return(c||n)&&(this._active=a,this._updateHoverStyles(a,s,n)),this._lastEvent=u,c}_getActiveElements(e,n,i,s){if(e.type==="mouseout")return[];if(!i)return n;const r=this.options.hover;return this.getElementsAtEventForMode(e,r.mode,r,s)}},D(Vt,"defaults",de),D(Vt,"instances",Jr),D(Vt,"overrides",zn),D(Vt,"registry",_t),D(Vt,"version",uS),D(Vt,"getChart",Tf),Vt);function Rf(){return Q(Wn.instances,t=>t._plugins.invalidate())}function pS(t,e,n){const{startAngle:i,x:s,y:r,outerRadius:o,innerRadius:a,options:l}=e,{borderWidth:u,borderJoinStyle:c}=l,d=Math.min(u/o,ct(i-n));if(t.beginPath(),t.arc(s,r,o-u/2,i+d/2,n-d/2),a>0){const f=Math.min(u/a,ct(i-n));t.arc(s,r,a+u/2,n-f/2,i+f/2,!0)}else{const f=Math.min(u/2,o*ct(i-n));if(c==="round")t.arc(s,r,f,n-q/2,i+q/2,!0);else if(c==="bevel"){const h=2*f*f,p=-h*Math.cos(n+q/2)+s,y=-h*Math.sin(n+q/2)+r,v=h*Math.cos(i+q/2)+s,m=h*Math.sin(i+q/2)+r;t.lineTo(p,y),t.lineTo(v,m)}}t.closePath(),t.moveTo(0,0),t.rect(0,0,t.canvas.width,t.canvas.height),t.clip("evenodd")}function gS(t,e,n){const{startAngle:i,pixelMargin:s,x:r,y:o,outerRadius:a,innerRadius:l}=e;let u=s/a;t.beginPath(),t.arc(r,o,a,i-u,n+u),l>s?(u=s/l,t.arc(r,o,l,n+u,i-u,!0)):t.arc(r,o,s,n+me,i-me),t.closePath(),t.clip()}function mS(t){return cc(t,["outerStart","outerEnd","innerStart","innerEnd"])}function yS(t,e,n,i){const s=mS(t.options.borderRadius),r=(n-e)/2,o=Math.min(r,i*e/2),a=l=>{const u=(n-Math.min(r,l))*i/2;return Ee(l,0,Math.min(r,u))};return{outerStart:a(s.outerStart),outerEnd:a(s.outerEnd),innerStart:Ee(s.innerStart,0,o),innerEnd:Ee(s.innerEnd,0,o)}}function Yn(t,e,n,i){return{x:n+t*Math.cos(e),y:i+t*Math.sin(e)}}function Ro(t,e,n,i,s,r){const{x:o,y:a,startAngle:l,pixelMargin:u,innerRadius:c}=e,d=Math.max(e.outerRadius+i+n-u,0),f=c>0?c+i+n+u:0;let h=0;const p=s-l;if(i){const W=c>0?c-i:0,z=d>0?d-i:0,V=(W+z)/2,N=V!==0?p*V/(V+i):p;h=(p-N)/2}const y=Math.max(.001,p*d-n/q)/d,v=(p-y)/2,m=l+v+h,g=s-v-h,{outerStart:x,outerEnd:w,innerStart:S,innerEnd:C}=yS(e,f,d,g-m),k=d-x,b=d-w,M=m+x/k,P=g-w/b,T=f+S,I=f+C,H=m+S/T,J=g-C/I;if(t.beginPath(),r){const W=(M+P)/2;if(t.arc(o,a,d,M,W),t.arc(o,a,d,W,P),w>0){const R=Yn(b,P,o,a);t.arc(R.x,R.y,w,P,g+me)}const z=Yn(I,g,o,a);if(t.lineTo(z.x,z.y),C>0){const R=Yn(I,J,o,a);t.arc(R.x,R.y,C,g+me,J+Math.PI)}const V=(g-C/f+(m+S/f))/2;if(t.arc(o,a,f,g-C/f,V,!0),t.arc(o,a,f,V,m+S/f,!0),S>0){const R=Yn(T,H,o,a);t.arc(R.x,R.y,S,H+Math.PI,m-me)}const N=Yn(k,m,o,a);if(t.lineTo(N.x,N.y),x>0){const R=Yn(k,M,o,a);t.arc(R.x,R.y,x,m-me,M)}}else{t.moveTo(o,a);const W=Math.cos(M)*d+o,z=Math.sin(M)*d+a;t.lineTo(W,z);const V=Math.cos(P)*d+o,N=Math.sin(P)*d+a;t.lineTo(V,N)}t.closePath()}function xS(t,e,n,i,s){const{fullCircles:r,startAngle:o,circumference:a}=e;let l=e.endAngle;if(r){Ro(t,e,n,i,l,s);for(let u=0;u<r;++u)t.fill();isNaN(a)||(l=o+(a%le||le))}return Ro(t,e,n,i,l,s),t.fill(),l}function vS(t,e,n,i,s){const{fullCircles:r,startAngle:o,circumference:a,options:l}=e,{borderWidth:u,borderJoinStyle:c,borderDash:d,borderDashOffset:f,borderRadius:h}=l,p=l.borderAlign==="inner";if(!u)return;t.setLineDash(d||[]),t.lineDashOffset=f,p?(t.lineWidth=u*2,t.lineJoin=c||"round"):(t.lineWidth=u,t.lineJoin=c||"bevel");let y=e.endAngle;if(r){Ro(t,e,n,i,y,s);for(let v=0;v<r;++v)t.stroke();isNaN(a)||(y=o+(a%le||le))}p&&gS(t,e,y),l.selfJoin&&y-o>=q&&h===0&&c!=="miter"&&pS(t,e,y),r||(Ro(t,e,n,i,y,s),t.stroke())}class es extends gt{constructor(n){super();D(this,"circumference");D(this,"endAngle");D(this,"fullCircles");D(this,"innerRadius");D(this,"outerRadius");D(this,"pixelMargin");D(this,"startAngle");this.options=void 0,this.circumference=void 0,this.startAngle=void 0,this.endAngle=void 0,this.innerRadius=void 0,this.outerRadius=void 0,this.pixelMargin=0,this.fullCircles=0,n&&Object.assign(this,n)}inRange(n,i,s){const r=this.getProps(["x","y"],s),{angle:o,distance:a}=Qg(r,{x:n,y:i}),{startAngle:l,endAngle:u,innerRadius:c,outerRadius:d,circumference:f}=this.getProps(["startAngle","endAngle","innerRadius","outerRadius","circumference"],s),h=(this.options.spacing+this.options.borderWidth)/2,p=B(f,u-l),y=$s(o,l,u)&&l!==u,v=p>=le||y,m=Gt(a,c+h,d+h);return v&&m}getCenterPoint(n){const{x:i,y:s,startAngle:r,endAngle:o,innerRadius:a,outerRadius:l}=this.getProps(["x","y","startAngle","endAngle","innerRadius","outerRadius"],n),{offset:u,spacing:c}=this.options,d=(r+o)/2,f=(a+l+c+u)/2;return{x:i+Math.cos(d)*f,y:s+Math.sin(d)*f}}tooltipPosition(n){return this.getCenterPoint(n)}draw(n){const{options:i,circumference:s}=this,r=(i.offset||0)/4,o=(i.spacing||0)/2,a=i.circular;if(this.pixelMargin=i.borderAlign==="inner"?.33:0,this.fullCircles=s>le?Math.floor(s/le):0,s===0||this.innerRadius<0||this.outerRadius<0)return;n.save();const l=(this.startAngle+this.endAngle)/2;n.translate(Math.cos(l)*r,Math.sin(l)*r);const u=1-Math.sin(Math.min(q,s||0)),c=r*u;n.fillStyle=i.backgroundColor,n.strokeStyle=i.borderColor,xS(n,this,c,o,a),vS(n,this,c,o,a),n.restore()}}D(es,"id","arc"),D(es,"defaults",{borderAlign:"center",borderColor:"#fff",borderDash:[],borderDashOffset:0,borderJoinStyle:void 0,borderRadius:0,borderWidth:2,offset:0,spacing:0,angle:void 0,circular:!0,selfJoin:!1}),D(es,"defaultRoutes",{backgroundColor:"backgroundColor"}),D(es,"descriptors",{_scriptable:!0,_indexable:n=>n!=="borderDash"});function bm(t,e,n=e){t.lineCap=B(n.borderCapStyle,e.borderCapStyle),t.setLineDash(B(n.borderDash,e.borderDash)),t.lineDashOffset=B(n.borderDashOffset,e.borderDashOffset),t.lineJoin=B(n.borderJoinStyle,e.borderJoinStyle),t.lineWidth=B(n.borderWidth,e.borderWidth),t.strokeStyle=B(n.borderColor,e.borderColor)}function _S(t,e,n){t.lineTo(n.x,n.y)}function wS(t){return t.stepped?$1:t.tension||t.cubicInterpolationMode==="monotone"?V1:_S}function km(t,e,n={}){const i=t.length,{start:s=0,end:r=i-1}=n,{start:o,end:a}=e,l=Math.max(s,o),u=Math.min(r,a),c=s<o&&r<o||s>a&&r>a;return{count:i,start:l,loop:e.loop,ilen:u<l&&!c?i+u-l:u-l}}function SS(t,e,n,i){const{points:s,options:r}=e,{count:o,start:a,loop:l,ilen:u}=km(s,n,i),c=wS(r);let{move:d=!0,reverse:f}=i||{},h,p,y;for(h=0;h<=u;++h)p=s[(a+(f?u-h:h))%o],!p.skip&&(d?(t.moveTo(p.x,p.y),d=!1):c(t,y,p,f,r.stepped),y=p);return l&&(p=s[(a+(f?u:0))%o],c(t,y,p,f,r.stepped)),!!l}function bS(t,e,n,i){const s=e.points,{count:r,start:o,ilen:a}=km(s,n,i),{move:l=!0,reverse:u}=i||{};let c=0,d=0,f,h,p,y,v,m;const g=w=>(o+(u?a-w:w))%r,x=()=>{y!==v&&(t.lineTo(c,v),t.lineTo(c,y),t.lineTo(c,m))};for(l&&(h=s[g(0)],t.moveTo(h.x,h.y)),f=0;f<=a;++f){if(h=s[g(f)],h.skip)continue;const w=h.x,S=h.y,C=w|0;C===p?(S<y?y=S:S>v&&(v=S),c=(d*c+w)/++d):(x(),t.lineTo(w,S),p=C,d=0,y=v=S),m=S}x()}function iu(t){const e=t.options,n=e.borderDash&&e.borderDash.length;return!t._decimated&&!t._loop&&!e.tension&&e.cubicInterpolationMode!=="monotone"&&!e.stepped&&!n?bS:SS}function kS(t){return t.stepped?__:t.tension||t.cubicInterpolationMode==="monotone"?w_:bn}function CS(t,e,n,i){let s=e._path;s||(s=e._path=new Path2D,e.path(s,n,i)&&s.closePath()),bm(t,e.options),t.stroke(s)}function PS(t,e,n,i){const{segments:s,options:r}=e,o=iu(e);for(const a of s)bm(t,r,a.style),t.beginPath(),o(t,e,a,{start:n,end:n+i-1})&&t.closePath(),t.stroke()}const MS=typeof Path2D=="function";function ES(t,e,n,i){MS&&!e.options.segment?CS(t,e,n,i):PS(t,e,n,i)}class ts extends gt{constructor(e){super(),this.animated=!0,this.options=void 0,this._chart=void 0,this._loop=void 0,this._fullLoop=void 0,this._path=void 0,this._points=void 0,this._segments=void 0,this._decimated=!1,this._pointsUpdated=!1,this._datasetIndex=void 0,e&&Object.assign(this,e)}updateControlPoints(e,n){const i=this.options;if((i.tension||i.cubicInterpolationMode==="monotone")&&!i.stepped&&!this._pointsUpdated){const s=i.spanGaps?this._loop:this._fullLoop;f_(this._points,i,e,s,n),this._pointsUpdated=!0}}set points(e){this._points=e,delete this._segments,delete this._path,this._pointsUpdated=!1}get points(){return this._points}get segments(){return this._segments||(this._segments=O_(this,this.options.segment))}first(){const e=this.segments,n=this.points;return e.length&&n[e[0].start]}last(){const e=this.segments,n=this.points,i=e.length;return i&&n[e[i-1].end]}interpolate(e,n){const i=this.options,s=e[n],r=this.points,o=P_(this,{property:n,start:s,end:s});if(!o.length)return;const a=[],l=kS(i);let u,c;for(u=0,c=o.length;u<c;++u){const{start:d,end:f}=o[u],h=r[d],p=r[f];if(h===p){a.push(h);continue}const y=Math.abs((s-h[n])/(p[n]-h[n])),v=l(h,p,y,i.stepped);v[n]=e[n],a.push(v)}return a.length===1?a[0]:a}pathSegment(e,n,i){return iu(this)(e,this,n,i)}path(e,n,i){const s=this.segments,r=iu(this);let o=this._loop;n=n||0,i=i||this.points.length-n;for(const a of s)o&=r(e,this,a,{start:n,end:n+i-1});return!!o}draw(e,n,i,s){const r=this.options||{};(this.points||[]).length&&r.borderWidth&&(e.save(),ES(e,this,i,s),e.restore()),this.animated&&(this._pointsUpdated=!1,this._path=void 0)}}D(ts,"id","line"),D(ts,"defaults",{borderCapStyle:"butt",borderDash:[],borderDashOffset:0,borderJoinStyle:"miter",borderWidth:3,capBezierPoints:!0,cubicInterpolationMode:"default",fill:!1,spanGaps:!1,stepped:!1,tension:0}),D(ts,"defaultRoutes",{backgroundColor:"backgroundColor",borderColor:"borderColor"}),D(ts,"descriptors",{_scriptable:!0,_indexable:e=>e!=="borderDash"&&e!=="fill"});function Df(t,e,n,i){const s=t.options,{[n]:r}=t.getProps([n],i);return Math.abs(e-r)<s.radius+s.hitRadius}class Zr extends gt{constructor(n){super();D(this,"parsed");D(this,"skip");D(this,"stop");this.options=void 0,this.parsed=void 0,this.skip=void 0,this.stop=void 0,n&&Object.assign(this,n)}inRange(n,i,s){const r=this.options,{x:o,y:a}=this.getProps(["x","y"],s);return Math.pow(n-o,2)+Math.pow(i-a,2)<Math.pow(r.hitRadius+r.radius,2)}inXRange(n,i){return Df(this,n,"x",i)}inYRange(n,i){return Df(this,n,"y",i)}getCenterPoint(n){const{x:i,y:s}=this.getProps(["x","y"],n);return{x:i,y:s}}size(n){n=n||this.options||{};let i=n.radius||0;i=Math.max(i,i&&n.hoverRadius||0);const s=i&&n.borderWidth||0;return(i+s)*2}draw(n,i){const s=this.options;this.skip||s.radius<.1||!Vs(this,i,this.size(s)/2)||(n.strokeStyle=s.borderColor,n.lineWidth=s.borderWidth,n.fillStyle=s.backgroundColor,Zl(n,s,this.x,this.y))}getRange(){const n=this.options||{};return n.radius+n.hitRadius}}D(Zr,"id","point"),D(Zr,"defaults",{borderWidth:1,hitRadius:1,hoverBorderWidth:1,hoverRadius:4,pointStyle:"circle",radius:3,rotation:0}),D(Zr,"defaultRoutes",{backgroundColor:"backgroundColor",borderColor:"borderColor"});function Cm(t,e){const{x:n,y:i,base:s,width:r,height:o}=t.getProps(["x","y","base","width","height"],e);let a,l,u,c,d;return t.horizontal?(d=o/2,a=Math.min(n,s),l=Math.max(n,s),u=i-d,c=i+d):(d=r/2,a=n-d,l=n+d,u=Math.min(i,s),c=Math.max(i,s)),{left:a,top:u,right:l,bottom:c}}function Jt(t,e,n,i){return t?0:Ee(e,n,i)}function OS(t,e,n){const i=t.options.borderWidth,s=t.borderSkipped,r=nm(i);return{t:Jt(s.top,r.top,0,n),r:Jt(s.right,r.right,0,e),b:Jt(s.bottom,r.bottom,0,n),l:Jt(s.left,r.left,0,e)}}function NS(t,e,n){const{enableBorderRadius:i}=t.getProps(["enableBorderRadius"]),s=t.options.borderRadius,r=mi(s),o=Math.min(e,n),a=t.borderSkipped,l=i||Y(s);return{topLeft:Jt(!l||a.top||a.left,r.topLeft,0,o),topRight:Jt(!l||a.top||a.right,r.topRight,0,o),bottomLeft:Jt(!l||a.bottom||a.left,r.bottomLeft,0,o),bottomRight:Jt(!l||a.bottom||a.right,r.bottomRight,0,o)}}function LS(t){const e=Cm(t),n=e.right-e.left,i=e.bottom-e.top,s=OS(t,n/2,i/2),r=NS(t,n/2,i/2);return{outer:{x:e.left,y:e.top,w:n,h:i,radius:r},inner:{x:e.left+s.l,y:e.top+s.t,w:n-s.l-s.r,h:i-s.t-s.b,radius:{topLeft:Math.max(0,r.topLeft-Math.max(s.t,s.l)),topRight:Math.max(0,r.topRight-Math.max(s.t,s.r)),bottomLeft:Math.max(0,r.bottomLeft-Math.max(s.b,s.l)),bottomRight:Math.max(0,r.bottomRight-Math.max(s.b,s.r))}}}}function Ya(t,e,n,i){const s=e===null,r=n===null,a=t&&!(s&&r)&&Cm(t,i);return a&&(s||Gt(e,a.left,a.right))&&(r||Gt(n,a.top,a.bottom))}function TS(t){return t.topLeft||t.topRight||t.bottomLeft||t.bottomRight}function RS(t,e){t.rect(e.x,e.y,e.w,e.h)}function Xa(t,e,n={}){const i=t.x!==n.x?-e:0,s=t.y!==n.y?-e:0,r=(t.x+t.w!==n.x+n.w?e:0)-i,o=(t.y+t.h!==n.y+n.h?e:0)-s;return{x:t.x+i,y:t.y+s,w:t.w+r,h:t.h+o,radius:t.radius}}class ms extends gt{constructor(e){super(),this.options=void 0,this.horizontal=void 0,this.base=void 0,this.width=void 0,this.height=void 0,this.inflateAmount=void 0,e&&Object.assign(this,e)}draw(e){const{inflateAmount:n,options:{borderColor:i,backgroundColor:s}}=this,{inner:r,outer:o}=LS(this),a=TS(o.radius)?No:RS;e.save(),(o.w!==r.w||o.h!==r.h)&&(e.beginPath(),a(e,Xa(o,n,r)),e.clip(),a(e,Xa(r,-n,o)),e.fillStyle=i,e.fill("evenodd")),e.beginPath(),a(e,Xa(r,n)),e.fillStyle=s,e.fill(),e.restore()}inRange(e,n,i){return Ya(this,e,n,i)}inXRange(e,n){return Ya(this,e,null,n)}inYRange(e,n){return Ya(this,null,e,n)}getCenterPoint(e){const{x:n,y:i,base:s,horizontal:r}=this.getProps(["x","y","base","horizontal"],e);return{x:r?(n+s)/2:n,y:r?i:(i+s)/2}}getRange(e){return e==="x"?this.width/2:this.height/2}}D(ms,"id","bar"),D(ms,"defaults",{borderSkipped:"start",borderWidth:0,borderRadius:0,inflateAmount:"auto",pointStyle:void 0}),D(ms,"defaultRoutes",{backgroundColor:"backgroundColor",borderColor:"borderColor"});const jf=(t,e)=>{let{boxHeight:n=e,boxWidth:i=e}=t;return t.usePointStyle&&(n=Math.min(n,e),i=t.pointStyleWidth||Math.min(i,e)),{boxWidth:i,boxHeight:n,itemHeight:Math.max(e,n)}},DS=(t,e)=>t!==null&&e!==null&&t.datasetIndex===e.datasetIndex&&t.index===e.index;class Af extends gt{constructor(e){super(),this._added=!1,this.legendHitBoxes=[],this._hoveredItem=null,this.doughnutMode=!1,this.chart=e.chart,this.options=e.options,this.ctx=e.ctx,this.legendItems=void 0,this.columnSizes=void 0,this.lineWidths=void 0,this.maxHeight=void 0,this.maxWidth=void 0,this.top=void 0,this.bottom=void 0,this.left=void 0,this.right=void 0,this.height=void 0,this.width=void 0,this._margins=void 0,this.position=void 0,this.weight=void 0,this.fullSize=void 0}update(e,n,i){this.maxWidth=e,this.maxHeight=n,this._margins=i,this.setDimensions(),this.buildLabels(),this.fit()}setDimensions(){this.isHorizontal()?(this.width=this.maxWidth,this.left=this._margins.left,this.right=this.width):(this.height=this.maxHeight,this.top=this._margins.top,this.bottom=this.height)}buildLabels(){const e=this.options.labels||{};let n=te(e.generateLabels,[this.chart],this)||[];e.filter&&(n=n.filter(i=>e.filter(i,this.chart.data))),e.sort&&(n=n.sort((i,s)=>e.sort(i,s,this.chart.data))),this.options.reverse&&n.reverse(),this.legendItems=n}fit(){const{options:e,ctx:n}=this;if(!e.display){this.width=this.height=0;return}const i=e.labels,s=Oe(i.font),r=s.size,o=this._computeTitleHeight(),{boxWidth:a,itemHeight:l}=jf(i,r);let u,c;n.font=s.string,this.isHorizontal()?(u=this.maxWidth,c=this._fitRows(o,r,a,l)+10):(c=this.maxHeight,u=this._fitCols(o,s,a,l)+10),this.width=Math.min(u,e.maxWidth||this.maxWidth),this.height=Math.min(c,e.maxHeight||this.maxHeight)}_fitRows(e,n,i,s){const{ctx:r,maxWidth:o,options:{labels:{padding:a}}}=this,l=this.legendHitBoxes=[],u=this.lineWidths=[0],c=s+a;let d=e;r.textAlign="left",r.textBaseline="middle";let f=-1,h=-c;return this.legendItems.forEach((p,y)=>{const v=i+n/2+r.measureText(p.text).width;(y===0||u[u.length-1]+v+2*a>o)&&(d+=c,u[u.length-(y>0?0:1)]=0,h+=c,f++),l[y]={left:0,top:h,row:f,width:v,height:s},u[u.length-1]+=v+a}),d}_fitCols(e,n,i,s){const{ctx:r,maxHeight:o,options:{labels:{padding:a}}}=this,l=this.legendHitBoxes=[],u=this.columnSizes=[],c=o-e;let d=a,f=0,h=0,p=0,y=0;return this.legendItems.forEach((v,m)=>{const{itemWidth:g,itemHeight:x}=jS(i,n,r,v,s);m>0&&h+x+2*a>c&&(d+=f+a,u.push({width:f,height:h}),p+=f+a,y++,f=h=0),l[m]={left:p,top:h,col:y,width:g,height:x},f=Math.max(f,g),h+=x+a}),d+=f,u.push({width:f,height:h}),d}adjustHitBoxes(){if(!this.options.display)return;const e=this._computeTitleHeight(),{legendHitBoxes:n,options:{align:i,labels:{padding:s},rtl:r}}=this,o=yi(r,this.left,this.width);if(this.isHorizontal()){let a=0,l=Pe(i,this.left+s,this.right-this.lineWidths[a]);for(const u of n)a!==u.row&&(a=u.row,l=Pe(i,this.left+s,this.right-this.lineWidths[a])),u.top+=this.top+e+s,u.left=o.leftForLtr(o.x(l),u.width),l+=u.width+s}else{let a=0,l=Pe(i,this.top+e+s,this.bottom-this.columnSizes[a].height);for(const u of n)u.col!==a&&(a=u.col,l=Pe(i,this.top+e+s,this.bottom-this.columnSizes[a].height)),u.top=l,u.left+=this.left+s,u.left=o.leftForLtr(o.x(u.left),u.width),l+=u.height+s}}isHorizontal(){return this.options.position==="top"||this.options.position==="bottom"}draw(){if(this.options.display){const e=this.ctx;lc(e,this),this._draw(),uc(e)}}_draw(){const{options:e,columnSizes:n,lineWidths:i,ctx:s}=this,{align:r,labels:o}=e,a=de.color,l=yi(e.rtl,this.left,this.width),u=Oe(o.font),{padding:c}=o,d=u.size,f=d/2;let h;this.drawTitle(),s.textAlign=l.textAlign("left"),s.textBaseline="middle",s.lineWidth=.5,s.font=u.string;const{boxWidth:p,boxHeight:y,itemHeight:v}=jf(o,d),m=function(C,k,b){if(isNaN(p)||p<=0||isNaN(y)||y<0)return;s.save();const M=B(b.lineWidth,1);if(s.fillStyle=B(b.fillStyle,a),s.lineCap=B(b.lineCap,"butt"),s.lineDashOffset=B(b.lineDashOffset,0),s.lineJoin=B(b.lineJoin,"miter"),s.lineWidth=M,s.strokeStyle=B(b.strokeStyle,a),s.setLineDash(B(b.lineDash,[])),o.usePointStyle){const P={radius:y*Math.SQRT2/2,pointStyle:b.pointStyle,rotation:b.rotation,borderWidth:M},T=l.xPlus(C,p/2),I=k+f;tm(s,P,T,I,o.pointStyleWidth&&p)}else{const P=k+Math.max((d-y)/2,0),T=l.leftForLtr(C,p),I=mi(b.borderRadius);s.beginPath(),Object.values(I).some(H=>H!==0)?No(s,{x:T,y:P,w:p,h:y,radius:I}):s.rect(T,P,p,y),s.fill(),M!==0&&s.stroke()}s.restore()},g=function(C,k,b){Hs(s,b.text,C,k+v/2,u,{strikethrough:b.hidden,textAlign:l.textAlign(b.textAlign)})},x=this.isHorizontal(),w=this._computeTitleHeight();x?h={x:Pe(r,this.left+c,this.right-i[0]),y:this.top+c+w,line:0}:h={x:this.left+c,y:Pe(r,this.top+w+c,this.bottom-n[0].height),line:0},lm(this.ctx,e.textDirection);const S=v+c;this.legendItems.forEach((C,k)=>{s.strokeStyle=C.fontColor,s.fillStyle=C.fontColor;const b=s.measureText(C.text).width,M=l.textAlign(C.textAlign||(C.textAlign=o.textAlign)),P=p+f+b;let T=h.x,I=h.y;l.setWidth(this.width),x?k>0&&T+P+c>this.right&&(I=h.y+=S,h.line++,T=h.x=Pe(r,this.left+c,this.right-i[h.line])):k>0&&I+S>this.bottom&&(T=h.x=T+n[h.line].width+c,h.line++,I=h.y=Pe(r,this.top+w+c,this.bottom-n[h.line].height));const H=l.x(T);if(m(H,I,C),T=E1(M,T+p+f,x?T+P:this.right,e.rtl),g(l.x(T),I,C),x)h.x+=P+c;else if(typeof C.text!="string"){const J=u.lineHeight;h.y+=Pm(C,J)+c}else h.y+=S}),um(this.ctx,e.textDirection)}drawTitle(){const e=this.options,n=e.title,i=Oe(n.font),s=ot(n.padding);if(!n.display)return;const r=yi(e.rtl,this.left,this.width),o=this.ctx,a=n.position,l=i.size/2,u=s.top+l;let c,d=this.left,f=this.width;if(this.isHorizontal())f=Math.max(...this.lineWidths),c=this.top+u,d=Pe(e.align,d,this.right-f);else{const p=this.columnSizes.reduce((y,v)=>Math.max(y,v.height),0);c=u+Pe(e.align,this.top,this.bottom-p-e.labels.padding-this._computeTitleHeight())}const h=Pe(a,d,d+f);o.textAlign=r.textAlign(rc(a)),o.textBaseline="middle",o.strokeStyle=n.color,o.fillStyle=n.color,o.font=i.string,Hs(o,n.text,h,c,i)}_computeTitleHeight(){const e=this.options.title,n=Oe(e.font),i=ot(e.padding);return e.display?n.lineHeight+i.height:0}_getLegendItemAt(e,n){let i,s,r;if(Gt(e,this.left,this.right)&&Gt(n,this.top,this.bottom)){for(r=this.legendHitBoxes,i=0;i<r.length;++i)if(s=r[i],Gt(e,s.left,s.left+s.width)&&Gt(n,s.top,s.top+s.height))return this.legendItems[i]}return null}handleEvent(e){const n=this.options;if(!FS(e.type,n))return;const i=this._getLegendItemAt(e.x,e.y);if(e.type==="mousemove"||e.type==="mouseout"){const s=this._hoveredItem,r=DS(s,i);s&&!r&&te(n.onLeave,[e,s,this],this),this._hoveredItem=i,i&&!r&&te(n.onHover,[e,i,this],this)}else i&&te(n.onClick,[e,i,this],this)}}function jS(t,e,n,i,s){const r=AS(i,t,e,n),o=IS(s,i,e.lineHeight);return{itemWidth:r,itemHeight:o}}function AS(t,e,n,i){let s=t.text;return s&&typeof s!="string"&&(s=s.reduce((r,o)=>r.length>o.length?r:o)),e+n.size/2+i.measureText(s).width}function IS(t,e,n){let i=t;return typeof e.text!="string"&&(i=Pm(e,n)),i}function Pm(t,e){const n=t.text?t.text.length:0;return e*n}function FS(t,e){return!!((t==="mousemove"||t==="mouseout")&&(e.onHover||e.onLeave)||e.onClick&&(t==="click"||t==="mouseup"))}var oa={id:"legend",_element:Af,start(t,e,n){const i=t.legend=new Af({ctx:t.ctx,options:n,chart:t});tt.configure(t,i,n),tt.addBox(t,i)},stop(t){tt.removeBox(t,t.legend),delete t.legend},beforeUpdate(t,e,n){const i=t.legend;tt.configure(t,i,n),i.options=n},afterUpdate(t){const e=t.legend;e.buildLabels(),e.adjustHitBoxes()},afterEvent(t,e){e.replay||t.legend.handleEvent(e.event)},defaults:{display:!0,position:"top",align:"center",fullSize:!0,reverse:!1,weight:1e3,onClick(t,e,n){const i=e.datasetIndex,s=n.chart;s.isDatasetVisible(i)?(s.hide(i),e.hidden=!0):(s.show(i),e.hidden=!1)},onHover:null,onLeave:null,labels:{color:t=>t.chart.options.color,boxWidth:40,padding:10,generateLabels(t){const e=t.data.datasets,{labels:{usePointStyle:n,pointStyle:i,textAlign:s,color:r,useBorderRadius:o,borderRadius:a}}=t.legend.options;return t._getSortedDatasetMetas().map(l=>{const u=l.controller.getStyle(n?0:void 0),c=ot(u.borderWidth);return{text:e[l.index].label,fillStyle:u.backgroundColor,fontColor:r,hidden:!l.visible,lineCap:u.borderCapStyle,lineDash:u.borderDash,lineDashOffset:u.borderDashOffset,lineJoin:u.borderJoinStyle,lineWidth:(c.width+c.height)/4,strokeStyle:u.borderColor,pointStyle:i||u.pointStyle,rotation:u.rotation,textAlign:s||u.textAlign,borderRadius:o&&(a||u.borderRadius),datasetIndex:l.index}},this)}},title:{color:t=>t.chart.options.color,display:!1,position:"center",text:""}},descriptors:{_scriptable:t=>!t.startsWith("on"),labels:{_scriptable:t=>!["generateLabels","filter","sort"].includes(t)}}};class Mm extends gt{constructor(e){super(),this.chart=e.chart,this.options=e.options,this.ctx=e.ctx,this._padding=void 0,this.top=void 0,this.bottom=void 0,this.left=void 0,this.right=void 0,this.width=void 0,this.height=void 0,this.position=void 0,this.weight=void 0,this.fullSize=void 0}update(e,n){const i=this.options;if(this.left=0,this.top=0,!i.display){this.width=this.height=this.right=this.bottom=0;return}this.width=this.right=e,this.height=this.bottom=n;const s=pe(i.text)?i.text.length:1;this._padding=ot(i.padding);const r=s*Oe(i.font).lineHeight+this._padding.height;this.isHorizontal()?this.height=r:this.width=r}isHorizontal(){const e=this.options.position;return e==="top"||e==="bottom"}_drawArgs(e){const{top:n,left:i,bottom:s,right:r,options:o}=this,a=o.align;let l=0,u,c,d;return this.isHorizontal()?(c=Pe(a,i,r),d=n+e,u=r-i):(o.position==="left"?(c=i+e,d=Pe(a,s,n),l=q*-.5):(c=r-e,d=Pe(a,n,s),l=q*.5),u=s-n),{titleX:c,titleY:d,maxWidth:u,rotation:l}}draw(){const e=this.ctx,n=this.options;if(!n.display)return;const i=Oe(n.font),r=i.lineHeight/2+this._padding.top,{titleX:o,titleY:a,maxWidth:l,rotation:u}=this._drawArgs(r);Hs(e,n.text,0,0,i,{color:n.color,maxWidth:l,rotation:u,textAlign:rc(n.align),textBaseline:"middle",translation:[o,a]})}}function zS(t,e){const n=new Mm({ctx:t.ctx,options:e,chart:t});tt.configure(t,n,e),tt.addBox(t,n),t.titleBlock=n}var Em={id:"title",_element:Mm,start(t,e,n){zS(t,n)},stop(t){const e=t.titleBlock;tt.removeBox(t,e),delete t.titleBlock},beforeUpdate(t,e,n){const i=t.titleBlock;tt.configure(t,i,n),i.options=n},defaults:{align:"center",display:!1,font:{weight:"bold"},fullSize:!0,padding:10,position:"top",text:"",weight:2e3},defaultRoutes:{color:"color"},descriptors:{_scriptable:!0,_indexable:!1}};const ns={average(t){if(!t.length)return!1;let e,n,i=new Set,s=0,r=0;for(e=0,n=t.length;e<n;++e){const a=t[e].element;if(a&&a.hasValue()){const l=a.tooltipPosition();i.add(l.x),s+=l.y,++r}}return r===0||i.size===0?!1:{x:[...i].reduce((a,l)=>a+l)/i.size,y:s/r}},nearest(t,e){if(!t.length)return!1;let n=e.x,i=e.y,s=Number.POSITIVE_INFINITY,r,o,a;for(r=0,o=t.length;r<o;++r){const l=t[r].element;if(l&&l.hasValue()){const u=l.getCenterPoint(),c=Gl(e,u);c<s&&(s=c,a=l)}}if(a){const l=a.tooltipPosition();n=l.x,i=l.y}return{x:n,y:i}}};function xt(t,e){return e&&(pe(e)?Array.prototype.push.apply(t,e):t.push(e)),t}function Et(t){return(typeof t=="string"||t instanceof String)&&t.indexOf(`
`)>-1?t.split(`
`):t}function BS(t,e){const{element:n,datasetIndex:i,index:s}=e,r=t.getDatasetMeta(i).controller,{label:o,value:a}=r.getLabelAndValue(s);return{chart:t,label:o,parsed:r.getParsed(s),raw:t.data.datasets[i].data[s],formattedValue:a,dataset:r.getDataset(),dataIndex:s,datasetIndex:i,element:n}}function If(t,e){const n=t.chart.ctx,{body:i,footer:s,title:r}=t,{boxWidth:o,boxHeight:a}=e,l=Oe(e.bodyFont),u=Oe(e.titleFont),c=Oe(e.footerFont),d=r.length,f=s.length,h=i.length,p=ot(e.padding);let y=p.height,v=0,m=i.reduce((w,S)=>w+S.before.length+S.lines.length+S.after.length,0);if(m+=t.beforeBody.length+t.afterBody.length,d&&(y+=d*u.lineHeight+(d-1)*e.titleSpacing+e.titleMarginBottom),m){const w=e.displayColors?Math.max(a,l.lineHeight):l.lineHeight;y+=h*w+(m-h)*l.lineHeight+(m-1)*e.bodySpacing}f&&(y+=e.footerMarginTop+f*c.lineHeight+(f-1)*e.footerSpacing);let g=0;const x=function(w){v=Math.max(v,n.measureText(w).width+g)};return n.save(),n.font=u.string,Q(t.title,x),n.font=l.string,Q(t.beforeBody.concat(t.afterBody),x),g=e.displayColors?o+2+e.boxPadding:0,Q(i,w=>{Q(w.before,x),Q(w.lines,x),Q(w.after,x)}),g=0,n.font=c.string,Q(t.footer,x),n.restore(),v+=p.width,{width:v,height:y}}function $S(t,e){const{y:n,height:i}=e;return n<i/2?"top":n>t.height-i/2?"bottom":"center"}function VS(t,e,n,i){const{x:s,width:r}=i,o=n.caretSize+n.caretPadding;if(t==="left"&&s+r+o>e.width||t==="right"&&s-r-o<0)return!0}function HS(t,e,n,i){const{x:s,width:r}=n,{width:o,chartArea:{left:a,right:l}}=t;let u="center";return i==="center"?u=s<=(a+l)/2?"left":"right":s<=r/2?u="left":s>=o-r/2&&(u="right"),VS(u,t,e,n)&&(u="center"),u}function Ff(t,e,n){const i=n.yAlign||e.yAlign||$S(t,n);return{xAlign:n.xAlign||e.xAlign||HS(t,e,n,i),yAlign:i}}function WS(t,e){let{x:n,width:i}=t;return e==="right"?n-=i:e==="center"&&(n-=i/2),n}function US(t,e,n){let{y:i,height:s}=t;return e==="top"?i+=n:e==="bottom"?i-=s+n:i-=s/2,i}function zf(t,e,n,i){const{caretSize:s,caretPadding:r,cornerRadius:o}=t,{xAlign:a,yAlign:l}=n,u=s+r,{topLeft:c,topRight:d,bottomLeft:f,bottomRight:h}=mi(o);let p=WS(e,a);const y=US(e,l,u);return l==="center"?a==="left"?p+=u:a==="right"&&(p-=u):a==="left"?p-=Math.max(c,f)+s:a==="right"&&(p+=Math.max(d,h)+s),{x:Ee(p,0,i.width-e.width),y:Ee(y,0,i.height-e.height)}}function Dr(t,e,n){const i=ot(n.padding);return e==="center"?t.x+t.width/2:e==="right"?t.x+t.width-i.right:t.x+i.left}function Bf(t){return xt([],Et(t))}function KS(t,e,n){return Hn(t,{tooltip:e,tooltipItems:n,type:"tooltip"})}function $f(t,e){const n=e&&e.dataset&&e.dataset.tooltip&&e.dataset.tooltip.callbacks;return n?t.override(n):t}const Om={beforeTitle:Pt,title(t){if(t.length>0){const e=t[0],n=e.chart.data.labels,i=n?n.length:0;if(this&&this.options&&this.options.mode==="dataset")return e.dataset.label||"";if(e.label)return e.label;if(i>0&&e.dataIndex<i)return n[e.dataIndex]}return""},afterTitle:Pt,beforeBody:Pt,beforeLabel:Pt,label(t){if(this&&this.options&&this.options.mode==="dataset")return t.label+": "+t.formattedValue||t.formattedValue;let e=t.dataset.label||"";e&&(e+=": ");const n=t.formattedValue;return X(n)||(e+=n),e},labelColor(t){const n=t.chart.getDatasetMeta(t.datasetIndex).controller.getStyle(t.dataIndex);return{borderColor:n.borderColor,backgroundColor:n.backgroundColor,borderWidth:n.borderWidth,borderDash:n.borderDash,borderDashOffset:n.borderDashOffset,borderRadius:0}},labelTextColor(){return this.options.bodyColor},labelPointStyle(t){const n=t.chart.getDatasetMeta(t.datasetIndex).controller.getStyle(t.dataIndex);return{pointStyle:n.pointStyle,rotation:n.rotation}},afterLabel:Pt,afterBody:Pt,beforeFooter:Pt,footer:Pt,afterFooter:Pt};function Ie(t,e,n,i){const s=t[e].call(n,i);return typeof s>"u"?Om[e].call(n,i):s}class su extends gt{constructor(e){super(),this.opacity=0,this._active=[],this._eventPosition=void 0,this._size=void 0,this._cachedAnimations=void 0,this._tooltipItems=[],this.$animations=void 0,this.$context=void 0,this.chart=e.chart,this.options=e.options,this.dataPoints=void 0,this.title=void 0,this.beforeBody=void 0,this.body=void 0,this.afterBody=void 0,this.footer=void 0,this.xAlign=void 0,this.yAlign=void 0,this.x=void 0,this.y=void 0,this.height=void 0,this.width=void 0,this.caretX=void 0,this.caretY=void 0,this.labelColors=void 0,this.labelPointStyles=void 0,this.labelTextColors=void 0}initialize(e){this.options=e,this._cachedAnimations=void 0,this.$context=void 0}_resolveAnimations(){const e=this._cachedAnimations;if(e)return e;const n=this.chart,i=this.options.setContext(this.getContext()),s=i.enabled&&n.options.animation&&i.animations,r=new dm(this.chart,s);return s._cacheable&&(this._cachedAnimations=Object.freeze(r)),r}getContext(){return this.$context||(this.$context=KS(this.chart.getContext(),this,this._tooltipItems))}getTitle(e,n){const{callbacks:i}=n,s=Ie(i,"beforeTitle",this,e),r=Ie(i,"title",this,e),o=Ie(i,"afterTitle",this,e);let a=[];return a=xt(a,Et(s)),a=xt(a,Et(r)),a=xt(a,Et(o)),a}getBeforeBody(e,n){return Bf(Ie(n.callbacks,"beforeBody",this,e))}getBody(e,n){const{callbacks:i}=n,s=[];return Q(e,r=>{const o={before:[],lines:[],after:[]},a=$f(i,r);xt(o.before,Et(Ie(a,"beforeLabel",this,r))),xt(o.lines,Ie(a,"label",this,r)),xt(o.after,Et(Ie(a,"afterLabel",this,r))),s.push(o)}),s}getAfterBody(e,n){return Bf(Ie(n.callbacks,"afterBody",this,e))}getFooter(e,n){const{callbacks:i}=n,s=Ie(i,"beforeFooter",this,e),r=Ie(i,"footer",this,e),o=Ie(i,"afterFooter",this,e);let a=[];return a=xt(a,Et(s)),a=xt(a,Et(r)),a=xt(a,Et(o)),a}_createItems(e){const n=this._active,i=this.chart.data,s=[],r=[],o=[];let a=[],l,u;for(l=0,u=n.length;l<u;++l)a.push(BS(this.chart,n[l]));return e.filter&&(a=a.filter((c,d,f)=>e.filter(c,d,f,i))),e.itemSort&&(a=a.sort((c,d)=>e.itemSort(c,d,i))),Q(a,c=>{const d=$f(e.callbacks,c);s.push(Ie(d,"labelColor",this,c)),r.push(Ie(d,"labelPointStyle",this,c)),o.push(Ie(d,"labelTextColor",this,c))}),this.labelColors=s,this.labelPointStyles=r,this.labelTextColors=o,this.dataPoints=a,a}update(e,n){const i=this.options.setContext(this.getContext()),s=this._active;let r,o=[];if(!s.length)this.opacity!==0&&(r={opacity:0});else{const a=ns[i.position].call(this,s,this._eventPosition);o=this._createItems(i),this.title=this.getTitle(o,i),this.beforeBody=this.getBeforeBody(o,i),this.body=this.getBody(o,i),this.afterBody=this.getAfterBody(o,i),this.footer=this.getFooter(o,i);const l=this._size=If(this,i),u=Object.assign({},a,l),c=Ff(this.chart,i,u),d=zf(i,u,c,this.chart);this.xAlign=c.xAlign,this.yAlign=c.yAlign,r={opacity:1,x:d.x,y:d.y,width:l.width,height:l.height,caretX:a.x,caretY:a.y}}this._tooltipItems=o,this.$context=void 0,r&&this._resolveAnimations().update(this,r),e&&i.external&&i.external.call(this,{chart:this.chart,tooltip:this,replay:n})}drawCaret(e,n,i,s){const r=this.getCaretPosition(e,i,s);n.lineTo(r.x1,r.y1),n.lineTo(r.x2,r.y2),n.lineTo(r.x3,r.y3)}getCaretPosition(e,n,i){const{xAlign:s,yAlign:r}=this,{caretSize:o,cornerRadius:a}=i,{topLeft:l,topRight:u,bottomLeft:c,bottomRight:d}=mi(a),{x:f,y:h}=e,{width:p,height:y}=n;let v,m,g,x,w,S;return r==="center"?(w=h+y/2,s==="left"?(v=f,m=v-o,x=w+o,S=w-o):(v=f+p,m=v+o,x=w-o,S=w+o),g=v):(s==="left"?m=f+Math.max(l,c)+o:s==="right"?m=f+p-Math.max(u,d)-o:m=this.caretX,r==="top"?(x=h,w=x-o,v=m-o,g=m+o):(x=h+y,w=x+o,v=m+o,g=m-o),S=x),{x1:v,x2:m,x3:g,y1:x,y2:w,y3:S}}drawTitle(e,n,i){const s=this.title,r=s.length;let o,a,l;if(r){const u=yi(i.rtl,this.x,this.width);for(e.x=Dr(this,i.titleAlign,i),n.textAlign=u.textAlign(i.titleAlign),n.textBaseline="middle",o=Oe(i.titleFont),a=i.titleSpacing,n.fillStyle=i.titleColor,n.font=o.string,l=0;l<r;++l)n.fillText(s[l],u.x(e.x),e.y+o.lineHeight/2),e.y+=o.lineHeight+a,l+1===r&&(e.y+=i.titleMarginBottom-a)}}_drawColorBox(e,n,i,s,r){const o=this.labelColors[i],a=this.labelPointStyles[i],{boxHeight:l,boxWidth:u}=r,c=Oe(r.bodyFont),d=Dr(this,"left",r),f=s.x(d),h=l<c.lineHeight?(c.lineHeight-l)/2:0,p=n.y+h;if(r.usePointStyle){const y={radius:Math.min(u,l)/2,pointStyle:a.pointStyle,rotation:a.rotation,borderWidth:1},v=s.leftForLtr(f,u)+u/2,m=p+l/2;e.strokeStyle=r.multiKeyBackground,e.fillStyle=r.multiKeyBackground,Zl(e,y,v,m),e.strokeStyle=o.borderColor,e.fillStyle=o.backgroundColor,Zl(e,y,v,m)}else{e.lineWidth=Y(o.borderWidth)?Math.max(...Object.values(o.borderWidth)):o.borderWidth||1,e.strokeStyle=o.borderColor,e.setLineDash(o.borderDash||[]),e.lineDashOffset=o.borderDashOffset||0;const y=s.leftForLtr(f,u),v=s.leftForLtr(s.xPlus(f,1),u-2),m=mi(o.borderRadius);Object.values(m).some(g=>g!==0)?(e.beginPath(),e.fillStyle=r.multiKeyBackground,No(e,{x:y,y:p,w:u,h:l,radius:m}),e.fill(),e.stroke(),e.fillStyle=o.backgroundColor,e.beginPath(),No(e,{x:v,y:p+1,w:u-2,h:l-2,radius:m}),e.fill()):(e.fillStyle=r.multiKeyBackground,e.fillRect(y,p,u,l),e.strokeRect(y,p,u,l),e.fillStyle=o.backgroundColor,e.fillRect(v,p+1,u-2,l-2))}e.fillStyle=this.labelTextColors[i]}drawBody(e,n,i){const{body:s}=this,{bodySpacing:r,bodyAlign:o,displayColors:a,boxHeight:l,boxWidth:u,boxPadding:c}=i,d=Oe(i.bodyFont);let f=d.lineHeight,h=0;const p=yi(i.rtl,this.x,this.width),y=function(b){n.fillText(b,p.x(e.x+h),e.y+f/2),e.y+=f+r},v=p.textAlign(o);let m,g,x,w,S,C,k;for(n.textAlign=o,n.textBaseline="middle",n.font=d.string,e.x=Dr(this,v,i),n.fillStyle=i.bodyColor,Q(this.beforeBody,y),h=a&&v!=="right"?o==="center"?u/2+c:u+2+c:0,w=0,C=s.length;w<C;++w){for(m=s[w],g=this.labelTextColors[w],n.fillStyle=g,Q(m.before,y),x=m.lines,a&&x.length&&(this._drawColorBox(n,e,w,p,i),f=Math.max(d.lineHeight,l)),S=0,k=x.length;S<k;++S)y(x[S]),f=d.lineHeight;Q(m.after,y)}h=0,f=d.lineHeight,Q(this.afterBody,y),e.y-=r}drawFooter(e,n,i){const s=this.footer,r=s.length;let o,a;if(r){const l=yi(i.rtl,this.x,this.width);for(e.x=Dr(this,i.footerAlign,i),e.y+=i.footerMarginTop,n.textAlign=l.textAlign(i.footerAlign),n.textBaseline="middle",o=Oe(i.footerFont),n.fillStyle=i.footerColor,n.font=o.string,a=0;a<r;++a)n.fillText(s[a],l.x(e.x),e.y+o.lineHeight/2),e.y+=o.lineHeight+i.footerSpacing}}drawBackground(e,n,i,s){const{xAlign:r,yAlign:o}=this,{x:a,y:l}=e,{width:u,height:c}=i,{topLeft:d,topRight:f,bottomLeft:h,bottomRight:p}=mi(s.cornerRadius);n.fillStyle=s.backgroundColor,n.strokeStyle=s.borderColor,n.lineWidth=s.borderWidth,n.beginPath(),n.moveTo(a+d,l),o==="top"&&this.drawCaret(e,n,i,s),n.lineTo(a+u-f,l),n.quadraticCurveTo(a+u,l,a+u,l+f),o==="center"&&r==="right"&&this.drawCaret(e,n,i,s),n.lineTo(a+u,l+c-p),n.quadraticCurveTo(a+u,l+c,a+u-p,l+c),o==="bottom"&&this.drawCaret(e,n,i,s),n.lineTo(a+h,l+c),n.quadraticCurveTo(a,l+c,a,l+c-h),o==="center"&&r==="left"&&this.drawCaret(e,n,i,s),n.lineTo(a,l+d),n.quadraticCurveTo(a,l,a+d,l),n.closePath(),n.fill(),s.borderWidth>0&&n.stroke()}_updateAnimationTarget(e){const n=this.chart,i=this.$animations,s=i&&i.x,r=i&&i.y;if(s||r){const o=ns[e.position].call(this,this._active,this._eventPosition);if(!o)return;const a=this._size=If(this,e),l=Object.assign({},o,this._size),u=Ff(n,e,l),c=zf(e,l,u,n);(s._to!==c.x||r._to!==c.y)&&(this.xAlign=u.xAlign,this.yAlign=u.yAlign,this.width=a.width,this.height=a.height,this.caretX=o.x,this.caretY=o.y,this._resolveAnimations().update(this,c))}}_willRender(){return!!this.opacity}draw(e){const n=this.options.setContext(this.getContext());let i=this.opacity;if(!i)return;this._updateAnimationTarget(n);const s={width:this.width,height:this.height},r={x:this.x,y:this.y};i=Math.abs(i)<.001?0:i;const o=ot(n.padding),a=this.title.length||this.beforeBody.length||this.body.length||this.afterBody.length||this.footer.length;n.enabled&&a&&(e.save(),e.globalAlpha=i,this.drawBackground(r,e,s,n),lm(e,n.textDirection),r.y+=o.top,this.drawTitle(r,e,n),this.drawBody(r,e,n),this.drawFooter(r,e,n),um(e,n.textDirection),e.restore())}getActiveElements(){return this._active||[]}setActiveElements(e,n){const i=this._active,s=e.map(({datasetIndex:a,index:l})=>{const u=this.chart.getDatasetMeta(a);if(!u)throw new Error("Cannot find a dataset at index "+a);return{datasetIndex:a,element:u.data[l],index:l}}),r=!Mo(i,s),o=this._positionChanged(s,n);(r||o)&&(this._active=s,this._eventPosition=n,this._ignoreReplayEvents=!0,this.update(!0))}handleEvent(e,n,i=!0){if(n&&this._ignoreReplayEvents)return!1;this._ignoreReplayEvents=!1;const s=this.options,r=this._active||[],o=this._getActiveElements(e,r,n,i),a=this._positionChanged(o,e),l=n||!Mo(o,r)||a;return l&&(this._active=o,(s.enabled||s.external)&&(this._eventPosition={x:e.x,y:e.y},this.update(!0,n))),l}_getActiveElements(e,n,i,s){const r=this.options;if(e.type==="mouseout")return[];if(!s)return n.filter(a=>this.chart.data.datasets[a.datasetIndex]&&this.chart.getDatasetMeta(a.datasetIndex).controller.getParsed(a.index)!==void 0);const o=this.chart.getElementsAtEventForMode(e,r.mode,r,i);return r.reverse&&o.reverse(),o}_positionChanged(e,n){const{caretX:i,caretY:s,options:r}=this,o=ns[r.position].call(this,e,n);return o!==!1&&(i!==o.x||s!==o.y)}}D(su,"positioners",ns);var aa={id:"tooltip",_element:su,positioners:ns,afterInit(t,e,n){n&&(t.tooltip=new su({chart:t,options:n}))},beforeUpdate(t,e,n){t.tooltip&&t.tooltip.initialize(n)},reset(t,e,n){t.tooltip&&t.tooltip.initialize(n)},afterDraw(t){const e=t.tooltip;if(e&&e._willRender()){const n={tooltip:e};if(t.notifyPlugins("beforeTooltipDraw",{...n,cancelable:!0})===!1)return;e.draw(t.ctx),t.notifyPlugins("afterTooltipDraw",n)}},afterEvent(t,e){if(t.tooltip){const n=e.replay;t.tooltip.handleEvent(e.event,n,e.inChartArea)&&(e.changed=!0)}},defaults:{enabled:!0,external:null,position:"average",backgroundColor:"rgba(0,0,0,0.8)",titleColor:"#fff",titleFont:{weight:"bold"},titleSpacing:2,titleMarginBottom:6,titleAlign:"left",bodyColor:"#fff",bodySpacing:2,bodyFont:{},bodyAlign:"left",footerColor:"#fff",footerSpacing:2,footerMarginTop:6,footerFont:{weight:"bold"},footerAlign:"left",padding:6,caretPadding:2,caretSize:5,cornerRadius:6,boxHeight:(t,e)=>e.bodyFont.size,boxWidth:(t,e)=>e.bodyFont.size,multiKeyBackground:"#fff",displayColors:!0,boxPadding:0,borderColor:"rgba(0,0,0,0)",borderWidth:0,animation:{duration:400,easing:"easeOutQuart"},animations:{numbers:{type:"number",properties:["x","y","width","height","caretX","caretY"]},opacity:{easing:"linear",duration:200}},callbacks:Om},defaultRoutes:{bodyFont:"font",footerFont:"font",titleFont:"font"},descriptors:{_scriptable:t=>t!=="filter"&&t!=="itemSort"&&t!=="external",_indexable:!1,callbacks:{_scriptable:!1,_indexable:!1},animation:{_fallback:!1},animations:{_fallback:"animation"}},additionalOptionScopes:["interaction"]};const YS=(t,e,n,i)=>(typeof e=="string"?(n=t.push(e)-1,i.unshift({index:n,label:e})):isNaN(e)&&(n=null),n);function XS(t,e,n,i){const s=t.indexOf(e);if(s===-1)return YS(t,e,n,i);const r=t.lastIndexOf(e);return s!==r?n:s}const QS=(t,e)=>t===null?null:Ee(Math.round(t),0,e);function Vf(t){const e=this.getLabels();return t>=0&&t<e.length?e[t]:t}class Us extends Ti{constructor(e){super(e),this._startValue=void 0,this._valueRange=0,this._addedLabels=[]}init(e){const n=this._addedLabels;if(n.length){const i=this.getLabels();for(const{index:s,label:r}of n)i[s]===r&&i.splice(s,1);this._addedLabels=[]}super.init(e)}parse(e,n){if(X(e))return null;const i=this.getLabels();return n=isFinite(n)&&i[n]===e?n:XS(i,e,B(n,e),this._addedLabels),QS(n,i.length-1)}determineDataLimits(){const{minDefined:e,maxDefined:n}=this.getUserBounds();let{min:i,max:s}=this.getMinMax(!0);this.options.bounds==="ticks"&&(e||(i=0),n||(s=this.getLabels().length-1)),this.min=i,this.max=s}buildTicks(){const e=this.min,n=this.max,i=this.options.offset,s=[];let r=this.getLabels();r=e===0&&n===r.length-1?r:r.slice(e,n+1),this._valueRange=Math.max(r.length-(i?0:1),1),this._startValue=this.min-(i?.5:0);for(let o=e;o<=n;o++)s.push({value:o});return s}getLabelForValue(e){return Vf.call(this,e)}configure(){super.configure(),this.isHorizontal()||(this._reversePixels=!this._reversePixels)}getPixelForValue(e){return typeof e!="number"&&(e=this.parse(e)),e===null?NaN:this.getPixelForDecimal((e-this._startValue)/this._valueRange)}getPixelForTick(e){const n=this.ticks;return e<0||e>n.length-1?null:this.getPixelForValue(n[e].value)}getValueForPixel(e){return Math.round(this._startValue+this.getDecimalForPixel(e)*this._valueRange)}getBasePixel(){return this.bottom}}D(Us,"id","category"),D(Us,"defaults",{ticks:{callback:Vf}});function qS(t,e){const n=[],{bounds:s,step:r,min:o,max:a,precision:l,count:u,maxTicks:c,maxDigits:d,includeBounds:f}=t,h=r||1,p=c-1,{min:y,max:v}=e,m=!X(o),g=!X(a),x=!X(u),w=(v-y)/(d+1);let S=Hd((v-y)/p/h)*h,C,k,b,M;if(S<1e-14&&!m&&!g)return[{value:y},{value:v}];M=Math.ceil(v/S)-Math.floor(y/S),M>p&&(S=Hd(M*S/p/h)*h),X(l)||(C=Math.pow(10,l),S=Math.ceil(S*C)/C),s==="ticks"?(k=Math.floor(y/S)*S,b=Math.ceil(v/S)*S):(k=y,b=v),m&&g&&r&&v1((a-o)/r,S/1e3)?(M=Math.round(Math.min((a-o)/S,c)),S=(a-o)/M,k=o,b=a):x?(k=m?o:k,b=g?a:b,M=u-1,S=(b-k)/M):(M=(b-k)/S,hs(M,Math.round(M),S/1e3)?M=Math.round(M):M=Math.ceil(M));const P=Math.max(Wd(S),Wd(k));C=Math.pow(10,X(l)?P:l),k=Math.round(k*C)/C,b=Math.round(b*C)/C;let T=0;for(m&&(f&&k!==o?(n.push({value:o}),k<o&&T++,hs(Math.round((k+T*S)*C)/C,o,Hf(o,w,t))&&T++):k<o&&T++);T<M;++T){const I=Math.round((k+T*S)*C)/C;if(g&&I>a)break;n.push({value:I})}return g&&f&&b!==a?n.length&&hs(n[n.length-1].value,a,Hf(a,w,t))?n[n.length-1].value=a:n.push({value:a}):(!g||b===a)&&n.push({value:b}),n}function Hf(t,e,{horizontal:n,minRotation:i}){const s=Dt(i),r=(n?Math.sin(s):Math.cos(s))||.001,o=.75*e*(""+t).length;return Math.min(e/r,o)}class GS extends Ti{constructor(e){super(e),this.start=void 0,this.end=void 0,this._startValue=void 0,this._endValue=void 0,this._valueRange=0}parse(e,n){return X(e)||(typeof e=="number"||e instanceof Number)&&!isFinite(+e)?null:+e}handleTickRangeOptions(){const{beginAtZero:e}=this.options,{minDefined:n,maxDefined:i}=this.getUserBounds();let{min:s,max:r}=this;const o=l=>s=n?s:l,a=l=>r=i?r:l;if(e){const l=Ct(s),u=Ct(r);l<0&&u<0?a(0):l>0&&u>0&&o(0)}if(s===r){let l=r===0?1:Math.abs(r*.05);a(r+l),e||o(s-l)}this.min=s,this.max=r}getTickLimit(){const e=this.options.ticks;let{maxTicksLimit:n,stepSize:i}=e,s;return i?(s=Math.ceil(this.max/i)-Math.floor(this.min/i)+1,s>1e3&&(console.warn(`scales.${this.id}.ticks.stepSize: ${i} would result generating up to ${s} ticks. Limiting to 1000.`),s=1e3)):(s=this.computeTickLimit(),n=n||11),n&&(s=Math.min(n,s)),s}computeTickLimit(){return Number.POSITIVE_INFINITY}buildTicks(){const e=this.options,n=e.ticks;let i=this.getTickLimit();i=Math.max(2,i);const s={maxTicks:i,bounds:e.bounds,min:e.min,max:e.max,precision:n.precision,step:n.stepSize,count:n.count,maxDigits:this._maxDigits(),horizontal:this.isHorizontal(),minRotation:n.minRotation||0,includeBounds:n.includeBounds!==!1},r=this._range||this,o=qS(s,r);return e.bounds==="ticks"&&_1(o,this,"value"),e.reverse?(o.reverse(),this.start=this.max,this.end=this.min):(this.start=this.min,this.end=this.max),o}configure(){const e=this.ticks;let n=this.min,i=this.max;if(super.configure(),this.options.offset&&e.length){const s=(i-n)/Math.max(e.length-1,1)/2;n-=s,i+=s}this._startValue=n,this._endValue=i,this._valueRange=i-n}getLabelForValue(e){return ac(e,this.chart.options.locale,this.options.ticks.format)}}class Ks extends GS{determineDataLimits(){const{min:e,max:n}=this.getMinMax(!0);this.min=rt(e)?e:0,this.max=rt(n)?n:1,this.handleTickRangeOptions()}computeTickLimit(){const e=this.isHorizontal(),n=e?this.width:this.height,i=Dt(this.options.ticks.minRotation),s=(e?Math.sin(i):Math.cos(i))||.001,r=this._resolveTickFontOptions(0);return Math.ceil(n/Math.min(40,r.lineHeight/s))}getPixelForValue(e){return e===null?NaN:this.getPixelForDecimal((e-this._startValue)/this._valueRange)}getValueForPixel(e){return this._startValue+this.getDecimalForPixel(e)*this._valueRange}}D(Ks,"id","linear"),D(Ks,"defaults",{ticks:{callback:em.formatters.numeric}});const la={millisecond:{common:!0,size:1,steps:1e3},second:{common:!0,size:1e3,steps:60},minute:{common:!0,size:6e4,steps:60},hour:{common:!0,size:36e5,steps:24},day:{common:!0,size:864e5,steps:30},week:{common:!1,size:6048e5,steps:4},month:{common:!0,size:2628e6,steps:12},quarter:{common:!1,size:7884e6,steps:4},year:{common:!0,size:3154e7}},ze=Object.keys(la);function Wf(t,e){return t-e}function Uf(t,e){if(X(e))return null;const n=t._adapter,{parser:i,round:s,isoWeekday:r}=t._parseOpts;let o=e;return typeof i=="function"&&(o=i(o)),rt(o)||(o=typeof i=="string"?n.parse(o,i):n.parse(o)),o===null?null:(s&&(o=s==="week"&&(Bs(r)||r===!0)?n.startOf(o,"isoWeek",r):n.startOf(o,s)),+o)}function Kf(t,e,n,i){const s=ze.length;for(let r=ze.indexOf(t);r<s-1;++r){const o=la[ze[r]],a=o.steps?o.steps:Number.MAX_SAFE_INTEGER;if(o.common&&Math.ceil((n-e)/(a*o.size))<=i)return ze[r]}return ze[s-1]}function JS(t,e,n,i,s){for(let r=ze.length-1;r>=ze.indexOf(n);r--){const o=ze[r];if(la[o].common&&t._adapter.diff(s,i,o)>=e-1)return o}return ze[n?ze.indexOf(n):0]}function ZS(t){for(let e=ze.indexOf(t)+1,n=ze.length;e<n;++e)if(la[ze[e]].common)return ze[e]}function Yf(t,e,n){if(!n)t[e]=!0;else if(n.length){const{lo:i,hi:s}=sc(n,e),r=n[i]>=e?n[i]:n[s];t[r]=!0}}function eb(t,e,n,i){const s=t._adapter,r=+s.startOf(e[0].value,i),o=e[e.length-1].value;let a,l;for(a=r;a<=o;a=+s.add(a,1,i))l=n[a],l>=0&&(e[l].major=!0);return e}function Xf(t,e,n){const i=[],s={},r=e.length;let o,a;for(o=0;o<r;++o)a=e[o],s[a]=o,i.push({value:a,major:!1});return r===0||!n?i:eb(t,i,s,n)}class Do extends Ti{constructor(e){super(e),this._cache={data:[],labels:[],all:[]},this._unit="day",this._majorUnit=void 0,this._offsets={},this._normalized=!1,this._parseOpts=void 0}init(e,n={}){const i=e.time||(e.time={}),s=this._adapter=new rw._date(e.adapters.date);s.init(n),fs(i.displayFormats,s.formats()),this._parseOpts={parser:i.parser,round:i.round,isoWeekday:i.isoWeekday},super.init(e),this._normalized=n.normalized}parse(e,n){return e===void 0?null:Uf(this,e)}beforeLayout(){super.beforeLayout(),this._cache={data:[],labels:[],all:[]}}determineDataLimits(){const e=this.options,n=this._adapter,i=e.time.unit||"day";let{min:s,max:r,minDefined:o,maxDefined:a}=this.getUserBounds();function l(u){!o&&!isNaN(u.min)&&(s=Math.min(s,u.min)),!a&&!isNaN(u.max)&&(r=Math.max(r,u.max))}(!o||!a)&&(l(this._getLabelBounds()),(e.bounds!=="ticks"||e.ticks.source!=="labels")&&l(this.getMinMax(!1))),s=rt(s)&&!isNaN(s)?s:+n.startOf(Date.now(),i),r=rt(r)&&!isNaN(r)?r:+n.endOf(Date.now(),i)+1,this.min=Math.min(s,r-1),this.max=Math.max(s+1,r)}_getLabelBounds(){const e=this.getLabelTimestamps();let n=Number.POSITIVE_INFINITY,i=Number.NEGATIVE_INFINITY;return e.length&&(n=e[0],i=e[e.length-1]),{min:n,max:i}}buildTicks(){const e=this.options,n=e.time,i=e.ticks,s=i.source==="labels"?this.getLabelTimestamps():this._generate();e.bounds==="ticks"&&s.length&&(this.min=this._userMin||s[0],this.max=this._userMax||s[s.length-1]);const r=this.min,o=this.max,a=C1(s,r,o);return this._unit=n.unit||(i.autoSkip?Kf(n.minUnit,this.min,this.max,this._getLabelCapacity(r)):JS(this,a.length,n.minUnit,this.min,this.max)),this._majorUnit=!i.major.enabled||this._unit==="year"?void 0:ZS(this._unit),this.initOffsets(s),e.reverse&&a.reverse(),Xf(this,a,this._majorUnit)}afterAutoSkip(){this.options.offsetAfterAutoskip&&this.initOffsets(this.ticks.map(e=>+e.value))}initOffsets(e=[]){let n=0,i=0,s,r;this.options.offset&&e.length&&(s=this.getDecimalForValue(e[0]),e.length===1?n=1-s:n=(this.getDecimalForValue(e[1])-s)/2,r=this.getDecimalForValue(e[e.length-1]),e.length===1?i=r:i=(r-this.getDecimalForValue(e[e.length-2]))/2);const o=e.length<3?.5:.25;n=Ee(n,0,o),i=Ee(i,0,o),this._offsets={start:n,end:i,factor:1/(n+1+i)}}_generate(){const e=this._adapter,n=this.min,i=this.max,s=this.options,r=s.time,o=r.unit||Kf(r.minUnit,n,i,this._getLabelCapacity(n)),a=B(s.ticks.stepSize,1),l=o==="week"?r.isoWeekday:!1,u=Bs(l)||l===!0,c={};let d=n,f,h;if(u&&(d=+e.startOf(d,"isoWeek",l)),d=+e.startOf(d,u?"day":o),e.diff(i,n,o)>1e5*a)throw new Error(n+" and "+i+" are too far apart with stepSize of "+a+" "+o);const p=s.ticks.source==="data"&&this.getDataTimestamps();for(f=d,h=0;f<i;f=+e.add(f,a,o),h++)Yf(c,f,p);return(f===i||s.bounds==="ticks"||h===1)&&Yf(c,f,p),Object.keys(c).sort(Wf).map(y=>+y)}getLabelForValue(e){const n=this._adapter,i=this.options.time;return i.tooltipFormat?n.format(e,i.tooltipFormat):n.format(e,i.displayFormats.datetime)}format(e,n){const s=this.options.time.displayFormats,r=this._unit,o=n||s[r];return this._adapter.format(e,o)}_tickFormatFunction(e,n,i,s){const r=this.options,o=r.ticks.callback;if(o)return te(o,[e,n,i],this);const a=r.time.displayFormats,l=this._unit,u=this._majorUnit,c=l&&a[l],d=u&&a[u],f=i[n],h=u&&d&&f&&f.major;return this._adapter.format(e,s||(h?d:c))}generateTickLabels(e){let n,i,s;for(n=0,i=e.length;n<i;++n)s=e[n],s.label=this._tickFormatFunction(s.value,n,e)}getDecimalForValue(e){return e===null?NaN:(e-this.min)/(this.max-this.min)}getPixelForValue(e){const n=this._offsets,i=this.getDecimalForValue(e);return this.getPixelForDecimal((n.start+i)*n.factor)}getValueForPixel(e){const n=this._offsets,i=this.getDecimalForPixel(e)/n.factor-n.end;return this.min+i*(this.max-this.min)}_getLabelSize(e){const n=this.options.ticks,i=this.ctx.measureText(e).width,s=Dt(this.isHorizontal()?n.maxRotation:n.minRotation),r=Math.cos(s),o=Math.sin(s),a=this._resolveTickFontOptions(0).size;return{w:i*r+a*o,h:i*o+a*r}}_getLabelCapacity(e){const n=this.options.time,i=n.displayFormats,s=i[n.unit]||i.millisecond,r=this._tickFormatFunction(e,0,Xf(this,[e],this._majorUnit),s),o=this._getLabelSize(r),a=Math.floor(this.isHorizontal()?this.width/o.w:this.height/o.h)-1;return a>0?a:1}getDataTimestamps(){let e=this._cache.data||[],n,i;if(e.length)return e;const s=this.getMatchingVisibleMetas();if(this._normalized&&s.length)return this._cache.data=s[0].controller.getAllParsedValues(this);for(n=0,i=s.length;n<i;++n)e=e.concat(s[n].controller.getAllParsedValues(this));return this._cache.data=this.normalize(e)}getLabelTimestamps(){const e=this._cache.labels||[];let n,i;if(e.length)return e;const s=this.getLabels();for(n=0,i=s.length;n<i;++n)e.push(Uf(this,s[n]));return this._cache.labels=this._normalized?e:this.normalize(e)}normalize(e){return Gg(e.sort(Wf))}}D(Do,"id","time"),D(Do,"defaults",{bounds:"data",adapters:{},time:{parser:!1,unit:!1,round:!1,isoWeekday:!1,minUnit:"millisecond",displayFormats:{}},ticks:{source:"auto",callback:!1,major:{enabled:!1}}});function jr(t,e,n){let i=0,s=t.length-1,r,o,a,l;n?(e>=t[i].pos&&e<=t[s].pos&&({lo:i,hi:s}=Mn(t,"pos",e)),{pos:r,time:a}=t[i],{pos:o,time:l}=t[s]):(e>=t[i].time&&e<=t[s].time&&({lo:i,hi:s}=Mn(t,"time",e)),{time:r,pos:a}=t[i],{time:o,pos:l}=t[s]);const u=o-r;return u?a+(l-a)*(e-r)/u:a}class Qf extends Do{constructor(e){super(e),this._table=[],this._minPos=void 0,this._tableRange=void 0}initOffsets(){const e=this._getTimestampsForTable(),n=this._table=this.buildLookupTable(e);this._minPos=jr(n,this.min),this._tableRange=jr(n,this.max)-this._minPos,super.initOffsets(e)}buildLookupTable(e){const{min:n,max:i}=this,s=[],r=[];let o,a,l,u,c;for(o=0,a=e.length;o<a;++o)u=e[o],u>=n&&u<=i&&s.push(u);if(s.length<2)return[{time:n,pos:0},{time:i,pos:1}];for(o=0,a=s.length;o<a;++o)c=s[o+1],l=s[o-1],u=s[o],Math.round((c+l)/2)!==u&&r.push({time:u,pos:o/(a-1)});return r}_generate(){const e=this.min,n=this.max;let i=super.getDataTimestamps();return(!i.includes(e)||!i.length)&&i.splice(0,0,e),(!i.includes(n)||i.length===1)&&i.push(n),i.sort((s,r)=>s-r)}_getTimestampsForTable(){let e=this._cache.all||[];if(e.length)return e;const n=this.getDataTimestamps(),i=this.getLabelTimestamps();return n.length&&i.length?e=this.normalize(n.concat(i)):e=n.length?n:i,e=this._cache.all=e,e}getDecimalForValue(e){return(jr(this._table,e)-this._minPos)/this._tableRange}getValueForPixel(e){const n=this._offsets,i=this.getDecimalForPixel(e)/n.factor-n.end;return jr(this._table,i*this._tableRange+this._minPos,!0)}}D(Qf,"id","timeseries"),D(Qf,"defaults",Do.defaults);const Nm="label";function qf(t,e){typeof t=="function"?t(e):t&&(t.current=e)}function tb(t,e){const n=t.options;n&&e&&Object.assign(n,e)}function Lm(t,e){t.labels=e}function Tm(t,e){let n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:Nm;const i=[];t.datasets=e.map(s=>{const r=t.datasets.find(o=>o[n]===s[n]);return!r||!s.data||i.includes(r)?{...s}:(i.push(r),Object.assign(r,s),r)})}function nb(t){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:Nm;const n={labels:[],datasets:[]};return Lm(n,t.labels),Tm(n,t.datasets,e),n}function ib(t,e){const{height:n=150,width:i=300,redraw:s=!1,datasetIdKey:r,type:o,data:a,options:l,plugins:u=[],fallbackContent:c,updateMode:d,...f}=t,h=O.useRef(null),p=O.useRef(null),y=()=>{h.current&&(p.current=new Wn(h.current,{type:o,data:nb(a,r),options:l&&{...l},plugins:u}),qf(e,p.current))},v=()=>{qf(e,null),p.current&&(p.current.destroy(),p.current=null)};return O.useEffect(()=>{!s&&p.current&&l&&tb(p.current,l)},[s,l]),O.useEffect(()=>{!s&&p.current&&Lm(p.current.config.data,a.labels)},[s,a.labels]),O.useEffect(()=>{!s&&p.current&&a.datasets&&Tm(p.current.config.data,a.datasets,r)},[s,a.datasets]),O.useEffect(()=>{p.current&&(s?(v(),setTimeout(y)):p.current.update(d))},[s,l,a.labels,a.datasets,d]),O.useEffect(()=>{p.current&&(v(),setTimeout(y))},[o]),O.useEffect(()=>(y(),()=>v()),[]),zo.createElement("canvas",{ref:h,role:"img",height:n,width:i,...f},c)}const sb=O.forwardRef(ib);function yc(t,e){return Wn.register(e),O.forwardRef((n,i)=>zo.createElement(sb,{...n,ref:i,type:t}))}const rb=yc("line",qr),Rm=yc("bar",Qr),ob=yc("pie",eu);Wn.register(Us,Ks,ms,Em,aa,oa);const ab=()=>{const{t}=nr(),e={labels:["Equipment","Mission","Simulation"],datasets:[{label:"Performance (%)",data:[95,80,90],backgroundColor:"hsl(var(--primary) / 0.7)",borderColor:"hsl(var(--accent))",borderWidth:2}]},n={responsive:!0,maintainAspectRatio:!1,plugins:{legend:{position:"top"},title:{display:!0,text:t("dashboard.systemOverview")}},scales:{y:{beginAtZero:!0,max:100}}};return _.jsx("div",{className:"p-6",children:_.jsxs("div",{className:"space-y-6",children:[_.jsx("div",{className:"bg-gradient-to-r from-primary/10 to-primary/5 p-4 rounded-lg border border-primary/20",children:_.jsxs("div",{className:"flex items-center justify-between",children:[_.jsxs("div",{className:"flex items-center gap-4",children:[_.jsx("div",{className:"w-3 h-3 bg-success rounded-full animate-pulse"}),_.jsx("span",{className:"font-medium",children:t("dashboard.allSystemsOperational")}),_.jsx("div",{className:"bg-success text-success-foreground px-2 py-1 rounded text-xs font-medium",children:t("common.secure")})]}),_.jsxs("div",{className:"text-sm text-muted-foreground",children:[t("dashboard.lastUpdated"),": ",new Date().toLocaleTimeString("en-IN")," IST"]})]})}),_.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[_.jsx("div",{className:"bg-card p-6 rounded-lg border border-border border-l-4 border-l-primary",children:_.jsxs("div",{className:"flex items-center justify-between",children:[_.jsxs("div",{children:[_.jsx("p",{className:"text-sm font-medium text-muted-foreground",children:t("dashboard.activeAssets")}),_.jsx("p",{className:"text-3xl font-bold text-primary",children:"247"}),_.jsxs("p",{className:"text-xs text-success",children:["+12 ",t("status.fromYesterday")]})]}),_.jsx("div",{className:"h-10 w-10 text-primary",children:"🛡️"})]})}),_.jsx("div",{className:"bg-card p-6 rounded-lg border border-border border-l-4 border-l-success",children:_.jsxs("div",{className:"flex items-center justify-between",children:[_.jsxs("div",{children:[_.jsx("p",{className:"text-sm font-medium text-muted-foreground",children:t("dashboard.activeMissions")}),_.jsx("p",{className:"text-3xl font-bold text-success",children:"89%"}),_.jsx("p",{className:"text-xs text-success",children:t("status.excellentStatus")})]}),_.jsx("div",{className:"h-10 w-10 text-success",children:"🎯"})]})}),_.jsx("div",{className:"bg-card p-6 rounded-lg border border-border border-l-4 border-l-warning",children:_.jsxs("div",{className:"flex items-center justify-between",children:[_.jsxs("div",{children:[_.jsx("p",{className:"text-sm font-medium text-muted-foreground",children:t("dashboard.threatAlerts")}),_.jsx("p",{className:"text-3xl font-bold text-warning",children:"3"}),_.jsx("p",{className:"text-xs text-muted-foreground",children:t("status.mediumPriority")})]}),_.jsx("div",{className:"h-10 w-10 text-warning",children:"⚠️"})]})}),_.jsx("div",{className:"bg-card p-6 rounded-lg border border-border border-l-4 border-l-primary",children:_.jsxs("div",{className:"flex items-center justify-between",children:[_.jsxs("div",{children:[_.jsx("p",{className:"text-sm font-medium text-muted-foreground",children:t("dashboard.systemHealth")}),_.jsx("p",{className:"text-3xl font-bold text-primary",children:"98.2%"}),_.jsx("p",{className:"text-xs text-success",children:t("status.aboveTarget")})]}),_.jsx("div",{className:"h-10 w-10 text-primary",children:"📈"})]})})]}),_.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[_.jsxs("div",{className:"bg-card p-6 rounded-lg border border-border",children:[_.jsxs("h3",{className:"text-lg font-semibold text-card-foreground mb-4 flex items-center gap-2",children:["📊 ",t("dashboard.systemOverview")]}),_.jsx("div",{className:"h-64",children:_.jsx(Rm,{data:e,options:n})})]}),_.jsxs("div",{className:"bg-card p-6 rounded-lg border border-border",children:[_.jsxs("h3",{className:"text-lg font-semibold text-card-foreground mb-4 flex items-center gap-2",children:["⚡ ",t("dashboard.quickActions")]}),_.jsxs("div",{className:"space-y-3",children:[_.jsxs("button",{className:"w-full p-3 text-left bg-secondary hover:bg-secondary/80 rounded-lg transition-colors flex items-center gap-2",children:["📊 ",t("dashboard.systemHealthCheck")]}),_.jsxs("button",{className:"w-full p-3 text-left bg-secondary hover:bg-secondary/80 rounded-lg transition-colors flex items-center gap-2",children:["🎯 ",t("dashboard.missionPlanning")]}),_.jsxs("button",{className:"w-full p-3 text-left bg-secondary hover:bg-secondary/80 rounded-lg transition-colors flex items-center gap-2",children:["🛡️ ",t("dashboard.securityAudit")]}),_.jsxs("button",{className:"w-full p-3 text-left bg-secondary hover:bg-secondary/80 rounded-lg transition-colors flex items-center gap-2",children:["📈 ",t("dashboard.generateReport")]})]})]})]})]})})};Wn.register(es,aa,oa);const lb=()=>{const{t}=nr(),e={labels:[t("common.operational"),t("common.maintenance")],datasets:[{data:[95,5],backgroundColor:["hsl(var(--success) / 0.8)","hsl(var(--destructive) / 0.8)"],borderColor:["hsl(var(--success))","hsl(var(--destructive))"],borderWidth:2}]},n={responsive:!0,maintainAspectRatio:!1,plugins:{legend:{position:"right"},title:{display:!0,text:t("equipment.title")}}};return _.jsx("div",{className:"p-6",children:_.jsxs("div",{className:"space-y-6",children:[_.jsxs("div",{className:"flex items-center justify-between",children:[_.jsxs("div",{children:[_.jsx("h1",{className:"text-3xl font-bold text-foreground",children:t("equipment.title")}),_.jsx("p",{className:"text-muted-foreground",children:t("equipment.subtitle")})]}),_.jsx("div",{className:"bg-success text-success-foreground px-3 py-1 rounded text-sm font-medium",children:t("status.operational")})]}),_.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[_.jsxs("div",{className:"bg-card p-6 rounded-lg border border-border",children:[_.jsxs("h3",{className:"text-lg font-semibold text-card-foreground mb-4 flex items-center gap-2",children:["⚙️ ",t("equipment.distribution")]}),_.jsx("div",{className:"h-64",children:_.jsx(ob,{data:e,options:n})})]}),_.jsxs("div",{className:"bg-card p-6 rounded-lg border border-border",children:[_.jsxs("h3",{className:"text-lg font-semibold text-card-foreground mb-4 flex items-center gap-2",children:["📊 ",t("equipment.overview")]}),_.jsxs("div",{className:"space-y-4",children:[_.jsxs("div",{className:"flex items-center justify-between p-3 bg-success/10 rounded-lg",children:[_.jsx("span",{className:"font-medium",children:t("equipment.operationalEquipment")}),_.jsxs("div",{className:"flex items-center gap-2",children:[_.jsx("span",{className:"text-2xl font-bold text-success",children:"95%"}),_.jsx("div",{className:"w-3 h-3 bg-success rounded-full"})]})]}),_.jsxs("div",{className:"flex items-center justify-between p-3 bg-destructive/10 rounded-lg",children:[_.jsx("span",{className:"font-medium",children:t("equipment.downForMaintenance")}),_.jsxs("div",{className:"flex items-center gap-2",children:[_.jsx("span",{className:"text-2xl font-bold text-destructive",children:"5%"}),_.jsx("div",{className:"w-3 h-3 bg-destructive rounded-full"})]})]}),_.jsxs("div",{className:"flex items-center justify-between p-3 bg-primary/10 rounded-lg",children:[_.jsx("span",{className:"font-medium",children:t("equipment.totalUnits")}),_.jsxs("div",{className:"flex items-center gap-2",children:[_.jsx("span",{className:"text-2xl font-bold text-primary",children:"247"}),_.jsx("div",{className:"w-3 h-3 bg-primary rounded-full"})]})]})]})]})]})]})})};Wn.register(ts,Zr,Ks,Us,aa,oa);const ub=()=>{const t={labels:["Week 1","Week 2","Week 3","Week 4"],datasets:[{label:"Mission Progress (%)",data:[70,80,85,90],borderColor:"hsl(var(--accent))",backgroundColor:"hsl(var(--accent) / 0.2)",tension:.4,fill:!0,pointBackgroundColor:"hsl(var(--accent))",pointBorderColor:"hsl(var(--accent))",pointRadius:6}]},e={responsive:!0,maintainAspectRatio:!1,plugins:{legend:{position:"top"},title:{display:!0,text:"Mission Progress Tracking"}},scales:{y:{beginAtZero:!0,max:100}}};return _.jsx("div",{className:"p-6",children:_.jsxs("div",{className:"space-y-6",children:[_.jsxs("div",{className:"flex items-center justify-between",children:[_.jsxs("div",{children:[_.jsx("h1",{className:"text-3xl font-bold text-foreground",children:"Mission Statistics"}),_.jsx("p",{className:"text-muted-foreground",children:"Progress tracking and mission analytics"})]}),_.jsx("div",{className:"bg-warning text-warning-foreground px-3 py-1 rounded text-sm font-medium",children:"IN PROGRESS"})]}),_.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[_.jsxs("div",{className:"lg:col-span-2 bg-card p-6 rounded-lg border border-border",children:[_.jsx("h3",{className:"text-lg font-semibold text-card-foreground mb-4 flex items-center gap-2",children:"📈 Mission Progress Trend"}),_.jsx("div",{className:"h-64",children:_.jsx(rb,{data:t,options:e})})]}),_.jsxs("div",{className:"bg-card p-6 rounded-lg border border-border",children:[_.jsx("h3",{className:"text-lg font-semibold text-card-foreground mb-4 flex items-center gap-2",children:"🎯 Mission Metrics"}),_.jsxs("div",{className:"space-y-4",children:[_.jsxs("div",{className:"p-3 bg-primary/10 rounded-lg",children:[_.jsx("div",{className:"text-sm text-muted-foreground",children:"Active Missions"}),_.jsx("div",{className:"text-2xl font-bold text-primary",children:"12"})]}),_.jsxs("div",{className:"p-3 bg-success/10 rounded-lg",children:[_.jsx("div",{className:"text-sm text-muted-foreground",children:"Success Rate"}),_.jsx("div",{className:"text-2xl font-bold text-success",children:"90%"})]}),_.jsxs("div",{className:"p-3 bg-warning/10 rounded-lg",children:[_.jsx("div",{className:"text-sm text-muted-foreground",children:"In Progress"}),_.jsx("div",{className:"text-2xl font-bold text-warning",children:"8"})]}),_.jsxs("div",{className:"p-3 bg-accent/10 rounded-lg",children:[_.jsx("div",{className:"text-sm text-muted-foreground",children:"Completed"}),_.jsx("div",{className:"text-2xl font-bold text-accent",children:"4"})]})]})]})]})]})})};Wn.register(Us,Ks,ms,Em,aa,oa);const cb=()=>{const t={labels:["BrahMos Test","Agni-V Test","Prithvi Test"],datasets:[{label:"Accuracy (%)",data:[90,85,95],backgroundColor:"hsl(var(--primary) / 0.8)",borderColor:"hsl(var(--primary))",borderWidth:2}]},e={responsive:!0,maintainAspectRatio:!1,plugins:{legend:{position:"top"},title:{display:!0,text:"Simulation Accuracy Results"}},scales:{y:{beginAtZero:!0,max:100}}};return _.jsx("div",{className:"p-6",children:_.jsxs("div",{className:"space-y-6",children:[_.jsxs("div",{className:"flex items-center justify-between",children:[_.jsxs("div",{children:[_.jsx("h1",{className:"text-3xl font-bold text-foreground",children:"Simulation Results"}),_.jsx("p",{className:"text-muted-foreground",children:"3D trajectory analysis and accuracy testing"})]}),_.jsx("div",{className:"bg-primary text-primary-foreground px-3 py-1 rounded text-sm font-medium",children:"COMPLETED"})]}),_.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[_.jsxs("div",{className:"bg-card p-6 rounded-lg border border-border",children:[_.jsx("h3",{className:"text-lg font-semibold text-card-foreground mb-4 flex items-center gap-2",children:"🎯 Accuracy Comparison"}),_.jsx("div",{className:"h-64",children:_.jsx(Rm,{data:t,options:e})})]}),_.jsxs("div",{className:"bg-card p-6 rounded-lg border border-border",children:[_.jsx("h3",{className:"text-lg font-semibold text-card-foreground mb-4 flex items-center gap-2",children:"📊 Test Results"}),_.jsxs("div",{className:"space-y-4",children:[_.jsx("div",{className:"p-4 bg-primary/10 rounded-lg border-l-4 border-l-primary",children:_.jsxs("div",{className:"flex justify-between items-center",children:[_.jsxs("div",{children:[_.jsx("div",{className:"font-medium",children:"BrahMos Test"}),_.jsx("div",{className:"text-sm text-muted-foreground",children:"Supersonic cruise missile"})]}),_.jsx("div",{className:"text-2xl font-bold text-primary",children:"90%"})]})}),_.jsx("div",{className:"p-4 bg-warning/10 rounded-lg border-l-4 border-l-warning",children:_.jsxs("div",{className:"flex justify-between items-center",children:[_.jsxs("div",{children:[_.jsx("div",{className:"font-medium",children:"Agni-V Test"}),_.jsx("div",{className:"text-sm text-muted-foreground",children:"Intercontinental ballistic missile"})]}),_.jsx("div",{className:"text-2xl font-bold text-warning",children:"85%"})]})}),_.jsx("div",{className:"p-4 bg-success/10 rounded-lg border-l-4 border-l-success",children:_.jsxs("div",{className:"flex justify-between items-center",children:[_.jsxs("div",{children:[_.jsx("div",{className:"font-medium",children:"Prithvi Test"}),_.jsx("div",{className:"text-sm text-muted-foreground",children:"Surface-to-surface missile"})]}),_.jsx("div",{className:"text-2xl font-bold text-success",children:"95%"})]})})]})]})]})]})})},db=()=>{const[t,e]=O.useState("drdo-formal");O.useEffect(()=>{document.body.className=`bg-${t==="drdo-formal"?"drdo-white":t}-theme text-drdo-navy`},[t]);const n=i=>{e(i.target.value)};return _.jsx("div",{className:"p-6",children:_.jsxs("div",{className:"space-y-6",children:[_.jsxs("div",{className:"flex items-center justify-between",children:[_.jsxs("div",{children:[_.jsx("h1",{className:"text-3xl font-bold text-foreground",children:"System Configuration"}),_.jsx("p",{className:"text-muted-foreground",children:"Dashboard settings and preferences"})]}),_.jsx("div",{className:"bg-accent text-accent-foreground px-3 py-1 rounded text-sm font-medium",children:"ADMIN ACCESS"})]}),_.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[_.jsxs("div",{className:"bg-card p-6 rounded-lg border border-border",children:[_.jsx("h3",{className:"text-lg font-semibold text-card-foreground mb-4 flex items-center gap-2",children:"🎨 Theme Configuration"}),_.jsxs("div",{className:"space-y-4",children:[_.jsxs("div",{children:[_.jsx("label",{className:"block text-sm font-medium mb-2",children:"Dashboard Theme"}),_.jsxs("select",{value:t,onChange:n,className:"w-full p-3 border border-border rounded-lg bg-background text-foreground focus:ring-2 focus:ring-primary focus:border-transparent",children:[_.jsx("option",{value:"drdo-formal",children:"DRDO Formal (Default)"}),_.jsx("option",{value:"light",children:"Light Mode"}),_.jsx("option",{value:"dark",children:"Dark Mode"}),_.jsx("option",{value:"tactical",children:"Tactical Mode"})]})]}),_.jsxs("div",{className:"p-4 bg-muted rounded-lg",children:[_.jsxs("div",{className:"text-sm text-muted-foreground",children:["Current theme: ",_.jsx("span",{className:"font-medium text-foreground",children:t})]}),_.jsx("div",{className:"text-xs text-muted-foreground mt-1",children:"Changes apply immediately across all modules"})]})]})]}),_.jsxs("div",{className:"bg-card p-6 rounded-lg border border-border",children:[_.jsx("h3",{className:"text-lg font-semibold text-card-foreground mb-4 flex items-center gap-2",children:"🔒 Security Settings"}),_.jsxs("div",{className:"space-y-4",children:[_.jsxs("div",{className:"p-4 bg-success/10 rounded-lg border border-success/20",children:[_.jsxs("div",{className:"flex items-center gap-2 mb-2",children:[_.jsx("div",{className:"w-3 h-3 bg-success rounded-full"}),_.jsx("span",{className:"font-medium text-success",children:"AES-256 Encryption"})]}),_.jsx("div",{className:"text-sm text-muted-foreground",children:"All data transmissions encrypted"})]}),_.jsxs("div",{className:"p-4 bg-primary/10 rounded-lg border border-primary/20",children:[_.jsxs("div",{className:"flex items-center gap-2 mb-2",children:[_.jsx("div",{className:"w-3 h-3 bg-primary rounded-full"}),_.jsx("span",{className:"font-medium text-primary",children:"Session Timeout"})]}),_.jsx("div",{className:"text-sm text-muted-foreground",children:"15 minutes idle timeout"})]}),_.jsxs("div",{className:"p-4 bg-warning/10 rounded-lg border border-warning/20",children:[_.jsxs("div",{className:"flex items-center gap-2 mb-2",children:[_.jsx("div",{className:"w-3 h-3 bg-warning rounded-full"}),_.jsx("span",{className:"font-medium text-warning",children:"Audit Logging"})]}),_.jsx("div",{className:"text-sm text-muted-foreground",children:"All actions logged and monitored"})]})]})]})]})]})})},fb=()=>_.jsx(h0,{children:_.jsxs("div",{className:"flex h-screen bg-drdo-white text-drdo-navy font-inter overflow-hidden",children:[_.jsx(z0,{}),_.jsxs("div",{className:"flex-1 ml-60",children:[_.jsx(F0,{}),_.jsxs("main",{className:"p-4 overflow-auto h-[calc(100vh-60px)] relative",children:[_.jsx("div",{className:"liquid-effect"}),_.jsxs(r0,{children:[_.jsx(Gn,{path:"/",element:_.jsx(ab,{})}),_.jsx(Gn,{path:"/equipment-status",element:_.jsx(lb,{})}),_.jsx(Gn,{path:"/mission-statistics",element:_.jsx(ub,{})}),_.jsx(Gn,{path:"/simulation-results",element:_.jsx(cb,{})}),_.jsx(Gn,{path:"/settings",element:_.jsx(db,{})})]})]})]})]})}),A=t=>typeof t=="string",Ki=()=>{let t,e;const n=new Promise((i,s)=>{t=i,e=s});return n.resolve=t,n.reject=e,n},Gf=t=>t==null?"":""+t,hb=(t,e,n)=>{t.forEach(i=>{e[i]&&(n[i]=e[i])})},pb=/###/g,Jf=t=>t&&t.indexOf("###")>-1?t.replace(pb,"."):t,Zf=t=>!t||A(t),ys=(t,e,n)=>{const i=A(e)?e.split("."):e;let s=0;for(;s<i.length-1;){if(Zf(t))return{};const r=Jf(i[s]);!t[r]&&n&&(t[r]=new n),Object.prototype.hasOwnProperty.call(t,r)?t=t[r]:t={},++s}return Zf(t)?{}:{obj:t,k:Jf(i[s])}},eh=(t,e,n)=>{const{obj:i,k:s}=ys(t,e,Object);if(i!==void 0||e.length===1){i[s]=n;return}let r=e[e.length-1],o=e.slice(0,e.length-1),a=ys(t,o,Object);for(;a.obj===void 0&&o.length;)r=`${o[o.length-1]}.${r}`,o=o.slice(0,o.length-1),a=ys(t,o,Object),a!=null&&a.obj&&typeof a.obj[`${a.k}.${r}`]<"u"&&(a.obj=void 0);a.obj[`${a.k}.${r}`]=n},gb=(t,e,n,i)=>{const{obj:s,k:r}=ys(t,e,Object);s[r]=s[r]||[],s[r].push(n)},jo=(t,e)=>{const{obj:n,k:i}=ys(t,e);if(n&&Object.prototype.hasOwnProperty.call(n,i))return n[i]},mb=(t,e,n)=>{const i=jo(t,n);return i!==void 0?i:jo(e,n)},Dm=(t,e,n)=>{for(const i in e)i!=="__proto__"&&i!=="constructor"&&(i in t?A(t[i])||t[i]instanceof String||A(e[i])||e[i]instanceof String?n&&(t[i]=e[i]):Dm(t[i],e[i],n):t[i]=e[i]);return t},Xn=t=>t.replace(/[\-\[\]\/\{\}\(\)\*\+\?\.\\\^\$\|]/g,"\\$&");var yb={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;","/":"&#x2F;"};const xb=t=>A(t)?t.replace(/[&<>"'\/]/g,e=>yb[e]):t;class vb{constructor(e){this.capacity=e,this.regExpMap=new Map,this.regExpQueue=[]}getRegExp(e){const n=this.regExpMap.get(e);if(n!==void 0)return n;const i=new RegExp(e);return this.regExpQueue.length===this.capacity&&this.regExpMap.delete(this.regExpQueue.shift()),this.regExpMap.set(e,i),this.regExpQueue.push(e),i}}const _b=[" ",",","?","!",";"],wb=new vb(20),Sb=(t,e,n)=>{e=e||"",n=n||"";const i=_b.filter(o=>e.indexOf(o)<0&&n.indexOf(o)<0);if(i.length===0)return!0;const s=wb.getRegExp(`(${i.map(o=>o==="?"?"\\?":o).join("|")})`);let r=!s.test(t);if(!r){const o=t.indexOf(n);o>0&&!s.test(t.substring(0,o))&&(r=!0)}return r},ru=(t,e,n=".")=>{if(!t)return;if(t[e])return Object.prototype.hasOwnProperty.call(t,e)?t[e]:void 0;const i=e.split(n);let s=t;for(let r=0;r<i.length;){if(!s||typeof s!="object")return;let o,a="";for(let l=r;l<i.length;++l)if(l!==r&&(a+=n),a+=i[l],o=s[a],o!==void 0){if(["string","number","boolean"].indexOf(typeof o)>-1&&l<i.length-1)continue;r+=l-r+1;break}s=o}return s},Ys=t=>t==null?void 0:t.replace("_","-"),bb={type:"logger",log(t){this.output("log",t)},warn(t){this.output("warn",t)},error(t){this.output("error",t)},output(t,e){var n,i;(i=(n=console==null?void 0:console[t])==null?void 0:n.apply)==null||i.call(n,console,e)}};class Ao{constructor(e,n={}){this.init(e,n)}init(e,n={}){this.prefix=n.prefix||"i18next:",this.logger=e||bb,this.options=n,this.debug=n.debug}log(...e){return this.forward(e,"log","",!0)}warn(...e){return this.forward(e,"warn","",!0)}error(...e){return this.forward(e,"error","")}deprecate(...e){return this.forward(e,"warn","WARNING DEPRECATED: ",!0)}forward(e,n,i,s){return s&&!this.debug?null:(A(e[0])&&(e[0]=`${i}${this.prefix} ${e[0]}`),this.logger[n](e))}create(e){return new Ao(this.logger,{prefix:`${this.prefix}:${e}:`,...this.options})}clone(e){return e=e||this.options,e.prefix=e.prefix||this.prefix,new Ao(this.logger,e)}}var St=new Ao;class ua{constructor(){this.observers={}}on(e,n){return e.split(" ").forEach(i=>{this.observers[i]||(this.observers[i]=new Map);const s=this.observers[i].get(n)||0;this.observers[i].set(n,s+1)}),this}off(e,n){if(this.observers[e]){if(!n){delete this.observers[e];return}this.observers[e].delete(n)}}emit(e,...n){this.observers[e]&&Array.from(this.observers[e].entries()).forEach(([s,r])=>{for(let o=0;o<r;o++)s(...n)}),this.observers["*"]&&Array.from(this.observers["*"].entries()).forEach(([s,r])=>{for(let o=0;o<r;o++)s.apply(s,[e,...n])})}}class th extends ua{constructor(e,n={ns:["translation"],defaultNS:"translation"}){super(),this.data=e||{},this.options=n,this.options.keySeparator===void 0&&(this.options.keySeparator="."),this.options.ignoreJSONStructure===void 0&&(this.options.ignoreJSONStructure=!0)}addNamespaces(e){this.options.ns.indexOf(e)<0&&this.options.ns.push(e)}removeNamespaces(e){const n=this.options.ns.indexOf(e);n>-1&&this.options.ns.splice(n,1)}getResource(e,n,i,s={}){var u,c;const r=s.keySeparator!==void 0?s.keySeparator:this.options.keySeparator,o=s.ignoreJSONStructure!==void 0?s.ignoreJSONStructure:this.options.ignoreJSONStructure;let a;e.indexOf(".")>-1?a=e.split("."):(a=[e,n],i&&(Array.isArray(i)?a.push(...i):A(i)&&r?a.push(...i.split(r)):a.push(i)));const l=jo(this.data,a);return!l&&!n&&!i&&e.indexOf(".")>-1&&(e=a[0],n=a[1],i=a.slice(2).join(".")),l||!o||!A(i)?l:ru((c=(u=this.data)==null?void 0:u[e])==null?void 0:c[n],i,r)}addResource(e,n,i,s,r={silent:!1}){const o=r.keySeparator!==void 0?r.keySeparator:this.options.keySeparator;let a=[e,n];i&&(a=a.concat(o?i.split(o):i)),e.indexOf(".")>-1&&(a=e.split("."),s=n,n=a[1]),this.addNamespaces(n),eh(this.data,a,s),r.silent||this.emit("added",e,n,i,s)}addResources(e,n,i,s={silent:!1}){for(const r in i)(A(i[r])||Array.isArray(i[r]))&&this.addResource(e,n,r,i[r],{silent:!0});s.silent||this.emit("added",e,n,i)}addResourceBundle(e,n,i,s,r,o={silent:!1,skipCopy:!1}){let a=[e,n];e.indexOf(".")>-1&&(a=e.split("."),s=i,i=n,n=a[1]),this.addNamespaces(n);let l=jo(this.data,a)||{};o.skipCopy||(i=JSON.parse(JSON.stringify(i))),s?Dm(l,i,r):l={...l,...i},eh(this.data,a,l),o.silent||this.emit("added",e,n,i)}removeResourceBundle(e,n){this.hasResourceBundle(e,n)&&delete this.data[e][n],this.removeNamespaces(n),this.emit("removed",e,n)}hasResourceBundle(e,n){return this.getResource(e,n)!==void 0}getResourceBundle(e,n){return n||(n=this.options.defaultNS),this.getResource(e,n)}getDataByLanguage(e){return this.data[e]}hasLanguageSomeTranslations(e){const n=this.getDataByLanguage(e);return!!(n&&Object.keys(n)||[]).find(s=>n[s]&&Object.keys(n[s]).length>0)}toJSON(){return this.data}}var jm={processors:{},addPostProcessor(t){this.processors[t.name]=t},handle(t,e,n,i,s){return t.forEach(r=>{var o;e=((o=this.processors[r])==null?void 0:o.process(e,n,i,s))??e}),e}};const nh={},ih=t=>!A(t)&&typeof t!="boolean"&&typeof t!="number";class Io extends ua{constructor(e,n={}){super(),hb(["resourceStore","languageUtils","pluralResolver","interpolator","backendConnector","i18nFormat","utils"],e,this),this.options=n,this.options.keySeparator===void 0&&(this.options.keySeparator="."),this.logger=St.create("translator")}changeLanguage(e){e&&(this.language=e)}exists(e,n={interpolation:{}}){const i={...n};if(e==null)return!1;const s=this.resolve(e,i);return(s==null?void 0:s.res)!==void 0}extractFromKey(e,n){let i=n.nsSeparator!==void 0?n.nsSeparator:this.options.nsSeparator;i===void 0&&(i=":");const s=n.keySeparator!==void 0?n.keySeparator:this.options.keySeparator;let r=n.ns||this.options.defaultNS||[];const o=i&&e.indexOf(i)>-1,a=!this.options.userDefinedKeySeparator&&!n.keySeparator&&!this.options.userDefinedNsSeparator&&!n.nsSeparator&&!Sb(e,i,s);if(o&&!a){const l=e.match(this.interpolator.nestingRegexp);if(l&&l.length>0)return{key:e,namespaces:A(r)?[r]:r};const u=e.split(i);(i!==s||i===s&&this.options.ns.indexOf(u[0])>-1)&&(r=u.shift()),e=u.join(s)}return{key:e,namespaces:A(r)?[r]:r}}translate(e,n,i){let s=typeof n=="object"?{...n}:n;if(typeof s!="object"&&this.options.overloadTranslationOptionHandler&&(s=this.options.overloadTranslationOptionHandler(arguments)),typeof options=="object"&&(s={...s}),s||(s={}),e==null)return"";Array.isArray(e)||(e=[String(e)]);const r=s.returnDetails!==void 0?s.returnDetails:this.options.returnDetails,o=s.keySeparator!==void 0?s.keySeparator:this.options.keySeparator,{key:a,namespaces:l}=this.extractFromKey(e[e.length-1],s),u=l[l.length-1];let c=s.nsSeparator!==void 0?s.nsSeparator:this.options.nsSeparator;c===void 0&&(c=":");const d=s.lng||this.language,f=s.appendNamespaceToCIMode||this.options.appendNamespaceToCIMode;if((d==null?void 0:d.toLowerCase())==="cimode")return f?r?{res:`${u}${c}${a}`,usedKey:a,exactUsedKey:a,usedLng:d,usedNS:u,usedParams:this.getUsedParamsDetails(s)}:`${u}${c}${a}`:r?{res:a,usedKey:a,exactUsedKey:a,usedLng:d,usedNS:u,usedParams:this.getUsedParamsDetails(s)}:a;const h=this.resolve(e,s);let p=h==null?void 0:h.res;const y=(h==null?void 0:h.usedKey)||a,v=(h==null?void 0:h.exactUsedKey)||a,m=["[object Number]","[object Function]","[object RegExp]"],g=s.joinArrays!==void 0?s.joinArrays:this.options.joinArrays,x=!this.i18nFormat||this.i18nFormat.handleAsObject,w=s.count!==void 0&&!A(s.count),S=Io.hasDefaultValue(s),C=w?this.pluralResolver.getSuffix(d,s.count,s):"",k=s.ordinal&&w?this.pluralResolver.getSuffix(d,s.count,{ordinal:!1}):"",b=w&&!s.ordinal&&s.count===0,M=b&&s[`defaultValue${this.options.pluralSeparator}zero`]||s[`defaultValue${C}`]||s[`defaultValue${k}`]||s.defaultValue;let P=p;x&&!p&&S&&(P=M);const T=ih(P),I=Object.prototype.toString.apply(P);if(x&&P&&T&&m.indexOf(I)<0&&!(A(g)&&Array.isArray(P))){if(!s.returnObjects&&!this.options.returnObjects){this.options.returnedObjectHandler||this.logger.warn("accessing an object - but returnObjects options is not enabled!");const H=this.options.returnedObjectHandler?this.options.returnedObjectHandler(y,P,{...s,ns:l}):`key '${a} (${this.language})' returned an object instead of string.`;return r?(h.res=H,h.usedParams=this.getUsedParamsDetails(s),h):H}if(o){const H=Array.isArray(P),J=H?[]:{},W=H?v:y;for(const z in P)if(Object.prototype.hasOwnProperty.call(P,z)){const V=`${W}${o}${z}`;S&&!p?J[z]=this.translate(V,{...s,defaultValue:ih(M)?M[z]:void 0,joinArrays:!1,ns:l}):J[z]=this.translate(V,{...s,joinArrays:!1,ns:l}),J[z]===V&&(J[z]=P[z])}p=J}}else if(x&&A(g)&&Array.isArray(p))p=p.join(g),p&&(p=this.extendTranslation(p,e,s,i));else{let H=!1,J=!1;!this.isValidLookup(p)&&S&&(H=!0,p=M),this.isValidLookup(p)||(J=!0,p=a);const z=(s.missingKeyNoValueFallbackToKey||this.options.missingKeyNoValueFallbackToKey)&&J?void 0:p,V=S&&M!==p&&this.options.updateMissing;if(J||H||V){if(this.logger.log(V?"updateKey":"missingKey",d,u,a,V?M:p),o){const F=this.resolve(a,{...s,keySeparator:!1});F&&F.res&&this.logger.warn("Seems the loaded translations were in flat JSON format instead of nested. Either set keySeparator: false on init or make sure your translations are published in nested format.")}let N=[];const R=this.languageUtils.getFallbackCodes(this.options.fallbackLng,s.lng||this.language);if(this.options.saveMissingTo==="fallback"&&R&&R[0])for(let F=0;F<R.length;F++)N.push(R[F]);else this.options.saveMissingTo==="all"?N=this.languageUtils.toResolveHierarchy(s.lng||this.language):N.push(s.lng||this.language);const j=(F,U,be)=>{var qe;const ye=S&&be!==p?be:z;this.options.missingKeyHandler?this.options.missingKeyHandler(F,u,U,ye,V,s):(qe=this.backendConnector)!=null&&qe.saveMissing&&this.backendConnector.saveMissing(F,u,U,ye,V,s),this.emit("missingKey",F,u,U,p)};this.options.saveMissing&&(this.options.saveMissingPlurals&&w?N.forEach(F=>{const U=this.pluralResolver.getSuffixes(F,s);b&&s[`defaultValue${this.options.pluralSeparator}zero`]&&U.indexOf(`${this.options.pluralSeparator}zero`)<0&&U.push(`${this.options.pluralSeparator}zero`),U.forEach(be=>{j([F],a+be,s[`defaultValue${be}`]||M)})}):j(N,a,M))}p=this.extendTranslation(p,e,s,h,i),J&&p===a&&this.options.appendNamespaceToMissingKey&&(p=`${u}${c}${a}`),(J||H)&&this.options.parseMissingKeyHandler&&(p=this.options.parseMissingKeyHandler(this.options.appendNamespaceToMissingKey?`${u}${c}${a}`:a,H?p:void 0,s))}return r?(h.res=p,h.usedParams=this.getUsedParamsDetails(s),h):p}extendTranslation(e,n,i,s,r){var l,u;if((l=this.i18nFormat)!=null&&l.parse)e=this.i18nFormat.parse(e,{...this.options.interpolation.defaultVariables,...i},i.lng||this.language||s.usedLng,s.usedNS,s.usedKey,{resolved:s});else if(!i.skipInterpolation){i.interpolation&&this.interpolator.init({...i,interpolation:{...this.options.interpolation,...i.interpolation}});const c=A(e)&&(((u=i==null?void 0:i.interpolation)==null?void 0:u.skipOnVariables)!==void 0?i.interpolation.skipOnVariables:this.options.interpolation.skipOnVariables);let d;if(c){const h=e.match(this.interpolator.nestingRegexp);d=h&&h.length}let f=i.replace&&!A(i.replace)?i.replace:i;if(this.options.interpolation.defaultVariables&&(f={...this.options.interpolation.defaultVariables,...f}),e=this.interpolator.interpolate(e,f,i.lng||this.language||s.usedLng,i),c){const h=e.match(this.interpolator.nestingRegexp),p=h&&h.length;d<p&&(i.nest=!1)}!i.lng&&s&&s.res&&(i.lng=this.language||s.usedLng),i.nest!==!1&&(e=this.interpolator.nest(e,(...h)=>(r==null?void 0:r[0])===h[0]&&!i.context?(this.logger.warn(`It seems you are nesting recursively key: ${h[0]} in key: ${n[0]}`),null):this.translate(...h,n),i)),i.interpolation&&this.interpolator.reset()}const o=i.postProcess||this.options.postProcess,a=A(o)?[o]:o;return e!=null&&(a!=null&&a.length)&&i.applyPostProcessor!==!1&&(e=jm.handle(a,e,n,this.options&&this.options.postProcessPassResolved?{i18nResolved:{...s,usedParams:this.getUsedParamsDetails(i)},...i}:i,this)),e}resolve(e,n={}){let i,s,r,o,a;return A(e)&&(e=[e]),e.forEach(l=>{if(this.isValidLookup(i))return;const u=this.extractFromKey(l,n),c=u.key;s=c;let d=u.namespaces;this.options.fallbackNS&&(d=d.concat(this.options.fallbackNS));const f=n.count!==void 0&&!A(n.count),h=f&&!n.ordinal&&n.count===0,p=n.context!==void 0&&(A(n.context)||typeof n.context=="number")&&n.context!=="",y=n.lngs?n.lngs:this.languageUtils.toResolveHierarchy(n.lng||this.language,n.fallbackLng);d.forEach(v=>{var m,g;this.isValidLookup(i)||(a=v,!nh[`${y[0]}-${v}`]&&((m=this.utils)!=null&&m.hasLoadedNamespace)&&!((g=this.utils)!=null&&g.hasLoadedNamespace(a))&&(nh[`${y[0]}-${v}`]=!0,this.logger.warn(`key "${s}" for languages "${y.join(", ")}" won't get resolved as namespace "${a}" was not yet loaded`,"This means something IS WRONG in your setup. You access the t function before i18next.init / i18next.loadNamespace / i18next.changeLanguage was done. Wait for the callback or Promise to resolve before accessing it!!!")),y.forEach(x=>{var C;if(this.isValidLookup(i))return;o=x;const w=[c];if((C=this.i18nFormat)!=null&&C.addLookupKeys)this.i18nFormat.addLookupKeys(w,c,x,v,n);else{let k;f&&(k=this.pluralResolver.getSuffix(x,n.count,n));const b=`${this.options.pluralSeparator}zero`,M=`${this.options.pluralSeparator}ordinal${this.options.pluralSeparator}`;if(f&&(w.push(c+k),n.ordinal&&k.indexOf(M)===0&&w.push(c+k.replace(M,this.options.pluralSeparator)),h&&w.push(c+b)),p){const P=`${c}${this.options.contextSeparator}${n.context}`;w.push(P),f&&(w.push(P+k),n.ordinal&&k.indexOf(M)===0&&w.push(P+k.replace(M,this.options.pluralSeparator)),h&&w.push(P+b))}}let S;for(;S=w.pop();)this.isValidLookup(i)||(r=S,i=this.getResource(x,v,S,n))}))})}),{res:i,usedKey:s,exactUsedKey:r,usedLng:o,usedNS:a}}isValidLookup(e){return e!==void 0&&!(!this.options.returnNull&&e===null)&&!(!this.options.returnEmptyString&&e==="")}getResource(e,n,i,s={}){var r;return(r=this.i18nFormat)!=null&&r.getResource?this.i18nFormat.getResource(e,n,i,s):this.resourceStore.getResource(e,n,i,s)}getUsedParamsDetails(e={}){const n=["defaultValue","ordinal","context","replace","lng","lngs","fallbackLng","ns","keySeparator","nsSeparator","returnObjects","returnDetails","joinArrays","postProcess","interpolation"],i=e.replace&&!A(e.replace);let s=i?e.replace:e;if(i&&typeof e.count<"u"&&(s.count=e.count),this.options.interpolation.defaultVariables&&(s={...this.options.interpolation.defaultVariables,...s}),!i){s={...s};for(const r of n)delete s[r]}return s}static hasDefaultValue(e){const n="defaultValue";for(const i in e)if(Object.prototype.hasOwnProperty.call(e,i)&&n===i.substring(0,n.length)&&e[i]!==void 0)return!0;return!1}}class sh{constructor(e){this.options=e,this.supportedLngs=this.options.supportedLngs||!1,this.logger=St.create("languageUtils")}getScriptPartFromCode(e){if(e=Ys(e),!e||e.indexOf("-")<0)return null;const n=e.split("-");return n.length===2||(n.pop(),n[n.length-1].toLowerCase()==="x")?null:this.formatLanguageCode(n.join("-"))}getLanguagePartFromCode(e){if(e=Ys(e),!e||e.indexOf("-")<0)return e;const n=e.split("-");return this.formatLanguageCode(n[0])}formatLanguageCode(e){if(A(e)&&e.indexOf("-")>-1){let n;try{n=Intl.getCanonicalLocales(e)[0]}catch{}return n&&this.options.lowerCaseLng&&(n=n.toLowerCase()),n||(this.options.lowerCaseLng?e.toLowerCase():e)}return this.options.cleanCode||this.options.lowerCaseLng?e.toLowerCase():e}isSupportedCode(e){return(this.options.load==="languageOnly"||this.options.nonExplicitSupportedLngs)&&(e=this.getLanguagePartFromCode(e)),!this.supportedLngs||!this.supportedLngs.length||this.supportedLngs.indexOf(e)>-1}getBestMatchFromCodes(e){if(!e)return null;let n;return e.forEach(i=>{if(n)return;const s=this.formatLanguageCode(i);(!this.options.supportedLngs||this.isSupportedCode(s))&&(n=s)}),!n&&this.options.supportedLngs&&e.forEach(i=>{if(n)return;const s=this.getScriptPartFromCode(i);if(this.isSupportedCode(s))return n=s;const r=this.getLanguagePartFromCode(i);if(this.isSupportedCode(r))return n=r;n=this.options.supportedLngs.find(o=>{if(o===r)return o;if(!(o.indexOf("-")<0&&r.indexOf("-")<0)&&(o.indexOf("-")>0&&r.indexOf("-")<0&&o.substring(0,o.indexOf("-"))===r||o.indexOf(r)===0&&r.length>1))return o})}),n||(n=this.getFallbackCodes(this.options.fallbackLng)[0]),n}getFallbackCodes(e,n){if(!e)return[];if(typeof e=="function"&&(e=e(n)),A(e)&&(e=[e]),Array.isArray(e))return e;if(!n)return e.default||[];let i=e[n];return i||(i=e[this.getScriptPartFromCode(n)]),i||(i=e[this.formatLanguageCode(n)]),i||(i=e[this.getLanguagePartFromCode(n)]),i||(i=e.default),i||[]}toResolveHierarchy(e,n){const i=this.getFallbackCodes((n===!1?[]:n)||this.options.fallbackLng||[],e),s=[],r=o=>{o&&(this.isSupportedCode(o)?s.push(o):this.logger.warn(`rejecting language code not found in supportedLngs: ${o}`))};return A(e)&&(e.indexOf("-")>-1||e.indexOf("_")>-1)?(this.options.load!=="languageOnly"&&r(this.formatLanguageCode(e)),this.options.load!=="languageOnly"&&this.options.load!=="currentOnly"&&r(this.getScriptPartFromCode(e)),this.options.load!=="currentOnly"&&r(this.getLanguagePartFromCode(e))):A(e)&&r(this.formatLanguageCode(e)),i.forEach(o=>{s.indexOf(o)<0&&r(this.formatLanguageCode(o))}),s}}const rh={zero:0,one:1,two:2,few:3,many:4,other:5},oh={select:t=>t===1?"one":"other",resolvedOptions:()=>({pluralCategories:["one","other"]})};class kb{constructor(e,n={}){this.languageUtils=e,this.options=n,this.logger=St.create("pluralResolver"),this.pluralRulesCache={}}addRule(e,n){this.rules[e]=n}clearCache(){this.pluralRulesCache={}}getRule(e,n={}){const i=Ys(e==="dev"?"en":e),s=n.ordinal?"ordinal":"cardinal",r=JSON.stringify({cleanedCode:i,type:s});if(r in this.pluralRulesCache)return this.pluralRulesCache[r];let o;try{o=new Intl.PluralRules(i,{type:s})}catch{if(!Intl)return this.logger.error("No Intl support, please use an Intl polyfill!"),oh;if(!e.match(/-|_/))return oh;const l=this.languageUtils.getLanguagePartFromCode(e);o=this.getRule(l,n)}return this.pluralRulesCache[r]=o,o}needsPlural(e,n={}){let i=this.getRule(e,n);return i||(i=this.getRule("dev",n)),(i==null?void 0:i.resolvedOptions().pluralCategories.length)>1}getPluralFormsOfKey(e,n,i={}){return this.getSuffixes(e,i).map(s=>`${n}${s}`)}getSuffixes(e,n={}){let i=this.getRule(e,n);return i||(i=this.getRule("dev",n)),i?i.resolvedOptions().pluralCategories.sort((s,r)=>rh[s]-rh[r]).map(s=>`${this.options.prepend}${n.ordinal?`ordinal${this.options.prepend}`:""}${s}`):[]}getSuffix(e,n,i={}){const s=this.getRule(e,i);return s?`${this.options.prepend}${i.ordinal?`ordinal${this.options.prepend}`:""}${s.select(n)}`:(this.logger.warn(`no plural rule found for: ${e}`),this.getSuffix("dev",n,i))}}const ah=(t,e,n,i=".",s=!0)=>{let r=mb(t,e,n);return!r&&s&&A(n)&&(r=ru(t,n,i),r===void 0&&(r=ru(e,n,i))),r},Qa=t=>t.replace(/\$/g,"$$$$");class Cb{constructor(e={}){var n;this.logger=St.create("interpolator"),this.options=e,this.format=((n=e==null?void 0:e.interpolation)==null?void 0:n.format)||(i=>i),this.init(e)}init(e={}){e.interpolation||(e.interpolation={escapeValue:!0});const{escape:n,escapeValue:i,useRawValueToEscape:s,prefix:r,prefixEscaped:o,suffix:a,suffixEscaped:l,formatSeparator:u,unescapeSuffix:c,unescapePrefix:d,nestingPrefix:f,nestingPrefixEscaped:h,nestingSuffix:p,nestingSuffixEscaped:y,nestingOptionsSeparator:v,maxReplaces:m,alwaysFormat:g}=e.interpolation;this.escape=n!==void 0?n:xb,this.escapeValue=i!==void 0?i:!0,this.useRawValueToEscape=s!==void 0?s:!1,this.prefix=r?Xn(r):o||"{{",this.suffix=a?Xn(a):l||"}}",this.formatSeparator=u||",",this.unescapePrefix=c?"":d||"-",this.unescapeSuffix=this.unescapePrefix?"":c||"",this.nestingPrefix=f?Xn(f):h||Xn("$t("),this.nestingSuffix=p?Xn(p):y||Xn(")"),this.nestingOptionsSeparator=v||",",this.maxReplaces=m||1e3,this.alwaysFormat=g!==void 0?g:!1,this.resetRegExp()}reset(){this.options&&this.init(this.options)}resetRegExp(){const e=(n,i)=>(n==null?void 0:n.source)===i?(n.lastIndex=0,n):new RegExp(i,"g");this.regexp=e(this.regexp,`${this.prefix}(.+?)${this.suffix}`),this.regexpUnescape=e(this.regexpUnescape,`${this.prefix}${this.unescapePrefix}(.+?)${this.unescapeSuffix}${this.suffix}`),this.nestingRegexp=e(this.nestingRegexp,`${this.nestingPrefix}(.+?)${this.nestingSuffix}`)}interpolate(e,n,i,s){var h;let r,o,a;const l=this.options&&this.options.interpolation&&this.options.interpolation.defaultVariables||{},u=p=>{if(p.indexOf(this.formatSeparator)<0){const g=ah(n,l,p,this.options.keySeparator,this.options.ignoreJSONStructure);return this.alwaysFormat?this.format(g,void 0,i,{...s,...n,interpolationkey:p}):g}const y=p.split(this.formatSeparator),v=y.shift().trim(),m=y.join(this.formatSeparator).trim();return this.format(ah(n,l,v,this.options.keySeparator,this.options.ignoreJSONStructure),m,i,{...s,...n,interpolationkey:v})};this.resetRegExp();const c=(s==null?void 0:s.missingInterpolationHandler)||this.options.missingInterpolationHandler,d=((h=s==null?void 0:s.interpolation)==null?void 0:h.skipOnVariables)!==void 0?s.interpolation.skipOnVariables:this.options.interpolation.skipOnVariables;return[{regex:this.regexpUnescape,safeValue:p=>Qa(p)},{regex:this.regexp,safeValue:p=>this.escapeValue?Qa(this.escape(p)):Qa(p)}].forEach(p=>{for(a=0;r=p.regex.exec(e);){const y=r[1].trim();if(o=u(y),o===void 0)if(typeof c=="function"){const m=c(e,r,s);o=A(m)?m:""}else if(s&&Object.prototype.hasOwnProperty.call(s,y))o="";else if(d){o=r[0];continue}else this.logger.warn(`missed to pass in variable ${y} for interpolating ${e}`),o="";else!A(o)&&!this.useRawValueToEscape&&(o=Gf(o));const v=p.safeValue(o);if(e=e.replace(r[0],v),d?(p.regex.lastIndex+=o.length,p.regex.lastIndex-=r[0].length):p.regex.lastIndex=0,a++,a>=this.maxReplaces)break}}),e}nest(e,n,i={}){let s,r,o;const a=(l,u)=>{const c=this.nestingOptionsSeparator;if(l.indexOf(c)<0)return l;const d=l.split(new RegExp(`${c}[ ]*{`));let f=`{${d[1]}`;l=d[0],f=this.interpolate(f,o);const h=f.match(/'/g),p=f.match(/"/g);(((h==null?void 0:h.length)??0)%2===0&&!p||p.length%2!==0)&&(f=f.replace(/'/g,'"'));try{o=JSON.parse(f),u&&(o={...u,...o})}catch(y){return this.logger.warn(`failed parsing options string in nesting for key ${l}`,y),`${l}${c}${f}`}return o.defaultValue&&o.defaultValue.indexOf(this.prefix)>-1&&delete o.defaultValue,l};for(;s=this.nestingRegexp.exec(e);){let l=[];o={...i},o=o.replace&&!A(o.replace)?o.replace:o,o.applyPostProcessor=!1,delete o.defaultValue;const u=/{.*}/.test(s[1])?s[1].lastIndexOf("}")+1:s[1].indexOf(this.formatSeparator);if(u!==-1&&(l=s[1].slice(u).split(this.formatSeparator).map(c=>c.trim()).filter(Boolean),s[1]=s[1].slice(0,u)),r=n(a.call(this,s[1].trim(),o),o),r&&s[0]===e&&!A(r))return r;A(r)||(r=Gf(r)),r||(this.logger.warn(`missed to resolve ${s[1]} for nesting ${e}`),r=""),l.length&&(r=l.reduce((c,d)=>this.format(c,d,i.lng,{...i,interpolationkey:s[1].trim()}),r.trim())),e=e.replace(s[0],r),this.regexp.lastIndex=0}return e}}const Pb=t=>{let e=t.toLowerCase().trim();const n={};if(t.indexOf("(")>-1){const i=t.split("(");e=i[0].toLowerCase().trim();const s=i[1].substring(0,i[1].length-1);e==="currency"&&s.indexOf(":")<0?n.currency||(n.currency=s.trim()):e==="relativetime"&&s.indexOf(":")<0?n.range||(n.range=s.trim()):s.split(";").forEach(o=>{if(o){const[a,...l]=o.split(":"),u=l.join(":").trim().replace(/^'+|'+$/g,""),c=a.trim();n[c]||(n[c]=u),u==="false"&&(n[c]=!1),u==="true"&&(n[c]=!0),isNaN(u)||(n[c]=parseInt(u,10))}})}return{formatName:e,formatOptions:n}},lh=t=>{const e={};return(n,i,s)=>{let r=s;s&&s.interpolationkey&&s.formatParams&&s.formatParams[s.interpolationkey]&&s[s.interpolationkey]&&(r={...r,[s.interpolationkey]:void 0});const o=i+JSON.stringify(r);let a=e[o];return a||(a=t(Ys(i),s),e[o]=a),a(n)}},Mb=t=>(e,n,i)=>t(Ys(n),i)(e);class Eb{constructor(e={}){this.logger=St.create("formatter"),this.options=e,this.init(e)}init(e,n={interpolation:{}}){this.formatSeparator=n.interpolation.formatSeparator||",";const i=n.cacheInBuiltFormats?lh:Mb;this.formats={number:i((s,r)=>{const o=new Intl.NumberFormat(s,{...r});return a=>o.format(a)}),currency:i((s,r)=>{const o=new Intl.NumberFormat(s,{...r,style:"currency"});return a=>o.format(a)}),datetime:i((s,r)=>{const o=new Intl.DateTimeFormat(s,{...r});return a=>o.format(a)}),relativetime:i((s,r)=>{const o=new Intl.RelativeTimeFormat(s,{...r});return a=>o.format(a,r.range||"day")}),list:i((s,r)=>{const o=new Intl.ListFormat(s,{...r});return a=>o.format(a)})}}add(e,n){this.formats[e.toLowerCase().trim()]=n}addCached(e,n){this.formats[e.toLowerCase().trim()]=lh(n)}format(e,n,i,s={}){const r=n.split(this.formatSeparator);if(r.length>1&&r[0].indexOf("(")>1&&r[0].indexOf(")")<0&&r.find(a=>a.indexOf(")")>-1)){const a=r.findIndex(l=>l.indexOf(")")>-1);r[0]=[r[0],...r.splice(1,a)].join(this.formatSeparator)}return r.reduce((a,l)=>{var d;const{formatName:u,formatOptions:c}=Pb(l);if(this.formats[u]){let f=a;try{const h=((d=s==null?void 0:s.formatParams)==null?void 0:d[s.interpolationkey])||{},p=h.locale||h.lng||s.locale||s.lng||i;f=this.formats[u](a,p,{...c,...s,...h})}catch(h){this.logger.warn(h)}return f}else this.logger.warn(`there was no format function for ${u}`);return a},e)}}const Ob=(t,e)=>{t.pending[e]!==void 0&&(delete t.pending[e],t.pendingCount--)};class Nb extends ua{constructor(e,n,i,s={}){var r,o;super(),this.backend=e,this.store=n,this.services=i,this.languageUtils=i.languageUtils,this.options=s,this.logger=St.create("backendConnector"),this.waitingReads=[],this.maxParallelReads=s.maxParallelReads||10,this.readingCalls=0,this.maxRetries=s.maxRetries>=0?s.maxRetries:5,this.retryTimeout=s.retryTimeout>=1?s.retryTimeout:350,this.state={},this.queue=[],(o=(r=this.backend)==null?void 0:r.init)==null||o.call(r,i,s.backend,s)}queueLoad(e,n,i,s){const r={},o={},a={},l={};return e.forEach(u=>{let c=!0;n.forEach(d=>{const f=`${u}|${d}`;!i.reload&&this.store.hasResourceBundle(u,d)?this.state[f]=2:this.state[f]<0||(this.state[f]===1?o[f]===void 0&&(o[f]=!0):(this.state[f]=1,c=!1,o[f]===void 0&&(o[f]=!0),r[f]===void 0&&(r[f]=!0),l[d]===void 0&&(l[d]=!0)))}),c||(a[u]=!0)}),(Object.keys(r).length||Object.keys(o).length)&&this.queue.push({pending:o,pendingCount:Object.keys(o).length,loaded:{},errors:[],callback:s}),{toLoad:Object.keys(r),pending:Object.keys(o),toLoadLanguages:Object.keys(a),toLoadNamespaces:Object.keys(l)}}loaded(e,n,i){const s=e.split("|"),r=s[0],o=s[1];n&&this.emit("failedLoading",r,o,n),!n&&i&&this.store.addResourceBundle(r,o,i,void 0,void 0,{skipCopy:!0}),this.state[e]=n?-1:2,n&&i&&(this.state[e]=0);const a={};this.queue.forEach(l=>{gb(l.loaded,[r],o),Ob(l,e),n&&l.errors.push(n),l.pendingCount===0&&!l.done&&(Object.keys(l.loaded).forEach(u=>{a[u]||(a[u]={});const c=l.loaded[u];c.length&&c.forEach(d=>{a[u][d]===void 0&&(a[u][d]=!0)})}),l.done=!0,l.errors.length?l.callback(l.errors):l.callback())}),this.emit("loaded",a),this.queue=this.queue.filter(l=>!l.done)}read(e,n,i,s=0,r=this.retryTimeout,o){if(!e.length)return o(null,{});if(this.readingCalls>=this.maxParallelReads){this.waitingReads.push({lng:e,ns:n,fcName:i,tried:s,wait:r,callback:o});return}this.readingCalls++;const a=(u,c)=>{if(this.readingCalls--,this.waitingReads.length>0){const d=this.waitingReads.shift();this.read(d.lng,d.ns,d.fcName,d.tried,d.wait,d.callback)}if(u&&c&&s<this.maxRetries){setTimeout(()=>{this.read.call(this,e,n,i,s+1,r*2,o)},r);return}o(u,c)},l=this.backend[i].bind(this.backend);if(l.length===2){try{const u=l(e,n);u&&typeof u.then=="function"?u.then(c=>a(null,c)).catch(a):a(null,u)}catch(u){a(u)}return}return l(e,n,a)}prepareLoading(e,n,i={},s){if(!this.backend)return this.logger.warn("No backend was added via i18next.use. Will not load resources."),s&&s();A(e)&&(e=this.languageUtils.toResolveHierarchy(e)),A(n)&&(n=[n]);const r=this.queueLoad(e,n,i,s);if(!r.toLoad.length)return r.pending.length||s(),null;r.toLoad.forEach(o=>{this.loadOne(o)})}load(e,n,i){this.prepareLoading(e,n,{},i)}reload(e,n,i){this.prepareLoading(e,n,{reload:!0},i)}loadOne(e,n=""){const i=e.split("|"),s=i[0],r=i[1];this.read(s,r,"read",void 0,void 0,(o,a)=>{o&&this.logger.warn(`${n}loading namespace ${r} for language ${s} failed`,o),!o&&a&&this.logger.log(`${n}loaded namespace ${r} for language ${s}`,a),this.loaded(e,o,a)})}saveMissing(e,n,i,s,r,o={},a=()=>{}){var l,u,c,d,f;if((u=(l=this.services)==null?void 0:l.utils)!=null&&u.hasLoadedNamespace&&!((d=(c=this.services)==null?void 0:c.utils)!=null&&d.hasLoadedNamespace(n))){this.logger.warn(`did not save key "${i}" as the namespace "${n}" was not yet loaded`,"This means something IS WRONG in your setup. You access the t function before i18next.init / i18next.loadNamespace / i18next.changeLanguage was done. Wait for the callback or Promise to resolve before accessing it!!!");return}if(!(i==null||i==="")){if((f=this.backend)!=null&&f.create){const h={...o,isUpdate:r},p=this.backend.create.bind(this.backend);if(p.length<6)try{let y;p.length===5?y=p(e,n,i,s,h):y=p(e,n,i,s),y&&typeof y.then=="function"?y.then(v=>a(null,v)).catch(a):a(null,y)}catch(y){a(y)}else p(e,n,i,s,a,h)}!e||!e[0]||this.store.addResource(e[0],n,i,s)}}}const uh=()=>({debug:!1,initAsync:!0,ns:["translation"],defaultNS:["translation"],fallbackLng:["dev"],fallbackNS:!1,supportedLngs:!1,nonExplicitSupportedLngs:!1,load:"all",preload:!1,simplifyPluralSuffix:!0,keySeparator:".",nsSeparator:":",pluralSeparator:"_",contextSeparator:"_",partialBundledLanguages:!1,saveMissing:!1,updateMissing:!1,saveMissingTo:"fallback",saveMissingPlurals:!0,missingKeyHandler:!1,missingInterpolationHandler:!1,postProcess:!1,postProcessPassResolved:!1,returnNull:!1,returnEmptyString:!0,returnObjects:!1,joinArrays:!1,returnedObjectHandler:!1,parseMissingKeyHandler:!1,appendNamespaceToMissingKey:!1,appendNamespaceToCIMode:!1,overloadTranslationOptionHandler:t=>{let e={};if(typeof t[1]=="object"&&(e=t[1]),A(t[1])&&(e.defaultValue=t[1]),A(t[2])&&(e.tDescription=t[2]),typeof t[2]=="object"||typeof t[3]=="object"){const n=t[3]||t[2];Object.keys(n).forEach(i=>{e[i]=n[i]})}return e},interpolation:{escapeValue:!0,format:t=>t,prefix:"{{",suffix:"}}",formatSeparator:",",unescapePrefix:"-",nestingPrefix:"$t(",nestingSuffix:")",nestingOptionsSeparator:",",maxReplaces:1e3,skipOnVariables:!0},cacheInBuiltFormats:!0}),ch=t=>{var e,n;return A(t.ns)&&(t.ns=[t.ns]),A(t.fallbackLng)&&(t.fallbackLng=[t.fallbackLng]),A(t.fallbackNS)&&(t.fallbackNS=[t.fallbackNS]),((n=(e=t.supportedLngs)==null?void 0:e.indexOf)==null?void 0:n.call(e,"cimode"))<0&&(t.supportedLngs=t.supportedLngs.concat(["cimode"])),typeof t.initImmediate=="boolean"&&(t.initAsync=t.initImmediate),t},Ar=()=>{},Lb=t=>{Object.getOwnPropertyNames(Object.getPrototypeOf(t)).forEach(n=>{typeof t[n]=="function"&&(t[n]=t[n].bind(t))})};class Xs extends ua{constructor(e={},n){if(super(),this.options=ch(e),this.services={},this.logger=St,this.modules={external:[]},Lb(this),n&&!this.isInitialized&&!e.isClone){if(!this.options.initAsync)return this.init(e,n),this;setTimeout(()=>{this.init(e,n)},0)}}init(e={},n){this.isInitializing=!0,typeof e=="function"&&(n=e,e={}),e.defaultNS==null&&e.ns&&(A(e.ns)?e.defaultNS=e.ns:e.ns.indexOf("translation")<0&&(e.defaultNS=e.ns[0]));const i=uh();this.options={...i,...this.options,...ch(e)},this.options.interpolation={...i.interpolation,...this.options.interpolation},e.keySeparator!==void 0&&(this.options.userDefinedKeySeparator=e.keySeparator),e.nsSeparator!==void 0&&(this.options.userDefinedNsSeparator=e.nsSeparator);const s=u=>u?typeof u=="function"?new u:u:null;if(!this.options.isClone){this.modules.logger?St.init(s(this.modules.logger),this.options):St.init(null,this.options);let u;this.modules.formatter?u=this.modules.formatter:u=Eb;const c=new sh(this.options);this.store=new th(this.options.resources,this.options);const d=this.services;d.logger=St,d.resourceStore=this.store,d.languageUtils=c,d.pluralResolver=new kb(c,{prepend:this.options.pluralSeparator,simplifyPluralSuffix:this.options.simplifyPluralSuffix}),this.options.interpolation.format&&this.options.interpolation.format!==i.interpolation.format&&this.logger.warn("init: you are still using the legacy format function, please use the new approach: https://www.i18next.com/translation-function/formatting"),u&&(!this.options.interpolation.format||this.options.interpolation.format===i.interpolation.format)&&(d.formatter=s(u),d.formatter.init&&d.formatter.init(d,this.options),this.options.interpolation.format=d.formatter.format.bind(d.formatter)),d.interpolator=new Cb(this.options),d.utils={hasLoadedNamespace:this.hasLoadedNamespace.bind(this)},d.backendConnector=new Nb(s(this.modules.backend),d.resourceStore,d,this.options),d.backendConnector.on("*",(h,...p)=>{this.emit(h,...p)}),this.modules.languageDetector&&(d.languageDetector=s(this.modules.languageDetector),d.languageDetector.init&&d.languageDetector.init(d,this.options.detection,this.options)),this.modules.i18nFormat&&(d.i18nFormat=s(this.modules.i18nFormat),d.i18nFormat.init&&d.i18nFormat.init(this)),this.translator=new Io(this.services,this.options),this.translator.on("*",(h,...p)=>{this.emit(h,...p)}),this.modules.external.forEach(h=>{h.init&&h.init(this)})}if(this.format=this.options.interpolation.format,n||(n=Ar),this.options.fallbackLng&&!this.services.languageDetector&&!this.options.lng){const u=this.services.languageUtils.getFallbackCodes(this.options.fallbackLng);u.length>0&&u[0]!=="dev"&&(this.options.lng=u[0])}!this.services.languageDetector&&!this.options.lng&&this.logger.warn("init: no languageDetector is used and no lng is defined"),["getResource","hasResourceBundle","getResourceBundle","getDataByLanguage"].forEach(u=>{this[u]=(...c)=>this.store[u](...c)}),["addResource","addResources","addResourceBundle","removeResourceBundle"].forEach(u=>{this[u]=(...c)=>(this.store[u](...c),this)});const a=Ki(),l=()=>{const u=(c,d)=>{this.isInitializing=!1,this.isInitialized&&!this.initializedStoreOnce&&this.logger.warn("init: i18next is already initialized. You should call init just once!"),this.isInitialized=!0,this.options.isClone||this.logger.log("initialized",this.options),this.emit("initialized",this.options),a.resolve(d),n(c,d)};if(this.languages&&!this.isInitialized)return u(null,this.t.bind(this));this.changeLanguage(this.options.lng,u)};return this.options.resources||!this.options.initAsync?l():setTimeout(l,0),a}loadResources(e,n=Ar){var r,o;let i=n;const s=A(e)?e:this.language;if(typeof e=="function"&&(i=e),!this.options.resources||this.options.partialBundledLanguages){if((s==null?void 0:s.toLowerCase())==="cimode"&&(!this.options.preload||this.options.preload.length===0))return i();const a=[],l=u=>{if(!u||u==="cimode")return;this.services.languageUtils.toResolveHierarchy(u).forEach(d=>{d!=="cimode"&&a.indexOf(d)<0&&a.push(d)})};s?l(s):this.services.languageUtils.getFallbackCodes(this.options.fallbackLng).forEach(c=>l(c)),(o=(r=this.options.preload)==null?void 0:r.forEach)==null||o.call(r,u=>l(u)),this.services.backendConnector.load(a,this.options.ns,u=>{!u&&!this.resolvedLanguage&&this.language&&this.setResolvedLanguage(this.language),i(u)})}else i(null)}reloadResources(e,n,i){const s=Ki();return typeof e=="function"&&(i=e,e=void 0),typeof n=="function"&&(i=n,n=void 0),e||(e=this.languages),n||(n=this.options.ns),i||(i=Ar),this.services.backendConnector.reload(e,n,r=>{s.resolve(),i(r)}),s}use(e){if(!e)throw new Error("You are passing an undefined module! Please check the object you are passing to i18next.use()");if(!e.type)throw new Error("You are passing a wrong module! Please check the object you are passing to i18next.use()");return e.type==="backend"&&(this.modules.backend=e),(e.type==="logger"||e.log&&e.warn&&e.error)&&(this.modules.logger=e),e.type==="languageDetector"&&(this.modules.languageDetector=e),e.type==="i18nFormat"&&(this.modules.i18nFormat=e),e.type==="postProcessor"&&jm.addPostProcessor(e),e.type==="formatter"&&(this.modules.formatter=e),e.type==="3rdParty"&&this.modules.external.push(e),this}setResolvedLanguage(e){if(!(!e||!this.languages)&&!(["cimode","dev"].indexOf(e)>-1)){for(let n=0;n<this.languages.length;n++){const i=this.languages[n];if(!(["cimode","dev"].indexOf(i)>-1)&&this.store.hasLanguageSomeTranslations(i)){this.resolvedLanguage=i;break}}!this.resolvedLanguage&&this.languages.indexOf(e)<0&&this.store.hasLanguageSomeTranslations(e)&&(this.resolvedLanguage=e,this.languages.unshift(e))}}changeLanguage(e,n){this.isLanguageChangingTo=e;const i=Ki();this.emit("languageChanging",e);const s=a=>{this.language=a,this.languages=this.services.languageUtils.toResolveHierarchy(a),this.resolvedLanguage=void 0,this.setResolvedLanguage(a)},r=(a,l)=>{l?this.isLanguageChangingTo===e&&(s(l),this.translator.changeLanguage(l),this.isLanguageChangingTo=void 0,this.emit("languageChanged",l),this.logger.log("languageChanged",l)):this.isLanguageChangingTo=void 0,i.resolve((...u)=>this.t(...u)),n&&n(a,(...u)=>this.t(...u))},o=a=>{var c,d;!e&&!a&&this.services.languageDetector&&(a=[]);const l=A(a)?a:a&&a[0],u=this.store.hasLanguageSomeTranslations(l)?l:this.services.languageUtils.getBestMatchFromCodes(A(a)?[a]:a);u&&(this.language||s(u),this.translator.language||this.translator.changeLanguage(u),(d=(c=this.services.languageDetector)==null?void 0:c.cacheUserLanguage)==null||d.call(c,u)),this.loadResources(u,f=>{r(f,u)})};return!e&&this.services.languageDetector&&!this.services.languageDetector.async?o(this.services.languageDetector.detect()):!e&&this.services.languageDetector&&this.services.languageDetector.async?this.services.languageDetector.detect.length===0?this.services.languageDetector.detect().then(o):this.services.languageDetector.detect(o):o(e),i}getFixedT(e,n,i){const s=(r,o,...a)=>{let l;typeof o!="object"?l=this.options.overloadTranslationOptionHandler([r,o].concat(a)):l={...o},l.lng=l.lng||s.lng,l.lngs=l.lngs||s.lngs,l.ns=l.ns||s.ns,l.keyPrefix!==""&&(l.keyPrefix=l.keyPrefix||i||s.keyPrefix);const u=this.options.keySeparator||".";let c;return l.keyPrefix&&Array.isArray(r)?c=r.map(d=>`${l.keyPrefix}${u}${d}`):c=l.keyPrefix?`${l.keyPrefix}${u}${r}`:r,this.t(c,l)};return A(e)?s.lng=e:s.lngs=e,s.ns=n,s.keyPrefix=i,s}t(...e){var n;return(n=this.translator)==null?void 0:n.translate(...e)}exists(...e){var n;return(n=this.translator)==null?void 0:n.exists(...e)}setDefaultNamespace(e){this.options.defaultNS=e}hasLoadedNamespace(e,n={}){if(!this.isInitialized)return this.logger.warn("hasLoadedNamespace: i18next was not initialized",this.languages),!1;if(!this.languages||!this.languages.length)return this.logger.warn("hasLoadedNamespace: i18n.languages were undefined or empty",this.languages),!1;const i=n.lng||this.resolvedLanguage||this.languages[0],s=this.options?this.options.fallbackLng:!1,r=this.languages[this.languages.length-1];if(i.toLowerCase()==="cimode")return!0;const o=(a,l)=>{const u=this.services.backendConnector.state[`${a}|${l}`];return u===-1||u===0||u===2};if(n.precheck){const a=n.precheck(this,o);if(a!==void 0)return a}return!!(this.hasResourceBundle(i,e)||!this.services.backendConnector.backend||this.options.resources&&!this.options.partialBundledLanguages||o(i,e)&&(!s||o(r,e)))}loadNamespaces(e,n){const i=Ki();return this.options.ns?(A(e)&&(e=[e]),e.forEach(s=>{this.options.ns.indexOf(s)<0&&this.options.ns.push(s)}),this.loadResources(s=>{i.resolve(),n&&n(s)}),i):(n&&n(),Promise.resolve())}loadLanguages(e,n){const i=Ki();A(e)&&(e=[e]);const s=this.options.preload||[],r=e.filter(o=>s.indexOf(o)<0&&this.services.languageUtils.isSupportedCode(o));return r.length?(this.options.preload=s.concat(r),this.loadResources(o=>{i.resolve(),n&&n(o)}),i):(n&&n(),Promise.resolve())}dir(e){var s,r;if(e||(e=this.resolvedLanguage||(((s=this.languages)==null?void 0:s.length)>0?this.languages[0]:this.language)),!e)return"rtl";if(Intl.Locale){const o=new Intl.Locale(e);if(o&&o.getTextInfo){const a=o.getTextInfo();if(a&&a.direction)return a.direction}}const n=["ar","shu","sqr","ssh","xaa","yhd","yud","aao","abh","abv","acm","acq","acw","acx","acy","adf","ads","aeb","aec","afb","ajp","apc","apd","arb","arq","ars","ary","arz","auz","avl","ayh","ayl","ayn","ayp","bbz","pga","he","iw","ps","pbt","pbu","pst","prp","prd","ug","ur","ydd","yds","yih","ji","yi","hbo","men","xmn","fa","jpr","peo","pes","prs","dv","sam","ckb"],i=((r=this.services)==null?void 0:r.languageUtils)||new sh(uh());return e.toLowerCase().indexOf("-latn")>1?"ltr":n.indexOf(i.getLanguagePartFromCode(e))>-1||e.toLowerCase().indexOf("-arab")>1?"rtl":"ltr"}static createInstance(e={},n){return new Xs(e,n)}cloneInstance(e={},n=Ar){const i=e.forkResourceStore;i&&delete e.forkResourceStore;const s={...this.options,...e,isClone:!0},r=new Xs(s);if((e.debug!==void 0||e.prefix!==void 0)&&(r.logger=r.logger.clone(e)),["store","services","language"].forEach(a=>{r[a]=this[a]}),r.services={...this.services},r.services.utils={hasLoadedNamespace:r.hasLoadedNamespace.bind(r)},i){const a=Object.keys(this.store.data).reduce((l,u)=>(l[u]={...this.store.data[u]},l[u]=Object.keys(l[u]).reduce((c,d)=>(c[d]={...l[u][d]},c),l[u]),l),{});r.store=new th(a,s),r.services.resourceStore=r.store}return r.translator=new Io(r.services,s),r.translator.on("*",(a,...l)=>{r.emit(a,...l)}),r.init(s,n),r.translator.options=s,r.translator.backendConnector.services.utils={hasLoadedNamespace:r.hasLoadedNamespace.bind(r)},r}toJSON(){return{options:this.options,store:this.store,language:this.language,languages:this.languages,resolvedLanguage:this.resolvedLanguage}}}const Le=Xs.createInstance();Le.createInstance=Xs.createInstance;Le.createInstance;Le.dir;Le.init;Le.loadResources;Le.reloadResources;Le.use;Le.changeLanguage;Le.getFixedT;Le.t;Le.exists;Le.setDefaultNamespace;Le.hasLoadedNamespace;Le.loadNamespaces;Le.loadLanguages;const{slice:Tb,forEach:Rb}=[];function Db(t){return Rb.call(Tb.call(arguments,1),e=>{if(e)for(const n in e)t[n]===void 0&&(t[n]=e[n])}),t}function jb(t){return typeof t!="string"?!1:[/<\s*script.*?>/i,/<\s*\/\s*script\s*>/i,/<\s*img.*?on\w+\s*=/i,/<\s*\w+\s*on\w+\s*=.*?>/i,/javascript\s*:/i,/vbscript\s*:/i,/expression\s*\(/i,/eval\s*\(/i,/alert\s*\(/i,/document\.cookie/i,/document\.write\s*\(/i,/window\.location/i,/innerHTML/i].some(n=>n.test(t))}const dh=/^[\u0009\u0020-\u007e\u0080-\u00ff]+$/,Ab=function(t,e){const i=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{path:"/"},s=encodeURIComponent(e);let r=`${t}=${s}`;if(i.maxAge>0){const o=i.maxAge-0;if(Number.isNaN(o))throw new Error("maxAge should be a Number");r+=`; Max-Age=${Math.floor(o)}`}if(i.domain){if(!dh.test(i.domain))throw new TypeError("option domain is invalid");r+=`; Domain=${i.domain}`}if(i.path){if(!dh.test(i.path))throw new TypeError("option path is invalid");r+=`; Path=${i.path}`}if(i.expires){if(typeof i.expires.toUTCString!="function")throw new TypeError("option expires is invalid");r+=`; Expires=${i.expires.toUTCString()}`}if(i.httpOnly&&(r+="; HttpOnly"),i.secure&&(r+="; Secure"),i.sameSite)switch(typeof i.sameSite=="string"?i.sameSite.toLowerCase():i.sameSite){case!0:r+="; SameSite=Strict";break;case"lax":r+="; SameSite=Lax";break;case"strict":r+="; SameSite=Strict";break;case"none":r+="; SameSite=None";break;default:throw new TypeError("option sameSite is invalid")}return i.partitioned&&(r+="; Partitioned"),r},fh={create(t,e,n,i){let s=arguments.length>4&&arguments[4]!==void 0?arguments[4]:{path:"/",sameSite:"strict"};n&&(s.expires=new Date,s.expires.setTime(s.expires.getTime()+n*60*1e3)),i&&(s.domain=i),document.cookie=Ab(t,e,s)},read(t){const e=`${t}=`,n=document.cookie.split(";");for(let i=0;i<n.length;i++){let s=n[i];for(;s.charAt(0)===" ";)s=s.substring(1,s.length);if(s.indexOf(e)===0)return s.substring(e.length,s.length)}return null},remove(t,e){this.create(t,"",-1,e)}};var Ib={name:"cookie",lookup(t){let{lookupCookie:e}=t;if(e&&typeof document<"u")return fh.read(e)||void 0},cacheUserLanguage(t,e){let{lookupCookie:n,cookieMinutes:i,cookieDomain:s,cookieOptions:r}=e;n&&typeof document<"u"&&fh.create(n,t,i,s,r)}},Fb={name:"querystring",lookup(t){var i;let{lookupQuerystring:e}=t,n;if(typeof window<"u"){let{search:s}=window.location;!window.location.search&&((i=window.location.hash)==null?void 0:i.indexOf("?"))>-1&&(s=window.location.hash.substring(window.location.hash.indexOf("?")));const o=s.substring(1).split("&");for(let a=0;a<o.length;a++){const l=o[a].indexOf("=");l>0&&o[a].substring(0,l)===e&&(n=o[a].substring(l+1))}}return n}},zb={name:"hash",lookup(t){var s;let{lookupHash:e,lookupFromHashIndex:n}=t,i;if(typeof window<"u"){const{hash:r}=window.location;if(r&&r.length>2){const o=r.substring(1);if(e){const a=o.split("&");for(let l=0;l<a.length;l++){const u=a[l].indexOf("=");u>0&&a[l].substring(0,u)===e&&(i=a[l].substring(u+1))}}if(i)return i;if(!i&&n>-1){const a=r.match(/\/([a-zA-Z-]*)/g);return Array.isArray(a)?(s=a[typeof n=="number"?n:0])==null?void 0:s.replace("/",""):void 0}}}return i}};let Qn=null;const hh=()=>{if(Qn!==null)return Qn;try{if(Qn=typeof window<"u"&&window.localStorage!==null,!Qn)return!1;const t="i18next.translate.boo";window.localStorage.setItem(t,"foo"),window.localStorage.removeItem(t)}catch{Qn=!1}return Qn};var Bb={name:"localStorage",lookup(t){let{lookupLocalStorage:e}=t;if(e&&hh())return window.localStorage.getItem(e)||void 0},cacheUserLanguage(t,e){let{lookupLocalStorage:n}=e;n&&hh()&&window.localStorage.setItem(n,t)}};let qn=null;const ph=()=>{if(qn!==null)return qn;try{if(qn=typeof window<"u"&&window.sessionStorage!==null,!qn)return!1;const t="i18next.translate.boo";window.sessionStorage.setItem(t,"foo"),window.sessionStorage.removeItem(t)}catch{qn=!1}return qn};var $b={name:"sessionStorage",lookup(t){let{lookupSessionStorage:e}=t;if(e&&ph())return window.sessionStorage.getItem(e)||void 0},cacheUserLanguage(t,e){let{lookupSessionStorage:n}=e;n&&ph()&&window.sessionStorage.setItem(n,t)}},Vb={name:"navigator",lookup(t){const e=[];if(typeof navigator<"u"){const{languages:n,userLanguage:i,language:s}=navigator;if(n)for(let r=0;r<n.length;r++)e.push(n[r]);i&&e.push(i),s&&e.push(s)}return e.length>0?e:void 0}},Hb={name:"htmlTag",lookup(t){let{htmlTag:e}=t,n;const i=e||(typeof document<"u"?document.documentElement:null);return i&&typeof i.getAttribute=="function"&&(n=i.getAttribute("lang")),n}},Wb={name:"path",lookup(t){var s;let{lookupFromPathIndex:e}=t;if(typeof window>"u")return;const n=window.location.pathname.match(/\/([a-zA-Z-]*)/g);return Array.isArray(n)?(s=n[typeof e=="number"?e:0])==null?void 0:s.replace("/",""):void 0}},Ub={name:"subdomain",lookup(t){var s,r;let{lookupFromSubdomainIndex:e}=t;const n=typeof e=="number"?e+1:1,i=typeof window<"u"&&((r=(s=window.location)==null?void 0:s.hostname)==null?void 0:r.match(/^(\w{2,5})\.(([a-z0-9-]{1,63}\.[a-z]{2,6})|localhost)/i));if(i)return i[n]}};let Am=!1;try{document.cookie,Am=!0}catch{}const Im=["querystring","cookie","localStorage","sessionStorage","navigator","htmlTag"];Am||Im.splice(1,1);const Kb=()=>({order:Im,lookupQuerystring:"lng",lookupCookie:"i18next",lookupLocalStorage:"i18nextLng",lookupSessionStorage:"i18nextLng",caches:["localStorage"],excludeCacheFor:["cimode"],convertDetectedLanguage:t=>t});class Fm{constructor(e){let n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};this.type="languageDetector",this.detectors={},this.init(e,n)}init(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{languageUtils:{}},n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},i=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{};this.services=e,this.options=Db(n,this.options||{},Kb()),typeof this.options.convertDetectedLanguage=="string"&&this.options.convertDetectedLanguage.indexOf("15897")>-1&&(this.options.convertDetectedLanguage=s=>s.replace("-","_")),this.options.lookupFromUrlIndex&&(this.options.lookupFromPathIndex=this.options.lookupFromUrlIndex),this.i18nOptions=i,this.addDetector(Ib),this.addDetector(Fb),this.addDetector(Bb),this.addDetector($b),this.addDetector(Vb),this.addDetector(Hb),this.addDetector(Wb),this.addDetector(Ub),this.addDetector(zb)}addDetector(e){return this.detectors[e.name]=e,this}detect(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:this.options.order,n=[];return e.forEach(i=>{if(this.detectors[i]){let s=this.detectors[i].lookup(this.options);s&&typeof s=="string"&&(s=[s]),s&&(n=n.concat(s))}}),n=n.filter(i=>i!=null&&!jb(i)).map(i=>this.options.convertDetectedLanguage(i)),this.services&&this.services.languageUtils&&this.services.languageUtils.getBestMatchFromCodes?n:n.length>0?n[0]:null}cacheUserLanguage(e){let n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:this.options.caches;n&&(this.options.excludeCacheFor&&this.options.excludeCacheFor.indexOf(e)>-1||n.forEach(i=>{this.detectors[i]&&this.detectors[i].cacheUserLanguage(e,this.options)}))}}Fm.type="languageDetector";const Yb={en:{translation:{"nav.dashboard":"Dashboard","nav.weapons":"Weapons Systems","nav.missions":"Mission Control","nav.simulation":"Simulation Lab","nav.cyber":"Cyber Defense","nav.strategic":"Strategic Command","nav.assets":"Asset Management","nav.labs":"Research Labs","nav.sandbox":"Visual Sandbox","nav.equipment":"Equipment Status","nav.statistics":"Mission Statistics","nav.results":"Simulation Results","nav.settings":"Settings","common.status":"Status","common.active":"Active","common.operational":"Operational","common.maintenance":"Maintenance","common.critical":"Critical","common.healthy":"Healthy","common.warning":"Warning","common.offline":"Offline","common.classified":"CLASSIFIED","common.restricted":"RESTRICTED","common.confidential":"CONFIDENTIAL","common.secret":"SECRET","common.top_secret":"TOP SECRET","common.secure":"SECURE","common.online":"System Online","common.encrypted":"Encrypted","dashboard.title":"DRDO Integrated Dashboard for eXcellence","dashboard.subtitle":"Interactive Defense eXcellence","dashboard.activeAssets":"Active Systems","dashboard.activeMissions":"Mission Ready","dashboard.threatAlerts":"Active Alerts","dashboard.systemHealth":"Success Rate","dashboard.recentAlerts":"Recent Threat Alerts","dashboard.allSystemsOperational":"All Systems Operational","dashboard.lastUpdated":"Last Updated","dashboard.systemOverview":"System Overview","dashboard.quickActions":"Quick Actions","dashboard.systemHealthCheck":"System Health Check","dashboard.missionPlanning":"Mission Planning","dashboard.securityAudit":"Security Audit","dashboard.generateReport":"Generate Report","equipment.title":"Equipment Health","equipment.subtitle":"Real-time equipment monitoring and status","equipment.distribution":"Equipment Distribution","equipment.overview":"Status Overview","equipment.operationalEquipment":"Operational Equipment","equipment.downForMaintenance":"Down for Maintenance","equipment.totalUnits":"Total Units","mission.title":"Mission Statistics","mission.subtitle":"Progress tracking and mission analytics","mission.progressTrend":"Mission Progress Trend","mission.metrics":"Mission Metrics","mission.activeMissions":"Active Missions","mission.successRate":"Success Rate","mission.inProgress":"In Progress","mission.completed":"Completed","mission.progress":"Mission Progress (%)","simulation.title":"Simulation Results","simulation.subtitle":"3D trajectory analysis and accuracy testing","simulation.accuracyComparison":"Accuracy Comparison","simulation.testResults":"Test Results","simulation.brahmosTest":"BrahMos Test","simulation.agniTest":"Agni-V Test","simulation.prithviTest":"Prithvi Test","simulation.supersonicCruise":"Supersonic cruise missile","simulation.intercontinental":"Intercontinental ballistic missile","simulation.surfaceToSurface":"Surface-to-surface missile","simulation.accuracy":"Accuracy (%)","settings.title":"System Configuration","settings.subtitle":"Dashboard settings and preferences","settings.themeConfig":"Theme Configuration","settings.securitySettings":"Security Settings","settings.dashboardTheme":"Dashboard Theme","settings.drdoFormal":"DRDO Formal (Default)","settings.lightMode":"Light Mode","settings.darkMode":"Dark Mode","settings.tacticalMode":"Tactical Mode","settings.currentTheme":"Current theme","settings.changesApply":"Changes apply immediately across all modules","settings.aesEncryption":"AES-256 Encryption","settings.dataTransmissions":"All data transmissions encrypted","settings.sessionTimeout":"Session Timeout","settings.idleTimeout":"15 minutes idle timeout","settings.auditLogging":"Audit Logging","settings.actionsLogged":"All actions logged and monitored","settings.adminAccess":"ADMIN ACCESS","security.login":"Secure Login","security.username":"Username","security.password":"Password","security.accessDenied":"Access Denied","security.insufficientClearance":"Your clearance level is insufficient for this resource.","security.yourClearance":"Your Clearance","security.required":"Required","security.demoCredentials":"Demo Credentials: admin / drdo123","security.secureConnection":"SECURE CONNECTION","footer.copyright":"© 2025 DRDO | Classified","footer.version":"v2.0 Military","status.excellentStatus":"Excellent status","status.mediumPriority":"Medium priority","status.aboveTarget":"Above target","status.operational":"OPERATIONAL","status.inProgress":"IN PROGRESS","status.completed":"COMPLETED","status.fromYesterday":"from yesterday"}},hi:{translation:{"nav.dashboard":"डैशबोर्ड","nav.weapons":"हथियार प्रणाली","nav.missions":"मिशन नियंत्रण","nav.simulation":"सिमुलेशन लैब","nav.cyber":"साइबर रक्षा","nav.strategic":"रणनीतिक कमान","nav.assets":"संपत्ति प्रबंधन","nav.labs":"अनुसंधान प्रयोगशाला","nav.sandbox":"विज़ुअल सैंडबॉक्स","nav.equipment":"उपकरण स्थिति","nav.statistics":"मिशन आंकड़े","nav.results":"सिमुलेशन परिणाम","nav.settings":"सेटिंग्स","common.status":"स्थिति","common.active":"सक्रिय","common.operational":"परिचालनात्मक","common.maintenance":"रखरखाव","common.critical":"गंभीर","common.healthy":"स्वस्थ","common.warning":"चेतावनी","common.offline":"ऑफलाइन","common.classified":"वर्गीकृत","common.restricted":"प्रतिबंधित","common.confidential":"गोपनीय","common.secret":"गुप्त","common.top_secret":"अति गुप्त","common.secure":"सुरक्षित","common.online":"सिस्टम ऑनलाइन","common.encrypted":"एन्क्रिप्टेड","dashboard.title":"डीआरडीओ एकीकृत डैशबोर्ड","dashboard.subtitle":"इंटरैक्टिव रक्षा उत्कृष्टता","dashboard.activeAssets":"सक्रिय सिस्टम","dashboard.activeMissions":"मिशन तैयार","dashboard.threatAlerts":"सक्रिय अलर्ट","dashboard.systemHealth":"सफलता दर","dashboard.allSystemsOperational":"सभी सिस्टम परिचालनात्मक","dashboard.lastUpdated":"अंतिम अपडेट","dashboard.systemOverview":"सिस्टम अवलोकन","dashboard.quickActions":"त्वरित कार्य","dashboard.systemHealthCheck":"सिस्टम स्वास्थ्य जांच","dashboard.missionPlanning":"मिशन योजना","dashboard.securityAudit":"सुरक्षा ऑडिट","dashboard.generateReport":"रिपोर्ट जेनरेट करें","equipment.title":"उपकरण स्वास्थ्य","equipment.subtitle":"रीयल-टाइम उपकरण निगरानी और स्थिति","equipment.distribution":"उपकरण वितरण","equipment.overview":"स्थिति अवलोकन","equipment.operationalEquipment":"परिचालनात्मक उपकरण","equipment.downForMaintenance":"रखरखाव के लिए बंद","equipment.totalUnits":"कुल इकाइयां","mission.title":"मिशन आंकड़े","mission.subtitle":"प्रगति ट्रैकिंग और मिशन विश्लेषण","mission.progressTrend":"मिशन प्रगति रुझान","mission.metrics":"मिशन मेट्रिक्स","mission.activeMissions":"सक्रिय मिशन","mission.successRate":"सफलता दर","mission.inProgress":"प्रगति में","mission.completed":"पूर्ण","mission.progress":"मिशन प्रगति (%)","simulation.title":"सिमुलेशन परिणाम","simulation.subtitle":"3D प्रक्षेपवक्र विश्लेषण और सटीकता परीक्षण","simulation.accuracyComparison":"सटीकता तुलना","simulation.testResults":"परीक्षण परिणाम","simulation.brahmosTest":"ब्रह्मोस परीक्षण","simulation.agniTest":"अग्नि-V परीक्षण","simulation.prithviTest":"पृथ्वी परीक्षण","simulation.supersonicCruise":"सुपरसोनिक क्रूज मिसाइल","simulation.intercontinental":"अंतरमहाद्वीपीय बैलिस्टिक मिसाइल","simulation.surfaceToSurface":"सतह से सतह मिसाइल","simulation.accuracy":"सटीकता (%)","settings.title":"सिस्टम कॉन्फ़िगरेशन","settings.subtitle":"डैशबोर्ड सेटिंग्स और प्राथमिकताएं","settings.themeConfig":"थीम कॉन्फ़िगरेशन","settings.securitySettings":"सुरक्षा सेटिंग्स","settings.dashboardTheme":"डैशबोर्ड थीम","settings.drdoFormal":"डीआरडीओ औपचारिक (डिफ़ॉल्ट)","settings.lightMode":"लाइट मोड","settings.darkMode":"डार्क मोड","settings.tacticalMode":"टैक्टिकल मोड","settings.currentTheme":"वर्तमान थीम","settings.changesApply":"परिवर्तन तुरंत सभी मॉड्यूल में लागू होते हैं","settings.aesEncryption":"AES-256 एन्क्रिप्शन","settings.dataTransmissions":"सभी डेटा ट्रांसमिशन एन्क्रिप्टेड","settings.sessionTimeout":"सेशन टाइमआउट","settings.idleTimeout":"15 मिनट निष्क्रिय टाइमआउट","settings.auditLogging":"ऑडिट लॉगिंग","settings.actionsLogged":"सभी कार्य लॉग और निगरानी","settings.adminAccess":"एडमिन एक्सेस","security.secureConnection":"सुरक्षित कनेक्शन","footer.copyright":"© 2025 डीआरडीओ | वर्गीकृत","footer.version":"v2.0 सैन्य","status.excellentStatus":"उत्कृष्ट स्थिति","status.mediumPriority":"मध्यम प्राथमिकता","status.aboveTarget":"लक्ष्य से ऊपर","status.operational":"परिचालनात्मक","status.inProgress":"प्रगति में","status.completed":"पूर्ण","status.fromYesterday":"कल से"}}};Le.use(Fm).use(T0).init({resources:Yb,lng:"en",fallbackLng:"en",interpolation:{escapeValue:!1},detection:{order:["localStorage","navigator","htmlTag"],caches:["localStorage"]}});qa.createRoot(document.getElementById("root")).render(_.jsx(zo.StrictMode,{children:_.jsx(fb,{})}));
//# sourceMappingURL=index-BWK3hR4x.js.map
