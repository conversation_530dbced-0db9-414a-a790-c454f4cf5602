// Generated by dts-bundle-generator v9.5.1

import Point from '@mapbox/point-geometry';
import TinySDF from '@mapbox/tiny-sdf';
import { VectorTileFeature, VectorTileLayer } from '@mapbox/vector-tile';
import { Color, CompositeExpression, DiffCommand, DiffOperations, Feature, FeatureFilter, FeatureState, FilterSpecification, Formatted, FormattedSection, GeoJSONSourceSpecification, GlobalProperties, ICanonicalTileID, IMercatorCoordinate, ImageSourceSpecification, InterpolationType, LayerSpecification, LightSpecification, Padding, PromoteIdSpecification, PropertyValueSpecification, RasterDEMSourceSpecification, RasterSourceSpecification, ResolvedImage, SkySpecification, SourceExpression, SourceSpecification, SpriteSpecification, StylePropertyExpression, StylePropertySpecification, StyleSpecification, TerrainSpecification, TransitionSpecification, VariableAnchorOffsetCollection, VectorSourceSpecification, VideoSourceSpecification } from '@maplibre/maplibre-gl-style-spec';
import { Options as GeoJSONVTOptions } from 'geojson-vt';
import { mat2, mat4, vec4 } from 'gl-matrix';
import KDBush from 'kdbush';
import { PotpackBox } from 'potpack';
import { ClusterProperties, Options as SuperclusterOptions } from 'supercluster';

/**
 * A type used to store the tile's expiration date and cache control definition
 */
export type ExpiryData = {
	cacheControl?: string | null;
	expires?: Date | string | null;
};
/**
 * A `RequestParameters` object to be returned from Map.options.transformRequest callbacks.
 * @example
 * ```ts
 * // use transformRequest to modify requests that begin with `http://myHost`
 * transformRequest: function(url, resourceType) {
 *  if (resourceType === 'Source' && url.indexOf('http://myHost') > -1) {
 *    return {
 *      url: url.replace('http', 'https'),
 *      headers: { 'my-custom-header': true },
 *      credentials: 'include'  // Include cookies for cross-origin requests
 *    }
 *   }
 * }
 * ```
 */
export type RequestParameters = {
	/**
	 * The URL to be requested.
	 */
	url: string;
	/**
	 * The headers to be sent with the request.
	 */
	headers?: any;
	/**
	 * Request method `'GET' | 'POST' | 'PUT'`.
	 */
	method?: "GET" | "POST" | "PUT";
	/**
	 * Request body.
	 */
	body?: string;
	/**
	 * Response body type to be returned.
	 */
	type?: "string" | "json" | "arrayBuffer" | "image";
	/**
	 * `'same-origin'|'include'` Use 'include' to send cookies with cross-origin requests.
	 */
	credentials?: "same-origin" | "include";
	/**
	 * If `true`, Resource Timing API information will be collected for these transformed requests and returned in a resourceTiming property of relevant data events.
	 */
	collectResourceTiming?: boolean;
	/**
	 * Parameters supported only by browser fetch API. Property of the Request interface contains the cache mode of the request. It controls how the request will interact with the browser's HTTP cache. (https://developer.mozilla.org/en-US/docs/Web/API/Request/cache)
	 */
	cache?: RequestCache;
};
/**
 * The response object returned from a successful AJAx request
 */
export type GetResourceResponse<T> = ExpiryData & {
	data: T;
};
/**
 * An error thrown when a HTTP request results in an error response.
 */
export declare class AJAXError extends Error {
	/**
	 * The response's HTTP status code.
	 */
	status: number;
	/**
	 * The response's HTTP status text.
	 */
	statusText: string;
	/**
	 * The request's URL.
	 */
	url: string;
	/**
	 * The response's body.
	 */
	body: Blob;
	/**
	 * @param status - The response's HTTP status code.
	 * @param statusText - The response's HTTP status text.
	 * @param url - The request's URL.
	 * @param body - The response's body.
	 */
	constructor(status: number, statusText: string, url: string, body: Blob);
}
/**
 * This method type is used to register a protocol handler.
 * Use the abort controller for aborting requests.
 * Return a promise with the relevant resource response.
 */
export type AddProtocolAction = (requestParameters: RequestParameters, abortController: AbortController) => Promise<GetResourceResponse<any>>;
/**
 * This is a global config object used to store the configuration
 * It is available in the workers as well.
 * Only serializable data should be stored in it.
 */
export type Config = {
	MAX_PARALLEL_IMAGE_REQUESTS: number;
	MAX_PARALLEL_IMAGE_REQUESTS_PER_FRAME: number;
	MAX_TILE_CACHE_ZOOM_LEVELS: number;
	REGISTERED_PROTOCOLS: {
		[x: string]: AddProtocolAction;
	};
	WORKER_URL: string;
};
export declare const config: Config;
/**
 * A class that is serialized to and json, that can be constructed back to the original class in the worker or in the main thread
 */
export type SerializedObject<S extends Serialized = any> = {
	[_: string]: S;
};
/**
 * All the possible values that can be serialized and sent to and from the worker
 */
export type Serialized = null | void | boolean | number | string | Boolean | Number | String | Date | RegExp | ArrayBuffer | ArrayBufferView | ImageData | ImageBitmap | Blob | Array<Serialized> | SerializedObject;
declare class ThrottledInvoker {
	_channel: MessageChannel;
	_triggered: boolean;
	_methodToThrottle: Function;
	constructor(methodToThrottle: Function);
	trigger(): void;
	remove(): void;
}
declare const viewTypes: {
	Int8: Int8ArrayConstructor;
	Uint8: Uint8ArrayConstructor;
	Int16: Int16ArrayConstructor;
	Uint16: Uint16ArrayConstructor;
	Int32: Int32ArrayConstructor;
	Uint32: Uint32ArrayConstructor;
	Float32: Float32ArrayConstructor;
};
/**
 * @internal
 * A view type size
 */
export type ViewType = keyof typeof viewTypes;
declare class Struct {
	_pos1: number;
	_pos2: number;
	_pos4: number;
	_pos8: number;
	readonly _structArray: StructArray;
	size: number;
	/**
	 * @param structArray - The StructArray the struct is stored in
	 * @param index - The index of the struct in the StructArray.
	 */
	constructor(structArray: StructArray, index: number);
}
/**
 * @internal
 * A struct array member
 */
export type StructArrayMember = {
	name: string;
	type: ViewType;
	components: number;
	offset: number;
};
/**
 * An array that can be deserialized
 */
export type SerializedStructArray = {
	length: number;
	arrayBuffer: ArrayBuffer;
};
declare abstract class StructArray {
	capacity: number;
	length: number;
	isTransferred: boolean;
	arrayBuffer: ArrayBuffer;
	uint8: Uint8Array;
	members: Array<StructArrayMember>;
	bytesPerElement: number;
	abstract emplaceBack(...v: number[]): any;
	abstract emplace(i: number, ...v: number[]): any;
	constructor();
	/**
	 * Serialize a StructArray instance.  Serializes both the raw data and the
	 * metadata needed to reconstruct the StructArray base class during
	 * deserialization.
	 */
	static serialize(array: StructArray, transferables?: Array<Transferable>): SerializedStructArray;
	static deserialize(input: SerializedStructArray): any;
	/**
	 * Resize the array to discard unused capacity.
	 */
	_trim(): void;
	/**
	 * Resets the length of the array to 0 without de-allocating capacity.
	 */
	clear(): void;
	/**
	 * Resize the array.
	 * If `n` is greater than the current length then additional elements with undefined values are added.
	 * If `n` is less than the current length then the array will be reduced to the first `n` elements.
	 * @param n - The new size of the array.
	 */
	resize(n: number): void;
	/**
	 * Indicate a planned increase in size, so that any necessary allocation may
	 * be done once, ahead of time.
	 * @param n - The expected size of the array.
	 */
	reserve(n: number): void;
	/**
	 * Create TypedArray views for the current ArrayBuffer.
	 */
	_refreshViews(): void;
}
declare class StructArrayLayout2i4 extends StructArray {
	uint8: Uint8Array;
	int16: Int16Array;
	_refreshViews(): void;
	emplaceBack(v0: number, v1: number): number;
	emplace(i: number, v0: number, v1: number): number;
}
declare class StructArrayLayout3i6 extends StructArray {
	uint8: Uint8Array;
	int16: Int16Array;
	_refreshViews(): void;
	emplaceBack(v0: number, v1: number, v2: number): number;
	emplace(i: number, v0: number, v1: number, v2: number): number;
}
declare class StructArrayLayout4i8 extends StructArray {
	uint8: Uint8Array;
	int16: Int16Array;
	_refreshViews(): void;
	emplaceBack(v0: number, v1: number, v2: number, v3: number): number;
	emplace(i: number, v0: number, v1: number, v2: number, v3: number): number;
}
declare class StructArrayLayout2i4i12 extends StructArray {
	uint8: Uint8Array;
	int16: Int16Array;
	_refreshViews(): void;
	emplaceBack(v0: number, v1: number, v2: number, v3: number, v4: number, v5: number): number;
	emplace(i: number, v0: number, v1: number, v2: number, v3: number, v4: number, v5: number): number;
}
declare class StructArrayLayout2i4ub8 extends StructArray {
	uint8: Uint8Array;
	int16: Int16Array;
	_refreshViews(): void;
	emplaceBack(v0: number, v1: number, v2: number, v3: number, v4: number, v5: number): number;
	emplace(i: number, v0: number, v1: number, v2: number, v3: number, v4: number, v5: number): number;
}
declare class StructArrayLayout2f8 extends StructArray {
	uint8: Uint8Array;
	float32: Float32Array;
	_refreshViews(): void;
	emplaceBack(v0: number, v1: number): number;
	emplace(i: number, v0: number, v1: number): number;
}
declare class StructArrayLayout4i4ui4i24 extends StructArray {
	uint8: Uint8Array;
	int16: Int16Array;
	uint16: Uint16Array;
	_refreshViews(): void;
	emplaceBack(v0: number, v1: number, v2: number, v3: number, v4: number, v5: number, v6: number, v7: number, v8: number, v9: number, v10: number, v11: number): number;
	emplace(i: number, v0: number, v1: number, v2: number, v3: number, v4: number, v5: number, v6: number, v7: number, v8: number, v9: number, v10: number, v11: number): number;
}
declare class StructArrayLayout3f12 extends StructArray {
	uint8: Uint8Array;
	float32: Float32Array;
	_refreshViews(): void;
	emplaceBack(v0: number, v1: number, v2: number): number;
	emplace(i: number, v0: number, v1: number, v2: number): number;
}
declare class StructArrayLayout1ul4 extends StructArray {
	uint8: Uint8Array;
	uint32: Uint32Array;
	_refreshViews(): void;
	emplaceBack(v0: number): number;
	emplace(i: number, v0: number): number;
}
declare class StructArrayLayout6i1ul2ui20 extends StructArray {
	uint8: Uint8Array;
	int16: Int16Array;
	uint32: Uint32Array;
	uint16: Uint16Array;
	_refreshViews(): void;
	emplaceBack(v0: number, v1: number, v2: number, v3: number, v4: number, v5: number, v6: number, v7: number, v8: number): number;
	emplace(i: number, v0: number, v1: number, v2: number, v3: number, v4: number, v5: number, v6: number, v7: number, v8: number): number;
}
declare class StructArrayLayout2ub2f2i16 extends StructArray {
	uint8: Uint8Array;
	float32: Float32Array;
	int16: Int16Array;
	_refreshViews(): void;
	emplaceBack(v0: number, v1: number, v2: number, v3: number, v4: number, v5: number): number;
	emplace(i: number, v0: number, v1: number, v2: number, v3: number, v4: number, v5: number): number;
}
declare class StructArrayLayout3ui6 extends StructArray {
	uint8: Uint8Array;
	uint16: Uint16Array;
	_refreshViews(): void;
	emplaceBack(v0: number, v1: number, v2: number): number;
	emplace(i: number, v0: number, v1: number, v2: number): number;
}
declare class StructArrayLayout2i2ui3ul3ui2f3ub1ul1i48 extends StructArray {
	uint8: Uint8Array;
	int16: Int16Array;
	uint16: Uint16Array;
	uint32: Uint32Array;
	float32: Float32Array;
	_refreshViews(): void;
	emplaceBack(v0: number, v1: number, v2: number, v3: number, v4: number, v5: number, v6: number, v7: number, v8: number, v9: number, v10: number, v11: number, v12: number, v13: number, v14: number, v15: number, v16: number): number;
	emplace(i: number, v0: number, v1: number, v2: number, v3: number, v4: number, v5: number, v6: number, v7: number, v8: number, v9: number, v10: number, v11: number, v12: number, v13: number, v14: number, v15: number, v16: number): number;
}
declare class StructArrayLayout8i15ui1ul2f2ui64 extends StructArray {
	uint8: Uint8Array;
	int16: Int16Array;
	uint16: Uint16Array;
	uint32: Uint32Array;
	float32: Float32Array;
	_refreshViews(): void;
	emplaceBack(v0: number, v1: number, v2: number, v3: number, v4: number, v5: number, v6: number, v7: number, v8: number, v9: number, v10: number, v11: number, v12: number, v13: number, v14: number, v15: number, v16: number, v17: number, v18: number, v19: number, v20: number, v21: number, v22: number, v23: number, v24: number, v25: number, v26: number, v27: number): number;
	emplace(i: number, v0: number, v1: number, v2: number, v3: number, v4: number, v5: number, v6: number, v7: number, v8: number, v9: number, v10: number, v11: number, v12: number, v13: number, v14: number, v15: number, v16: number, v17: number, v18: number, v19: number, v20: number, v21: number, v22: number, v23: number, v24: number, v25: number, v26: number, v27: number): number;
}
declare class StructArrayLayout1f4 extends StructArray {
	uint8: Uint8Array;
	float32: Float32Array;
	_refreshViews(): void;
	emplaceBack(v0: number): number;
	emplace(i: number, v0: number): number;
}
declare class StructArrayLayout1ui2f12 extends StructArray {
	uint8: Uint8Array;
	uint16: Uint16Array;
	float32: Float32Array;
	_refreshViews(): void;
	emplaceBack(v0: number, v1: number, v2: number): number;
	emplace(i: number, v0: number, v1: number, v2: number): number;
}
declare class StructArrayLayout1ul2ui8 extends StructArray {
	uint8: Uint8Array;
	uint32: Uint32Array;
	uint16: Uint16Array;
	_refreshViews(): void;
	emplaceBack(v0: number, v1: number, v2: number): number;
	emplace(i: number, v0: number, v1: number, v2: number): number;
}
declare class StructArrayLayout2ui4 extends StructArray {
	uint8: Uint8Array;
	uint16: Uint16Array;
	_refreshViews(): void;
	emplaceBack(v0: number, v1: number): number;
	emplace(i: number, v0: number, v1: number): number;
}
declare class StructArrayLayout1ui2 extends StructArray {
	uint8: Uint8Array;
	uint16: Uint16Array;
	_refreshViews(): void;
	emplaceBack(v0: number): number;
	emplace(i: number, v0: number): number;
}
declare class CollisionBoxStruct extends Struct {
	_structArray: CollisionBoxArray;
	get anchorPointX(): number;
	get anchorPointY(): number;
	get x1(): number;
	get y1(): number;
	get x2(): number;
	get y2(): number;
	get featureIndex(): number;
	get sourceLayerIndex(): number;
	get bucketIndex(): number;
	get anchorPoint(): Point;
}
declare class CollisionBoxArray extends StructArrayLayout6i1ul2ui20 {
	/**
	 * Return the CollisionBoxStruct at the given location in the array.
	 * @param index The index of the element.
	 */
	get(index: number): CollisionBoxStruct;
}
declare class PlacedSymbolStruct extends Struct {
	_structArray: PlacedSymbolArray;
	get anchorX(): number;
	get anchorY(): number;
	get glyphStartIndex(): number;
	get numGlyphs(): number;
	get vertexStartIndex(): number;
	get lineStartIndex(): number;
	get lineLength(): number;
	get segment(): number;
	get lowerSize(): number;
	get upperSize(): number;
	get lineOffsetX(): number;
	get lineOffsetY(): number;
	get writingMode(): number;
	get placedOrientation(): number;
	set placedOrientation(x: number);
	get hidden(): number;
	set hidden(x: number);
	get crossTileID(): number;
	set crossTileID(x: number);
	get associatedIconIndex(): number;
}
declare class PlacedSymbolArray extends StructArrayLayout2i2ui3ul3ui2f3ub1ul1i48 {
	/**
	 * Return the PlacedSymbolStruct at the given location in the array.
	 * @param index The index of the element.
	 */
	get(index: number): PlacedSymbolStruct;
}
declare class SymbolInstanceStruct extends Struct {
	_structArray: SymbolInstanceArray;
	get anchorX(): number;
	get anchorY(): number;
	get rightJustifiedTextSymbolIndex(): number;
	get centerJustifiedTextSymbolIndex(): number;
	get leftJustifiedTextSymbolIndex(): number;
	get verticalPlacedTextSymbolIndex(): number;
	get placedIconSymbolIndex(): number;
	get verticalPlacedIconSymbolIndex(): number;
	get key(): number;
	get textBoxStartIndex(): number;
	get textBoxEndIndex(): number;
	get verticalTextBoxStartIndex(): number;
	get verticalTextBoxEndIndex(): number;
	get iconBoxStartIndex(): number;
	get iconBoxEndIndex(): number;
	get verticalIconBoxStartIndex(): number;
	get verticalIconBoxEndIndex(): number;
	get featureIndex(): number;
	get numHorizontalGlyphVertices(): number;
	get numVerticalGlyphVertices(): number;
	get numIconVertices(): number;
	get numVerticalIconVertices(): number;
	get useRuntimeCollisionCircles(): number;
	get crossTileID(): number;
	set crossTileID(x: number);
	get textBoxScale(): number;
	get collisionCircleDiameter(): number;
	get textAnchorOffsetStartIndex(): number;
	get textAnchorOffsetEndIndex(): number;
}
export type SymbolInstance = SymbolInstanceStruct;
declare class SymbolInstanceArray extends StructArrayLayout8i15ui1ul2f2ui64 {
	/**
	 * Return the SymbolInstanceStruct at the given location in the array.
	 * @param index The index of the element.
	 */
	get(index: number): SymbolInstanceStruct;
}
declare class GlyphOffsetArray extends StructArrayLayout1f4 {
	getoffsetX(index: number): number;
}
declare class SymbolLineVertexArray extends StructArrayLayout3i6 {
	getx(index: number): number;
	gety(index: number): number;
	gettileUnitDistanceFromAnchor(index: number): number;
}
declare class TextAnchorOffsetStruct extends Struct {
	_structArray: TextAnchorOffsetArray;
	get textAnchor(): number;
	get textOffset0(): number;
	get textOffset1(): number;
}
export type TextAnchorOffset = TextAnchorOffsetStruct;
declare class TextAnchorOffsetArray extends StructArrayLayout1ui2f12 {
	/**
	 * Return the TextAnchorOffsetStruct at the given location in the array.
	 * @param index The index of the element.
	 */
	get(index: number): TextAnchorOffsetStruct;
}
declare class FeatureIndexStruct extends Struct {
	_structArray: FeatureIndexArray;
	get featureIndex(): number;
	get sourceLayerIndex(): number;
	get bucketIndex(): number;
}
declare class FeatureIndexArray extends StructArrayLayout1ul2ui8 {
	/**
	 * Return the FeatureIndexStruct at the given location in the array.
	 * @param index The index of the element.
	 */
	get(index: number): FeatureIndexStruct;
}
declare class PosArray extends StructArrayLayout2i4 {
}
declare class RasterBoundsArray extends StructArrayLayout4i8 {
}
declare class CircleLayoutArray extends StructArrayLayout2i4 {
}
declare class FillLayoutArray extends StructArrayLayout2i4 {
}
declare class FillExtrusionLayoutArray extends StructArrayLayout2i4i12 {
}
declare class LineLayoutArray extends StructArrayLayout2i4ub8 {
}
declare class LineExtLayoutArray extends StructArrayLayout2f8 {
}
declare class SymbolLayoutArray extends StructArrayLayout4i4ui4i24 {
}
declare class SymbolDynamicLayoutArray extends StructArrayLayout3f12 {
}
declare class SymbolOpacityArray extends StructArrayLayout1ul4 {
}
declare class CollisionVertexArray extends StructArrayLayout2ub2f2i16 {
}
declare class TriangleIndexArray extends StructArrayLayout3ui6 {
}
declare class LineIndexArray extends StructArrayLayout2ui4 {
}
declare class LineStripIndexArray extends StructArrayLayout1ui2 {
}
/**
 * A {@link LngLat} object, an array of two numbers representing longitude and latitude,
 * or an object with `lng` and `lat` or `lon` and `lat` properties.
 *
 * @group Geography and Geometry
 *
 * @example
 * ```ts
 * let v1 = new LngLat(-122.420679, 37.772537);
 * let v2 = [-122.420679, 37.772537];
 * let v3 = {lon: -122.420679, lat: 37.772537};
 * ```
 */
export type LngLatLike = LngLat | {
	lng: number;
	lat: number;
} | {
	lon: number;
	lat: number;
} | [
	number,
	number
];
/**
 * A `LngLat` object represents a given longitude and latitude coordinate, measured in degrees.
 * These coordinates are based on the [WGS84 (EPSG:4326) standard](https://en.wikipedia.org/wiki/World_Geodetic_System#WGS84).
 *
 * MapLibre GL JS uses longitude, latitude coordinate order (as opposed to latitude, longitude) to match the
 * [GeoJSON specification](https://tools.ietf.org/html/rfc7946).
 *
 * Note that any MapLibre GL JS method that accepts a `LngLat` object as an argument or option
 * can also accept an `Array` of two numbers and will perform an implicit conversion.
 * This flexible type is documented as {@link LngLatLike}.
 *
 * @group Geography and Geometry
 *
 * @example
 * ```ts
 * let ll = new LngLat(-123.9749, 40.7736);
 * ll.lng; // = -123.9749
 * ```
 * @see [Get coordinates of the mouse pointer](https://maplibre.org/maplibre-gl-js/docs/examples/mouse-position/)
 * @see [Display a popup](https://maplibre.org/maplibre-gl-js/docs/examples/popup/)
 * @see [Create a timeline animation](https://maplibre.org/maplibre-gl-js/docs/examples/timeline-animation/)
 */
export declare class LngLat {
	lng: number;
	lat: number;
	/**
	 * @param lng - Longitude, measured in degrees.
	 * @param lat - Latitude, measured in degrees.
	 */
	constructor(lng: number, lat: number);
	/**
	 * Returns a new `LngLat` object whose longitude is wrapped to the range (-180, 180).
	 *
	 * @returns The wrapped `LngLat` object.
	 * @example
	 * ```ts
	 * let ll = new LngLat(286.0251, 40.7736);
	 * let wrapped = ll.wrap();
	 * wrapped.lng; // = -73.9749
	 * ```
	 */
	wrap(): LngLat;
	/**
	 * Returns the coordinates represented as an array of two numbers.
	 *
	 * @returns The coordinates represented as an array of longitude and latitude.
	 * @example
	 * ```ts
	 * let ll = new LngLat(-73.9749, 40.7736);
	 * ll.toArray(); // = [-73.9749, 40.7736]
	 * ```
	 */
	toArray(): [
		number,
		number
	];
	/**
	 * Returns the coordinates represent as a string.
	 *
	 * @returns The coordinates represented as a string of the format `'LngLat(lng, lat)'`.
	 * @example
	 * ```ts
	 * let ll = new LngLat(-73.9749, 40.7736);
	 * ll.toString(); // = "LngLat(-73.9749, 40.7736)"
	 * ```
	 */
	toString(): string;
	/**
	 * Returns the approximate distance between a pair of coordinates in meters
	 * Uses the Haversine Formula (from R.W. Sinnott, "Virtues of the Haversine", Sky and Telescope, vol. 68, no. 2, 1984, p. 159)
	 *
	 * @param lngLat - coordinates to compute the distance to
	 * @returns Distance in meters between the two coordinates.
	 * @example
	 * ```ts
	 * let new_york = new LngLat(-74.0060, 40.7128);
	 * let los_angeles = new LngLat(-118.2437, 34.0522);
	 * new_york.distanceTo(los_angeles); // = 3935751.690893987, "true distance" using a non-spherical approximation is ~3966km
	 * ```
	 */
	distanceTo(lngLat: LngLat): number;
	/**
	 * Converts an array of two numbers or an object with `lng` and `lat` or `lon` and `lat` properties
	 * to a `LngLat` object.
	 *
	 * If a `LngLat` object is passed in, the function returns it unchanged.
	 *
	 * @param input - An array of two numbers or object to convert, or a `LngLat` object to return.
	 * @returns A new `LngLat` object, if a conversion occurred, or the original `LngLat` object.
	 * @example
	 * ```ts
	 * let arr = [-73.9749, 40.7736];
	 * let ll = LngLat.convert(arr);
	 * ll;   // = LngLat {lng: -73.9749, lat: 40.7736}
	 * ```
	 */
	static convert(input: LngLatLike): LngLat;
}
/**
 * A `MercatorCoordinate` object represents a projected three dimensional position.
 *
 * `MercatorCoordinate` uses the web mercator projection ([EPSG:3857](https://epsg.io/3857)) with slightly different units:
 *
 * - the size of 1 unit is the width of the projected world instead of the "mercator meter"
 * - the origin of the coordinate space is at the north-west corner instead of the middle
 *
 * For example, `MercatorCoordinate(0, 0, 0)` is the north-west corner of the mercator world and
 * `MercatorCoordinate(1, 1, 0)` is the south-east corner. If you are familiar with
 * [vector tiles](https://github.com/mapbox/vector-tile-spec) it may be helpful to think
 * of the coordinate space as the `0/0/0` tile with an extent of `1`.
 *
 * The `z` dimension of `MercatorCoordinate` is conformal. A cube in the mercator coordinate space would be rendered as a cube.
 *
 * @group Geography and Geometry
 *
 * @example
 * ```ts
 * let nullIsland = new MercatorCoordinate(0.5, 0.5, 0);
 * ```
 * @see [Add a custom style layer](https://maplibre.org/maplibre-gl-js/docs/examples/custom-style-layer/)
 */
export declare class MercatorCoordinate implements IMercatorCoordinate {
	x: number;
	y: number;
	z: number;
	/**
	 * @param x - The x component of the position.
	 * @param y - The y component of the position.
	 * @param z - The z component of the position.
	 */
	constructor(x: number, y: number, z?: number);
	/**
	 * Project a `LngLat` to a `MercatorCoordinate`.
	 *
	 * @param lngLatLike - The location to project.
	 * @param altitude - The altitude in meters of the position.
	 * @returns The projected mercator coordinate.
	 * @example
	 * ```ts
	 * let coord = MercatorCoordinate.fromLngLat({ lng: 0, lat: 0}, 0);
	 * coord; // MercatorCoordinate(0.5, 0.5, 0)
	 * ```
	 */
	static fromLngLat(lngLatLike: LngLatLike, altitude?: number): MercatorCoordinate;
	/**
	 * Returns the `LngLat` for the coordinate.
	 *
	 * @returns The `LngLat` object.
	 * @example
	 * ```ts
	 * let coord = new MercatorCoordinate(0.5, 0.5, 0);
	 * let lngLat = coord.toLngLat(); // LngLat(0, 0)
	 * ```
	 */
	toLngLat(): LngLat;
	/**
	 * Returns the altitude in meters of the coordinate.
	 *
	 * @returns The altitude in meters.
	 * @example
	 * ```ts
	 * let coord = new MercatorCoordinate(0, 0, 0.02);
	 * coord.toAltitude(); // 6914.281956295339
	 * ```
	 */
	toAltitude(): number;
	/**
	 * Returns the distance of 1 meter in `MercatorCoordinate` units at this latitude.
	 *
	 * For coordinates in real world units using meters, this naturally provides the scale
	 * to transform into `MercatorCoordinate`s.
	 *
	 * @returns Distance of 1 meter in `MercatorCoordinate` units.
	 */
	meterInMercatorCoordinateUnits(): number;
}
declare class CanonicalTileID implements ICanonicalTileID {
	z: number;
	x: number;
	y: number;
	key: string;
	constructor(z: number, x: number, y: number);
	equals(id: ICanonicalTileID): boolean;
	url(urls: Array<string>, pixelRatio: number, scheme?: string | null): string;
	isChildOf(parent: ICanonicalTileID): boolean;
	getTilePoint(coord: IMercatorCoordinate): Point;
	toString(): string;
}
declare class UnwrappedTileID {
	wrap: number;
	canonical: CanonicalTileID;
	key: string;
	constructor(wrap: number, canonical: CanonicalTileID);
}
declare class OverscaledTileID {
	overscaledZ: number;
	wrap: number;
	canonical: CanonicalTileID;
	key: string;
	posMatrix: mat4;
	constructor(overscaledZ: number, wrap: number, z: number, x: number, y: number);
	clone(): OverscaledTileID;
	equals(id: OverscaledTileID): boolean;
	scaledTo(targetZ: number): OverscaledTileID;
	calculateScaledKey(targetZ: number, withWrap: boolean): string;
	isChildOf(parent: OverscaledTileID): boolean;
	children(sourceMaxZoom: number): OverscaledTileID[];
	isLessThan(rhs: OverscaledTileID): boolean;
	wrapped(): OverscaledTileID;
	unwrapTo(wrap: number): OverscaledTileID;
	overscaleFactor(): number;
	toUnwrapped(): UnwrappedTileID;
	toString(): string;
	getTilePoint(coord: MercatorCoordinate): Point;
}
/**
 * A listener method used as a callback to events
 */
export type Listener = (a: any) => any;
export type Listeners = {
	[_: string]: Array<Listener>;
};
declare class Event$1 {
	readonly type: string;
	constructor(type: string, data?: any);
}
/**
 * Methods mixed in to other classes for event capabilities.
 *
 * @group Event Related
 */
export declare class Evented {
	_listeners: Listeners;
	_oneTimeListeners: Listeners;
	_eventedParent: Evented;
	_eventedParentData: any | (() => any);
	/**
	 * Adds a listener to a specified event type.
	 *
	 * @param type - The event type to add a listen for.
	 * @param listener - The function to be called when the event is fired.
	 * The listener function is called with the data object passed to `fire`,
	 * extended with `target` and `type` properties.
	 */
	on(type: string, listener: Listener): this;
	/**
	 * Removes a previously registered event listener.
	 *
	 * @param type - The event type to remove listeners for.
	 * @param listener - The listener function to remove.
	 */
	off(type: string, listener: Listener): this;
	/**
	 * Adds a listener that will be called only once to a specified event type.
	 *
	 * The listener will be called first time the event fires after the listener is registered.
	 *
	 * @param type - The event type to listen for.
	 * @param listener - The function to be called when the event is fired the first time.
	 * @returns `this` or a promise if a listener is not provided
	 */
	once(type: string, listener?: Listener): this | Promise<any>;
	fire(event: Event$1 | string, properties?: any): this;
	/**
	 * Returns a true if this instance of Evented or any forwardeed instances of Evented have a listener for the specified type.
	 *
	 * @param type - The event type
	 * @returns `true` if there is at least one registered listener for specified event type, `false` otherwise
	 */
	listens(type: string): boolean;
	/**
	 * Bubble all events fired by this instance of Evented to this parent instance of Evented.
	 */
	setEventedParent(parent?: Evented | null, data?: any | (() => any)): this;
}
declare class ZoomHistory {
	lastZoom: number;
	lastFloorZoom: number;
	lastIntegerZoom: number;
	lastIntegerZoomTime: number;
	first: boolean;
	constructor();
	update(z: number, now: number): boolean;
}
export type CrossfadeParameters = {
	fromScale: number;
	toScale: number;
	t: number;
};
declare class EvaluationParameters {
	zoom: number;
	now: number;
	fadeDuration: number;
	zoomHistory: ZoomHistory;
	transition: TransitionSpecification;
	constructor(zoom: number, options?: any);
	isSupportedScript(str: string): boolean;
	crossFadingFactor(): number;
	getCrossfadeParameters(): CrossfadeParameters;
}
export type TimePoint = number;
/**
 * A from-to type
 */
export type CrossFaded<T> = {
	to: T;
	from: T;
};
/**
 * @internal
 *  Implementations of the `Property` interface:
 *
 *  * Hold metadata about a property that's independent of any specific value: stuff like the type of the value,
 *    the default value, etc. This comes from the style specification JSON.
 *  * Define behavior that needs to be polymorphic across different properties: "possibly evaluating"
 *    an input value (see below), and interpolating between two possibly-evaluted values.
 *
 *  The type `T` is the fully-evaluated value type (e.g. `number`, `string`, `Color`).
 *  The type `R` is the intermediate "possibly evaluated" value type. See below.
 *
 *  There are two main implementations of the interface -- one for properties that allow data-driven values,
 *  and one for properties that don't. There are a few "special case" implementations as well: one for properties
 *  which cross-fade between two values rather than interpolating, one for `heatmap-color` and `line-gradient`,
 *  and one for `light-position`.
 */
export interface Property<T, R> {
	specification: StylePropertySpecification;
	possiblyEvaluate(value: PropertyValue<T, R>, parameters: EvaluationParameters, canonical?: CanonicalTileID, availableImages?: Array<string>): R;
	interpolate(a: R, b: R, t: number): R;
}
declare class PropertyValue<T, R> {
	property: Property<T, R>;
	value: PropertyValueSpecification<T> | void;
	expression: StylePropertyExpression;
	constructor(property: Property<T, R>, value: PropertyValueSpecification<T> | void);
	isDataDriven(): boolean;
	possiblyEvaluate(parameters: EvaluationParameters, canonical?: CanonicalTileID, availableImages?: Array<string>): R;
}
export type TransitionParameters = {
	now: TimePoint;
	transition: TransitionSpecification;
};
declare class TransitionablePropertyValue<T, R> {
	property: Property<T, R>;
	value: PropertyValue<T, R>;
	transition: TransitionSpecification | void;
	constructor(property: Property<T, R>);
	transitioned(parameters: TransitionParameters, prior: TransitioningPropertyValue<T, R>): TransitioningPropertyValue<T, R>;
	untransitioned(): TransitioningPropertyValue<T, R>;
}
declare class Transitionable<Props> {
	_properties: Properties<Props>;
	_values: {
		[K in keyof Props]: TransitionablePropertyValue<any, unknown>;
	};
	constructor(properties: Properties<Props>);
	getValue<S extends keyof Props, T>(name: S): PropertyValueSpecification<T> | void;
	setValue<S extends keyof Props, T>(name: S, value: PropertyValueSpecification<T> | void): void;
	getTransition<S extends keyof Props>(name: S): TransitionSpecification | void;
	setTransition<S extends keyof Props>(name: S, value: TransitionSpecification | void): void;
	serialize(): any;
	transitioned(parameters: TransitionParameters, prior: Transitioning<Props>): Transitioning<Props>;
	untransitioned(): Transitioning<Props>;
}
declare class TransitioningPropertyValue<T, R> {
	property: Property<T, R>;
	value: PropertyValue<T, R>;
	prior: TransitioningPropertyValue<T, R>;
	begin: TimePoint;
	end: TimePoint;
	constructor(property: Property<T, R>, value: PropertyValue<T, R>, prior: TransitioningPropertyValue<T, R>, transition: TransitionSpecification, now: TimePoint);
	possiblyEvaluate(parameters: EvaluationParameters, canonical: CanonicalTileID, availableImages: Array<string>): R;
}
declare class Transitioning<Props> {
	_properties: Properties<Props>;
	_values: {
		[K in keyof Props]: PossiblyEvaluatedPropertyValue<unknown>;
	};
	constructor(properties: Properties<Props>);
	possiblyEvaluate(parameters: EvaluationParameters, canonical?: CanonicalTileID, availableImages?: Array<string>): PossiblyEvaluated<Props, any>;
	hasTransition(): boolean;
}
declare class Layout<Props> {
	_properties: Properties<Props>;
	_values: {
		[K in keyof Props]: PropertyValue<any, PossiblyEvaluatedPropertyValue<any>>;
	};
	constructor(properties: Properties<Props>);
	hasValue<S extends keyof Props>(name: S): boolean;
	getValue<S extends keyof Props>(name: S): any;
	setValue<S extends keyof Props>(name: S, value: any): void;
	serialize(): any;
	possiblyEvaluate(parameters: EvaluationParameters, canonical?: CanonicalTileID, availableImages?: Array<string>): PossiblyEvaluated<Props, any>;
}
/**
 * "Possibly evaluated value" is an intermediate stage in the evaluation chain for both paint and layout property
 * values. The purpose of this stage is to optimize away unnecessary recalculations for data-driven properties. Code
 * which uses data-driven property values must assume that the value is dependent on feature data, and request that it
 * be evaluated for each feature. But when that property value is in fact a constant or camera function, the calculation
 * will not actually depend on the feature, and we can benefit from returning the prior result of having done the
 * evaluation once, ahead of time, in an intermediate step whose inputs are just the value and "global" parameters
 * such as current zoom level.
 *
 * `PossiblyEvaluatedValue` represents the three possible outcomes of this step: if the input value was a constant or
 * camera expression, then the "possibly evaluated" result is a constant value. Otherwise, the input value was either
 * a source or composite expression, and we must defer final evaluation until supplied a feature. We separate
 * the source and composite cases because they are handled differently when generating GL attributes, buffers, and
 * uniforms.
 *
 * Note that `PossiblyEvaluatedValue` (and `PossiblyEvaluatedPropertyValue`, below) are _not_ used for properties that
 * do not allow data-driven values. For such properties, we know that the "possibly evaluated" result is always a constant
 * scalar value. See below.
 */
export type PossiblyEvaluatedValue<T> = {
	kind: "constant";
	value: T;
} | SourceExpression | CompositeExpression;
declare class PossiblyEvaluatedPropertyValue<T> {
	property: DataDrivenProperty<T>;
	value: PossiblyEvaluatedValue<T>;
	parameters: EvaluationParameters;
	constructor(property: DataDrivenProperty<T>, value: PossiblyEvaluatedValue<T>, parameters: EvaluationParameters);
	isConstant(): boolean;
	constantOr(value: T): T;
	evaluate(feature: Feature, featureState: FeatureState, canonical?: CanonicalTileID, availableImages?: Array<string>): T;
}
declare class PossiblyEvaluated<Props, PossibleEvaluatedProps> {
	_properties: Properties<Props>;
	_values: PossibleEvaluatedProps;
	constructor(properties: Properties<Props>);
	get<S extends keyof PossibleEvaluatedProps>(name: S): PossibleEvaluatedProps[S];
}
declare class DataConstantProperty<T> implements Property<T, T> {
	specification: StylePropertySpecification;
	constructor(specification: StylePropertySpecification);
	possiblyEvaluate(value: PropertyValue<T, T>, parameters: EvaluationParameters): T;
	interpolate(a: T, b: T, t: number): T;
}
declare class DataDrivenProperty<T> implements Property<T, PossiblyEvaluatedPropertyValue<T>> {
	specification: StylePropertySpecification;
	overrides: any;
	constructor(specification: StylePropertySpecification, overrides?: any);
	possiblyEvaluate(value: PropertyValue<T, PossiblyEvaluatedPropertyValue<T>>, parameters: EvaluationParameters, canonical?: CanonicalTileID, availableImages?: Array<string>): PossiblyEvaluatedPropertyValue<T>;
	interpolate(a: PossiblyEvaluatedPropertyValue<T>, b: PossiblyEvaluatedPropertyValue<T>, t: number): PossiblyEvaluatedPropertyValue<T>;
	evaluate(value: PossiblyEvaluatedValue<T>, parameters: EvaluationParameters, feature: Feature, featureState: FeatureState, canonical?: CanonicalTileID, availableImages?: Array<string>): T;
}
declare class CrossFadedDataDrivenProperty<T> extends DataDrivenProperty<CrossFaded<T>> {
	possiblyEvaluate(value: PropertyValue<CrossFaded<T>, PossiblyEvaluatedPropertyValue<CrossFaded<T>>>, parameters: EvaluationParameters, canonical?: CanonicalTileID, availableImages?: Array<string>): PossiblyEvaluatedPropertyValue<CrossFaded<T>>;
	evaluate(value: PossiblyEvaluatedValue<CrossFaded<T>>, globals: EvaluationParameters, feature: Feature, featureState: FeatureState, canonical?: CanonicalTileID, availableImages?: Array<string>): CrossFaded<T>;
	_calculate(min: T, mid: T, max: T, parameters: EvaluationParameters): CrossFaded<T>;
	interpolate(a: PossiblyEvaluatedPropertyValue<CrossFaded<T>>): PossiblyEvaluatedPropertyValue<CrossFaded<T>>;
}
declare class CrossFadedProperty<T> implements Property<T, CrossFaded<T>> {
	specification: StylePropertySpecification;
	constructor(specification: StylePropertySpecification);
	possiblyEvaluate(value: PropertyValue<T, CrossFaded<T>>, parameters: EvaluationParameters, canonical?: CanonicalTileID, availableImages?: Array<string>): CrossFaded<T>;
	_calculate(min: T, mid: T, max: T, parameters: EvaluationParameters): CrossFaded<T>;
	interpolate(a?: CrossFaded<T> | null): CrossFaded<T>;
}
declare class ColorRampProperty implements Property<Color, boolean> {
	specification: StylePropertySpecification;
	constructor(specification: StylePropertySpecification);
	possiblyEvaluate(value: PropertyValue<Color, boolean>, parameters: EvaluationParameters, canonical?: CanonicalTileID, availableImages?: Array<string>): boolean;
	interpolate(): boolean;
}
declare class Properties<Props> {
	properties: Props;
	defaultPropertyValues: {
		[K in keyof Props]: PropertyValue<unknown, any>;
	};
	defaultTransitionablePropertyValues: {
		[K in keyof Props]: TransitionablePropertyValue<unknown, unknown>;
	};
	defaultTransitioningPropertyValues: {
		[K in keyof Props]: TransitioningPropertyValue<unknown, unknown>;
	};
	defaultPossiblyEvaluatedValues: {
		[K in keyof Props]: PossiblyEvaluatedPropertyValue<unknown>;
	};
	overridableProperties: Array<string>;
	constructor(properties: Props);
}
export type Size = {
	width: number;
	height: number;
};
export type Point2D = {
	x: number;
	y: number;
};
declare class AlphaImage {
	width: number;
	height: number;
	data: Uint8Array;
	constructor(size: Size, data?: Uint8Array | Uint8ClampedArray);
	resize(size: Size): void;
	clone(): AlphaImage;
	static copy(srcImg: AlphaImage, dstImg: AlphaImage, srcPt: Point2D, dstPt: Point2D, size: Size): void;
}
declare class RGBAImage {
	width: number;
	height: number;
	/**
	 * data must be a Uint8Array instead of Uint8ClampedArray because texImage2D does not support Uint8ClampedArray in all browsers.
	 */
	data: Uint8Array;
	constructor(size: Size, data?: Uint8Array | Uint8ClampedArray);
	resize(size: Size): void;
	replace(data: Uint8Array | Uint8ClampedArray, copy?: boolean): void;
	clone(): RGBAImage;
	static copy(srcImg: RGBAImage | ImageData, dstImg: RGBAImage, srcPt: Point2D, dstPt: Point2D, size: Size): void;
}
/**
 * The sprite data
 */
export type SpriteOnDemandStyleImage = {
	width: number;
	height: number;
	x: number;
	y: number;
	context: CanvasRenderingContext2D;
};
/**
 * The style's image metadata
 */
export type StyleImageData = {
	data: RGBAImage;
	version?: number;
	hasRenderCallback?: boolean;
	userImage?: StyleImageInterface;
	spriteData?: SpriteOnDemandStyleImage;
};
/**
 * Enumeration of possible values for StyleImageMetadata.textFitWidth and textFitHeight.
 */
export declare const enum TextFit {
	/**
	 * The image will be resized on the specified axis to tightly fit the content rectangle to target text.
	 * This is the same as not being defined.
	 */
	stretchOrShrink = "stretchOrShrink",
	/**
	 * The image will be resized on the specified axis to fit the content rectangle to the target text, but will not
	 * fall below the aspect ratio of the original content rectangle if the other axis is set to proportional.
	 */
	stretchOnly = "stretchOnly",
	/**
	 * The image will be resized on the specified axis to fit the content rectangle to the target text and
	 * will resize the other axis to maintain the aspect ratio of the content rectangle.
	 */
	proportional = "proportional"
}
/**
 * The style's image metadata
 */
export type StyleImageMetadata = {
	/**
	 * The ratio of pixels in the image to physical pixels on the screen
	 */
	pixelRatio: number;
	/**
	 * Whether the image should be interpreted as an SDF image
	 */
	sdf: boolean;
	/**
	 * If `icon-text-fit` is used in a layer with this image, this option defines the part(s) of the image that can be stretched horizontally.
	 */
	stretchX?: Array<[
		number,
		number
	]>;
	/**
	 * If `icon-text-fit` is used in a layer with this image, this option defines the part(s) of the image that can be stretched vertically.
	 */
	stretchY?: Array<[
		number,
		number
	]>;
	/**
	 * If `icon-text-fit` is used in a layer with this image, this option defines the part of the image that can be covered by the content in `text-field`.
	 */
	content?: [
		number,
		number,
		number,
		number
	];
	/**
	 * If `icon-text-fit` is used in a layer with this image, this option defines constraints on the horizontal scaling of the image.
	 */
	textFitWidth?: TextFit;
	/**
	 * If `icon-text-fit` is used in a layer with this image, this option defines constraints on the vertical scaling of the image.
	 */
	textFitHeight?: TextFit;
};
/**
 * the style's image, including data and metedata
 */
export type StyleImage = StyleImageData & StyleImageMetadata;
/**
 * Interface for dynamically generated style images. This is a specification for
 * implementers to model: it is not an exported method or class.
 *
 * Images implementing this interface can be redrawn for every frame. They can be used to animate
 * icons and patterns or make them respond to user input. Style images can implement a
 * {@link StyleImageInterface#render} method. The method is called every frame and
 * can be used to update the image.
 *
 * @see [Add an animated icon to the map.](https://maplibre.org/maplibre-gl-js/docs/examples/add-image-animated/)
 *
 * @example
 * ```ts
 * let flashingSquare = {
 *     width: 64,
 *     height: 64,
 *     data: new Uint8Array(64 * 64 * 4),
 *
 *     onAdd: function(map) {
 *         this.map = map;
 *     },
 *
 *     render: function() {
 *         // keep repainting while the icon is on the map
 *         this.map.triggerRepaint();
 *
 *         // alternate between black and white based on the time
 *         let value = Math.round(Date.now() / 1000) % 2 === 0  ? 255 : 0;
 *
 *         // check if image needs to be changed
 *         if (value !== this.previousValue) {
 *             this.previousValue = value;
 *
 *             let bytesPerPixel = 4;
 *             for (let x = 0; x < this.width; x++) {
 *                 for (let y = 0; y < this.height; y++) {
 *                     let offset = (y * this.width + x) * bytesPerPixel;
 *                     this.data[offset + 0] = value;
 *                     this.data[offset + 1] = value;
 *                     this.data[offset + 2] = value;
 *                     this.data[offset + 3] = 255;
 *                 }
 *             }
 *
 *             // return true to indicate that the image changed
 *             return true;
 *         }
 *     }
 *  }
 *
 *  map.addImage('flashing_square', flashingSquare);
 * ```
 */
export interface StyleImageInterface {
	width: number;
	height: number;
	data: Uint8Array | Uint8ClampedArray;
	/**
	 * This method is called once before every frame where the icon will be used.
	 * The method can optionally update the image's `data` member with a new image.
	 *
	 * If the method updates the image it must return `true` to commit the change.
	 * If the method returns `false` or nothing the image is assumed to not have changed.
	 *
	 * If updates are infrequent it maybe easier to use {@link Map#updateImage} to update
	 * the image instead of implementing this method.
	 *
	 * @returns `true` if this method updated the image. `false` if the image was not changed.
	 */
	render?: () => boolean;
	/**
	 * Optional method called when the layer has been added to the Map with {@link Map#addImage}.
	 *
	 * @param map - The Map this custom layer was just added to.
	 */
	onAdd?: (map: Map$1, id: string) => void;
	/**
	 * Optional method called when the icon is removed from the map with {@link Map#removeImage}.
	 * This gives the image a chance to clean up resources and event listeners.
	 */
	onRemove?: () => void;
}
declare class IndexBuffer {
	context: Context;
	buffer: WebGLBuffer;
	dynamicDraw: boolean;
	constructor(context: Context, array: TriangleIndexArray | LineIndexArray | LineStripIndexArray, dynamicDraw?: boolean);
	bind(): void;
	updateData(array: StructArray): void;
	destroy(): void;
}
export type SerializedFeaturePositionMap = {
	ids: Float64Array;
	positions: Uint32Array;
};
export type FeaturePosition = {
	index: number;
	start: number;
	end: number;
};
declare class FeaturePositionMap {
	ids: Array<number>;
	positions: Array<number>;
	indexed: boolean;
	constructor();
	add(id: unknown, index: number, start: number, end: number): void;
	getPositions(id: unknown): Array<FeaturePosition>;
	static serialize(map: FeaturePositionMap, transferables: Array<ArrayBuffer>): SerializedFeaturePositionMap;
	static deserialize(obj: SerializedFeaturePositionMap): FeaturePositionMap;
}
export type $ObjMap<T extends {}, F extends (v: any) => any> = {
	[K in keyof T]: F extends (v: T[K]) => infer R ? R : never;
};
export type UniformValues<Us extends {}> = $ObjMap<Us, <V>(u: Uniform<V>) => V>;
export type UniformLocations = {
	[_: string]: WebGLUniformLocation;
};
declare abstract class Uniform<T> {
	gl: WebGLRenderingContext | WebGL2RenderingContext;
	location: WebGLUniformLocation;
	current: T;
	constructor(context: Context, location: WebGLUniformLocation);
	abstract set(v: T): void;
}
declare class Uniform1i extends Uniform<number> {
	constructor(context: Context, location: WebGLUniformLocation);
	set(v: number): void;
}
declare class Uniform1f extends Uniform<number> {
	constructor(context: Context, location: WebGLUniformLocation);
	set(v: number): void;
}
declare class Uniform4f extends Uniform<vec4> {
	constructor(context: Context, location: WebGLUniformLocation);
	set(v: vec4): void;
}
declare class UniformMatrix4f extends Uniform<mat4> {
	constructor(context: Context, location: WebGLUniformLocation);
	set(v: mat4): void;
}
/**
 * @internal
 * A uniform bindings
 */
export type UniformBindings = {
	[_: string]: Uniform<any>;
};
declare class VertexArrayObject {
	context: Context;
	boundProgram: Program<any>;
	boundLayoutVertexBuffer: VertexBuffer;
	boundPaintVertexBuffers: Array<VertexBuffer>;
	boundIndexBuffer: IndexBuffer;
	boundVertexOffset: number;
	boundDynamicVertexBuffer: VertexBuffer;
	boundDynamicVertexBuffer2: VertexBuffer;
	boundDynamicVertexBuffer3: VertexBuffer;
	vao: any;
	constructor();
	bind(context: Context, program: Program<any>, layoutVertexBuffer: VertexBuffer, paintVertexBuffers: Array<VertexBuffer>, indexBuffer?: IndexBuffer | null, vertexOffset?: number | null, dynamicVertexBuffer?: VertexBuffer | null, dynamicVertexBuffer2?: VertexBuffer | null, dynamicVertexBuffer3?: VertexBuffer | null): void;
	freshBind(program: Program<any>, layoutVertexBuffer: VertexBuffer, paintVertexBuffers: Array<VertexBuffer>, indexBuffer?: IndexBuffer | null, vertexOffset?: number | null, dynamicVertexBuffer?: VertexBuffer | null, dynamicVertexBuffer2?: VertexBuffer | null, dynamicVertexBuffer3?: VertexBuffer | null): void;
	destroy(): void;
}
/**
 * @internal
 * A single segment of a vector
 */
export type Segment = {
	sortKey?: number;
	vertexOffset: number;
	primitiveOffset: number;
	vertexLength: number;
	primitiveLength: number;
	vaos: {
		[_: string]: VertexArrayObject;
	};
};
declare class SegmentVector {
	static MAX_VERTEX_ARRAY_LENGTH: number;
	segments: Array<Segment>;
	constructor(segments?: Array<Segment>);
	prepareSegment(numVertices: number, layoutVertexArray: StructArray, indexArray: StructArray, sortKey?: number): Segment;
	get(): Segment[];
	destroy(): void;
	static simpleSegment(vertexOffset: number, primitiveOffset: number, vertexLength: number, primitiveLength: number): SegmentVector;
}
declare class HeatmapBucket extends CircleBucket<HeatmapStyleLayer> {
	layers: Array<HeatmapStyleLayer>;
}
export type HeatmapPaintProps = {
	"heatmap-radius": DataDrivenProperty<number>;
	"heatmap-weight": DataDrivenProperty<number>;
	"heatmap-intensity": DataConstantProperty<number>;
	"heatmap-color": ColorRampProperty;
	"heatmap-opacity": DataConstantProperty<number>;
};
export type HeatmapPaintPropsPossiblyEvaluated = {
	"heatmap-radius": PossiblyEvaluatedPropertyValue<number>;
	"heatmap-weight": PossiblyEvaluatedPropertyValue<number>;
	"heatmap-intensity": number;
	"heatmap-color": ColorRampProperty;
	"heatmap-opacity": number;
};
export type BlendFuncConstant = WebGLRenderingContextBase["ZERO"] | WebGLRenderingContextBase["ONE"] | WebGLRenderingContextBase["SRC_COLOR"] | WebGLRenderingContextBase["ONE_MINUS_SRC_COLOR"] | WebGLRenderingContextBase["DST_COLOR"] | WebGLRenderingContextBase["ONE_MINUS_DST_COLOR"] | WebGLRenderingContextBase["SRC_ALPHA"] | WebGLRenderingContextBase["ONE_MINUS_SRC_ALPHA"] | WebGLRenderingContextBase["DST_ALPHA"] | WebGLRenderingContextBase["ONE_MINUS_DST_ALPHA"] | WebGLRenderingContextBase["CONSTANT_COLOR"] | WebGLRenderingContextBase["ONE_MINUS_CONSTANT_COLOR"] | WebGLRenderingContextBase["CONSTANT_ALPHA"] | WebGLRenderingContextBase["ONE_MINUS_CONSTANT_ALPHA"] | WebGLRenderingContextBase["BLEND_COLOR"];
export type BlendFuncType = [
	BlendFuncConstant,
	BlendFuncConstant
];
export type BlendEquationType = WebGLRenderingContextBase["FUNC_ADD"] | WebGLRenderingContextBase["FUNC_SUBTRACT"] | WebGLRenderingContextBase["FUNC_REVERSE_SUBTRACT"];
export type ColorMaskType = [
	boolean,
	boolean,
	boolean,
	boolean
];
export type CompareFuncType = WebGLRenderingContextBase["NEVER"] | WebGLRenderingContextBase["LESS"] | WebGLRenderingContextBase["EQUAL"] | WebGLRenderingContextBase["LEQUAL"] | WebGLRenderingContextBase["GREATER"] | WebGLRenderingContextBase["NOTEQUAL"] | WebGLRenderingContextBase["GEQUAL"] | WebGLRenderingContextBase["ALWAYS"];
export type DepthMaskType = boolean;
export type DepthRangeType = [
	number,
	number
];
export type DepthFuncType = CompareFuncType;
export type StencilFuncType = {
	func: CompareFuncType;
	ref: number;
	mask: number;
};
export type StencilOpConstant = WebGLRenderingContextBase["KEEP"] | WebGLRenderingContextBase["ZERO"] | WebGLRenderingContextBase["REPLACE"] | WebGLRenderingContextBase["INCR"] | WebGLRenderingContextBase["INCR_WRAP"] | WebGLRenderingContextBase["DECR"] | WebGLRenderingContextBase["DECR_WRAP"] | WebGLRenderingContextBase["INVERT"];
export type StencilOpType = [
	StencilOpConstant,
	StencilOpConstant,
	StencilOpConstant
];
export type TextureUnitType = number;
export type ViewportType = [
	number,
	number,
	number,
	number
];
export type StencilTestGL = {
	func: WebGLRenderingContextBase["NEVER"];
	mask: 0;
} | {
	func: WebGLRenderingContextBase["LESS"];
	mask: number;
} | {
	func: WebGLRenderingContextBase["EQUAL"];
	mask: number;
} | {
	func: WebGLRenderingContextBase["LEQUAL"];
	mask: number;
} | {
	func: WebGLRenderingContextBase["GREATER"];
	mask: number;
} | {
	func: WebGLRenderingContextBase["NOTEQUAL"];
	mask: number;
} | {
	func: WebGLRenderingContextBase["GEQUAL"];
	mask: number;
} | {
	func: WebGLRenderingContextBase["ALWAYS"];
	mask: 0;
};
export type CullFaceModeType = WebGLRenderingContextBase["FRONT"] | WebGLRenderingContextBase["BACK"] | WebGLRenderingContextBase["FRONT_AND_BACK"];
export type FrontFaceType = WebGLRenderingContextBase["CW"] | WebGLRenderingContextBase["CCW"];
export interface IValue<T> {
	current: T;
	default: T;
	dirty: boolean;
	get(): T;
	setDefault(): void;
	set(value: T): void;
}
declare class BaseValue<T> implements IValue<T> {
	gl: WebGLRenderingContext | WebGL2RenderingContext;
	current: T;
	default: T;
	dirty: boolean;
	constructor(context: Context);
	get(): T;
	set(value: T): void;
	getDefault(): T;
	setDefault(): void;
}
declare class ClearColor extends BaseValue<Color> {
	getDefault(): Color;
	set(v: Color): void;
}
declare class ClearDepth extends BaseValue<number> {
	getDefault(): number;
	set(v: number): void;
}
declare class ClearStencil extends BaseValue<number> {
	getDefault(): number;
	set(v: number): void;
}
declare class ColorMask extends BaseValue<ColorMaskType> {
	getDefault(): ColorMaskType;
	set(v: ColorMaskType): void;
}
declare class DepthMask extends BaseValue<DepthMaskType> {
	getDefault(): DepthMaskType;
	set(v: DepthMaskType): void;
}
declare class StencilMask extends BaseValue<number> {
	getDefault(): number;
	set(v: number): void;
}
declare class StencilFunc extends BaseValue<StencilFuncType> {
	getDefault(): StencilFuncType;
	set(v: StencilFuncType): void;
}
declare class StencilOp extends BaseValue<StencilOpType> {
	getDefault(): StencilOpType;
	set(v: StencilOpType): void;
}
declare class StencilTest extends BaseValue<boolean> {
	getDefault(): boolean;
	set(v: boolean): void;
}
declare class DepthRange extends BaseValue<DepthRangeType> {
	getDefault(): DepthRangeType;
	set(v: DepthRangeType): void;
}
declare class DepthTest extends BaseValue<boolean> {
	getDefault(): boolean;
	set(v: boolean): void;
}
declare class DepthFunc extends BaseValue<DepthFuncType> {
	getDefault(): DepthFuncType;
	set(v: DepthFuncType): void;
}
declare class Blend extends BaseValue<boolean> {
	getDefault(): boolean;
	set(v: boolean): void;
}
declare class BlendFunc extends BaseValue<BlendFuncType> {
	getDefault(): BlendFuncType;
	set(v: BlendFuncType): void;
}
declare class BlendColor extends BaseValue<Color> {
	getDefault(): Color;
	set(v: Color): void;
}
declare class BlendEquation extends BaseValue<BlendEquationType> {
	getDefault(): BlendEquationType;
	set(v: BlendEquationType): void;
}
declare class CullFace extends BaseValue<boolean> {
	getDefault(): boolean;
	set(v: boolean): void;
}
declare class CullFaceSide extends BaseValue<CullFaceModeType> {
	getDefault(): CullFaceModeType;
	set(v: CullFaceModeType): void;
}
declare class FrontFace extends BaseValue<FrontFaceType> {
	getDefault(): FrontFaceType;
	set(v: FrontFaceType): void;
}
declare class ProgramValue extends BaseValue<WebGLProgram> {
	getDefault(): WebGLProgram;
	set(v?: WebGLProgram | null): void;
}
declare class ActiveTextureUnit extends BaseValue<TextureUnitType> {
	getDefault(): TextureUnitType;
	set(v: TextureUnitType): void;
}
declare class Viewport extends BaseValue<ViewportType> {
	getDefault(): ViewportType;
	set(v: ViewportType): void;
}
declare class BindFramebuffer extends BaseValue<WebGLFramebuffer> {
	getDefault(): WebGLFramebuffer;
	set(v?: WebGLFramebuffer | null): void;
}
declare class BindRenderbuffer extends BaseValue<WebGLRenderbuffer> {
	getDefault(): WebGLRenderbuffer;
	set(v?: WebGLRenderbuffer | null): void;
}
declare class BindTexture extends BaseValue<WebGLTexture> {
	getDefault(): WebGLTexture;
	set(v?: WebGLTexture | null): void;
}
declare class BindVertexBuffer extends BaseValue<WebGLBuffer> {
	getDefault(): WebGLBuffer;
	set(v?: WebGLBuffer | null): void;
}
declare class BindElementBuffer extends BaseValue<WebGLBuffer> {
	getDefault(): WebGLBuffer;
	set(v?: WebGLBuffer | null): void;
}
declare class BindVertexArray extends BaseValue<WebGLVertexArrayObject | null> {
	getDefault(): WebGLVertexArrayObject | null;
	set(v: WebGLVertexArrayObject | null): void;
}
declare class PixelStoreUnpack extends BaseValue<number> {
	getDefault(): number;
	set(v: number): void;
}
declare class PixelStoreUnpackPremultiplyAlpha extends BaseValue<boolean> {
	getDefault(): boolean;
	set(v: boolean): void;
}
declare class PixelStoreUnpackFlipY extends BaseValue<boolean> {
	getDefault(): boolean;
	set(v: boolean): void;
}
declare class FramebufferAttachment<T> extends BaseValue<T> {
	parent: WebGLFramebuffer;
	context: Context;
	constructor(context: Context, parent: WebGLFramebuffer);
	getDefault(): any;
}
declare class ColorAttachment extends FramebufferAttachment<WebGLTexture> {
	setDirty(): void;
	set(v?: WebGLTexture | null): void;
}
declare class DepthAttachment extends FramebufferAttachment<WebGLRenderbuffer> {
	set(v?: WebGLRenderbuffer | null): void;
}
declare class Framebuffer {
	context: Context;
	width: number;
	height: number;
	framebuffer: WebGLFramebuffer;
	colorAttachment: ColorAttachment;
	depthAttachment: DepthAttachment;
	constructor(context: Context, width: number, height: number, hasDepth: boolean, hasStencil: boolean);
	destroy(): void;
}
declare class HeatmapStyleLayer extends StyleLayer {
	heatmapFbos: Map<string, Framebuffer>;
	colorRamp: RGBAImage;
	colorRampTexture: Texture;
	_transitionablePaint: Transitionable<HeatmapPaintProps>;
	_transitioningPaint: Transitioning<HeatmapPaintProps>;
	paint: PossiblyEvaluated<HeatmapPaintProps, HeatmapPaintPropsPossiblyEvaluated>;
	createBucket(options: any): HeatmapBucket;
	constructor(layer: LayerSpecification);
	_handleSpecialPaintPropertyUpdate(name: string): void;
	_updateColorRamp(): void;
	resize(): void;
	queryRadius(): number;
	queryIntersectsFeature(): boolean;
	hasOffscreenPass(): boolean;
}
export type SerializedGrid = {
	buffer: ArrayBuffer;
};
declare class TransferableGridIndex {
	cells: number[][];
	arrayBuffer: ArrayBuffer;
	d: number;
	keys: number[];
	bboxes: number[];
	n: number;
	extent: number;
	padding: number;
	scale: any;
	uid: number;
	min: number;
	max: number;
	constructor(extent: number | ArrayBuffer, n?: number, padding?: number);
	insert(key: number, x1: number, y1: number, x2: number, y2: number): void;
	_insertReadonly(): void;
	_insertCell(x1: number, y1: number, x2: number, y2: number, cellIndex: number, uid: number): void;
	query(x1: number, y1: number, x2: number, y2: number, intersectionTest?: Function): number[];
	_queryCell(x1: number, y1: number, x2: number, y2: number, cellIndex: number, result: any, seenUids: any, intersectionTest: Function): void;
	_forEachCell(x1: number, y1: number, x2: number, y2: number, fn: Function, arg1: any, arg2: any, intersectionTest: any): void;
	_convertFromCellCoord(x: any): number;
	_convertToCellCoord(x: any): number;
	toArrayBuffer(): ArrayBuffer;
	static serialize(grid: TransferableGridIndex, transferables?: Array<Transferable>): SerializedGrid;
	static deserialize(serialized: SerializedGrid): TransferableGridIndex;
}
declare class DictionaryCoder {
	_stringToNumber: {
		[_: string]: number;
	};
	_numberToString: Array<string>;
	constructor(strings: Array<string>);
	encode(string: string): number;
	decode(n: number): string;
}
/**
 * A helper for type to omit a property from a type
 */
export type DistributiveKeys<T> = T extends T ? keyof T : never;
/**
 * A helper for type to omit a property from a type
 */
export type DistributiveOmit<T, K extends DistributiveKeys<T>> = T extends unknown ? Omit<T, K> : never;
/**
 * An extended geojson feature used by the events to return data to the listener
 */
export type MapGeoJSONFeature = GeoJSONFeature & {
	layer: DistributiveOmit<LayerSpecification, "source"> & {
		source: string;
	};
	source: string;
	sourceLayer?: string;
	state: {
		[key: string]: any;
	};
};
declare class GeoJSONFeature {
	type: "Feature";
	_geometry: GeoJSON.Geometry;
	properties: {
		[name: string]: any;
	};
	id: number | string | undefined;
	_vectorTileFeature: VectorTileFeature;
	constructor(vectorTileFeature: VectorTileFeature, z: number, x: number, y: number, id: string | number | undefined);
	get geometry(): GeoJSON.Geometry;
	set geometry(g: GeoJSON.Geometry);
	toJSON(): any;
}
/**
 * A {@link LngLatBounds} object, an array of {@link LngLatLike} objects in [sw, ne] order,
 * or an array of numbers in [west, south, east, north] order.
 *
 * @group Geography and Geometry
 *
 * @example
 * ```ts
 * let v1 = new LngLatBounds(
 *   new LngLat(-73.9876, 40.7661),
 *   new LngLat(-73.9397, 40.8002)
 * );
 * let v2 = new LngLatBounds([-73.9876, 40.7661], [-73.9397, 40.8002])
 * let v3 = [[-73.9876, 40.7661], [-73.9397, 40.8002]];
 * ```
 */
export type LngLatBoundsLike = LngLatBounds | [
	LngLatLike,
	LngLatLike
] | [
	number,
	number,
	number,
	number
];
/**
 * A `LngLatBounds` object represents a geographical bounding box,
 * defined by its southwest and northeast points in longitude and latitude.
 *
 * If no arguments are provided to the constructor, a `null` bounding box is created.
 *
 * Note that any Mapbox GL method that accepts a `LngLatBounds` object as an argument or option
 * can also accept an `Array` of two {@link LngLatLike} constructs and will perform an implicit conversion.
 * This flexible type is documented as {@link LngLatBoundsLike}.
 *
 * @group Geography and Geometry
 *
 * @example
 * ```ts
 * let sw = new LngLat(-73.9876, 40.7661);
 * let ne = new LngLat(-73.9397, 40.8002);
 * let llb = new LngLatBounds(sw, ne);
 * ```
 */
export declare class LngLatBounds {
	_ne: LngLat;
	_sw: LngLat;
	/**
	 * @param sw - The southwest corner of the bounding box.
	 * OR array of 4 numbers in the order of  west, south, east, north
	 * OR array of 2 LngLatLike: [sw,ne]
	 * @param ne - The northeast corner of the bounding box.
	 * @example
	 * ```ts
	 * let sw = new LngLat(-73.9876, 40.7661);
	 * let ne = new LngLat(-73.9397, 40.8002);
	 * let llb = new LngLatBounds(sw, ne);
	 * ```
	 * OR
	 * ```ts
	 * let llb = new LngLatBounds([-73.9876, 40.7661, -73.9397, 40.8002]);
	 * ```
	 * OR
	 * ```ts
	 * let llb = new LngLatBounds([sw, ne]);
	 * ```
	 */
	constructor(sw?: LngLatLike | [
		number,
		number,
		number,
		number
	] | [
		LngLatLike,
		LngLatLike
	], ne?: LngLatLike);
	/**
	 * Set the northeast corner of the bounding box
	 *
	 * @param ne - a {@link LngLatLike} object describing the northeast corner of the bounding box.
	 */
	setNorthEast(ne: LngLatLike): this;
	/**
	 * Set the southwest corner of the bounding box
	 *
	 * @param sw - a {@link LngLatLike} object describing the southwest corner of the bounding box.
	 */
	setSouthWest(sw: LngLatLike): this;
	/**
	 * Extend the bounds to include a given LngLatLike or LngLatBoundsLike.
	 *
	 * @param obj - object to extend to
	 */
	extend(obj: LngLatLike | LngLatBoundsLike): this;
	/**
	 * Returns the geographical coordinate equidistant from the bounding box's corners.
	 *
	 * @returns The bounding box's center.
	 * @example
	 * ```ts
	 * let llb = new LngLatBounds([-73.9876, 40.7661], [-73.9397, 40.8002]);
	 * llb.getCenter(); // = LngLat {lng: -73.96365, lat: 40.78315}
	 * ```
	 */
	getCenter(): LngLat;
	/**
	 * Returns the southwest corner of the bounding box.
	 *
	 * @returns The southwest corner of the bounding box.
	 */
	getSouthWest(): LngLat;
	/**
	 * Returns the northeast corner of the bounding box.
	 *
	 * @returns The northeast corner of the bounding box.
	 */
	getNorthEast(): LngLat;
	/**
	 * Returns the northwest corner of the bounding box.
	 *
	 * @returns The northwest corner of the bounding box.
	 */
	getNorthWest(): LngLat;
	/**
	 * Returns the southeast corner of the bounding box.
	 *
	 * @returns The southeast corner of the bounding box.
	 */
	getSouthEast(): LngLat;
	/**
	 * Returns the west edge of the bounding box.
	 *
	 * @returns The west edge of the bounding box.
	 */
	getWest(): number;
	/**
	 * Returns the south edge of the bounding box.
	 *
	 * @returns The south edge of the bounding box.
	 */
	getSouth(): number;
	/**
	 * Returns the east edge of the bounding box.
	 *
	 * @returns The east edge of the bounding box.
	 */
	getEast(): number;
	/**
	 * Returns the north edge of the bounding box.
	 *
	 * @returns The north edge of the bounding box.
	 */
	getNorth(): number;
	/**
	 * Returns the bounding box represented as an array.
	 *
	 * @returns The bounding box represented as an array, consisting of the
	 * southwest and northeast coordinates of the bounding represented as arrays of numbers.
	 * @example
	 * ```ts
	 * let llb = new LngLatBounds([-73.9876, 40.7661], [-73.9397, 40.8002]);
	 * llb.toArray(); // = [[-73.9876, 40.7661], [-73.9397, 40.8002]]
	 * ```
	 */
	toArray(): [
		number,
		number
	][];
	/**
	 * Return the bounding box represented as a string.
	 *
	 * @returns The bounding box represents as a string of the format
	 * `'LngLatBounds(LngLat(lng, lat), LngLat(lng, lat))'`.
	 * @example
	 * ```ts
	 * let llb = new LngLatBounds([-73.9876, 40.7661], [-73.9397, 40.8002]);
	 * llb.toString(); // = "LngLatBounds(LngLat(-73.9876, 40.7661), LngLat(-73.9397, 40.8002))"
	 * ```
	 */
	toString(): string;
	/**
	 * Check if the bounding box is an empty/`null`-type box.
	 *
	 * @returns True if bounds have been defined, otherwise false.
	 */
	isEmpty(): boolean;
	/**
	 * Check if the point is within the bounding box.
	 *
	 * @param lnglat - geographic point to check against.
	 * @returns `true` if the point is within the bounding box.
	 * @example
	 * ```ts
	 * let llb = new LngLatBounds(
	 *   new LngLat(-73.9876, 40.7661),
	 *   new LngLat(-73.9397, 40.8002)
	 * );
	 *
	 * let ll = new LngLat(-73.9567, 40.7789);
	 *
	 * console.log(llb.contains(ll)); // = true
	 * ```
	 */
	contains(lnglat: LngLatLike): boolean;
	/**
	 * Converts an array to a `LngLatBounds` object.
	 *
	 * If a `LngLatBounds` object is passed in, the function returns it unchanged.
	 *
	 * Internally, the function calls `LngLat#convert` to convert arrays to `LngLat` values.
	 *
	 * @param input - An array of two coordinates to convert, or a `LngLatBounds` object to return.
	 * @returns A new `LngLatBounds` object, if a conversion occurred, or the original `LngLatBounds` object.
	 * @example
	 * ```ts
	 * let arr = [[-73.9876, 40.7661], [-73.9397, 40.8002]];
	 * let llb = LngLatBounds.convert(arr); // = LngLatBounds {_sw: LngLat {lng: -73.9876, lat: 40.7661}, _ne: LngLat {lng: -73.9397, lat: 40.8002}}
	 * ```
	 */
	static convert(input: LngLatBoundsLike | null): LngLatBounds;
	/**
	 * Returns a `LngLatBounds` from the coordinates extended by a given `radius`. The returned `LngLatBounds` completely contains the `radius`.
	 *
	 * @param center - center coordinates of the new bounds.
	 * @param radius - Distance in meters from the coordinates to extend the bounds.
	 * @returns A new `LngLatBounds` object representing the coordinates extended by the `radius`.
	 * @example
	 * ```ts
	 * let center = new LngLat(-73.9749, 40.7736);
	 * LngLatBounds.fromLngLat(100).toArray(); // = [[-73.97501862141328, 40.77351016847229], [-73.97478137858673, 40.77368983152771]]
	 * ```
	 */
	static fromLngLat(center: LngLat, radius?: number): LngLatBounds;
	/**
	 * Adjusts the given bounds to handle the case where the bounds cross the 180th meridian (antimeridian).
	 *
	 * @returns The adjusted LngLatBounds
	 * @example
	 * ```ts
	 * let bounds = new LngLatBounds([175.813127, -20.157768], [-178. 340903, -15.449124]);
	 * let adjustedBounds = bounds.adjustAntiMeridian();
	 * // adjustedBounds will be: [[175.813127, -20.157768], [181.659097, -15.449124]]
	 * ```
	 */
	adjustAntiMeridian(): LngLatBounds;
}
/**
 * An `EdgeInset` object represents screen space padding applied to the edges of the viewport.
 * This shifts the apprent center or the vanishing point of the map. This is useful for adding floating UI elements
 * on top of the map and having the vanishing point shift as UI elements resize.
 *
 * @group Geography and Geometry
 */
export declare class EdgeInsets {
	/**
	 * @defaultValue 0
	 */
	top: number;
	/**
	 * @defaultValue 0
	 */
	bottom: number;
	/**
	 * @defaultValue 0
	 */
	left: number;
	/**
	 * @defaultValue 0
	 */
	right: number;
	constructor(top?: number, bottom?: number, left?: number, right?: number);
	/**
	 * Interpolates the inset in-place.
	 * This maintains the current inset value for any inset not present in `target`.
	 * @param start - interpolation start
	 * @param target - interpolation target
	 * @param t - interpolation step/weight
	 * @returns the insets
	 */
	interpolate(start: PaddingOptions | EdgeInsets, target: PaddingOptions, t: number): EdgeInsets;
	/**
	 * Utility method that computes the new apprent center or vanishing point after applying insets.
	 * This is in pixels and with the top left being (0.0) and +y being downwards.
	 *
	 * @param width - the width
	 * @param height - the height
	 * @returns the point
	 */
	getCenter(width: number, height: number): Point;
	equals(other: PaddingOptions): boolean;
	clone(): EdgeInsets;
	/**
	 * Returns the current state as json, useful when you want to have a
	 * read-only representation of the inset.
	 *
	 * @returns state as json
	 */
	toJSON(): PaddingOptions;
}
/**
 * Options for setting padding on calls to methods such as {@link Map#fitBounds}, {@link Map#fitScreenCoordinates}, and {@link Map#setPadding}. Adjust these options to set the amount of padding in pixels added to the edges of the canvas. Set a uniform padding on all edges or individual values for each edge. All properties of this object must be
 * non-negative integers.
 *
 * @group Geography and Geometry
 *
 * @example
 * ```ts
 * let bbox = [[-79, 43], [-73, 45]];
 * map.fitBounds(bbox, {
 *   padding: {top: 10, bottom:25, left: 15, right: 5}
 * });
 * ```
 *
 * @example
 * ```ts
 * let bbox = [[-79, 43], [-73, 45]];
 * map.fitBounds(bbox, {
 *   padding: 20
 * });
 * ```
 * @see [Fit to the bounds of a LineString](https://maplibre.org/maplibre-gl-js/docs/examples/zoomto-linestring/)
 * @see [Fit a map to a bounding box](https://maplibre.org/maplibre-gl-js/docs/examples/fitbounds/)
 */
export type PaddingOptions = {
	/**
	 * Padding in pixels from the top of the map canvas.
	 */
	top: number;
	/**
	 * Padding in pixels from the bottom of the map canvas.
	 */
	bottom: number;
	/**
	 * Padding in pixels from the left of the map canvas.
	 */
	right: number;
	/**
	 * Padding in pixels from the right of the map canvas.
	 */
	left: number;
};
declare class TileCache {
	max: number;
	data: {
		[key: string]: Array<{
			value: Tile;
			timeout: ReturnType<typeof setTimeout>;
		}>;
	};
	order: Array<string>;
	onRemove: (element: Tile) => void;
	/**
	 * @param max - number of permitted values
	 * @param onRemove - callback called with items when they expire
	 */
	constructor(max: number, onRemove: (element: Tile) => void);
	/**
	 * Clear the cache
	 *
	 * @returns this cache
	 */
	reset(): this;
	/**
	 * Add a key, value combination to the cache, trimming its size if this pushes
	 * it over max length.
	 *
	 * @param tileID - lookup key for the item
	 * @param data - tile data
	 *
	 * @returns this cache
	 */
	add(tileID: OverscaledTileID, data: Tile, expiryTimeout: number | void): this;
	/**
	 * Determine whether the value attached to `key` is present
	 *
	 * @param tileID - the key to be looked-up
	 * @returns whether the cache has this value
	 */
	has(tileID: OverscaledTileID): boolean;
	/**
	 * Get the value attached to a specific key and remove data from cache.
	 * If the key is not found, returns `null`
	 *
	 * @param tileID - the key to look up
	 * @returns the tile data, or null if it isn't found
	 */
	getAndRemove(tileID: OverscaledTileID): Tile;
	_getAndRemoveByKey(key: string): Tile;
	getByKey(key: string): Tile;
	/**
	 * Get the value attached to a specific key without removing data
	 * from the cache. If the key is not found, returns `null`
	 *
	 * @param tileID - the key to look up
	 * @returns the tile data, or null if it isn't found
	 */
	get(tileID: OverscaledTileID): Tile;
	/**
	 * Remove a key/value combination from the cache.
	 *
	 * @param tileID - the key for the pair to delete
	 * @param value - If a value is provided, remove that exact version of the value.
	 * @returns this cache
	 */
	remove(tileID: OverscaledTileID, value?: {
		value: Tile;
		timeout: ReturnType<typeof setTimeout>;
	}): this;
	/**
	 * Change the max size of the cache.
	 *
	 * @param max - the max size of the cache
	 * @returns this cache
	 */
	setMaxSize(max: number): TileCache;
	/**
	 * Remove entries that do not pass a filter function. Used for removing
	 * stale tiles from the cache.
	 *
	 * @param filterFn - Determines whether the tile is filtered. If the supplied function returns false, the tile will be filtered out.
	 */
	filter(filterFn: (tile: Tile) => boolean): void;
}
declare class WorkerPool {
	static workerCount: number;
	active: {
		[_ in number | string]: boolean;
	};
	workers: Array<ActorTarget>;
	constructor();
	acquire(mapId: number | string): Array<ActorTarget>;
	release(mapId: number | string): void;
	isPreloaded(): boolean;
	numActive(): number;
}
declare class Dispatcher {
	workerPool: WorkerPool;
	actors: Array<Actor>;
	currentActor: number;
	id: string | number;
	constructor(workerPool: WorkerPool, mapId: string | number);
	/**
	 * Broadcast a message to all Workers.
	 */
	broadcast<T extends MessageType>(type: T, data: RequestResponseMessageMap[T][0]): Promise<RequestResponseMessageMap[T][1][]>;
	/**
	 * Acquires an actor to dispatch messages to. The actors are distributed in round-robin fashion.
	 * @returns An actor object backed by a web worker for processing messages.
	 */
	getActor(): Actor;
	remove(mapRemoved?: boolean): void;
	registerMessageHandler<T extends MessageType>(type: T, handler: MessageHandler<T>): void;
}
/**
 * Four geographical coordinates,
 * represented as arrays of longitude and latitude numbers, which define the corners of the image.
 * The coordinates start at the top left corner of the image and proceed in clockwise order.
 * They do not have to represent a rectangle.
 */
export type Coordinates = [
	[
		number,
		number
	],
	[
		number,
		number
	],
	[
		number,
		number
	],
	[
		number,
		number
	]
];
/**
 * The options object for the {@link ImageSource#updateImage} method
 */
export type UpdateImageOptions = {
	/**
	 * Required image URL.
	 */
	url: string;
	/**
	 * The image coordinates
	 */
	coordinates?: Coordinates;
};
/**
 * A data source containing an image.
 * (See the [Style Specification](https://maplibre.org/maplibre-style-spec/#sources-image) for detailed documentation of options.)
 *
 * @group Sources
 *
 * @example
 * ```ts
 * // add to map
 * map.addSource('some id', {
 *    type: 'image',
 *    url: 'https://www.maplibre.org/images/foo.png',
 *    coordinates: [
 *        [-76.54, 39.18],
 *        [-76.52, 39.18],
 *        [-76.52, 39.17],
 *        [-76.54, 39.17]
 *    ]
 * });
 *
 * // update coordinates
 * let mySource = map.getSource('some id');
 * mySource.setCoordinates([
 *     [-76.54335737228394, 39.18579907229748],
 *     [-76.52803659439087, 39.1838364847587],
 *     [-76.5295386314392, 39.17683392507606],
 *     [-76.54520273208618, 39.17876344106642]
 * ]);
 *
 * // update url and coordinates simultaneously
 * mySource.updateImage({
 *    url: 'https://www.maplibre.org/images/bar.png',
 *    coordinates: [
 *        [-76.54335737228394, 39.18579907229748],
 *        [-76.52803659439087, 39.1838364847587],
 *        [-76.5295386314392, 39.17683392507606],
 *        [-76.54520273208618, 39.17876344106642]
 *    ]
 * })
 *
 * map.removeSource('some id');  // remove
 * ```
 */
export declare class ImageSource extends Evented implements Source {
	type: string;
	id: string;
	minzoom: number;
	maxzoom: number;
	tileSize: number;
	url: string;
	coordinates: Coordinates;
	tiles: {
		[_: string]: Tile;
	};
	options: any;
	dispatcher: Dispatcher;
	map: Map$1;
	texture: Texture | null;
	image: HTMLImageElement | ImageBitmap;
	tileID: CanonicalTileID;
	_boundsArray: RasterBoundsArray;
	boundsBuffer: VertexBuffer;
	boundsSegments: SegmentVector;
	_loaded: boolean;
	_request: AbortController;
	/** @internal */
	constructor(id: string, options: ImageSourceSpecification | VideoSourceSpecification | CanvasSourceSpecification, dispatcher: Dispatcher, eventedParent: Evented);
	load(newCoordinates?: Coordinates): Promise<void>;
	loaded(): boolean;
	/**
	 * Updates the image URL and, optionally, the coordinates. To avoid having the image flash after changing,
	 * set the `raster-fade-duration` paint property on the raster layer to 0.
	 *
	 * @param options - The options object.
	 */
	updateImage(options: UpdateImageOptions): this;
	_finishLoading(): void;
	onAdd(map: Map$1): void;
	onRemove(): void;
	/**
	 * Sets the image's coordinates and re-renders the map.
	 *
	 * @param coordinates - Four geographical coordinates,
	 * represented as arrays of longitude and latitude numbers, which define the corners of the image.
	 * The coordinates start at the top left corner of the image and proceed in clockwise order.
	 * They do not have to represent a rectangle.
	 */
	setCoordinates(coordinates: Coordinates): this;
	prepare(): void;
	loadTile(tile: Tile): Promise<void>;
	serialize(): ImageSourceSpecification | VideoSourceSpecification | CanvasSourceSpecification;
	hasTransition(): boolean;
}
/**
 * Options to add a canvas source type to the map.
 */
export type CanvasSourceSpecification = {
	/**
	 * Source type. Must be `"canvas"`.
	 */
	type: "canvas";
	/**
	 * Four geographical coordinates denoting where to place the corners of the canvas, specified in `[longitude, latitude]` pairs.
	 */
	coordinates: [
		[
			number,
			number
		],
		[
			number,
			number
		],
		[
			number,
			number
		],
		[
			number,
			number
		]
	];
	/**
	 * Whether the canvas source is animated. If the canvas is static (i.e. pixels do not need to be re-read on every frame), `animate` should be set to `false` to improve performance.
	 * @defaultValue true
	 */
	animate?: boolean;
	/**
	 * Canvas source from which to read pixels. Can be a string representing the ID of the canvas element, or the `HTMLCanvasElement` itself.
	 */
	canvas?: string | HTMLCanvasElement;
};
/**
 * A data source containing the contents of an HTML canvas. See {@link CanvasSourceSpecification} for detailed documentation of options.
 *
 * @group Sources
 *
 * @example
 * ```ts
 * // add to map
 * map.addSource('some id', {
 *    type: 'canvas',
 *    canvas: 'idOfMyHTMLCanvas',
 *    animate: true,
 *    coordinates: [
 *        [-76.54, 39.18],
 *        [-76.52, 39.18],
 *        [-76.52, 39.17],
 *        [-76.54, 39.17]
 *    ]
 * });
 *
 * // update
 * let mySource = map.getSource('some id');
 * mySource.setCoordinates([
 *     [-76.54335737228394, 39.18579907229748],
 *     [-76.52803659439087, 39.1838364847587],
 *     [-76.5295386314392, 39.17683392507606],
 *     [-76.54520273208618, 39.17876344106642]
 * ]);
 *
 * map.removeSource('some id');  // remove
 * ```
 */
export declare class CanvasSource extends ImageSource {
	options: CanvasSourceSpecification;
	animate: boolean;
	canvas: HTMLCanvasElement;
	width: number;
	height: number;
	/**
	 * Enables animation. The image will be copied from the canvas to the map on each frame.
	 */
	play: () => void;
	/**
	 * Disables animation. The map will display a static copy of the canvas image.
	 */
	pause: () => void;
	_playing: boolean;
	/** @internal */
	constructor(id: string, options: CanvasSourceSpecification, dispatcher: Dispatcher, eventedParent: Evented);
	load(): Promise<void>;
	/**
	 * Returns the HTML `canvas` element.
	 *
	 * @returns The HTML `canvas` element.
	 */
	getCanvas(): HTMLCanvasElement;
	onAdd(map: Map$1): void;
	onRemove(): void;
	prepare(): void;
	serialize(): CanvasSourceSpecification;
	hasTransition(): boolean;
	_hasInvalidDimensions(): boolean;
}
/**
 * The `Source` interface must be implemented by each source type, including "core" types (`vector`, `raster`,
 * `video`, etc.) and all custom, third-party types.
 *
 * **Event** `data` - Fired with `{dataType: 'source', sourceDataType: 'metadata'}` to indicate that any necessary metadata
 * has been loaded so that it's okay to call `loadTile`; and with `{dataType: 'source', sourceDataType: 'content'}`
 * to indicate that the source data has changed, so that any current caches should be flushed.
 *
 * @group Sources
 */
export interface Source {
	readonly type: string;
	/**
	 * The id for the source. Must not be used by any existing source.
	 */
	id: string;
	/**
	 * The minimum zoom level for the source.
	 */
	minzoom: number;
	/**
	 * The maximum zoom level for the source.
	 */
	maxzoom: number;
	/**
	 * The tile size for the source.
	 */
	tileSize: number;
	/**
	 * The attribution for the source.
	 */
	attribution?: string;
	/**
	 * `true` if zoom levels are rounded to the nearest integer in the source data, `false` if they are floor-ed to the nearest integer.
	 */
	roundZoom?: boolean;
	/**
	 * `false` if tiles can be drawn outside their boundaries, `true` if they cannot.
	 */
	isTileClipped?: boolean;
	tileID?: CanonicalTileID;
	/**
	 * `true` if tiles should be sent back to the worker for each overzoomed zoom level, `false` if not.
	 */
	reparseOverscaled?: boolean;
	vectorLayerIds?: Array<string>;
	/**
	 * True if the source has transition, false otherwise.
	 */
	hasTransition(): boolean;
	/**
	 * True if the source is loaded, false otherwise.
	 */
	loaded(): boolean;
	/**
	 * An ability to fire an event to all the listeners, see {@link Evented}
	 * @param event - The event to fire
	 */
	fire(event: Event$1): unknown;
	/**
	 * This method is called when the source is added to the map.
	 * @param map - The map instance
	 */
	onAdd?(map: Map$1): void;
	/**
	 * This method is called when the source is removed from the map.
	 * @param map - The map instance
	 */
	onRemove?(map: Map$1): void;
	/**
	 * This method does the heavy lifting of loading a tile.
	 * In most cases it will defer the work to the relevant worker source.
	 * @param tile - The tile to load
	 */
	loadTile(tile: Tile): Promise<void>;
	/**
	 * True is the tile is part of the source, false otherwise.
	 * @param tileID - The tile ID
	 */
	hasTile?(tileID: OverscaledTileID): boolean;
	/**
	 * Allows to abort a tile loading.
	 * @param tile - The tile to abort
	 */
	abortTile?(tile: Tile): Promise<void>;
	/**
	 * Allows to unload a tile.
	 * @param tile - The tile to unload
	 */
	unloadTile?(tile: Tile): Promise<void>;
	/**
	 * @returns A plain (stringifiable) JS object representing the current state of the source.
	 * Creating a source using the returned object as the `options` should result in a Source that is
	 * equivalent to this one.
	 */
	serialize(): any;
	/**
	 * Allows to execute a prepare step before the source is used.
	 */
	prepare?(): void;
}
/**
 * A general definition of a {@link Source} class for factory usage
 */
export type SourceClass = {
	new (id: string, specification: SourceSpecification | CanvasSourceSpecification, dispatcher: Dispatcher, eventedParent: Evented): Source;
};
/**
 * Adds a custom source type, making it available for use with {@link Map#addSource}.
 * @param name - The name of the source type; source definition objects use this name in the `{type: ...}` field.
 * @param SourceType - A {@link SourceClass} - which is a constructor for the `Source` interface.
 * @returns a promise that is resolved when the source type is ready or rejected with an error.
 */
export declare const addSourceType: (name: string, SourceType: SourceClass) => Promise<void>;
declare class SourceCache extends Evented {
	id: string;
	dispatcher: Dispatcher;
	map: Map$1;
	style: Style;
	_source: Source;
	/**
	 * @internal
	 * signifies that the TileJSON is loaded if applicable.
	 * if the source type does not come with a TileJSON, the flag signifies the
	 * source data has loaded (i.e geojson has been tiled on the worker and is ready)
	 */
	_sourceLoaded: boolean;
	_sourceErrored: boolean;
	_tiles: {
		[_: string]: Tile;
	};
	_prevLng: number;
	_cache: TileCache;
	_timers: {
		[_ in any]: ReturnType<typeof setTimeout>;
	};
	_cacheTimers: {
		[_ in any]: ReturnType<typeof setTimeout>;
	};
	_maxTileCacheSize: number;
	_maxTileCacheZoomLevels: number;
	_paused: boolean;
	_shouldReloadOnResume: boolean;
	_coveredTiles: {
		[_: string]: boolean;
	};
	transform: Transform;
	terrain: Terrain;
	used: boolean;
	usedForTerrain: boolean;
	tileSize: number;
	_state: SourceFeatureState;
	_loadedParentTiles: {
		[_: string]: Tile;
	};
	_loadedSiblingTiles: {
		[_: string]: Tile;
	};
	_didEmitContent: boolean;
	_updated: boolean;
	static maxUnderzooming: number;
	static maxOverzooming: number;
	constructor(id: string, options: SourceSpecification | CanvasSourceSpecification, dispatcher: Dispatcher);
	onAdd(map: Map$1): void;
	onRemove(map: Map$1): void;
	/**
	 * Return true if no tile data is pending, tiles will not change unless
	 * an additional API call is received.
	 */
	loaded(): boolean;
	getSource(): Source;
	pause(): void;
	resume(): void;
	_loadTile(tile: Tile, id: string, state: TileState): Promise<void>;
	_unloadTile(tile: Tile): void;
	_abortTile(tile: Tile): void;
	serialize(): any;
	prepare(context: Context): void;
	/**
	 * Return all tile ids ordered with z-order, and cast to numbers
	 */
	getIds(): Array<string>;
	getRenderableIds(symbolLayer?: boolean): Array<string>;
	hasRenderableParent(tileID: OverscaledTileID): boolean;
	_isIdRenderable(id: string, symbolLayer?: boolean): boolean;
	reload(): void;
	_reloadTile(id: string, state: TileState): Promise<void>;
	_tileLoaded(tile: Tile, id: string, previousState: TileState): void;
	/**
	 * For raster terrain source, backfill DEM to eliminate visible tile boundaries
	 */
	_backfillDEM(tile: Tile): void;
	/**
	 * Get a specific tile by TileID
	 */
	getTile(tileID: OverscaledTileID): Tile;
	/**
	 * Get a specific tile by id
	 */
	getTileByID(id: string): Tile;
	/**
	 * For a given set of tiles, retain children that are loaded and have a zoom
	 * between `zoom` (exclusive) and `maxCoveringZoom` (inclusive)
	 */
	_retainLoadedChildren(idealTiles: {
		[_ in any]: OverscaledTileID;
	}, zoom: number, maxCoveringZoom: number, retain: {
		[_ in any]: OverscaledTileID;
	}): void;
	/**
	 * Find a loaded parent of the given tile (up to minCoveringZoom)
	 */
	findLoadedParent(tileID: OverscaledTileID, minCoveringZoom: number): Tile;
	/**
	 * Find a loaded sibling of the given tile
	 */
	findLoadedSibling(tileID: OverscaledTileID): Tile;
	_getLoadedTile(tileID: OverscaledTileID): Tile;
	/**
	 * Resizes the tile cache based on the current viewport's size
	 * or the maxTileCacheSize option passed during map creation
	 *
	 * Larger viewports use more tiles and need larger caches. Larger viewports
	 * are more likely to be found on devices with more memory and on pages where
	 * the map is more important.
	 */
	updateCacheSize(transform: Transform): void;
	handleWrapJump(lng: number): void;
	_updateCoveredAndRetainedTiles(retain: {
		[_: string]: OverscaledTileID;
	}, minCoveringZoom: number, maxCoveringZoom: number, zoom: number, idealTileIDs: OverscaledTileID[], terrain?: Terrain): void;
	/**
	 * Removes tiles that are outside the viewport and adds new tiles that
	 * are inside the viewport.
	 */
	update(transform: Transform, terrain?: Terrain): void;
	releaseSymbolFadeTiles(): void;
	_updateRetainedTiles(idealTileIDs: Array<OverscaledTileID>, zoom: number): {
		[_: string]: OverscaledTileID;
	};
	_updateLoadedParentTileCache(): void;
	/**
	 * Update the cache of loaded sibling tiles
	 *
	 * Sibling tiles are tiles that share the same zoom level and
	 * x/y position but have different wrap values
	 * Maintaining sibling tile cache allows fading from old to new tiles
	 * of the same position and zoom level
	 */
	_updateLoadedSiblingTileCache(): void;
	/**
	 * Add a tile, given its coordinate, to the pyramid.
	 */
	_addTile(tileID: OverscaledTileID): Tile;
	_setTileReloadTimer(id: string, tile: Tile): void;
	/**
	 * Remove a tile, given its id, from the pyramid
	 */
	_removeTile(id: string): void;
	/** @internal */
	private _dataHandler;
	/**
	 * Remove all tiles from this pyramid
	 */
	clearTiles(): void;
	/**
	 * Search through our current tiles and attempt to find the tiles that
	 * cover the given bounds.
	 * @param pointQueryGeometry - coordinates of the corners of bounding rectangle
	 * @returns result items have `{tile, minX, maxX, minY, maxY}`, where min/max bounding values are the given bounds transformed in into the coordinate space of this tile.
	 */
	tilesIn(pointQueryGeometry: Array<Point>, maxPitchScaleFactor: number, has3DLayer: boolean): any[];
	getVisibleCoordinates(symbolLayer?: boolean): Array<OverscaledTileID>;
	hasTransition(): boolean;
	/**
	 * Set the value of a particular state for a feature
	 */
	setFeatureState(sourceLayer: string, featureId: number | string, state: any): void;
	/**
	 * Resets the value of a particular state key for a feature
	 */
	removeFeatureState(sourceLayer?: string, featureId?: number | string, key?: string): void;
	/**
	 * Get the entire state object for a feature
	 */
	getFeatureState(sourceLayer: string, featureId: number | string): import("@maplibre/maplibre-gl-style-spec").FeatureState;
	/**
	 * Sets the set of keys that the tile depends on. This allows tiles to
	 * be reloaded when their dependencies change.
	 */
	setDependencies(tileKey: string, namespace: string, dependencies: Array<string>): void;
	/**
	 * Reloads all tiles that depend on the given keys.
	 */
	reloadTilesForDependencies(namespaces: Array<string>, keys: Array<string>): void;
}
/**
 * Some metices related to a glyph
 */
export type GlyphMetrics = {
	width: number;
	height: number;
	left: number;
	top: number;
	advance: number;
	/**
	 * isDoubleResolution = true for 48px textures
	 */
	isDoubleResolution?: boolean;
};
/**
 * A style glyph type
 */
export type StyleGlyph = {
	id: number;
	bitmap: AlphaImage;
	metrics: GlyphMetrics;
};
/**
 * A rectangle type with postion, width and height.
 */
export type Rect = {
	x: number;
	y: number;
	w: number;
	h: number;
};
/**
 * The glyph's position
 */
export type GlyphPosition = {
	rect: Rect;
	metrics: GlyphMetrics;
};
/**
 * The glyphs' positions
 */
export type GlyphPositions = {
	[_: string]: {
		[_: number]: GlyphPosition;
	};
};
declare enum WritingMode {
	none = 0,
	horizontal = 1,
	vertical = 2,
	horizontalOnly = 3
}
declare class Anchor extends Point {
	angle: any;
	segment?: number;
	constructor(x: number, y: number, angle: number, segment?: number);
	clone(): Anchor;
}
export type SymbolLayoutProps = {
	"symbol-placement": DataConstantProperty<"point" | "line" | "line-center">;
	"symbol-spacing": DataConstantProperty<number>;
	"symbol-avoid-edges": DataConstantProperty<boolean>;
	"symbol-sort-key": DataDrivenProperty<number>;
	"symbol-z-order": DataConstantProperty<"auto" | "viewport-y" | "source">;
	"icon-allow-overlap": DataConstantProperty<boolean>;
	"icon-overlap": DataConstantProperty<"never" | "always" | "cooperative">;
	"icon-ignore-placement": DataConstantProperty<boolean>;
	"icon-optional": DataConstantProperty<boolean>;
	"icon-rotation-alignment": DataConstantProperty<"map" | "viewport" | "auto">;
	"icon-size": DataDrivenProperty<number>;
	"icon-text-fit": DataConstantProperty<"none" | "width" | "height" | "both">;
	"icon-text-fit-padding": DataConstantProperty<[
		number,
		number,
		number,
		number
	]>;
	"icon-image": DataDrivenProperty<ResolvedImage>;
	"icon-rotate": DataDrivenProperty<number>;
	"icon-padding": DataDrivenProperty<Padding>;
	"icon-keep-upright": DataConstantProperty<boolean>;
	"icon-offset": DataDrivenProperty<[
		number,
		number
	]>;
	"icon-anchor": DataDrivenProperty<"center" | "left" | "right" | "top" | "bottom" | "top-left" | "top-right" | "bottom-left" | "bottom-right">;
	"icon-pitch-alignment": DataConstantProperty<"map" | "viewport" | "auto">;
	"text-pitch-alignment": DataConstantProperty<"map" | "viewport" | "auto">;
	"text-rotation-alignment": DataConstantProperty<"map" | "viewport" | "viewport-glyph" | "auto">;
	"text-field": DataDrivenProperty<Formatted>;
	"text-font": DataDrivenProperty<Array<string>>;
	"text-size": DataDrivenProperty<number>;
	"text-max-width": DataDrivenProperty<number>;
	"text-line-height": DataConstantProperty<number>;
	"text-letter-spacing": DataDrivenProperty<number>;
	"text-justify": DataDrivenProperty<"auto" | "left" | "center" | "right">;
	"text-radial-offset": DataDrivenProperty<number>;
	"text-variable-anchor": DataConstantProperty<Array<"center" | "left" | "right" | "top" | "bottom" | "top-left" | "top-right" | "bottom-left" | "bottom-right">>;
	"text-variable-anchor-offset": DataDrivenProperty<VariableAnchorOffsetCollection>;
	"text-anchor": DataDrivenProperty<"center" | "left" | "right" | "top" | "bottom" | "top-left" | "top-right" | "bottom-left" | "bottom-right">;
	"text-max-angle": DataConstantProperty<number>;
	"text-writing-mode": DataConstantProperty<Array<"horizontal" | "vertical">>;
	"text-rotate": DataDrivenProperty<number>;
	"text-padding": DataConstantProperty<number>;
	"text-keep-upright": DataConstantProperty<boolean>;
	"text-transform": DataDrivenProperty<"none" | "uppercase" | "lowercase">;
	"text-offset": DataDrivenProperty<[
		number,
		number
	]>;
	"text-allow-overlap": DataConstantProperty<boolean>;
	"text-overlap": DataConstantProperty<"never" | "always" | "cooperative">;
	"text-ignore-placement": DataConstantProperty<boolean>;
	"text-optional": DataConstantProperty<boolean>;
};
export type SymbolLayoutPropsPossiblyEvaluated = {
	"symbol-placement": "point" | "line" | "line-center";
	"symbol-spacing": number;
	"symbol-avoid-edges": boolean;
	"symbol-sort-key": PossiblyEvaluatedPropertyValue<number>;
	"symbol-z-order": "auto" | "viewport-y" | "source";
	"icon-allow-overlap": boolean;
	"icon-overlap": "never" | "always" | "cooperative";
	"icon-ignore-placement": boolean;
	"icon-optional": boolean;
	"icon-rotation-alignment": "map" | "viewport" | "auto";
	"icon-size": PossiblyEvaluatedPropertyValue<number>;
	"icon-text-fit": "none" | "width" | "height" | "both";
	"icon-text-fit-padding": [
		number,
		number,
		number,
		number
	];
	"icon-image": PossiblyEvaluatedPropertyValue<ResolvedImage>;
	"icon-rotate": PossiblyEvaluatedPropertyValue<number>;
	"icon-padding": PossiblyEvaluatedPropertyValue<Padding>;
	"icon-keep-upright": boolean;
	"icon-offset": PossiblyEvaluatedPropertyValue<[
		number,
		number
	]>;
	"icon-anchor": PossiblyEvaluatedPropertyValue<"center" | "left" | "right" | "top" | "bottom" | "top-left" | "top-right" | "bottom-left" | "bottom-right">;
	"icon-pitch-alignment": "map" | "viewport" | "auto";
	"text-pitch-alignment": "map" | "viewport" | "auto";
	"text-rotation-alignment": "map" | "viewport" | "viewport-glyph" | "auto";
	"text-field": PossiblyEvaluatedPropertyValue<Formatted>;
	"text-font": PossiblyEvaluatedPropertyValue<Array<string>>;
	"text-size": PossiblyEvaluatedPropertyValue<number>;
	"text-max-width": PossiblyEvaluatedPropertyValue<number>;
	"text-line-height": number;
	"text-letter-spacing": PossiblyEvaluatedPropertyValue<number>;
	"text-justify": PossiblyEvaluatedPropertyValue<"auto" | "left" | "center" | "right">;
	"text-radial-offset": PossiblyEvaluatedPropertyValue<number>;
	"text-variable-anchor": Array<"center" | "left" | "right" | "top" | "bottom" | "top-left" | "top-right" | "bottom-left" | "bottom-right">;
	"text-variable-anchor-offset": PossiblyEvaluatedPropertyValue<VariableAnchorOffsetCollection>;
	"text-anchor": PossiblyEvaluatedPropertyValue<"center" | "left" | "right" | "top" | "bottom" | "top-left" | "top-right" | "bottom-left" | "bottom-right">;
	"text-max-angle": number;
	"text-writing-mode": Array<"horizontal" | "vertical">;
	"text-rotate": PossiblyEvaluatedPropertyValue<number>;
	"text-padding": number;
	"text-keep-upright": boolean;
	"text-transform": PossiblyEvaluatedPropertyValue<"none" | "uppercase" | "lowercase">;
	"text-offset": PossiblyEvaluatedPropertyValue<[
		number,
		number
	]>;
	"text-allow-overlap": boolean;
	"text-overlap": "never" | "always" | "cooperative";
	"text-ignore-placement": boolean;
	"text-optional": boolean;
};
export type SymbolPaintProps = {
	"icon-opacity": DataDrivenProperty<number>;
	"icon-color": DataDrivenProperty<Color>;
	"icon-halo-color": DataDrivenProperty<Color>;
	"icon-halo-width": DataDrivenProperty<number>;
	"icon-halo-blur": DataDrivenProperty<number>;
	"icon-translate": DataConstantProperty<[
		number,
		number
	]>;
	"icon-translate-anchor": DataConstantProperty<"map" | "viewport">;
	"text-opacity": DataDrivenProperty<number>;
	"text-color": DataDrivenProperty<Color>;
	"text-halo-color": DataDrivenProperty<Color>;
	"text-halo-width": DataDrivenProperty<number>;
	"text-halo-blur": DataDrivenProperty<number>;
	"text-translate": DataConstantProperty<[
		number,
		number
	]>;
	"text-translate-anchor": DataConstantProperty<"map" | "viewport">;
};
export type SymbolPaintPropsPossiblyEvaluated = {
	"icon-opacity": PossiblyEvaluatedPropertyValue<number>;
	"icon-color": PossiblyEvaluatedPropertyValue<Color>;
	"icon-halo-color": PossiblyEvaluatedPropertyValue<Color>;
	"icon-halo-width": PossiblyEvaluatedPropertyValue<number>;
	"icon-halo-blur": PossiblyEvaluatedPropertyValue<number>;
	"icon-translate": [
		number,
		number
	];
	"icon-translate-anchor": "map" | "viewport";
	"text-opacity": PossiblyEvaluatedPropertyValue<number>;
	"text-color": PossiblyEvaluatedPropertyValue<Color>;
	"text-halo-color": PossiblyEvaluatedPropertyValue<Color>;
	"text-halo-width": PossiblyEvaluatedPropertyValue<number>;
	"text-halo-blur": PossiblyEvaluatedPropertyValue<number>;
	"text-translate": [
		number,
		number
	];
	"text-translate-anchor": "map" | "viewport";
};
declare class SymbolStyleLayer extends StyleLayer {
	_unevaluatedLayout: Layout<SymbolLayoutProps>;
	layout: PossiblyEvaluated<SymbolLayoutProps, SymbolLayoutPropsPossiblyEvaluated>;
	_transitionablePaint: Transitionable<SymbolPaintProps>;
	_transitioningPaint: Transitioning<SymbolPaintProps>;
	paint: PossiblyEvaluated<SymbolPaintProps, SymbolPaintPropsPossiblyEvaluated>;
	constructor(layer: LayerSpecification);
	recalculate(parameters: EvaluationParameters, availableImages: Array<string>): void;
	getValueAndResolveTokens(name: any, feature: Feature, canonical: CanonicalTileID, availableImages: Array<string>): any;
	createBucket(parameters: BucketParameters<any>): SymbolBucket;
	queryRadius(): number;
	queryIntersectsFeature(): boolean;
	_setPaintOverrides(): void;
	_handleOverridablePaintPropertyUpdate<T, R>(name: string, oldValue: PropertyValue<T, R>, newValue: PropertyValue<T, R>): boolean;
	static hasPaintOverride(layout: PossiblyEvaluated<SymbolLayoutProps, SymbolLayoutPropsPossiblyEvaluated>, propertyName: string): boolean;
}
/**
 * A textured quad for rendering a single icon or glyph.
 *
 * The zoom range the glyph can be shown is defined by minScale and maxScale.
 *
 * @param tl - The offset of the top left corner from the anchor.
 * @param tr - The offset of the top right corner from the anchor.
 * @param bl - The offset of the bottom left corner from the anchor.
 * @param br - The offset of the bottom right corner from the anchor.
 * @param tex - The texture coordinates.
 */
export type SymbolQuad = {
	tl: Point;
	tr: Point;
	bl: Point;
	br: Point;
	tex: {
		x: number;
		y: number;
		w: number;
		h: number;
	};
	pixelOffsetTL: Point;
	pixelOffsetBR: Point;
	writingMode: any | void;
	glyphOffset: [
		number,
		number
	];
	sectionIndex: number;
	isSDF: boolean;
	minFontScaleX: number;
	minFontScaleY: number;
};
export type SizeData = {
	kind: "constant";
	layoutSize: number;
} | {
	kind: "source";
} | {
	kind: "camera";
	minZoom: number;
	maxZoom: number;
	minSize: number;
	maxSize: number;
	interpolationType: InterpolationType;
} | {
	kind: "composite";
	minZoom: number;
	maxZoom: number;
	interpolationType: InterpolationType;
};
export type SingleCollisionBox = {
	x1: number;
	y1: number;
	x2: number;
	y2: number;
	anchorPointX: number;
	anchorPointY: number;
};
export type CollisionArrays = {
	textBox?: SingleCollisionBox;
	verticalTextBox?: SingleCollisionBox;
	iconBox?: SingleCollisionBox;
	verticalIconBox?: SingleCollisionBox;
	textFeatureIndex?: number;
	verticalTextFeatureIndex?: number;
	iconFeatureIndex?: number;
	verticalIconFeatureIndex?: number;
};
export type SymbolFeature = {
	sortKey: number | void;
	text: Formatted | void;
	icon: ResolvedImage;
	index: number;
	sourceLayerIndex: number;
	geometry: Array<Array<Point>>;
	properties: any;
	type: "Unknown" | "Point" | "LineString" | "Polygon";
	id?: any;
};
export type SortKeyRange = {
	sortKey: number;
	symbolInstanceStart: number;
	symbolInstanceEnd: number;
};
declare function addDynamicAttributes(dynamicLayoutVertexArray: StructArray, p: Point, angle: number): void;
declare class SymbolBuffers {
	layoutVertexArray: SymbolLayoutArray;
	layoutVertexBuffer: VertexBuffer;
	indexArray: TriangleIndexArray;
	indexBuffer: IndexBuffer;
	programConfigurations: ProgramConfigurationSet<SymbolStyleLayer>;
	segments: SegmentVector;
	dynamicLayoutVertexArray: SymbolDynamicLayoutArray;
	dynamicLayoutVertexBuffer: VertexBuffer;
	opacityVertexArray: SymbolOpacityArray;
	opacityVertexBuffer: VertexBuffer;
	hasVisibleVertices: boolean;
	collisionVertexArray: CollisionVertexArray;
	collisionVertexBuffer: VertexBuffer;
	placedSymbolArray: PlacedSymbolArray;
	constructor(programConfigurations: ProgramConfigurationSet<SymbolStyleLayer>);
	isEmpty(): boolean;
	upload(context: Context, dynamicIndexBuffer: boolean, upload?: boolean, update?: boolean): void;
	destroy(): void;
}
declare class CollisionBuffers {
	layoutVertexArray: StructArray;
	layoutAttributes: Array<StructArrayMember>;
	layoutVertexBuffer: VertexBuffer;
	indexArray: TriangleIndexArray | LineIndexArray;
	indexBuffer: IndexBuffer;
	segments: SegmentVector;
	collisionVertexArray: CollisionVertexArray;
	collisionVertexBuffer: VertexBuffer;
	constructor(LayoutArray: {
		new (...args: any): StructArray;
	}, layoutAttributes: Array<StructArrayMember>, IndexArray: {
		new (...args: any): TriangleIndexArray | LineIndexArray;
	});
	upload(context: Context): void;
	destroy(): void;
}
declare class SymbolBucket implements Bucket {
	static MAX_GLYPHS: number;
	static addDynamicAttributes: typeof addDynamicAttributes;
	collisionBoxArray: CollisionBoxArray;
	zoom: number;
	overscaling: number;
	layers: Array<SymbolStyleLayer>;
	layerIds: Array<string>;
	stateDependentLayers: Array<SymbolStyleLayer>;
	stateDependentLayerIds: Array<string>;
	index: number;
	sdfIcons: boolean;
	iconsInText: boolean;
	iconsNeedLinear: boolean;
	bucketInstanceId: number;
	justReloaded: boolean;
	hasPattern: boolean;
	textSizeData: SizeData;
	iconSizeData: SizeData;
	glyphOffsetArray: GlyphOffsetArray;
	lineVertexArray: SymbolLineVertexArray;
	features: Array<SymbolFeature>;
	symbolInstances: SymbolInstanceArray;
	textAnchorOffsets: TextAnchorOffsetArray;
	collisionArrays: Array<CollisionArrays>;
	sortKeyRanges: Array<SortKeyRange>;
	pixelRatio: number;
	tilePixelRatio: number;
	compareText: {
		[_: string]: Array<Point>;
	};
	fadeStartTime: number;
	sortFeaturesByKey: boolean;
	sortFeaturesByY: boolean;
	canOverlap: boolean;
	sortedAngle: number;
	featureSortOrder: Array<number>;
	collisionCircleArray: Array<number>;
	placementInvProjMatrix: mat4;
	placementViewportMatrix: mat4;
	text: SymbolBuffers;
	icon: SymbolBuffers;
	textCollisionBox: CollisionBuffers;
	iconCollisionBox: CollisionBuffers;
	uploaded: boolean;
	sourceLayerIndex: number;
	sourceID: string;
	symbolInstanceIndexes: Array<number>;
	writingModes: WritingMode[];
	allowVerticalPlacement: boolean;
	hasRTLText: boolean;
	constructor(options: BucketParameters<SymbolStyleLayer>);
	createArrays(): void;
	private calculateGlyphDependencies;
	populate(features: Array<IndexedFeature>, options: PopulateParameters, canonical: CanonicalTileID): void;
	update(states: FeatureStates, vtLayer: VectorTileLayer, imagePositions: {
		[_: string]: ImagePosition;
	}): void;
	isEmpty(): boolean;
	uploadPending(): boolean;
	upload(context: Context): void;
	destroyDebugData(): void;
	destroy(): void;
	addToLineVertexArray(anchor: Anchor, line: any): {
		lineStartIndex: number;
		lineLength: number;
	};
	addSymbols(arrays: SymbolBuffers, quads: Array<SymbolQuad>, sizeVertex: any, lineOffset: [
		number,
		number
	], alongLine: boolean, feature: SymbolFeature, writingMode: WritingMode, labelAnchor: Anchor, lineStartIndex: number, lineLength: number, associatedIconIndex: number, canonical: CanonicalTileID): void;
	_addCollisionDebugVertex(layoutVertexArray: StructArray, collisionVertexArray: StructArray, point: Point, anchorX: number, anchorY: number, extrude: Point): any;
	addCollisionDebugVertices(x1: number, y1: number, x2: number, y2: number, arrays: CollisionBuffers, boxAnchorPoint: Point, symbolInstance: SymbolInstance): void;
	addDebugCollisionBoxes(startIndex: number, endIndex: number, symbolInstance: SymbolInstance, isText: boolean): void;
	generateCollisionDebugBuffers(): void;
	_deserializeCollisionBoxesForSymbol(collisionBoxArray: CollisionBoxArray, textStartIndex: number, textEndIndex: number, verticalTextStartIndex: number, verticalTextEndIndex: number, iconStartIndex: number, iconEndIndex: number, verticalIconStartIndex: number, verticalIconEndIndex: number): CollisionArrays;
	deserializeCollisionBoxes(collisionBoxArray: CollisionBoxArray): void;
	hasTextData(): boolean;
	hasIconData(): boolean;
	hasDebugData(): CollisionBuffers;
	hasTextCollisionBoxData(): boolean;
	hasIconCollisionBoxData(): boolean;
	addIndicesForPlacedSymbol(iconOrText: SymbolBuffers, placedSymbolIndex: number): void;
	getSortedSymbolIndexes(angle: number): any[];
	addToSortKeyRanges(symbolInstanceIndex: number, sortKey: number): void;
	sortFeatures(angle: number): void;
}
export interface SymbolsByKeyEntry {
	index?: KDBush;
	positions?: {
		x: number;
		y: number;
	}[];
	crossTileIDs: number[];
}
declare class TileLayerIndex {
	tileID: OverscaledTileID;
	bucketInstanceId: number;
	_symbolsByKey: Record<number, SymbolsByKeyEntry>;
	constructor(tileID: OverscaledTileID, symbolInstances: SymbolInstanceArray, bucketInstanceId: number);
	getScaledCoordinates(symbolInstance: SymbolInstance, childTileID: OverscaledTileID): {
		x: number;
		y: number;
	};
	findMatches(symbolInstances: SymbolInstanceArray, newTileID: OverscaledTileID, zoomCrossTileIDs: {
		[crossTileID: number]: boolean;
	}): void;
	getCrossTileIDsLists(): number[][];
}
declare class CrossTileIDs {
	maxCrossTileID: number;
	constructor();
	generate(): number;
}
declare class CrossTileSymbolLayerIndex {
	indexes: {
		[zoom in string | number]: {
			[tileId in string | number]: TileLayerIndex;
		};
	};
	usedCrossTileIDs: {
		[zoom in string | number]: {
			[crossTileID: number]: boolean;
		};
	};
	lng: number;
	constructor();
	handleWrapJump(lng: number): void;
	addBucket(tileID: OverscaledTileID, bucket: SymbolBucket, crossTileIDs: CrossTileIDs): boolean;
	removeBucketCrossTileIDs(zoom: string | number, removedBucket: TileLayerIndex): void;
	removeStaleBuckets(currentIDs: {
		[k in string | number]: boolean;
	}): boolean;
}
declare class CrossTileSymbolIndex {
	layerIndexes: {
		[layerId: string]: CrossTileSymbolLayerIndex;
	};
	crossTileIDs: CrossTileIDs;
	maxBucketInstanceId: number;
	bucketsInCurrentPlacement: {
		[_: number]: boolean;
	};
	constructor();
	addLayer(styleLayer: StyleLayer, tiles: Array<Tile>, lng: number): boolean;
	pruneUnusedLayers(usedLayers: Array<string>): void;
}
declare class DepthMode {
	func: DepthFuncType;
	mask: DepthMaskType;
	range: DepthRangeType;
	static ReadOnly: boolean;
	static ReadWrite: boolean;
	constructor(depthFunc: DepthFuncType, depthMask: DepthMaskType, depthRange: DepthRangeType);
	static disabled: Readonly<DepthMode>;
}
declare class StencilMode {
	test: StencilTestGL;
	ref: number;
	mask: number;
	fail: StencilOpConstant;
	depthFail: StencilOpConstant;
	pass: StencilOpConstant;
	constructor(test: StencilTestGL, ref: number, mask: number, fail: StencilOpConstant, depthFail: StencilOpConstant, pass: StencilOpConstant);
	static disabled: Readonly<StencilMode>;
}
declare class ColorMode {
	blendFunction: BlendFuncType;
	blendColor: Color;
	mask: ColorMaskType;
	constructor(blendFunction: BlendFuncType, blendColor: Color, mask: ColorMaskType);
	static Replace: BlendFuncType;
	static disabled: Readonly<ColorMode>;
	static unblended: Readonly<ColorMode>;
	static alphaBlended: Readonly<ColorMode>;
}
export type PoolObject = {
	id: number;
	fbo: Framebuffer;
	texture: Texture;
	stamp: number;
	inUse: boolean;
};
declare class RenderPool {
	private readonly _context;
	private readonly _size;
	private readonly _tileSize;
	private _objects;
	/**
	 * An index array of recently used pool objects.
	 * Items that are used recently are last in the array
	 */
	private _recentlyUsed;
	private _stamp;
	constructor(_context: Context, _size: number, _tileSize: number);
	destruct(): void;
	private _createObject;
	getObjectForId(id: number): PoolObject;
	useObject(obj: PoolObject): void;
	stampObject(obj: PoolObject): void;
	getOrCreateFreeObject(): PoolObject;
	freeObject(obj: PoolObject): void;
	freeAllObjects(): void;
	isFull(): boolean;
}
declare class RenderToTexture {
	painter: Painter;
	terrain: Terrain;
	pool: RenderPool;
	/**
	 * coordsDescendingInv contains a list of all tiles which should be rendered for one render-to-texture tile
	 * e.g. render 4 raster-tiles with size 256px to the 512px render-to-texture tile
	 */
	_coordsDescendingInv: {
		[_: string]: {
			[_: string]: Array<OverscaledTileID>;
		};
	};
	/**
	 * create a string representation of all to tiles rendered to render-to-texture tiles
	 * this string representation is used to check if tile should be re-rendered.
	 */
	_coordsDescendingInvStr: {
		[_: string]: {
			[_: string]: string;
		};
	};
	/**
	 * store for render-stacks
	 * a render stack is a set of layers which should be rendered into one texture
	 * every stylesheet can have multiple stacks. A new stack is created if layers which should
	 * not rendered to texture sit between layers which should rendered to texture. e.g. hillshading or symbols
	 */
	_stacks: Array<Array<string>>;
	/**
	 * remember the previous processed layer to check if a new stack is needed
	 */
	_prevType: string;
	/**
	 * a list of tiles that can potentially rendered
	 */
	_renderableTiles: Array<Tile>;
	/**
	 * a list of tiles that should be rendered to screen in the next render-call
	 */
	_rttTiles: Array<Tile>;
	/**
	 * a list of all layer-ids which should be rendered
	 */
	_renderableLayerIds: Array<string>;
	constructor(painter: Painter, terrain: Terrain);
	destruct(): void;
	getTexture(tile: Tile): Texture;
	prepareForRender(style: Style, zoom: number): void;
	/**
	 * due that switching textures is relatively slow, the render
	 * layer-by-layer context is not practicable. To bypass this problem
	 * this lines of code stack all layers and later render all at once.
	 * Because of the stylesheet possibility to mixing render-to-texture layers
	 * and 'live'-layers (f.e. symbols) it is necessary to create more stacks. For example
	 * a symbol-layer is in between of fill-layers.
	 * @param layer - the layer to render
	 * @returns if true layer is rendered to texture, otherwise false
	 */
	renderLayer(layer: StyleLayer): boolean;
}
/**
 * A dash entry
 */
export type DashEntry = {
	y: number;
	height: number;
	width: number;
};
declare class LineAtlas {
	width: number;
	height: number;
	nextRow: number;
	bytes: number;
	data: Uint8Array;
	dashEntry: {
		[_: string]: DashEntry;
	};
	dirty: boolean;
	texture: WebGLTexture;
	constructor(width: number, height: number);
	/**
	 * Get or create a dash line pattern.
	 *
	 * @param dasharray - the key (represented by numbers) to get the dash texture
	 * @param round - whether to add circle caps in between dash segments
	 * @returns position of dash texture in {@link DashEntry}
	 */
	getDash(dasharray: Array<number>, round: boolean): DashEntry;
	getDashRanges(dasharray: Array<number>, lineAtlasWidth: number, stretch: number): any[];
	addRoundDash(ranges: any, stretch: number, n: number): void;
	addRegularDash(ranges: any): void;
	addDash(dasharray: Array<number>, round: boolean): DashEntry;
	bind(context: Context): void;
}
/**
 * A type of MapLibre resource.
 */
export declare const enum ResourceType {
	Glyphs = "Glyphs",
	Image = "Image",
	Source = "Source",
	SpriteImage = "SpriteImage",
	SpriteJSON = "SpriteJSON",
	Style = "Style",
	Tile = "Tile",
	Unknown = "Unknown"
}
/**
 * This function is used to tranform a request.
 * It is used just before executing the relevant request.
 */
export type RequestTransformFunction = (url: string, resourceType?: ResourceType) => RequestParameters | undefined;
declare class RequestManager {
	_transformRequestFn: RequestTransformFunction;
	constructor(transformRequestFn?: RequestTransformFunction);
	transformRequest(url: string, type: ResourceType): RequestParameters;
	setTransformRequest(transformRequest: RequestTransformFunction): void;
}
declare function loadGlyphRange(fontstack: string, range: number, urlTemplate: string, requestManager: RequestManager): Promise<{
	[_: number]: StyleGlyph | null;
}>;
export type Entry = {
	glyphs: {
		[id: number]: StyleGlyph | null;
	};
	requests: {
		[range: number]: Promise<{
			[_: number]: StyleGlyph | null;
		}>;
	};
	ranges: {
		[range: number]: boolean | null;
	};
	tinySDF?: TinySDF;
};
declare class GlyphManager {
	requestManager: RequestManager;
	localIdeographFontFamily: string | false;
	entries: {
		[stack: string]: Entry;
	};
	url: string;
	static loadGlyphRange: typeof loadGlyphRange;
	static TinySDF: typeof TinySDF;
	constructor(requestManager: RequestManager, localIdeographFontFamily?: string | false);
	setURL(url?: string | null): void;
	getGlyphs(glyphs: {
		[stack: string]: Array<number>;
	}): Promise<GetGlyphsResponse>;
	_getAndCacheGlyphsPromise(stack: string, id: number): Promise<{
		stack: string;
		id: number;
		glyph: StyleGlyph;
	}>;
	_doesCharSupportLocalGlyph(id: number): boolean;
	_tinySDF(entry: Entry, stack: string, id: number): StyleGlyph;
}
export type RenderPass = "offscreen" | "opaque" | "translucent";
export type PainterOptions = {
	showOverdrawInspector: boolean;
	showTileBoundaries: boolean;
	showPadding: boolean;
	rotating: boolean;
	zooming: boolean;
	moving: boolean;
	fadeDuration: number;
};
declare class Painter {
	context: Context;
	transform: Transform;
	renderToTexture: RenderToTexture;
	_tileTextures: {
		[_: number]: Array<Texture>;
	};
	numSublayers: number;
	depthEpsilon: number;
	emptyProgramConfiguration: ProgramConfiguration;
	width: number;
	height: number;
	pixelRatio: number;
	tileExtentBuffer: VertexBuffer;
	tileExtentSegments: SegmentVector;
	debugBuffer: VertexBuffer;
	debugSegments: SegmentVector;
	rasterBoundsBuffer: VertexBuffer;
	rasterBoundsSegments: SegmentVector;
	viewportBuffer: VertexBuffer;
	viewportSegments: SegmentVector;
	quadTriangleIndexBuffer: IndexBuffer;
	tileBorderIndexBuffer: IndexBuffer;
	_tileClippingMaskIDs: {
		[_: string]: number;
	};
	stencilClearMode: StencilMode;
	style: Style;
	options: PainterOptions;
	lineAtlas: LineAtlas;
	imageManager: ImageManager;
	glyphManager: GlyphManager;
	depthRangeFor3D: DepthRangeType;
	opaquePassCutoff: number;
	renderPass: RenderPass;
	currentLayer: number;
	currentStencilSource: string;
	nextStencilID: number;
	id: string;
	_showOverdrawInspector: boolean;
	cache: {
		[_: string]: Program<any>;
	};
	crossTileSymbolIndex: CrossTileSymbolIndex;
	symbolFadeChange: number;
	debugOverlayTexture: Texture;
	debugOverlayCanvas: HTMLCanvasElement;
	terrainFacilitator: {
		dirty: boolean;
		matrix: mat4;
		renderTime: number;
	};
	constructor(gl: WebGLRenderingContext | WebGL2RenderingContext, transform: Transform);
	resize(width: number, height: number, pixelRatio: number): void;
	setup(): void;
	clearStencil(): void;
	_renderTileClippingMasks(layer: StyleLayer, tileIDs: Array<OverscaledTileID>): void;
	stencilModeFor3D(): StencilMode;
	stencilModeForClipping(tileID: OverscaledTileID): StencilMode;
	stencilConfigForOverlap(tileIDs: Array<OverscaledTileID>): [
		{
			[_: number]: Readonly<StencilMode>;
		},
		Array<OverscaledTileID>
	];
	colorModeForRenderPass(): Readonly<ColorMode>;
	depthModeForSublayer(n: number, mask: DepthMaskType, func?: DepthFuncType | null): Readonly<DepthMode>;
	opaquePassEnabledForLayer(): boolean;
	render(style: Style, options: PainterOptions): void;
	/**
	 * Update the depth and coords framebuffers, if the contents of those frame buffers is out of date.
	 * If requireExact is false, then the contents of those frame buffers is not updated if it is close
	 * to accurate (that is, the camera has not moved much since it was updated last).
	 */
	maybeDrawDepthAndCoords(requireExact: boolean): void;
	renderLayer(painter: Painter, sourceCache: SourceCache, layer: StyleLayer, coords: Array<OverscaledTileID>): void;
	/**
	 * Transform a matrix to incorporate the *-translate and *-translate-anchor properties into it.
	 * @param inViewportPixelUnitsUnits - True when the units accepted by the matrix are in viewport pixels instead of tile units.
	 * @returns matrix
	 */
	translatePosMatrix(matrix: mat4, tile: Tile, translate: [
		number,
		number
	], translateAnchor: "map" | "viewport", inViewportPixelUnitsUnits?: boolean): mat4;
	saveTileTexture(texture: Texture): void;
	getTileTexture(size: number): Texture;
	/**
	 * Checks whether a pattern image is needed, and if it is, whether it is not loaded.
	 *
	 * @returns true if a needed image is missing and rendering needs to be skipped.
	 */
	isPatternMissing(image?: CrossFaded<ResolvedImage> | null): boolean;
	useProgram(name: string, programConfiguration?: ProgramConfiguration | null): Program<any>;
	setCustomLayerDefaults(): void;
	setBaseState(): void;
	initDebugOverlayCanvas(): void;
	destroy(): void;
	overLimit(): boolean;
}
declare class TerrainSourceCache extends Evented {
	/**
	 * source-cache for the raster-dem source.
	 */
	sourceCache: SourceCache;
	/**
	 * stores all render-to-texture tiles.
	 */
	_tiles: {
		[_: string]: Tile;
	};
	/**
	 * contains a list of tileID-keys for the current scene. (only for performance)
	 */
	_renderableTilesKeys: Array<string>;
	/**
	 * raster-dem-tile for a TileID cache.
	 */
	_sourceTileCache: {
		[_: string]: string;
	};
	/**
	 * minimum zoomlevel to render the terrain.
	 */
	minzoom: number;
	/**
	 * maximum zoomlevel to render the terrain.
	 */
	maxzoom: number;
	/**
	 * render-to-texture tileSize in scene.
	 */
	tileSize: number;
	/**
	 * raster-dem tiles will load for performance the actualZoom - deltaZoom zoom-level.
	 */
	deltaZoom: number;
	constructor(sourceCache: SourceCache);
	destruct(): void;
	/**
	 * Load Terrain Tiles, create internal render-to-texture tiles, free GPU memory.
	 * @param transform - the operation to do
	 * @param terrain - the terrain
	 */
	update(transform: Transform, terrain: Terrain): void;
	/**
	 * Free render to texture cache
	 * @param tileID - optional, free only corresponding to tileID.
	 */
	freeRtt(tileID?: OverscaledTileID): void;
	/**
	 * get a list of tiles, which are loaded and should be rendered in the current scene
	 * @returns the renderable tiles
	 */
	getRenderableTiles(): Array<Tile>;
	/**
	 * get terrain tile by the TileID key
	 * @param id - the tile id
	 * @returns the tile
	 */
	getTileByID(id: string): Tile;
	/**
	 * Searches for the corresponding current renderable terrain-tiles
	 * @param tileID - the tile to look for
	 * @returns the tiles that were found
	 */
	getTerrainCoords(tileID: OverscaledTileID): Record<string, OverscaledTileID>;
	/**
	 * find the covering raster-dem tile
	 * @param tileID - the tile to look for
	 * @param searchForDEM - Optional parameter to search for (parent) sourcetiles with loaded dem.
	 * @returns the tile
	 */
	getSourceTile(tileID: OverscaledTileID, searchForDEM?: boolean): Tile;
	/**
	 * get a list of tiles, loaded after a specific time. This is used to update depth & coords framebuffers.
	 * @param time - the time
	 * @returns the relevant tiles
	 */
	tilesAfterTime(time?: number): Array<Tile>;
}
declare class Mesh {
	vertexBuffer: VertexBuffer;
	indexBuffer: IndexBuffer;
	segments: SegmentVector;
	constructor(vertexBuffer: VertexBuffer, indexBuffer: IndexBuffer, segments: SegmentVector);
	destroy(): void;
}
/**
 * @internal
 * A terrain GPU related object
 */
export type TerrainData = {
	"u_depth": number;
	"u_terrain": number;
	"u_terrain_dim": number;
	"u_terrain_matrix": mat4;
	"u_terrain_unpack": number[];
	"u_terrain_exaggeration": number;
	texture: WebGLTexture;
	depthTexture: WebGLTexture;
	tile: Tile;
};
declare class Terrain {
	/**
	 * The style this terrain corresponds to
	 */
	painter: Painter;
	/**
	 * the sourcecache this terrain is based on
	 */
	sourceCache: TerrainSourceCache;
	/**
	 * the TerrainSpecification object passed to this instance
	 */
	options: TerrainSpecification;
	/**
	 * define the meshSize per tile.
	 */
	meshSize: number;
	/**
	 * multiplicator for the elevation. Used to make terrain more "extreme".
	 */
	exaggeration: number;
	/**
	 * to not see pixels in the render-to-texture tiles it is good to render them bigger
	 * this number is the multiplicator (must be a power of 2) for the current tileSize.
	 * So to get good results with not too much memory footprint a value of 2 should be fine.
	 */
	qualityFactor: number;
	/**
	 * holds the framebuffer object in size of the screen to render the coords & depth into a texture.
	 */
	_fbo: Framebuffer;
	_fboCoordsTexture: Texture;
	_fboDepthTexture: Texture;
	_emptyDepthTexture: Texture;
	/**
	 * GL Objects for the terrain-mesh
	 * The mesh is a regular mesh, which has the advantage that it can be reused for all tiles.
	 */
	_mesh: Mesh;
	/**
	 * coords index contains a list of tileID.keys. This index is used to identify
	 * the tile via the alpha-cannel in the coords-texture.
	 * As the alpha-channel has 1 Byte a max of 255 tiles can rendered without an error.
	 */
	coordsIndex: Array<string>;
	/**
	 * tile-coords encoded in the rgb channel, _coordsIndex is in the alpha-channel.
	 */
	_coordsTexture: Texture;
	/**
	 * accuracy of the coords. 2 * tileSize should be enough.
	 */
	_coordsTextureSize: number;
	/**
	 * variables for an empty dem texture, which is used while the raster-dem tile is loading.
	 */
	_emptyDemUnpack: number[];
	_emptyDemTexture: Texture;
	_emptyDemMatrix: mat4;
	/**
	 * as of overzooming of raster-dem tiles in high zoomlevels, this cache contains
	 * matrices to transform from vector-tile coords to raster-dem-tile coords.
	 */
	_demMatrixCache: {
		[_: string]: {
			matrix: mat4;
			coord: OverscaledTileID;
		};
	};
	constructor(painter: Painter, sourceCache: SourceCache, options: TerrainSpecification);
	/**
	 * get the elevation-value from original dem-data for a given tile-coordinate
	 * @param tileID - the tile to get elevation for
	 * @param x - between 0 .. EXTENT
	 * @param y - between 0 .. EXTENT
	 * @param extent - optional, default 8192
	 * @returns the elevation
	 */
	getDEMElevation(tileID: OverscaledTileID, x: number, y: number, extent?: number): number;
	/**
	 * Get the elevation for given {@link LngLat} in respect of exaggeration.
	 * @param lnglat - the location
	 * @param zoom - the zoom
	 * @returns the elevation
	 */
	getElevationForLngLatZoom(lnglat: LngLat, zoom: number): number;
	/**
	 * Get the elevation for given coordinate in respect of exaggeration.
	 * @param tileID - the tile id
	 * @param x - between 0 .. EXTENT
	 * @param y - between 0 .. EXTENT
	 * @param extent - optional, default 8192
	 * @returns the elevation
	 */
	getElevation(tileID: OverscaledTileID, x: number, y: number, extent?: number): number;
	/**
	 * returns a Terrain Object for a tile. Unless the tile corresponds to data (e.g. tile is loading), return a flat dem object
	 * @param tileID - the tile to get the terrain for
	 * @returns the terrain data to use in the program
	 */
	getTerrainData(tileID: OverscaledTileID): TerrainData;
	/**
	 * get a framebuffer as big as the map-div, which will be used to render depth & coords into a texture
	 * @param texture - the texture
	 * @returns the frame buffer
	 */
	getFramebuffer(texture: string): Framebuffer;
	/**
	 * create coords texture, needed to grab coordinates from canvas
	 * encode coords coordinate into 4 bytes:
	 *   - 8 lower bits for x
	 *   - 8 lower bits for y
	 *   - 4 higher bits for x
	 *   - 4 higher bits for y
	 *   - 8 bits for coordsIndex (1 .. 255) (= number of terraintile), is later setted in draw_terrain uniform value
	 * @returns the texture
	 */
	getCoordsTexture(): Texture;
	/**
	 * Reads a pixel from the coords-framebuffer and translate this to mercator.
	 * @param p - Screen-Coordinate
	 * @returns mercator coordinate for a screen pixel
	 */
	pointCoordinate(p: Point): MercatorCoordinate;
	/**
	 * Reads the depth value from the depth-framebuffer at a given screen pixel
	 * @param p - Screen coordinate
	 * @returns depth value in clip space (between 0 and 1)
	 */
	depthAtPoint(p: Point): number;
	/**
	 * create a regular mesh which will be used by all terrain-tiles
	 * @returns the created regular mesh
	 */
	getTerrainMesh(): Mesh;
	/**
	 * Calculates a height of the frame around the terrain-mesh to avoid stiching between
	 * tile boundaries in different zoomlevels.
	 * @param zoom - current zoomlevel
	 * @returns the elevation delta in meters
	 */
	getMeshFrameDelta(zoom: number): number;
	getMinTileElevationForLngLatZoom(lnglat: LngLat, zoom: number): number;
	/**
	 * Get the minimum and maximum elevation contained in a tile. This includes any
	 * exaggeration included in the terrain.
	 *
	 * @param tileID - ID of the tile to be used as a source for the min/max elevation
	 * @returns the minimum and maximum elevation found in the tile, including the terrain's
	 * exaggeration
	 */
	getMinMaxElevation(tileID: OverscaledTileID): {
		minElevation: number | null;
		maxElevation: number | null;
	};
	_getOverscaledTileIDFromLngLatZoom(lnglat: LngLat, zoom: number): {
		tileID: OverscaledTileID;
		mercatorX: number;
		mercatorY: number;
	};
}
declare class Transform {
	tileSize: number;
	tileZoom: number;
	lngRange: [
		number,
		number
	];
	latRange: [
		number,
		number
	];
	scale: number;
	width: number;
	height: number;
	angle: number;
	rotationMatrix: mat2;
	pixelsToGLUnits: [
		number,
		number
	];
	cameraToCenterDistance: number;
	mercatorMatrix: mat4;
	projectionMatrix: mat4;
	modelViewProjectionMatrix: mat4;
	invModelViewProjectionMatrix: mat4;
	alignedModelViewProjectionMatrix: mat4;
	fogMatrix: mat4;
	pixelMatrix: mat4;
	pixelMatrix3D: mat4;
	pixelMatrixInverse: mat4;
	glCoordMatrix: mat4;
	labelPlaneMatrix: mat4;
	minElevationForCurrentTile: number;
	_fov: number;
	_pitch: number;
	_zoom: number;
	_unmodified: boolean;
	_renderWorldCopies: boolean;
	_minZoom: number;
	_maxZoom: number;
	_minPitch: number;
	_maxPitch: number;
	_center: LngLat;
	_elevation: number;
	_pixelPerMeter: number;
	_edgeInsets: EdgeInsets;
	_constraining: boolean;
	_posMatrixCache: {
		[_: string]: mat4;
	};
	_alignedPosMatrixCache: {
		[_: string]: mat4;
	};
	_fogMatrixCache: {
		[_: string]: mat4;
	};
	/**
	 * This value represents the distance from the camera to the far clipping plane.
	 * It is used in the calculation of the projection matrix to determine which objects are visible.
	 * farZ should be larger than nearZ.
	 */
	farZ: number;
	/**
	 * This value represents the distance from the camera to the near clipping plane.
	 * It is used in the calculation of the projection matrix to determine which objects are visible.
	 * nearZ should be smaller than farZ.
	 */
	nearZ: number;
	constructor(minZoom?: number, maxZoom?: number, minPitch?: number, maxPitch?: number, renderWorldCopies?: boolean);
	clone(): Transform;
	apply(that: Transform): void;
	get minZoom(): number;
	set minZoom(zoom: number);
	get maxZoom(): number;
	set maxZoom(zoom: number);
	get minPitch(): number;
	set minPitch(pitch: number);
	get maxPitch(): number;
	set maxPitch(pitch: number);
	get renderWorldCopies(): boolean;
	set renderWorldCopies(renderWorldCopies: boolean);
	get worldSize(): number;
	get centerOffset(): Point;
	get size(): Point;
	get bearing(): number;
	set bearing(bearing: number);
	get pitch(): number;
	set pitch(pitch: number);
	get fov(): number;
	set fov(fov: number);
	get zoom(): number;
	set zoom(zoom: number);
	get center(): LngLat;
	set center(center: LngLat);
	/**
	 * Elevation at current center point, meters above sea level
	 */
	get elevation(): number;
	set elevation(elevation: number);
	get padding(): PaddingOptions;
	set padding(padding: PaddingOptions);
	/**
	 * The center of the screen in pixels with the top-left corner being (0,0)
	 * and +y axis pointing downwards. This accounts for padding.
	 */
	get centerPoint(): Point;
	/**
	 * Returns if the padding params match
	 *
	 * @param padding - the padding to check against
	 * @returns true if they are equal, false otherwise
	 */
	isPaddingEqual(padding: PaddingOptions): boolean;
	/**
	 * Helper method to update edge-insets in place
	 *
	 * @param start - the starting padding
	 * @param target - the target padding
	 * @param t - the step/weight
	 */
	interpolatePadding(start: PaddingOptions, target: PaddingOptions, t: number): void;
	/**
	 * Return a zoom level that will cover all tiles the transform
	 * @param options - the options
	 * @returns zoom level An integer zoom level at which all tiles will be visible.
	 */
	coveringZoomLevel(options: {
		/**
		 * Target zoom level. If true, the value will be rounded to the closest integer. Otherwise the value will be floored.
		 */
		roundZoom?: boolean;
		/**
		 * Tile size, expressed in screen pixels.
		 */
		tileSize: number;
	}): number;
	/**
	 * Return any "wrapped" copies of a given tile coordinate that are visible
	 * in the current view.
	 */
	getVisibleUnwrappedCoordinates(tileID: CanonicalTileID): UnwrappedTileID[];
	/**
	 * Return all coordinates that could cover this transform for a covering
	 * zoom level.
	 * @param options - the options
	 * @returns OverscaledTileIDs
	 */
	coveringTiles(options: {
		tileSize: number;
		minzoom?: number;
		maxzoom?: number;
		roundZoom?: boolean;
		reparseOverscaled?: boolean;
		renderWorldCopies?: boolean;
		terrain?: Terrain;
	}): Array<OverscaledTileID>;
	resize(width: number, height: number): void;
	get unmodified(): boolean;
	zoomScale(zoom: number): number;
	scaleZoom(scale: number): number;
	/**
	 * Convert from LngLat to world coordinates (Mercator coordinates scaled by 512)
	 * @param lnglat - the lngLat
	 * @returns Point
	 */
	project(lnglat: LngLat): Point;
	/**
	 * Convert from world coordinates ([0, 512],[0, 512]) to LngLat ([-180, 180], [-90, 90])
	 * @param point - world coordinate
	 * @returns LngLat
	 */
	unproject(point: Point): LngLat;
	get point(): Point;
	/**
	 * get the camera position in LngLat and altitudes in meter
	 * @returns An object with lngLat & altitude.
	 */
	getCameraPosition(): {
		lngLat: LngLat;
		altitude: number;
	};
	/**
	 * This method works in combination with freezeElevation activated.
	 * freezeElevation is enabled during map-panning because during this the camera should sit in constant height.
	 * After panning finished, call this method to recalculate the zoomlevel for the current camera-height in current terrain.
	 * @param terrain - the terrain
	 */
	recalculateZoom(terrain: Terrain): void;
	setLocationAtPoint(lnglat: LngLat, point: Point): void;
	/**
	 * Given a LngLat location, return the screen point that corresponds to it
	 * @param lnglat - location
	 * @param terrain - optional terrain
	 * @returns screen point
	 */
	locationPoint(lnglat: LngLat, terrain?: Terrain): Point;
	/**
	 * Given a point on screen, return its lnglat
	 * @param p - screen point
	 * @param terrain - optional terrain
	 * @returns lnglat location
	 */
	pointLocation(p: Point, terrain?: Terrain): LngLat;
	/**
	 * Given a geographical lnglat, return an unrounded
	 * coordinate that represents it at low zoom level.
	 * @param lnglat - the location
	 * @returns The mercator coordinate
	 */
	locationCoordinate(lnglat: LngLat): MercatorCoordinate;
	/**
	 * Given a Coordinate, return its geographical position.
	 * @param coord - mercator coordinates
	 * @returns lng and lat
	 */
	coordinateLocation(coord: MercatorCoordinate): LngLat;
	/**
	 * Given a Point, return its mercator coordinate.
	 * @param p - the point
	 * @param terrain - optional terrain
	 * @returns lnglat
	 */
	pointCoordinate(p: Point, terrain?: Terrain): MercatorCoordinate;
	/**
	 * Given a coordinate, return the screen point that corresponds to it
	 * @param coord - the coordinates
	 * @param elevation - the elevation
	 * @param pixelMatrix - the pixel matrix
	 * @returns screen point
	 */
	coordinatePoint(coord: MercatorCoordinate, elevation?: number, pixelMatrix?: mat4): Point;
	/**
	 * Returns the map's geographical bounds. When the bearing or pitch is non-zero, the visible region is not
	 * an axis-aligned rectangle, and the result is the smallest bounds that encompasses the visible region.
	 * @returns Returns a {@link LngLatBounds} object describing the map's geographical bounds.
	 */
	getBounds(): LngLatBounds;
	/**
	 * Returns the maximum geographical bounds the map is constrained to, or `null` if none set.
	 * @returns max bounds
	 */
	getMaxBounds(): LngLatBounds | null;
	/**
	 * Calculate pixel height of the visible horizon in relation to map-center (e.g. height/2),
	 * multiplied by a static factor to simulate the earth-radius.
	 * The calculated value is the horizontal line from the camera-height to sea-level.
	 * @returns Horizon above center in pixels.
	 */
	getHorizon(): number;
	/**
	 * Sets or clears the map's geographical constraints.
	 * @param bounds - A {@link LngLatBounds} object describing the new geographic boundaries of the map.
	 */
	setMaxBounds(bounds?: LngLatBounds | null): void;
	calculateTileMatrix(unwrappedTileID: UnwrappedTileID): mat4;
	/**
	 * Calculate the posMatrix that, given a tile coordinate, would be used to display the tile on a map.
	 * @param unwrappedTileID - the tile ID
	 */
	calculatePosMatrix(unwrappedTileID: UnwrappedTileID, aligned?: boolean): mat4;
	/**
	 * Calculate the fogMatrix that, given a tile coordinate, would be used to calculate fog on the map.
	 * @param unwrappedTileID - the tile ID
	 * @private
	 */
	calculateFogMatrix(unwrappedTileID: UnwrappedTileID): mat4;
	customLayerMatrix(): mat4;
	/**
	 * Get center lngLat and zoom to ensure that
	 * 1) everything beyond the bounds is excluded
	 * 2) a given lngLat is as near the center as possible
	 * Bounds are those set by maxBounds or North & South "Poles" and, if only 1 globe is displayed, antimeridian.
	 */
	getConstrained(lngLat: LngLat, zoom: number): {
		center: LngLat;
		zoom: number;
	};
	_constrain(): void;
	_calcMatrices(): void;
	maxPitchScaleFactor(): number;
	/**
	 * The camera looks at the map from a 3D (lng, lat, altitude) location. Let's use `cameraLocation`
	 * as the name for the location under the camera and on the surface of the earth (lng, lat, 0).
	 * `cameraPoint` is the projected position of the `cameraLocation`.
	 *
	 * This point is useful to us because only fill-extrusions that are between `cameraPoint` and
	 * the query point on the surface of the earth can extend and intersect the query.
	 *
	 * When the map is not pitched the `cameraPoint` is equivalent to the center of the map because
	 * the camera is right above the center of the map.
	 */
	getCameraPoint(): Point;
	/**
	 * When the map is pitched, some of the 3D features that intersect a query will not intersect
	 * the query at the surface of the earth. Instead the feature may be closer and only intersect
	 * the query because it extrudes into the air.
	 * @param queryGeometry - For point queries, the line from the query point to the "camera point",
	 * for other geometries, the envelope of the query geometry and the "camera point"
	 * @returns a geometry that includes all of the original query as well as all possible ares of the
	 * screen where the *base* of a visible extrusion could be.
	 *
	 */
	getCameraQueryGeometry(queryGeometry: Array<Point>): Array<Point>;
	/**
	 * Return the distance to the camera in clip space from a LngLat.
	 * This can be compared to the value from the depth buffer (terrain.depthAtPoint)
	 * to determine whether a point is occluded.
	 * @param lngLat - the point
	 * @param elevation - the point's elevation
	 * @returns depth value in clip space (between 0 and 1)
	 */
	lngLatToCameraDepth(lngLat: LngLat, elevation: number): number;
}
export type QueryParameters = {
	scale: number;
	pixelPosMatrix: mat4;
	transform: Transform;
	tileSize: number;
	queryGeometry: Array<Point>;
	cameraQueryGeometry: Array<Point>;
	queryPadding: number;
	params: {
		filter: FilterSpecification;
		layers: Array<string>;
		availableImages: Array<string>;
	};
};
declare class FeatureIndex {
	tileID: OverscaledTileID;
	x: number;
	y: number;
	z: number;
	grid: TransferableGridIndex;
	grid3D: TransferableGridIndex;
	featureIndexArray: FeatureIndexArray;
	promoteId?: PromoteIdSpecification;
	rawTileData: ArrayBuffer;
	bucketLayerIDs: Array<Array<string>>;
	vtLayers: {
		[_: string]: VectorTileLayer;
	};
	sourceLayerCoder: DictionaryCoder;
	constructor(tileID: OverscaledTileID, promoteId?: PromoteIdSpecification | null);
	insert(feature: VectorTileFeature, geometry: Array<Array<Point>>, featureIndex: number, sourceLayerIndex: number, bucketIndex: number, is3D?: boolean): void;
	loadVTLayers(): {
		[_: string]: VectorTileLayer;
	};
	query(args: QueryParameters, styleLayers: {
		[_: string]: StyleLayer;
	}, serializedLayers: {
		[_: string]: any;
	}, sourceFeatureState: SourceFeatureState): {
		[_: string]: Array<{
			featureIndex: number;
			feature: GeoJSONFeature;
		}>;
	};
	loadMatchingFeature(result: {
		[_: string]: Array<{
			featureIndex: number;
			feature: GeoJSONFeature;
			intersectionZ?: boolean | number;
		}>;
	}, bucketIndex: number, sourceLayerIndex: number, featureIndex: number, filter: FeatureFilter, filterLayerIDs: Array<string>, availableImages: Array<string>, styleLayers: {
		[_: string]: StyleLayer;
	}, serializedLayers: {
		[_: string]: any;
	}, sourceFeatureState?: SourceFeatureState, intersectionTest?: (feature: VectorTileFeature, styleLayer: StyleLayer, featureState: any, id: string | number | void) => boolean | number): void;
	lookupSymbolFeatures(symbolFeatureIndexes: Array<number>, serializedLayers: {
		[_: string]: StyleLayer;
	}, bucketIndex: number, sourceLayerIndex: number, filterSpec: FilterSpecification, filterLayerIDs: Array<string>, availableImages: Array<string>, styleLayers: {
		[_: string]: StyleLayer;
	}): {};
	hasLayer(id: string): boolean;
	getId(feature: VectorTileFeature, sourceLayerId: string): string | number;
}
/**
 * The possible DEM encoding types
 */
export type DEMEncoding = "mapbox" | "terrarium" | "custom";
declare class DEMData {
	uid: string | number;
	data: Uint32Array;
	stride: number;
	dim: number;
	min: number;
	max: number;
	redFactor: number;
	greenFactor: number;
	blueFactor: number;
	baseShift: number;
	/**
	 * Constructs a `DEMData` object
	 * @param uid - the tile's unique id
	 * @param data - RGBAImage data has uniform 1px padding on all sides: square tile edge size defines stride
	// and dim is calculated as stride - 2.
	 * @param encoding - the encoding type of the data
	 * @param redFactor - the red channel factor used to unpack the data, used for `custom` encoding only
	 * @param greenFactor - the green channel factor used to unpack the data, used for `custom` encoding only
	 * @param blueFactor - the blue channel factor used to unpack the data, used for `custom` encoding only
	 * @param baseShift - the base shift used to unpack the data, used for `custom` encoding only
	 */
	constructor(uid: string | number, data: RGBAImage | ImageData, encoding: DEMEncoding, redFactor?: number, greenFactor?: number, blueFactor?: number, baseShift?: number);
	get(x: number, y: number): number;
	getUnpackVector(): number[];
	_idx(x: number, y: number): number;
	unpack(r: number, g: number, b: number): number;
	getPixels(): RGBAImage;
	backfillBorder(borderTile: DEMData, dx: number, dy: number): void;
}
/**
 * Parameters to identify a tile
 */
export type TileParameters = {
	type: string;
	source: string;
	uid: string | number;
};
/**
 * Parameters that are send when requesting to load a tile to the worker
 */
export type WorkerTileParameters = TileParameters & {
	tileID: OverscaledTileID;
	request?: RequestParameters;
	zoom: number;
	maxZoom?: number;
	tileSize: number;
	promoteId: PromoteIdSpecification;
	pixelRatio: number;
	showCollisionBoxes: boolean;
	collectResourceTiming?: boolean;
	returnDependencies?: boolean;
};
/**
 * The paremeters needed in order to load a DEM tile
 */
export type WorkerDEMTileParameters = TileParameters & {
	rawImageData: RGBAImage | ImageBitmap | ImageData;
	encoding: DEMEncoding;
	redFactor: number;
	greenFactor: number;
	blueFactor: number;
	baseShift: number;
};
/**
 * The worker tile's result type
 */
export type WorkerTileResult = ExpiryData & {
	buckets: Array<Bucket>;
	imageAtlas: ImageAtlas;
	glyphAtlasImage: AlphaImage;
	featureIndex: FeatureIndex;
	collisionBoxArray: CollisionBoxArray;
	rawTileData?: ArrayBuffer;
	resourceTiming?: Array<PerformanceResourceTiming>;
	glyphMap?: {
		[_: string]: {
			[_: number]: StyleGlyph;
		};
	} | null;
	iconMap?: {
		[_: string]: StyleImage;
	} | null;
	glyphPositions?: GlyphPositions | null;
};
/**
 * The tile's state, can be:
 *
 * - `loading` Tile data is in the process of loading.
 * - `loaded` Tile data has been loaded. Tile can be rendered.
 * - `reloading` Tile data has been loaded and is being updated. Tile can be rendered.
 * - `unloaded` Tile data has been deleted.
 * - `errored` Tile data was not loaded because of an error.
 * - `expired` Tile data was previously loaded, but has expired per its HTTP headers and is in the process of refreshing.
 */
export type TileState = "loading" | "loaded" | "reloading" | "unloaded" | "errored" | "expired";
declare class Tile {
	tileID: OverscaledTileID;
	uid: number;
	uses: number;
	tileSize: number;
	buckets: {
		[_: string]: Bucket;
	};
	latestFeatureIndex: FeatureIndex;
	latestRawTileData: ArrayBuffer;
	imageAtlas: ImageAtlas;
	imageAtlasTexture: Texture;
	glyphAtlasImage: AlphaImage;
	glyphAtlasTexture: Texture;
	expirationTime: any;
	expiredRequestCount: number;
	state: TileState;
	timeAdded: number;
	fadeEndTime: number;
	collisionBoxArray: CollisionBoxArray;
	redoWhenDone: boolean;
	showCollisionBoxes: boolean;
	placementSource: any;
	actor: Actor;
	vtLayers: {
		[_: string]: VectorTileLayer;
	};
	neighboringTiles: any;
	dem: DEMData;
	demMatrix: mat4;
	aborted: boolean;
	needsHillshadePrepare: boolean;
	needsTerrainPrepare: boolean;
	abortController: AbortController;
	texture: any;
	fbo: Framebuffer;
	demTexture: Texture;
	refreshedUponExpiration: boolean;
	reloadPromise: {
		resolve: () => void;
		reject: () => void;
	};
	resourceTiming: Array<PerformanceResourceTiming>;
	queryPadding: number;
	symbolFadeHoldUntil: number;
	hasSymbolBuckets: boolean;
	hasRTLText: boolean;
	dependencies: any;
	rtt: Array<{
		id: number;
		stamp: number;
	}>;
	rttCoords: {
		[_: string]: string;
	};
	/**
	 * @param tileID - the tile ID
	 * @param size - The tile size
	 */
	constructor(tileID: OverscaledTileID, size: number);
	registerFadeDuration(duration: number): void;
	wasRequested(): boolean;
	clearTextures(painter: any): void;
	/**
	 * Given a data object with a 'buffers' property, load it into
	 * this tile's elementGroups and buffers properties and set loaded
	 * to true. If the data is null, like in the case of an empty
	 * GeoJSON tile, no-op but still set loaded to true.
	 * @param data - The data from the worker
	 * @param painter - the painter
	 * @param justReloaded - `true` to just reload
	 */
	loadVectorData(data: WorkerTileResult, painter: any, justReloaded?: boolean | null): void;
	/**
	 * Release any data or WebGL resources referenced by this tile.
	 */
	unloadVectorData(): void;
	getBucket(layer: StyleLayer): Bucket;
	upload(context: Context): void;
	prepare(imageManager: ImageManager): void;
	queryRenderedFeatures(layers: {
		[_: string]: StyleLayer;
	}, serializedLayers: {
		[_: string]: any;
	}, sourceFeatureState: SourceFeatureState, queryGeometry: Array<Point>, cameraQueryGeometry: Array<Point>, scale: number, params: {
		filter: FilterSpecification;
		layers: Array<string>;
		availableImages: Array<string>;
	}, transform: Transform, maxPitchScaleFactor: number, pixelPosMatrix: mat4): {
		[_: string]: Array<{
			featureIndex: number;
			feature: GeoJSONFeature;
		}>;
	};
	querySourceFeatures(result: Array<GeoJSONFeature>, params?: {
		sourceLayer?: string;
		filter?: FilterSpecification;
		validate?: boolean;
	}): void;
	hasData(): boolean;
	patternsLoaded(): boolean;
	setExpiryData(data: ExpiryData): void;
	getExpiryTimeout(): number;
	setFeatureState(states: LayerFeatureStates, painter: any): void;
	holdingForFade(): boolean;
	symbolFadeFinished(): boolean;
	clearFadeHold(): void;
	setHoldDuration(duration: number): void;
	setDependencies(namespace: string, dependencies: Array<string>): void;
	hasDependency(namespaces: Array<string>, keys: Array<string>): boolean;
}
export type FeatureStates = {
	[featureId: string]: FeatureState;
};
export type LayerFeatureStates = {
	[layer: string]: FeatureStates;
};
declare class SourceFeatureState {
	state: LayerFeatureStates;
	stateChanges: LayerFeatureStates;
	deletedStates: {};
	constructor();
	updateState(sourceLayer: string, featureId: number | string, newState: any): void;
	removeFeatureState(sourceLayer: string, featureId?: number | string, key?: string): void;
	getState(sourceLayer: string, featureId: number | string): FeatureState;
	initializeTileState(tile: Tile, painter: any): void;
	coalesceChanges(tiles: {
		[_ in any]: Tile;
	}, painter: any): void;
}
declare class CircleBucket<Layer extends CircleStyleLayer | HeatmapStyleLayer> implements Bucket {
	index: number;
	zoom: number;
	overscaling: number;
	layerIds: Array<string>;
	layers: Array<Layer>;
	stateDependentLayers: Array<Layer>;
	stateDependentLayerIds: Array<string>;
	layoutVertexArray: CircleLayoutArray;
	layoutVertexBuffer: VertexBuffer;
	indexArray: TriangleIndexArray;
	indexBuffer: IndexBuffer;
	hasPattern: boolean;
	programConfigurations: ProgramConfigurationSet<Layer>;
	segments: SegmentVector;
	uploaded: boolean;
	constructor(options: BucketParameters<Layer>);
	populate(features: Array<IndexedFeature>, options: PopulateParameters, canonical: CanonicalTileID): void;
	update(states: FeatureStates, vtLayer: VectorTileLayer, imagePositions: {
		[_: string]: ImagePosition;
	}): void;
	isEmpty(): boolean;
	uploadPending(): boolean;
	upload(context: Context): void;
	destroy(): void;
	addFeature(feature: BucketFeature, geometry: Array<Array<Point>>, index: number, canonical: CanonicalTileID): void;
}
export type CircleLayoutProps = {
	"circle-sort-key": DataDrivenProperty<number>;
};
export type CircleLayoutPropsPossiblyEvaluated = {
	"circle-sort-key": PossiblyEvaluatedPropertyValue<number>;
};
export type CirclePaintProps = {
	"circle-radius": DataDrivenProperty<number>;
	"circle-color": DataDrivenProperty<Color>;
	"circle-blur": DataDrivenProperty<number>;
	"circle-opacity": DataDrivenProperty<number>;
	"circle-translate": DataConstantProperty<[
		number,
		number
	]>;
	"circle-translate-anchor": DataConstantProperty<"map" | "viewport">;
	"circle-pitch-scale": DataConstantProperty<"map" | "viewport">;
	"circle-pitch-alignment": DataConstantProperty<"map" | "viewport">;
	"circle-stroke-width": DataDrivenProperty<number>;
	"circle-stroke-color": DataDrivenProperty<Color>;
	"circle-stroke-opacity": DataDrivenProperty<number>;
};
export type CirclePaintPropsPossiblyEvaluated = {
	"circle-radius": PossiblyEvaluatedPropertyValue<number>;
	"circle-color": PossiblyEvaluatedPropertyValue<Color>;
	"circle-blur": PossiblyEvaluatedPropertyValue<number>;
	"circle-opacity": PossiblyEvaluatedPropertyValue<number>;
	"circle-translate": [
		number,
		number
	];
	"circle-translate-anchor": "map" | "viewport";
	"circle-pitch-scale": "map" | "viewport";
	"circle-pitch-alignment": "map" | "viewport";
	"circle-stroke-width": PossiblyEvaluatedPropertyValue<number>;
	"circle-stroke-color": PossiblyEvaluatedPropertyValue<Color>;
	"circle-stroke-opacity": PossiblyEvaluatedPropertyValue<number>;
};
declare class CircleStyleLayer extends StyleLayer {
	_unevaluatedLayout: Layout<CircleLayoutProps>;
	layout: PossiblyEvaluated<CircleLayoutProps, CircleLayoutPropsPossiblyEvaluated>;
	_transitionablePaint: Transitionable<CirclePaintProps>;
	_transitioningPaint: Transitioning<CirclePaintProps>;
	paint: PossiblyEvaluated<CirclePaintProps, CirclePaintPropsPossiblyEvaluated>;
	constructor(layer: LayerSpecification);
	createBucket(parameters: BucketParameters<any>): CircleBucket<any>;
	queryRadius(bucket: Bucket): number;
	queryIntersectsFeature(queryGeometry: Array<Point>, feature: VectorTileFeature, featureState: FeatureState, geometry: Array<Array<Point>>, zoom: number, transform: Transform, pixelsToTileUnits: number, pixelPosMatrix: mat4): boolean;
}
declare class FillBucket implements Bucket {
	index: number;
	zoom: number;
	overscaling: number;
	layers: Array<FillStyleLayer>;
	layerIds: Array<string>;
	stateDependentLayers: Array<FillStyleLayer>;
	stateDependentLayerIds: Array<string>;
	patternFeatures: Array<BucketFeature>;
	layoutVertexArray: FillLayoutArray;
	layoutVertexBuffer: VertexBuffer;
	indexArray: TriangleIndexArray;
	indexBuffer: IndexBuffer;
	indexArray2: LineIndexArray;
	indexBuffer2: IndexBuffer;
	hasPattern: boolean;
	programConfigurations: ProgramConfigurationSet<FillStyleLayer>;
	segments: SegmentVector;
	segments2: SegmentVector;
	uploaded: boolean;
	constructor(options: BucketParameters<FillStyleLayer>);
	populate(features: Array<IndexedFeature>, options: PopulateParameters, canonical: CanonicalTileID): void;
	update(states: FeatureStates, vtLayer: VectorTileLayer, imagePositions: {
		[_: string]: ImagePosition;
	}): void;
	addFeatures(options: PopulateParameters, canonical: CanonicalTileID, imagePositions: {
		[_: string]: ImagePosition;
	}): void;
	isEmpty(): boolean;
	uploadPending(): boolean;
	upload(context: Context): void;
	destroy(): void;
	addFeature(feature: BucketFeature, geometry: Array<Array<Point>>, index: number, canonical: CanonicalTileID, imagePositions: {
		[_: string]: ImagePosition;
	}): void;
}
export type FillLayoutProps = {
	"fill-sort-key": DataDrivenProperty<number>;
};
export type FillLayoutPropsPossiblyEvaluated = {
	"fill-sort-key": PossiblyEvaluatedPropertyValue<number>;
};
export type FillPaintProps = {
	"fill-antialias": DataConstantProperty<boolean>;
	"fill-opacity": DataDrivenProperty<number>;
	"fill-color": DataDrivenProperty<Color>;
	"fill-outline-color": DataDrivenProperty<Color>;
	"fill-translate": DataConstantProperty<[
		number,
		number
	]>;
	"fill-translate-anchor": DataConstantProperty<"map" | "viewport">;
	"fill-pattern": CrossFadedDataDrivenProperty<ResolvedImage>;
};
export type FillPaintPropsPossiblyEvaluated = {
	"fill-antialias": boolean;
	"fill-opacity": PossiblyEvaluatedPropertyValue<number>;
	"fill-color": PossiblyEvaluatedPropertyValue<Color>;
	"fill-outline-color": PossiblyEvaluatedPropertyValue<Color>;
	"fill-translate": [
		number,
		number
	];
	"fill-translate-anchor": "map" | "viewport";
	"fill-pattern": PossiblyEvaluatedPropertyValue<CrossFaded<ResolvedImage>>;
};
declare class FillStyleLayer extends StyleLayer {
	_unevaluatedLayout: Layout<FillLayoutProps>;
	layout: PossiblyEvaluated<FillLayoutProps, FillLayoutPropsPossiblyEvaluated>;
	_transitionablePaint: Transitionable<FillPaintProps>;
	_transitioningPaint: Transitioning<FillPaintProps>;
	paint: PossiblyEvaluated<FillPaintProps, FillPaintPropsPossiblyEvaluated>;
	constructor(layer: LayerSpecification);
	recalculate(parameters: EvaluationParameters, availableImages: Array<string>): void;
	createBucket(parameters: BucketParameters<any>): FillBucket;
	queryRadius(): number;
	queryIntersectsFeature(queryGeometry: Array<Point>, feature: VectorTileFeature, featureState: FeatureState, geometry: Array<Array<Point>>, zoom: number, transform: Transform, pixelsToTileUnits: number): boolean;
	isTileClipped(): boolean;
}
declare class FillExtrusionBucket implements Bucket {
	index: number;
	zoom: number;
	overscaling: number;
	layers: Array<FillExtrusionStyleLayer>;
	layerIds: Array<string>;
	stateDependentLayers: Array<FillExtrusionStyleLayer>;
	stateDependentLayerIds: Array<string>;
	layoutVertexArray: FillExtrusionLayoutArray;
	layoutVertexBuffer: VertexBuffer;
	centroidVertexArray: PosArray;
	centroidVertexBuffer: VertexBuffer;
	indexArray: TriangleIndexArray;
	indexBuffer: IndexBuffer;
	hasPattern: boolean;
	programConfigurations: ProgramConfigurationSet<FillExtrusionStyleLayer>;
	segments: SegmentVector;
	uploaded: boolean;
	features: Array<BucketFeature>;
	constructor(options: BucketParameters<FillExtrusionStyleLayer>);
	populate(features: Array<IndexedFeature>, options: PopulateParameters, canonical: CanonicalTileID): void;
	addFeatures(options: PopulateParameters, canonical: CanonicalTileID, imagePositions: {
		[_: string]: ImagePosition;
	}): void;
	update(states: FeatureStates, vtLayer: VectorTileLayer, imagePositions: {
		[_: string]: ImagePosition;
	}): void;
	isEmpty(): boolean;
	uploadPending(): boolean;
	upload(context: Context): void;
	destroy(): void;
	addFeature(feature: BucketFeature, geometry: Array<Array<Point>>, index: number, canonical: CanonicalTileID, imagePositions: {
		[_: string]: ImagePosition;
	}): void;
}
export type FillExtrusionPaintProps = {
	"fill-extrusion-opacity": DataConstantProperty<number>;
	"fill-extrusion-color": DataDrivenProperty<Color>;
	"fill-extrusion-translate": DataConstantProperty<[
		number,
		number
	]>;
	"fill-extrusion-translate-anchor": DataConstantProperty<"map" | "viewport">;
	"fill-extrusion-pattern": CrossFadedDataDrivenProperty<ResolvedImage>;
	"fill-extrusion-height": DataDrivenProperty<number>;
	"fill-extrusion-base": DataDrivenProperty<number>;
	"fill-extrusion-vertical-gradient": DataConstantProperty<boolean>;
};
export type FillExtrusionPaintPropsPossiblyEvaluated = {
	"fill-extrusion-opacity": number;
	"fill-extrusion-color": PossiblyEvaluatedPropertyValue<Color>;
	"fill-extrusion-translate": [
		number,
		number
	];
	"fill-extrusion-translate-anchor": "map" | "viewport";
	"fill-extrusion-pattern": PossiblyEvaluatedPropertyValue<CrossFaded<ResolvedImage>>;
	"fill-extrusion-height": PossiblyEvaluatedPropertyValue<number>;
	"fill-extrusion-base": PossiblyEvaluatedPropertyValue<number>;
	"fill-extrusion-vertical-gradient": boolean;
};
declare class FillExtrusionStyleLayer extends StyleLayer {
	_transitionablePaint: Transitionable<FillExtrusionPaintProps>;
	_transitioningPaint: Transitioning<FillExtrusionPaintProps>;
	paint: PossiblyEvaluated<FillExtrusionPaintProps, FillExtrusionPaintPropsPossiblyEvaluated>;
	constructor(layer: LayerSpecification);
	createBucket(parameters: BucketParameters<FillExtrusionStyleLayer>): FillExtrusionBucket;
	queryRadius(): number;
	is3D(): boolean;
	queryIntersectsFeature(queryGeometry: Array<Point>, feature: VectorTileFeature, featureState: FeatureState, geometry: Array<Array<Point>>, zoom: number, transform: Transform, pixelsToTileUnits: number, pixelPosMatrix: mat4): boolean | number;
}
export type HillshadePaintProps = {
	"hillshade-illumination-direction": DataConstantProperty<number>;
	"hillshade-illumination-anchor": DataConstantProperty<"map" | "viewport">;
	"hillshade-exaggeration": DataConstantProperty<number>;
	"hillshade-shadow-color": DataConstantProperty<Color>;
	"hillshade-highlight-color": DataConstantProperty<Color>;
	"hillshade-accent-color": DataConstantProperty<Color>;
};
export type HillshadePaintPropsPossiblyEvaluated = {
	"hillshade-illumination-direction": number;
	"hillshade-illumination-anchor": "map" | "viewport";
	"hillshade-exaggeration": number;
	"hillshade-shadow-color": Color;
	"hillshade-highlight-color": Color;
	"hillshade-accent-color": Color;
};
declare class HillshadeStyleLayer extends StyleLayer {
	_transitionablePaint: Transitionable<HillshadePaintProps>;
	_transitioningPaint: Transitioning<HillshadePaintProps>;
	paint: PossiblyEvaluated<HillshadePaintProps, HillshadePaintPropsPossiblyEvaluated>;
	constructor(layer: LayerSpecification);
	hasOffscreenPass(): boolean;
}
export type LineClips = {
	start: number;
	end: number;
};
export type GradientTexture = {
	texture?: Texture;
	gradient?: RGBAImage;
	version?: number;
};
declare class LineBucket implements Bucket {
	distance: number;
	totalDistance: number;
	maxLineLength: number;
	scaledDistance: number;
	lineClips?: LineClips;
	e1: number;
	e2: number;
	index: number;
	zoom: number;
	overscaling: number;
	layers: Array<LineStyleLayer>;
	layerIds: Array<string>;
	gradients: {
		[x: string]: GradientTexture;
	};
	stateDependentLayers: Array<any>;
	stateDependentLayerIds: Array<string>;
	patternFeatures: Array<BucketFeature>;
	lineClipsArray: Array<LineClips>;
	layoutVertexArray: LineLayoutArray;
	layoutVertexBuffer: VertexBuffer;
	layoutVertexArray2: LineExtLayoutArray;
	layoutVertexBuffer2: VertexBuffer;
	indexArray: TriangleIndexArray;
	indexBuffer: IndexBuffer;
	hasPattern: boolean;
	programConfigurations: ProgramConfigurationSet<LineStyleLayer>;
	segments: SegmentVector;
	uploaded: boolean;
	constructor(options: BucketParameters<LineStyleLayer>);
	populate(features: Array<IndexedFeature>, options: PopulateParameters, canonical: CanonicalTileID): void;
	update(states: FeatureStates, vtLayer: VectorTileLayer, imagePositions: {
		[_: string]: ImagePosition;
	}): void;
	addFeatures(options: PopulateParameters, canonical: CanonicalTileID, imagePositions: {
		[_: string]: ImagePosition;
	}): void;
	isEmpty(): boolean;
	uploadPending(): boolean;
	upload(context: Context): void;
	destroy(): void;
	lineFeatureClips(feature: BucketFeature): LineClips | undefined;
	addFeature(feature: BucketFeature, geometry: Array<Array<Point>>, index: number, canonical: CanonicalTileID, imagePositions: {
		[_: string]: ImagePosition;
	}): void;
	addLine(vertices: Array<Point>, feature: BucketFeature, join: string, cap: string, miterLimit: number, roundLimit: number): void;
	/**
	 * Add two vertices to the buffers.
	 *
	 * @param p - the line vertex to add buffer vertices for
	 * @param normal - vertex normal
	 * @param endLeft - extrude to shift the left vertex along the line
	 * @param endRight - extrude to shift the left vertex along the line
	 * @param segment - the segment object to add the vertex to
	 * @param round - whether this is a round cap
	 */
	addCurrentVertex(p: Point, normal: Point, endLeft: number, endRight: number, segment: Segment, round?: boolean): void;
	addHalfVertex({ x, y }: Point, extrudeX: number, extrudeY: number, round: boolean, up: boolean, dir: number, segment: Segment): void;
	updateScaledDistance(): void;
	updateDistance(prev: Point, next: Point): void;
}
export type LineLayoutProps = {
	"line-cap": DataConstantProperty<"butt" | "round" | "square">;
	"line-join": DataDrivenProperty<"bevel" | "round" | "miter">;
	"line-miter-limit": DataConstantProperty<number>;
	"line-round-limit": DataConstantProperty<number>;
	"line-sort-key": DataDrivenProperty<number>;
};
export type LineLayoutPropsPossiblyEvaluated = {
	"line-cap": "butt" | "round" | "square";
	"line-join": PossiblyEvaluatedPropertyValue<"bevel" | "round" | "miter">;
	"line-miter-limit": number;
	"line-round-limit": number;
	"line-sort-key": PossiblyEvaluatedPropertyValue<number>;
};
export type LinePaintProps = {
	"line-opacity": DataDrivenProperty<number>;
	"line-color": DataDrivenProperty<Color>;
	"line-translate": DataConstantProperty<[
		number,
		number
	]>;
	"line-translate-anchor": DataConstantProperty<"map" | "viewport">;
	"line-width": DataDrivenProperty<number>;
	"line-gap-width": DataDrivenProperty<number>;
	"line-offset": DataDrivenProperty<number>;
	"line-blur": DataDrivenProperty<number>;
	"line-dasharray": CrossFadedProperty<Array<number>>;
	"line-pattern": CrossFadedDataDrivenProperty<ResolvedImage>;
	"line-gradient": ColorRampProperty;
};
export type LinePaintPropsPossiblyEvaluated = {
	"line-opacity": PossiblyEvaluatedPropertyValue<number>;
	"line-color": PossiblyEvaluatedPropertyValue<Color>;
	"line-translate": [
		number,
		number
	];
	"line-translate-anchor": "map" | "viewport";
	"line-width": PossiblyEvaluatedPropertyValue<number>;
	"line-gap-width": PossiblyEvaluatedPropertyValue<number>;
	"line-offset": PossiblyEvaluatedPropertyValue<number>;
	"line-blur": PossiblyEvaluatedPropertyValue<number>;
	"line-dasharray": CrossFaded<Array<number>>;
	"line-pattern": PossiblyEvaluatedPropertyValue<CrossFaded<ResolvedImage>>;
	"line-gradient": ColorRampProperty;
};
declare class LineStyleLayer extends StyleLayer {
	_unevaluatedLayout: Layout<LineLayoutProps>;
	layout: PossiblyEvaluated<LineLayoutProps, LineLayoutPropsPossiblyEvaluated>;
	gradientVersion: number;
	stepInterpolant: boolean;
	_transitionablePaint: Transitionable<LinePaintProps>;
	_transitioningPaint: Transitioning<LinePaintProps>;
	paint: PossiblyEvaluated<LinePaintProps, LinePaintPropsPossiblyEvaluated>;
	constructor(layer: LayerSpecification);
	_handleSpecialPaintPropertyUpdate(name: string): void;
	gradientExpression(): import("@maplibre/maplibre-gl-style-spec").StylePropertyExpression;
	recalculate(parameters: EvaluationParameters, availableImages: Array<string>): void;
	createBucket(parameters: BucketParameters<any>): LineBucket;
	queryRadius(bucket: Bucket): number;
	queryIntersectsFeature(queryGeometry: Array<Point>, feature: VectorTileFeature, featureState: FeatureState, geometry: Array<Array<Point>>, zoom: number, transform: Transform, pixelsToTileUnits: number): boolean;
	isTileClipped(): boolean;
}
export type TypedStyleLayer = CircleStyleLayer | FillStyleLayer | FillExtrusionStyleLayer | HeatmapStyleLayer | HillshadeStyleLayer | LineStyleLayer | SymbolStyleLayer;
export type BinderUniform = {
	name: string;
	property: string;
	binding: Uniform<any>;
};
/**
 *  `Binder` is the interface definition for the strategies for constructing,
 *  uploading, and binding paint property data as GLSL attributes. Most style-
 *  spec properties have a 1:1 relationship to shader attribute/uniforms, but
 *  some require multiple values per feature to be passed to the GPU, and in
 *  those cases we bind multiple attributes/uniforms.
 *
 *  It has three implementations, one for each of the three strategies we use:
 *
 *  * For _constant_ properties -- those whose value is a constant, or the constant
 *    result of evaluating a camera expression at a particular camera position -- we
 *    don't need a vertex attribute buffer, and instead use a uniform.
 *  * For data expressions, we use a vertex buffer with a single attribute value,
 *    the evaluated result of the source function for the given feature.
 *  * For composite expressions, we use a vertex buffer with two attributes: min and
 *    max values covering the range of zooms at which we expect the tile to be
 *    displayed. These values are calculated by evaluating the composite expression for
 *    the given feature at strategically chosen zoom levels. In addition to this
 *    attribute data, we also use a uniform value which the shader uses to interpolate
 *    between the min and max value at the final displayed zoom level. The use of a
 *    uniform allows us to cheaply update the value on every frame.
 *
 *  Note that the shader source varies depending on whether we're using a uniform or
 *  attribute. We dynamically compile shaders at runtime to accommodate this.
 */
export interface AttributeBinder {
	populatePaintArray(length: number, feature: Feature, imagePositions: {
		[_: string]: ImagePosition;
	}, canonical?: CanonicalTileID, formattedSection?: FormattedSection): void;
	updatePaintArray(start: number, length: number, feature: Feature, featureState: FeatureState, imagePositions: {
		[_: string]: ImagePosition;
	}): void;
	upload(a: Context): void;
	destroy(): void;
}
export interface UniformBinder {
	uniformNames: Array<string>;
	setUniform(uniform: Uniform<any>, globals: GlobalProperties, currentValue: PossiblyEvaluatedPropertyValue<any>, uniformName: string): void;
	getBinding(context: Context, location: WebGLUniformLocation, name: string): Partial<Uniform<any>>;
}
declare class ProgramConfiguration {
	binders: {
		[_: string]: AttributeBinder | UniformBinder;
	};
	cacheKey: string;
	_buffers: Array<VertexBuffer>;
	constructor(layer: TypedStyleLayer, zoom: number, filterProperties: (_: string) => boolean);
	getMaxValue(property: string): number;
	populatePaintArrays(newLength: number, feature: Feature, imagePositions: {
		[_: string]: ImagePosition;
	}, canonical?: CanonicalTileID, formattedSection?: FormattedSection): void;
	setConstantPatternPositions(posTo: ImagePosition, posFrom: ImagePosition): void;
	updatePaintArrays(featureStates: FeatureStates, featureMap: FeaturePositionMap, vtLayer: VectorTileLayer, layer: TypedStyleLayer, imagePositions: {
		[_: string]: ImagePosition;
	}): boolean;
	defines(): Array<string>;
	getBinderAttributes(): Array<string>;
	getBinderUniforms(): Array<string>;
	getPaintVertexBuffers(): Array<VertexBuffer>;
	getUniforms(context: Context, locations: UniformLocations): Array<BinderUniform>;
	setUniforms(context: Context, binderUniforms: Array<BinderUniform>, properties: any, globals: GlobalProperties): void;
	updatePaintBuffers(crossfade?: CrossfadeParameters): void;
	upload(context: Context): void;
	destroy(): void;
}
declare class ProgramConfigurationSet<Layer extends TypedStyleLayer> {
	programConfigurations: {
		[_: string]: ProgramConfiguration;
	};
	needsUpload: boolean;
	_featureMap: FeaturePositionMap;
	_bufferOffset: number;
	constructor(layers: ReadonlyArray<Layer>, zoom: number, filterProperties?: (_: string) => boolean);
	populatePaintArrays(length: number, feature: Feature, index: number, imagePositions: {
		[_: string]: ImagePosition;
	}, canonical: CanonicalTileID, formattedSection?: FormattedSection): void;
	updatePaintArrays(featureStates: FeatureStates, vtLayer: VectorTileLayer, layers: ReadonlyArray<TypedStyleLayer>, imagePositions: {
		[_: string]: ImagePosition;
	}): void;
	get(layerId: string): ProgramConfiguration;
	upload(context: Context): void;
	destroy(): void;
}
declare class CullFaceMode {
	enable: boolean;
	mode: CullFaceModeType;
	frontFace: FrontFaceType;
	constructor(enable: boolean, mode: CullFaceModeType, frontFace: FrontFaceType);
	static disabled: Readonly<CullFaceMode>;
	static backCCW: Readonly<CullFaceMode>;
}
export type SkyProps = {
	"sky-color": DataConstantProperty<Color>;
	"horizon-color": DataConstantProperty<Color>;
	"fog-color": DataConstantProperty<Color>;
	"fog-ground-blend": DataConstantProperty<number>;
	"horizon-fog-blend": DataConstantProperty<number>;
	"sky-horizon-blend": DataConstantProperty<number>;
	"atmosphere-blend": DataConstantProperty<number>;
};
export type SkyPropsPossiblyEvaluated = {
	"sky-color": Color;
	"horizon-color": Color;
	"fog-color": Color;
	"fog-ground-blend": number;
	"horizon-fog-blend": number;
	"sky-horizon-blend": number;
	"atmosphere-blend": number;
};
declare class Sky extends Evented {
	properties: PossiblyEvaluated<SkyProps, SkyPropsPossiblyEvaluated>;
	/**
	 * This is used to cache the gl mesh for the sky, it should be initialized only once.
	 */
	mesh: Mesh | undefined;
	_transitionable: Transitionable<SkyProps>;
	_transitioning: Transitioning<SkyProps>;
	constructor(sky?: SkySpecification);
	setSky(sky?: SkySpecification, options?: StyleSetterOptions): void;
	getSky(): SkySpecification;
	updateTransitions(parameters: TransitionParameters): void;
	hasTransition(): boolean;
	recalculate(parameters: EvaluationParameters): void;
	_validate(validate: Function, value: unknown, options?: StyleSetterOptions): boolean;
	/**
	 * Currently fog is a very simple implementation, and should only used
	 * to create an atmosphere near the horizon.
	 * But because the fog is drawn from the far-clipping-plane to
	 * map-center, and because the fog does nothing know about the horizon,
	 * this method does a fadeout in respect of pitch. So, when the horizon
	 * gets out of view, which is at about pitch 70, this methods calculates
	 * the corresponding opacity values. Below pitch 60 the fog is completely
	 * invisible.
	 */
	calculateFogBlendOpacity(pitch: number): number;
}
export type TerrainPreludeUniformsType = {
	"u_depth": Uniform1i;
	"u_terrain": Uniform1i;
	"u_terrain_dim": Uniform1f;
	"u_terrain_matrix": UniformMatrix4f;
	"u_terrain_unpack": Uniform4f;
	"u_terrain_exaggeration": Uniform1f;
};
export type DrawMode = WebGLRenderingContextBase["LINES"] | WebGLRenderingContextBase["TRIANGLES"] | WebGL2RenderingContext["LINE_STRIP"];
declare class Program<Us extends UniformBindings> {
	program: WebGLProgram;
	attributes: {
		[_: string]: number;
	};
	numAttributes: number;
	fixedUniforms: Us;
	terrainUniforms: TerrainPreludeUniformsType;
	binderUniforms: Array<BinderUniform>;
	failedToCreate: boolean;
	constructor(context: Context, source: {
		fragmentSource: string;
		vertexSource: string;
		staticAttributes: Array<string>;
		staticUniforms: Array<string>;
	}, configuration: ProgramConfiguration, fixedUniforms: (b: Context, a: UniformLocations) => Us, showOverdrawInspector: boolean, terrain: Terrain);
	draw(context: Context, drawMode: DrawMode, depthMode: Readonly<DepthMode>, stencilMode: Readonly<StencilMode>, colorMode: Readonly<ColorMode>, cullFaceMode: Readonly<CullFaceMode>, uniformValues: UniformValues<Us>, terrain: TerrainData, layerID: string, layoutVertexBuffer: VertexBuffer, indexBuffer: IndexBuffer, segments: SegmentVector, currentProperties?: any, zoom?: number | null, configuration?: ProgramConfiguration | null, dynamicLayoutBuffer?: VertexBuffer | null, dynamicLayoutBuffer2?: VertexBuffer | null, dynamicLayoutBuffer3?: VertexBuffer | null): void;
}
declare class VertexBuffer {
	length: number;
	attributes: ReadonlyArray<StructArrayMember>;
	itemSize: number;
	dynamicDraw: boolean;
	context: Context;
	buffer: WebGLBuffer;
	/**
	 * @param dynamicDraw - Whether this buffer will be repeatedly updated.
	 */
	constructor(context: Context, array: StructArray, attributes: ReadonlyArray<StructArrayMember>, dynamicDraw?: boolean);
	bind(): void;
	updateData(array: StructArray): void;
	enableAttributes(gl: WebGLRenderingContext | WebGL2RenderingContext, program: Program<any>): void;
	/**
	 * Set the attribute pointers in a WebGL context
	 * @param gl - The WebGL context
	 * @param program - The active WebGL program
	 * @param vertexOffset - Index of the starting vertex of the segment
	 */
	setVertexAttribPointers(gl: WebGLRenderingContext | WebGL2RenderingContext, program: Program<any>, vertexOffset?: number | null): void;
	/**
	 * Destroy the GL buffer bound to the given WebGL context
	 */
	destroy(): void;
}
export type ClearArgs = {
	color?: Color;
	depth?: number;
	stencil?: number;
};
declare class Context {
	gl: WebGLRenderingContext | WebGL2RenderingContext;
	currentNumAttributes: number;
	maxTextureSize: number;
	clearColor: ClearColor;
	clearDepth: ClearDepth;
	clearStencil: ClearStencil;
	colorMask: ColorMask;
	depthMask: DepthMask;
	stencilMask: StencilMask;
	stencilFunc: StencilFunc;
	stencilOp: StencilOp;
	stencilTest: StencilTest;
	depthRange: DepthRange;
	depthTest: DepthTest;
	depthFunc: DepthFunc;
	blend: Blend;
	blendFunc: BlendFunc;
	blendColor: BlendColor;
	blendEquation: BlendEquation;
	cullFace: CullFace;
	cullFaceSide: CullFaceSide;
	frontFace: FrontFace;
	program: ProgramValue;
	activeTexture: ActiveTextureUnit;
	viewport: Viewport;
	bindFramebuffer: BindFramebuffer;
	bindRenderbuffer: BindRenderbuffer;
	bindTexture: BindTexture;
	bindVertexBuffer: BindVertexBuffer;
	bindElementBuffer: BindElementBuffer;
	bindVertexArray: BindVertexArray;
	pixelStoreUnpack: PixelStoreUnpack;
	pixelStoreUnpackPremultiplyAlpha: PixelStoreUnpackPremultiplyAlpha;
	pixelStoreUnpackFlipY: PixelStoreUnpackFlipY;
	extTextureFilterAnisotropic: EXT_texture_filter_anisotropic | null;
	extTextureFilterAnisotropicMax?: GLfloat;
	HALF_FLOAT?: GLenum;
	RGBA16F?: GLenum;
	RGB16F?: GLenum;
	constructor(gl: WebGLRenderingContext | WebGL2RenderingContext);
	setDefault(): void;
	setDirty(): void;
	createIndexBuffer(array: TriangleIndexArray | LineIndexArray | LineStripIndexArray, dynamicDraw?: boolean): IndexBuffer;
	createVertexBuffer(array: StructArray, attributes: ReadonlyArray<StructArrayMember>, dynamicDraw?: boolean): VertexBuffer;
	createRenderbuffer(storageFormat: number, width: number, height: number): WebGLRenderbuffer;
	createFramebuffer(width: number, height: number, hasDepth: boolean, hasStencil: boolean): Framebuffer;
	clear({ color, depth, stencil }: ClearArgs): void;
	setCullFace(cullFaceMode: Readonly<CullFaceMode>): void;
	setDepthMode(depthMode: Readonly<DepthMode>): void;
	setStencilMode(stencilMode: Readonly<StencilMode>): void;
	setColorMode(colorMode: Readonly<ColorMode>): void;
	createVertexArray(): WebGLVertexArrayObject | undefined;
	deleteVertexArray(x: WebGLVertexArrayObject | undefined): void;
	unbindVAO(): void;
}
export type TextureFormat = WebGLRenderingContextBase["RGBA"] | WebGLRenderingContextBase["ALPHA"];
export type TextureFilter = WebGLRenderingContextBase["LINEAR"] | WebGLRenderingContextBase["LINEAR_MIPMAP_NEAREST"] | WebGLRenderingContextBase["NEAREST"];
export type TextureWrap = WebGLRenderingContextBase["REPEAT"] | WebGLRenderingContextBase["CLAMP_TO_EDGE"] | WebGLRenderingContextBase["MIRRORED_REPEAT"];
export type EmptyImage = {
	width: number;
	height: number;
	data: null;
};
export type DataTextureImage = RGBAImage | AlphaImage | EmptyImage;
export type TextureImage = TexImageSource | DataTextureImage;
declare class Texture {
	context: Context;
	size: [
		number,
		number
	];
	texture: WebGLTexture;
	format: TextureFormat;
	filter: TextureFilter;
	wrap: TextureWrap;
	useMipmap: boolean;
	constructor(context: Context, image: TextureImage, format: TextureFormat, options?: {
		premultiply?: boolean;
		useMipmap?: boolean;
	} | null);
	update(image: TextureImage, options?: {
		premultiply?: boolean;
		useMipmap?: boolean;
	} | null, position?: {
		x: number;
		y: number;
	}): void;
	bind(filter: TextureFilter, wrap: TextureWrap, minFilter?: TextureFilter | null): void;
	isSizePowerOfTwo(): boolean;
	destroy(): void;
}
declare class ImagePosition {
	paddedRect: Rect;
	pixelRatio: number;
	version: number;
	stretchY: Array<[
		number,
		number
	]>;
	stretchX: Array<[
		number,
		number
	]>;
	content: [
		number,
		number,
		number,
		number
	];
	textFitWidth: TextFit;
	textFitHeight: TextFit;
	constructor(paddedRect: Rect, { pixelRatio, version, stretchX, stretchY, content, textFitWidth, textFitHeight }: StyleImage);
	get tl(): [
		number,
		number
	];
	get br(): [
		number,
		number
	];
	get tlbr(): Array<number>;
	get displaySize(): [
		number,
		number
	];
}
declare class ImageAtlas {
	image: RGBAImage;
	iconPositions: {
		[_: string]: ImagePosition;
	};
	patternPositions: {
		[_: string]: ImagePosition;
	};
	haveRenderCallbacks: Array<string>;
	uploaded: boolean;
	constructor(icons: GetImagesResponse, patterns: GetImagesResponse);
	addImages(images: {
		[_: string]: StyleImage;
	}, positions: {
		[_: string]: ImagePosition;
	}, bins: Array<Rect>): void;
	patchUpdatedImages(imageManager: ImageManager, texture: Texture): void;
	patchUpdatedImage(position: ImagePosition, image: StyleImage, texture: Texture): void;
}
export type Pattern = {
	bin: PotpackBox;
	position: ImagePosition;
};
declare class ImageManager extends Evented {
	images: {
		[_: string]: StyleImage;
	};
	updatedImages: {
		[_: string]: boolean;
	};
	callbackDispatchedThisFrame: {
		[_: string]: boolean;
	};
	loaded: boolean;
	/**
	 * This is used to track requests for images that are not yet available. When the image is loaded,
	 * the requestors will be notified.
	 */
	requestors: Array<{
		ids: Array<string>;
		promiseResolve: (value: GetImagesResponse) => void;
	}>;
	patterns: {
		[_: string]: Pattern;
	};
	atlasImage: RGBAImage;
	atlasTexture: Texture;
	dirty: boolean;
	constructor();
	isLoaded(): boolean;
	setLoaded(loaded: boolean): void;
	getImage(id: string): StyleImage;
	addImage(id: string, image: StyleImage): void;
	_validate(id: string, image: StyleImage): boolean;
	_validateStretch(stretch: Array<[
		number,
		number
	]>, size: number): boolean;
	_validateContent(content: [
		number,
		number,
		number,
		number
	], image: StyleImage): boolean;
	updateImage(id: string, image: StyleImage, validate?: boolean): void;
	removeImage(id: string): void;
	listImages(): Array<string>;
	getImages(ids: Array<string>): Promise<GetImagesResponse>;
	_getImagesForIds(ids: Array<string>): GetImagesResponse;
	getPixelSize(): {
		width: number;
		height: number;
	};
	getPattern(id: string): ImagePosition;
	bind(context: Context): void;
	_updatePatternAtlas(): void;
	beginFrame(): void;
	dispatchRenderCallbacks(ids: Array<string>): void;
}
export type LightPosition = {
	x: number;
	y: number;
	z: number;
};
declare class LightPositionProperty implements Property<[
	number,
	number,
	number
], LightPosition> {
	specification: StylePropertySpecification;
	constructor();
	possiblyEvaluate(value: PropertyValue<[
		number,
		number,
		number
	], LightPosition>, parameters: EvaluationParameters): LightPosition;
	interpolate(a: LightPosition, b: LightPosition, t: number): LightPosition;
}
export type LightProps = {
	"anchor": DataConstantProperty<"map" | "viewport">;
	"position": LightPositionProperty;
	"color": DataConstantProperty<Color>;
	"intensity": DataConstantProperty<number>;
};
export type LightPropsPossiblyEvaluated = {
	"anchor": "map" | "viewport";
	"position": LightPosition;
	"color": Color;
	"intensity": number;
};
declare class Light extends Evented {
	_transitionable: Transitionable<LightProps>;
	_transitioning: Transitioning<LightProps>;
	properties: PossiblyEvaluated<LightProps, LightPropsPossiblyEvaluated>;
	constructor(lightOptions?: LightSpecification);
	getLight(): LightSpecification;
	setLight(light?: LightSpecification, options?: StyleSetterOptions): void;
	updateTransitions(parameters: TransitionParameters): void;
	hasTransition(): boolean;
	recalculate(parameters: EvaluationParameters): void;
	_validate(validate: Function, value: unknown, options?: {
		validate?: boolean;
	}): boolean;
}
/**
 * The overlap mode for properties like `icon-overlap`and `text-overlap`
 */
export type OverlapMode = "never" | "always" | "cooperative";
export type QueryResult<T> = {
	key: T;
	x1: number;
	y1: number;
	x2: number;
	y2: number;
};
/**
 * A key for the grid
 */
export type GridKey = {
	overlapMode?: OverlapMode;
};
declare class GridIndex<T extends GridKey> {
	circleKeys: Array<T>;
	boxKeys: Array<T>;
	boxCells: Array<Array<number>>;
	circleCells: Array<Array<number>>;
	bboxes: Array<number>;
	circles: Array<number>;
	xCellCount: number;
	yCellCount: number;
	width: number;
	height: number;
	xScale: number;
	yScale: number;
	boxUid: number;
	circleUid: number;
	constructor(width: number, height: number, cellSize: number);
	keysLength(): number;
	insert(key: T, x1: number, y1: number, x2: number, y2: number): void;
	insertCircle(key: T, x: number, y: number, radius: number): void;
	private _insertBoxCell;
	private _insertCircleCell;
	private _query;
	query(x1: number, y1: number, x2: number, y2: number): Array<QueryResult<T>>;
	hitTest(x1: number, y1: number, x2: number, y2: number, overlapMode: OverlapMode, predicate?: (key: T) => boolean): boolean;
	hitTestCircle(x: number, y: number, radius: number, overlapMode: OverlapMode, predicate?: (key: T) => boolean): boolean;
	private _queryCell;
	private _queryCellCircle;
	private _forEachCell;
	private _convertToXCellCoord;
	private _convertToYCellCoord;
	private _circlesCollide;
	private _circleAndRectCollide;
}
/**
 * A greatly reduced version of the `Projection` interface from the globe branch,
 * used to port symbol bugfixes over to the main branch. Will be replaced with
 * the proper interface once globe is merged.
 */
export type Projection = {
	useSpecialProjectionForSymbols: boolean;
	isOccluded(_x: any, _y: any, _t: any): boolean;
	projectTileCoordinates(_x: any, _y: any, _t: any, _ele: any): PointProjection;
	getPitchedTextCorrection(_transform: any, _anchor: any, _tile: any): number;
	translatePosition(transform: {
		angle: number;
		zoom: number;
	}, tile: Tile, translate: [
		number,
		number
	], translateAnchor: "map" | "viewport"): [
		number,
		number
	];
	getCircleRadiusCorrection(tr: any): number;
};
/**
 * The result of projecting a point to the screen, with some additional information about the projection.
 */
export type PointProjection = {
	/**
	 * The projected point.
	 */
	point: Point;
	/**
	 * The original W component of the projection.
	 */
	signedDistanceFromCamera: number;
	/**
	 * For complex projections (such as globe), true if the point is occluded by the projection, such as by being on the backfacing side of the globe.
	 */
	isOccluded: boolean;
};
export type IndexToPointCache = {
	[lineIndex: number]: Point;
};
/**
 * @internal
 * We calculate label-plane projected points for line vertices as we place glyphs along the line
 * Since we will use the same vertices for potentially many glyphs, cache the results for this bucket
 * over the course of the render. Each vertex location also potentially has one offset equivalent
 * for us to hold onto. The vertex indices are per-symbol-bucket.
 */
export type ProjectionCache = {
	/**
	 * tile-unit vertices projected into label-plane units
	 */
	projections: IndexToPointCache;
	/**
	 * label-plane vertices which have been shifted to follow an offset line
	 */
	offsets: IndexToPointCache;
	/**
	 * Cached projected anchor point.
	 */
	cachedAnchorPoint: Point | undefined;
	/**
	 * Was any projected point occluded by the map itself (eg. occluded by the planet when using globe projection).
	 *
	 * Viewport-pitched line-following texts where *any* of the line points is hidden behind the planet curve becomes entirely hidden.
	 * This is perhaps not the most ideal behavior, but it works, it is simple and planetary-scale texts such as this seem to be a rare edge case.
	 */
	anyProjectionOccluded: boolean;
};
/**
 * @internal
 * Arguments necessary to project a vertex to the label plane
 */
export type SymbolProjectionContext = {
	/**
	 * Used to cache results, save cost if projecting the same vertex multiple times
	 */
	projectionCache: ProjectionCache;
	/**
	 * The array of tile-unit vertices transferred from worker
	 */
	lineVertexArray: SymbolLineVertexArray;
	/**
	 * Label plane projection matrix
	 */
	labelPlaneMatrix: mat4;
	/**
	 * Function to get elevation at a point
	 * @param x - the x coordinate
	 * @param y - the y coordinate
	*/
	getElevation: (x: number, y: number) => number;
	/**
	 * Only for creating synthetic vertices if vertex would otherwise project behind plane of camera,
	 * but still convenient to pass it inside this type.
	 */
	tileAnchorPoint: Point;
	/**
	 * True when line glyphs are projected onto the map, instead of onto the viewport.
	 */
	pitchWithMap: boolean;
	projection: Projection;
	unwrappedTileID: UnwrappedTileID;
	/**
	 * Viewport width.
	 */
	width: number;
	/**
	 * Viewport height.
	 */
	height: number;
	/**
	 * Translation in tile units, computed using text-translate and text-translate-anchor paint style properties.
	 */
	translation: [
		number,
		number
	];
};
export type PlacedCircles = {
	circles: Array<number>;
	offscreen: boolean;
	collisionDetected: boolean;
};
export type PlacedBox = {
	box: Array<number>;
	placeable: boolean;
	offscreen: boolean;
};
export type FeatureKey = {
	bucketInstanceId: number;
	featureIndex: number;
	collisionGroupID: number;
	overlapMode: OverlapMode;
};
declare class CollisionIndex {
	grid: GridIndex<FeatureKey>;
	ignoredGrid: GridIndex<FeatureKey>;
	transform: Transform;
	pitchFactor: number;
	screenRightBoundary: number;
	screenBottomBoundary: number;
	gridRightBoundary: number;
	gridBottomBoundary: number;
	mapProjection: Projection;
	perspectiveRatioCutoff: number;
	constructor(transform: Transform, projection: Projection, grid?: GridIndex<FeatureKey>, ignoredGrid?: GridIndex<FeatureKey>);
	placeCollisionBox(collisionBox: SingleCollisionBox, overlapMode: OverlapMode, textPixelRatio: number, posMatrix: mat4, unwrappedTileID: UnwrappedTileID, pitchWithMap: boolean, rotateWithMap: boolean, translation: [
		number,
		number
	], collisionGroupPredicate?: (key: FeatureKey) => boolean, getElevation?: (x: number, y: number) => number, shift?: Point): PlacedBox;
	placeCollisionCircles(overlapMode: OverlapMode, symbol: any, lineVertexArray: SymbolLineVertexArray, glyphOffsetArray: GlyphOffsetArray, fontSize: number, posMatrix: mat4, unwrappedTileID: UnwrappedTileID, labelPlaneMatrix: mat4, labelToScreenMatrix: mat4, showCollisionCircles: boolean, pitchWithMap: boolean, collisionGroupPredicate: (key: FeatureKey) => boolean, circlePixelDiameter: number, textPixelPadding: number, translation: [
		number,
		number
	], getElevation: (x: number, y: number) => number): PlacedCircles;
	projectPathToScreenSpace(projectedPath: Array<Point>, projectionContext: SymbolProjectionContext, labelToScreenMatrix: mat4): PointProjection[];
	/**
	 * Because the geometries in the CollisionIndex are an approximation of the shape of
	 * symbols on the map, we use the CollisionIndex to look up the symbol part of
	 * `queryRenderedFeatures`.
	 */
	queryRenderedSymbols(viewportQueryGeometry: Array<Point>): {};
	insertCollisionBox(collisionBox: Array<number>, overlapMode: OverlapMode, ignorePlacement: boolean, bucketInstanceId: number, featureIndex: number, collisionGroupID: number): void;
	insertCollisionCircles(collisionCircles: Array<number>, overlapMode: OverlapMode, ignorePlacement: boolean, bucketInstanceId: number, featureIndex: number, collisionGroupID: number): void;
	projectAndGetPerspectiveRatio(posMatrix: mat4, x: number, y: number, _unwrappedTileID: UnwrappedTileID, getElevation?: (x: number, y: number) => number): {
		point: Point;
		perspectiveRatio: number;
		isOccluded: boolean;
		signedDistanceFromCamera: any;
	};
	getPerspectiveRatio(posMatrix: mat4, x: number, y: number, unwrappedTileID: UnwrappedTileID, getElevation?: (x: number, y: number) => number): number;
	isOffscreen(x1: number, y1: number, x2: number, y2: number): boolean;
	isInsideGrid(x1: number, y1: number, x2: number, y2: number): boolean;
	getViewportMatrix(): mat4;
	/**
	 * Applies all layout+paint properties of the given box in order to find as good approximation of its screen-space bounding box as possible.
	 */
	private _projectCollisionBox;
}
declare enum TextAnchorEnum {
	"center" = 1,
	"left" = 2,
	"right" = 3,
	"top" = 4,
	"bottom" = 5,
	"top-left" = 6,
	"top-right" = 7,
	"bottom-left" = 8,
	"bottom-right" = 9
}
export type TextAnchor = keyof typeof TextAnchorEnum;
declare class OpacityState {
	opacity: number;
	placed: boolean;
	constructor(prevState: OpacityState, increment: number, placed: boolean, skipFade?: boolean | null);
	isHidden(): boolean;
}
declare class JointOpacityState {
	text: OpacityState;
	icon: OpacityState;
	constructor(prevState: JointOpacityState, increment: number, placedText: boolean, placedIcon: boolean, skipFade?: boolean | null);
	isHidden(): boolean;
}
declare class JointPlacement {
	text: boolean;
	icon: boolean;
	skipFade: boolean;
	constructor(text: boolean, icon: boolean, skipFade: boolean);
}
declare class CollisionCircleArray {
	invProjMatrix: mat4;
	viewportMatrix: mat4;
	circles: Array<number>;
	constructor();
}
declare class RetainedQueryData {
	bucketInstanceId: number;
	featureIndex: FeatureIndex;
	sourceLayerIndex: number;
	bucketIndex: number;
	tileID: OverscaledTileID;
	featureSortOrder: Array<number>;
	constructor(bucketInstanceId: number, featureIndex: FeatureIndex, sourceLayerIndex: number, bucketIndex: number, tileID: OverscaledTileID);
}
export type CollisionGroup = {
	ID: number;
	predicate?: (key: FeatureKey) => boolean;
};
declare class CollisionGroups {
	collisionGroups: {
		[groupName: string]: CollisionGroup;
	};
	maxGroupID: number;
	crossSourceCollisions: boolean;
	constructor(crossSourceCollisions: boolean);
	get(sourceID: string): CollisionGroup;
}
export type VariableOffset = {
	textOffset: [
		number,
		number
	];
	width: number;
	height: number;
	anchor: TextAnchor;
	textBoxScale: number;
	prevAnchor?: TextAnchor;
};
export type TileLayerParameters = {
	bucket: SymbolBucket;
	layout: PossiblyEvaluated<SymbolLayoutProps, SymbolLayoutPropsPossiblyEvaluated>;
	translationText: [
		number,
		number
	];
	translationIcon: [
		number,
		number
	];
	unwrappedTileID: UnwrappedTileID;
	posMatrix: mat4;
	textLabelPlaneMatrix: mat4;
	labelToScreenMatrix: mat4;
	scale: number;
	textPixelRatio: number;
	holdingForFade: boolean;
	collisionBoxArray: CollisionBoxArray;
	partiallyEvaluatedTextSize: {
		uSize: number;
		uSizeT: number;
	};
	collisionGroup: CollisionGroup;
};
export type BucketPart = {
	sortKey?: number | void;
	symbolInstanceStart: number;
	symbolInstanceEnd: number;
	parameters: TileLayerParameters;
};
export type CrossTileID = string | number;
declare class Placement {
	transform: Transform;
	terrain: Terrain;
	collisionIndex: CollisionIndex;
	placements: {
		[_ in CrossTileID]: JointPlacement;
	};
	opacities: {
		[_ in CrossTileID]: JointOpacityState;
	};
	variableOffsets: {
		[_ in CrossTileID]: VariableOffset;
	};
	placedOrientations: {
		[_ in CrossTileID]: number;
	};
	commitTime: number;
	prevZoomAdjustment: number;
	lastPlacementChangeTime: number;
	stale: boolean;
	fadeDuration: number;
	retainedQueryData: {
		[_: number]: RetainedQueryData;
	};
	collisionGroups: CollisionGroups;
	prevPlacement: Placement;
	zoomAtLastRecencyCheck: number;
	collisionCircleArrays: {
		[k in any]: CollisionCircleArray;
	};
	collisionBoxArrays: Map<number, Map<number, {
		text: number[];
		icon: number[];
	}>>;
	constructor(transform: Transform, projection: Projection, terrain: Terrain, fadeDuration: number, crossSourceCollisions: boolean, prevPlacement?: Placement);
	private _getTerrainElevationFunc;
	getBucketParts(results: Array<BucketPart>, styleLayer: StyleLayer, tile: Tile, sortAcrossTiles: boolean): void;
	attemptAnchorPlacement(textAnchorOffset: TextAnchorOffset, textBox: SingleCollisionBox, width: number, height: number, textBoxScale: number, rotateWithMap: boolean, pitchWithMap: boolean, textPixelRatio: number, posMatrix: mat4, unwrappedTileID: any, collisionGroup: CollisionGroup, textOverlapMode: OverlapMode, symbolInstance: SymbolInstance, bucket: SymbolBucket, orientation: number, translationText: [
		number,
		number
	], translationIcon: [
		number,
		number
	], iconBox?: SingleCollisionBox | null, getElevation?: (x: number, y: number) => number): {
		shift: Point;
		placedGlyphBoxes: PlacedBox;
	};
	placeLayerBucketPart(bucketPart: BucketPart, seenCrossTileIDs: {
		[k in string | number]: boolean;
	}, showCollisionBoxes: boolean): void;
	storeCollisionData(bucketInstanceId: number, symbolIndex: number, collisionArrays: CollisionArrays, placedGlyphBoxes: PlacedBox, placedIconBoxes: PlacedBox, placedGlyphCircles: PlacedCircles): void;
	markUsedJustification(bucket: SymbolBucket, placedAnchor: TextAnchor, symbolInstance: SymbolInstance, orientation: number): void;
	markUsedOrientation(bucket: SymbolBucket, orientation: number, symbolInstance: SymbolInstance): void;
	commit(now: number): void;
	updateLayerOpacities(styleLayer: StyleLayer, tiles: Array<Tile>): void;
	updateBucketOpacities(bucket: SymbolBucket, tileID: OverscaledTileID, seenCrossTileIDs: {
		[k in string | number]: boolean;
	}, collisionBoxArray?: CollisionBoxArray | null): void;
	symbolFadeChange(now: number): number;
	zoomAdjustment(zoom: number): number;
	hasTransitions(now: number): boolean;
	stillRecent(now: number, zoom: number): boolean;
	setStale(): void;
}
/**
 * Options to pass to query the map for the rendered features
 */
export type QueryRenderedFeaturesOptions = {
	/**
	 * An array of [style layer IDs](https://maplibre.org/maplibre-style-spec/#layer-id) for the query to inspect.
	 * Only features within these layers will be returned. If this parameter is undefined, all layers will be checked.
	 */
	layers?: Array<string>;
	/**
	 * A [filter](https://maplibre.org/maplibre-style-spec/layers/#filter) to limit query results.
	 */
	filter?: FilterSpecification;
	/**
	 * An array of string representing the available images
	 */
	availableImages?: Array<string>;
	/**
	 * Whether to check if the [options.filter] conforms to the MapLibre Style Specification. Disabling validation is a performance optimization that should only be used if you have previously validated the values you will be passing to this function.
	 */
	validate?: boolean;
};
/**
 * The options object related to the {@link Map#querySourceFeatures} method
 */
export type QuerySourceFeatureOptions = {
	/**
	 * The name of the source layer to query. *For vector tile sources, this parameter is required.* For GeoJSON sources, it is ignored.
	 */
	sourceLayer?: string;
	/**
	 * A [filter](https://maplibre.org/maplibre-style-spec/layers/#filter)
	 * to limit query results.
	 */
	filter?: FilterSpecification;
	/**
	 * Whether to check if the [parameters.filter] conforms to the MapLibre Style Specification. Disabling validation is a performance optimization that should only be used if you have previously validated the values you will be passing to this function.
	 * @defaultValue true
	 */
	validate?: boolean;
};
declare class LayerPlacement {
	_sortAcrossTiles: boolean;
	_currentTileIndex: number;
	_currentPartIndex: number;
	_seenCrossTileIDs: {
		[k in string | number]: boolean;
	};
	_bucketParts: Array<BucketPart>;
	constructor(styleLayer: SymbolStyleLayer);
	continuePlacement(tiles: Array<Tile>, placement: Placement, showCollisionBoxes: boolean, styleLayer: StyleLayer, shouldPausePlacement: () => boolean): boolean;
}
declare class PauseablePlacement {
	placement: Placement;
	_done: boolean;
	_currentPlacementIndex: number;
	_forceFullPlacement: boolean;
	_showCollisionBoxes: boolean;
	_inProgressLayer: LayerPlacement;
	constructor(transform: Transform, terrain: Terrain, order: Array<string>, forceFullPlacement: boolean, showCollisionBoxes: boolean, fadeDuration: number, crossSourceCollisions: boolean, prevPlacement?: Placement);
	isDone(): boolean;
	continuePlacement(order: Array<string>, layers: {
		[_: string]: StyleLayer;
	}, layerTiles: {
		[_: string]: Array<Tile>;
	}): void;
	commit(now: number): Placement;
}
/**
* Input arguments exposed by custom render function.
*/
export type CustomRenderMethodInput = {
	/**
	 * This value represents the distance from the camera to the far clipping plane.
	 * It is used in the calculation of the projection matrix to determine which objects are visible.
	 * farZ should be larger than nearZ.
	 */
	farZ: number;
	/**
	 * This value represents the distance from the camera to the near clipping plane.
	 * It is used in the calculation of the projection matrix to determine which objects are visible.
	 * nearZ should be smaller than farZ.
	 */
	nearZ: number;
	/** field of view of camera **/
	fov: number;
	/**
	* model view projection matrix
	* represents the matrix converting from world space to clip space
	* https://learnopengl.com/Getting-started/Coordinate-Systems
	* **/
	modelViewProjectionMatrix: mat4;
	/**
	* projection matrix
	* represents the matrix converting from view space to clip space
	* https://learnopengl.com/Getting-started/Coordinate-Systems
	*/
	projectionMatrix: mat4;
};
/**
 * @param gl - The map's gl context.
 * @param matrix - The map's camera matrix. It projects spherical mercator
 * coordinates to gl clip space coordinates. The spherical mercator coordinate `[0, 0]` represents the
 * top left corner of the mercator world and `[1, 1]` represents the bottom right corner. When
 * the `renderingMode` is `"3d"`, the z coordinate is conformal. A box with identical x, y, and z
 * lengths in mercator units would be rendered as a cube. {@link MercatorCoordinate.fromLngLat}
 * can be used to project a `LngLat` to a mercator coordinate.
 * @param options - Argument object with additional render inputs like camera properties.
 */
export type CustomRenderMethod = (gl: WebGLRenderingContext | WebGL2RenderingContext, matrix: mat4, options: CustomRenderMethodInput) => void;
/**
 * Interface for custom style layers. This is a specification for
 * implementers to model: it is not an exported method or class.
 *
 * Custom layers allow a user to render directly into the map's GL context using the map's camera.
 * These layers can be added between any regular layers using {@link Map#addLayer}.
 *
 * Custom layers must have a unique `id` and must have the `type` of `"custom"`.
 * They must implement `render` and may implement `prerender`, `onAdd` and `onRemove`.
 * They can trigger rendering using {@link Map#triggerRepaint}
 * and they should appropriately handle {@link MapContextEvent} with `webglcontextlost` and `webglcontextrestored`.
 *
 * The `renderingMode` property controls whether the layer is treated as a `"2d"` or `"3d"` map layer. Use:
 *
 * - `"renderingMode": "3d"` to use the depth buffer and share it with other layers
 * - `"renderingMode": "2d"` to add a layer with no depth. If you need to use the depth buffer for a `"2d"` layer you must use an offscreen
 *   framebuffer and {@link CustomLayerInterface#prerender}
 *
 * @example
 * Custom layer implemented as ES6 class
 * ```ts
 * class NullIslandLayer {
 *     constructor() {
 *         this.id = 'null-island';
 *         this.type = 'custom';
 *         this.renderingMode = '2d';
 *     }
 *
 *     onAdd(map, gl) {
 *         const vertexSource = `
 *         uniform mat4 u_matrix;
 *         void main() {
 *             gl_Position = u_matrix * vec4(0.5, 0.5, 0.0, 1.0);
 *             gl_PointSize = 20.0;
 *         }`;
 *
 *         const fragmentSource = `
 *         void main() {
 *             fragColor = vec4(1.0, 0.0, 0.0, 1.0);
 *         }`;
 *
 *         const vertexShader = gl.createShader(gl.VERTEX_SHADER);
 *         gl.shaderSource(vertexShader, vertexSource);
 *         gl.compileShader(vertexShader);
 *         const fragmentShader = gl.createShader(gl.FRAGMENT_SHADER);
 *         gl.shaderSource(fragmentShader, fragmentSource);
 *         gl.compileShader(fragmentShader);
 *
 *         this.program = gl.createProgram();
 *         gl.attachShader(this.program, vertexShader);
 *         gl.attachShader(this.program, fragmentShader);
 *         gl.linkProgram(this.program);
 *     }
 *
 *     render(gl, matrix) {
 *         gl.useProgram(this.program);
 *         gl.uniformMatrix4fv(gl.getUniformLocation(this.program, "u_matrix"), false, matrix);
 *         gl.drawArrays(gl.POINTS, 0, 1);
 *     }
 * }
 *
 * map.on('load', () => {
 *     map.addLayer(new NullIslandLayer());
 * });
 * ```
 */
export interface CustomLayerInterface {
	/**
	 * A unique layer id.
	 */
	id: string;
	/**
	 * The layer's type. Must be `"custom"`.
	 */
	type: "custom";
	/**
	 * Either `"2d"` or `"3d"`. Defaults to `"2d"`.
	 */
	renderingMode?: "2d" | "3d";
	/**
	 * Called during a render frame allowing the layer to draw into the GL context.
	 *
	 * The layer can assume blending and depth state is set to allow the layer to properly
	 * blend and clip other layers. The layer cannot make any other assumptions about the
	 * current GL state.
	 *
	 * If the layer needs to render to a texture, it should implement the `prerender` method
	 * to do this and only use the `render` method for drawing directly into the main framebuffer.
	 *
	 * The blend function is set to `gl.blendFunc(gl.ONE, gl.ONE_MINUS_SRC_ALPHA)`. This expects
	 * colors to be provided in premultiplied alpha form where the `r`, `g` and `b` values are already
	 * multiplied by the `a` value. If you are unable to provide colors in premultiplied form you
	 * may want to change the blend function to
	 * `gl.blendFuncSeparate(gl.SRC_ALPHA, gl.ONE_MINUS_SRC_ALPHA, gl.ONE, gl.ONE_MINUS_SRC_ALPHA)`.
	 */
	render: CustomRenderMethod;
	/**
	 * Optional method called during a render frame to allow a layer to prepare resources or render into a texture.
	 *
	 * The layer cannot make any assumptions about the current GL state and must bind a framebuffer before rendering.
	 */
	prerender?: CustomRenderMethod;
	/**
	 * Optional method called when the layer has been added to the Map with {@link Map#addLayer}. This
	 * gives the layer a chance to initialize gl resources and register event listeners.
	 *
	 * @param map - The Map this custom layer was just added to.
	 * @param gl - The gl context for the map.
	 */
	onAdd?(map: Map$1, gl: WebGLRenderingContext | WebGL2RenderingContext): void;
	/**
	 * Optional method called when the layer has been removed from the Map with {@link Map#removeLayer}. This
	 * gives the layer a chance to clean up gl resources and event listeners.
	 *
	 * @param map - The Map this custom layer was just added to.
	 * @param gl - The gl context for the map.
	 */
	onRemove?(map: Map$1, gl: WebGLRenderingContext | WebGL2RenderingContext): void;
}
export type ValidationError = {
	message: string;
	line: number;
	identifier?: string;
};
export type Validator = (a: any) => ReadonlyArray<ValidationError>;
/**
 * A feature identifier that is bound to a source
 */
export type FeatureIdentifier = {
	/**
	 * Unique id of the feature.
	 */
	id?: string | number | undefined;
	/**
	 * The id of the vector or GeoJSON source for the feature.
	 */
	source: string;
	/**
	 * *For vector tile sources, `sourceLayer` is required.*
	 */
	sourceLayer?: string | undefined;
};
/**
 * The options object related to the {@link Map}'s style related methods
 */
export type StyleOptions = {
	/**
	 * If false, style validation will be skipped. Useful in production environment.
	 */
	validate?: boolean;
	/**
	 * Defines a CSS
	 * font-family for locally overriding generation of Chinese, Japanese, and Korean characters.
	 * For these characters, font settings from the map's style will be ignored, except for font-weight keywords (light/regular/medium/bold).
	 * Set to `false`, to enable font settings from the map's style for these glyph ranges.
	 * Forces a full update.
	 */
	localIdeographFontFamily?: string | false;
};
/**
 * Supporting type to add validation to another style related type
 */
export type StyleSetterOptions = {
	/**
	 * Whether to check if the filter conforms to the MapLibre Style Specification. Disabling validation is a performance optimization that should only be used if you have previously validated the values you will be passing to this function.
	 */
	validate?: boolean;
};
/**
 * Part of {@link Map#setStyle} options, transformStyle is a convenience function that allows to modify a style after it is fetched but before it is committed to the map state.
 *
 * This function exposes previous and next styles, it can be commonly used to support a range of functionalities like:
 *
 * - when previous style carries certain 'state' that needs to be carried over to a new style gracefully;
 * - when a desired style is a certain combination of previous and incoming style;
 * - when an incoming style requires modification based on external state.
 *
 * @param previous - The current style.
 * @param next - The next style.
 * @returns resulting style that will to be applied to the map
 *
 * @example
 * ```ts
 * map.setStyle('https://demotiles.maplibre.org/style.json', {
 *   transformStyle: (previousStyle, nextStyle) => ({
 *       ...nextStyle,
 *       sources: {
 *           ...nextStyle.sources,
 *           // copy a source from previous style
 *           'osm': previousStyle.sources.osm
 *       },
 *       layers: [
 *           // background layer
 *           nextStyle.layers[0],
 *           // copy a layer from previous style
 *           previousStyle.layers[0],
 *           // other layers from the next style
 *           ...nextStyle.layers.slice(1).map(layer => {
 *               // hide the layers we don't need from demotiles style
 *               if (layer.id.startsWith('geolines')) {
 *                   layer.layout = {...layer.layout || {}, visibility: 'none'};
 *               // filter out US polygons
 *               } else if (layer.id.startsWith('coastline') || layer.id.startsWith('countries')) {
 *                   layer.filter = ['!=', ['get', 'ADM0_A3'], 'USA'];
 *               }
 *               return layer;
 *           })
 *       ]
 *   })
 * });
 * ```
 */
export type TransformStyleFunction = (previous: StyleSpecification | undefined, next: StyleSpecification) => StyleSpecification;
/**
 * The options object related to the {@link Map}'s style related methods
 */
export type StyleSwapOptions = {
	/**
	 * If false, force a 'full' update, removing the current style
	 * and building the given one instead of attempting a diff-based update.
	 */
	diff?: boolean;
	/**
	 * TransformStyleFunction is a convenience function
	 * that allows to modify a style after it is fetched but before it is committed to the map state. Refer to {@link TransformStyleFunction}.
	 */
	transformStyle?: TransformStyleFunction;
};
/**
 * Specifies a layer to be added to a {@link Style}. In addition to a standard {@link LayerSpecification}
 * or a {@link CustomLayerInterface}, a {@link LayerSpecification} with an embedded {@link SourceSpecification} can also be provided.
 */
export type AddLayerObject = LayerSpecification | (Omit<LayerSpecification, "source"> & {
	source: SourceSpecification;
}) | CustomLayerInterface;
/**
 * The Style base class
 */
export declare class Style extends Evented {
	map: Map$1;
	stylesheet: StyleSpecification;
	dispatcher: Dispatcher;
	imageManager: ImageManager;
	glyphManager: GlyphManager;
	lineAtlas: LineAtlas;
	light: Light;
	sky: Sky;
	_frameRequest: AbortController;
	_loadStyleRequest: AbortController;
	_spriteRequest: AbortController;
	_layers: {
		[_: string]: StyleLayer;
	};
	_serializedLayers: {
		[_: string]: LayerSpecification;
	};
	_order: Array<string>;
	sourceCaches: {
		[_: string]: SourceCache;
	};
	zoomHistory: ZoomHistory;
	_loaded: boolean;
	_changed: boolean;
	_updatedSources: {
		[_: string]: "clear" | "reload";
	};
	_updatedLayers: {
		[_: string]: true;
	};
	_removedLayers: {
		[_: string]: StyleLayer;
	};
	_changedImages: {
		[_: string]: true;
	};
	_glyphsDidChange: boolean;
	_updatedPaintProps: {
		[layer: string]: true;
	};
	_layerOrderChanged: boolean;
	_spritesImagesIds: {
		[spriteId: string]: string[];
	};
	_availableImages: Array<string>;
	crossTileSymbolIndex: CrossTileSymbolIndex;
	pauseablePlacement: PauseablePlacement;
	placement: Placement;
	z: number;
	constructor(map: Map$1, options?: StyleOptions);
	_rtlPluginLoaded: () => void;
	loadURL(url: string, options?: StyleSwapOptions & StyleSetterOptions, previousStyle?: StyleSpecification): void;
	loadJSON(json: StyleSpecification, options?: StyleSetterOptions & StyleSwapOptions, previousStyle?: StyleSpecification): void;
	loadEmpty(): void;
	_load(json: StyleSpecification, options: StyleSwapOptions & StyleSetterOptions, previousStyle?: StyleSpecification): void;
	private _createLayers;
	_loadSprite(sprite: SpriteSpecification, isUpdate?: boolean, completion?: (err: Error) => void): void;
	_unloadSprite(): void;
	_validateLayer(layer: StyleLayer): void;
	loaded(): boolean;
	/**
	 * @hidden
	 * take an array of string IDs, and based on this._layers, generate an array of LayerSpecification
	 * @param ids - an array of string IDs, for which serialized layers will be generated. If omitted, all serialized layers will be returned
	 * @param returnClose - if true, return a clone of the layer object
	 * @returns generated result
	 */
	private _serializeByIds;
	/**
	 * @hidden
	 * Lazy initialization of this._serializedLayers dictionary and return it
	 * @returns this._serializedLayers dictionary
	 */
	private _serializedAllLayers;
	hasTransitions(): boolean;
	_checkLoaded(): void;
	/**
	 * @internal
	 * Apply queued style updates in a batch and recalculate zoom-dependent paint properties.
	 */
	update(parameters: EvaluationParameters): void;
	_updateTilesForChangedImages(): void;
	_updateTilesForChangedGlyphs(): void;
	_updateWorkerLayers(updatedIds: Array<string>, removedIds: Array<string>): void;
	_resetUpdates(): void;
	/**
	 * Update this style's state to match the given style JSON, performing only
	 * the necessary mutations.
	 *
	 * May throw an Error ('Unimplemented: METHOD') if the mapbox-gl-style-spec
	 * diff algorithm produces an operation that is not supported.
	 *
	 * @returns true if any changes were made; false otherwise
	 */
	setState(nextState: StyleSpecification, options?: StyleSwapOptions & StyleSetterOptions): boolean;
	_getOperationsToPerform(diff: DiffCommand<DiffOperations>[]): {
		operations: Function[];
		unimplemented: string[];
	};
	addImage(id: string, image: StyleImage): this;
	updateImage(id: string, image: StyleImage): void;
	getImage(id: string): StyleImage;
	removeImage(id: string): this;
	_afterImageUpdated(id: string): void;
	listImages(): string[];
	addSource(id: string, source: SourceSpecification | CanvasSourceSpecification, options?: StyleSetterOptions): void;
	/**
	 * Remove a source from this stylesheet, given its id.
	 * @param id - id of the source to remove
	 * @throws if no source is found with the given ID
	 */
	removeSource(id: string): this;
	/**
	 * Set the data of a GeoJSON source, given its id.
	 * @param id - id of the source
	 * @param data - GeoJSON source
	 */
	setGeoJSONSourceData(id: string, data: GeoJSON.GeoJSON | string): void;
	/**
	 * Get a source by ID.
	 * @param id - ID of the desired source
	 * @returns source
	 */
	getSource(id: string): Source | undefined;
	/**
	 * Add a layer to the map style. The layer will be inserted before the layer with
	 * ID `before`, or appended if `before` is omitted.
	 * @param layerObject - The style layer to add.
	 * @param before - ID of an existing layer to insert before
	 * @param options - Style setter options.
	 */
	addLayer(layerObject: AddLayerObject, before?: string, options?: StyleSetterOptions): this;
	/**
	 * Moves a layer to a different z-position. The layer will be inserted before the layer with
	 * ID `before`, or appended if `before` is omitted.
	 * @param id - ID of the layer to move
	 * @param before - ID of an existing layer to insert before
	 */
	moveLayer(id: string, before?: string): void;
	/**
	 * Remove the layer with the given id from the style.
	 * A {@link ErrorEvent} event will be fired if no such layer exists.
	 *
	 * @param id - id of the layer to remove
	 */
	removeLayer(id: string): void;
	/**
	 * Return the style layer object with the given `id`.
	 *
	 * @param id - id of the desired layer
	 * @returns a layer, if one with the given `id` exists
	 */
	getLayer(id: string): StyleLayer | undefined;
	/**
	 * Return the ids of all layers currently in the style, including custom layers, in order.
	 *
	 * @returns ids of layers, in order
	 */
	getLayersOrder(): string[];
	/**
	 * Checks if a specific layer is present within the style.
	 *
	 * @param id - the id of the desired layer
	 * @returns a boolean specifying if the given layer is present
	 */
	hasLayer(id: string): boolean;
	setLayerZoomRange(layerId: string, minzoom?: number | null, maxzoom?: number | null): void;
	setFilter(layerId: string, filter?: FilterSpecification | null, options?: StyleSetterOptions): void;
	/**
	 * Get a layer's filter object
	 * @param layer - the layer to inspect
	 * @returns the layer's filter, if any
	 */
	getFilter(layer: string): FilterSpecification | void;
	setLayoutProperty(layerId: string, name: string, value: any, options?: StyleSetterOptions): void;
	/**
	 * Get a layout property's value from a given layer
	 * @param layerId - the layer to inspect
	 * @param name - the name of the layout property
	 * @returns the property value
	 */
	getLayoutProperty(layerId: string, name: string): any;
	setPaintProperty(layerId: string, name: string, value: any, options?: StyleSetterOptions): void;
	getPaintProperty(layer: string, name: string): unknown;
	setFeatureState(target: FeatureIdentifier, state: any): void;
	removeFeatureState(target: FeatureIdentifier, key?: string): void;
	getFeatureState(target: FeatureIdentifier): import("@maplibre/maplibre-gl-style-spec").FeatureState;
	getTransition(): {
		duration: number;
		delay: number;
	} & import("@maplibre/maplibre-gl-style-spec").TransitionSpecification;
	serialize(): StyleSpecification | undefined;
	_updateLayer(layer: StyleLayer): void;
	_flattenAndSortRenderedFeatures(sourceResults: Array<{
		[key: string]: Array<{
			featureIndex: number;
			feature: MapGeoJSONFeature;
		}>;
	}>): any[];
	queryRenderedFeatures(queryGeometry: any, params: QueryRenderedFeaturesOptions, transform: Transform): any[];
	querySourceFeatures(sourceID: string, params?: QuerySourceFeatureOptions): any[];
	getLight(): LightSpecification;
	setLight(lightOptions: LightSpecification, options?: StyleSetterOptions): void;
	getSky(): SkySpecification;
	setSky(skyOptions?: SkySpecification, options?: StyleSetterOptions): void;
	_validate(validate: Validator, key: string, value: any, props: any, options?: {
		validate?: boolean;
	}): boolean;
	_remove(mapRemoved?: boolean): void;
	_clearSource(id: string): void;
	_reloadSource(id: string): void;
	_updateSources(transform: Transform): void;
	_generateCollisionBoxes(): void;
	_updatePlacement(transform: Transform, showCollisionBoxes: boolean, fadeDuration: number, crossSourceCollisions: boolean, forceFullPlacement?: boolean): boolean;
	_releaseSymbolFadeTiles(): void;
	getImages(mapId: string | number, params: GetImagesParameters): Promise<GetImagesResponse>;
	getGlyphs(mapId: string | number, params: GetGlyphsParameters): Promise<GetGlyphsResponse>;
	getGlyphsUrl(): string;
	setGlyphs(glyphsUrl: string | null, options?: StyleSetterOptions): void;
	/**
	 * Add a sprite.
	 *
	 * @param id - The id of the desired sprite
	 * @param url - The url to load the desired sprite from
	 * @param options - The style setter options
	 * @param completion - The completion handler
	 */
	addSprite(id: string, url: string, options?: StyleSetterOptions, completion?: (err: Error) => void): void;
	/**
	 * Remove a sprite by its id. When the last sprite is removed, the whole `this.stylesheet.sprite` object becomes
	 * `undefined`. This falsy `undefined` value later prevents attempts to load the sprite when it's absent.
	 *
	 * @param id - the id of the sprite to remove
	 */
	removeSprite(id: string): void;
	/**
	 * Get the current sprite value.
	 *
	 * @returns empty array when no sprite is set; id-url pairs otherwise
	 */
	getSprite(): {
		id: string;
		url: string;
	}[];
	/**
	 * Set a new value for the style's sprite.
	 *
	 * @param sprite - new sprite value
	 * @param options - style setter options
	 * @param completion - the completion handler
	 */
	setSprite(sprite: SpriteSpecification, options?: StyleSetterOptions, completion?: (err: Error) => void): void;
}
export type BucketParameters<Layer extends TypedStyleLayer> = {
	index: number;
	layers: Array<Layer>;
	zoom: number;
	pixelRatio: number;
	overscaling: number;
	collisionBoxArray: CollisionBoxArray;
	sourceLayerIndex: number;
	sourceID: string;
};
export type PopulateParameters = {
	featureIndex: FeatureIndex;
	iconDependencies: {};
	patternDependencies: {};
	glyphDependencies: {};
	availableImages: Array<string>;
};
export type IndexedFeature = {
	feature: VectorTileFeature;
	id: number | string;
	index: number;
	sourceLayerIndex: number;
};
export type BucketFeature = {
	index: number;
	sourceLayerIndex: number;
	geometry: Array<Array<Point>>;
	properties: any;
	type: 0 | 1 | 2 | 3;
	id?: any;
	readonly patterns: {
		[_: string]: {
			"min": string;
			"mid": string;
			"max": string;
		};
	};
	sortKey?: number;
};
/**
 * The `Bucket` interface is the single point of knowledge about turning vector
 * tiles into WebGL buffers.
 *
 * `Bucket` is an abstract interface. An implementation exists for each style layer type.
 * Create a bucket via the `StyleLayer#createBucket` method.
 *
 * The concrete bucket types, using layout options from the style layer,
 * transform feature geometries into vertex and index data for use by the
 * vertex shader.  They also (via `ProgramConfiguration`) use feature
 * properties and the zoom level to populate the attributes needed for
 * data-driven styling.
 *
 * Buckets are designed to be built on a worker thread and then serialized and
 * transferred back to the main thread for rendering.  On the worker side, a
 * bucket's vertex, index, and attribute data is stored in `bucket.arrays: ArrayGroup`.
 * When a bucket's data is serialized and sent back to the main thread,
 * is gets deserialized (using `new Bucket(serializedBucketData)`, with
 * the array data now stored in `bucket.buffers: BufferGroup`. BufferGroups
 * hold the same data as ArrayGroups, but are tuned for consumption by WebGL.
 */
export interface Bucket {
	layerIds: Array<string>;
	hasPattern: boolean;
	readonly layers: Array<any>;
	readonly stateDependentLayers: Array<any>;
	readonly stateDependentLayerIds: Array<string>;
	populate(features: Array<IndexedFeature>, options: PopulateParameters, canonical: CanonicalTileID): void;
	update(states: FeatureStates, vtLayer: VectorTileLayer, imagePositions: {
		[_: string]: ImagePosition;
	}): void;
	isEmpty(): boolean;
	upload(context: Context): void;
	uploadPending(): boolean;
	/**
	 * Release the WebGL resources associated with the buffers. Note that because
	 * buckets are shared between layers having the same layout properties, they
	 * must be destroyed in groups (all buckets for a tile, or all symbol buckets).
	 */
	destroy(): void;
}
declare abstract class StyleLayer extends Evented {
	id: string;
	metadata: unknown;
	type: LayerSpecification["type"] | CustomLayerInterface["type"];
	source: string;
	sourceLayer: string;
	minzoom: number;
	maxzoom: number;
	filter: FilterSpecification | void;
	visibility: "visible" | "none" | void;
	_crossfadeParameters: CrossfadeParameters;
	_unevaluatedLayout: Layout<any>;
	readonly layout: unknown;
	_transitionablePaint: Transitionable<any>;
	_transitioningPaint: Transitioning<any>;
	readonly paint: unknown;
	_featureFilter: FeatureFilter;
	readonly onAdd: ((map: Map$1) => void);
	readonly onRemove: ((map: Map$1) => void);
	queryRadius?(bucket: Bucket): number;
	queryIntersectsFeature?(queryGeometry: Array<Point>, feature: VectorTileFeature, featureState: FeatureState, geometry: Array<Array<Point>>, zoom: number, transform: Transform, pixelsToTileUnits: number, pixelPosMatrix: mat4): boolean | number;
	constructor(layer: LayerSpecification | CustomLayerInterface, properties: Readonly<{
		layout?: Properties<any>;
		paint?: Properties<any>;
	}>);
	getCrossfadeParameters(): CrossfadeParameters;
	getLayoutProperty(name: string): any;
	setLayoutProperty(name: string, value: any, options?: StyleSetterOptions): void;
	getPaintProperty(name: string): unknown;
	setPaintProperty(name: string, value: unknown, options?: StyleSetterOptions): boolean;
	_handleSpecialPaintPropertyUpdate(_: string): void;
	_handleOverridablePaintPropertyUpdate<T, R>(name: string, oldValue: PropertyValue<T, R>, newValue: PropertyValue<T, R>): boolean;
	isHidden(zoom: number): boolean;
	updateTransitions(parameters: TransitionParameters): void;
	hasTransition(): boolean;
	recalculate(parameters: EvaluationParameters, availableImages: Array<string>): void;
	serialize(): LayerSpecification;
	_validate(validate: Function, key: string, name: string, value: unknown, options?: StyleSetterOptions): boolean;
	is3D(): boolean;
	isTileClipped(): boolean;
	hasOffscreenPass(): boolean;
	resize(): void;
	isStateDependent(): boolean;
}
/**
 * A way to identify a feature, either by string or by number
 */
export type GeoJSONFeatureId = number | string;
/**
 * The geojson source diff object
 */
export type GeoJSONSourceDiff = {
	/**
	 * When set to `true` it will remove all features
	 */
	removeAll?: boolean;
	/**
	 * An array of features IDs to remove
	 */
	remove?: Array<GeoJSONFeatureId>;
	/**
	 * An array of features to add
	 */
	add?: Array<GeoJSON.Feature>;
	/**
	 * An array of update objects
	 */
	update?: Array<GeoJSONFeatureDiff>;
};
/**
 * A geojson feature diff object
 */
export type GeoJSONFeatureDiff = {
	/**
	 * The feature ID
	 */
	id: GeoJSONFeatureId;
	/**
	 * If it's a new geometry, place it here
	 */
	newGeometry?: GeoJSON.Geometry;
	/**
	 * Setting to `true` will remove all preperties
	 */
	removeAllProperties?: boolean;
	/**
	 * The properties keys to remove
	 */
	removeProperties?: Array<string>;
	/**
	 * The properties to add or update along side their values
	 */
	addOrUpdateProperties?: Array<{
		key: string;
		value: any;
	}>;
};
/**
 * The geojson worker options that can be passed to the worker
 */
export type GeoJSONWorkerOptions = {
	source?: string;
	cluster?: boolean;
	geojsonVtOptions?: GeoJSONVTOptions;
	superclusterOptions?: SuperclusterOptions<any, any>;
	clusterProperties?: ClusterProperties;
	filter?: Array<unknown>;
	promoteId?: string;
	collectResourceTiming?: boolean;
};
/**
 * Parameters needed to load a geojson to the worker
 */
export type LoadGeoJSONParameters = GeoJSONWorkerOptions & {
	type: "geojson";
	request?: RequestParameters;
	/**
	 * Literal GeoJSON data. Must be provided if `request.url` is not.
	 */
	data?: string;
	dataDiff?: GeoJSONSourceDiff;
};
/**
 * The possible option of the plugin's status
 *
 * `unavailable`: Not loaded.
 *
 * `deferred`: The plugin URL has been specified, but loading has been deferred.
 *
 * `requested`: at least one tile needs RTL to render, but the plugin has not been set
 *
 * `loading`: RTL is in the process of being loaded by worker.
 *
 * `loaded`: The plugin is now loaded
 *
 *  `error`: The plugin failed to load
 */
export type RTLPluginStatus = "unavailable" | "deferred" | "requested" | "loading" | "loaded" | "error";
/**
 * The RTL plugin state
 */
export type PluginState = {
	pluginStatus: RTLPluginStatus;
	pluginURL: string;
};
/**
 * The parameters needed in order to get information about the cluster
 */
export type ClusterIDAndSource = {
	type: "geojson";
	clusterId: number;
	source: string;
};
/**
 * Parameters needed to get the leaves of a cluster
 */
export type GetClusterLeavesParams = ClusterIDAndSource & {
	limit: number;
	offset: number;
};
/**
 * The result of the call to load a geojson source
 */
export type GeoJSONWorkerSourceLoadDataResult = {
	resourceTiming?: {
		[_: string]: Array<PerformanceResourceTiming>;
	};
	abandoned?: boolean;
};
/**
 * Parameters needed to remove a source
 */
export type RemoveSourceParams = {
	source: string;
	type: string;
};
/**
 * Parameters needed to update the layers
 */
export type UpdateLayersParamaeters = {
	layers: Array<LayerSpecification>;
	removedIds: Array<string>;
};
/**
 * Parameters needed to get the images
 */
export type GetImagesParameters = {
	icons: Array<string>;
	source: string;
	tileID: OverscaledTileID;
	type: string;
};
/**
 * Parameters needed to get the glyphs
 */
export type GetGlyphsParameters = {
	type: string;
	stacks: {
		[_: string]: Array<number>;
	};
	source: string;
	tileID: OverscaledTileID;
};
/**
 * A response object returned when requesting glyphs
 */
export type GetGlyphsResponse = {
	[stack: string]: {
		[id: number]: StyleGlyph;
	};
};
/**
 * A response object returned when requesting images
 */
export type GetImagesResponse = {
	[_: string]: StyleImage;
};
/**
 * All the possible message types that can be sent to and from the worker
 */
export declare const enum MessageType {
	loadDEMTile = "LDT",
	getClusterExpansionZoom = "GCEZ",
	getClusterChildren = "GCC",
	getClusterLeaves = "GCL",
	loadData = "LD",
	getData = "GD",
	loadTile = "LT",
	reloadTile = "RT",
	getGlyphs = "GG",
	getImages = "GI",
	setImages = "SI",
	setLayers = "SL",
	updateLayers = "UL",
	syncRTLPluginState = "SRPS",
	setReferrer = "SR",
	removeSource = "RS",
	removeMap = "RM",
	importScript = "IS",
	removeTile = "RMT",
	abortTile = "AT",
	removeDEMTile = "RDT",
	getResource = "GR"
}
/**
 * This is basically a mapping between all the calls that are made to and from the workers.
 * The key is the event name, the first parameter is the event input type, and the last parameter is the output type.
 */
export type RequestResponseMessageMap = {
	[MessageType.loadDEMTile]: [
		WorkerDEMTileParameters,
		DEMData
	];
	[MessageType.getClusterExpansionZoom]: [
		ClusterIDAndSource,
		number
	];
	[MessageType.getClusterChildren]: [
		ClusterIDAndSource,
		Array<GeoJSON.Feature>
	];
	[MessageType.getClusterLeaves]: [
		GetClusterLeavesParams,
		Array<GeoJSON.Feature>
	];
	[MessageType.loadData]: [
		LoadGeoJSONParameters,
		GeoJSONWorkerSourceLoadDataResult
	];
	[MessageType.getData]: [
		LoadGeoJSONParameters,
		GeoJSON.GeoJSON
	];
	[MessageType.loadTile]: [
		WorkerTileParameters,
		WorkerTileResult
	];
	[MessageType.reloadTile]: [
		WorkerTileParameters,
		WorkerTileResult
	];
	[MessageType.getGlyphs]: [
		GetGlyphsParameters,
		GetGlyphsResponse
	];
	[MessageType.getImages]: [
		GetImagesParameters,
		GetImagesResponse
	];
	[MessageType.setImages]: [
		string[],
		void
	];
	[MessageType.setLayers]: [
		Array<LayerSpecification>,
		void
	];
	[MessageType.updateLayers]: [
		UpdateLayersParamaeters,
		void
	];
	[MessageType.syncRTLPluginState]: [
		PluginState,
		PluginState
	];
	[MessageType.setReferrer]: [
		string,
		void
	];
	[MessageType.removeSource]: [
		RemoveSourceParams,
		void
	];
	[MessageType.removeMap]: [
		undefined,
		void
	];
	[MessageType.importScript]: [
		string,
		void
	];
	[MessageType.removeTile]: [
		TileParameters,
		void
	];
	[MessageType.abortTile]: [
		TileParameters,
		void
	];
	[MessageType.removeDEMTile]: [
		TileParameters,
		void
	];
	[MessageType.getResource]: [
		RequestParameters,
		GetResourceResponse<any>
	];
};
/**
 * The message to be sent by the actor
 */
export type ActorMessage<T extends MessageType> = {
	type: T;
	data: RequestResponseMessageMap[T][0];
	targetMapId?: string | number | null;
	mustQueue?: boolean;
	sourceMapId?: string | number | null;
};
/**
 * An interface to be sent to the actor in order for it to allow communication between the worker and the main thread
 */
export interface ActorTarget {
	addEventListener: typeof window.addEventListener;
	removeEventListener: typeof window.removeEventListener;
	postMessage: typeof window.postMessage;
	terminate?: () => void;
}
/**
 * This is used to define the parameters of the message that is sent to the worker and back
 */
export type MessageData = {
	id: string;
	type: MessageType | "<cancel>" | "<response>";
	origin: string;
	data?: Serialized;
	targetMapId?: string | number | null;
	mustQueue?: boolean;
	error?: Serialized | null;
	sourceMapId: string | number | null;
};
export type ResolveReject = {
	resolve: (value?: RequestResponseMessageMap[MessageType][1]) => void;
	reject: (reason?: Error) => void;
};
/**
 * This interface allowing to substitute only the sendAsync method of the Actor class.
 */
export interface IActor {
	sendAsync<T extends MessageType>(message: ActorMessage<T>, abortController?: AbortController): Promise<RequestResponseMessageMap[T][1]>;
}
export type MessageHandler<T extends MessageType> = (mapId: string | number, params: RequestResponseMessageMap[T][0], abortController?: AbortController) => Promise<RequestResponseMessageMap[T][1]>;
declare class Actor implements IActor {
	target: ActorTarget;
	mapId: string | number | null;
	resolveRejects: {
		[x: string]: ResolveReject;
	};
	name: string;
	tasks: {
		[x: string]: MessageData;
	};
	taskQueue: Array<string>;
	abortControllers: {
		[x: number | string]: AbortController;
	};
	invoker: ThrottledInvoker;
	globalScope: ActorTarget;
	messageHandlers: {
		[x in MessageType]?: MessageHandler<MessageType>;
	};
	subscription: Subscription;
	/**
	 * @param target - The target
	 * @param mapId - A unique identifier for the Map instance using this Actor.
	 */
	constructor(target: ActorTarget, mapId?: string | number);
	registerMessageHandler<T extends MessageType>(type: T, handler: MessageHandler<T>): void;
	/**
	 * Sends a message from a main-thread map to a Worker or from a Worker back to
	 * a main-thread map instance.
	 * @param message - the message to send
	 * @param abortController - an optional AbortController to abort the request
	 * @returns a promise that will be resolved with the response data
	 */
	sendAsync<T extends MessageType>(message: ActorMessage<T>, abortController?: AbortController): Promise<RequestResponseMessageMap[T][1]>;
	receive(message: {
		data: MessageData;
	}): void;
	process(): void;
	processTask(id: string, task: MessageData): Promise<void>;
	completeTask(id: string, err: Error, data?: RequestResponseMessageMap[MessageType][1]): void;
	remove(): void;
}
export interface Subscription {
	unsubscribe(): void;
}
/**
 * Makes optional keys required and add the the undefined type.
 *
 * ```
 * interface Test {
 *  foo: number;
 *  bar?: number;
 *  baz: number | undefined;
 * }
 *
 * Complete<Test> {
 *  foo: number;
 *  bar: number | undefined;
 *  baz: number | undefined;
 * }
 *
 * ```
 *
 * See https://medium.com/terria/typescript-transforming-optional-properties-to-required-properties-that-may-be-undefined-7482cb4e1585
 */
export type Complete<T> = {
	[P in keyof Required<T>]: Pick<T, P> extends Required<Pick<T, P>> ? T[P] : (T[P] | undefined);
};
/**
 * Adds the map's position to its page's location hash.
 * Passed as an option to the map object.
 *
 * @group Markers and Controls
 */
export declare class Hash {
	_map: Map$1;
	_hashName: string;
	constructor(hashName?: string | null);
	/**
	 * Map element to listen for coordinate changes
	 *
	 * @param map - The map object
	 */
	addTo(map: Map$1): this;
	/**
	 * Removes hash
	 */
	remove(): this;
	getHashString(mapFeedback?: boolean): string;
	_getCurrentHash: () => any;
	_onHashChange: () => boolean;
	_updateHashUnthrottled: () => void;
	_removeHash: () => void;
	/**
	 * Mobile Safari doesn't allow updating the hash more than 100 times per 30 seconds.
	 */
	_updateHash: () => ReturnType<typeof setTimeout>;
}
export type TaskID = number;
export type Task = {
	callback: (timeStamp: number) => void;
	id: TaskID;
	cancelled: boolean;
};
declare class TaskQueue {
	_queue: Array<Task>;
	_id: TaskID;
	_cleared: boolean;
	_currentlyRunning: Array<Task> | false;
	constructor();
	add(callback: (timeStamp: number) => void): TaskID;
	remove(id: TaskID): void;
	run(timeStamp?: number): void;
	clear(): void;
}
export interface DragMovementResult {
	bearingDelta?: number;
	pitchDelta?: number;
	around?: Point;
	panDelta?: Point;
}
export interface DragPanResult extends DragMovementResult {
	around: Point;
	panDelta: Point;
}
export interface DragRotateResult extends DragMovementResult {
	bearingDelta: number;
}
export interface DragPitchResult extends DragMovementResult {
	pitchDelta: number;
}
export interface DragMoveHandler<T extends DragMovementResult, E extends Event> extends Handler {
	dragStart: (e: E, point: Point) => void;
	dragMove: (e: E, point: Point) => T | void;
	dragEnd: (e: E) => void;
	getClickTolerance: () => number;
}
/**
 * `MousePanHandler` allows the user to pan the map by clicking and dragging
 */
export interface MousePanHandler extends DragMoveHandler<DragPanResult, MouseEvent> {
}
/**
 * `MouseRotateHandler` allows the user to rotate the map by clicking and dragging
 */
export interface MouseRotateHandler extends DragMoveHandler<DragRotateResult, MouseEvent> {
}
/**
 * `MousePitchHandler` allows the user to zoom the map by pitching
 */
export interface MousePitchHandler extends DragMoveHandler<DragPitchResult, MouseEvent> {
}
declare class TouchPanHandler implements Handler {
	_enabled: boolean;
	_active: boolean;
	_touches: {
		[k in string | number]: Point;
	};
	_clickTolerance: number;
	_sum: Point;
	_map: Map$1;
	constructor(options: {
		clickTolerance: number;
	}, map: Map$1);
	reset(): void;
	_shouldBePrevented(touchesCount: number): boolean;
	touchstart(e: TouchEvent, points: Array<Point>, mapTouches: Array<Touch>): {
		around: Point;
		panDelta: Point;
	};
	touchmove(e: TouchEvent, points: Array<Point>, mapTouches: Array<Touch>): {
		around: Point;
		panDelta: Point;
	};
	touchend(e: TouchEvent, points: Array<Point>, mapTouches: Array<Touch>): void;
	touchcancel(): void;
	_calculateTransform(e: TouchEvent, points: Array<Point>, mapTouches: Array<Touch>): {
		around: Point;
		panDelta: Point;
	};
	enable(): void;
	disable(): void;
	isEnabled(): boolean;
	isActive(): boolean;
}
/**
 * A {@link DragPanHandler} options object
 */
export type DragPanOptions = {
	/**
	 * factor used to scale the drag velocity
	 * @defaultValue 0
	 */
	linearity?: number;
	/**
	 * easing function applied to `map.panTo` when applying the drag.
	 * @param t - the easing function
	 * @defaultValue bezier(0, 0, 0.3, 1)
	 */
	easing?: (t: number) => number;
	/**
	 * the maximum value of the drag velocity.
	 * @defaultValue 1400
	 */
	deceleration?: number;
	/**
	 * the rate at which the speed reduces after the pan ends.
	 * @defaultValue 2500
	 */
	maxSpeed?: number;
};
/**
 * The `DragPanHandler` allows the user to pan the map by clicking and dragging
 * the cursor.
 *
 * @group Handlers
 */
export declare class DragPanHandler {
	_el: HTMLElement;
	_mousePan: MousePanHandler;
	_touchPan: TouchPanHandler;
	_inertiaOptions: DragPanOptions | boolean;
	/** @internal */
	constructor(el: HTMLElement, mousePan: MousePanHandler, touchPan: TouchPanHandler);
	/**
	 * Enables the "drag to pan" interaction.
	 *
	 * @param options - Options object
	 * @example
	 * ```ts
	 *   map.dragPan.enable();
	 *   map.dragPan.enable({
	 *      linearity: 0.3,
	 *      easing: bezier(0, 0, 0.3, 1),
	 *      maxSpeed: 1400,
	 *      deceleration: 2500,
	 *   });
	 * ```
	 */
	enable(options?: DragPanOptions | boolean): void;
	/**
	 * Disables the "drag to pan" interaction.
	 *
	 * @example
	 * ```ts
	 * map.dragPan.disable();
	 * ```
	 */
	disable(): void;
	/**
	 * Returns a Boolean indicating whether the "drag to pan" interaction is enabled.
	 *
	 * @returns `true` if the "drag to pan" interaction is enabled.
	 */
	isEnabled(): boolean;
	/**
	 * Returns a Boolean indicating whether the "drag to pan" interaction is active, i.e. currently being used.
	 *
	 * @returns `true` if the "drag to pan" interaction is active.
	 */
	isActive(): boolean;
}
declare class HandlerInertia {
	_map: Map$1;
	_inertiaBuffer: Array<{
		time: number;
		settings: any;
	}>;
	constructor(map: Map$1);
	clear(): void;
	record(settings: any): void;
	_drainInertiaBuffer(): void;
	_onMoveEnd(panInertiaOptions?: DragPanOptions | boolean): any;
}
/**
 * Handlers interpret dom events and return camera changes that should be
 * applied to the map (`HandlerResult`s). The camera changes are all deltas.
 * The handler itself should have no knowledge of the map's current state.
 * This makes it easier to merge multiple results and keeps handlers simpler.
 * For example, if there is a mousedown and mousemove, the mousePan handler
 * would return a `panDelta` on the mousemove.
 */
export interface Handler {
	enable(): void;
	disable(): void;
	isEnabled(): boolean;
	/**
	 * This is used to indicate if the handler is currently active or not.
	 * In case a handler is active, it will block other handlers from getting the relevant events.
	 * There is an allow list of handlers that can be active at the same time, which is configured when adding a handler.
	 */
	isActive(): boolean;
	/**
	 * `reset` can be called by the manager at any time and must reset everything to it's original state
	 */
	reset(): void;
	readonly touchstart?: (e: TouchEvent, points: Array<Point>, mapTouches: Array<Touch>) => HandlerResult | void;
	readonly touchmove?: (e: TouchEvent, points: Array<Point>, mapTouches: Array<Touch>) => HandlerResult | void;
	readonly touchmoveWindow?: (e: TouchEvent, points: Array<Point>, mapTouches: Array<Touch>) => HandlerResult | void;
	readonly touchend?: (e: TouchEvent, points: Array<Point>, mapTouches: Array<Touch>) => HandlerResult | void;
	readonly touchcancel?: (e: TouchEvent, points: Array<Point>, mapTouches: Array<Touch>) => HandlerResult | void;
	readonly mousedown?: (e: MouseEvent, point: Point) => HandlerResult | void;
	readonly mousemove?: (e: MouseEvent, point: Point) => HandlerResult | void;
	readonly mousemoveWindow?: (e: MouseEvent, point: Point) => HandlerResult | void;
	readonly mouseup?: (e: MouseEvent, point: Point) => HandlerResult | void;
	readonly mouseupWindow?: (e: MouseEvent, point: Point) => HandlerResult | void;
	readonly dblclick?: (e: MouseEvent, point: Point) => HandlerResult | void;
	readonly contextmenu?: (e: MouseEvent) => HandlerResult | void;
	readonly wheel?: (e: WheelEvent, point: Point) => HandlerResult | void;
	readonly keydown?: (e: KeyboardEvent) => HandlerResult | void;
	readonly keyup?: (e: KeyboardEvent) => HandlerResult | void;
	/**
	 * `renderFrame` is the only non-dom event. It is called during render
	 * frames and can be used to smooth camera changes (see scroll handler).
	 */
	readonly renderFrame?: () => HandlerResult | void;
}
/**
 * All handler methods that are called with events can optionally return a `HandlerResult`.
 */
export type HandlerResult = {
	panDelta?: Point;
	zoomDelta?: number;
	bearingDelta?: number;
	pitchDelta?: number;
	/**
	 * the point to not move when changing the camera
	 */
	around?: Point | null;
	/**
	 * same as above, except for pinch actions, which are given higher priority
	 */
	pinchAround?: Point | null;
	/**
	 * A method that can fire a one-off easing by directly changing the map's camera.
	 */
	cameraAnimation?: (map: Map$1) => any;
	/**
	 * The last three properties are needed by only one handler: scrollzoom.
	 * The DOM event to be used as the `originalEvent` on any camera change events.
	 */
	originalEvent?: Event$1;
	/**
	 * Makes the manager trigger a frame, allowing the handler to return multiple results over time (see scrollzoom).
	 */
	needsRenderFrame?: boolean;
	/**
	 * The camera changes won't get recorded for inertial zooming.
	 */
	noInertia?: boolean;
};
export type EventInProgress = {
	handlerName: string;
	originalEvent: Event$1;
};
export type EventsInProgress = {
	zoom?: EventInProgress;
	pitch?: EventInProgress;
	rotate?: EventInProgress;
	drag?: EventInProgress;
};
declare class HandlerManager {
	_map: Map$1;
	_el: HTMLElement;
	_handlers: Array<{
		handlerName: string;
		handler: Handler;
		allowed: Array<string>;
	}>;
	_eventsInProgress: EventsInProgress;
	_frameId: number;
	_inertia: HandlerInertia;
	_bearingSnap: number;
	_handlersById: {
		[x: string]: Handler;
	};
	_updatingCamera: boolean;
	_changes: Array<[
		HandlerResult,
		EventsInProgress,
		{
			[handlerName: string]: Event$1;
		}
	]>;
	_terrainMovement: boolean;
	_zoom: {
		handlerName: string;
	};
	_previousActiveHandlers: {
		[x: string]: Handler;
	};
	_listeners: Array<[
		Window | Document | HTMLElement,
		string,
		{
			passive?: boolean;
			capture?: boolean;
		} | undefined
	]>;
	constructor(map: Map$1, options: CompleteMapOptions);
	destroy(): void;
	_addDefaultHandlers(options: CompleteMapOptions): void;
	_add(handlerName: string, handler: Handler, allowed?: Array<string>): void;
	stop(allowEndAnimation: boolean): void;
	isActive(): boolean;
	isZooming(): boolean;
	isRotating(): boolean;
	isMoving(): boolean;
	_blockedByActive(activeHandlers: {
		[x: string]: Handler;
	}, allowed: Array<string>, myName: string): boolean;
	handleWindowEvent: (e: {
		type: "mousemove" | "mouseup" | "touchmove";
	}) => void;
	_getMapTouches(touches: TouchList): TouchList;
	handleEvent: (e: Event$1, eventName?: keyof Handler) => void;
	mergeHandlerResult(mergedHandlerResult: HandlerResult, eventsInProgress: EventsInProgress, handlerResult: HandlerResult, name: string, e?: UIEvent): void;
	_applyChanges(): void;
	_updateMapTransform(combinedResult: HandlerResult, combinedEventsInProgress: EventsInProgress, deactivatedHandlers: {
		[handlerName: string]: Event$1;
	}): void;
	_fireEvents(newEventsInProgress: EventsInProgress, deactivatedHandlers: {
		[handlerName: string]: Event$1;
	}, allowEndAnimation: boolean): void;
	_fireEvent(type: string, e?: Event$1): void;
	_requestFrame(): number;
	_triggerRenderFrame(): void;
}
/**
 * A [Point](https://github.com/mapbox/point-geometry) or an array of two numbers representing `x` and `y` screen coordinates in pixels.
 *
 * @group Geography and Geometry
 *
 * @example
 * ```ts
 * let p1 = new Point(-77, 38); // a PointLike which is a Point
 * let p2 = [-77, 38]; // a PointLike which is an array of two numbers
 * ```
 */
export type PointLike = Point | [
	number,
	number
];
/**
 * A helper to allow require of at least one property
 */
export type RequireAtLeastOne<T> = {
	[K in keyof T]-?: Required<Pick<T, K>> & Partial<Pick<T, Exclude<keyof T, K>>>;
}[keyof T];
/**
 * Options common to {@link Map#jumpTo}, {@link Map#easeTo}, and {@link Map#flyTo}, controlling the desired location,
 * zoom, bearing, and pitch of the camera. All properties are optional, and when a property is omitted, the current
 * camera value for that property will remain unchanged.
 *
 * @example
 * Set the map's initial perspective with CameraOptions
 * ```ts
 * let map = new Map({
 *   container: 'map',
 *   style: 'https://demotiles.maplibre.org/style.json',
 *   center: [-73.5804, 45.53483],
 *   pitch: 60,
 *   bearing: -60,
 *   zoom: 10
 * });
 * ```
 * @see [Set pitch and bearing](https://maplibre.org/maplibre-gl-js/docs/examples/set-perspective/)
 * @see [Jump to a series of locations](https://maplibre.org/maplibre-gl-js/docs/examples/jump-to/)
 * @see [Fly to a location](https://maplibre.org/maplibre-gl-js/docs/examples/flyto/)
 * @see [Display buildings in 3D](https://maplibre.org/maplibre-gl-js/docs/examples/3d-buildings/)
 */
export type CameraOptions = CenterZoomBearing & {
	/**
	 * The desired pitch in degrees. The pitch is the angle towards the horizon
	 * measured in degrees with a range between 0 and 60 degrees. For example, pitch: 0 provides the appearance
	 * of looking straight down at the map, while pitch: 60 tilts the user's perspective towards the horizon.
	 * Increasing the pitch value is often used to display 3D objects.
	 */
	pitch?: number;
	/**
	 * If `zoom` is specified, `around` determines the point around which the zoom is centered.
	 */
	around?: LngLatLike;
};
/**
 * Holds center, zoom and bearing properties
 */
export type CenterZoomBearing = {
	/**
	 * The desired center.
	 */
	center?: LngLatLike;
	/**
	 * The desired zoom level.
	 */
	zoom?: number;
	/**
	 * The desired bearing in degrees. The bearing is the compass direction that
	 * is "up". For example, `bearing: 90` orients the map so that east is up.
	 */
	bearing?: number;
};
/**
 * The options object related to the {@link Map#jumpTo} method
 */
export type JumpToOptions = CameraOptions & {
	/**
	 * Dimensions in pixels applied on each side of the viewport for shifting the vanishing point.
	 */
	padding?: PaddingOptions;
};
/**
 * A options object for the {@link Map#cameraForBounds} method
 */
export type CameraForBoundsOptions = CameraOptions & {
	/**
	 * The amount of padding in pixels to add to the given bounds.
	 */
	padding?: number | RequireAtLeastOne<PaddingOptions>;
	/**
	 * The center of the given bounds relative to the map's center, measured in pixels.
	 * @defaultValue [0, 0]
	 */
	offset?: PointLike;
	/**
	 * The maximum zoom level to allow when the camera would transition to the specified bounds.
	 */
	maxZoom?: number;
};
/**
 * The {@link Map#flyTo} options object
 */
export type FlyToOptions = AnimationOptions & CameraOptions & {
	/**
	 * The zooming "curve" that will occur along the
	 * flight path. A high value maximizes zooming for an exaggerated animation, while a low
	 * value minimizes zooming for an effect closer to {@link Map#easeTo}. 1.42 is the average
	 * value selected by participants in the user study discussed in
	 * [van Wijk (2003)](https://www.win.tue.nl/~vanwijk/zoompan.pdf). A value of
	 * `Math.pow(6, 0.25)` would be equivalent to the root mean squared average velocity. A
	 * value of 1 would produce a circular motion.
	 * @defaultValue 1.42
	 */
	curve?: number;
	/**
	 * The zero-based zoom level at the peak of the flight path. If
	 * `options.curve` is specified, this option is ignored.
	 */
	minZoom?: number;
	/**
	 * The average speed of the animation defined in relation to
	 * `options.curve`. A speed of 1.2 means that the map appears to move along the flight path
	 * by 1.2 times `options.curve` screenfuls every second. A _screenful_ is the map's visible span.
	 * It does not correspond to a fixed physical distance, but varies by zoom level.
	 * @defaultValue 1.2
	 */
	speed?: number;
	/**
	 * The average speed of the animation measured in screenfuls
	 * per second, assuming a linear timing curve. If `options.speed` is specified, this option is ignored.
	 */
	screenSpeed?: number;
	/**
	 * The animation's maximum duration, measured in milliseconds.
	 * If duration exceeds maximum duration, it resets to 0.
	 */
	maxDuration?: number;
	/**
	 * The amount of padding in pixels to add to the given bounds.
	 */
	padding?: number | RequireAtLeastOne<PaddingOptions>;
};
export type EaseToOptions = AnimationOptions & CameraOptions & {
	delayEndEvents?: number;
	padding?: number | RequireAtLeastOne<PaddingOptions>;
};
/**
 * Options for {@link Map#fitBounds} method
 */
export type FitBoundsOptions = FlyToOptions & {
	/**
	 * If `true`, the map transitions using {@link Map#easeTo}. If `false`, the map transitions using {@link Map#flyTo}.
	 * See those functions and {@link AnimationOptions} for information about options available.
	 * @defaultValue false
	 */
	linear?: boolean;
	/**
	 * The center of the given bounds relative to the map's center, measured in pixels.
	 * @defaultValue [0, 0]
	 */
	offset?: PointLike;
	/**
	 * The maximum zoom level to allow when the map view transitions to the specified bounds.
	 */
	maxZoom?: number;
};
/**
 * Options common to map movement methods that involve animation, such as {@link Map#panBy} and
 * {@link Map#easeTo}, controlling the duration and easing function of the animation. All properties
 * are optional.
 *
 */
export type AnimationOptions = {
	/**
	 * The animation's duration, measured in milliseconds.
	 */
	duration?: number;
	/**
	 * A function taking a time in the range 0..1 and returning a number where 0 is
	 * the initial state and 1 is the final state.
	 */
	easing?: (_: number) => number;
	/**
	 * of the target center relative to real map container center at the end of animation.
	 */
	offset?: PointLike;
	/**
	 * If `false`, no animation will occur.
	 */
	animate?: boolean;
	/**
	 * If `true`, then the animation is considered essential and will not be affected by
	 * [`prefers-reduced-motion`](https://developer.mozilla.org/en-US/docs/Web/CSS/\@media/prefers-reduced-motion).
	 */
	essential?: boolean;
	/**
	 * Default false. Needed in 3D maps to let the camera stay in a constant
	 * height based on sea-level. After the animation finished the zoom-level will be recalculated in respect of
	 * the distance from the camera to the center-coordinate-altitude.
	 */
	freezeElevation?: boolean;
};
/**
 * A callback hook that allows manipulating the camera and being notified about camera updates before they happen
 */
export type CameraUpdateTransformFunction = (next: {
	center: LngLat;
	zoom: number;
	pitch: number;
	bearing: number;
	elevation: number;
}) => {
	center?: LngLat;
	zoom?: number;
	pitch?: number;
	bearing?: number;
	elevation?: number;
};
declare abstract class Camera extends Evented {
	transform: Transform;
	terrain: Terrain;
	handlers: HandlerManager;
	_moving: boolean;
	_zooming: boolean;
	_rotating: boolean;
	_pitching: boolean;
	_padding: boolean;
	_bearingSnap: number;
	_easeStart: number;
	_easeOptions: {
		duration?: number;
		easing?: (_: number) => number;
	};
	_easeId: string | void;
	_onEaseFrame: (_: number) => void;
	_onEaseEnd: (easeId?: string) => void;
	_easeFrameId: TaskID;
	/**
	 * @internal
	 * holds the geographical coordinate of the target
	 */
	_elevationCenter: LngLat;
	/**
	 * @internal
	 * holds the targ altitude value, = center elevation of the target.
	 * This value may changes during flight, because new terrain-tiles loads during flight.
	 */
	_elevationTarget: number;
	/**
	 * @internal
	 * holds the start altitude value, = center elevation before animation begins
	 * this value will recalculated during flight in respect of changing _elevationTarget values,
	 * so the linear interpolation between start and target keeps smooth and without jumps.
	 */
	_elevationStart: number;
	/**
	 * @internal
	 * Saves the current state of the elevation freeze - this is used during map movement to prevent "rocky" camera movement.
	 */
	_elevationFreeze: boolean;
	/**
	 * @internal
	 * Used to track accumulated changes during continuous interaction
	 */
	_requestedCameraState?: Transform;
	/**
	 * A callback used to defer camera updates or apply arbitrary constraints.
	 * If specified, this Camera instance can be used as a stateless component in React etc.
	 */
	transformCameraUpdate: CameraUpdateTransformFunction | null;
	abstract _requestRenderFrame(a: () => void): TaskID;
	abstract _cancelRenderFrame(_: TaskID): void;
	constructor(transform: Transform, options: {
		bearingSnap: number;
	});
	/**
	 * Returns the map's geographical centerpoint.
	 *
	 * @returns The map's geographical centerpoint.
	 * @example
	 * Return a LngLat object such as `{lng: 0, lat: 0}`
	 * ```ts
	 * let center = map.getCenter();
	 * // access longitude and latitude values directly
	 * let {lng, lat} = map.getCenter();
	 * ```
	 */
	getCenter(): LngLat;
	/**
	 * Sets the map's geographical centerpoint. Equivalent to `jumpTo({center: center})`.
	 *
	 * Triggers the following events: `movestart` and `moveend`.
	 *
	 * @param center - The centerpoint to set.
	 * @param eventData - Additional properties to be added to event objects of events triggered by this method.
	 * @example
	 * ```ts
	 * map.setCenter([-74, 38]);
	 * ```
	 */
	setCenter(center: LngLatLike, eventData?: any): this;
	/**
	 * Pans the map by the specified offset.
	 *
	 * Triggers the following events: `movestart` and `moveend`.
	 *
	 * @param offset - `x` and `y` coordinates by which to pan the map.
	 * @param options - Options object
	 * @param eventData - Additional properties to be added to event objects of events triggered by this method.
	 * @see [Navigate the map with game-like controls](https://maplibre.org/maplibre-gl-js/docs/examples/game-controls/)
	 */
	panBy(offset: PointLike, options?: AnimationOptions, eventData?: any): this;
	/**
	 * Pans the map to the specified location with an animated transition.
	 *
	 * Triggers the following events: `movestart` and `moveend`.
	 *
	 * @param lnglat - The location to pan the map to.
	 * @param options - Options describing the destination and animation of the transition.
	 * @param eventData - Additional properties to be added to event objects of events triggered by this method.
	 * @example
	 * ```ts
	 * map.panTo([-74, 38]);
	 * // Specify that the panTo animation should last 5000 milliseconds.
	 * map.panTo([-74, 38], {duration: 5000});
	 * ```
	 * @see [Update a feature in realtime](https://maplibre.org/maplibre-gl-js/docs/examples/live-update-feature/)
	 */
	panTo(lnglat: LngLatLike, options?: AnimationOptions, eventData?: any): this;
	/**
	 * Returns the map's current zoom level.
	 *
	 * @returns The map's current zoom level.
	 * @example
	 * ```ts
	 * map.getZoom();
	 * ```
	 */
	getZoom(): number;
	/**
	 * Sets the map's zoom level. Equivalent to `jumpTo({zoom: zoom})`.
	 *
	 * Triggers the following events: `movestart`, `move`, `moveend`, `zoomstart`, `zoom`, and `zoomend`.
	 *
	 * @param zoom - The zoom level to set (0-20).
	 * @param eventData - Additional properties to be added to event objects of events triggered by this method.
	 * @example
	 * Zoom to the zoom level 5 without an animated transition
	 * ```ts
	 * map.setZoom(5);
	 * ```
	 */
	setZoom(zoom: number, eventData?: any): this;
	/**
	 * Zooms the map to the specified zoom level, with an animated transition.
	 *
	 * Triggers the following events: `movestart`, `move`, `moveend`, `zoomstart`, `zoom`, and `zoomend`.
	 *
	 * @param zoom - The zoom level to transition to.
	 * @param options - Options object
	 * @param eventData - Additional properties to be added to event objects of events triggered by this method.
	 * @example
	 * ```ts
	 * // Zoom to the zoom level 5 without an animated transition
	 * map.zoomTo(5);
	 * // Zoom to the zoom level 8 with an animated transition
	 * map.zoomTo(8, {
	 *   duration: 2000,
	 *   offset: [100, 50]
	 * });
	 * ```
	 */
	zoomTo(zoom: number, options?: AnimationOptions | null, eventData?: any): this;
	/**
	 * Increases the map's zoom level by 1.
	 *
	 * Triggers the following events: `movestart`, `move`, `moveend`, `zoomstart`, `zoom`, and `zoomend`.
	 *
	 * @param options - Options object
	 * @param eventData - Additional properties to be added to event objects of events triggered by this method.
	 * @example
	 * Zoom the map in one level with a custom animation duration
	 * ```ts
	 * map.zoomIn({duration: 1000});
	 * ```
	 */
	zoomIn(options?: AnimationOptions, eventData?: any): this;
	/**
	 * Decreases the map's zoom level by 1.
	 *
	 * Triggers the following events: `movestart`, `move`, `moveend`, `zoomstart`, `zoom`, and `zoomend`.
	 *
	 * @param options - Options object
	 * @param eventData - Additional properties to be added to event objects of events triggered by this method.
	 * @example
	 * Zoom the map out one level with a custom animation offset
	 * ```ts
	 * map.zoomOut({offset: [80, 60]});
	 * ```
	 */
	zoomOut(options?: AnimationOptions, eventData?: any): this;
	/**
	 * Returns the map's current bearing. The bearing is the compass direction that is "up"; for example, a bearing
	 * of 90° orients the map so that east is up.
	 *
	 * @returns The map's current bearing.
	 * @see [Navigate the map with game-like controls](https://maplibre.org/maplibre-gl-js/docs/examples/game-controls/)
	 */
	getBearing(): number;
	/**
	 * Sets the map's bearing (rotation). The bearing is the compass direction that is "up"; for example, a bearing
	 * of 90° orients the map so that east is up.
	 *
	 * Equivalent to `jumpTo({bearing: bearing})`.
	 *
	 * Triggers the following events: `movestart`, `moveend`, and `rotate`.
	 *
	 * @param bearing - The desired bearing.
	 * @param eventData - Additional properties to be added to event objects of events triggered by this method.
	 * @example
	 * Rotate the map to 90 degrees
	 * ```ts
	 * map.setBearing(90);
	 * ```
	 */
	setBearing(bearing: number, eventData?: any): this;
	/**
	 * Returns the current padding applied around the map viewport.
	 *
	 * @returns The current padding around the map viewport.
	 */
	getPadding(): PaddingOptions;
	/**
	 * Sets the padding in pixels around the viewport.
	 *
	 * Equivalent to `jumpTo({padding: padding})`.
	 *
	 * Triggers the following events: `movestart` and `moveend`.
	 *
	 * @param padding - The desired padding.
	 * @param eventData - Additional properties to be added to event objects of events triggered by this method.
	 * @example
	 * Sets a left padding of 300px, and a top padding of 50px
	 * ```ts
	 * map.setPadding({ left: 300, top: 50 });
	 * ```
	 */
	setPadding(padding: PaddingOptions, eventData?: any): this;
	/**
	 * Rotates the map to the specified bearing, with an animated transition. The bearing is the compass direction
	 * that is "up"; for example, a bearing of 90° orients the map so that east is up.
	 *
	 * Triggers the following events: `movestart`, `moveend`, and `rotate`.
	 *
	 * @param bearing - The desired bearing.
	 * @param options - Options object
	 * @param eventData - Additional properties to be added to event objects of events triggered by this method.
	 */
	rotateTo(bearing: number, options?: AnimationOptions, eventData?: any): this;
	/**
	 * Rotates the map so that north is up (0° bearing), with an animated transition.
	 *
	 * Triggers the following events: `movestart`, `moveend`, and `rotate`.
	 *
	 * @param options - Options object
	 * @param eventData - Additional properties to be added to event objects of events triggered by this method.
	 */
	resetNorth(options?: AnimationOptions, eventData?: any): this;
	/**
	 * Rotates and pitches the map so that north is up (0° bearing) and pitch is 0°, with an animated transition.
	 *
	 * Triggers the following events: `movestart`, `move`, `moveend`, `pitchstart`, `pitch`, `pitchend`, and `rotate`.
	 *
	 * @param options - Options object
	 * @param eventData - Additional properties to be added to event objects of events triggered by this method.
	 */
	resetNorthPitch(options?: AnimationOptions, eventData?: any): this;
	/**
	 * Snaps the map so that north is up (0° bearing), if the current bearing is close enough to it (i.e. within the
	 * `bearingSnap` threshold).
	 *
	 * Triggers the following events: `movestart`, `moveend`, and `rotate`.
	 *
	 * @param options - Options object
	 * @param eventData - Additional properties to be added to event objects of events triggered by this method.
	 */
	snapToNorth(options?: AnimationOptions, eventData?: any): this;
	/**
	 * Returns the map's current pitch (tilt).
	 *
	 * @returns The map's current pitch, measured in degrees away from the plane of the screen.
	 */
	getPitch(): number;
	/**
	 * Sets the map's pitch (tilt). Equivalent to `jumpTo({pitch: pitch})`.
	 *
	 * Triggers the following events: `movestart`, `moveend`, `pitchstart`, and `pitchend`.
	 *
	 * @param pitch - The pitch to set, measured in degrees away from the plane of the screen (0-60).
	 * @param eventData - Additional properties to be added to event objects of events triggered by this method.
	 */
	setPitch(pitch: number, eventData?: any): this;
	/**
	 * @param bounds - Calculate the center for these bounds in the viewport and use
	 * the highest zoom level up to and including `Map#getMaxZoom()` that fits
	 * in the viewport. LngLatBounds represent a box that is always axis-aligned with bearing 0.
	 * Bounds will be taken in [sw, ne] order. Southwest point will always be to the left of the northeast point.
	 * @param options - Options object
	 * @returns If map is able to fit to provided bounds, returns `center`, `zoom`, and `bearing`.
	 * If map is unable to fit, method will warn and return undefined.
	 * @example
	 * ```ts
	 * let bbox = [[-79, 43], [-73, 45]];
	 * let newCameraTransform = map.cameraForBounds(bbox, {
	 *   padding: {top: 10, bottom:25, left: 15, right: 5}
	 * });
	 * ```
	 */
	cameraForBounds(bounds: LngLatBoundsLike, options?: CameraForBoundsOptions): CenterZoomBearing | undefined;
	/**
	 * @internal
	 * Calculate the center of these two points in the viewport and use
	 * the highest zoom level up to and including `Map#getMaxZoom()` that fits
	 * the points in the viewport at the specified bearing.
	 * @param p0 - First point
	 * @param p1 - Second point
	 * @param bearing - Desired map bearing at end of animation, in degrees
	 * @param options - the camera options
	 * @returns If map is able to fit to provided bounds, returns `center`, `zoom`, and `bearing`.
	 *      If map is unable to fit, method will warn and return undefined.
	 * @example
	 * ```ts
	 * let p0 = [-79, 43];
	 * let p1 = [-73, 45];
	 * let bearing = 90;
	 * let newCameraTransform = map._cameraForBoxAndBearing(p0, p1, bearing, {
	 *   padding: {top: 10, bottom:25, left: 15, right: 5}
	 * });
	 * ```
	 */
	_cameraForBoxAndBearing(p0: LngLatLike, p1: LngLatLike, bearing: number, options?: CameraForBoundsOptions): CenterZoomBearing | undefined;
	/**
	 * Pans and zooms the map to contain its visible area within the specified geographical bounds.
	 * This function will also reset the map's bearing to 0 if bearing is nonzero.
	 *
	 * Triggers the following events: `movestart` and `moveend`.
	 *
	 * @param bounds - Center these bounds in the viewport and use the highest
	 * zoom level up to and including `Map#getMaxZoom()` that fits them in the viewport.
	 * Bounds will be taken in [sw, ne] order. Southwest point will always be to the left of the northeast point.
	 * @param options - Options supports all properties from {@link AnimationOptions} and {@link CameraOptions} in addition to the fields below.
	 * @param eventData - Additional properties to be added to event objects of events triggered by this method.
	 * @example
	 * ```ts
	 * let bbox = [[-79, 43], [-73, 45]];
	 * map.fitBounds(bbox, {
	 *   padding: {top: 10, bottom:25, left: 15, right: 5}
	 * });
	 * ```
	 * @see [Fit a map to a bounding box](https://maplibre.org/maplibre-gl-js/docs/examples/fitbounds/)
	 */
	fitBounds(bounds: LngLatBoundsLike, options?: FitBoundsOptions, eventData?: any): this;
	/**
	 * Pans, rotates and zooms the map to to fit the box made by points p0 and p1
	 * once the map is rotated to the specified bearing. To zoom without rotating,
	 * pass in the current map bearing.
	 *
	 * Triggers the following events: `movestart`, `move`, `moveend`, `zoomstart`, `zoom`, `zoomend` and `rotate`.
	 *
	 * @param p0 - First point on screen, in pixel coordinates
	 * @param p1 - Second point on screen, in pixel coordinates
	 * @param bearing - Desired map bearing at end of animation, in degrees
	 * @param options - Options object
	 * @param eventData - Additional properties to be added to event objects of events triggered by this method.
	 * @example
	 * ```ts
	 * let p0 = [220, 400];
	 * let p1 = [500, 900];
	 * map.fitScreenCoordinates(p0, p1, map.getBearing(), {
	 *   padding: {top: 10, bottom:25, left: 15, right: 5}
	 * });
	 * ```
	 * @see Used by {@link BoxZoomHandler}
	 */
	fitScreenCoordinates(p0: PointLike, p1: PointLike, bearing: number, options?: FitBoundsOptions, eventData?: any): this;
	_fitInternal(calculatedOptions?: CenterZoomBearing, options?: FitBoundsOptions, eventData?: any): this;
	/**
	 * Changes any combination of center, zoom, bearing, and pitch, without
	 * an animated transition. The map will retain its current values for any
	 * details not specified in `options`.
	 *
	 * Triggers the following events: `movestart`, `move`, `moveend`, `zoomstart`, `zoom`, `zoomend`, `pitchstart`,
	 * `pitch`, `pitchend`, and `rotate`.
	 *
	 * @param options - Options object
	 * @param eventData - Additional properties to be added to event objects of events triggered by this method.
	 * @example
	 * ```ts
	 * // jump to coordinates at current zoom
	 * map.jumpTo({center: [0, 0]});
	 * // jump with zoom, pitch, and bearing options
	 * map.jumpTo({
	 *   center: [0, 0],
	 *   zoom: 8,
	 *   pitch: 45,
	 *   bearing: 90
	 * });
	 * ```
	 * @see [Jump to a series of locations](https://maplibre.org/maplibre-gl-js/docs/examples/jump-to/)
	 * @see [Update a feature in realtime](https://maplibre.org/maplibre-gl-js/docs/examples/live-update-feature/)
	 */
	jumpTo(options: JumpToOptions, eventData?: any): this;
	/**
	 * Calculates pitch, zoom and bearing for looking at `newCenter` with the camera position being `newCenter`
	 * and returns them as {@link CameraOptions}.
	 * @param from - The camera to look from
	 * @param altitudeFrom - The altitude of the camera to look from
	 * @param to - The center to look at
	 * @param altitudeTo - Optional altitude of the center to look at. If none given the ground height will be used.
	 * @returns the calculated camera options
	 */
	calculateCameraOptionsFromTo(from: LngLat, altitudeFrom: number, to: LngLat, altitudeTo?: number): CameraOptions;
	/**
	 * Changes any combination of `center`, `zoom`, `bearing`, `pitch`, and `padding` with an animated transition
	 * between old and new values. The map will retain its current values for any
	 * details not specified in `options`.
	 *
	 * Note: The transition will happen instantly if the user has enabled
	 * the `reduced motion` accessibility feature enabled in their operating system,
	 * unless `options` includes `essential: true`.
	 *
	 * Triggers the following events: `movestart`, `move`, `moveend`, `zoomstart`, `zoom`, `zoomend`, `pitchstart`,
	 * `pitch`, `pitchend`, and `rotate`.
	 *
	 * @param options - Options describing the destination and animation of the transition.
	 * Accepts {@link CameraOptions} and {@link AnimationOptions}.
	 * @param eventData - Additional properties to be added to event objects of events triggered by this method.
	 * @see [Navigate the map with game-like controls](https://maplibre.org/maplibre-gl-js/docs/examples/game-controls/)
	 */
	easeTo(options: EaseToOptions & {
		easeId?: string;
		noMoveStart?: boolean;
	}, eventData?: any): this;
	_prepareEase(eventData: any, noMoveStart: boolean, currently?: any): void;
	_prepareElevation(center: LngLat): void;
	_updateElevation(k: number): void;
	_finalizeElevation(): void;
	/**
	 * @internal
	 * Called when the camera is about to be manipulated.
	 * If `transformCameraUpdate` is specified or terrain is enabled, a copy of
	 * the current transform is created to track the accumulated changes.
	 * This underlying transform represents the "desired state" proposed by input handlers / animations / UI controls.
	 * It may differ from the state used for rendering (`this.transform`).
	 * @returns Transform to apply changes to
	 */
	_getTransformForUpdate(): Transform;
	/**
	 * @internal
	 * Checks the given transform for the camera being below terrain surface and
	 * returns new pitch and zoom to fix that.
	 *
	 * With the new pitch and zoom, the camera will be at the same ground
	 * position but at higher altitude. It will still point to the same spot on
	 * the map.
	 *
	 * @param tr - The transform to check.
	 */
	_elevateCameraIfInsideTerrain(tr: Transform): {
		pitch?: number;
		zoom?: number;
	};
	/**
	 * @internal
	 * Called after the camera is done being manipulated.
	 * @param tr - the requested camera end state
	 * If the camera is inside terrain, it gets elevated.
	 * Call `transformCameraUpdate` if present, and then apply the "approved" changes.
	 */
	_applyUpdatedTransform(tr: Transform): void;
	_fireMoveEvents(eventData?: any): void;
	_afterEase(eventData?: any, easeId?: string): void;
	/**
	 * Changes any combination of center, zoom, bearing, and pitch, animating the transition along a curve that
	 * evokes flight. The animation seamlessly incorporates zooming and panning to help
	 * the user maintain her bearings even after traversing a great distance.
	 *
	 * Note: The animation will be skipped, and this will behave equivalently to `jumpTo`
	 * if the user has the `reduced motion` accessibility feature enabled in their operating system,
	 * unless 'options' includes `essential: true`.
	 *
	 * Triggers the following events: `movestart`, `move`, `moveend`, `zoomstart`, `zoom`, `zoomend`, `pitchstart`,
	 * `pitch`, `pitchend`, and `rotate`.
	 *
	 * @param options - Options describing the destination and animation of the transition.
	 * Accepts {@link CameraOptions}, {@link AnimationOptions},
	 * and the following additional options.
	 * @param eventData - Additional properties to be added to event objects of events triggered by this method.
	 * @example
	 * ```ts
	 * // fly with default options to null island
	 * map.flyTo({center: [0, 0], zoom: 9});
	 * // using flyTo options
	 * map.flyTo({
	 *   center: [0, 0],
	 *   zoom: 9,
	 *   speed: 0.2,
	 *   curve: 1,
	 *   easing(t) {
	 *     return t;
	 *   }
	 * });
	 * ```
	 * @see [Fly to a location](https://maplibre.org/maplibre-gl-js/docs/examples/flyto/)
	 * @see [Slowly fly to a location](https://maplibre.org/maplibre-gl-js/docs/examples/flyto-options/)
	 * @see [Fly to a location based on scroll position](https://maplibre.org/maplibre-gl-js/docs/examples/scroll-fly-to/)
	 */
	flyTo(options: FlyToOptions, eventData?: any): this;
	isEasing(): boolean;
	/**
	 * Stops any animated transition underway.
	 */
	stop(): this;
	_stop(allowGestures?: boolean, easeId?: string): this;
	_ease(frame: (_: number) => void, finish: () => void, options: {
		animate?: boolean;
		duration?: number;
		easing?: (_: number) => number;
	}): void;
	_renderFrameCallback: () => void;
	_normalizeBearing(bearing: number, currentBearing: number): number;
	_normalizeCenter(center: LngLat, tr: Transform): void;
	/**
	 * Get the elevation difference between a given point
	 * and a point that is currently in the middle of the screen.
	 * This method should be used for proper positioning of custom 3d objects, as explained [here](https://maplibre.org/maplibre-gl-js/docs/examples/add-3d-model-with-terrain/)
	 * Returns null if terrain is not enabled.
	 * This method is subject to change in Maplibre GL JS v5.
	 * @param lngLatLike - [x,y] or LngLat coordinates of the location
	 * @returns elevation offset in meters
	 */
	queryTerrainElevation(lngLatLike: LngLatLike): number | null;
}
/**
 * A position defintion for the control to be placed, can be in one of the corners of the map.
 * When two or more controls are places in the same location they are stacked toward the center of the map.
 */
export type ControlPosition = "top-left" | "top-right" | "bottom-left" | "bottom-right";
/**
 * Interface for interactive controls added to the map. This is a
 * specification for implementers to model: it is not
 * an exported method or class.
 *
 * Controls must implement `onAdd` and `onRemove`, and must own an
 * element, which is often a `div` element. To use MapLibre GL JS's
 * default control styling, add the `maplibregl-ctrl` class to your control's
 * node.
 *
 * @example
 * ```ts
 * class HelloWorldControl: IControl {
 *     onAdd(map) {
 *         this._map = map;
 *         this._container = document.createElement('div');
 *         this._container.className = 'maplibregl-ctrl';
 *         this._container.textContent = 'Hello, world';
 *         return this._container;
 *     }
 *
 *     onRemove() {
 *         this._container.parentNode.removeChild(this._container);
 *         this._map = undefined;
 *     }
 * }
 * ```
 */
export interface IControl {
	/**
	 * Register a control on the map and give it a chance to register event listeners
	 * and resources. This method is called by {@link Map#addControl}
	 * internally.
	 *
	 * @param map - the Map this control will be added to
	 * @returns The control's container element. This should
	 * be created by the control and returned by onAdd without being attached
	 * to the DOM: the map will insert the control's element into the DOM
	 * as necessary.
	 */
	onAdd(map: Map$1): HTMLElement;
	/**
	 * Unregister a control on the map and give it a chance to detach event listeners
	 * and resources. This method is called by {@link Map#removeControl}
	 * internally.
	 *
	 * @param map - the Map this control will be removed from
	 */
	onRemove(map: Map$1): void;
	/**
	 * Optionally provide a default position for this control. If this method
	 * is implemented and {@link Map#addControl} is called without the `position`
	 * parameter, the value returned by getDefaultPosition will be used as the
	 * control's position.
	 *
	 * @returns a control position, one of the values valid in addControl.
	 */
	readonly getDefaultPosition?: () => ControlPosition;
}
/**
 * An event from the mouse relevant to a specific layer.
 *
 * @group Event Related
 */
export type MapLayerMouseEvent = MapMouseEvent & {
	features?: MapGeoJSONFeature[];
};
/**
 * An event from a touch device relevant to a specific layer.
 *
 * @group Event Related
 */
export type MapLayerTouchEvent = MapTouchEvent & {
	features?: MapGeoJSONFeature[];
};
/**
 * The source event data type
 */
export type MapSourceDataType = "content" | "metadata" | "visibility" | "idle";
/**
 * `MapLayerEventType` - a mapping between the event name and the event.
 * **Note:** These events are compatible with the optional `layerId` parameter.
 * If `layerId` is included as the second argument in {@link Map#on}, the event listener will fire only when the
 * event action contains a visible portion of the specified layer.
 * The following example can be used for all the events.
 *
 * @group Event Related
 * @example
 * ```ts
 * // Initialize the map
 * let map = new Map({ // map options });
 * // Set an event listener for a specific layer
 * map.on('the-event-name', 'poi-label', (e) => {
 *   console.log('An event has occurred on a visible portion of the poi-label layer');
 * });
 * ```
 */
export type MapLayerEventType = {
	/**
	 * Fired when a pointing device (usually a mouse) is pressed and released contains a visible portion of the specified layer.
	 *
	 * @see [Measure distances](https://maplibre.org/maplibre-gl-js/docs/examples/measure/)
	 * @see [Center the map on a clicked symbol](https://maplibre.org/maplibre-gl-js/docs/examples/center-on-symbol/)
	 */
	click: MapLayerMouseEvent;
	/**
	 * Fired when a pointing device (usually a mouse) is pressed and released twice contains a visible portion of the specified layer.
	 *
	 * **Note:** Under normal conditions, this event will be preceded by two `click` events.
	 */
	dblclick: MapLayerMouseEvent;
	/**
	 * Fired when a pointing device (usually a mouse) is pressed while inside a visible portion of the specified layer.
	 * @see [Create a draggable point](https://maplibre.org/maplibre-gl-js/docs/examples/drag-a-point/)
	 */
	mousedown: MapLayerMouseEvent;
	/**
	 * Fired when a pointing device (usually a mouse) is released while inside a visible portion of the specified layer.
	 * @see [Create a draggable point](https://maplibre.org/maplibre-gl-js/docs/examples/drag-a-point/)
	 */
	mouseup: MapLayerMouseEvent;
	/**
	 * Fired when a pointing device (usually a mouse) is moved while the cursor is inside a visible portion of the specified layer.
	 * As you move the cursor across the layer, the event will fire every time the cursor changes position within that layer.
	 *
	 * @see [Get coordinates of the mouse pointer](https://maplibre.org/maplibre-gl-js/docs/examples/mouse-position/)
	 * @see [Highlight features under the mouse pointer](https://maplibre.org/maplibre-gl-js/docs/examples/hover-styles/)
	 * @see [Display a popup on over](https://maplibre.org/maplibre-gl-js/docs/examples/popup-on-hover/)
	 */
	mousemove: MapLayerMouseEvent;
	/**
	 * Fired when a pointing device (usually a mouse) enters a visible portion of a specified layer from
	 * outside that layer or outside the map canvas.
	 *
	 * @see [Center the map on a clicked symbol](https://maplibre.org/maplibre-gl-js/docs/examples/center-on-symbol/)
	 * @see [Display a popup on click](https://maplibre.org/maplibre-gl-js/docs/examples/popup-on-click/)
	 */
	mouseenter: MapLayerMouseEvent;
	/**
	 * Fired when a pointing device (usually a mouse) leaves a visible portion of a specified layer, or leaves
	 * the map canvas.
	 *
	 * @see [Highlight features under the mouse pointer](https://maplibre.org/maplibre-gl-js/docs/examples/hover-styles/)
	 * @see [Display a popup on click](https://maplibre.org/maplibre-gl-js/docs/examples/popup-on-click/)
	 */
	mouseleave: MapLayerMouseEvent;
	/**
	 * Fired when a pointing device (usually a mouse) is moved inside a visible portion of the specified layer.
	 *
	 * @see [Get coordinates of the mouse pointer](https://maplibre.org/maplibre-gl-js/docs/examples/mouse-position/)
	 * @see [Highlight features under the mouse pointer](https://maplibre.org/maplibre-gl-js/docs/examples/hover-styles/)
	 * @see [Display a popup on hover](https://maplibre.org/maplibre-gl-js/docs/examples/popup-on-hover/)
	 */
	mouseover: MapLayerMouseEvent;
	/**
	 * Fired when a point device (usually a mouse) leaves the visible portion of the specified layer.
	 */
	mouseout: MapLayerMouseEvent;
	/**
	 * Fired when the right button of the mouse is clicked or the context menu key is pressed within visible portion of the specified layer.
	 */
	contextmenu: MapLayerMouseEvent;
	/**
	 * Fired when a [`touchstart`](https://developer.mozilla.org/en-US/docs/Web/Events/touchstart) event occurs within the visible portion of the specified layer.
	 * @see [Create a draggable point](https://maplibre.org/maplibre-gl-js/docs/examples/drag-a-point/)
	 */
	touchstart: MapLayerTouchEvent;
	/**
	 * Fired when a [`touchend`](https://developer.mozilla.org/en-US/docs/Web/Events/touchend) event occurs within the visible portion of the specified layer.
	 * @see [Create a draggable point](https://maplibre.org/maplibre-gl-js/docs/examples/drag-a-point/)
	 */
	touchend: MapLayerTouchEvent;
	/**
	 * Fired when a [`touchstart`](https://developer.mozilla.org/en-US/docs/Web/Events/touchstart) event occurs within the visible portion of the specified layer.
	 * @see [Create a draggable point](https://maplibre.org/maplibre-gl-js/docs/examples/drag-a-point/)
	 */
	touchcancel: MapLayerTouchEvent;
};
/**
 * `MapEventType` - a mapping between the event name and the event value.
 * These events are used with the {@link Map#on} method.
 * When using a `layerId` with {@link Map#on} method, please refer to {@link MapLayerEventType}.
 * The following example can be used for all the events.
 *
 * @group Event Related
 * @example
 * ```ts
 * // Initialize the map
 * let map = new Map({ // map options });
 * // Set an event listener
 * map.on('the-event-name', () => {
 *   console.log('An event has occurred!');
 * });
 * ```
 */
export type MapEventType = {
	/**
	 * Fired when an error occurs. This is GL JS's primary error reporting
	 * mechanism. We use an event instead of `throw` to better accommodate
	 * asynchronous operations. If no listeners are bound to the `error` event, the
	 * error will be printed to the console.
	 */
	error: ErrorEvent;
	/**
	 * Fired immediately after all necessary resources have been downloaded
	 * and the first visually complete rendering of the map has occurred.
	 *
	 * @see [Draw GeoJSON points](https://maplibre.org/maplibre-gl-js/docs/examples/geojson-markers/)
	 * @see [Add live realtime data](https://maplibre.org/maplibre-gl-js/docs/examples/live-geojson/)
	 * @see [Animate a point](https://maplibre.org/maplibre-gl-js/docs/examples/animate-point-along-line/)
	 */
	load: MapLibreEvent;
	/**
	 * Fired after the last frame rendered before the map enters an
	 * "idle" state:
	 *
	 * - No camera transitions are in progress
	 * - All currently requested tiles have loaded
	 * - All fade/transition animations have completed
	 */
	idle: MapLibreEvent;
	/**
	 * Fired immediately after the map has been removed with {@link Map#remove}.
	 */
	remove: MapLibreEvent;
	/**
	 * Fired whenever the map is drawn to the screen, as the result of
	 *
	 * - a change to the map's position, zoom, pitch, or bearing
	 * - a change to the map's style
	 * - a change to a GeoJSON source
	 * - the loading of a vector tile, GeoJSON file, glyph, or sprite
	 */
	render: MapLibreEvent;
	/**
	 * Fired immediately after the map has been resized.
	 */
	resize: MapLibreEvent;
	/**
	 * Fired when the WebGL context is lost.
	 */
	webglcontextlost: MapContextEvent;
	/**
	 * Fired when the WebGL context is restored.
	 */
	webglcontextrestored: MapContextEvent;
	/**
	 * Fired when any map data (style, source, tile, etc) begins loading or
	 * changing asynchronously. All `dataloading` events are followed by a `data`,
	 * `dataabort` or `error` event.
	 */
	dataloading: MapDataEvent;
	/**
	 * Fired when any map data loads or changes. See {@link MapDataEvent} for more information.
	 * @see [Display HTML clusters with custom properties](https://maplibre.org/maplibre-gl-js/docs/examples/cluster-html/)
	 */
	data: MapDataEvent;
	tiledataloading: MapDataEvent;
	/**
	 * Fired when one of the map's sources begins loading or changing asynchronously.
	 * All `sourcedataloading` events are followed by a `sourcedata`, `sourcedataabort` or `error` event.
	 */
	sourcedataloading: MapSourceDataEvent;
	/**
	 * Fired when the map's style begins loading or changing asynchronously.
	 * All `styledataloading` events are followed by a `styledata`
	 * or `error` event.
	 */
	styledataloading: MapStyleDataEvent;
	/**
	 * Fired when one of the map's sources loads or changes, including if a tile belonging
	 * to a source loads or changes.
	 */
	sourcedata: MapSourceDataEvent;
	/**
	 * Fired when the map's style loads or changes.
	 */
	styledata: MapStyleDataEvent;
	/**
	 * Fired when an icon or pattern needed by the style is missing. The missing image can
	 * be added with {@link Map#addImage} within this event listener callback to prevent the image from
	 * being skipped. This event can be used to dynamically generate icons and patterns.
	 * @see [Generate and add a missing icon to the map](https://maplibre.org/maplibre-gl-js/docs/examples/add-image-missing-generated/)
	 */
	styleimagemissing: MapStyleImageMissingEvent;
	/**
	 * Fired when a request for one of the map's sources' tiles or data is aborted.
	 */
	dataabort: MapDataEvent;
	/**
	 * Fired when a request for one of the map's sources' data is aborted.
	 */
	sourcedataabort: MapSourceDataEvent;
	/**
	 * Fired when the user cancels a "box zoom" interaction, or when the bounding box does not meet the minimum size threshold.
	 * See {@link BoxZoomHandler}.
	 */
	boxzoomcancel: MapLibreZoomEvent;
	/**
	 * Fired when a "box zoom" interaction starts. See {@link BoxZoomHandler}.
	 */
	boxzoomstart: MapLibreZoomEvent;
	/**
	 * Fired when a "box zoom" interaction ends.  See {@link BoxZoomHandler}.
	 */
	boxzoomend: MapLibreZoomEvent;
	/**
	 * Fired when a [`touchcancel`](https://developer.mozilla.org/en-US/docs/Web/Events/touchcancel) event occurs within the map.
	 */
	touchcancel: MapTouchEvent;
	/**
	 * Fired when a [`touchmove`](https://developer.mozilla.org/en-US/docs/Web/Events/touchmove) event occurs within the map.
	 * @see [Create a draggable point](https://maplibre.org/maplibre-gl-js/docs/examples/drag-a-point/)
	 */
	touchmove: MapTouchEvent;
	/**
	 * Fired when a [`touchend`](https://developer.mozilla.org/en-US/docs/Web/Events/touchend) event occurs within the map.
	 * @see [Create a draggable point](https://maplibre.org/maplibre-gl-js/docs/examples/drag-a-point/)
	 */
	touchend: MapTouchEvent;
	/**
	 * Fired when a [`touchstart`](https://developer.mozilla.org/en-US/docs/Web/Events/touchstart) event occurs within the map.
	 * @see [Create a draggable point](https://maplibre.org/maplibre-gl-js/docs/examples/drag-a-point/)
	 */
	touchstart: MapTouchEvent;
	/**
	 * Fired when a pointing device (usually a mouse) is pressed and released at the same point on the map.
	 *
	 * @see [Measure distances](https://maplibre.org/maplibre-gl-js/docs/examples/measure/)
	 * @see [Center the map on a clicked symbol](https://maplibre.org/maplibre-gl-js/docs/examples/center-on-symbol/)
	 */
	click: MapMouseEvent;
	/**
	 * Fired when the right button of the mouse is clicked or the context menu key is pressed within the map.
	 */
	contextmenu: MapMouseEvent;
	/**
	 * Fired when a pointing device (usually a mouse) is pressed and released twice at the same point on the map in rapid succession.
	 *
	 * **Note:** Under normal conditions, this event will be preceded by two `click` events.
	 */
	dblclick: MapMouseEvent;
	/**
	 * Fired when a pointing device (usually a mouse) is moved while the cursor is inside the map.
	 * As you move the cursor across the map, the event will fire every time the cursor changes position within the map.
	 *
	 * @see [Get coordinates of the mouse pointer](https://maplibre.org/maplibre-gl-js/docs/examples/mouse-position/)
	 * @see [Highlight features under the mouse pointer](https://maplibre.org/maplibre-gl-js/docs/examples/hover-styles/)
	 * @see [Display a popup on over](https://maplibre.org/maplibre-gl-js/docs/examples/popup-on-hover/)
	 */
	mousemove: MapMouseEvent;
	/**
	 * Fired when a pointing device (usually a mouse) is released within the map.
	 *
	 * @see [Create a draggable point](https://maplibre.org/maplibre-gl-js/docs/examples/drag-a-point/)
	 */
	mouseup: MapMouseEvent;
	/**
	 * Fired when a pointing device (usually a mouse) is pressed within the map.
	 *
	 * @see [Create a draggable point](https://maplibre.org/maplibre-gl-js/docs/examples/drag-a-point/)
	 */
	mousedown: MapMouseEvent;
	/**
	 * Fired when a point device (usually a mouse) leaves the map's canvas.
	 */
	mouseout: MapMouseEvent;
	/**
	 * Fired when a pointing device (usually a mouse) is moved within the map.
	 * As you move the cursor across a web page containing a map,
	 * the event will fire each time it enters the map or any child elements.
	 *
	 * @see [Get coordinates of the mouse pointer](https://maplibre.org/maplibre-gl-js/docs/examples/mouse-position/)
	 * @see [Highlight features under the mouse pointer](https://maplibre.org/maplibre-gl-js/docs/examples/hover-styles/)
	 * @see [Display a popup on hover](https://maplibre.org/maplibre-gl-js/docs/examples/popup-on-hover/)
	 */
	mouseover: MapMouseEvent;
	/**
	 * Fired just before the map begins a transition from one
	 * view to another, as the result of either user interaction or methods such as {@link Map#jumpTo}.
	 *
	 */
	movestart: MapLibreEvent<MouseEvent | TouchEvent | WheelEvent | undefined>;
	/**
	 * Fired repeatedly during an animated transition from one view to
	 * another, as the result of either user interaction or methods such as {@link Map#flyTo}.
	 *
	 * @see [Display HTML clusters with custom properties](https://maplibre.org/maplibre-gl-js/docs/examples/cluster-html/)
	 */
	move: MapLibreEvent<MouseEvent | TouchEvent | WheelEvent | undefined>;
	/**
	 * Fired just after the map completes a transition from one
	 * view to another, as the result of either user interaction or methods such as {@link Map#jumpTo}.
	 *
	 * @see [Display HTML clusters with custom properties](https://maplibre.org/maplibre-gl-js/docs/examples/cluster-html/)
	 */
	moveend: MapLibreEvent<MouseEvent | TouchEvent | WheelEvent | undefined>;
	/**
	 * Fired just before the map begins a transition from one zoom level to another,
	 * as the result of either user interaction or methods such as {@link Map#flyTo}.
	 */
	zoomstart: MapLibreEvent<MouseEvent | TouchEvent | WheelEvent | undefined>;
	/**
	 * Fired repeatedly during an animated transition from one zoom level to another,
	 * as the result of either user interaction or methods such as {@link Map#flyTo}.
	 */
	zoom: MapLibreEvent<MouseEvent | TouchEvent | WheelEvent | undefined>;
	/**
	 * Fired just after the map completes a transition from one zoom level to another,
	 * as the result of either user interaction or methods such as {@link Map#flyTo}.
	 */
	zoomend: MapLibreEvent<MouseEvent | TouchEvent | WheelEvent | undefined>;
	/**
	 * Fired when a "drag to rotate" interaction starts. See {@link DragRotateHandler}.
	 */
	rotatestart: MapLibreEvent<MouseEvent | TouchEvent | undefined>;
	/**
	 * Fired repeatedly during a "drag to rotate" interaction. See {@link DragRotateHandler}.
	 */
	rotate: MapLibreEvent<MouseEvent | TouchEvent | undefined>;
	/**
	 * Fired when a "drag to rotate" interaction ends. See {@link DragRotateHandler}.
	 */
	rotateend: MapLibreEvent<MouseEvent | TouchEvent | undefined>;
	/**
	 * Fired when a "drag to pan" interaction starts. See {@link DragPanHandler}.
	 */
	dragstart: MapLibreEvent<MouseEvent | TouchEvent | undefined>;
	/**
	 * Fired repeatedly during a "drag to pan" interaction. See {@link DragPanHandler}.
	 */
	drag: MapLibreEvent<MouseEvent | TouchEvent | undefined>;
	/**
	 * Fired when a "drag to pan" interaction ends. See {@link DragPanHandler}.
	 * @see [Create a draggable marker](https://maplibre.org/maplibre-gl-js/docs/examples/drag-a-marker/)
	 */
	dragend: MapLibreEvent<MouseEvent | TouchEvent | undefined>;
	/**
	 * Fired whenever the map's pitch (tilt) begins a change as
	 * the result of either user interaction or methods such as {@link Map#flyTo} .
	 */
	pitchstart: MapLibreEvent<MouseEvent | TouchEvent | undefined>;
	/**
	 * Fired repeatedly during the map's pitch (tilt) animation between
	 * one state and another as the result of either user interaction
	 * or methods such as {@link Map#flyTo}.
	 */
	pitch: MapLibreEvent<MouseEvent | TouchEvent | undefined>;
	/**
	 * Fired immediately after the map's pitch (tilt) finishes changing as
	 * the result of either user interaction or methods such as {@link Map#flyTo}.
	 */
	pitchend: MapLibreEvent<MouseEvent | TouchEvent | undefined>;
	/**
	 * Fired when a [`wheel`](https://developer.mozilla.org/en-US/docs/Web/Events/wheel) event occurs within the map.
	 */
	wheel: MapWheelEvent;
	/**
	 * Fired when terrain is changed
	 */
	terrain: MapTerrainEvent;
	/**
	 * Fired whenever the cooperativeGestures option prevents a gesture from being handled by the map.
	 * This is useful for showing your own UI when this happens.
	 */
	cooperativegestureprevented: MapLibreEvent<WheelEvent | TouchEvent> & {
		gestureType: "wheel_zoom" | "touch_pan";
	};
};
/**
 * The base event for MapLibre
 *
 * @group Event Related
 */
export type MapLibreEvent<TOrig = unknown> = {
	type: keyof MapEventType | keyof MapLayerEventType;
	target: Map$1;
	originalEvent: TOrig;
};
/**
 * The style data event
 *
 * @group Event Related
 */
export type MapStyleDataEvent = MapLibreEvent & {
	dataType: "style";
};
/**
 * The source data event interface
 *
 * @group Event Related
 */
export type MapSourceDataEvent = MapLibreEvent & {
	dataType: "source";
	/**
	 * True if the event has a `dataType` of `source` and the source has no outstanding network requests.
	 */
	isSourceLoaded: boolean;
	/**
	 * The [style spec representation of the source](https://maplibre.org/maplibre-style-spec/#sources) if the event has a `dataType` of `source`.
	 */
	source: SourceSpecification;
	sourceId: string;
	sourceDataType: MapSourceDataType;
	/**
	 * The tile being loaded or changed, if the event has a `dataType` of `source` and
	 * the event is related to loading of a tile.
	 */
	tile: any;
};
/**
 * `MapMouseEvent` is the event type for mouse-related map events.
 *
 * @group Event Related
 *
 * @example
 * ```ts
 * // The `click` event is an example of a `MapMouseEvent`.
 * // Set up an event listener on the map.
 * map.on('click', (e) => {
 *   // The event object (e) contains information like the
 *   // coordinates of the point on the map that was clicked.
 *   console.log('A click event has occurred at ' + e.lngLat);
 * });
 * ```
 */
export declare class MapMouseEvent extends Event$1 implements MapLibreEvent<MouseEvent> {
	/**
	 * The event type
	 */
	type: "mousedown" | "mouseup" | "click" | "dblclick" | "mousemove" | "mouseover" | "mouseenter" | "mouseleave" | "mouseout" | "contextmenu";
	/**
	 * The `Map` object that fired the event.
	 */
	target: Map$1;
	/**
	 * The DOM event which caused the map event.
	 */
	originalEvent: MouseEvent;
	/**
	 * The pixel coordinates of the mouse cursor, relative to the map and measured from the top left corner.
	 */
	point: Point;
	/**
	 * The geographic location on the map of the mouse cursor.
	 */
	lngLat: LngLat;
	/**
	 * Prevents subsequent default processing of the event by the map.
	 *
	 * Calling this method will prevent the following default map behaviors:
	 *
	 *   * On `mousedown` events, the behavior of {@link DragPanHandler}
	 *   * On `mousedown` events, the behavior of {@link DragRotateHandler}
	 *   * On `mousedown` events, the behavior of {@link BoxZoomHandler}
	 *   * On `dblclick` events, the behavior of {@link DoubleClickZoomHandler}
	 *
	 */
	preventDefault(): void;
	/**
	 * `true` if `preventDefault` has been called.
	 */
	get defaultPrevented(): boolean;
	_defaultPrevented: boolean;
	constructor(type: string, map: Map$1, originalEvent: MouseEvent, data?: any);
}
/**
 * `MapTouchEvent` is the event type for touch-related map events.
 *
 * @group Event Related
 */
export declare class MapTouchEvent extends Event$1 implements MapLibreEvent<TouchEvent> {
	/**
	 * The event type.
	 */
	type: "touchstart" | "touchmove" | "touchend" | "touchcancel";
	/**
	 * The `Map` object that fired the event.
	 */
	target: Map$1;
	/**
	 * The DOM event which caused the map event.
	 */
	originalEvent: TouchEvent;
	/**
	 * The geographic location on the map of the center of the touch event points.
	 */
	lngLat: LngLat;
	/**
	 * The pixel coordinates of the center of the touch event points, relative to the map and measured from the top left
	 * corner.
	 */
	point: Point;
	/**
	 * The array of pixel coordinates corresponding to a
	 * [touch event's `touches`](https://developer.mozilla.org/en-US/docs/Web/API/TouchEvent/touches) property.
	 */
	points: Array<Point>;
	/**
	 * The geographical locations on the map corresponding to a
	 * [touch event's `touches`](https://developer.mozilla.org/en-US/docs/Web/API/TouchEvent/touches) property.
	 */
	lngLats: Array<LngLat>;
	/**
	 * Prevents subsequent default processing of the event by the map.
	 *
	 * Calling this method will prevent the following default map behaviors:
	 *
	 *   * On `touchstart` events, the behavior of {@link DragPanHandler}
	 *   * On `touchstart` events, the behavior of {@link TwoFingersTouchZoomRotateHandler}
	 *
	 */
	preventDefault(): void;
	/**
	 * `true` if `preventDefault` has been called.
	 */
	get defaultPrevented(): boolean;
	_defaultPrevented: boolean;
	constructor(type: string, map: Map$1, originalEvent: TouchEvent);
}
/**
 * `MapWheelEvent` is the event type for the `wheel` map event.
 *
 * @group Event Related
 */
export declare class MapWheelEvent extends Event$1 {
	/**
	 * The event type.
	 */
	type: "wheel";
	/**
	 * The `Map` object that fired the event.
	 */
	target: Map$1;
	/**
	 * The DOM event which caused the map event.
	 */
	originalEvent: WheelEvent;
	/**
	 * Prevents subsequent default processing of the event by the map.
	 *
	 * Calling this method will prevent the behavior of {@link ScrollZoomHandler}.
	 */
	preventDefault(): void;
	/**
	 * `true` if `preventDefault` has been called.
	 */
	get defaultPrevented(): boolean;
	_defaultPrevented: boolean;
	/** */
	constructor(type: string, map: Map$1, originalEvent: WheelEvent);
}
/**
 * A `MapLibreZoomEvent` is the event type for the boxzoom-related map events emitted by the {@link BoxZoomHandler}.
 *
 * @group Event Related
 */
export type MapLibreZoomEvent = {
	/**
	 * The type of boxzoom event. One of `boxzoomstart`, `boxzoomend` or `boxzoomcancel`
	 */
	type: "boxzoomstart" | "boxzoomend" | "boxzoomcancel";
	/**
	 * The `Map` instance that triggered the event
	 */
	target: Map$1;
	/**
	 * The DOM event that triggered the boxzoom event. Can be a `MouseEvent` or `KeyboardEvent`
	 */
	originalEvent: MouseEvent;
};
/**
 * A `MapDataEvent` object is emitted with the `data`
 * and `dataloading` events. Possible values for
 * `dataType`s are:
 *
 * - `'source'`: The non-tile data associated with any source
 * - `'style'`: The [style](https://maplibre.org/maplibre-style-spec/) used by the map
 *
 * Possible values for `sourceDataType`s are:
 *
 * - `'metadata'`: indicates that any necessary source metadata has been loaded (such as TileJSON) and it is ok to start loading tiles
 * - `'content'`: indicates the source data has changed (such as when source.setData() has been called on GeoJSONSource)
 * - `'visibility'`: send when the source becomes used when at least one of its layers becomes visible in style sense (inside the layer's zoom range and with layout.visibility set to 'visible')
 * - `'idle'`: indicates that no new source data has been fetched (but the source has done loading)
 *
 * @group Event Related
 *
 * @example
 * ```ts
 * // The sourcedata event is an example of MapDataEvent.
 * // Set up an event listener on the map.
 * map.on('sourcedata', (e) => {
 *    if (e.isSourceLoaded) {
 *        // Do something when the source has finished loading
 *    }
 * });
 * ```
 */
export type MapDataEvent = {
	/**
	 * The event type.
	 */
	type: string;
	/**
	 * The type of data that has changed. One of `'source'`, `'style'`.
	 */
	dataType: string;
	/**
	 *  Included if the event has a `dataType` of `source` and the event signals that internal data has been received or changed. Possible values are `metadata`, `content`, `visibility` and `idle`.
	 */
	sourceDataType: MapSourceDataType;
};
/**
 * The terrain event
 *
 * @group Event Related
 */
export type MapTerrainEvent = {
	type: "terrain";
};
/**
 * An event related to the web gl context
 *
 * @group Event Related
 */
export type MapContextEvent = {
	type: "webglcontextlost" | "webglcontextrestored";
	originalEvent: WebGLContextEvent;
};
/**
 * The style image missing event
 *
 * @group Event Related
 *
 * @see [Generate and add a missing icon to the map](https://maplibre.org/maplibre-gl-js/docs/examples/add-image-missing-generated/)
 */
export type MapStyleImageMissingEvent = MapLibreEvent & {
	type: "styleimagemissing";
	id: string;
};
/**
 * The {@link AttributionControl} options object
 */
export type AttributionControlOptions = {
	/**
	 * If `true`, the attribution control will always collapse when moving the map. If `false`,
	 * force the expanded attribution control. The default is a responsive attribution that collapses when the user moves the map on maps less than 640 pixels wide.
	 * **Attribution should not be collapsed if it can comfortably fit on the map. `compact` should only be used to modify default attribution when map size makes it impossible to fit default attribution and when the automatic compact resizing for default settings are not sufficient.**
	 */
	compact?: boolean;
	/**
	 * Attributions to show in addition to any other attributions.
	 */
	customAttribution?: string | Array<string>;
};
/**
 * An `AttributionControl` control presents the map's attribution information. By default, the attribution control is expanded (regardless of map width).
 * @group Markers and Controls
 * @example
 * ```ts
 * let map = new Map({attributionControl: false})
 *     .addControl(new AttributionControl({
 *         compact: true
 *     }));
 * ```
 */
export declare class AttributionControl implements IControl {
	options: AttributionControlOptions;
	_map: Map$1;
	_compact: boolean | undefined;
	_container: HTMLElement;
	_innerContainer: HTMLElement;
	_compactButton: HTMLElement;
	_editLink: HTMLAnchorElement;
	_attribHTML: string;
	styleId: string;
	styleOwner: string;
	/**
	 * @param options - the attribution options
	 */
	constructor(options?: AttributionControlOptions);
	getDefaultPosition(): ControlPosition;
	/** {@inheritDoc IControl.onAdd} */
	onAdd(map: Map$1): HTMLElement;
	/** {@inheritDoc IControl.onRemove} */
	onRemove(): void;
	_setElementTitle(element: HTMLElement, title: "ToggleAttribution" | "MapFeedback"): void;
	_toggleAttribution: () => void;
	_updateData: (e: MapDataEvent) => void;
	_updateAttributions(): void;
	_updateCompact: () => void;
	_updateCompactMinimize: () => void;
}
declare const defaultLocale: {
	"AttributionControl.ToggleAttribution": string;
	"AttributionControl.MapFeedback": string;
	"FullscreenControl.Enter": string;
	"FullscreenControl.Exit": string;
	"GeolocateControl.FindMyLocation": string;
	"GeolocateControl.LocationNotAvailable": string;
	"LogoControl.Title": string;
	"Map.Title": string;
	"Marker.Title": string;
	"NavigationControl.ResetBearing": string;
	"NavigationControl.ZoomIn": string;
	"NavigationControl.ZoomOut": string;
	"Popup.Close": string;
	"ScaleControl.Feet": string;
	"ScaleControl.Meters": string;
	"ScaleControl.Kilometers": string;
	"ScaleControl.Miles": string;
	"ScaleControl.NauticalMiles": string;
	"TerrainControl.Enable": string;
	"TerrainControl.Disable": string;
	"CooperativeGesturesHandler.WindowsHelpText": string;
	"CooperativeGesturesHandler.MacHelpText": string;
	"CooperativeGesturesHandler.MobileHelpText": string;
};
declare class TransformProvider {
	_map: Map$1;
	constructor(map: Map$1);
	get transform(): Transform;
	get center(): {
		lng: number;
		lat: number;
	};
	get zoom(): number;
	get pitch(): number;
	get bearing(): number;
	unproject(point: PointLike): LngLat;
}
/**
 * An options object sent to the enable function of some of the handlers
 */
export type AroundCenterOptions = {
	/**
	 * If "center" is passed, map will zoom around the center of map
	 */
	around: "center";
};
declare abstract class TwoFingersTouchHandler implements Handler {
	_enabled?: boolean;
	_active?: boolean;
	_firstTwoTouches?: [
		number,
		number
	];
	_vector?: Point;
	_startVector?: Point;
	_aroundCenter?: boolean;
	/** @internal */
	constructor();
	reset(): void;
	abstract _start(points: [
		Point,
		Point
	]): void;
	abstract _move(points: [
		Point,
		Point
	], pinchAround: Point | null, e: TouchEvent): HandlerResult | void;
	touchstart(e: TouchEvent, points: Array<Point>, mapTouches: Array<Touch>): void;
	touchmove(e: TouchEvent, points: Array<Point>, mapTouches: Array<Touch>): HandlerResult | void;
	touchend(e: TouchEvent, points: Array<Point>, mapTouches: Array<Touch>): void;
	touchcancel(): void;
	/**
	 * Enables the "drag to pitch" interaction.
	 *
	 * @example
	 * ```ts
	 * map.touchPitch.enable();
	 * ```
	 */
	enable(options?: AroundCenterOptions | boolean | null): void;
	/**
	 * Disables the "drag to pitch" interaction.
	 *
	 * @example
	 * ```ts
	 * map.touchPitch.disable();
	 * ```
	 */
	disable(): void;
	/**
	 * Returns a Boolean indicating whether the "drag to pitch" interaction is enabled.
	 *
	 * @returns  `true` if the "drag to pitch" interaction is enabled.
	 */
	isEnabled(): boolean;
	/**
	 * Returns a Boolean indicating whether the "drag to pitch" interaction is active, i.e. currently being used.
	 *
	 * @returns `true` if the "drag to pitch" interaction is active.
	 */
	isActive(): boolean;
}
/**
 * The `TwoFingersTouchHandler`s allows the user to zoom the map two fingers
 *
 * @group Handlers
 */
export declare class TwoFingersTouchZoomHandler extends TwoFingersTouchHandler {
	_distance?: number;
	_startDistance?: number;
	reset(): void;
	_start(points: [
		Point,
		Point
	]): void;
	_move(points: [
		Point,
		Point
	], pinchAround: Point | null): HandlerResult | void;
}
/**
 * The `TwoFingersTouchHandler`s allows the user to rotate the map two fingers
 *
 * @group Handlers
 */
export declare class TwoFingersTouchRotateHandler extends TwoFingersTouchHandler {
	_minDiameter?: number;
	reset(): void;
	_start(points: [
		Point,
		Point
	]): void;
	_move(points: [
		Point,
		Point
	], pinchAround: Point | null, _e: TouchEvent): HandlerResult | void;
	_isBelowThreshold(vector: Point): boolean;
}
/**
 * The `TwoFingersTouchPitchHandler` allows the user to pitch the map by dragging up and down with two fingers.
 *
 * @group Handlers
 */
export declare class TwoFingersTouchPitchHandler extends TwoFingersTouchHandler {
	_valid?: boolean;
	_firstMove?: number;
	_lastPoints?: [
		Point,
		Point
	];
	_map: Map$1;
	_currentTouchCount: number;
	constructor(map: Map$1);
	reset(): void;
	touchstart(e: TouchEvent, points: Array<Point>, mapTouches: Array<Touch>): void;
	_start(points: [
		Point,
		Point
	]): void;
	_move(points: [
		Point,
		Point
	], center: Point | null, e: TouchEvent): HandlerResult | void;
	gestureBeginsVertically(vectorA: Point, vectorB: Point, timeStamp: number): boolean | undefined;
}
/**
 * The `ScrollZoomHandler` allows the user to zoom the map by scrolling.
 *
 * @group Handlers
 */
export declare class ScrollZoomHandler implements Handler {
	_map: Map$1;
	_tr: TransformProvider;
	_enabled: boolean;
	_active: boolean;
	_zooming: boolean;
	_aroundCenter: boolean;
	_around: LngLat;
	_aroundPoint: Point;
	_type: "wheel" | "trackpad" | null;
	_lastValue: number;
	_timeout: ReturnType<typeof setTimeout>;
	_finishTimeout: ReturnType<typeof setTimeout>;
	_lastWheelEvent: any;
	_lastWheelEventTime: number;
	_startZoom: number;
	_targetZoom: number;
	_delta: number;
	_easing: ((a: number) => number);
	_prevEase: {
		start: number;
		duration: number;
		easing: (_: number) => number;
	};
	_frameId: boolean;
	_triggerRenderFrame: () => void;
	_defaultZoomRate: number;
	_wheelZoomRate: number;
	/** @internal */
	constructor(map: Map$1, triggerRenderFrame: () => void);
	/**
	 * Set the zoom rate of a trackpad
	 * @param zoomRate - 1/100 The rate used to scale trackpad movement to a zoom value.
	 * @example
	 * Speed up trackpad zoom
	 * ```ts
	 * map.scrollZoom.setZoomRate(1/25);
	 * ```
	 */
	setZoomRate(zoomRate: number): void;
	/**
	 * Set the zoom rate of a mouse wheel
	 * @param wheelZoomRate - 1/450 The rate used to scale mouse wheel movement to a zoom value.
	 * @example
	 * Slow down zoom of mouse wheel
	 * ```ts
	 * map.scrollZoom.setWheelZoomRate(1/600);
	 * ```
	 */
	setWheelZoomRate(wheelZoomRate: number): void;
	/**
	 * Returns a Boolean indicating whether the "scroll to zoom" interaction is enabled.
	 * @returns `true` if the "scroll to zoom" interaction is enabled.
	 */
	isEnabled(): boolean;
	isActive(): boolean;
	isZooming(): boolean;
	/**
	 * Enables the "scroll to zoom" interaction.
	 *
	 * @param options - Options object.
	 * @example
	 * ```ts
	 * map.scrollZoom.enable();
	 * map.scrollZoom.enable({ around: 'center' })
	 * ```
	 */
	enable(options?: AroundCenterOptions | boolean): void;
	/**
	 * Disables the "scroll to zoom" interaction.
	 *
	 * @example
	 * ```ts
	 * map.scrollZoom.disable();
	 * ```
	 */
	disable(): void;
	/**
	 * Determines whether or not the gesture is blocked due to cooperativeGestures.
	 */
	_shouldBePrevented(e: WheelEvent): boolean;
	wheel(e: WheelEvent): void;
	_onTimeout: (initialEvent: MouseEvent) => void;
	_start(e: MouseEvent): void;
	renderFrame(): {
		noInertia: boolean;
		needsRenderFrame: boolean;
		zoomDelta: number;
		around: Point;
		originalEvent: any;
	};
	_smoothOutEasing(duration: number): (t: number) => number;
	reset(): void;
}
/**
 * The `BoxZoomHandler` allows the user to zoom the map to fit within a bounding box.
 * The bounding box is defined by clicking and holding `shift` while dragging the cursor.
 *
 * @group Handlers
 */
export declare class BoxZoomHandler implements Handler {
	_map: Map$1;
	_tr: TransformProvider;
	_el: HTMLElement;
	_container: HTMLElement;
	_enabled: boolean;
	_active: boolean;
	_startPos: Point;
	_lastPos: Point;
	_box: HTMLElement;
	_clickTolerance: number;
	/** @internal */
	constructor(map: Map$1, options: {
		clickTolerance: number;
	});
	/**
	 * Returns a Boolean indicating whether the "box zoom" interaction is enabled.
	 *
	 * @returns `true` if the "box zoom" interaction is enabled.
	 */
	isEnabled(): boolean;
	/**
	 * Returns a Boolean indicating whether the "box zoom" interaction is active, i.e. currently being used.
	 *
	 * @returns `true` if the "box zoom" interaction is active.
	 */
	isActive(): boolean;
	/**
	 * Enables the "box zoom" interaction.
	 *
	 * @example
	 * ```ts
	 * map.boxZoom.enable();
	 * ```
	 */
	enable(): void;
	/**
	 * Disables the "box zoom" interaction.
	 *
	 * @example
	 * ```ts
	 * map.boxZoom.disable();
	 * ```
	 */
	disable(): void;
	mousedown(e: MouseEvent, point: Point): void;
	mousemoveWindow(e: MouseEvent, point: Point): void;
	mouseupWindow(e: MouseEvent, point: Point): {
		cameraAnimation: (map: any) => any;
	};
	keydown(e: KeyboardEvent): void;
	reset(): void;
	_fireEvent(type: string, e: any): Map$1;
}
/**
 * Options object for `DragRotateHandler`.
 */
export type DragRotateHandlerOptions = {
	/**
	 * Control the map pitch in addition to the bearing
	 * @defaultValue true
	 */
	pitchWithRotate: boolean;
};
/**
 * The `DragRotateHandler` allows the user to rotate the map by clicking and
 * dragging the cursor while holding the right mouse button or `ctrl` key.
 *
 * @group Handlers
 */
export declare class DragRotateHandler {
	_mouseRotate: MouseRotateHandler;
	_mousePitch: MousePitchHandler;
	_pitchWithRotate: boolean;
	/** @internal */
	constructor(options: DragRotateHandlerOptions, mouseRotate: MouseRotateHandler, mousePitch: MousePitchHandler);
	/**
	 * Enables the "drag to rotate" interaction.
	 *
	 * @example
	 * ```ts
	 * map.dragRotate.enable();
	 * ```
	 */
	enable(): void;
	/**
	 * Disables the "drag to rotate" interaction.
	 *
	 * @example
	 * ```ts
	 * map.dragRotate.disable();
	 * ```
	 */
	disable(): void;
	/**
	 * Returns a Boolean indicating whether the "drag to rotate" interaction is enabled.
	 *
	 * @returns `true` if the "drag to rotate" interaction is enabled.
	 */
	isEnabled(): boolean;
	/**
	 * Returns a Boolean indicating whether the "drag to rotate" interaction is active, i.e. currently being used.
	 *
	 * @returns `true` if the "drag to rotate" interaction is active.
	 */
	isActive(): boolean;
}
/**
 * The {@link CooperativeGesturesHandler} options object for the gesture settings
 */
export type GestureOptions = boolean;
/**
 * A `CooperativeGestureHandler` is a control that adds cooperative gesture info when user tries to zoom in/out.
 *
 * When the CooperativeGestureHandler blocks a gesture, it will emit a `cooperativegestureprevented` event.
 *
 * @group Handlers
 *
 * @example
 * ```ts
 * const map = new Map({
 *   cooperativeGestures: true
 * });
 * ```
 * @see [Example: cooperative gestures](https://maplibre.org/maplibre-gl-js/docs/examples/cooperative-gestures/)
 **/
export declare class CooperativeGesturesHandler implements Handler {
	_options: GestureOptions;
	_map: Map$1;
	_container: HTMLElement;
	/**
	 * This is the key that will allow to bypass the cooperative gesture protection
	 */
	_bypassKey: "metaKey" | "ctrlKey";
	_enabled: boolean;
	constructor(map: Map$1, options: GestureOptions);
	isActive(): boolean;
	reset(): void;
	_setupUI(): void;
	_destroyUI(): void;
	enable(): void;
	disable(): void;
	isEnabled(): boolean;
	isBypassed(event: MouseEvent | WheelEvent | PointerEvent): boolean;
	notifyGestureBlocked(gestureType: "wheel_zoom" | "touch_pan", originalEvent: Event$1): void;
}
/**
 * The `KeyboardHandler` allows the user to zoom, rotate, and pan the map using
 * the following keyboard shortcuts:
 *
 * - `=` / `+`: Increase the zoom level by 1.
 * - `Shift-=` / `Shift-+`: Increase the zoom level by 2.
 * - `-`: Decrease the zoom level by 1.
 * - `Shift--`: Decrease the zoom level by 2.
 * - Arrow keys: Pan by 100 pixels.
 * - `Shift+⇢`: Increase the rotation by 15 degrees.
 * - `Shift+⇠`: Decrease the rotation by 15 degrees.
 * - `Shift+⇡`: Increase the pitch by 10 degrees.
 * - `Shift+⇣`: Decrease the pitch by 10 degrees.
 *
 * @group Handlers
 */
export declare class KeyboardHandler implements Handler {
	_tr: TransformProvider;
	_enabled: boolean;
	_active: boolean;
	_panStep: number;
	_bearingStep: number;
	_pitchStep: number;
	_rotationDisabled: boolean;
	/** @internal */
	constructor(map: Map$1);
	reset(): void;
	keydown(e: KeyboardEvent): {
		cameraAnimation: (map: Map$1) => void;
	};
	/**
	 * Enables the "keyboard rotate and zoom" interaction.
	 *
	 * @example
	 * ```ts
	 * map.keyboard.enable();
	 * ```
	 */
	enable(): void;
	/**
	 * Disables the "keyboard rotate and zoom" interaction.
	 *
	 * @example
	 * ```ts
	 * map.keyboard.disable();
	 * ```
	 */
	disable(): void;
	/**
	 * Returns a Boolean indicating whether the "keyboard rotate and zoom"
	 * interaction is enabled.
	 *
	 * @returns `true` if the "keyboard rotate and zoom"
	 * interaction is enabled.
	 */
	isEnabled(): boolean;
	/**
	 * Returns true if the handler is enabled and has detected the start of a
	 * zoom/rotate gesture.
	 *
	 * @returns `true` if the handler is enabled and has detected the
	 * start of a zoom/rotate gesture.
	 */
	isActive(): boolean;
	/**
	 * Disables the "keyboard pan/rotate" interaction, leaving the
	 * "keyboard zoom" interaction enabled.
	 *
	 * @example
	 * ```ts
	 * map.keyboard.disableRotation();
	 * ```
	 */
	disableRotation(): void;
	/**
	 * Enables the "keyboard pan/rotate" interaction.
	 *
	 * @example
	 * ```ts
	 * map.keyboard.enable();
	 * map.keyboard.enableRotation();
	 * ```
	 */
	enableRotation(): void;
}
declare class ClickZoomHandler implements Handler {
	_tr: TransformProvider;
	_enabled: boolean;
	_active: boolean;
	/** @internal */
	constructor(map: Map$1);
	reset(): void;
	dblclick(e: MouseEvent, point: Point): {
		cameraAnimation: (map: Map$1) => void;
	};
	enable(): void;
	disable(): void;
	isEnabled(): boolean;
	isActive(): boolean;
}
declare class SingleTapRecognizer {
	numTouches: number;
	centroid: Point;
	startTime: number;
	aborted: boolean;
	touches: {
		[k in number | string]: Point;
	};
	constructor(options: {
		numTouches: number;
	});
	reset(): void;
	touchstart(e: TouchEvent, points: Array<Point>, mapTouches: Array<Touch>): void;
	touchmove(e: TouchEvent, points: Array<Point>, mapTouches: Array<Touch>): void;
	touchend(e: TouchEvent, points: Array<Point>, mapTouches: Array<Touch>): Point;
}
declare class TapRecognizer {
	singleTap: SingleTapRecognizer;
	numTaps: number;
	lastTime: number;
	lastTap: Point;
	count: number;
	constructor(options: {
		numTaps: number;
		numTouches: number;
	});
	reset(): void;
	touchstart(e: TouchEvent, points: Array<Point>, mapTouches: Array<Touch>): void;
	touchmove(e: TouchEvent, points: Array<Point>, mapTouches: Array<Touch>): void;
	touchend(e: TouchEvent, points: Array<Point>, mapTouches: Array<Touch>): Point;
}
declare class TapZoomHandler implements Handler {
	_tr: TransformProvider;
	_enabled: boolean;
	_active: boolean;
	_zoomIn: TapRecognizer;
	_zoomOut: TapRecognizer;
	constructor(map: Map$1);
	reset(): void;
	touchstart(e: TouchEvent, points: Array<Point>, mapTouches: Array<Touch>): void;
	touchmove(e: TouchEvent, points: Array<Point>, mapTouches: Array<Touch>): void;
	touchend(e: TouchEvent, points: Array<Point>, mapTouches: Array<Touch>): {
		cameraAnimation: (map: Map$1) => Map$1;
	};
	touchcancel(): void;
	enable(): void;
	disable(): void;
	isEnabled(): boolean;
	isActive(): boolean;
}
/**
 * The `DoubleClickZoomHandler` allows the user to zoom the map at a point by
 * double clicking or double tapping.
 *
 * @group Handlers
 */
export declare class DoubleClickZoomHandler {
	_clickZoom: ClickZoomHandler;
	_tapZoom: TapZoomHandler;
	/** @internal */
	constructor(clickZoom: ClickZoomHandler, TapZoom: TapZoomHandler);
	/**
	 * Enables the "double click to zoom" interaction.
	 *
	 * @example
	 * ```ts
	 * map.doubleClickZoom.enable();
	 * ```
	 */
	enable(): void;
	/**
	 * Disables the "double click to zoom" interaction.
	 *
	 * @example
	 * ```ts
	 * map.doubleClickZoom.disable();
	 * ```
	 */
	disable(): void;
	/**
	 * Returns a Boolean indicating whether the "double click to zoom" interaction is enabled.
	 *
	 * @returns `true` if the "double click to zoom" interaction is enabled.
	 */
	isEnabled(): boolean;
	/**
	 * Returns a Boolean indicating whether the "double click to zoom" interaction is active, i.e. currently being used.
	 *
	 * @returns `true` if the "double click to zoom" interaction is active.
	 */
	isActive(): boolean;
}
declare class TapDragZoomHandler implements Handler {
	_enabled: boolean;
	_active: boolean;
	_swipePoint: Point;
	_swipeTouch: number;
	_tapTime: number;
	_tapPoint: Point;
	_tap: TapRecognizer;
	constructor();
	reset(): void;
	touchstart(e: TouchEvent, points: Array<Point>, mapTouches: Array<Touch>): void;
	touchmove(e: TouchEvent, points: Array<Point>, mapTouches: Array<Touch>): {
		zoomDelta: number;
	};
	touchend(e: TouchEvent, points: Array<Point>, mapTouches: Array<Touch>): void;
	touchcancel(): void;
	enable(): void;
	disable(): void;
	isEnabled(): boolean;
	isActive(): boolean;
}
/**
 * The `TwoFingersTouchZoomRotateHandler` allows the user to zoom and rotate the map by
 * pinching on a touchscreen.
 *
 * They can zoom with one finger by double tapping and dragging. On the second tap,
 * hold the finger down and drag up or down to zoom in or out.
 *
 * @group Handlers
 */
export declare class TwoFingersTouchZoomRotateHandler {
	_el: HTMLElement;
	_touchZoom: TwoFingersTouchZoomHandler;
	_touchRotate: TwoFingersTouchRotateHandler;
	_tapDragZoom: TapDragZoomHandler;
	_rotationDisabled: boolean;
	_enabled: boolean;
	/** @internal */
	constructor(el: HTMLElement, touchZoom: TwoFingersTouchZoomHandler, touchRotate: TwoFingersTouchRotateHandler, tapDragZoom: TapDragZoomHandler);
	/**
	 * Enables the "pinch to rotate and zoom" interaction.
	 *
	 * @param options - Options object.
	 *
	 * @example
	 * ```ts
	 * map.touchZoomRotate.enable();
	 * map.touchZoomRotate.enable({ around: 'center' });
	 * ```
	 */
	enable(options?: AroundCenterOptions | boolean | null): void;
	/**
	 * Disables the "pinch to rotate and zoom" interaction.
	 *
	 * @example
	 * ```ts
	 * map.touchZoomRotate.disable();
	 * ```
	 */
	disable(): void;
	/**
	 * Returns a Boolean indicating whether the "pinch to rotate and zoom" interaction is enabled.
	 *
	 * @returns `true` if the "pinch to rotate and zoom" interaction is enabled.
	 */
	isEnabled(): boolean;
	/**
	 * Returns true if the handler is enabled and has detected the start of a zoom/rotate gesture.
	 *
	 * @returns `true` if the handler is active, `false` otherwise
	 */
	isActive(): boolean;
	/**
	 * Disables the "pinch to rotate" interaction, leaving the "pinch to zoom"
	 * interaction enabled.
	 *
	 * @example
	 * ```ts
	 * map.touchZoomRotate.disableRotation();
	 * ```
	 */
	disableRotation(): void;
	/**
	 * Enables the "pinch to rotate" interaction.
	 *
	 * @example
	 * ```ts
	 * map.touchZoomRotate.enable();
	 * map.touchZoomRotate.enableRotation();
	 * ```
	 */
	enableRotation(): void;
}
/**
 * The {@link Map} options object.
 */
export type MapOptions = {
	/**
	 * If `true`, the map's position (zoom, center latitude, center longitude, bearing, and pitch) will be synced with the hash fragment of the page's URL.
	 * For example, `http://path/to/my/page.html#2.59/39.26/53.07/-24.1/60`.
	 * An additional string may optionally be provided to indicate a parameter-styled hash,
	 * e.g. http://path/to/my/page.html#map=2.59/39.26/53.07/-24.1/60&foo=bar, where foo
	 * is a custom parameter and bar is an arbitrary hash distinct from the map hash.
	 * @defaultValue false
	 */
	hash?: boolean | string;
	/**
	 * If `false`, no mouse, touch, or keyboard listeners will be attached to the map, so it will not respond to interaction.
	 * @defaultValue true
	 */
	interactive?: boolean;
	/**
	 * The HTML element in which MapLibre GL JS will render the map, or the element's string `id`. The specified element must have no children.
	 */
	container: HTMLElement | string;
	/**
	 * The threshold, measured in degrees, that determines when the map's
	 * bearing will snap to north. For example, with a `bearingSnap` of 7, if the user rotates
	 * the map within 7 degrees of north, the map will automatically snap to exact north.
	 * @defaultValue 7
	 */
	bearingSnap?: number;
	/**
	 * If set, an {@link AttributionControl} will be added to the map with the provided options.
	 * To disable the attribution control, pass `false`.
	 * Note: showing the logo of MapLibre is not required for using MapLibre.
	 * @defaultValue compact: true, customAttribution: "MapLibre ...".
	 */
	attributionControl?: false | AttributionControlOptions;
	/**
	 * If `true`, the MapLibre logo will be shown.
	 */
	maplibreLogo?: boolean;
	/**
	 * A string representing the position of the MapLibre wordmark on the map. Valid options are `top-left`,`top-right`, `bottom-left`, or `bottom-right`.
	 * @defaultValue 'bottom-left'
	 */
	logoPosition?: ControlPosition;
	/**
	 * If `true`, map creation will fail if the performance of MapLibre GL JS would be dramatically worse than expected
	 * (i.e. a software renderer would be used).
	 * @defaultValue false
	 */
	failIfMajorPerformanceCaveat?: boolean;
	/**
	 * If `true`, the map's canvas can be exported to a PNG using `map.getCanvas().toDataURL()`. This is `false` by default as a performance optimization.
	 * @defaultValue false
	 */
	preserveDrawingBuffer?: boolean;
	/**
	 * If `true`, the gl context will be created with MSAA antialiasing, which can be useful for antialiasing custom layers.
	 * Disabled by default as a performance optimization.
	 */
	antialias?: boolean;
	/**
	 * If `false`, the map won't attempt to re-request tiles once they expire per their HTTP `cacheControl`/`expires` headers.
	 * @defaultValue true
	 */
	refreshExpiredTiles?: boolean;
	/**
	 * If set, the map will be constrained to the given bounds.
	 */
	maxBounds?: LngLatBoundsLike;
	/**
	 * If `true`, the "scroll to zoom" interaction is enabled. {@link AroundCenterOptions} are passed as options to {@link ScrollZoomHandler#enable}.
	 * @defaultValue true
	 */
	scrollZoom?: boolean | AroundCenterOptions;
	/**
	 * The minimum zoom level of the map (0-24).
	 * @defaultValue 0
	 */
	minZoom?: number | null;
	/**
	 * The maximum zoom level of the map (0-24).
	 * @defaultValue 22
	 */
	maxZoom?: number | null;
	/**
	 * The minimum pitch of the map (0-85). Values greater than 60 degrees are experimental and may result in rendering issues. If you encounter any, please raise an issue with details in the MapLibre project.
	 * @defaultValue 0
	 */
	minPitch?: number | null;
	/**
	 * The maximum pitch of the map (0-85). Values greater than 60 degrees are experimental and may result in rendering issues. If you encounter any, please raise an issue with details in the MapLibre project.
	 * @defaultValue 60
	 */
	maxPitch?: number | null;
	/**
	 * If `true`, the "box zoom" interaction is enabled (see {@link BoxZoomHandler}).
	 * @defaultValue true
	 */
	boxZoom?: boolean;
	/**
	 * If `true`, the "drag to rotate" interaction is enabled (see {@link DragRotateHandler}).
	 * @defaultValue true
	 */
	dragRotate?: boolean;
	/**
	 * If `true`, the "drag to pan" interaction is enabled. An `Object` value is passed as options to {@link DragPanHandler#enable}.
	 * @defaultValue true
	 */
	dragPan?: boolean | DragPanOptions;
	/**
	 * If `true`, keyboard shortcuts are enabled (see {@link KeyboardHandler}).
	 * @defaultValue true
	 */
	keyboard?: boolean;
	/**
	 * If `true`, the "double click to zoom" interaction is enabled (see {@link DoubleClickZoomHandler}).
	 * @defaultValue true
	 */
	doubleClickZoom?: boolean;
	/**
	 * If `true`, the "pinch to rotate and zoom" interaction is enabled. An `Object` value is passed as options to {@link TwoFingersTouchZoomRotateHandler#enable}.
	 * @defaultValue true
	 */
	touchZoomRotate?: boolean | AroundCenterOptions;
	/**
	 * If `true`, the "drag to pitch" interaction is enabled. An `Object` value is passed as options to {@link TwoFingersTouchPitchHandler#enable}.
	 * @defaultValue true
	 */
	touchPitch?: boolean | AroundCenterOptions;
	/**
	 * If `true` or set to an options object, the map is only accessible on desktop while holding Command/Ctrl and only accessible on mobile with two fingers. Interacting with the map using normal gestures will trigger an informational screen. With this option enabled, "drag to pitch" requires a three-finger gesture. Cooperative gestures are disabled when a map enters fullscreen using {@link FullscreenControl}.
	 * @defaultValue false
	 */
	cooperativeGestures?: GestureOptions;
	/**
	 * If `true`, the map will automatically resize when the browser window resizes.
	 * @defaultValue true
	 */
	trackResize?: boolean;
	/**
	 * The initial geographical centerpoint of the map. If `center` is not specified in the constructor options, MapLibre GL JS will look for it in the map's style object. If it is not specified in the style, either, it will default to `[0, 0]` Note: MapLibre GL JS uses longitude, latitude coordinate order (as opposed to latitude, longitude) to match GeoJSON.
	 * @defaultValue [0, 0]
	 */
	center?: LngLatLike;
	/**
	 * The initial zoom level of the map. If `zoom` is not specified in the constructor options, MapLibre GL JS will look for it in the map's style object. If it is not specified in the style, either, it will default to `0`.
	 * @defaultValue 0
	 */
	zoom?: number;
	/**
	 * The initial bearing (rotation) of the map, measured in degrees counter-clockwise from north. If `bearing` is not specified in the constructor options, MapLibre GL JS will look for it in the map's style object. If it is not specified in the style, either, it will default to `0`.
	 * @defaultValue 0
	 */
	bearing?: number;
	/**
	 * The initial pitch (tilt) of the map, measured in degrees away from the plane of the screen (0-85). If `pitch` is not specified in the constructor options, MapLibre GL JS will look for it in the map's style object. If it is not specified in the style, either, it will default to `0`. Values greater than 60 degrees are experimental and may result in rendering issues. If you encounter any, please raise an issue with details in the MapLibre project.
	 * @defaultValue 0
	 */
	pitch?: number;
	/**
	 * If `true`, multiple copies of the world will be rendered side by side beyond -180 and 180 degrees longitude. If set to `false`:
	 *
	 * - When the map is zoomed out far enough that a single representation of the world does not fill the map's entire
	 * container, there will be blank space beyond 180 and -180 degrees longitude.
	 * - Features that cross 180 and -180 degrees longitude will be cut in two (with one portion on the right edge of the
	 * map and the other on the left edge of the map) at every zoom level.
	 * @defaultValue true
	 */
	renderWorldCopies?: boolean;
	/**
	 * The maximum number of tiles stored in the tile cache for a given source. If omitted, the cache will be dynamically sized based on the current viewport which can be set using `maxTileCacheZoomLevels` constructor options.
	 * @defaultValue null
	 */
	maxTileCacheSize?: number | null;
	/**
	 * The maximum number of zoom levels for which to store tiles for a given source. Tile cache dynamic size is calculated by multiplying `maxTileCacheZoomLevels` with the approximate number of tiles in the viewport for a given source.
	 * @defaultValue 5
	 */
	maxTileCacheZoomLevels?: number;
	/**
	 * A callback run before the Map makes a request for an external URL. The callback can be used to modify the url, set headers, or set the credentials property for cross-origin requests.
	 * Expected to return an object with a `url` property and optionally `headers` and `credentials` properties.
	 * @defaultValue null
	 */
	transformRequest?: RequestTransformFunction | null;
	/**
	 * A callback run before the map's camera is moved due to user input or animation. The callback can be used to modify the new center, zoom, pitch and bearing.
	 * Expected to return an object containing center, zoom, pitch or bearing values to overwrite.
	 * @defaultValue null
	 */
	transformCameraUpdate?: CameraUpdateTransformFunction | null;
	/**
	 * A patch to apply to the default localization table for UI strings, e.g. control tooltips. The `locale` object maps namespaced UI string IDs to translated strings in the target language; see `src/ui/default_locale.js` for an example with all supported string IDs. The object may specify all UI strings (thereby adding support for a new translation) or only a subset of strings (thereby patching the default translation table).
	 * @defaultValue null
	 */
	locale?: any;
	/**
	 * Controls the duration of the fade-in/fade-out animation for label collisions after initial map load, in milliseconds. This setting affects all symbol layers. This setting does not affect the duration of runtime styling transitions or raster tile cross-fading.
	 * @defaultValue 300
	 */
	fadeDuration?: number;
	/**
	 * If `true`, symbols from multiple sources can collide with each other during collision detection. If `false`, collision detection is run separately for the symbols in each source.
	 * @defaultValue true
	 */
	crossSourceCollisions?: boolean;
	/**
	 * If `true`, Resource Timing API information will be collected for requests made by GeoJSON and Vector Tile web workers (this information is normally inaccessible from the main Javascript thread). Information will be returned in a `resourceTiming` property of relevant `data` events.
	 * @defaultValue false
	 */
	collectResourceTiming?: boolean;
	/**
	 * The max number of pixels a user can shift the mouse pointer during a click for it to be considered a valid click (as opposed to a mouse drag).
	 * @defaultValue 3
	 */
	clickTolerance?: number;
	/**
	 * The initial bounds of the map. If `bounds` is specified, it overrides `center` and `zoom` constructor options.
	 */
	bounds?: LngLatBoundsLike;
	/**
	 * A {@link FitBoundsOptions} options object to use _only_ when fitting the initial `bounds` provided above.
	 */
	fitBoundsOptions?: FitBoundsOptions;
	/**
	 * Defines a CSS
	 * font-family for locally overriding generation of Chinese, Japanese, and Korean characters.
	 * For these characters, font settings from the map's style will be ignored, except for font-weight keywords (light/regular/medium/bold).
	 * Set to `false`, to enable font settings from the map's style for these glyph ranges.
	 * The purpose of this option is to avoid bandwidth-intensive glyph server requests. (See [Use locally generated ideographs](https://maplibre.org/maplibre-gl-js/docs/examples/local-ideographs).)
	 * @defaultValue 'sans-serif'
	 */
	localIdeographFontFamily?: string | false;
	/**
	 * The map's MapLibre style. This must be a JSON object conforming to
	 * the schema described in the [MapLibre Style Specification](https://maplibre.org/maplibre-style-spec/),
	 * or a URL to such JSON.
	 * When the style is not specified, calling {@link Map#setStyle} is required to render the map.
	 */
	style?: StyleSpecification | string;
	/**
	 * If `false`, the map's pitch (tilt) control with "drag to rotate" interaction will be disabled.
	 * @defaultValue true
	 */
	pitchWithRotate?: boolean;
	/**
	 * The pixel ratio.
	 * The canvas' `width` attribute will be `container.clientWidth * pixelRatio` and its `height` attribute will be `container.clientHeight * pixelRatio`. Defaults to `devicePixelRatio` if not specified.
	 */
	pixelRatio?: number;
	/**
	 * If false, style validation will be skipped. Useful in production environment.
	 * @defaultValue true
	 */
	validateStyle?: boolean;
	/**
	 * The canvas' `width` and `height` max size. The values are passed as an array where the first element is max width and the second element is max height.
	 * You shouldn't set this above WebGl `MAX_TEXTURE_SIZE`.
	 * @defaultValue [4096, 4096].
	 */
	maxCanvasSize?: [
		number,
		number
	];
	/**
	 * Determines whether to cancel, or retain, tiles from the current viewport which are still loading but which belong to a farther (smaller) zoom level than the current one.
	 * * If `true`, when zooming in, tiles which didn't manage to load for previous zoom levels will become canceled. This might save some computing resources for slower devices, but the map details might appear more abruptly at the end of the zoom.
	 * * If `false`, when zooming in, the previous zoom level(s) tiles will progressively appear, giving a smoother map details experience. However, more tiles will be rendered in a short period of time.
	 * @defaultValue true
	 */
	cancelPendingTileRequestsWhileZooming?: boolean;
};
export type CompleteMapOptions = Complete<MapOptions>;
export type DelegatedListener = {
	layers: string[];
	listener: Listener;
	delegates: {
		[E in keyof MapEventType]?: Delegate<MapEventType[E]>;
	};
};
export type Delegate<E extends Event$1 = Event$1> = (e: E) => void;
/**
 * The `Map` object represents the map on your page. It exposes methods
 * and properties that enable you to programmatically change the map,
 * and fires events as users interact with it.
 *
 * You create a `Map` by specifying a `container` and other options, see {@link MapOptions} for the full list.
 * Then MapLibre GL JS initializes the map on the page and returns your `Map` object.
 *
 * @group Main
 *
 * @example
 * ```ts
 * let map = new Map({
 *   container: 'map',
 *   center: [-122.420679, 37.772537],
 *   zoom: 13,
 *   style: style_object,
 *   hash: true,
 *   transformRequest: (url, resourceType)=> {
 *     if(resourceType === 'Source' && url.startsWith('http://myHost')) {
 *       return {
 *        url: url.replace('http', 'https'),
 *        headers: { 'my-custom-header': true},
 *        credentials: 'include'  // Include cookies for cross-origin requests
 *      }
 *     }
 *   }
 * });
 * ```
 * @see [Display a map](https://maplibre.org/maplibre-gl-js/docs/examples/simple-map/)
 */
declare class Map$1 extends Camera {
	style: Style;
	painter: Painter;
	_container: HTMLElement;
	_canvasContainer: HTMLElement;
	_controlContainer: HTMLElement;
	_controlPositions: Record<string, HTMLElement>;
	_interactive: boolean;
	_showTileBoundaries: boolean;
	_showCollisionBoxes: boolean;
	_showPadding: boolean;
	_showOverdrawInspector: boolean;
	_repaint: boolean;
	_vertices: boolean;
	_canvas: HTMLCanvasElement;
	_maxTileCacheSize: number | null;
	_maxTileCacheZoomLevels: number;
	_frameRequest: AbortController;
	_styleDirty: boolean;
	_sourcesDirty: boolean;
	_placementDirty: boolean;
	_loaded: boolean;
	_idleTriggered: boolean;
	_fullyLoaded: boolean;
	_trackResize: boolean;
	_resizeObserver: ResizeObserver;
	_preserveDrawingBuffer: boolean;
	_failIfMajorPerformanceCaveat: boolean;
	_antialias: boolean;
	_refreshExpiredTiles: boolean;
	_hash: Hash;
	_delegatedListeners: Record<string, DelegatedListener[]>;
	_fadeDuration: number;
	_crossSourceCollisions: boolean;
	_crossFadingFactor: number;
	_collectResourceTiming: boolean;
	_renderTaskQueue: TaskQueue;
	_controls: Array<IControl>;
	_mapId: number;
	_localIdeographFontFamily: string | false;
	_validateStyle: boolean;
	_requestManager: RequestManager;
	_locale: typeof defaultLocale;
	_removed: boolean;
	_clickTolerance: number;
	_overridePixelRatio: number | null | undefined;
	_maxCanvasSize: [
		number,
		number
	];
	_terrainDataCallback: (e: MapStyleDataEvent | MapSourceDataEvent) => void;
	/**
	 * @internal
	 * image queue throttling handle. To be used later when clean up
	 */
	_imageQueueHandle: number;
	/**
	 * The map's {@link ScrollZoomHandler}, which implements zooming in and out with a scroll wheel or trackpad.
	 * Find more details and examples using `scrollZoom` in the {@link ScrollZoomHandler} section.
	 */
	scrollZoom: ScrollZoomHandler;
	/**
	 * The map's {@link BoxZoomHandler}, which implements zooming using a drag gesture with the Shift key pressed.
	 * Find more details and examples using `boxZoom` in the {@link BoxZoomHandler} section.
	 */
	boxZoom: BoxZoomHandler;
	/**
	 * The map's {@link DragRotateHandler}, which implements rotating the map while dragging with the right
	 * mouse button or with the Control key pressed. Find more details and examples using `dragRotate`
	 * in the {@link DragRotateHandler} section.
	 */
	dragRotate: DragRotateHandler;
	/**
	 * The map's {@link DragPanHandler}, which implements dragging the map with a mouse or touch gesture.
	 * Find more details and examples using `dragPan` in the {@link DragPanHandler} section.
	 */
	dragPan: DragPanHandler;
	/**
	 * The map's {@link KeyboardHandler}, which allows the user to zoom, rotate, and pan the map using keyboard
	 * shortcuts. Find more details and examples using `keyboard` in the {@link KeyboardHandler} section.
	 */
	keyboard: KeyboardHandler;
	/**
	 * The map's {@link DoubleClickZoomHandler}, which allows the user to zoom by double clicking.
	 * Find more details and examples using `doubleClickZoom` in the {@link DoubleClickZoomHandler} section.
	 */
	doubleClickZoom: DoubleClickZoomHandler;
	/**
	 * The map's {@link TwoFingersTouchZoomRotateHandler}, which allows the user to zoom or rotate the map with touch gestures.
	 * Find more details and examples using `touchZoomRotate` in the {@link TwoFingersTouchZoomRotateHandler} section.
	 */
	touchZoomRotate: TwoFingersTouchZoomRotateHandler;
	/**
	 * The map's {@link TwoFingersTouchPitchHandler}, which allows the user to pitch the map with touch gestures.
	 * Find more details and examples using `touchPitch` in the {@link TwoFingersTouchPitchHandler} section.
	 */
	touchPitch: TwoFingersTouchPitchHandler;
	/**
	 * The map's {@link CooperativeGesturesHandler}, which allows the user to see cooperative gesture info when user tries to zoom in/out.
	 * Find more details and examples using `cooperativeGestures` in the {@link CooperativeGesturesHandler} section.
	 */
	cooperativeGestures: CooperativeGesturesHandler;
	/**
	 * The map's property which determines whether to cancel, or retain, tiles from the current viewport which are still loading but which belong to a farther (smaller) zoom level than the current one.
	 * * If `true`, when zooming in, tiles which didn't manage to load for previous zoom levels will become canceled. This might save some computing resources for slower devices, but the map details might appear more abruptly at the end of the zoom.
	 * * If `false`, when zooming in, the previous zoom level(s) tiles will progressively appear, giving a smoother map details experience. However, more tiles will be rendered in a short period of time.
	 * @defaultValue true
	 */
	cancelPendingTileRequestsWhileZooming: boolean;
	constructor(options: MapOptions);
	/**
	 * @internal
	 * Returns a unique number for this map instance which is used for the MapLoadEvent
	 * to make sure we only fire one event per instantiated map object.
	 * @returns the uniq map ID
	 */
	_getMapId(): number;
	/**
	 * Adds an {@link IControl} to the map, calling `control.onAdd(this)`.
	 *
	 * An {@link ErrorEvent} will be fired if the image parameter is invald.
	 *
	 * @param control - The {@link IControl} to add.
	 * @param position - position on the map to which the control will be added.
	 * Valid values are `'top-left'`, `'top-right'`, `'bottom-left'`, and `'bottom-right'`. Defaults to `'top-right'`.
	 * @example
	 * Add zoom and rotation controls to the map.
	 * ```ts
	 * map.addControl(new NavigationControl());
	 * ```
	 * @see [Display map navigation controls](https://maplibre.org/maplibre-gl-js/docs/examples/navigation/)
	 */
	addControl(control: IControl, position?: ControlPosition): Map$1;
	/**
	 * Removes the control from the map.
	 *
	 * An {@link ErrorEvent} will be fired if the image parameter is invald.
	 *
	 * @param control - The {@link IControl} to remove.
	 * @example
	 * ```ts
	 * // Define a new navigation control.
	 * let navigation = new NavigationControl();
	 * // Add zoom and rotation controls to the map.
	 * map.addControl(navigation);
	 * // Remove zoom and rotation controls from the map.
	 * map.removeControl(navigation);
	 * ```
	 */
	removeControl(control: IControl): Map$1;
	/**
	 * Checks if a control exists on the map.
	 *
	 * @param control - The {@link IControl} to check.
	 * @returns true if map contains control.
	 * @example
	 * ```ts
	 * // Define a new navigation control.
	 * let navigation = new NavigationControl();
	 * // Add zoom and rotation controls to the map.
	 * map.addControl(navigation);
	 * // Check that the navigation control exists on the map.
	 * map.hasControl(navigation);
	 * ```
	 */
	hasControl(control: IControl): boolean;
	calculateCameraOptionsFromTo(from: LngLat, altitudeFrom: number, to: LngLat, altitudeTo?: number): CameraOptions;
	/**
	 * Resizes the map according to the dimensions of its
	 * `container` element.
	 *
	 * Checks if the map container size changed and updates the map if it has changed.
	 * This method must be called after the map's `container` is resized programmatically
	 * or when the map is shown after being initially hidden with CSS.
	 *
	 * Triggers the following events: `movestart`, `move`, `moveend`, and `resize`.
	 *
	 * @param eventData - Additional properties to be passed to `movestart`, `move`, `resize`, and `moveend`
	 * events that get triggered as a result of resize. This can be useful for differentiating the
	 * source of an event (for example, user-initiated or programmatically-triggered events).
	 * @example
	 * Resize the map when the map container is shown after being initially hidden with CSS.
	 * ```ts
	 * let mapDiv = document.getElementById('map');
	 * if (mapDiv.style.visibility === true) map.resize();
	 * ```
	 */
	resize(eventData?: any): Map$1;
	/**
	 * @internal
	 * Return the map's pixel ratio eventually scaled down to respect maxCanvasSize.
	 * Internally you should use this and not getPixelRatio().
	 */
	_getClampedPixelRatio(width: number, height: number): number;
	/**
	 * Returns the map's pixel ratio.
	 * Note that the pixel ratio actually applied may be lower to respect maxCanvasSize.
	 * @returns The pixel ratio.
	 */
	getPixelRatio(): number;
	/**
	 * Sets the map's pixel ratio. This allows to override `devicePixelRatio`.
	 * After this call, the canvas' `width` attribute will be `container.clientWidth * pixelRatio`
	 * and its height attribute will be `container.clientHeight * pixelRatio`.
	 * Set this to null to disable `devicePixelRatio` override.
	 * Note that the pixel ratio actually applied may be lower to respect maxCanvasSize.
	 * @param pixelRatio - The pixel ratio.
	 */
	setPixelRatio(pixelRatio: number): void;
	/**
	 * Returns the map's geographical bounds. When the bearing or pitch is non-zero, the visible region is not
	 * an axis-aligned rectangle, and the result is the smallest bounds that encompasses the visible region.
	 * @returns The geographical bounds of the map as {@link LngLatBounds}.
	 * @example
	 * ```ts
	 * let bounds = map.getBounds();
	 * ```
	 */
	getBounds(): LngLatBounds;
	/**
	 * Returns the maximum geographical bounds the map is constrained to, or `null` if none set.
	 * @returns The map object.
	 * @example
	 * ```ts
	 * let maxBounds = map.getMaxBounds();
	 * ```
	 */
	getMaxBounds(): LngLatBounds | null;
	/**
	 * Sets or clears the map's geographical bounds.
	 *
	 * Pan and zoom operations are constrained within these bounds.
	 * If a pan or zoom is performed that would
	 * display regions outside these bounds, the map will
	 * instead display a position and zoom level
	 * as close as possible to the operation's request while still
	 * remaining within the bounds.
	 *
	 * @param bounds - The maximum bounds to set. If `null` or `undefined` is provided, the function removes the map's maximum bounds.
	 * @example
	 * Define bounds that conform to the `LngLatBoundsLike` object as set the max bounds.
	 * ```ts
	 * let bounds = [
	 *   [-74.04728, 40.68392], // [west, south]
	 *   [-73.91058, 40.87764]  // [east, north]
	 * ];
	 * map.setMaxBounds(bounds);
	 * ```
	 */
	setMaxBounds(bounds?: LngLatBoundsLike | null): Map$1;
	/**
	 * Sets or clears the map's minimum zoom level.
	 * If the map's current zoom level is lower than the new minimum,
	 * the map will zoom to the new minimum.
	 *
	 * It is not always possible to zoom out and reach the set `minZoom`.
	 * Other factors such as map height may restrict zooming. For example,
	 * if the map is 512px tall it will not be possible to zoom below zoom 0
	 * no matter what the `minZoom` is set to.
	 *
	 * A {@link ErrorEvent} event will be fired if minZoom is out of bounds.
	 *
	 * @param minZoom - The minimum zoom level to set (-2 - 24).
	 * If `null` or `undefined` is provided, the function removes the current minimum zoom (i.e. sets it to -2).
	 * @example
	 * ```ts
	 * map.setMinZoom(12.25);
	 * ```
	 */
	setMinZoom(minZoom?: number | null): Map$1;
	/**
	 * Returns the map's minimum allowable zoom level.
	 *
	 * @returns minZoom
	 * @example
	 * ```ts
	 * let minZoom = map.getMinZoom();
	 * ```
	 */
	getMinZoom(): number;
	/**
	 * Sets or clears the map's maximum zoom level.
	 * If the map's current zoom level is higher than the new maximum,
	 * the map will zoom to the new maximum.
	 *
	 * A {@link ErrorEvent} event will be fired if minZoom is out of bounds.
	 *
	 * @param maxZoom - The maximum zoom level to set.
	 * If `null` or `undefined` is provided, the function removes the current maximum zoom (sets it to 22).
	 * @example
	 * ```ts
	 * map.setMaxZoom(18.75);
	 * ```
	 */
	setMaxZoom(maxZoom?: number | null): Map$1;
	/**
	 * Returns the map's maximum allowable zoom level.
	 *
	 * @returns The maxZoom
	 * @example
	 * ```ts
	 * let maxZoom = map.getMaxZoom();
	 * ```
	 */
	getMaxZoom(): number;
	/**
	 * Sets or clears the map's minimum pitch.
	 * If the map's current pitch is lower than the new minimum,
	 * the map will pitch to the new minimum.
	 *
	 * A {@link ErrorEvent} event will be fired if minPitch is out of bounds.
	 *
	 * @param minPitch - The minimum pitch to set (0-85). Values greater than 60 degrees are experimental and may result in rendering issues. If you encounter any, please raise an issue with details in the MapLibre project.
	 * If `null` or `undefined` is provided, the function removes the current minimum pitch (i.e. sets it to 0).
	 */
	setMinPitch(minPitch?: number | null): Map$1;
	/**
	 * Returns the map's minimum allowable pitch.
	 *
	 * @returns The minPitch
	 */
	getMinPitch(): number;
	/**
	 * Sets or clears the map's maximum pitch.
	 * If the map's current pitch is higher than the new maximum,
	 * the map will pitch to the new maximum.
	 *
	 * A {@link ErrorEvent} event will be fired if maxPitch is out of bounds.
	 *
	 * @param maxPitch - The maximum pitch to set (0-85). Values greater than 60 degrees are experimental and may result in rendering issues. If you encounter any, please raise an issue with details in the MapLibre project.
	 * If `null` or `undefined` is provided, the function removes the current maximum pitch (sets it to 60).
	 */
	setMaxPitch(maxPitch?: number | null): Map$1;
	/**
	 * Returns the map's maximum allowable pitch.
	 *
	 * @returns The maxPitch
	 */
	getMaxPitch(): number;
	/**
	 * Returns the state of `renderWorldCopies`. If `true`, multiple copies of the world will be rendered side by side beyond -180 and 180 degrees longitude. If set to `false`:
	 *
	 * - When the map is zoomed out far enough that a single representation of the world does not fill the map's entire
	 * container, there will be blank space beyond 180 and -180 degrees longitude.
	 * - Features that cross 180 and -180 degrees longitude will be cut in two (with one portion on the right edge of the
	 * map and the other on the left edge of the map) at every zoom level.
	 * @returns The renderWorldCopies
	 * @example
	 * ```ts
	 * let worldCopiesRendered = map.getRenderWorldCopies();
	 * ```
	 * @see [Render world copies](https://maplibre.org/maplibre-gl-js/docs/examples/render-world-copies/)
	 */
	getRenderWorldCopies(): boolean;
	/**
	 * Sets the state of `renderWorldCopies`.
	 *
	 * @param renderWorldCopies - If `true`, multiple copies of the world will be rendered side by side beyond -180 and 180 degrees longitude. If set to `false`:
	 *
	 * - When the map is zoomed out far enough that a single representation of the world does not fill the map's entire
	 * container, there will be blank space beyond 180 and -180 degrees longitude.
	 * - Features that cross 180 and -180 degrees longitude will be cut in two (with one portion on the right edge of the
	 * map and the other on the left edge of the map) at every zoom level.
	 *
	 * `undefined` is treated as `true`, `null` is treated as `false`.
	 * @example
	 * ```ts
	 * map.setRenderWorldCopies(true);
	 * ```
	 * @see [Render world copies](https://maplibre.org/maplibre-gl-js/docs/examples/render-world-copies/)
	 */
	setRenderWorldCopies(renderWorldCopies?: boolean | null): Map$1;
	/**
	 * Returns a [Point](https://github.com/mapbox/point-geometry) representing pixel coordinates, relative to the map's `container`,
	 * that correspond to the specified geographical location.
	 *
	 * @param lnglat - The geographical location to project.
	 * @returns The [Point](https://github.com/mapbox/point-geometry) corresponding to `lnglat`, relative to the map's `container`.
	 * @example
	 * ```ts
	 * let coordinate = [-122.420679, 37.772537];
	 * let point = map.project(coordinate);
	 * ```
	 */
	project(lnglat: LngLatLike): Point;
	/**
	 * Returns a {@link LngLat} representing geographical coordinates that correspond
	 * to the specified pixel coordinates.
	 *
	 * @param point - The pixel coordinates to unproject.
	 * @returns The {@link LngLat} corresponding to `point`.
	 * @example
	 * ```ts
	 * map.on('click', (e) => {
	 *   // When the map is clicked, get the geographic coordinate.
	 *   let coordinate = map.unproject(e.point);
	 * });
	 * ```
	 */
	unproject(point: PointLike): LngLat;
	/**
	 * Returns true if the map is panning, zooming, rotating, or pitching due to a camera animation or user gesture.
	 * @returns true if the map is moving.
	 * @example
	 * ```ts
	 * let isMoving = map.isMoving();
	 * ```
	 */
	isMoving(): boolean;
	/**
	 * Returns true if the map is zooming due to a camera animation or user gesture.
	 * @returns true if the map is zooming.
	 * @example
	 * ```ts
	 * let isZooming = map.isZooming();
	 * ```
	 */
	isZooming(): boolean;
	/**
	 * Returns true if the map is rotating due to a camera animation or user gesture.
	 * @returns true if the map is rotating.
	 * @example
	 * ```ts
	 * map.isRotating();
	 * ```
	 */
	isRotating(): boolean;
	_createDelegatedListener(type: keyof MapEventType | string, layerIds: string[], listener: Listener): DelegatedListener;
	_saveDelegatedListener(type: keyof MapEventType | string, delegatedListener: DelegatedListener): void;
	_removeDelegatedListener(type: string, layerIds: string[], listener: Listener): void;
	/**
	 * @event
	 * Adds a listener for events of a specified type, optionally limited to features in a specified style layer(s).
	 * See {@link MapEventType} and {@link MapLayerEventType} for a full list of events and their description.
	 *
	 * | Event                  | Compatible with `layerId` |
	 * |------------------------|---------------------------|
	 * | `mousedown`            | yes                       |
	 * | `mouseup`              | yes                       |
	 * | `mouseover`            | yes                       |
	 * | `mouseout`             | yes                       |
	 * | `mousemove`            | yes                       |
	 * | `mouseenter`           | yes (required)            |
	 * | `mouseleave`           | yes (required)            |
	 * | `click`                | yes                       |
	 * | `dblclick`             | yes                       |
	 * | `contextmenu`          | yes                       |
	 * | `touchstart`           | yes                       |
	 * | `touchend`             | yes                       |
	 * | `touchcancel`          | yes                       |
	 * | `wheel`                |                           |
	 * | `resize`               |                           |
	 * | `remove`               |                           |
	 * | `touchmove`            |                           |
	 * | `movestart`            |                           |
	 * | `move`                 |                           |
	 * | `moveend`              |                           |
	 * | `dragstart`            |                           |
	 * | `drag`                 |                           |
	 * | `dragend`              |                           |
	 * | `zoomstart`            |                           |
	 * | `zoom`                 |                           |
	 * | `zoomend`              |                           |
	 * | `rotatestart`          |                           |
	 * | `rotate`               |                           |
	 * | `rotateend`            |                           |
	 * | `pitchstart`           |                           |
	 * | `pitch`                |                           |
	 * | `pitchend`             |                           |
	 * | `boxzoomstart`         |                           |
	 * | `boxzoomend`           |                           |
	 * | `boxzoomcancel`        |                           |
	 * | `webglcontextlost`     |                           |
	 * | `webglcontextrestored` |                           |
	 * | `load`                 |                           |
	 * | `render`               |                           |
	 * | `idle`                 |                           |
	 * | `error`                |                           |
	 * | `data`                 |                           |
	 * | `styledata`            |                           |
	 * | `sourcedata`           |                           |
	 * | `dataloading`          |                           |
	 * | `styledataloading`     |                           |
	 * | `sourcedataloading`    |                           |
	 * | `styleimagemissing`    |                           |
	 * | `dataabort`            |                           |
	 * | `sourcedataabort`      |                           |
	 *
	 * @param type - The event type to listen for. Events compatible with the optional `layerId` parameter are triggered
	 * when the cursor enters a visible portion of the specified layer from outside that layer or outside the map canvas.
	 * @param layer - The ID of a style layer or a listener if no ID is provided. Event will only be triggered if its location
	 * is within a visible feature in this layer. The event will have a `features` property containing
	 * an array of the matching features. If `layer` is not supplied, the event will not have a `features` property.
	 * Please note that many event types are not compatible with the optional `layer` parameter.
	 * @param listener - The function to be called when the event is fired.
	 * @example
	 * ```ts
	 * // Set an event listener that will fire
	 * // when the map has finished loading
	 * map.on('load', () => {
	 *   // Once the map has finished loading,
	 *   // add a new layer
	 *   map.addLayer({
	 *     id: 'points-of-interest',
	 *     source: {
	 *       type: 'vector',
	 *       url: 'https://maplibre.org/maplibre-style-spec/'
	 *     },
	 *     'source-layer': 'poi_label',
	 *     type: 'circle',
	 *     paint: {
	 *       // MapLibre Style Specification paint properties
	 *     },
	 *     layout: {
	 *       // MapLibre Style Specification layout properties
	 *     }
	 *   });
	 * });
	 * ```
	 * @example
	 * ```ts
	 * // Set an event listener that will fire
	 * // when a feature on the countries layer of the map is clicked
	 * map.on('click', 'countries', (e) => {
	 *   new Popup()
	 *     .setLngLat(e.lngLat)
	 *     .setHTML(`Country name: ${e.features[0].properties.name}`)
	 *     .addTo(map);
	 * });
	 * ```
	 * @see [Display popup on click](https://maplibre.org/maplibre-gl-js/docs/examples/popup-on-click/)
	 * @see [Center the map on a clicked symbol](https://maplibre.org/maplibre-gl-js/docs/examples/center-on-symbol/)
	 * @see [Create a hover effect](https://maplibre.org/maplibre-gl-js/docs/examples/hover-styles/)
	 * @see [Create a draggable marker](https://maplibre.org/maplibre-gl-js/docs/examples/drag-a-point/)
	 */
	on<T extends keyof MapLayerEventType>(type: T, layer: string, listener: (ev: MapLayerEventType[T] & Object) => void): Map$1;
	/**
	 * Overload of the `on` method that allows to listen to events specifying multiple layers.
	 * @event
	 * @param type - The type of the event.
	 * @param layerIds - The array of style layer IDs.
	 * @param listener - The listener callback.
	 */
	on<T extends keyof MapLayerEventType>(type: T, layerIds: string[], listener: (ev: MapLayerEventType[T] & Object) => void): this;
	/**
	 * Overload of the `on` method that allows to listen to events without specifying a layer.
	 * @event
	 * @param type - The type of the event.
	 * @param listener - The listener callback.
	 */
	on<T extends keyof MapEventType>(type: T, listener: (ev: MapEventType[T] & Object) => void): this;
	/**
	 * Overload of the `on` method that allows to listen to events without specifying a layer.
	 * @event
	 * @param type - The type of the event.
	 * @param listener - The listener callback.
	 */
	on(type: keyof MapEventType | string, listener: Listener): this;
	/**
	 * Adds a listener that will be called only once to a specified event type, optionally limited to features in a specified style layer.
	 *
	 * @event
	 * @param type - The event type to listen for; one of `'mousedown'`, `'mouseup'`, `'click'`, `'dblclick'`,
	 * `'mousemove'`, `'mouseenter'`, `'mouseleave'`, `'mouseover'`, `'mouseout'`, `'contextmenu'`, `'touchstart'`,
	 * `'touchend'`, or `'touchcancel'`. `mouseenter` and `mouseover` events are triggered when the cursor enters
	 * a visible portion of the specified layer from outside that layer or outside the map canvas. `mouseleave`
	 * and `mouseout` events are triggered when the cursor leaves a visible portion of the specified layer, or leaves
	 * the map canvas.
	 * @param layer - The ID of a style layer or a listener if no ID is provided. Only events whose location is within a visible
	 * feature in this layer will trigger the listener. The event will have a `features` property containing
	 * an array of the matching features.
	 * @param listener - The function to be called when the event is fired.
	 * @returns `this` if listener is provided, promise otherwise to allow easier usage of async/await
	 */
	once<T extends keyof MapLayerEventType>(type: T, layer: string, listener?: (ev: MapLayerEventType[T] & Object) => void): this | Promise<MapLayerEventType[T] & Object>;
	/**
	 * Overload of the `once` method that allows to listen to events specifying multiple layers.
	 * @event
	 * @param type - The type of the event.
	 * @param layerIds - The array of style layer IDs.
	 * @param listener - The listener callback.
	 */
	once<T extends keyof MapLayerEventType>(type: T, layerIds: string[], listener?: (ev: MapLayerEventType[T] & Object) => void): this | Promise<any>;
	/**
	 * Overload of the `once` method that allows to listen to events without specifying a layer.
	 * @event
	 * @param type - The type of the event.
	 * @param listener - The listener callback.
	 */
	once<T extends keyof MapEventType>(type: T, listener?: (ev: MapEventType[T] & Object) => void): this | Promise<any>;
	/**
	 * Overload of the `once` method that allows to listen to events without specifying a layer.
	 * @event
	 * @param type - The type of the event.
	 * @param listener - The listener callback.
	 */
	once(type: keyof MapEventType | string, listener?: Listener): this | Promise<any>;
	/**
	 * Removes an event listener for events previously added with `Map#on`.
	 *
	 * @event
	 * @param type - The event type previously used to install the listener.
	 * @param layer - The layer ID or listener previously used to install the listener.
	 * @param listener - The function previously installed as a listener.
	 */
	off<T extends keyof MapLayerEventType>(type: T, layer: string, listener: (ev: MapLayerEventType[T] & Object) => void): this;
	/**
	 * Overload of the `off` method that allows to remove an event created with multiple layers.
	 * Provide the same layer IDs as to `on` or `once`, when the listener was registered.
	 * @event
	 * @param type - The type of the event.
	 * @param layers - The layer IDs previously used to install the listener.
	 * @param listener - The function previously installed as a listener.
	 */
	off<T extends keyof MapLayerEventType>(type: T, layers: string[], listener: (ev: MapLayerEventType[T] & Object) => void): this;
	/**
	 * Overload of the `off` method that allows to remove an event created without specifying a layer.
	 * @event
	 * @param type - The type of the event.
	 * @param listener - The function previously installed as a listener.
	 */
	off<T extends keyof MapEventType>(type: T, listener: (ev: MapEventType[T] & Object) => void): this;
	/**
	 * Overload of the `off` method that allows to remove an event created without specifying a layer.
	 * @event
	 * @param type - The type of the event.
	 * @param listener - The function previously installed as a listener.
	 */
	off(type: keyof MapEventType | string, listener: Listener): this;
	/**
	 * Returns an array of MapGeoJSONFeature objects
	 * representing visible features that satisfy the query parameters.
	 *
	 * @param geometryOrOptions - (optional) The geometry of the query region:
	 * either a single point or southwest and northeast points describing a bounding box.
	 * Omitting this parameter (i.e. calling {@link Map#queryRenderedFeatures} with zero arguments,
	 * or with only a `options` argument) is equivalent to passing a bounding box encompassing the entire
	 * map viewport.
	 * The geometryOrOptions can receive a {@link QueryRenderedFeaturesOptions} only to support a situation where the function receives only one parameter which is the options parameter.
	 * @param options - (optional) Options object.
	 *
	 * @returns An array of MapGeoJSONFeature objects.
	 *
	 * The `properties` value of each returned feature object contains the properties of its source feature. For GeoJSON sources, only
	 * string and numeric property values are supported (i.e. `null`, `Array`, and `Object` values are not supported).
	 *
	 * Each feature includes top-level `layer`, `source`, and `sourceLayer` properties. The `layer` property is an object
	 * representing the style layer to  which the feature belongs. Layout and paint properties in this object contain values
	 * which are fully evaluated for the given zoom level and feature.
	 *
	 * Only features that are currently rendered are included. Some features will **not** be included, like:
	 *
	 * - Features from layers whose `visibility` property is `"none"`.
	 * - Features from layers whose zoom range excludes the current zoom level.
	 * - Symbol features that have been hidden due to text or icon collision.
	 *
	 * Features from all other layers are included, including features that may have no visible
	 * contribution to the rendered result; for example, because the layer's opacity or color alpha component is set to
	 * 0.
	 *
	 * The topmost rendered feature appears first in the returned array, and subsequent features are sorted by
	 * descending z-order. Features that are rendered multiple times (due to wrapping across the antemeridian at low
	 * zoom levels) are returned only once (though subject to the following caveat).
	 *
	 * Because features come from tiled vector data or GeoJSON data that is converted to tiles internally, feature
	 * geometries may be split or duplicated across tile boundaries and, as a result, features may appear multiple
	 * times in query results. For example, suppose there is a highway running through the bounding rectangle of a query.
	 * The results of the query will be those parts of the highway that lie within the map tiles covering the bounding
	 * rectangle, even if the highway extends into other tiles, and the portion of the highway within each map tile
	 * will be returned as a separate feature. Similarly, a point feature near a tile boundary may appear in multiple
	 * tiles due to tile buffering.
	 *
	 * @example
	 * Find all features at a point
	 * ```ts
	 * let features = map.queryRenderedFeatures(
	 *   [20, 35],
	 *   { layers: ['my-layer-name'] }
	 * );
	 * ```
	 *
	 * @example
	 * Find all features within a static bounding box
	 * ```ts
	 * let features = map.queryRenderedFeatures(
	 *   [[10, 20], [30, 50]],
	 *   { layers: ['my-layer-name'] }
	 * );
	 * ```
	 *
	 * @example
	 * Find all features within a bounding box around a point
	 * ```ts
	 * let width = 10;
	 * let height = 20;
	 * let features = map.queryRenderedFeatures([
	 *   [point.x - width / 2, point.y - height / 2],
	 *   [point.x + width / 2, point.y + height / 2]
	 * ], { layers: ['my-layer-name'] });
	 * ```
	 *
	 * @example
	 * Query all rendered features from a single layer
	 * ```ts
	 * let features = map.queryRenderedFeatures({ layers: ['my-layer-name'] });
	 * ```
	 * @see [Get features under the mouse pointer](https://maplibre.org/maplibre-gl-js/docs/examples/queryrenderedfeatures/)
	 */
	queryRenderedFeatures(geometryOrOptions?: PointLike | [
		PointLike,
		PointLike
	] | QueryRenderedFeaturesOptions, options?: QueryRenderedFeaturesOptions): MapGeoJSONFeature[];
	/**
	 * Returns an array of MapGeoJSONFeature objects
	 * representing features within the specified vector tile or GeoJSON source that satisfy the query parameters.
	 *
	 * @param sourceId - The ID of the vector tile or GeoJSON source to query.
	 * @param parameters - The options object.
	 * @returns An array of MapGeoJSONFeature objects.
	 *
	 * In contrast to {@link Map#queryRenderedFeatures}, this function returns all features matching the query parameters,
	 * whether or not they are rendered by the current style (i.e. visible). The domain of the query includes all currently-loaded
	 * vector tiles and GeoJSON source tiles: this function does not check tiles outside the currently
	 * visible viewport.
	 *
	 * Because features come from tiled vector data or GeoJSON data that is converted to tiles internally, feature
	 * geometries may be split or duplicated across tile boundaries and, as a result, features may appear multiple
	 * times in query results. For example, suppose there is a highway running through the bounding rectangle of a query.
	 * The results of the query will be those parts of the highway that lie within the map tiles covering the bounding
	 * rectangle, even if the highway extends into other tiles, and the portion of the highway within each map tile
	 * will be returned as a separate feature. Similarly, a point feature near a tile boundary may appear in multiple
	 * tiles due to tile buffering.
	 *
	 * @example
	 * Find all features in one source layer in a vector source
	 * ```ts
	 * let features = map.querySourceFeatures('your-source-id', {
	 *   sourceLayer: 'your-source-layer'
	 * });
	 * ```
	 *
	 */
	querySourceFeatures(sourceId: string, parameters?: QuerySourceFeatureOptions | null): MapGeoJSONFeature[];
	/**
	 * Updates the map's MapLibre style object with a new value.
	 *
	 * If a style is already set when this is used and options.diff is set to true, the map renderer will attempt to compare the given style
	 * against the map's current state and perform only the changes necessary to make the map style match the desired state. Changes in sprites
	 * (images used for icons and patterns) and glyphs (fonts for label text) **cannot** be diffed. If the sprites or fonts used in the current
	 * style and the given style are different in any way, the map renderer will force a full update, removing the current style and building
	 * the given one from scratch.
	 *
	 *
	 * @param style - A JSON object conforming to the schema described in the
	 * [MapLibre Style Specification](https://maplibre.org/maplibre-style-spec/), or a URL to such JSON.
	 * @param options - The options object.
	 *
	 * @example
	 * ```ts
	 * map.setStyle("https://demotiles.maplibre.org/style.json");
	 *
	 * map.setStyle('https://demotiles.maplibre.org/style.json', {
	 *   transformStyle: (previousStyle, nextStyle) => ({
	 *       ...nextStyle,
	 *       sources: {
	 *           ...nextStyle.sources,
	 *           // copy a source from previous style
	 *           'osm': previousStyle.sources.osm
	 *       },
	 *       layers: [
	 *           // background layer
	 *           nextStyle.layers[0],
	 *           // copy a layer from previous style
	 *           previousStyle.layers[0],
	 *           // other layers from the next style
	 *           ...nextStyle.layers.slice(1).map(layer => {
	 *               // hide the layers we don't need from demotiles style
	 *               if (layer.id.startsWith('geolines')) {
	 *                   layer.layout = {...layer.layout || {}, visibility: 'none'};
	 *               // filter out US polygons
	 *               } else if (layer.id.startsWith('coastline') || layer.id.startsWith('countries')) {
	 *                   layer.filter = ['!=', ['get', 'ADM0_A3'], 'USA'];
	 *               }
	 *               return layer;
	 *           })
	 *       ]
	 *   })
	 * });
	 * ```
	 */
	setStyle(style: StyleSpecification | string | null, options?: StyleSwapOptions & StyleOptions): this;
	/**
	 *  Updates the requestManager's transform request with a new function
	 *
	 * @param transformRequest - A callback run before the Map makes a request for an external URL. The callback can be used to modify the url, set headers, or set the credentials property for cross-origin requests.
	 * Expected to return an object with a `url` property and optionally `headers` and `credentials` properties
	 *
	 * @example
	 * ```ts
	 * map.setTransformRequest((url: string, resourceType: string) => {});
	 * ```
	 */
	setTransformRequest(transformRequest: RequestTransformFunction): this;
	_getUIString(key: keyof typeof defaultLocale): string;
	_updateStyle(style: StyleSpecification | string | null, options?: StyleSwapOptions & StyleOptions): this;
	_lazyInitEmptyStyle(): void;
	_diffStyle(style: StyleSpecification | string, options?: StyleSwapOptions & StyleOptions): void;
	_updateDiff(style: StyleSpecification, options?: StyleSwapOptions & StyleOptions): void;
	/**
	 * Returns the map's MapLibre style object, a JSON object which can be used to recreate the map's style.
	 *
	 * @returns The map's style JSON object.
	 *
	 * @example
	 * ```ts
	 * let styleJson = map.getStyle();
	 * ```
	 *
	 */
	getStyle(): StyleSpecification;
	/**
	 * Returns a Boolean indicating whether the map's style is fully loaded.
	 *
	 * @returns A Boolean indicating whether the style is fully loaded.
	 *
	 * @example
	 * ```ts
	 * let styleLoadStatus = map.isStyleLoaded();
	 * ```
	 */
	isStyleLoaded(): boolean | void;
	/**
	 * Adds a source to the map's style.
	 *
	 * Events triggered:
	 *
	 * Triggers the `source.add` event.
	 *
	 * @param id - The ID of the source to add. Must not conflict with existing sources.
	 * @param source - The source object, conforming to the
	 * MapLibre Style Specification's [source definition](https://maplibre.org/maplibre-style-spec/sources) or
	 * {@link CanvasSourceSpecification}.
	 * @example
	 * ```ts
	 * map.addSource('my-data', {
	 *   type: 'vector',
	 *   url: 'https://demotiles.maplibre.org/tiles/tiles.json'
	 * });
	 * ```
	 * @example
	 * ```ts
	 * map.addSource('my-data', {
	 *   "type": "geojson",
	 *   "data": {
	 *     "type": "Feature",
	 *     "geometry": {
	 *       "type": "Point",
	 *       "coordinates": [-77.0323, 38.9131]
	 *     },
	 *     "properties": {
	 *       "title": "Mapbox DC",
	 *       "marker-symbol": "monument"
	 *     }
	 *   }
	 * });
	 * ```
	 * @see GeoJSON source: [Add live realtime data](https://maplibre.org/maplibre-gl-js/docs/examples/live-geojson/)
	 */
	addSource(id: string, source: SourceSpecification | CanvasSourceSpecification): this;
	/**
	 * Returns a Boolean indicating whether the source is loaded. Returns `true` if the source with
	 * the given ID in the map's style has no outstanding network requests, otherwise `false`.
	 *
	 * A {@link ErrorEvent} event will be fired if there is no source wit the specified ID.
	 *
	 * @param id - The ID of the source to be checked.
	 * @returns A Boolean indicating whether the source is loaded.
	 * @example
	 * ```ts
	 * let sourceLoaded = map.isSourceLoaded('bathymetry-data');
	 * ```
	 */
	isSourceLoaded(id: string): boolean;
	/**
	 * Loads a 3D terrain mesh, based on a "raster-dem" source.
	 *
	 * Triggers the `terrain` event.
	 *
	 * @param options - Options object.
	 * @example
	 * ```ts
	 * map.setTerrain({ source: 'terrain' });
	 * ```
	 */
	setTerrain(options: TerrainSpecification | null): this;
	/**
	 * Get the terrain-options if terrain is loaded
	 * @returns the TerrainSpecification passed to setTerrain
	 * @example
	 * ```ts
	 * map.getTerrain(); // { source: 'terrain' };
	 * ```
	 */
	getTerrain(): TerrainSpecification | null;
	/**
	 * Returns a Boolean indicating whether all tiles in the viewport from all sources on
	 * the style are loaded.
	 *
	 * @returns A Boolean indicating whether all tiles are loaded.
	 * @example
	 * ```ts
	 * let tilesLoaded = map.areTilesLoaded();
	 * ```
	 */
	areTilesLoaded(): boolean;
	/**
	 * Removes a source from the map's style.
	 *
	 * @param id - The ID of the source to remove.
	 * @example
	 * ```ts
	 * map.removeSource('bathymetry-data');
	 * ```
	 */
	removeSource(id: string): Map$1;
	/**
	 * Returns the source with the specified ID in the map's style.
	 *
	 * This method is often used to update a source using the instance members for the relevant
	 * source type as defined in classes that derive from {@link Source}.
	 * For example, setting the `data` for a GeoJSON source or updating the `url` and `coordinates`
	 * of an image source.
	 *
	 * @param id - The ID of the source to get.
	 * @returns The style source with the specified ID or `undefined` if the ID
	 * corresponds to no existing sources.
	 * The shape of the object varies by source type.
	 * A list of options for each source type is available on the MapLibre Style Specification's
	 * [Sources](https://maplibre.org/maplibre-style-spec/sources/) page.
	 * @example
	 * ```ts
	 * let sourceObject = map.getSource('points');
	 * ```
	 * @see [Create a draggable point](https://maplibre.org/maplibre-gl-js/docs/examples/drag-a-point/)
	 * @see [Animate a point](https://maplibre.org/maplibre-gl-js/docs/examples/animate-point-along-line/)
	 * @see [Add live realtime data](https://maplibre.org/maplibre-gl-js/docs/examples/live-geojson/)
	 */
	getSource<TSource extends Source>(id: string): TSource | undefined;
	/**
	 * Add an image to the style. This image can be displayed on the map like any other icon in the style's
	 * sprite using the image's ID with
	 * [`icon-image`](https://maplibre.org/maplibre-style-spec/layers/#layout-symbol-icon-image),
	 * [`background-pattern`](https://maplibre.org/maplibre-style-spec/layers/#paint-background-background-pattern),
	 * [`fill-pattern`](https://maplibre.org/maplibre-style-spec/layers/#paint-fill-fill-pattern),
	 * or [`line-pattern`](https://maplibre.org/maplibre-style-spec/layers/#paint-line-line-pattern).
	 *
	 * A {@link ErrorEvent} event will be fired if the image parameter is invalid or there is not enough space in the sprite to add this image.
	 *
	 * @param id - The ID of the image.
	 * @param image - The image as an `HTMLImageElement`, `ImageData`, `ImageBitmap` or object with `width`, `height`, and `data`
	 * properties with the same format as `ImageData`.
	 * @param options - Options object.
	 * @example
	 * ```ts
	 * // If the style's sprite does not already contain an image with ID 'cat',
	 * // add the image 'cat-icon.png' to the style's sprite with the ID 'cat'.
	 * const image = await map.loadImage('https://upload.wikimedia.org/wikipedia/commons/thumb/6/60/Cat_silhouette.svg/400px-Cat_silhouette.svg.png');
	 * if (!map.hasImage('cat')) map.addImage('cat', image.data);
	 *
	 * // Add a stretchable image that can be used with `icon-text-fit`
	 * // In this example, the image is 600px wide by 400px high.
	 * const image = await map.loadImage('https://upload.wikimedia.org/wikipedia/commons/8/89/Black_and_White_Boxed_%28bordered%29.png');
	 * if (map.hasImage('border-image')) return;
	 * map.addImage('border-image', image.data, {
	 *     content: [16, 16, 300, 384], // place text over left half of image, avoiding the 16px border
	 *     stretchX: [[16, 584]], // stretch everything horizontally except the 16px border
	 *     stretchY: [[16, 384]], // stretch everything vertically except the 16px border
	 * });
	 * ```
	 * @see Use `HTMLImageElement`: [Add an icon to the map](https://maplibre.org/maplibre-gl-js/docs/examples/add-image/)
	 * @see Use `ImageData`: [Add a generated icon to the map](https://maplibre.org/maplibre-gl-js/docs/examples/add-image-generated/)
	 */
	addImage(id: string, image: HTMLImageElement | ImageBitmap | ImageData | {
		width: number;
		height: number;
		data: Uint8Array | Uint8ClampedArray;
	} | StyleImageInterface, options?: Partial<StyleImageMetadata>): this;
	/**
	 * Update an existing image in a style. This image can be displayed on the map like any other icon in the style's
	 * sprite using the image's ID with
	 * [`icon-image`](https://maplibre.org/maplibre-style-spec/layers/#layout-symbol-icon-image),
	 * [`background-pattern`](https://maplibre.org/maplibre-style-spec/layers/#paint-background-background-pattern),
	 * [`fill-pattern`](https://maplibre.org/maplibre-style-spec/layers/#paint-fill-fill-pattern),
	 * or [`line-pattern`](https://maplibre.org/maplibre-style-spec/layers/#paint-line-line-pattern).
	 *
	 * An {@link ErrorEvent} will be fired if the image parameter is invald.
	 *
	 * @param id - The ID of the image.
	 * @param image - The image as an `HTMLImageElement`, `ImageData`, `ImageBitmap` or object with `width`, `height`, and `data`
	 * properties with the same format as `ImageData`.
	 * @example
	 * ```ts
	 * // If an image with the ID 'cat' already exists in the style's sprite,
	 * // replace that image with a new image, 'other-cat-icon.png'.
	 * if (map.hasImage('cat')) map.updateImage('cat', './other-cat-icon.png');
	 * ```
	 */
	updateImage(id: string, image: HTMLImageElement | ImageBitmap | ImageData | {
		width: number;
		height: number;
		data: Uint8Array | Uint8ClampedArray;
	} | StyleImageInterface): this;
	/**
	 * Returns an image, specified by ID, currently available in the map.
	 * This includes both images from the style's original sprite
	 * and any images that have been added at runtime using {@link Map#addImage}.
	 *
	 * @param id - The ID of the image.
	 * @returns An image in the map with the specified ID.
	 *
	 * @example
	 * ```ts
	 * let coffeeShopIcon = map.getImage("coffee_cup");
	 * ```
	 */
	getImage(id: string): StyleImage;
	/**
	 * Check whether or not an image with a specific ID exists in the style. This checks both images
	 * in the style's original sprite and any images
	 * that have been added at runtime using {@link Map#addImage}.
	 *
	 * An {@link ErrorEvent} will be fired if the image parameter is invald.
	 *
	 * @param id - The ID of the image.
	 *
	 * @returns A Boolean indicating whether the image exists.
	 * @example
	 * Check if an image with the ID 'cat' exists in the style's sprite.
	 * ```ts
	 * let catIconExists = map.hasImage('cat');
	 * ```
	 */
	hasImage(id: string): boolean;
	/**
	 * Remove an image from a style. This can be an image from the style's original
	 * sprite or any images
	 * that have been added at runtime using {@link Map#addImage}.
	 *
	 * @param id - The ID of the image.
	 *
	 * @example
	 * ```ts
	 * // If an image with the ID 'cat' exists in
	 * // the style's sprite, remove it.
	 * if (map.hasImage('cat')) map.removeImage('cat');
	 * ```
	 */
	removeImage(id: string): void;
	/**
	 * Load an image from an external URL to be used with {@link Map#addImage}. External
	 * domains must support [CORS](https://developer.mozilla.org/en-US/docs/Web/HTTP/Access_control_CORS).
	 *
	 * @param url - The URL of the image file. Image file must be in png, webp, or jpg format.
	 * @returns a promise that is resolved when the image is loaded
	 *
	 * @example
	 * Load an image from an external URL.
	 * ```ts
	 * const response = await map.loadImage('https://picsum.photos/50/50');
	 * // Add the loaded image to the style's sprite with the ID 'photo'.
	 * map.addImage('photo', response.data);
	 * ```
	 * @see [Add an icon to the map](https://maplibre.org/maplibre-gl-js/docs/examples/add-image/)
	 */
	loadImage(url: string): Promise<GetResourceResponse<HTMLImageElement | ImageBitmap>>;
	/**
	 * Returns an Array of strings containing the IDs of all images currently available in the map.
	 * This includes both images from the style's original sprite
	 * and any images that have been added at runtime using {@link Map#addImage}.
	 *
	 * @returns An Array of strings containing the names of all sprites/images currently available in the map.
	 *
	 * @example
	 * ```ts
	 * let allImages = map.listImages();
	 * ```
	 */
	listImages(): Array<string>;
	/**
	 * Adds a [MapLibre style layer](https://maplibre.org/maplibre-style-spec/layers)
	 * to the map's style.
	 *
	 * A layer defines how data from a specified source will be styled. Read more about layer types
	 * and available paint and layout properties in the [MapLibre Style Specification](https://maplibre.org/maplibre-style-spec/layers).
	 *
	 * @param layer - The layer to add,
	 * conforming to either the MapLibre Style Specification's [layer definition](https://maplibre.org/maplibre-style-spec/layers) or,
	 * less commonly, the {@link CustomLayerInterface} specification. Can also be a layer definition with an embedded source definition.
	 * The MapLibre Style Specification's layer definition is appropriate for most layers.
	 *
	 * @param beforeId - The ID of an existing layer to insert the new layer before,
	 * resulting in the new layer appearing visually beneath the existing layer.
	 * If this argument is not specified, the layer will be appended to the end of the layers array
	 * and appear visually above all other layers.
	 *
	 * @example
	 * Add a circle layer with a vector source
	 * ```ts
	 * map.addLayer({
	 *   id: 'points-of-interest',
	 *   source: {
	 *     type: 'vector',
	 *     url: 'https://demotiles.maplibre.org/tiles/tiles.json'
	 *   },
	 *   'source-layer': 'poi_label',
	 *   type: 'circle',
	 *   paint: {
	 *     // MapLibre Style Specification paint properties
	 *   },
	 *   layout: {
	 *     // MapLibre Style Specification layout properties
	 *   }
	 * });
	 * ```
	 *
	 * @example
	 * Define a source before using it to create a new layer
	 * ```ts
	 * map.addSource('state-data', {
	 *   type: 'geojson',
	 *   data: 'path/to/data.geojson'
	 * });
	 *
	 * map.addLayer({
	 *   id: 'states',
	 *   // References the GeoJSON source defined above
	 *   // and does not require a `source-layer`
	 *   source: 'state-data',
	 *   type: 'symbol',
	 *   layout: {
	 *     // Set the label content to the
	 *     // feature's `name` property
	 *     text-field: ['get', 'name']
	 *   }
	 * });
	 * ```
	 *
	 * @example
	 * Add a new symbol layer before an existing layer
	 * ```ts
	 * map.addLayer({
	 *   id: 'states',
	 *   // References a source that's already been defined
	 *   source: 'state-data',
	 *   type: 'symbol',
	 *   layout: {
	 *     // Set the label content to the
	 *     // feature's `name` property
	 *     text-field: ['get', 'name']
	 *   }
	 * // Add the layer before the existing `cities` layer
	 * }, 'cities');
	 * ```
	 * @see [Create and style clusters](https://maplibre.org/maplibre-gl-js/docs/examples/cluster/)
	 * @see [Add a vector tile source](https://maplibre.org/maplibre-gl-js/docs/examples/vector-source/)
	 * @see [Add a WMS source](https://maplibre.org/maplibre-gl-js/docs/examples/wms/)
	 */
	addLayer(layer: AddLayerObject, beforeId?: string): this;
	/**
	 * Moves a layer to a different z-position.
	 *
	 * @param id - The ID of the layer to move.
	 * @param beforeId - The ID of an existing layer to insert the new layer before. When viewing the map, the `id` layer will appear beneath the `beforeId` layer. If `beforeId` is omitted, the layer will be appended to the end of the layers array and appear above all other layers on the map.
	 *
	 * @example
	 * Move a layer with ID 'polygon' before the layer with ID 'country-label'. The `polygon` layer will appear beneath the `country-label` layer on the map.
	 * ```ts
	 * map.moveLayer('polygon', 'country-label');
	 * ```
	 */
	moveLayer(id: string, beforeId?: string): this;
	/**
	 * Removes the layer with the given ID from the map's style.
	 *
	 * An {@link ErrorEvent} will be fired if the image parameter is invald.
	 *
	 * @param id - The ID of the layer to remove
	 *
	 * @example
	 * If a layer with ID 'state-data' exists, remove it.
	 * ```ts
	 * if (map.getLayer('state-data')) map.removeLayer('state-data');
	 * ```
	 */
	removeLayer(id: string): this;
	/**
	 * Returns the layer with the specified ID in the map's style.
	 *
	 * @param id - The ID of the layer to get.
	 * @returns The layer with the specified ID, or `undefined`
	 * if the ID corresponds to no existing layers.
	 *
	 * @example
	 * ```ts
	 * let stateDataLayer = map.getLayer('state-data');
	 * ```
	 * @see [Filter symbols by toggling a list](https://maplibre.org/maplibre-gl-js/docs/examples/filter-markers/)
	 * @see [Filter symbols by text input](https://maplibre.org/maplibre-gl-js/docs/examples/filter-markers-by-input/)
	 */
	getLayer(id: string): StyleLayer | undefined;
	/**
	 * Return the ids of all layers currently in the style, including custom layers, in order.
	 *
	 * @returns ids of layers, in order
	 *
	 * @example
	 * ```ts
	 * const orderedLayerIds = map.getLayersOrder();
	 * ```
	 */
	getLayersOrder(): string[];
	/**
	 * Sets the zoom extent for the specified style layer. The zoom extent includes the
	 * [minimum zoom level](https://maplibre.org/maplibre-style-spec/layers/#minzoom)
	 * and [maximum zoom level](https://maplibre.org/maplibre-style-spec/layers/#maxzoom))
	 * at which the layer will be rendered.
	 *
	 * Note: For style layers using vector sources, style layers cannot be rendered at zoom levels lower than the
	 * minimum zoom level of the _source layer_ because the data does not exist at those zoom levels. If the minimum
	 * zoom level of the source layer is higher than the minimum zoom level defined in the style layer, the style
	 * layer will not be rendered at all zoom levels in the zoom range.
	 *
	 * @param layerId - The ID of the layer to which the zoom extent will be applied.
	 * @param minzoom - The minimum zoom to set (0-24).
	 * @param maxzoom - The maximum zoom to set (0-24).
	 *
	 * @example
	 * ```ts
	 * map.setLayerZoomRange('my-layer', 2, 5);
	 * ```
	 */
	setLayerZoomRange(layerId: string, minzoom: number, maxzoom: number): this;
	/**
	 * Sets the filter for the specified style layer.
	 *
	 * Filters control which features a style layer renders from its source.
	 * Any feature for which the filter expression evaluates to `true` will be
	 * rendered on the map. Those that are false will be hidden.
	 *
	 * Use `setFilter` to show a subset of your source data.
	 *
	 * To clear the filter, pass `null` or `undefined` as the second parameter.
	 *
	 * @param layerId - The ID of the layer to which the filter will be applied.
	 * @param filter - The filter, conforming to the MapLibre Style Specification's
	 * [filter definition](https://maplibre.org/maplibre-style-spec/layers/#filter).  If `null` or `undefined` is provided, the function removes any existing filter from the layer.
	 * @param options - Options object.
	 *
	 * @example
	 * Display only features with the 'name' property 'USA'
	 * ```ts
	 * map.setFilter('my-layer', ['==', ['get', 'name'], 'USA']);
	 * ```
	 * @example
	 * Display only features with five or more 'available-spots'
	 * ```ts
	 * map.setFilter('bike-docks', ['>=', ['get', 'available-spots'], 5]);
	 * ```
	 * @example
	 * Remove the filter for the 'bike-docks' style layer
	 * ```ts
	 * map.setFilter('bike-docks', null);
	 * ```
	 * @see [Create a timeline animation](https://maplibre.org/maplibre-gl-js/docs/examples/timeline-animation/)
	 */
	setFilter(layerId: string, filter?: FilterSpecification | null, options?: StyleSetterOptions): this;
	/**
	 * Returns the filter applied to the specified style layer.
	 *
	 * @param layerId - The ID of the style layer whose filter to get.
	 * @returns The layer's filter.
	 */
	getFilter(layerId: string): FilterSpecification | void;
	/**
	 * Sets the value of a paint property in the specified style layer.
	 *
	 * @param layerId - The ID of the layer to set the paint property in.
	 * @param name - The name of the paint property to set.
	 * @param value - The value of the paint property to set.
	 * Must be of a type appropriate for the property, as defined in the [MapLibre Style Specification](https://maplibre.org/maplibre-style-spec/).
	 * Pass `null` to unset the existing value.
	 * @param options - Options object.
	 * @example
	 * ```ts
	 * map.setPaintProperty('my-layer', 'fill-color', '#faafee');
	 * ```
	 * @see [Change a layer's color with buttons](https://maplibre.org/maplibre-gl-js/docs/examples/color-switcher/)
	 * @see [Create a draggable point](https://maplibre.org/maplibre-gl-js/docs/examples/drag-a-point/)
	 */
	setPaintProperty(layerId: string, name: string, value: any, options?: StyleSetterOptions): this;
	/**
	 * Returns the value of a paint property in the specified style layer.
	 *
	 * @param layerId - The ID of the layer to get the paint property from.
	 * @param name - The name of a paint property to get.
	 * @returns The value of the specified paint property.
	 */
	getPaintProperty(layerId: string, name: string): unknown;
	/**
	 * Sets the value of a layout property in the specified style layer.
	 *
	 * @param layerId - The ID of the layer to set the layout property in.
	 * @param name - The name of the layout property to set.
	 * @param value - The value of the layout property. Must be of a type appropriate for the property, as defined in the [MapLibre Style Specification](https://maplibre.org/maplibre-style-spec/).
	 * @param options - The options object.
	 * @example
	 * ```ts
	 * map.setLayoutProperty('my-layer', 'visibility', 'none');
	 * ```
	 */
	setLayoutProperty(layerId: string, name: string, value: any, options?: StyleSetterOptions): this;
	/**
	 * Returns the value of a layout property in the specified style layer.
	 *
	 * @param layerId - The ID of the layer to get the layout property from.
	 * @param name - The name of the layout property to get.
	 * @returns The value of the specified layout property.
	 */
	getLayoutProperty(layerId: string, name: string): any;
	/**
	 * Sets the value of the style's glyphs property.
	 *
	 * @param glyphsUrl - Glyph URL to set. Must conform to the [MapLibre Style Specification](https://maplibre.org/maplibre-style-spec/glyphs/).
	 * @param options - Options object.
	 * @example
	 * ```ts
	 * map.setGlyphs('https://demotiles.maplibre.org/font/{fontstack}/{range}.pbf');
	 * ```
	 */
	setGlyphs(glyphsUrl: string | null, options?: StyleSetterOptions): this;
	/**
	 * Returns the value of the style's glyphs URL
	 *
	 * @returns glyphs Style's glyphs url
	 */
	getGlyphs(): string | null;
	/**
	 * Adds a sprite to the map's style. Fires the `style` event.
	 *
	 * @param id - The ID of the sprite to add. Must not conflict with existing sprites.
	 * @param url - The URL to load the sprite from
	 * @param options - Options object.
	 * @example
	 * ```ts
	 * map.addSprite('sprite-two', 'http://example.com/sprite-two');
	 * ```
	 */
	addSprite(id: string, url: string, options?: StyleSetterOptions): this;
	/**
	 * Removes the sprite from the map's style. Fires the `style` event.
	 *
	 * @param id - The ID of the sprite to remove. If the sprite is declared as a single URL, the ID must be "default".
	 * @example
	 * ```ts
	 * map.removeSprite('sprite-two');
	 * map.removeSprite('default');
	 * ```
	 */
	removeSprite(id: string): this;
	/**
	 * Returns the as-is value of the style's sprite.
	 *
	 * @returns style's sprite list of id-url pairs
	 */
	getSprite(): {
		id: string;
		url: string;
	}[];
	/**
	 * Sets the value of the style's sprite property.
	 *
	 * @param spriteUrl - Sprite URL to set.
	 * @param options - Options object.
	 * @example
	 * ```ts
	 * map.setSprite('YOUR_SPRITE_URL');
	 * ```
	 */
	setSprite(spriteUrl: string | null, options?: StyleSetterOptions): this;
	/**
	 * Sets the any combination of light values.
	 *
	 * @param light - Light properties to set. Must conform to the [MapLibre Style Specification](https://maplibre.org/maplibre-style-spec/light).
	 * @param options - Options object.
	 *
	 * @example
	 * ```ts
	 * let layerVisibility = map.getLayoutProperty('my-layer', 'visibility');
	 * ```
	 */
	setLight(light: LightSpecification, options?: StyleSetterOptions): this;
	/**
	 * Returns the value of the light object.
	 *
	 * @returns light Light properties of the style.
	 */
	getLight(): LightSpecification;
	/**
	 * Loads sky and fog defined by {@link SkySpecification} onto the map.
	 * Note: The fog only shows when using the terrain 3D feature.
	 * @param sky - Sky properties to set. Must conform to the [MapLibre Style Specification](https://maplibre.org/maplibre-style-spec/sky/).
	 * @returns `this`
	 * @example
	 * ```ts
	 * map.setSky({ 'sky-color': '#00f' });
	 * ```
	 */
	setSky(sky: SkySpecification): this;
	/**
	 * Returns the value of the sky object.
	 *
	 * @returns the sky properties of the style.
	 * @example
	 * ```ts
	 * map.getSky();
	 * ```
	 */
	getSky(): SkySpecification;
	/**
	 * Sets the `state` of a feature.
	 * A feature's `state` is a set of user-defined key-value pairs that are assigned to a feature at runtime.
	 * When using this method, the `state` object is merged with any existing key-value pairs in the feature's state.
	 * Features are identified by their `feature.id` attribute, which can be any number or string.
	 *
	 * This method can only be used with sources that have a `feature.id` attribute. The `feature.id` attribute can be defined in three ways:
	 *
	 * - For vector or GeoJSON sources, including an `id` attribute in the original data file.
	 * - For vector or GeoJSON sources, using the [`promoteId`](https://maplibre.org/maplibre-style-spec/sources/#vector-promoteId) option at the time the source is defined.
	 * - For GeoJSON sources, using the [`generateId`](https://maplibre.org/maplibre-style-spec/sources/#geojson-generateId) option to auto-assign an `id` based on the feature's index in the source data. If you change feature data using `map.getSource('some id').setData(..)`, you may need to re-apply state taking into account updated `id` values.
	 *
	 * _Note: You can use the [`feature-state` expression](https://maplibre.org/maplibre-style-spec/expressions/#feature-state) to access the values in a feature's state object for the purposes of styling._
	 *
	 * @param feature - Feature identifier. Feature objects returned from
	 * {@link Map#queryRenderedFeatures} or event handlers can be used as feature identifiers.
	 * @param state - A set of key-value pairs. The values should be valid JSON types.
	 *
	 * @example
	 * ```ts
	 * // When the mouse moves over the `my-layer` layer, update
	 * // the feature state for the feature under the mouse
	 * map.on('mousemove', 'my-layer', (e) => {
	 *   if (e.features.length > 0) {
	 *     map.setFeatureState({
	 *       source: 'my-source',
	 *       sourceLayer: 'my-source-layer',
	 *       id: e.features[0].id,
	 *     }, {
	 *       hover: true
	 *     });
	 *   }
	 * });
	 * ```
	 * @see [Create a hover effect](https://maplibre.org/maplibre-gl-js/docs/examples/hover-styles/)
	 */
	setFeatureState(feature: FeatureIdentifier, state: any): this;
	/**
	 * Removes the `state` of a feature, setting it back to the default behavior.
	 * If only a `target.source` is specified, it will remove the state for all features from that source.
	 * If `target.id` is also specified, it will remove all keys for that feature's state.
	 * If `key` is also specified, it removes only that key from that feature's state.
	 * Features are identified by their `feature.id` attribute, which can be any number or string.
	 *
	 * @param target - Identifier of where to remove state. It can be a source, a feature, or a specific key of feature.
	 * Feature objects returned from {@link Map#queryRenderedFeatures} or event handlers can be used as feature identifiers.
	 * @param key - (optional) The key in the feature state to reset.
	 * @example
	 * Reset the entire state object for all features in the `my-source` source
	 * ```ts
	 * map.removeFeatureState({
	 *   source: 'my-source'
	 * });
	 * ```
	 *
	 * @example
	 * When the mouse leaves the `my-layer` layer,
	 * reset the entire state object for the
	 * feature under the mouse
	 * ```ts
	 * map.on('mouseleave', 'my-layer', (e) => {
	 *   map.removeFeatureState({
	 *     source: 'my-source',
	 *     sourceLayer: 'my-source-layer',
	 *     id: e.features[0].id
	 *   });
	 * });
	 * ```
	 *
	 * @example
	 * When the mouse leaves the `my-layer` layer,
	 * reset only the `hover` key-value pair in the
	 * state for the feature under the mouse
	 * ```ts
	 * map.on('mouseleave', 'my-layer', (e) => {
	 *   map.removeFeatureState({
	 *     source: 'my-source',
	 *     sourceLayer: 'my-source-layer',
	 *     id: e.features[0].id
	 *   }, 'hover');
	 * });
	 * ```
	 */
	removeFeatureState(target: FeatureIdentifier, key?: string): this;
	/**
	 * Gets the `state` of a feature.
	 * A feature's `state` is a set of user-defined key-value pairs that are assigned to a feature at runtime.
	 * Features are identified by their `feature.id` attribute, which can be any number or string.
	 *
	 * _Note: To access the values in a feature's state object for the purposes of styling the feature, use the [`feature-state` expression](https://maplibre.org/maplibre-style-spec/expressions/#feature-state)._
	 *
	 * @param feature - Feature identifier. Feature objects returned from
	 * {@link Map#queryRenderedFeatures} or event handlers can be used as feature identifiers.
	 * @returns The state of the feature: a set of key-value pairs that was assigned to the feature at runtime.
	 *
	 * @example
	 * When the mouse moves over the `my-layer` layer,
	 * get the feature state for the feature under the mouse
	 * ```ts
	 * map.on('mousemove', 'my-layer', (e) => {
	 *   if (e.features.length > 0) {
	 *     map.getFeatureState({
	 *       source: 'my-source',
	 *       sourceLayer: 'my-source-layer',
	 *       id: e.features[0].id
	 *     });
	 *   }
	 * });
	 * ```
	 */
	getFeatureState(feature: FeatureIdentifier): any;
	/**
	 * Returns the map's containing HTML element.
	 *
	 * @returns The map's container.
	 */
	getContainer(): HTMLElement;
	/**
	 * Returns the HTML element containing the map's `<canvas>` element.
	 *
	 * If you want to add non-GL overlays to the map, you should append them to this element.
	 *
	 * This is the element to which event bindings for map interactivity (such as panning and zooming) are
	 * attached. It will receive bubbled events from child elements such as the `<canvas>`, but not from
	 * map controls.
	 *
	 * @returns The container of the map's `<canvas>`.
	 * @see [Create a draggable point](https://maplibre.org/maplibre-gl-js/docs/examples/drag-a-point/)
	 */
	getCanvasContainer(): HTMLElement;
	/**
	 * Returns the map's `<canvas>` element.
	 *
	 * @returns The map's `<canvas>` element.
	 * @see [Measure distances](https://maplibre.org/maplibre-gl-js/docs/examples/measure/)
	 * @see [Display a popup on hover](https://maplibre.org/maplibre-gl-js/docs/examples/popup-on-hover/)
	 * @see [Center the map on a clicked symbol](https://maplibre.org/maplibre-gl-js/docs/examples/center-on-symbol/)
	 */
	getCanvas(): HTMLCanvasElement;
	_containerDimensions(): number[];
	_setupContainer(): void;
	_resizeCanvas(width: number, height: number, pixelRatio: number): void;
	_setupPainter(): void;
	_contextLost: (event: any) => void;
	_contextRestored: (event: any) => void;
	_onMapScroll: (event: any) => boolean;
	/**
	 * Returns a Boolean indicating whether the map is fully loaded.
	 *
	 * Returns `false` if the style is not yet fully loaded,
	 * or if there has been a change to the sources or style that
	 * has not yet fully loaded.
	 *
	 * @returns A Boolean indicating whether the map is fully loaded.
	 */
	loaded(): boolean;
	/**
	 * @internal
	 * Update this map's style and sources, and re-render the map.
	 *
	 * @param updateStyle - mark the map's style for reprocessing as
	 * well as its sources
	 */
	_update(updateStyle?: boolean): this;
	/**
	 * @internal
	 * Request that the given callback be executed during the next render
	 * frame.  Schedule a render frame if one is not already scheduled.
	 *
	 * @returns An id that can be used to cancel the callback
	 */
	_requestRenderFrame(callback: () => void): TaskID;
	_cancelRenderFrame(id: TaskID): void;
	/**
	 * @internal
	 * Call when a (re-)render of the map is required:
	 *
	 * - The style has changed (`setPaintProperty()`, etc.)
	 * - Source data has changed (e.g. tiles have finished loading)
	 * - The map has is moving (or just finished moving)
	 * - A transition is in progress
	 *
	 * @param paintStartTimeStamp - The time when the animation frame began executing.
	 */
	_render(paintStartTimeStamp: number): this;
	/**
	 * Force a synchronous redraw of the map.
	 * @example
	 * ```ts
	 * map.redraw();
	 * ```
	 */
	redraw(): this;
	/**
	 * Clean up and release all internal resources associated with this map.
	 *
	 * This includes DOM elements, event bindings, web workers, and WebGL resources.
	 *
	 * Use this method when you are done using the map and wish to ensure that it no
	 * longer consumes browser resources. Afterwards, you must not call any other
	 * methods on the map.
	 */
	remove(): void;
	/**
	 * Trigger the rendering of a single frame. Use this method with custom layers to
	 * repaint the map when the layer changes. Calling this multiple times before the
	 * next frame is rendered will still result in only a single frame being rendered.
	 * @example
	 * ```ts
	 * map.triggerRepaint();
	 * ```
	 * @see [Add a 3D model](https://maplibre.org/maplibre-gl-js/docs/examples/add-3d-model/)
	 * @see [Add an animated icon to the map](https://maplibre.org/maplibre-gl-js/docs/examples/add-image-animated/)
	 */
	triggerRepaint(): void;
	_onWindowOnline: () => void;
	/**
	 * Gets and sets a Boolean indicating whether the map will render an outline
	 * around each tile and the tile ID. These tile boundaries are useful for
	 * debugging.
	 *
	 * The uncompressed file size of the first vector source is drawn in the top left
	 * corner of each tile, next to the tile ID.
	 *
	 * @example
	 * ```ts
	 * map.showTileBoundaries = true;
	 * ```
	 */
	get showTileBoundaries(): boolean;
	set showTileBoundaries(value: boolean);
	/**
	 * Gets and sets a Boolean indicating whether the map will visualize
	 * the padding offsets.
	 */
	get showPadding(): boolean;
	set showPadding(value: boolean);
	/**
	 * Gets and sets a Boolean indicating whether the map will render boxes
	 * around all symbols in the data source, revealing which symbols
	 * were rendered or which were hidden due to collisions.
	 * This information is useful for debugging.
	 */
	get showCollisionBoxes(): boolean;
	set showCollisionBoxes(value: boolean);
	/**
	 * Gets and sets a Boolean indicating whether the map should color-code
	 * each fragment to show how many times it has been shaded.
	 * White fragments have been shaded 8 or more times.
	 * Black fragments have been shaded 0 times.
	 * This information is useful for debugging.
	 */
	get showOverdrawInspector(): boolean;
	set showOverdrawInspector(value: boolean);
	/**
	 * Gets and sets a Boolean indicating whether the map will
	 * continuously repaint. This information is useful for analyzing performance.
	 */
	get repaint(): boolean;
	set repaint(value: boolean);
	get vertices(): boolean;
	set vertices(value: boolean);
	/**
	 * Returns the package version of the library
	 * @returns Package version of the library
	 */
	get version(): string;
	/**
	 * Returns the elevation for the point where the camera is looking.
	 * This value corresponds to:
	 * "meters above sea level" * "exaggeration"
	 * @returns The elevation.
	 */
	getCameraTargetElevation(): number;
}
export interface OneFingerTouchRotateHandler extends DragMoveHandler<DragRotateResult, TouchEvent> {
}
export interface OneFingerTouchPitchHandler extends DragMoveHandler<DragPitchResult, TouchEvent> {
}
/**
 * The {@link NavigationControl} options object
 */
export type NavigationControlOptions = {
	/**
	 * If `true` the compass button is included.
	 */
	showCompass?: boolean;
	/**
	 * If `true` the zoom-in and zoom-out buttons are included.
	 */
	showZoom?: boolean;
	/**
	 * If `true` the pitch is visualized by rotating X-axis of compass.
	 */
	visualizePitch?: boolean;
};
/**
 * A `NavigationControl` control contains zoom buttons and a compass.
 *
 * @group Markers and Controls
 *
 * @example
 * ```ts
 * let nav = new NavigationControl();
 * map.addControl(nav, 'top-left');
 * ```
 * @see [Display map navigation controls](https://maplibre.org/maplibre-gl-js/docs/examples/navigation/)
 */
export declare class NavigationControl implements IControl {
	_map: Map$1;
	options: NavigationControlOptions;
	_container: HTMLElement;
	_zoomInButton: HTMLButtonElement;
	_zoomOutButton: HTMLButtonElement;
	_compass: HTMLButtonElement;
	_compassIcon: HTMLElement;
	_handler: MouseRotateWrapper;
	/**
	 * @param options - the control's options
	 */
	constructor(options?: NavigationControlOptions);
	_updateZoomButtons: () => void;
	_rotateCompassArrow: () => void;
	/** {@inheritDoc IControl.onAdd} */
	onAdd(map: Map$1): HTMLElement;
	/** {@inheritDoc IControl.onRemove} */
	onRemove(): void;
	_createButton(className: string, fn: (e?: any) => unknown): HTMLButtonElement;
	_setButtonTitle: (button: HTMLButtonElement, title: "ZoomIn" | "ZoomOut" | "ResetBearing") => void;
}
declare class MouseRotateWrapper {
	map: Map$1;
	_clickTolerance: number;
	element: HTMLElement;
	mouseRotate: MouseRotateHandler;
	touchRotate: OneFingerTouchRotateHandler;
	mousePitch: MousePitchHandler;
	touchPitch: OneFingerTouchPitchHandler;
	_startPos: Point;
	_lastPos: Point;
	constructor(map: Map$1, element: HTMLElement, pitch?: boolean);
	startMouse(e: MouseEvent, point: Point): void;
	startTouch(e: TouchEvent, point: Point): void;
	moveMouse(e: MouseEvent, point: Point): void;
	moveTouch(e: TouchEvent, point: Point): void;
	off(): void;
	offTemp(): void;
	mousedown: (e: MouseEvent) => void;
	mousemove: (e: MouseEvent) => void;
	mouseup: (e: MouseEvent) => void;
	touchstart: (e: TouchEvent) => void;
	touchmove: (e: TouchEvent) => void;
	touchend: (e: TouchEvent) => void;
	reset: () => void;
}
/**
 * Where to position the anchor.
 * Used by a popup and a marker.
 */
export type PositionAnchor = "center" | "top" | "bottom" | "left" | "right" | "top-left" | "top-right" | "bottom-left" | "bottom-right";
/**
 * A pixel offset specified as:
 *
 * - a single number specifying a distance from the location
 * - a {@link PointLike} specifying a constant offset
 * - an object of {@link Point}s specifying an offset for each anchor position
 *
 * Negative offsets indicate left and up.
 */
export type Offset = number | PointLike | {
	[_ in PositionAnchor]: PointLike;
};
/**
 * The {@link Popup} options object
 */
export type PopupOptions = {
	/**
	 * If `true`, a close button will appear in the top right corner of the popup.
	 * @defaultValue true
	 */
	closeButton?: boolean;
	/**
	 * If `true`, the popup will closed when the map is clicked.
	 * @defaultValue true
	 */
	closeOnClick?: boolean;
	/**
	 * If `true`, the popup will closed when the map moves.
	 * @defaultValue false
	 */
	closeOnMove?: boolean;
	/**
	 * If `true`, the popup will try to focus the first focusable element inside the popup.
	 * @defaultValue true
	 */
	focusAfterOpen?: boolean;
	/**
	 * A string indicating the part of the Popup that should
	 * be positioned closest to the coordinate set via {@link Popup#setLngLat}.
	 * Options are `'center'`, `'top'`, `'bottom'`, `'left'`, `'right'`, `'top-left'`,
	 * `'top-right'`, `'bottom-left'`, and `'bottom-right'`. If unset the anchor will be
	 * dynamically set to ensure the popup falls within the map container with a preference
	 * for `'bottom'`.
	 */
	anchor?: PositionAnchor;
	/**
	 * A pixel offset applied to the popup's location
	 */
	offset?: Offset;
	/**
	 * Space-separated CSS class names to add to popup container
	 */
	className?: string;
	/**
	 * A string that sets the CSS property of the popup's maximum width, eg `'300px'`.
	 * To ensure the popup resizes to fit its content, set this property to `'none'`.
	 * Available values can be found here: https://developer.mozilla.org/en-US/docs/Web/CSS/max-width
	 * @defaultValue '240px'
	 */
	maxWidth?: string;
	/**
	 * If `true`, rounding is disabled for placement of the popup, allowing for
	 * subpixel positioning and smoother movement when the popup is translated.
	 * @defaultValue false
	 */
	subpixelPositioning?: boolean;
};
/**
 * A popup component.
 *
 * @group Markers and Controls
 *
 *
 * @example
 * Create a popup
 * ```ts
 * let popup = new Popup();
 * // Set an event listener that will fire
 * // any time the popup is opened
 * popup.on('open', () => {
 *   console.log('popup was opened');
 * });
 * ```
 *
 * @example
 * Create a popup
 * ```ts
 * let popup = new Popup();
 * // Set an event listener that will fire
 * // any time the popup is closed
 * popup.on('close', () => {
 *   console.log('popup was closed');
 * });
 * ```
 *
 * @example
 * ```ts
 * let markerHeight = 50, markerRadius = 10, linearOffset = 25;
 * let popupOffsets = {
 *  'top': [0, 0],
 *  'top-left': [0,0],
 *  'top-right': [0,0],
 *  'bottom': [0, -markerHeight],
 *  'bottom-left': [linearOffset, (markerHeight - markerRadius + linearOffset) * -1],
 *  'bottom-right': [-linearOffset, (markerHeight - markerRadius + linearOffset) * -1],
 *  'left': [markerRadius, (markerHeight - markerRadius) * -1],
 *  'right': [-markerRadius, (markerHeight - markerRadius) * -1]
 *  };
 * let popup = new Popup({offset: popupOffsets, className: 'my-class'})
 *   .setLngLat(e.lngLat)
 *   .setHTML("<h1>Hello World!</h1>")
 *   .setMaxWidth("300px")
 *   .addTo(map);
 * ```
 * @see [Display a popup](https://maplibre.org/maplibre-gl-js/docs/examples/popup/)
 * @see [Display a popup on hover](https://maplibre.org/maplibre-gl-js/docs/examples/popup-on-hover/)
 * @see [Display a popup on click](https://maplibre.org/maplibre-gl-js/docs/examples/popup-on-click/)
 * @see [Attach a popup to a marker instance](https://maplibre.org/maplibre-gl-js/docs/examples/set-popup/)
 *
 * ## Events
 *
 * **Event** `open` of type {@link Event} will be fired when the popup is opened manually or programmatically.
 *
 * **Event** `close` of type {@link Event} will be fired when the popup is closed manually or programmatically.
 */
export declare class Popup extends Evented {
	_map: Map$1;
	options: PopupOptions;
	_content: HTMLElement;
	_container: HTMLElement;
	_closeButton: HTMLButtonElement;
	_tip: HTMLElement;
	_lngLat: LngLat;
	_trackPointer: boolean;
	_pos: Point;
	_flatPos: Point;
	/**
	 * @param options - the options
	 */
	constructor(options?: PopupOptions);
	/**
	 * Adds the popup to a map.
	 *
	 * @param map - The MapLibre GL JS map to add the popup to.
	 * @example
	 * ```ts
	 * new Popup()
	 *   .setLngLat([0, 0])
	 *   .setHTML("<h1>Null Island</h1>")
	 *   .addTo(map);
	 * ```
	 * @see [Display a popup](https://maplibre.org/maplibre-gl-js/docs/examples/popup/)
	 * @see [Display a popup on hover](https://maplibre.org/maplibre-gl-js/docs/examples/popup-on-hover/)
	 * @see [Display a popup on click](https://maplibre.org/maplibre-gl-js/docs/examples/popup-on-click/)
	 * @see [Show polygon information on click](https://maplibre.org/maplibre-gl-js/docs/examples/polygon-popup-on-click/)
	 */
	addTo(map: Map$1): this;
	/**
	 * @returns `true` if the popup is open, `false` if it is closed.
	 */
	isOpen(): boolean;
	/**
	 * Removes the popup from the map it has been added to.
	 *
	 * @example
	 * ```ts
	 * let popup = new Popup().addTo(map);
	 * popup.remove();
	 * ```
	 */
	remove: () => this;
	/**
	 * Returns the geographical location of the popup's anchor.
	 *
	 * The longitude of the result may differ by a multiple of 360 degrees from the longitude previously
	 * set by `setLngLat` because `Popup` wraps the anchor longitude across copies of the world to keep
	 * the popup on screen.
	 *
	 * @returns The geographical location of the popup's anchor.
	 */
	getLngLat(): LngLat;
	/**
	 * Sets the geographical location of the popup's anchor, and moves the popup to it. Replaces trackPointer() behavior.
	 *
	 * @param lnglat - The geographical location to set as the popup's anchor.
	 */
	setLngLat(lnglat: LngLatLike): this;
	/**
	 * Tracks the popup anchor to the cursor position on screens with a pointer device (it will be hidden on touchscreens). Replaces the `setLngLat` behavior.
	 * For most use cases, set `closeOnClick` and `closeButton` to `false`.
	 * @example
	 * ```ts
	 * let popup = new Popup({ closeOnClick: false, closeButton: false })
	 *   .setHTML("<h1>Hello World!</h1>")
	 *   .trackPointer()
	 *   .addTo(map);
	 * ```
	 */
	trackPointer(): this;
	/**
	 * Returns the `Popup`'s HTML element.
	 * @example
	 * Change the `Popup` element's font size
	 * ```ts
	 * let popup = new Popup()
	 *   .setLngLat([-96, 37.8])
	 *   .setHTML("<p>Hello World!</p>")
	 *   .addTo(map);
	 * let popupElem = popup.getElement();
	 * popupElem.style.fontSize = "25px";
	 * ```
	 * @returns element
	 */
	getElement(): HTMLElement;
	/**
	 * Sets the popup's content to a string of text.
	 *
	 * This function creates a [Text](https://developer.mozilla.org/en-US/docs/Web/API/Text) node in the DOM,
	 * so it cannot insert raw HTML. Use this method for security against XSS
	 * if the popup content is user-provided.
	 *
	 * @param text - Textual content for the popup.
	 * @example
	 * ```ts
	 * let popup = new Popup()
	 *   .setLngLat(e.lngLat)
	 *   .setText('Hello, world!')
	 *   .addTo(map);
	 * ```
	 */
	setText(text: string): this;
	/**
	 * Sets the popup's content to the HTML provided as a string.
	 *
	 * This method does not perform HTML filtering or sanitization, and must be
	 * used only with trusted content. Consider {@link Popup#setText} if
	 * the content is an untrusted text string.
	 *
	 * @param html - A string representing HTML content for the popup.
	 * @example
	 * ```ts
	 * let popup = new Popup()
	 *   .setLngLat(e.lngLat)
	 *   .setHTML("<h1>Hello World!</h1>")
	 *   .addTo(map);
	 * ```
	 * @see [Display a popup](https://maplibre.org/maplibre-gl-js/docs/examples/popup/)
	 * @see [Display a popup on hover](https://maplibre.org/maplibre-gl-js/docs/examples/popup-on-hover/)
	 * @see [Display a popup on click](https://maplibre.org/maplibre-gl-js/docs/examples/popup-on-click/)
	 * @see [Attach a popup to a marker instance](https://maplibre.org/maplibre-gl-js/docs/examples/set-popup/)
	 */
	setHTML(html: string): this;
	/**
	 * Returns the popup's maximum width.
	 *
	 * @returns The maximum width of the popup.
	 */
	getMaxWidth(): string;
	/**
	 * Sets the popup's maximum width. This is setting the CSS property `max-width`.
	 * Available values can be found here: https://developer.mozilla.org/en-US/docs/Web/CSS/max-width
	 *
	 * @param maxWidth - A string representing the value for the maximum width.
	 */
	setMaxWidth(maxWidth: string): this;
	/**
	 * Sets the popup's content to the element provided as a DOM node.
	 *
	 * @param htmlNode - A DOM node to be used as content for the popup.
	 * @example
	 * Create an element with the popup content
	 * ```ts
	 * let div = document.createElement('div');
	 * div.innerHTML = 'Hello, world!';
	 * let popup = new Popup()
	 *   .setLngLat(e.lngLat)
	 *   .setDOMContent(div)
	 *   .addTo(map);
	 * ```
	 */
	setDOMContent(htmlNode: Node): this;
	/**
	 * Adds a CSS class to the popup container element.
	 *
	 * @param className - Non-empty string with CSS class name to add to popup container
	 *
	 * @example
	 * ```ts
	 * let popup = new Popup()
	 * popup.addClassName('some-class')
	 * ```
	 */
	addClassName(className: string): this;
	/**
	 * Removes a CSS class from the popup container element.
	 *
	 * @param className - Non-empty string with CSS class name to remove from popup container
	 *
	 * @example
	 * ```ts
	 * let popup = new Popup()
	 * popup.removeClassName('some-class')
	 * ```
	 */
	removeClassName(className: string): this;
	/**
	 * Sets the popup's offset.
	 *
	 * @param offset - Sets the popup's offset.
	 */
	setOffset(offset?: Offset): this;
	/**
	 * Add or remove the given CSS class on the popup container, depending on whether the container currently has that class.
	 *
	 * @param className - Non-empty string with CSS class name to add/remove
	 *
	 * @returns if the class was removed return false, if class was added, then return true, undefined if there is no container
	 *
	 * @example
	 * ```ts
	 * let popup = new Popup()
	 * popup.toggleClassName('toggleClass')
	 * ```
	 */
	toggleClassName(className: string): boolean | undefined;
	/**
	 * Set the option to allow subpixel positioning of the popup by passing a boolean
	 *
	 * @param value - When boolean is true, subpixel positioning is enabled for the popup.
	 *
	 * @example
	 * ```ts
	 * let popup = new Popup()
	 * popup.setSubpixelPositioning(true);
	 * ```
	 */
	setSubpixelPositioning(value: boolean): void;
	_createCloseButton(): void;
	_onMouseUp: (event: MapMouseEvent) => void;
	_onMouseMove: (event: MapMouseEvent) => void;
	_onDrag: (event: MapMouseEvent) => void;
	_update: (cursor?: Point) => void;
	_focusFirstElement(): void;
	_onClose: () => void;
}
/**
 * Alignment options of rotation and pitch
 */
export type Alignment = "map" | "viewport" | "auto";
/**
 * The {@link Marker} options object
 */
export type MarkerOptions = {
	/**
	 * DOM element to use as a marker. The default is a light blue, droplet-shaped SVG marker.
	 */
	element?: HTMLElement;
	/**
	 * Space-separated CSS class names to add to marker element.
	 */
	className?: string;
	/**
	 * The offset in pixels as a {@link PointLike} object to apply relative to the element's center. Negatives indicate left and up.
	 */
	offset?: PointLike;
	/**
	 * A string indicating the part of the Marker that should be positioned closest to the coordinate set via {@link Marker#setLngLat}.
	 * Options are `'center'`, `'top'`, `'bottom'`, `'left'`, `'right'`, `'top-left'`, `'top-right'`, `'bottom-left'`, and `'bottom-right'`.
	 * @defaultValue 'center'
	 * */
	anchor?: PositionAnchor;
	/**
	 * The color to use for the default marker if options.element is not provided. The default is light blue.
	 * @defaultValue '#3FB1CE'
	 */
	color?: string;
	/**
	 * The scale to use for the default marker if options.element is not provided. The default scale corresponds to a height of `41px` and a width of `27px`.
	 * @defaultValue 1
	 */
	scale?: number;
	/**
	 * A boolean indicating whether or not a marker is able to be dragged to a new position on the map.
	 * @defaultValue false
	 */
	draggable?: boolean;
	/**
	 * The max number of pixels a user can shift the mouse pointer during a click on the marker for it to be considered a valid click (as opposed to a marker drag). The default is to inherit map's clickTolerance.
	 * @defaultValue 0
	 */
	clickTolerance?: number;
	/**
	 * The rotation angle of the marker in degrees, relative to its respective `rotationAlignment` setting. A positive value will rotate the marker clockwise.
	 * @defaultValue 0
	 */
	rotation?: number;
	/**
	 * `map` aligns the `Marker`'s rotation relative to the map, maintaining a bearing as the map rotates. `viewport` aligns the `Marker`'s rotation relative to the viewport, agnostic to map rotations. `auto` is equivalent to `viewport`.
	 * @defaultValue 'auto'
	 */
	rotationAlignment?: Alignment;
	/**
	 * `map` aligns the `Marker` to the plane of the map. `viewport` aligns the `Marker` to the plane of the viewport. `auto` automatically matches the value of `rotationAlignment`.
	 * @defaultValue 'auto'
	 */
	pitchAlignment?: Alignment;
	/**
	 * Marker's opacity when it's in clear view (not behind 3d terrain)
	 * @defaultValue 1
	 */
	opacity?: string;
	/**
	 * Marker's opacity when it's behind 3d terrain
	 * @defaultValue 0.2
	 */
	opacityWhenCovered?: string;
	/**
	  * If `true`, rounding is disabled for placement of the marker, allowing for
	  * subpixel positioning and smoother movement when the marker is translated.
	  * @defaultValue false
	  */
	subpixelPositioning?: boolean;
};
/**
 * Creates a marker component
 *
 * @group Markers and Controls
 *
 * @example
 * ```ts
 * let marker = new Marker()
 *   .setLngLat([30.5, 50.5])
 *   .addTo(map);
 * ```
 *
 * @example
 * Set options
 * ```ts
 * let marker = new Marker({
 *     color: "#FFFFFF",
 *     draggable: true
 *   }).setLngLat([30.5, 50.5])
 *   .addTo(map);
 * ```
 * @see [Add custom icons with Markers](https://maplibre.org/maplibre-gl-js/docs/examples/custom-marker-icons/)
 * @see [Create a draggable Marker](https://maplibre.org/maplibre-gl-js/docs/examples/drag-a-marker/)
 *
 * ## Events
 *
 * **Event** `dragstart` of type {@link Event} will be fired when dragging starts.
 *
 * **Event** `drag` of type {@link Event} will be fired while dragging.
 *
 * **Event** `dragend` of type {@link Event} will be fired when the marker is finished being dragged.
 */
export declare class Marker extends Evented {
	_map: Map$1;
	_anchor: PositionAnchor;
	_offset: Point;
	_element: HTMLElement;
	_popup: Popup;
	_lngLat: LngLat;
	_pos: Point;
	_flatPos: Point;
	_color: string;
	_scale: number;
	_defaultMarker: boolean;
	_draggable: boolean;
	_clickTolerance: number;
	_isDragging: boolean;
	_state: "inactive" | "pending" | "active";
	_positionDelta: Point;
	_pointerdownPos: Point;
	_rotation: number;
	_pitchAlignment: Alignment;
	_rotationAlignment: Alignment;
	_originalTabIndex: string;
	_opacity: string;
	_opacityWhenCovered: string;
	_opacityTimeout: ReturnType<typeof setTimeout>;
	_subpixelPositioning: boolean;
	/**
	 * @param options - the options
	 */
	constructor(options?: MarkerOptions);
	/**
	 * Attaches the `Marker` to a `Map` object.
	 * @param map - The MapLibre GL JS map to add the marker to.
	 * @example
	 * ```ts
	 * let marker = new Marker()
	 *   .setLngLat([30.5, 50.5])
	 *   .addTo(map); // add the marker to the map
	 * ```
	 */
	addTo(map: Map$1): this;
	/**
	 * Removes the marker from a map
	 * @example
	 * ```ts
	 * let marker = new Marker().addTo(map);
	 * marker.remove();
	 * ```
	 */
	remove(): this;
	/**
	 * Get the marker's geographical location.
	 *
	 * The longitude of the result may differ by a multiple of 360 degrees from the longitude previously
	 * set by `setLngLat` because `Marker` wraps the anchor longitude across copies of the world to keep
	 * the marker on screen.
	 *
	 * @returns A {@link LngLat} describing the marker's location.
	 * @example
	 * ```ts
	 * // Store the marker's longitude and latitude coordinates in a variable
	 * let lngLat = marker.getLngLat();
	 * // Print the marker's longitude and latitude values in the console
	 * console.log('Longitude: ' + lngLat.lng + ', Latitude: ' + lngLat.lat )
	 * ```
	 * @see [Create a draggable Marker](https://maplibre.org/maplibre-gl-js/docs/examples/drag-a-marker/)
	 */
	getLngLat(): LngLat;
	/**
	 * Set the marker's geographical position and move it.
	 * @param lnglat - A {@link LngLat} describing where the marker should be located.
	 * @example
	 * Create a new marker, set the longitude and latitude, and add it to the map
	 * ```ts
	 * new Marker()
	 *   .setLngLat([-65.017, -16.457])
	 *   .addTo(map);
	 * ```
	 * @see [Add custom icons with Markers](https://maplibre.org/maplibre-gl-js/docs/examples/custom-marker-icons/)
	 * @see [Create a draggable Marker](https://maplibre.org/maplibre-gl-js/docs/examples/drag-a-marker/)
	 */
	setLngLat(lnglat: LngLatLike): this;
	/**
	 * Returns the `Marker`'s HTML element.
	 * @returns element
	 */
	getElement(): HTMLElement;
	/**
	 * Binds a {@link Popup} to the {@link Marker}.
	 * @param popup - An instance of the {@link Popup} class. If undefined or null, any popup
	 * set on this {@link Marker} instance is unset.
	 * @example
	 * ```ts
	 * let marker = new Marker()
	 *  .setLngLat([0, 0])
	 *  .setPopup(new Popup().setHTML("<h1>Hello World!</h1>")) // add popup
	 *  .addTo(map);
	 * ```
	 * @see [Attach a popup to a marker instance](https://maplibre.org/maplibre-gl-js/docs/examples/set-popup/)
	 */
	setPopup(popup?: Popup | null): this;
	/**
	  * Set the option to allow subpixel positioning of the marker by passing a boolean
	  *
	  * @param value - when set to `true`, subpixel positioning is enabled for the marker.
	  *
	  * @example
	  * ```ts
	  * let marker = new Marker()
	  * marker.setSubpixelPositioning(true);
	  * ```
	  */
	setSubpixelPositioning(value: boolean): this;
	_onKeyPress: (e: KeyboardEvent) => void;
	_onMapClick: (e: MapMouseEvent) => void;
	/**
	 * Returns the {@link Popup} instance that is bound to the {@link Marker}.
	 * @returns popup
	 * @example
	 * ```ts
	 * let marker = new Marker()
	 *  .setLngLat([0, 0])
	 *  .setPopup(new Popup().setHTML("<h1>Hello World!</h1>"))
	 *  .addTo(map);
	 *
	 * console.log(marker.getPopup()); // return the popup instance
	 * ```
	 */
	getPopup(): Popup;
	/**
	 * Opens or closes the {@link Popup} instance that is bound to the {@link Marker}, depending on the current state of the {@link Popup}.
	 * @example
	 * ```ts
	 * let marker = new Marker()
	 *  .setLngLat([0, 0])
	 *  .setPopup(new Popup().setHTML("<h1>Hello World!</h1>"))
	 *  .addTo(map);
	 *
	 * marker.togglePopup(); // toggle popup open or closed
	 * ```
	 */
	togglePopup(): this;
	_updateOpacity(force?: boolean): void;
	_update: (e?: {
		type: "move" | "moveend" | "terrain" | "render";
	}) => void;
	/**
	 * Get the marker's offset.
	 * @returns The marker's screen coordinates in pixels.
	 */
	getOffset(): Point;
	/**
	 * Sets the offset of the marker
	 * @param offset - The offset in pixels as a {@link PointLike} object to apply relative to the element's center. Negatives indicate left and up.
	 */
	setOffset(offset: PointLike): this;
	/**
	 * Adds a CSS class to the marker element.
	 *
	 * @param className - on-empty string with CSS class name to add to marker element
	 *
	 * @example
	 * ```
	 * let marker = new Marker()
	 * marker.addClassName('some-class')
	 * ```
	 */
	addClassName(className: string): void;
	/**
	 * Removes a CSS class from the marker element.
	 *
	 * @param className - Non-empty string with CSS class name to remove from marker element
	 *
	 * @example
	 * ```ts
	 * let marker = new Marker()
	 * marker.removeClassName('some-class')
	 * ```
	 */
	removeClassName(className: string): void;
	/**
	 * Add or remove the given CSS class on the marker element, depending on whether the element currently has that class.
	 *
	 * @param className - Non-empty string with CSS class name to add/remove
	 *
	 * @returns if the class was removed return false, if class was added, then return true
	 *
	 * @example
	 * ```ts
	 * let marker = new Marker()
	 * marker.toggleClassName('toggleClass')
	 * ```
	 */
	toggleClassName(className: string): boolean;
	_onMove: (e: MapMouseEvent | MapTouchEvent) => void;
	_onUp: () => void;
	_addDragHandler: (e: MapMouseEvent | MapTouchEvent) => void;
	/**
	 * Sets the `draggable` property and functionality of the marker
	 * @param shouldBeDraggable - Turns drag functionality on/off
	 */
	setDraggable(shouldBeDraggable?: boolean): this;
	/**
	 * Returns true if the marker can be dragged
	 * @returns True if the marker is draggable.
	 */
	isDraggable(): boolean;
	/**
	 * Sets the `rotation` property of the marker.
	 * @param rotation - The rotation angle of the marker (clockwise, in degrees), relative to its respective {@link Marker#setRotationAlignment} setting.
	 */
	setRotation(rotation?: number): this;
	/**
	 * Returns the current rotation angle of the marker (in degrees).
	 * @returns The current rotation angle of the marker.
	 */
	getRotation(): number;
	/**
	 * Sets the `rotationAlignment` property of the marker.
	 * @param alignment - Sets the `rotationAlignment` property of the marker. defaults to 'auto'
	 */
	setRotationAlignment(alignment?: Alignment): this;
	/**
	 * Returns the current `rotationAlignment` property of the marker.
	 * @returns The current rotational alignment of the marker.
	 */
	getRotationAlignment(): Alignment;
	/**
	 * Sets the `pitchAlignment` property of the marker.
	 * @param alignment - Sets the `pitchAlignment` property of the marker. If alignment is 'auto', it will automatically match `rotationAlignment`.
	 */
	setPitchAlignment(alignment?: Alignment): this;
	/**
	 * Returns the current `pitchAlignment` property of the marker.
	 * @returns The current pitch alignment of the marker in degrees.
	 */
	getPitchAlignment(): Alignment;
	/**
	 * Sets the `opacity` and `opacityWhenCovered` properties of the marker.
	 * When called without arguments, resets opacity and opacityWhenCovered to defaults
	 * @param opacity - Sets the `opacity` property of the marker.
	 * @param opacityWhenCovered - Sets the `opacityWhenCovered` property of the marker.
	 */
	setOpacity(opacity?: string, opacityWhenCovered?: string): this;
}
/**
 * The {@link GeolocateControl} options object
 */
export type GeolocateControlOptions = {
	/**
	 * A Geolocation API [PositionOptions](https://developer.mozilla.org/en-US/docs/Web/API/PositionOptions) object.
	 * @defaultValue `{enableHighAccuracy: false, timeout: 6000}`
	 */
	positionOptions?: PositionOptions;
	/**
	 * A options object to use when the map is panned and zoomed to the user's location. The default is to use a `maxZoom` of 15 to limit how far the map will zoom in for very accurate locations.
	 */
	fitBoundsOptions?: FitBoundsOptions;
	/**
	 * If `true` the `GeolocateControl` becomes a toggle button and when active the map will receive updates to the user's location as it changes.
	 * @defaultValue false
	 */
	trackUserLocation?: boolean;
	/**
	 * By default, if `showUserLocation` is `true`, a transparent circle will be drawn around the user location indicating the accuracy (95% confidence level) of the user's location. Set to `false` to disable. Always disabled when `showUserLocation` is `false`.
	 * @defaultValue true
	 */
	showAccuracyCircle?: boolean;
	/**
	 * By default a dot will be shown on the map at the user's location. Set to `false` to disable.
	 * @defaultValue true
	 */
	showUserLocation?: boolean;
};
/**
 * A `GeolocateControl` control provides a button that uses the browser's geolocation
 * API to locate the user on the map.
 *
 * Not all browsers support geolocation,
 * and some users may disable the feature. Geolocation support for modern
 * browsers including Chrome requires sites to be served over HTTPS. If
 * geolocation support is not available, the `GeolocateControl` will show
 * as disabled.
 *
 * The zoom level applied will depend on the accuracy of the geolocation provided by the device.
 *
 * The `GeolocateControl` has two modes. If `trackUserLocation` is `false` (default) the control acts as a button, which when pressed will set the map's camera to target the user location. If the user moves, the map won't update. This is most suited for the desktop. If `trackUserLocation` is `true` the control acts as a toggle button that when active the user's location is actively monitored for changes. In this mode the `GeolocateControl` has three interaction states:
 * * active - the map's camera automatically updates as the user's location changes, keeping the location dot in the center. Initial state and upon clicking the `GeolocateControl` button.
 * * passive - the user's location dot automatically updates, but the map's camera does not. Occurs upon the user initiating a map movement.
 * * disabled - occurs if Geolocation is not available, disabled or denied.
 *
 * These interaction states can't be controlled programmatically, rather they are set based on user interactions.
 *
 * ## State Diagram
 * ![GeolocateControl state diagram](https://github.com/maplibre/maplibre-gl-js/assets/3269297/78e720e5-d781-4da8-9803-a7a0e6aaaa9f)
 *
 * @group Markers and Controls
 *
 * @example
 * ```ts
 * map.addControl(new GeolocateControl({
 *     positionOptions: {
 *         enableHighAccuracy: true
 *     },
 *     trackUserLocation: true
 * }));
 * ```
 * @see [Locate the user](https://maplibre.org/maplibre-gl-js/docs/examples/locate-user/)
 *
 * ## Events
 *
 * **Event** `trackuserlocationend` of type {@link Event} will be fired when the `GeolocateControl` changes to the background state, which happens when a user changes the camera during an active position lock. This only applies when `trackUserLocation` is `true`. In the background state, the dot on the map will update with location updates but the camera will not.
 *
 * **Event** `trackuserlocationstart` of type {@link Event} will be fired when the `GeolocateControl` changes to the active lock state, which happens either upon first obtaining a successful Geolocation API position for the user (a `geolocate` event will follow), or the user clicks the geolocate button when in the background state which uses the last known position to recenter the map and enter active lock state (no `geolocate` event will follow unless the users's location changes).
 *
 * **Event** `userlocationlostfocus` of type {@link Event} will be fired when the `GeolocateControl` changes to the background state, which happens when a user changes the camera during an active position lock. This only applies when `trackUserLocation` is `true`. In the background state, the dot on the map will update with location updates but the camera will not.
 *
 * **Event** `userlocationfocus` of type {@link Event} will be fired when the `GeolocateControl` changes to the active lock state, which happens upon the user clicks the geolocate button when in the background state which uses the last known position to recenter the map and enter active lock state.
 *
 * **Event** `geolocate` of type {@link Event} will be fired on each Geolocation API position update which returned as success.
 * `data` - The returned [Position](https://developer.mozilla.org/en-US/docs/Web/API/Position) object from the callback in [Geolocation.getCurrentPosition()](https://developer.mozilla.org/en-US/docs/Web/API/Geolocation/getCurrentPosition) or [Geolocation.watchPosition()](https://developer.mozilla.org/en-US/docs/Web/API/Geolocation/watchPosition).
 *
 * **Event** `error` of type {@link Event} will be fired on each Geolocation API position update which returned as an error.
 * `data` - The returned [PositionError](https://developer.mozilla.org/en-US/docs/Web/API/PositionError) object from the callback in [Geolocation.getCurrentPosition()](https://developer.mozilla.org/en-US/docs/Web/API/Geolocation/getCurrentPosition) or [Geolocation.watchPosition()](https://developer.mozilla.org/en-US/docs/Web/API/Geolocation/watchPosition).
 *
 * **Event** `outofmaxbounds` of type {@link Event} will be fired on each Geolocation API position update which returned as success but user position is out of map `maxBounds`.
 * `data` - The returned [Position](https://developer.mozilla.org/en-US/docs/Web/API/Position) object from the callback in [Geolocation.getCurrentPosition()](https://developer.mozilla.org/en-US/docs/Web/API/Geolocation/getCurrentPosition) or [Geolocation.watchPosition()](https://developer.mozilla.org/en-US/docs/Web/API/Geolocation/watchPosition).
 *
 * @example
 * ```ts
 * // Initialize the geolocate control.
 * let geolocate = new GeolocateControl({
 *   positionOptions: {
 *       enableHighAccuracy: true
 *   },
 *   trackUserLocation: true
 * });
 * // Add the control to the map.
 * map.addControl(geolocate);
 * // Set an event listener that fires
 * // when a trackuserlocationend event occurs.
 * geolocate.on('trackuserlocationend', () => {
 *   console.log('A trackuserlocationend event has occurred.')
 * });
 * ```
 *
 * @example
 * ```ts
 * // Initialize the geolocate control.
 * let geolocate = new GeolocateControl({
 *   positionOptions: {
 *       enableHighAccuracy: true
 *   },
 *   trackUserLocation: true
 * });
 * // Add the control to the map.
 * map.addControl(geolocate);
 * // Set an event listener that fires
 * // when a trackuserlocationstart event occurs.
 * geolocate.on('trackuserlocationstart', () => {
 *   console.log('A trackuserlocationstart event has occurred.')
 * });
 * ```
 *
 * @example
 * ```ts
 * // Initialize the geolocate control.
 * let geolocate = new GeolocateControl({
 *   positionOptions: {
 *       enableHighAccuracy: true
 *   },
 *   trackUserLocation: true
 * });
 * // Add the control to the map.
 * map.addControl(geolocate);
 * // Set an event listener that fires
 * // when an userlocationlostfocus event occurs.
 * geolocate.on('userlocationlostfocus', function() {
 *   console.log('An userlocationlostfocus event has occurred.')
 * });
 * ```
 *
 * @example
 * ```ts
 * // Initialize the geolocate control.
 * let geolocate = new GeolocateControl({
 *   positionOptions: {
 *       enableHighAccuracy: true
 *   },
 *   trackUserLocation: true
 * });
 * // Add the control to the map.
 * map.addControl(geolocate);
 * // Set an event listener that fires
 * // when an userlocationfocus event occurs.
 * geolocate.on('userlocationfocus', function() {
 *   console.log('An userlocationfocus event has occurred.')
 * });
 * ```
 *
 * @example
 * ```ts
 * // Initialize the geolocate control.
 * let geolocate = new GeolocateControl({
 *   positionOptions: {
 *       enableHighAccuracy: true
 *   },
 *   trackUserLocation: true
 * });
 * // Add the control to the map.
 * map.addControl(geolocate);
 * // Set an event listener that fires
 * // when a geolocate event occurs.
 * geolocate.on('geolocate', () => {
 *   console.log('A geolocate event has occurred.')
 * });
 * ```
 *
 * @example
 * ```ts
 * // Initialize the geolocate control.
 * let geolocate = new GeolocateControl({
 *   positionOptions: {
 *       enableHighAccuracy: true
 *   },
 *   trackUserLocation: true
 * });
 * // Add the control to the map.
 * map.addControl(geolocate);
 * // Set an event listener that fires
 * // when an error event occurs.
 * geolocate.on('error', () => {
 *   console.log('An error event has occurred.')
 * });
 * ```
 *
 * @example
 * ```ts
 * // Initialize the geolocate control.
 * let geolocate = new GeolocateControl({
 *   positionOptions: {
 *       enableHighAccuracy: true
 *   },
 *   trackUserLocation: true
 * });
 * // Add the control to the map.
 * map.addControl(geolocate);
 * // Set an event listener that fires
 * // when an outofmaxbounds event occurs.
 * geolocate.on('outofmaxbounds', () => {
 *   console.log('An outofmaxbounds event has occurred.')
 * });
 * ```
 */
export declare class GeolocateControl extends Evented implements IControl {
	_map: Map$1;
	options: GeolocateControlOptions;
	_container: HTMLElement;
	_dotElement: HTMLElement;
	_circleElement: HTMLElement;
	_geolocateButton: HTMLButtonElement;
	_geolocationWatchID: number;
	_timeoutId: ReturnType<typeof setTimeout>;
	_watchState: "OFF" | "ACTIVE_LOCK" | "WAITING_ACTIVE" | "ACTIVE_ERROR" | "BACKGROUND" | "BACKGROUND_ERROR";
	_lastKnownPosition: any;
	_userLocationDotMarker: Marker;
	_accuracyCircleMarker: Marker;
	_accuracy: number;
	_setup: boolean;
	/**
	 * @param options - the control's options
	 */
	constructor(options: GeolocateControlOptions);
	/** {@inheritDoc IControl.onAdd} */
	onAdd(map: Map$1): HTMLElement;
	/** {@inheritDoc IControl.onRemove} */
	onRemove(): void;
	/**
	 * Check if the Geolocation API Position is outside the map's `maxBounds`.
	 *
	 * @param position - the Geolocation API Position
	 * @returns `true` if position is outside the map's `maxBounds`, otherwise returns `false`.
	 */
	_isOutOfMapMaxBounds(position: GeolocationPosition): boolean;
	_setErrorState(): void;
	/**
	 * When the Geolocation API returns a new location, update the `GeolocateControl`.
	 *
	 * @param position - the Geolocation API Position
	 */
	_onSuccess: (position: GeolocationPosition) => void;
	/**
	 * Update the camera location to center on the current position
	 *
	 * @param position - the Geolocation API Position
	 */
	_updateCamera: (position: GeolocationPosition) => void;
	/**
	 * Update the user location dot Marker to the current position
	 *
	 * @param position - the Geolocation API Position
	 */
	_updateMarker: (position?: GeolocationPosition | null) => void;
	_updateCircleRadius(): void;
	_onZoom: () => void;
	_onError: (error: GeolocationPositionError) => void;
	_finish: () => void;
	_setupUI: () => void;
	_finishSetupUI: (supported: boolean) => void;
	/**
	 * Programmatically request and move the map to the user's location.
	 *
	 * @returns `false` if called before control was added to a map, otherwise returns `true`.
	 * @example
	 * ```ts
	 * // Initialize the geolocate control.
	 * let geolocate = new GeolocateControl({
	 *  positionOptions: {
	 *    enableHighAccuracy: true
	 *  },
	 *  trackUserLocation: true
	 * });
	 * // Add the control to the map.
	 * map.addControl(geolocate);
	 * map.on('load', () => {
	 *   geolocate.trigger();
	 * });
	 * ```
	 */
	trigger(): boolean;
	_clearWatch(): void;
}
/**
 * The {@link LogoControl} options object
 */
export type LogoControlOptions = {
	/**
	 * If `true`, force a compact logo.
	 * If `false`, force the full logo. The default is a responsive logo that collapses when the map is less than 640 pixels wide.
	 */
	compact?: boolean;
};
/**
 * A `LogoControl` is a control that adds the watermark.
 *
 * @group Markers and Controls
 *
 * @example
 * ```ts
 * map.addControl(new LogoControl({compact: false}));
 * ```
 **/
export declare class LogoControl implements IControl {
	options: LogoControlOptions;
	_map: Map$1;
	_compact: boolean;
	_container: HTMLElement;
	/**
	 * @param options - the control's options
	 */
	constructor(options?: LogoControlOptions);
	getDefaultPosition(): ControlPosition;
	/** {@inheritDoc IControl.onAdd} */
	onAdd(map: Map$1): HTMLElement;
	/** {@inheritDoc IControl.onRemove} */
	onRemove(): void;
	_updateCompact: () => void;
}
/**
 * The unit type for length to use for the {@link ScaleControl}
 */
export type Unit = "imperial" | "metric" | "nautical";
/**
 * The {@link ScaleControl} options object
 */
export type ScaleControlOptions = {
	/**
	 * The maximum length of the scale control in pixels.
	 * @defaultValue 100
	 */
	maxWidth?: number;
	/**
	 * Unit of the distance (`'imperial'`, `'metric'` or `'nautical'`).
	 * @defaultValue 'metric'
	 */
	unit?: Unit;
};
/**
 * A `ScaleControl` control displays the ratio of a distance on the map to the corresponding distance on the ground.
 *
 * @group Markers and Controls
 *
 * @example
 * ```ts
 * let scale = new ScaleControl({
 *     maxWidth: 80,
 *     unit: 'imperial'
 * });
 * map.addControl(scale);
 *
 * scale.setUnit('metric');
 * ```
 */
export declare class ScaleControl implements IControl {
	_map: Map$1;
	_container: HTMLElement;
	options: ScaleControlOptions;
	/**
	 * @param options - the control's options
	 */
	constructor(options?: ScaleControlOptions);
	getDefaultPosition(): ControlPosition;
	_onMove: () => void;
	/** {@inheritDoc IControl.onAdd} */
	onAdd(map: Map$1): HTMLElement;
	/** {@inheritDoc IControl.onRemove} */
	onRemove(): void;
	/**
	 * Set the scale's unit of the distance
	 *
	 * @param unit - Unit of the distance (`'imperial'`, `'metric'` or `'nautical'`).
	 */
	setUnit: (unit: Unit) => void;
}
/**
 * The {@link FullscreenControl} options object
 */
export type FullscreenControlOptions = {
	/**
	 * `container` is the [compatible DOM element](https://developer.mozilla.org/en-US/docs/Web/API/Element/requestFullScreen#Compatible_elements) which should be made full screen. By default, the map container element will be made full screen.
	 */
	container?: HTMLElement;
};
/**
 * A `FullscreenControl` control contains a button for toggling the map in and out of fullscreen mode.
 * When [requestFullscreen](https://developer.mozilla.org/en-US/docs/Web/API/Element/requestFullscreen) is not supported, fullscreen is handled via CSS properties.
 * The map's `cooperativeGestures` option is temporarily disabled while the map
 * is in fullscreen mode, and is restored when the map exist fullscreen mode.
 *
 * @group Markers and Controls
 * @param options - the full screen control options
 *
 * @example
 * ```ts
 * map.addControl(new FullscreenControl({container: document.querySelector('body')}));
 * ```
 * @see [View a fullscreen map](https://maplibre.org/maplibre-gl-js/docs/examples/fullscreen/)
 *
 * ## Events
 *
 * **Event** `fullscreenstart` of type {@link Event} will be fired when fullscreen mode has started.
 *
 * **Event** `fullscreenend` of type {@link Event} will be fired when fullscreen mode has ended.
 */
export declare class FullscreenControl extends Evented implements IControl {
	_map: Map$1;
	_controlContainer: HTMLElement;
	_fullscreen: boolean;
	_fullscreenchange: string;
	_fullscreenButton: HTMLButtonElement;
	_container: HTMLElement;
	_prevCooperativeGesturesEnabled: boolean;
	/**
	 * @param options - the control's options
	 */
	constructor(options?: FullscreenControlOptions);
	/** {@inheritDoc IControl.onAdd} */
	onAdd(map: Map$1): HTMLElement;
	/** {@inheritDoc IControl.onRemove} */
	onRemove(): void;
	_setupUI(): void;
	_updateTitle(): void;
	_getTitle(): string;
	_isFullscreen(): boolean;
	_onFullscreenChange: () => void;
	_handleFullscreenChange(): void;
	_onClickFullscreen: () => void;
	_exitFullscreen(): void;
	_requestFullscreen(): void;
	_togglePseudoFullScreen(): void;
}
/**
 * A `TerrainControl` control contains a button for turning the terrain on and off.
 *
 * @group Markers and Controls
 *
 * @example
 * ```ts
 * let map = new Map({TerrainControl: false})
 *     .addControl(new TerrainControl({
 *         source: "terrain"
 *     }));
 * ```
 */
export declare class TerrainControl implements IControl {
	options: TerrainSpecification;
	_map: Map$1;
	_container: HTMLElement;
	_terrainButton: HTMLButtonElement;
	/**
	 * @param options - the control's options
	 */
	constructor(options: TerrainSpecification);
	/** {@inheritDoc IControl.onAdd} */
	onAdd(map: Map$1): HTMLElement;
	/** {@inheritDoc IControl.onRemove} */
	onRemove(): void;
	_toggleTerrain: () => void;
	_updateTerrainIcon: () => void;
}
/**
 * Initializes resources like WebWorkers that can be shared across maps to lower load
 * times in some situations. `setWorkerUrl()` and `setWorkerCount()`, if being
 * used, must be set before `prewarm()` is called to have an effect.
 *
 * By default, the lifecycle of these resources is managed automatically, and they are
 * lazily initialized when a Map is first created. By invoking `prewarm()`, these
 * resources will be created ahead of time, and will not be cleared when the last Map
 * is removed from the page. This allows them to be re-used by new Map instances that
 * are created later. They can be manually cleared by calling
 * `clearPrewarmedResources()`. This is only necessary if your web page remains
 * active but stops using maps altogether.
 *
 * This is primarily useful when using GL-JS maps in a single page app, wherein a user
 * would navigate between various views that can cause Map instances to constantly be
 * created and destroyed.
 *
 * @example
 * ```ts
 * prewarm()
 * ```
 */
export declare function prewarm(): void;
/**
 * Clears up resources that have previously been created by `prewarm()`.
 * Note that this is typically not necessary. You should only call this function
 * if you expect the user of your app to not return to a Map view at any point
 * in your application.
 *
 * @example
 * ```ts
 * clearPrewarmedResources()
 * ```
 */
export declare function clearPrewarmedResources(): void;
/**
 * Options object for GeoJSONSource.
 */
export type GeoJSONSourceOptions = GeoJSONSourceSpecification & {
	workerOptions?: GeoJSONWorkerOptions;
	collectResourceTiming?: boolean;
	data: GeoJSON.GeoJSON | string;
};
export type GeoJSONSourceInternalOptions = {
	data?: GeoJSON.GeoJSON | string | undefined;
	cluster?: boolean;
	clusterMaxZoom?: number;
	clusterRadius?: number;
	clusterMinPoints?: number;
	generateId?: boolean;
};
/**
 * The cluster options to set
 */
export type SetClusterOptions = {
	/**
	 * Whether or not to cluster
	 */
	cluster?: boolean;
	/**
	 * The cluster's max zoom
	 */
	clusterMaxZoom?: number;
	/**
	 * The cluster's radius
	 */
	clusterRadius?: number;
};
/**
 * A source containing GeoJSON.
 * (See the [Style Specification](https://maplibre.org/maplibre-style-spec/#sources-geojson) for detailed documentation of options.)
 *
 * @group Sources
 *
 * @example
 * ```ts
 * map.addSource('some id', {
 *     type: 'geojson',
 *     data: 'https://d2ad6b4ur7yvpq.cloudfront.net/naturalearth-3.3.0/ne_10m_ports.geojson'
 * });
 * ```
 *
 * @example
 * ```ts
 * map.addSource('some id', {
 *    type: 'geojson',
 *    data: {
 *        "type": "FeatureCollection",
 *        "features": [{
 *            "type": "Feature",
 *            "properties": {},
 *            "geometry": {
 *                "type": "Point",
 *                "coordinates": [
 *                    -76.53063297271729,
 *                    39.18174077994108
 *                ]
 *            }
 *        }]
 *    }
 * });
 * ```
 *
 * @example
 * ```ts
 * map.getSource('some id').setData({
 *   "type": "FeatureCollection",
 *   "features": [{
 *       "type": "Feature",
 *       "properties": { "name": "Null Island" },
 *       "geometry": {
 *           "type": "Point",
 *           "coordinates": [ 0, 0 ]
 *       }
 *   }]
 * });
 * ```
 * @see [Draw GeoJSON points](https://maplibre.org/maplibre-gl-js/docs/examples/geojson-markers/)
 * @see [Add a GeoJSON line](https://maplibre.org/maplibre-gl-js/docs/examples/geojson-line/)
 * @see [Create a heatmap from points](https://maplibre.org/maplibre-gl-js/docs/examples/heatmap-layer/)
 * @see [Create and style clusters](https://maplibre.org/maplibre-gl-js/docs/examples/cluster/)
 */
export declare class GeoJSONSource extends Evented implements Source {
	type: "geojson";
	id: string;
	minzoom: number;
	maxzoom: number;
	tileSize: number;
	attribution: string;
	promoteId: PromoteIdSpecification;
	isTileClipped: boolean;
	reparseOverscaled: boolean;
	_data: GeoJSON.GeoJSON | string | undefined;
	_options: GeoJSONSourceInternalOptions;
	workerOptions: GeoJSONWorkerOptions;
	map: Map$1;
	actor: Actor;
	_pendingLoads: number;
	_collectResourceTiming: boolean;
	_removed: boolean;
	/** @internal */
	constructor(id: string, options: GeoJSONSourceOptions, dispatcher: Dispatcher, eventedParent: Evented);
	load(): Promise<void>;
	onAdd(map: Map$1): void;
	/**
	 * Sets the GeoJSON data and re-renders the map.
	 *
	 * @param data - A GeoJSON data object or a URL to one. The latter is preferable in the case of large GeoJSON files.
	 */
	setData(data: GeoJSON.GeoJSON | string): this;
	/**
	 * Updates the source's GeoJSON, and re-renders the map.
	 *
	 * For sources with lots of features, this method can be used to make updates more quickly.
	 *
	 * This approach requires unique IDs for every feature in the source. The IDs can either be specified on the feature,
	 * or by using the promoteId option to specify which property should be used as the ID.
	 *
	 * It is an error to call updateData on a source that did not have unique IDs for each of its features already.
	 *
	 * Updates are applied on a best-effort basis, updating an ID that does not exist will not result in an error.
	 *
	 * @param diff - The changes that need to be applied.
	 */
	updateData(diff: GeoJSONSourceDiff): this;
	/**
	 * Allows to get the source's actual GeoJSON data.
	 *
	 * @returns a promise which resolves to the source's actual GeoJSON data
	 */
	getData(): Promise<GeoJSON.GeoJSON>;
	/**
	 * To disable/enable clustering on the source options
	 * @param options - The options to set
	 * @example
	 * ```ts
	 * map.getSource('some id').setClusterOptions({cluster: false});
	 * map.getSource('some id').setClusterOptions({cluster: false, clusterRadius: 50, clusterMaxZoom: 14});
	 * ```
	 */
	setClusterOptions(options: SetClusterOptions): this;
	/**
	 * For clustered sources, fetches the zoom at which the given cluster expands.
	 *
	 * @param clusterId - The value of the cluster's `cluster_id` property.
	 * @returns a promise that is resolved with the zoom number
	 */
	getClusterExpansionZoom(clusterId: number): Promise<number>;
	/**
	 * For clustered sources, fetches the children of the given cluster on the next zoom level (as an array of GeoJSON features).
	 *
	 * @param clusterId - The value of the cluster's `cluster_id` property.
	 * @returns a promise that is resolved when the features are retrieved
	 */
	getClusterChildren(clusterId: number): Promise<Array<GeoJSON.Feature>>;
	/**
	 * For clustered sources, fetches the original points that belong to the cluster (as an array of GeoJSON features).
	 *
	 * @param clusterId - The value of the cluster's `cluster_id` property.
	 * @param limit - The maximum number of features to return.
	 * @param offset - The number of features to skip (e.g. for pagination).
	 * @returns a promise that is resolved when the features are retrieved
	 * @example
	 * Retrieve cluster leaves on click
	 * ```ts
	 * map.on('click', 'clusters', (e) => {
	 *   let features = map.queryRenderedFeatures(e.point, {
	 *     layers: ['clusters']
	 *   });
	 *
	 *   let clusterId = features[0].properties.cluster_id;
	 *   let pointCount = features[0].properties.point_count;
	 *   let clusterSource = map.getSource('clusters');
	 *
	 *   const features = await clusterSource.getClusterLeaves(clusterId, pointCount);
	 *   // Print cluster leaves in the console
	 *   console.log('Cluster leaves:', features);
	 * });
	 * ```
	 */
	getClusterLeaves(clusterId: number, limit: number, offset: number): Promise<Array<GeoJSON.Feature>>;
	/**
	 * Responsible for invoking WorkerSource's geojson.loadData target, which
	 * handles loading the geojson data and preparing to serve it up as tiles,
	 * using geojson-vt or supercluster as appropriate.
	 * @param diff - the diff object
	 */
	_updateWorkerData(diff?: GeoJSONSourceDiff): Promise<void>;
	loaded(): boolean;
	loadTile(tile: Tile): Promise<void>;
	abortTile(tile: Tile): Promise<void>;
	unloadTile(tile: Tile): Promise<void>;
	onRemove(): void;
	serialize(): GeoJSONSourceSpecification;
	hasTransition(): boolean;
}
declare class TileBounds {
	bounds: LngLatBounds;
	minzoom: number;
	maxzoom: number;
	constructor(bounds: [
		number,
		number,
		number,
		number
	], minzoom?: number | null, maxzoom?: number | null);
	validateBounds(bounds: [
		number,
		number,
		number,
		number
	]): LngLatBoundsLike;
	contains(tileID: CanonicalTileID): boolean;
}
/**
 * A source containing raster tiles (See the [Style Specification](https://maplibre.org/maplibre-style-spec/) for detailed documentation of options.)
 *
 * @group Sources
 *
 * @example
 * ```ts
 * map.addSource('raster-source', {
 *     'type': 'raster',
 *     'tiles': ['https://tiles.stadiamaps.com/tiles/stamen_watercolor/{z}/{x}/{y}.jpg'],
 *     'tileSize': 256,
 * });
 * ```
 *
 * @example
 * ```ts
 * map.addSource('wms-test-source', {
 *      'type': 'raster',
 * // use the tiles option to specify a WMS tile source URL
 *      'tiles': [
 *          'https://img.nj.gov/imagerywms/Natural2015?bbox={bbox-epsg-3857}&format=image/png&service=WMS&version=1.1.1&request=GetMap&srs=EPSG:3857&transparent=true&width=256&height=256&layers=Natural2015'
 *      ],
 *      'tileSize': 256
 * });
 * ```
 * @see [Add a raster tile source](https://maplibre.org/maplibre-gl-js/docs/examples/map-tiles/)
 * @see [Add a WMS source](https://maplibre.org/maplibre-gl-js/docs/examples/wms/)
 * @see [Display a satellite map](https://maplibre.org/maplibre-gl-js/docs/examples/satellite-map/)
 */
export declare class RasterTileSource extends Evented implements Source {
	type: "raster" | "raster-dem";
	id: string;
	minzoom: number;
	maxzoom: number;
	url: string;
	scheme: string;
	tileSize: number;
	bounds: [
		number,
		number,
		number,
		number
	];
	tileBounds: TileBounds;
	roundZoom: boolean;
	dispatcher: Dispatcher;
	map: Map$1;
	tiles: Array<string>;
	_loaded: boolean;
	_options: RasterSourceSpecification | RasterDEMSourceSpecification;
	_tileJSONRequest: AbortController;
	constructor(id: string, options: RasterSourceSpecification | RasterDEMSourceSpecification, dispatcher: Dispatcher, eventedParent: Evented);
	load(): Promise<void>;
	loaded(): boolean;
	onAdd(map: Map$1): void;
	onRemove(): void;
	setSourceProperty(callback: Function): void;
	/**
	 * Sets the source `tiles` property and re-renders the map.
	 *
	 * @param tiles - An array of one or more tile source URLs, as in the raster tiles spec (See the [Style Specification](https://maplibre.org/maplibre-style-spec/)
	 */
	setTiles(tiles: Array<string>): this;
	/**
	 * Sets the source `url` property and re-renders the map.
	 *
	 * @param url - A URL to a TileJSON resource. Supported protocols are `http:` and `https:`.
	 */
	setUrl(url: string): this;
	serialize(): RasterSourceSpecification | RasterDEMSourceSpecification;
	hasTile(tileID: OverscaledTileID): boolean;
	loadTile(tile: Tile): Promise<void>;
	abortTile(tile: Tile): Promise<void>;
	unloadTile(tile: Tile): Promise<void>;
	hasTransition(): boolean;
}
/**
 * A source containing raster DEM tiles (See the [Style Specification](https://maplibre.org/maplibre-style-spec/) for detailed documentation of options.)
 * This source can be used to show hillshading and 3D terrain
 *
 * @group Sources
 *
 * @example
 * ```ts
 * map.addSource('raster-dem-source', {
 *      type: 'raster-dem',
 *      url: 'https://demotiles.maplibre.org/terrain-tiles/tiles.json',
 *      tileSize: 256
 * });
 * ```
 * @see [3D Terrain](https://maplibre.org/maplibre-gl-js/docs/examples/3d-terrain/)
 */
export declare class RasterDEMTileSource extends RasterTileSource implements Source {
	encoding: DEMEncoding;
	redFactor?: number;
	greenFactor?: number;
	blueFactor?: number;
	baseShift?: number;
	constructor(id: string, options: RasterDEMSourceSpecification, dispatcher: Dispatcher, eventedParent: Evented);
	loadTile(tile: Tile): Promise<void>;
	readImageNow(img: ImageBitmap | HTMLImageElement): Promise<RGBAImage | ImageData>;
	_getNeighboringTiles(tileID: OverscaledTileID): {};
	unloadTile(tile: Tile): Promise<void>;
}
export type VectorTileSourceOptions = VectorSourceSpecification & {
	collectResourceTiming?: boolean;
	tileSize?: number;
};
/**
 * A source containing vector tiles in [Mapbox Vector Tile format](https://docs.mapbox.com/vector-tiles/reference/).
 * (See the [Style Specification](https://maplibre.org/maplibre-style-spec/) for detailed documentation of options.)
 *
 * @group Sources
 *
 * @example
 * ```ts
 * map.addSource('some id', {
 *     type: 'vector',
 *     url: 'https://demotiles.maplibre.org/tiles/tiles.json'
 * });
 * ```
 *
 * @example
 * ```ts
 * map.addSource('some id', {
 *     type: 'vector',
 *     tiles: ['https://d25uarhxywzl1j.cloudfront.net/v0.1/{z}/{x}/{y}.mvt'],
 *     minzoom: 6,
 *     maxzoom: 14
 * });
 * ```
 *
 * @example
 * ```ts
 * map.getSource('some id').setUrl("https://demotiles.maplibre.org/tiles/tiles.json");
 * ```
 *
 * @example
 * ```ts
 * map.getSource('some id').setTiles(['https://d25uarhxywzl1j.cloudfront.net/v0.1/{z}/{x}/{y}.mvt']);
 * ```
 * @see [Add a vector tile source](https://maplibre.org/maplibre-gl-js/docs/examples/vector-source/)
 */
export declare class VectorTileSource extends Evented implements Source {
	type: "vector";
	id: string;
	minzoom: number;
	maxzoom: number;
	url: string;
	scheme: string;
	tileSize: number;
	promoteId: PromoteIdSpecification;
	_options: VectorSourceSpecification;
	_collectResourceTiming: boolean;
	dispatcher: Dispatcher;
	map: Map$1;
	bounds: [
		number,
		number,
		number,
		number
	];
	tiles: Array<string>;
	tileBounds: TileBounds;
	reparseOverscaled: boolean;
	isTileClipped: boolean;
	_tileJSONRequest: AbortController;
	_loaded: boolean;
	constructor(id: string, options: VectorTileSourceOptions, dispatcher: Dispatcher, eventedParent: Evented);
	load(): Promise<void>;
	loaded(): boolean;
	hasTile(tileID: OverscaledTileID): boolean;
	onAdd(map: Map$1): void;
	setSourceProperty(callback: Function): void;
	/**
	 * Sets the source `tiles` property and re-renders the map.
	 *
	 * @param tiles - An array of one or more tile source URLs, as in the TileJSON spec.
	 */
	setTiles(tiles: Array<string>): this;
	/**
	 * Sets the source `url` property and re-renders the map.
	 *
	 * @param url - A URL to a TileJSON resource. Supported protocols are `http:` and `https:`.
	 */
	setUrl(url: string): this;
	onRemove(): void;
	serialize(): VectorSourceSpecification;
	loadTile(tile: Tile): Promise<void>;
	private _afterTileLoadWorkerResponse;
	abortTile(tile: Tile): Promise<void>;
	unloadTile(tile: Tile): Promise<void>;
	hasTransition(): boolean;
}
/**
 * A data source containing video.
 * (See the [Style Specification](https://maplibre.org/maplibre-style-spec/#sources-video) for detailed documentation of options.)
 *
 * @group Sources
 *
 * @example
 * ```ts
 * // add to map
 * map.addSource('some id', {
 *    type: 'video',
 *    url: [
 *        'https://www.mapbox.com/blog/assets/baltimore-smoke.mp4',
 *        'https://www.mapbox.com/blog/assets/baltimore-smoke.webm'
 *    ],
 *    coordinates: [
 *        [-76.54, 39.18],
 *        [-76.52, 39.18],
 *        [-76.52, 39.17],
 *        [-76.54, 39.17]
 *    ]
 * });
 *
 * // update
 * let mySource = map.getSource('some id');
 * mySource.setCoordinates([
 *     [-76.54335737228394, 39.18579907229748],
 *     [-76.52803659439087, 39.1838364847587],
 *     [-76.5295386314392, 39.17683392507606],
 *     [-76.54520273208618, 39.17876344106642]
 * ]);
 *
 * map.removeSource('some id');  // remove
 * ```
 * @see [Add a video](https://maplibre.org/maplibre-gl-js/docs/examples/video-on-a-map/)
 *
 * Note that when rendered as a raster layer, the layer's `raster-fade-duration` property will cause the video to fade in.
 * This happens when playback is started, paused and resumed, or when the video's coordinates are updated. To avoid this behavior,
 * set the layer's `raster-fade-duration` property to `0`.
 */
export declare class VideoSource extends ImageSource {
	options: VideoSourceSpecification;
	urls: Array<string>;
	video: HTMLVideoElement;
	roundZoom: boolean;
	constructor(id: string, options: VideoSourceSpecification, dispatcher: Dispatcher, eventedParent: Evented);
	load(): Promise<void>;
	/**
	 * Pauses the video.
	 */
	pause(): void;
	/**
	 * Plays the video.
	 */
	play(): void;
	/**
	 * Sets playback to a timestamp, in seconds.
	 */
	seek(seconds: number): void;
	/**
	 * Returns the HTML `video` element.
	 *
	 * @returns The HTML `video` element.
	 */
	getVideo(): HTMLVideoElement;
	onAdd(map: Map$1): void;
	/**
	 * Sets the video's coordinates and re-renders the map.
	 */
	prepare(): this;
	serialize(): VideoSourceSpecification;
	hasTransition(): boolean;
}
/**
 * Adds a custom load resource function that will be called when using a URL that starts with a custom url schema.
 * This will happen in the main thread, and workers might call it if they don't know how to handle the protocol.
 * The example below will be triggered for custom:// urls defined in the sources list in the style definitions.
 * The function passed will receive the request parameters and should return with the resulting resource,
 * for example a pbf vector tile, non-compressed, represented as ArrayBuffer.
 *
 * @param customProtocol - the protocol to hook, for example 'custom'
 * @param loadFn - the function to use when trying to fetch a tile specified by the customProtocol
 * @example
 * ```ts
 * // This will fetch a file using the fetch API (this is obviously a non interesting example...)
 * addProtocol('custom', async (params, abortController) => {
 *      const t = await fetch(`https://${params.url.split("://")[1]}`);
 *      if (t.status == 200) {
 *          const buffer = await t.arrayBuffer();
 *          return {data: buffer}
 *      } else {
 *          throw new Error(`Tile fetch error: ${t.statusText}`);
 *      }
 *  });
 * // the following is an example of a way to return an error when trying to load a tile
 * addProtocol('custom2', async (params, abortController) => {
 *      throw new Error('someErrorMessage'));
 * });
 * ```
 */
export declare function addProtocol(customProtocol: string, loadFn: AddProtocolAction): void;
/**
 * Removes a previously added protocol in the main thread.
 *
 * @param customProtocol - the custom protocol to remove registration for
 * @example
 * ```ts
 * removeProtocol('custom');
 * ```
 */
export declare function removeProtocol(customProtocol: string): void;
/**
 * Sets the map's [RTL text plugin](https://www.mapbox.com/mapbox-gl-js/plugins/#mapbox-gl-rtl-text).
 * Necessary for supporting the Arabic and Hebrew languages, which are written right-to-left.
 *
 * @param pluginURL - URL pointing to the Mapbox RTL text plugin source.
 * @param lazy - If set to `true`, maplibre will defer loading the plugin until rtl text is encountered,
 * rtl text will then be rendered only after the plugin finishes loading.
 * @example
 * ```ts
 * setRTLTextPlugin('https://unpkg.com/@mapbox/mapbox-gl-rtl-text@0.2.3/mapbox-gl-rtl-text.js', false);
 * ```
 * @see [Add support for right-to-left scripts](https://maplibre.org/maplibre-gl-js/docs/examples/mapbox-gl-rtl-text/)
 */
export declare function setRTLTextPlugin(pluginURL: string, lazy: boolean): Promise<void>;
/**
 * Gets the map's [RTL text plugin](https://www.mapbox.com/mapbox-gl-js/plugins/#mapbox-gl-rtl-text) status.
 * The status can be `unavailable` (i.e. not requested or removed), `loading`, `loaded` or `error`.
 * If the status is `loaded` and the plugin is requested again, an error will be thrown.
 *
 * @example
 * ```ts
 * const pluginStatus = getRTLTextPluginStatus();
 * ```
 */
export declare function getRTLTextPluginStatus(): string;
/**
 * Returns the package version of the library
 * @returns Package version of the library
 */
export declare function getVersion(): string;
/**
 * Gets the number of web workers instantiated on a page with GL JS maps.
 * By default, workerCount is 1 except for Safari browser where it is set to half the number of CPU cores (capped at 3).
 * Make sure to set this property before creating any map instances for it to have effect.
 *
 * @returns Number of workers currently configured.
 * @example
 * ```ts
 * const workerCount = getWorkerCount()
 * ```
 */
export declare function getWorkerCount(): number;
/**
 * Sets the number of web workers instantiated on a page with GL JS maps.
 * By default, workerCount is 1 except for Safari browser where it is set to half the number of CPU cores (capped at 3).
 * Make sure to set this property before creating any map instances for it to have effect.
 *
 * @example
 * ```ts
 * setWorkerCount(2);
 * ```
 */
export declare function setWorkerCount(count: number): void;
/**
 * Gets and sets the maximum number of images (raster tiles, sprites, icons) to load in parallel,
 * which affects performance in raster-heavy maps. 16 by default.
 *
 * @returns Number of parallel requests currently configured.
 * @example
 * ```ts
 * getMaxParallelImageRequests();
 * ```
 */
export declare function getMaxParallelImageRequests(): number;
/**
 * Sets the maximum number of images (raster tiles, sprites, icons) to load in parallel,
 * which affects performance in raster-heavy maps. 16 by default.
 *
 * @example
 * ```ts
 * setMaxParallelImageRequests(10);
 * ```
 */
export declare function setMaxParallelImageRequests(numRequests: number): void;
/**
 * Gets the worker url
 * @returns The worker url
 */
export declare function getWorkerUrl(): string;
/**
 * Sets the worker url
 */
export declare function setWorkerUrl(value: string): void;
/**
 * Allows loading javascript code in the worker thread.
 * *Note* that since this is using some very internal classes and flows it is considered experimental and can break at any point.
 *
 * It can be useful for the following examples:
 * 1. Using `self.addProtocol` in the worker thread - note that you might need to also register the protocol on the main thread.
 * 2. Using `self.registerWorkerSource(workerSource: WorkerSource)` to register a worker source, which should come with `addSourceType` usually.
 * 3. using `self.actor.registerMessageHandler` to override some internal worker operations
 * @param workerUrl - the worker url e.g. a url of a javascript file to load in the worker
 * @returns
 *
 * @example
 * ```ts
 * // below is an example of sending a js file to the worker to load the method there
 * // Note that you'll need to call the global function `addProtocol` in the worker to register the protocol there.
 * // add-protocol-worker.js
 * async function loadFn(params, abortController) {
 *     const t = await fetch(`https://${params.url.split("://")[1]}`);
 *     if (t.status == 200) {
 *         const buffer = await t.arrayBuffer();
 *         return {data: buffer}
 *     } else {
 *         throw new Error(`Tile fetch error: ${t.statusText}`);
 *     }
 * }
 * self.addProtocol('custom', loadFn);
 *
 * // main.js
 * importScriptInWorkers('add-protocol-worker.js');
 * ```
 */
export declare function importScriptInWorkers(workerUrl: string): Promise<void[]>;
export type * from "@maplibre/maplibre-gl-style-spec";

export {
	Color,
	CompositeExpression,
	DiffCommand,
	DiffOperations,
	Feature,
	FeatureFilter,
	FeatureState,
	FilterSpecification,
	Formatted,
	FormattedSection,
	GeoJSONSourceSpecification,
	GlobalProperties,
	ICanonicalTileID,
	IMercatorCoordinate,
	ImageSourceSpecification,
	InterpolationType,
	LayerSpecification,
	LightSpecification,
	Map$1 as Map,
	Padding,
	Point,
	PromoteIdSpecification,
	PropertyValueSpecification,
	RasterDEMSourceSpecification,
	RasterSourceSpecification,
	ResolvedImage,
	SkySpecification,
	SourceExpression,
	SourceSpecification,
	SpriteSpecification,
	StylePropertyExpression,
	StylePropertySpecification,
	StyleSpecification,
	TerrainSpecification,
	TransitionSpecification,
	VariableAnchorOffsetCollection,
	VectorSourceSpecification,
	VideoSourceSpecification,
};

export as namespace maplibregl;

export {};
