var locale={moduleType:"locale",name:"zh-TW",dictionary:{Autoscale:"\u81ea\u52d5\u7e2e\u653e","Box Select":"\u77e9\u5f62\u9078\u64c7","Click to enter Colorscale title":"\u9ede\u64ca\u4ee5\u8f38\u5165\u8272\u968e\u6a19\u984c","Click to enter Component A title":"\u9ede\u64ca\u4ee5\u8f38\u5165\u5143\u4ef6 A \u6a19\u984c","Click to enter Component B title":"\u9ede\u64ca\u4ee5\u8f38\u5165\u5143\u4ef6 B \u6a19\u984c","Click to enter Component C title":"\u9ede\u64ca\u4ee5\u8f38\u5165\u5143\u4ef6 C \u6a19\u984c","Click to enter Plot title":"\u9ede\u64ca\u4ee5\u8f38\u5165\u7e6a\u5716\u6a19\u984c","Click to enter X axis title":"\u9ede\u64ca\u4ee5\u8f38\u5165 X \u8ef8\u6a19\u984c","Click to enter Y axis title":"\u9ede\u64ca\u4ee5\u8f38\u5165 Y \u8ef8\u6a19\u984c","Click to enter radial axis title":"\u9ede\u64ca\u4ee5\u8f38\u5165\u8f3b\u5c04\u8ef8\u6a19\u984c","Compare data on hover":"\u6e38\u6a19\u505c\u7559\u6642\u6bd4\u8f03\u8cc7\u6599","Double-click on legend to isolate one trace":"\u96d9\u64ca\u5716\u4f8b\u4ee5\u9694\u96e2\u55ae\u4e00\u8ecc\u8de1","Double-click to zoom back out":"\u96d9\u64ca\u56de\u5fa9\u7e2e\u653e","Download plot as a png":"\u4e0b\u8f09\u5716\u8868\u70ba PNG \u5716\u6a94","Download plot":"\u4e0b\u8f09\u5716\u8868","Draw circle":"\u7e6a\u88fd\u5713\u5708","Draw closed freeform":"\u7e6a\u88fd\u5c01\u9589\u7684\u4efb\u610f\u5716\u5f62","Draw line":"\u7e6a\u88fd\u7dda\u689d","Draw open freeform":"\u7e6a\u88fd\u958b\u653e\u7684\u4efb\u610f\u5716\u5f62","Draw rectangle":"\u7e6a\u88fd\u77e9\u5f62","Edit in Chart Studio":"\u65bc Chart Studio \u7de8\u8f2f","Erase active shape":"\u6e05\u9664\u4f5c\u7528\u4e2d\u7684\u5f62\u72c0","IE only supports svg.  Changing format to svg.":"IE \u50c5\u652f\u63f4 SVG\uff0c\u5c07\u8b8a\u66f4\u683c\u5f0f\u70ba SVG\u3002","Lasso Select":"\u5957\u7d22\u9078\u64c7","Orbital rotation":"\u8ecc\u9053\u65cb\u8f49",Pan:"\u5e73\u79fb","Produced with Plotly.js":"\u4f7f\u7528 Plotly.js \u88fd\u4f5c",Reset:"\u91cd\u7f6e","Reset axes":"\u91cd\u7f6e\u8ef8","Reset camera to default":"\u91cd\u7f6e\u76f8\u6a5f\u81f3\u9810\u8a2d\u4f4d\u7f6e","Reset camera to last save":"\u91cd\u7f6e\u76f8\u6a5f\u81f3\u4e0a\u6b21\u5132\u5b58\u7684\u4f4d\u7f6e","Reset view":"\u91cd\u7f6e\u8996\u5716","Reset views":"\u91cd\u7f6e\u8996\u5716","Show closest data on hover":"\u6e38\u6a19\u505c\u7559\u6642\u986f\u793a\u6700\u63a5\u8fd1\u7684\u8cc7\u6599","Snapshot succeeded":"\u5feb\u7167\u6210\u529f","Sorry, there was a problem downloading your snapshot!":"\u62b1\u6b49\uff0c\u4e0b\u8f09\u5feb\u7167\u6642\u767c\u751f\u932f\u8aa4!","Taking snapshot - this may take a few seconds":"\u7522\u751f\u5feb\u7167\u4e2d - \u53ef\u80fd\u9700\u8981\u4e00\u9ede\u6642\u9593",Zoom:"\u7e2e\u653e","Zoom in":"\u653e\u5927","Zoom out":"\u7e2e\u5c0f","close:":"\u95dc\u9589:",concentration:"\u96c6\u4e2d",trace:"\u8ecc\u8de1:","lat:":"\u7def\u5ea6:","lon:":"\u7d93\u5ea6:","q1:":"\u7b2c\u4e00\u56db\u5206\u4f4d\u6578:","q3:":"\u7b2c\u4e09\u56db\u5206\u4f4d\u6578:","source:":"\u4f86\u6e90:","target:":"\u76ee\u6a19:","lower fence:":"\u4e0b\u570d\u7c6c\u503c:","upper fence:":"\u4e0a\u570d\u7c6c\u503c:","max:":"\u6700\u5927\u503c:","mean \xb1 \u03c3:":"\u5e73\u5747 \xb1 \u03c3:","mean:":"\u5e73\u5747\u503c:","median:":"\u4e2d\u4f4d\u6578:","min:":"\u6700\u5c0f\u503c:","Turntable rotation":"\u8f49\u76e4\u65cb\u8f49:","Toggle Spike Lines":"\u5207\u63db\u5c16\u5cf0\u7dda","open:":"\u958b\u555f:","high:":"\u9ad8:","low:":"\u4f4e:","Toggle show closest data on hover":"\u5207\u63db\u6ed1\u9f20\u61f8\u505c\u986f\u793a\u6700\u63a5\u8fd1\u7684\u8cc7\u6599","incoming flow count:":"\u50b3\u5165\u6d41\u91cf\u8a08\u6578:","outgoing flow count:":"\u50b3\u51fa\u6d41\u91cf\u8a08\u6578:","kde:":"kde:","new text":"\u65b0\u6587\u672c"},format:{days:["\u661f\u671f\u65e5","\u661f\u671f\u4e00","\u661f\u671f\u4e8c","\u661f\u671f\u4e09","\u661f\u671f\u56db","\u661f\u671f\u4e94","\u661f\u671f\u516d"],shortDays:["\u9031\u65e5","\u9031\u4e00","\u9031\u4e8c","\u9031\u4e09","\u9031\u56db","\u9031\u4e94","\u9031\u516d"],months:["\u4e00\u6708","\u4e8c\u6708","\u4e09\u6708","\u56db\u6708","\u4e94\u6708","\u516d\u6708","\u4e03\u6708","\u516b\u6708","\u4e5d\u6708","\u5341\u6708","\u5341\u4e00\u6708","\u5341\u4e8c\u6708"],shortMonths:["1\u6708","2\u6708","3\u6708","4\u6708","5\u6708","6\u6708","7\u6708","8\u6708","9\u6708","10\u6708","11\u6708","12\u6708"],date:"%Y-%m-%d"}};"undefined"==typeof Plotly?(window.PlotlyLocales=window.PlotlyLocales||[],window.PlotlyLocales.push(locale)):Plotly.register(locale);