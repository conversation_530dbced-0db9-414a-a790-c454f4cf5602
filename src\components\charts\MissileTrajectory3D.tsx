// DRDO IDX - 3D Missile Trajectory Visualization

import React, { useEffect, useRef } from 'react';
import Plot from 'react-plotly.js';

interface TrajectoryPoint {
  x: number;
  y: number;
  z: number;
  time: number;
  velocity: number;
}

interface MissileTrajectory3DProps {
  trajectoryData?: TrajectoryPoint[];
  title?: string;
  width?: number;
  height?: number;
}

// Generate sample missile trajectory data
const generateTrajectoryData = (): TrajectoryPoint[] => {
  const points: TrajectoryPoint[] = [];
  const totalTime = 600; // 10 minutes
  const timeStep = 2; // 2 seconds
  const launchAngle = 45; // degrees
  const initialVelocity = 7000; // m/s
  const g = 9.81; // gravity
  
  for (let t = 0; t <= totalTime; t += timeStep) {
    const radians = (launchAngle * Math.PI) / 180;
    
    // Ballistic trajectory with air resistance approximation
    const airResistanceFactor = Math.exp(-t / 200); // Simplified air resistance
    const adjustedVelocity = initialVelocity * airResistanceFactor;
    
    const x = adjustedVelocity * Math.cos(radians) * t;
    const y = x * 0.1 + Math.sin((t / 100) * Math.PI) * 50; // Add some lateral movement
    const z = Math.max(0, adjustedVelocity * Math.sin(radians) * t - 0.5 * g * t * t);
    
    const currentVelocity = Math.sqrt(
      Math.pow(adjustedVelocity * Math.cos(radians), 2) +
      Math.pow(adjustedVelocity * Math.sin(radians) - g * t, 2)
    );
    
    points.push({
      x: x / 1000, // Convert to km
      y: y / 1000, // Convert to km
      z: z / 1000, // Convert to km
      time: t,
      velocity: currentVelocity
    });
    
    // Stop when missile hits ground
    if (z <= 0 && t > 0) break;
  }
  
  return points;
};

export const MissileTrajectory3D: React.FC<MissileTrajectory3DProps> = ({
  trajectoryData,
  title = "Agni-V Trajectory Analysis",
  width = 800,
  height = 600
}) => {
  const [isPlaying, setIsPlaying] = React.useState(false);
  const [currentFrame, setCurrentFrame] = React.useState(0);
  const animationRef = useRef<NodeJS.Timeout>();
  
  const data = trajectoryData || generateTrajectoryData();
  const maxFrames = data.length;

  useEffect(() => {
    if (isPlaying && currentFrame < maxFrames - 1) {
      animationRef.current = setTimeout(() => {
        setCurrentFrame(prev => prev + 1);
      }, 100);
    } else if (currentFrame >= maxFrames - 1) {
      setIsPlaying(false);
    }

    return () => {
      if (animationRef.current) {
        clearTimeout(animationRef.current);
      }
    };
  }, [isPlaying, currentFrame, maxFrames]);

  const handlePlayPause = () => {
    setIsPlaying(!isPlaying);
  };

  const handleReset = () => {
    setIsPlaying(false);
    setCurrentFrame(0);
  };

  // Prepare data for plotting
  const fullTrajectoryX = data.map(point => point.x);
  const fullTrajectoryY = data.map(point => point.y);
  const fullTrajectoryZ = data.map(point => point.z);

  const currentTrajectoryX = data.slice(0, currentFrame + 1).map(point => point.x);
  const currentTrajectoryY = data.slice(0, currentFrame + 1).map(point => point.y);
  const currentTrajectoryZ = data.slice(0, currentFrame + 1).map(point => point.z);

  const currentPoint = data[currentFrame];

  const plotData = [
    // Full trajectory (faded)
    {
      x: fullTrajectoryX,
      y: fullTrajectoryY,
      z: fullTrajectoryZ,
      type: 'scatter3d' as const,
      mode: 'lines' as const,
      line: {
        color: 'rgba(59, 130, 246, 0.3)',
        width: 2,
      },
      name: 'Planned Trajectory',
      hovertemplate: 'Range: %{x:.1f} km<br>Cross-range: %{y:.1f} km<br>Altitude: %{z:.1f} km<extra></extra>',
    },
    // Current trajectory (bright)
    {
      x: currentTrajectoryX,
      y: currentTrajectoryY,
      z: currentTrajectoryZ,
      type: 'scatter3d' as const,
      mode: 'lines' as const,
      line: {
        color: '#3b82f6',
        width: 4,
      },
      name: 'Current Path',
      hovertemplate: 'Range: %{x:.1f} km<br>Cross-range: %{y:.1f} km<br>Altitude: %{z:.1f} km<extra></extra>',
    },
    // Current position
    {
      x: [currentPoint.x],
      y: [currentPoint.y],
      z: [currentPoint.z],
      type: 'scatter3d' as const,
      mode: 'markers' as const,
      marker: {
        color: '#ef4444',
        size: 8,
        symbol: 'diamond',
      },
      name: 'Current Position',
      hovertemplate: 'Range: %{x:.1f} km<br>Cross-range: %{y:.1f} km<br>Altitude: %{z:.1f} km<br>Velocity: ' + currentPoint.velocity.toFixed(0) + ' m/s<extra></extra>',
    },
    // Launch point
    {
      x: [0],
      y: [0],
      z: [0],
      type: 'scatter3d' as const,
      mode: 'markers' as const,
      marker: {
        color: '#22c55e',
        size: 10,
        symbol: 'square',
      },
      name: 'Launch Site',
      hovertemplate: 'Launch Site<br>Wheeler Island<extra></extra>',
    }
  ];

  const layout = {
    title: {
      text: title,
      font: { color: '#374151' }
    },
    scene: {
      xaxis: {
        title: 'Range (km)',
        gridcolor: '#e5e7eb',
        color: '#6b7280'
      },
      yaxis: {
        title: 'Cross-range (km)',
        gridcolor: '#e5e7eb',
        color: '#6b7280'
      },
      zaxis: {
        title: 'Altitude (km)',
        gridcolor: '#e5e7eb',
        color: '#6b7280'
      },
      bgcolor: 'transparent',
      camera: {
        eye: { x: 1.5, y: 1.5, z: 1.5 }
      }
    },
    paper_bgcolor: 'transparent',
    plot_bgcolor: 'transparent',
    font: { color: '#374151' },
    showlegend: true,
    legend: {
      font: { color: '#374151' }
    }
  };

  const config = {
    displayModeBar: true,
    displaylogo: false,
    modeBarButtonsToRemove: ['pan2d', 'lasso2d'],
    responsive: true
  };

  return (
    <div className="w-full bg-card p-6 rounded-lg border border-border">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-bold text-card-foreground">{title}</h3>
        <div className="flex items-center gap-2">
          <div className="bg-destructive text-destructive-foreground px-2 py-1 rounded text-xs font-medium">
            TOP SECRET
          </div>
          <div className="bg-primary/10 text-primary border border-primary px-2 py-1 rounded text-xs font-medium">
            SIMULATION
          </div>
        </div>
      </div>
      
      {/* Control Panel */}
      <div className="flex items-center justify-between mb-4 p-3 bg-muted/50 rounded-lg">
        <div className="flex items-center gap-4">
          <button
            onClick={handlePlayPause}
            className="flex items-center gap-2 px-3 py-1 bg-primary text-primary-foreground rounded hover:bg-primary/80 transition-colors"
          >
            {isPlaying ? '⏸️' : '▶️'}
            {isPlaying ? 'Pause' : 'Play'}
          </button>
          <button
            onClick={handleReset}
            className="flex items-center gap-2 px-3 py-1 bg-secondary text-secondary-foreground rounded hover:bg-secondary/80 transition-colors"
          >
            🔄 Reset
          </button>
        </div>
        
        <div className="text-sm text-muted-foreground">
          Time: {currentPoint.time}s | Frame: {currentFrame + 1}/{maxFrames}
        </div>
      </div>

      {/* Telemetry Data */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
        <div className="p-3 bg-card rounded-lg border border-border">
          <div className="text-xs text-muted-foreground">Range</div>
          <div className="text-lg font-bold text-primary">
            {currentPoint.x.toFixed(1)} km
          </div>
        </div>
        <div className="p-3 bg-card rounded-lg border border-border">
          <div className="text-xs text-muted-foreground">Altitude</div>
          <div className="text-lg font-bold text-accent">
            {currentPoint.z.toFixed(1)} km
          </div>
        </div>
        <div className="p-3 bg-card rounded-lg border border-border">
          <div className="text-xs text-muted-foreground">Velocity</div>
          <div className="text-lg font-bold text-secondary">
            {currentPoint.velocity.toFixed(0)} m/s
          </div>
        </div>
        <div className="p-3 bg-card rounded-lg border border-border">
          <div className="text-xs text-muted-foreground">Flight Time</div>
          <div className="text-lg font-bold text-foreground">
            {Math.floor(currentPoint.time / 60)}:{(currentPoint.time % 60).toString().padStart(2, '0')}
          </div>
        </div>
      </div>

      {/* 3D Plot */}
      <div className="w-full">
        <Plot
          data={plotData}
          layout={layout}
          config={config}
          style={{ width: '100%', height: `${height}px` }}
          useResizeHandler={true}
        />
      </div>
    </div>
  );
};
