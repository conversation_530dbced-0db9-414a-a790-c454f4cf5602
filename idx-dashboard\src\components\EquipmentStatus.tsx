import React from 'react';
import { Pie } from 'react-chartjs-2';
import { Chart as ChartJS, ArcElement, Tooltip, Legend } from 'chart.js';

ChartJS.register(ArcElement, Tooltip, Legend);

const EquipmentStatus: React.FC = () => {
  const data = {
    labels: ['Operational', 'Down'],
    datasets: [{
      data: [95, 5],
      backgroundColor: ['hsl(var(--success) / 0.8)', 'hsl(var(--destructive) / 0.8)'],
      borderColor: ['hsl(var(--success))', 'hsl(var(--destructive))'],
      borderWidth: 2,
    }],
  };

  const options = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: { position: 'right' as const },
      title: { display: true, text: 'Equipment Health Status' }
    },
  };

  return (
    <div className="p-6">
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-foreground">Equipment Health</h1>
            <p className="text-muted-foreground">Real-time equipment monitoring and status</p>
          </div>
          <div className="bg-success text-success-foreground px-3 py-1 rounded text-sm font-medium">
            OPERATIONAL
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <div className="bg-card p-6 rounded-lg border border-border">
            <h3 className="text-lg font-semibold text-card-foreground mb-4 flex items-center gap-2">
              ⚙️ Equipment Distribution
            </h3>
            <div className="h-64">
              <Pie data={data} options={options} />
            </div>
          </div>

          <div className="bg-card p-6 rounded-lg border border-border">
            <h3 className="text-lg font-semibold text-card-foreground mb-4 flex items-center gap-2">
              📊 Status Overview
            </h3>
            <div className="space-y-4">
              <div className="flex items-center justify-between p-3 bg-success/10 rounded-lg">
                <span className="font-medium">Operational Equipment</span>
                <div className="flex items-center gap-2">
                  <span className="text-2xl font-bold text-success">95%</span>
                  <div className="w-3 h-3 bg-success rounded-full"></div>
                </div>
              </div>
              <div className="flex items-center justify-between p-3 bg-destructive/10 rounded-lg">
                <span className="font-medium">Down for Maintenance</span>
                <div className="flex items-center gap-2">
                  <span className="text-2xl font-bold text-destructive">5%</span>
                  <div className="w-3 h-3 bg-destructive rounded-full"></div>
                </div>
              </div>
              <div className="flex items-center justify-between p-3 bg-primary/10 rounded-lg">
                <span className="font-medium">Total Units</span>
                <div className="flex items-center gap-2">
                  <span className="text-2xl font-bold text-primary">247</span>
                  <div className="w-3 h-3 bg-primary rounded-full"></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default EquipmentStatus;
