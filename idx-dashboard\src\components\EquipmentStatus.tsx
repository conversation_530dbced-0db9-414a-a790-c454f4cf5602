import React from 'react';
import { Pie } from 'react-chartjs-2';
import { Chart as ChartJS, ArcElement, Tooltip, Legend } from 'chart.js';

ChartJS.register(ArcElement, Tooltip, Legend);

const EquipmentStatus: React.FC = () => {
  const data = {
    labels: ['Operational', 'Down'],
    datasets: [{
      data: [95, 5],
      backgroundColor: ['rgba(0, 48, 135, 0.7)', 'rgba(255, 153, 51, 0.7)'],
      borderWidth: 1,
    }],
  };

  const options = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: { position: 'right' as const },
      title: { display: true, text: 'Equipment Health' }
    },
  };

  return (
    <div className="space-y-4">
      <h2 className="text-2xl font-semibold">Equipment Health</h2>
      <div className="bg-white p-4 rounded-lg shadow-md h-64">
        <Pie data={data} options={options} />
      </div>
    </div>
  );
};

export default EquipmentStatus;
