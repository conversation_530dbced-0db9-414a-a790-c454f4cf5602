import React, { useEffect, useRef } from 'react';
import * as d3 from 'd3';

const EquipmentStatus: React.FC = () => {
  const chartRef = useRef<SVGSVGElement>(null);

  const drawPieChart = (svgRef: SVGSVGElement, data: number[], labels: string[]) => {
    const svg = d3.select(svgRef);
    const width = 400;
    const height = 300;
    const radius = Math.min(width, height) / 2 - 20;

    svg.selectAll('*').remove();
    svg.attr('width', width).attr('height', height);

    const g = svg.append('g')
      .attr('transform', `translate(${width / 2},${height / 2})`);

    const pie = d3.pie<number>().value((d) => d);
    const arc = d3.arc<d3.PieArcDatum<number>>().innerRadius(0).outerRadius(radius);

    const colors = ['#003087', '#FF9933'];

    const arcs = g.selectAll('.arc')
      .data(pie(data))
      .enter()
      .append('g')
      .attr('class', 'arc');

    arcs.append('path')
      .attr('d', arc)
      .attr('fill', (d, i) => colors[i % colors.length])
      .attr('stroke', '#FFFFFF')
      .style('stroke-width', '2px')
      .style('transition', 'all 0.3s');

    arcs.append('text')
      .attr('transform', (d) => `translate(${arc.centroid(d)})`)
      .attr('dy', '0.35em')
      .text((d, i) => `${labels[i]}: ${data[i]}%`)
      .style('text-anchor', 'middle')
      .style('font-size', '14px')
      .style('fill', '#FFFFFF')
      .style('font-weight', 'bold')
      .style('transition', 'all 0.3s');
  };

  useEffect(() => {
    if (chartRef.current) {
      drawPieChart(chartRef.current, [95, 5], ['Operational', 'Down']);
    }
  }, []);

  return (
    <div className="space-y-6">
      <h2 className="text-3xl font-semibold">Equipment Status</h2>
      <div className="bg-white p-6 rounded-lg shadow-lg">
        <svg ref={chartRef} className="w-full h-64"></svg>
      </div>
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-white p-6 rounded-lg shadow-lg text-center">
          <div className="text-3xl font-bold text-green-600">95%</div>
          <div className="text-sm text-gray-600">Operational</div>
        </div>
        <div className="bg-white p-6 rounded-lg shadow-lg text-center">
          <div className="text-3xl font-bold text-red-600">5%</div>
          <div className="text-sm text-gray-600">Down</div>
        </div>
        <div className="bg-white p-6 rounded-lg shadow-lg text-center">
          <div className="text-3xl font-bold text-drdo-navy">52</div>
          <div className="text-sm text-gray-600">Total Units</div>
        </div>
      </div>
    </div>
  );
};

export default EquipmentStatus;
