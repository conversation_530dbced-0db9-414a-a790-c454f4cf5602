{"type": "Topology", "objects": {"coastlines": {"type": "GeometryCollection", "geometries": [{"type": "LineString", "arcs": [0]}, {"type": "LineString", "arcs": [1, 2]}, {"type": "LineString", "arcs": [3, 4]}, {"type": "LineString", "arcs": [5, 6, 7, 8]}, {"type": "LineString", "arcs": [9]}, {"type": "LineString", "arcs": [10]}, {"type": "LineString", "arcs": [11]}, {"type": "LineString", "arcs": [12]}, {"type": "LineString", "arcs": [13]}, {"type": "LineString", "arcs": [14]}, {"type": "LineString", "arcs": [15]}, {"type": "LineString", "arcs": [16]}, {"type": "LineString", "arcs": [17]}, {"type": "LineString", "arcs": [18]}, {"type": "LineString", "arcs": [19]}, {"type": "LineString", "arcs": [20]}, {"type": "LineString", "arcs": [21]}, {"type": "LineString", "arcs": [22, 23, 24]}, {"type": "LineString", "arcs": [25]}, {"type": "LineString", "arcs": [26]}, {"type": "LineString", "arcs": [27, 28, 29, 30, 31]}, {"type": "LineString", "arcs": [32]}, {"type": "LineString", "arcs": [33]}, {"type": "LineString", "arcs": [34]}, {"type": "LineString", "arcs": [35]}, {"type": "LineString", "arcs": [36]}, {"type": "LineString", "arcs": [37]}, {"type": "LineString", "arcs": [38, 39, 40, 41, 42]}, {"type": "LineString", "arcs": [43]}, {"type": "LineString", "arcs": [44]}, {"type": "LineString", "arcs": [45]}, {"type": "LineString", "arcs": [46]}, {"type": "LineString", "arcs": [47]}, {"type": "LineString", "arcs": [48]}, {"type": "LineString", "arcs": [49]}, {"type": "LineString", "arcs": [50]}, {"type": "LineString", "arcs": [51]}, {"type": "LineString", "arcs": [52]}, {"type": "LineString", "arcs": [53]}, {"type": "LineString", "arcs": [54]}, {"type": "LineString", "arcs": [55]}, {"type": "LineString", "arcs": [56]}, {"type": "LineString", "arcs": [57]}, {"type": "LineString", "arcs": [58]}, {"type": "LineString", "arcs": [59]}, {"type": "LineString", "arcs": [60]}, {"type": "LineString", "arcs": [61]}, {"type": "LineString", "arcs": [62]}, {"type": "LineString", "arcs": [63]}, {"type": "LineString", "arcs": [64]}, {"type": "LineString", "arcs": [65]}, {"type": "LineString", "arcs": [66]}, {"type": "LineString", "arcs": [67]}, {"type": "LineString", "arcs": [68]}, {"type": "LineString", "arcs": [69]}, {"type": "LineString", "arcs": [70]}, {"type": "LineString", "arcs": [71]}, {"type": "LineString", "arcs": [72]}, {"type": "LineString", "arcs": [73]}, {"type": "LineString", "arcs": [74]}, {"type": "LineString", "arcs": [75]}, {"type": "LineString", "arcs": [76]}, {"type": "LineString", "arcs": [77]}, {"type": "LineString", "arcs": [78]}, {"type": "LineString", "arcs": [79]}, {"type": "LineString", "arcs": [80]}, {"type": "LineString", "arcs": [81]}, {"type": "LineString", "arcs": [82]}, {"type": "LineString", "arcs": [83]}, {"type": "LineString", "arcs": [84]}, {"type": "LineString", "arcs": [85]}, {"type": "LineString", "arcs": [86]}, {"type": "LineString", "arcs": [87, 88, 89, 90, 91]}, {"type": "LineString", "arcs": [92]}, {"type": "LineString", "arcs": [93]}, {"type": "LineString", "arcs": [94]}, {"type": "LineString", "arcs": [95]}, {"type": "LineString", "arcs": [96]}, {"type": "LineString", "arcs": [97]}, {"type": "LineString", "arcs": [98, 99, 100, 101, 102, 103, 104, 105, 106, 107]}, {"type": "LineString", "arcs": [108, 109, 110, 111]}, {"type": "LineString", "arcs": [112, 113, 114, 115, 116]}, {"type": "LineString", "arcs": [117]}, {"type": "LineString", "arcs": [118]}, {"type": "LineString", "arcs": [119]}, {"type": "LineString", "arcs": [120]}, {"type": "LineString", "arcs": [121]}, {"type": "LineString", "arcs": [122, 123, 124, 125, 126, 127, 128, 129, 130, 131, 132, 133, 134, 135, 136, 137, 138, 139, 140, 141, 142, 143, 144, 145, 146, 147, 148, 149, 150, 151, 152, 153, 154, 155, 156, 157, 158, 159, 160, 161, 162, 163, 164, 165, 166, 167, 168, 169, 170, 171, 172, 173, 174, 175, 176, 177, 178, 179, 180, 181, 182, 183, 184, 185]}, {"type": "LineString", "arcs": [186, 187]}, {"type": "LineString", "arcs": [188, 189, 190, 191]}, {"type": "LineString", "arcs": [192]}, {"type": "LineString", "arcs": [193]}, {"type": "LineString", "arcs": [194]}, {"type": "LineString", "arcs": [195, 196, 197, 198, 199, 200, 201, 202, 203, 204, 205, 206, 207, 208, 209, -194, 210, 211, 212, 213, 214, 215, 216, 217, 218, 219, 220, 221, 222, 223, 224, 225, 226, 227, 228, 229, 230, 231, 232, 233, 234, 235, 236, 237, 238, 239, 240, 241, 242, 243, 244, 245, 246, 247, 248, 249, 250, 251, 252, 253, 254, 255, 256, 257]}, {"type": "LineString", "arcs": [258, 259, 260, 261, 262, 263, 264, 265, 266, 267, 268, 269, 270, 271, 272, 273, 274, 275, 276, 277, 278, 279, 280, 281, 282, 283, 284, 285, 286, 287, 288, 289, 290, 291, 292, 293, 294, 295, 296, 297, 298, 299, 300, 301, 302, 303, 304, 305, 306, 307, 308, 309, 310, 311, 312, 313, 314, 315, 316, 317, 318, 319, 320, 321, 322, 323, 324, 325, 326, 327, 328, 329, 330, 331]}, {"type": "LineString", "arcs": [332]}, {"type": "LineString", "arcs": [333]}, {"type": "LineString", "arcs": [334, 335, 336]}, {"type": "LineString", "arcs": [337, 338, 339, 340, 341]}, {"type": "LineString", "arcs": [-333, 342]}, {"type": "LineString", "arcs": [343]}, {"type": "LineString", "arcs": [344, 345, 346]}, {"type": "LineString", "arcs": [347, 348, 349]}, {"type": "LineString", "arcs": [350]}, {"type": "LineString", "arcs": [351]}, {"type": "LineString", "arcs": [352]}, {"type": "LineString", "arcs": [353]}, {"type": "LineString", "arcs": [354]}, {"type": "LineString", "arcs": [355]}, {"type": "LineString", "arcs": [356]}, {"type": "LineString", "arcs": [357]}, {"type": "LineString", "arcs": [358]}, {"type": "LineString", "arcs": [359]}, {"type": "LineString", "arcs": [360]}, {"type": "LineString", "arcs": [361]}, {"type": "LineString", "arcs": [362]}, {"type": "LineString", "arcs": [363, 364, 365, 366, 367, 368, 369, 370]}, {"type": "LineString", "arcs": [371]}, {"type": "LineString", "arcs": [372]}, {"type": "LineString", "arcs": [373]}, {"type": "LineString", "arcs": [374]}, {"type": "LineString", "arcs": [375, 376]}, {"type": "LineString", "arcs": [377, 378, 379, 380, 381]}, {"type": "LineString", "arcs": [382]}, {"type": "LineString", "arcs": [383]}, {"type": "LineString", "arcs": [384]}, {"type": "LineString", "arcs": [385]}, {"type": "LineString", "arcs": [386]}, {"type": "LineString", "arcs": [387]}, {"type": "LineString", "arcs": [388]}, {"type": "LineString", "arcs": [389, 390, 391]}, {"type": "LineString", "arcs": [392]}, {"type": "LineString", "arcs": [393]}, {"type": "LineString", "arcs": [394]}]}, "land": {"type": "GeometryCollection", "geometries": [{"type": "Polygon", "arcs": [[49]]}, {"type": "Polygon", "arcs": [[0]]}, {"type": "Polygon", "arcs": [[48]]}, {"type": "Polygon", "arcs": [[52]]}, {"type": "Polygon", "arcs": [[53]]}, {"type": "Polygon", "arcs": [[51]]}, {"type": "Polygon", "arcs": [[50]]}, {"type": "Polygon", "arcs": [[337, 443, 444, 445, 340, 446]]}, {"type": "Polygon", "arcs": [[114, 447, 116, 112, 113]]}, {"type": "Polygon", "arcs": [[350]]}, {"type": "Polygon", "arcs": [[351]]}, {"type": "Polygon", "arcs": [[65]]}, {"type": "Polygon", "arcs": [[64]]}, {"type": "Polygon", "arcs": [[63]]}, {"type": "Polygon", "arcs": [[54]]}, {"type": "Polygon", "arcs": [[352]]}, {"type": "Polygon", "arcs": [[448, 348, 449, 450]]}, {"type": "Polygon", "arcs": [[333, -451]]}, {"type": "Polygon", "arcs": [[45]]}, {"type": "Polygon", "arcs": [[46]]}, {"type": "Polygon", "arcs": [[44]]}, {"type": "Polygon", "arcs": [[66]]}, {"type": "Polygon", "arcs": [[57]]}, {"type": "Polygon", "arcs": [[72]]}, {"type": "Polygon", "arcs": [[59]]}, {"type": "Polygon", "arcs": [[58]]}, {"type": "Polygon", "arcs": [[336, 334, 335]]}, {"type": "Polygon", "arcs": [[74]]}, {"type": "Polygon", "arcs": [[73]]}, {"type": "Polygon", "arcs": [[60]]}, {"type": "Polygon", "arcs": [[61]]}, {"type": "Polygon", "arcs": [[75]]}, {"type": "Polygon", "arcs": [[47]]}, {"type": "Polygon", "arcs": [[62]]}, {"type": "Polygon", "arcs": [[56]]}, {"type": "Polygon", "arcs": [[69]]}, {"type": "Polygon", "arcs": [[68]]}, {"type": "Polygon", "arcs": [[55]]}, {"type": "Polygon", "arcs": [[3, 4]]}, {"type": "Polygon", "arcs": [[71]]}, {"type": "Polygon", "arcs": [[70]]}, {"type": "Polygon", "arcs": [[76]]}, {"type": "Polygon", "arcs": [[8, 5, 6, 7]]}, {"type": "Polygon", "arcs": [[79]]}, {"type": "Polygon", "arcs": [[67]]}, {"type": "Polygon", "arcs": [[353]]}, {"type": "Polygon", "arcs": [[78]]}, {"type": "Polygon", "arcs": [[94]]}, {"type": "Polygon", "arcs": [[96]]}, {"type": "Polygon", "arcs": [[97]]}, {"type": "Polygon", "arcs": [[77]]}, {"type": "Polygon", "arcs": [[95]]}, {"type": "Polygon", "arcs": [[15]]}, {"type": "Polygon", "arcs": [[16]]}, {"type": "Polygon", "arcs": [[188, 451, 190, 191]]}, {"type": "Polygon", "arcs": [[80]]}, {"type": "Polygon", "arcs": [[354]]}, {"type": "Polygon", "arcs": [[355]]}, {"type": "Polygon", "arcs": [[356]]}, {"type": "Polygon", "arcs": [[357]]}, {"type": "Polygon", "arcs": [[358]]}, {"type": "Polygon", "arcs": [[17]]}, {"type": "Polygon", "arcs": [[359]]}, {"type": "Polygon", "arcs": [[81]]}, {"type": "Polygon", "arcs": [[360]]}, {"type": "Polygon", "arcs": [[361]]}, {"type": "Polygon", "arcs": [[194]]}, {"type": "Polygon", "arcs": [[452, 453, 40, 454, 455, 42, 38]]}, {"type": "Polygon", "arcs": [[43]]}, {"type": "Polygon", "arcs": [[192]]}, {"type": "Polygon", "arcs": [[84]]}, {"type": "Polygon", "arcs": [[82]]}, {"type": "Polygon", "arcs": [[85]]}, {"type": "Polygon", "arcs": [[83]]}, {"type": "Polygon", "arcs": [[362]]}, {"type": "Polygon", "arcs": [[371]]}, {"type": "Polygon", "arcs": [[35]]}, {"type": "Polygon", "arcs": [[18]]}, {"type": "Polygon", "arcs": [[34]]}, {"type": "Polygon", "arcs": [[93]]}, {"type": "Polygon", "arcs": [[2, 1]]}, {"type": "Polygon", "arcs": [[86]]}, {"type": "Polygon", "arcs": [[33]]}, {"type": "Polygon", "arcs": [[88, 89, 90, 91, 87]]}, {"type": "Polygon", "arcs": [[32]]}, {"type": "Polygon", "arcs": [[372]]}, {"type": "Polygon", "arcs": [[373]]}, {"type": "Polygon", "arcs": [[36]]}, {"type": "Polygon", "arcs": [[19]]}, {"type": "Polygon", "arcs": [[92]]}, {"type": "Polygon", "arcs": [[374]]}, {"type": "Polygon", "arcs": [[-333, 342, 456]]}, {"type": "Polygon", "arcs": [[376, 457]]}, {"type": "Polygon", "arcs": [[344, 345, 458, 459]]}, {"type": "Polygon", "arcs": [[343, 460]]}, {"type": "Polygon", "arcs": [[461, 145, 146, 462, 149, 150, 151, 152, 153, 463, 155, 156, 157, 158, 159, 160, 161, 464, 164, 165, 166, 167, 168, 169, 170, 171, 465, 466, 174, 175, 176, 467, 178, 179, 180, 468, 469, 182, 183, 470, 185, 186, 471, 98, 99, 100, 101, 102, 472, 104, 105, 106, 107, -112, -111, -110, -109, 122, 473, 124, 125, 474, 127, 128, 129, 475, 131, 476, 134, 135, 136, 137, 477, 139, 140, 141, 142, 143]]}, {"type": "Polygon", "arcs": [[377, 478, 379, 479, 381]]}, {"type": "Polygon", "arcs": [[394]]}, {"type": "Polygon", "arcs": [[382]]}, {"type": "Polygon", "arcs": [[20]]}, {"type": "Polygon", "arcs": [[480, 24, 22]]}, {"type": "Polygon", "arcs": [[383]]}, {"type": "Polygon", "arcs": [[21]]}, {"type": "Polygon", "arcs": [[29, 481, 31, 27, 482]]}, {"type": "Polygon", "arcs": [[384]]}, {"type": "Polygon", "arcs": [[9]]}, {"type": "Polygon", "arcs": [[385]]}, {"type": "Polygon", "arcs": [[386]]}, {"type": "Polygon", "arcs": [[25]]}, {"type": "Polygon", "arcs": [[118]]}, {"type": "Polygon", "arcs": [[12]]}, {"type": "Polygon", "arcs": [[26]]}, {"type": "Polygon", "arcs": [[-369, -368, -367, 483, -365, -364, -371, -370], [484, 259, 485, 261, 486, 263, 264, 265, 266, 487, 268, 269, 488, 271, 272, 273, 274, 275, 276, 277, 278, 279, 489, 281, 282, 283, 284, 285, 286, 287, 288, 289, 290, 291, 292, 490, 294, 295, 296, 297, 298, 299, 300, 301, 302, 491, 304, 305, 306, 307, 308, 492, 310, 311, 312, 313, 314, 315, 316, 317, 318, 319, 320, 321, 322, 323, 324, 325, 326, 327, 328, 329, 330, 331, 195, 196, 197, 198, 199, 200, 493, 202, 203, 204, 205, 206, 207, 208, 209, -194, 210, 211, 212, 213, 214, 215, 216, 217, 218, 219, 220, 221, 222, 223, 224, 225, 226, 227, 228, 229, 230, 231, 232, 233, 234, 235, 236, 237, 238, 239, 240, 241, 242, 243, 244, 494, 246, 495, 248, 496, 250, 497, 252, 498, 254, 499, 256, 500, -457]]}, {"type": "Polygon", "arcs": [[10]]}, {"type": "Polygon", "arcs": [[13]]}, {"type": "Polygon", "arcs": [[120]]}, {"type": "Polygon", "arcs": [[14]]}, {"type": "Polygon", "arcs": [[11]]}, {"type": "Polygon", "arcs": [[37]]}, {"type": "Polygon", "arcs": [[387]]}, {"type": "Polygon", "arcs": [[121]]}, {"type": "Polygon", "arcs": [[119]]}, {"type": "Polygon", "arcs": [[117]]}, {"type": "Polygon", "arcs": [[388]]}, {"type": "Polygon", "arcs": [[389, 501, 391]]}, {"type": "Polygon", "arcs": [[392]]}, {"type": "Polygon", "arcs": [[393]]}]}, "ocean": {"type": "GeometryCollection", "geometries": [{"type": "Polygon", "arcs": [[370, 363, 364, -484, 366, 367, 368, 369]]}, {"type": "Polygon", "arcs": [[-447, -341, -446, -445, -444, -338], [-384], [-386], [-385], [-121], [-122], [-389], [-120], [-394], [-118], [-119], [-93], [-143, -142, -141, -140, -478, -138, -137, -136, -135, -477, -132, -476, -130, -129, -128, -475, -126, -125, -474, -123, 108, 109, 110, 111, -108, -107, -106, -105, -473, -103, -102, -101, -100, -99, -472, -187, -186, -471, -184, -183, -470, -469, -181, -180, -179, -468, -177, -176, -175, -467, -466, -172, -171, -170, -169, -168, -167, -166, -165, -465, -162, -161, -160, -159, -158, -157, -156, -464, -154, -153, -152, -151, -150, -463, -147, -146, -462, -144], [-21], [-383], [-374], [-373], [-20], [-375], [-25, -481, -23], [-22], [-28, -32, -482, -30, -483], [-480, -380, -479, -378, -382], [-395], [-458, -377], [-26], [-387], [-13], [-393], [-27], [-14], [-38], [-15], [-502, -390, -392], [-12], [-11], [-10], [-92, -91, -90, -89, -88], [-19], [-363], [-372], [-3, -2], [-362], [-361], [-360], [-18], [-191, -452, -189, -192], [-16], [-17], [-354], [-67], [-47], [-46], [-353], [-55], [-64], [-65], [-66], [-45], [-58], [-4, -5], [-56], [-57], [-63], [-62], [-61], [-59], [-60], [-335, -337, -336], [-73], [-75], [-74], [-76], [-48], [-77], [-7, -6, -9, -8], [-72], [-71], [-69], [-70], [-80], [-95], [-97], [-98], [-79], [-78], [-96], [-81], [-352], [-351], [-113, -117, -448, -115, -114], [-355], [-356], [-357], [-358], [-359], [-36], [-35], [-34], [-33], [-37], [-388], [-51], [-52], [-54], [-53], [-1], [-49], [-50], [-82], [-83], [-195], [-84], [-94], [-68], [-193], [-44], [-43, -456, -455, -41, -454, -453, -39], [-85], [-86], [-87], [-343, 332, -501, -257, -500, -255, -499, -253, -498, -251, -497, -249, -496, -247, -495, -245, -244, -243, -242, -241, -240, -239, -238, -237, -236, -235, -234, -233, -232, -231, -230, -229, -228, -227, -226, -225, -224, -223, -222, -221, -220, -219, -218, -217, -216, -215, -214, -213, -212, -211, 193, -210, -209, -208, -207, -206, -205, -204, -203, -494, -201, -200, -199, -198, -197, -196, -332, -331, -330, -329, -328, -327, -326, -325, -324, -323, -322, -321, -320, -319, -318, -317, -316, -315, -314, -313, -312, -311, -493, -309, -308, -307, -306, -305, -492, -303, -302, -301, -300, -299, -298, -297, -296, -295, -491, -293, -292, -291, -290, -289, -288, -287, -286, -285, -284, -283, -282, -490, -280, -279, -278, -277, -276, -275, -274, -273, -272, -489, -270, -269, -488, -267, -266, -265, -264, -487, -262, -486, -260, -485], [-334, -450, -349, -449], [-347, -346, 502, -344, 503]]}]}, "lakes": {"type": "GeometryCollection", "geometries": [{"type": "Polygon", "arcs": [[504]]}, {"type": "Polygon", "arcs": [[505]]}, {"type": "Polygon", "arcs": [[506]]}, {"type": "Polygon", "arcs": [[507, 508]]}, {"type": "Polygon", "arcs": [[509, 510, 511, 512, 513]]}, {"type": "Polygon", "arcs": [[514, 515, 516, 517, 518]]}, {"type": "Polygon", "arcs": [[519, 520]]}, {"type": "Polygon", "arcs": [[521]]}, {"type": "Polygon", "arcs": [[522]]}, {"type": "Polygon", "arcs": [[523]]}, {"type": "Polygon", "arcs": [[524]]}, {"type": "Polygon", "arcs": [[525, 526]]}, {"type": "Polygon", "arcs": [[527]]}, {"type": "Polygon", "arcs": [[528]]}, {"type": "Polygon", "arcs": [[529]]}, {"type": "Polygon", "arcs": [[530]]}, {"type": "Polygon", "arcs": [[531]]}, {"type": "Polygon", "arcs": [[532]]}, {"type": "Polygon", "arcs": [[533]]}, {"type": "Polygon", "arcs": [[534]]}, {"type": "Polygon", "arcs": [[535]]}, {"type": "Polygon", "arcs": [[536]]}, {"type": "Polygon", "arcs": [[537]]}, {"type": "Polygon", "arcs": [[538, 539, 540, 541]]}, {"type": "Polygon", "arcs": [[-540, 542, 543, 544, 545, 546, 547]]}]}, "rivers": {"type": "GeometryCollection", "geometries": [{"type": "LineString", "arcs": [395]}, {"type": "LineString", "arcs": [396, 397, 398, 399, 400, 401, 402]}, {"type": "LineString", "arcs": [403]}, {"type": "LineString", "arcs": [404]}, {"type": "LineString", "arcs": [405, 406, 407, 408, 409, 410, 411, 412]}, {"type": "LineString", "arcs": [413, 414, 415, 416, 417, 418, 419]}, {"type": "LineString", "arcs": [420, 421, 422, 423]}, {"type": "LineString", "arcs": [424]}, {"type": "LineString", "arcs": [425]}, {"type": "LineString", "arcs": [426]}, {"type": "LineString", "arcs": [427]}, {"type": "LineString", "arcs": [428, 429, 430, 431, 432, 433, 434, 435, 436, 437, 438, 439, 440, 441]}, {"type": "LineString", "arcs": [442]}]}, "countries": {"type": "GeometryCollection", "geometries": [{"type": "MultiPolygon", "properties": {"ct": [178, -17.83]}, "id": "FJI", "arcs": [[], [[352]], [[333, 448, 348, 349, 548]]]}, {"type": "Polygon", "properties": {"ct": [34.75, -6.26]}, "id": "TZA", "arcs": [[549, 550, 306, 551, 552, 553, 554, 555, 556, 557, 558]]}, {"type": "Polygon", "properties": {"ct": [-12.14, 24.29]}, "id": "ESH", "arcs": [[559, 560, 196, 561]]}, {"type": "MultiPolygon", "properties": {"ct": [-101.57, 57.75]}, "id": "CAN", "arcs": [[[141, 562, 143, 144, 145, 563, 564, 565, 566, 567, 568, 569, 570, 571, 572, 573, 574, 575, 576, 577, 578]], [[373]], [[382]], [[372]], [[9]], [[10]], [[11]], [[12]], [[13]], [[14]], [[18]], [[19]], [[20]], [[21]], [[26]], [[34]], [[37]], [[35]], [[27, 28, 29, 30, 31]], [[25]], [[377, 478, 379, 380, 381]], [[22, 480, 24]], [[394]], [[386]], [[389, 501, 391]], [[392]], [[374]], [[376, 457]], [[371]], [[362]]]}, {"type": "MultiPolygon", "properties": {"ct": [-99.06, 39.5]}, "id": "USA", "arcs": [[[-579, -578, -577, -576, -575, -574, -573, -572, -571, -570, -569, -568, -567, -566, -565, -564, 146, 147, 148, 149, 150, 151, 152, 153, 154, 155, 156, 157, 158, 159, 160, 161, 162, 163, 164, 165, 166, 167, 168, 169, 170, 171, 172, 173, 174, 175, 176, 579, 580, 581, 582, 136, 137, 138, 139, 140]], [[354]], [[355]], [[356]], [[357]], [[358]], [[32]], [[33]], [[-563, 142]], [[36]]]}, {"type": "Polygon", "properties": {"ct": [67.28, 48.19]}, "id": "KAZ", "arcs": [[583, 584, 585, 586, 587, 588, -367, -366, -365, 589]]}, {"type": "Polygon", "properties": {"ct": [63.2, 41.75]}, "id": "UZB", "arcs": [[-588, -587, -586, 590, 591, 592, 593]]}, {"type": "MultiPolygon", "properties": {"ct": [144.33, -6.65]}, "id": "PNG", "arcs": [[[3, 594]], [[55]], [[56]], [[62]]]}, {"type": "MultiPolygon", "properties": {"ct": [114.02, -0.25]}, "id": "IDN", "arcs": [[[-595, 4]], [[595, 335]], [[47]], [[7, 596]], [[68]], [[69]], [[70]], [[71]], [[72]], [[73]], [[74]], [[75]], [[76]]]}, {"type": "MultiPolygon", "properties": {"ct": [-65.15, -35.22]}, "id": "ARG", "arcs": [[[113, 597]], [[598, 106, 599, 600, 601, 602, 603, 604]]]}, {"type": "MultiPolygon", "properties": {"ct": [-71.67, -37.34]}, "id": "CHL", "arcs": [[[-598, 114, 115, 116, 112]], [[605, -600, 107, -112, 606]]]}, {"type": "Polygon", "properties": {"ct": [23.58, -2.85]}, "id": "COD", "arcs": [[-555, 607, 608, 423, 313, 609, 610, -422, 611, 612, 613, 614, 615, 616]]}, {"type": "Polygon", "properties": {"ct": [45.73, 4.75]}, "id": "SOM", "arcs": [[617, 618, 619, 302, 303, 304]]}, {"type": "Polygon", "properties": {"ct": [37.79, 0.6]}, "id": "KEN", "arcs": [[-551, -550, 620, 621, 622, -618, 305]]}, {"type": "Polygon", "properties": {"ct": [29.86, 15.99]}, "id": "SDN", "arcs": [[623, 624, 625, 626, 298, 627, 628, 629]]}, {"type": "Polygon", "properties": {"ct": [18.58, 15.33]}, "id": "TCD", "arcs": [[-625, 630, 631, 632, 633]]}, {"type": "Polygon", "properties": {"ct": [-72.66, 18.9]}, "id": "HTI", "arcs": [[634, 191]]}, {"type": "Polygon", "properties": {"ct": [-70.46, 18.88]}, "id": "DOM", "arcs": [[-635, 188, 451, 190]]}, {"type": "MultiPolygon", "properties": {"ct": [99.22, 61.69]}, "id": "RUS", "arcs": [[], [], [[388]], [[387]], [[385]], [[384]], [[383]], [[117]], [[635, 236, 636]], [[118]], [[93]], [[-333, 342, 484, 259, 260, 261, 262, 263, 637, 638, 639, 640, -590, -364, -371, 641, 642, -194, 210, 643, 644, 645, 646, 240, 647, 648, 244, 245, 246, 247, 248, 496, 250, 497, 252, 253, 254, 499, 256, 500]], [[343, -503, 345, 458]], [[649, 650, 213, 651]]]}, {"type": "MultiPolygon", "properties": {"ct": [-77.92, 24.51]}, "id": "BHS", "arcs": [[[360]], [[361]], [[359]]]}, {"type": "Polygon", "properties": {"ct": [-59.42, -51.71]}, "id": "FLK", "arcs": [[350]]}, {"type": "MultiPolygon", "properties": {"ct": [14.24, 64.54]}, "id": "NOR", "arcs": [[[121]], [[-649, 652, 653, 243]], [[119]], [[120]]]}, {"type": "Polygon", "properties": {"ct": [-41.5, 74.77]}, "id": "GRL", "arcs": [[393]]}, {"type": "Polygon", "properties": {"ct": [69.53, -49.31]}, "id": "ATF", "arcs": [[351]]}, {"type": "Polygon", "properties": {"ct": [125.97, -8.77]}, "id": "TLS", "arcs": [[336, 334, -596]]}, {"type": "Polygon", "properties": {"ct": [25.12, -28.96]}, "id": "ZAF", "arcs": [[654, 655, 656, 657, 658, 659, 308, 309, 310], [660]]}, {"type": "Polygon", "properties": {"ct": [28.17, -29.63]}, "id": "LSO", "arcs": [[-661]]}, {"type": "Polygon", "properties": {"ct": [-102.58, 23.94]}, "id": "MEX", "arcs": [[-583, -582, -581, -580, 177, 178, 661, 662, 135]]}, {"type": "Polygon", "properties": {"ct": [-56, -32.78]}, "id": "URY", "arcs": [[663, 105, -599]]}, {"type": "Polygon", "properties": {"ct": [-53.05, -10.81]}, "id": "BRA", "arcs": [[-664, -605, 664, 665, -416, 666, 667, 668, 669, 670, 671, 672, 673, 472, 104]]}, {"type": "Polygon", "properties": {"ct": [-64.64, -16.73]}, "id": "BOL", "arcs": [[-668, -667, 674, -601, -606, 675]]}, {"type": "Polygon", "properties": {"ct": [-74.39, -9.19]}, "id": "PER", "arcs": [[-669, -676, -607, -111, 676, 677]]}, {"type": "Polygon", "properties": {"ct": [-73.08, 3.93]}, "id": "COL", "arcs": [[-670, -678, 678, -109, 679, 98, 680]]}, {"type": "Polygon", "properties": {"ct": [-80.11, 8.53]}, "id": "PAN", "arcs": [[-680, 122, 473, 124, 681, 186, 187]]}, {"type": "Polygon", "properties": {"ct": [-84.18, 9.97]}, "id": "CRI", "arcs": [[-682, 125, 474, 127, 682, 470, 185]]}, {"type": "Polygon", "properties": {"ct": [-85.02, 12.85]}, "id": "NIC", "arcs": [[-683, 128, 683, 183]]}, {"type": "Polygon", "properties": {"ct": [-86.59, 14.82]}, "id": "HND", "arcs": [[-684, 129, 475, 684, 685, 469, 182]]}, {"type": "Polygon", "properties": {"ct": [-88.87, 13.73]}, "id": "SLV", "arcs": [[-685, 131, 132, 686]]}, {"type": "Polygon", "properties": {"ct": [-90.37, 15.7]}, "id": "GTM", "arcs": [[-663, 687, 180, 468, -686, -687, 133, 134]]}, {"type": "Polygon", "properties": {"ct": [-88.7, 17.2]}, "id": "BLZ", "arcs": [[-662, 179, -688]]}, {"type": "Polygon", "properties": {"ct": [-66.16, 7.16]}, "id": "VEN", "arcs": [[-671, -681, 99, 688]]}, {"type": "Polygon", "properties": {"ct": [-58.97, 4.79]}, "id": "GUY", "arcs": [[-672, -689, 100, 689]]}, {"type": "Polygon", "properties": {"ct": [-55.91, 4.12]}, "id": "SUR", "arcs": [[-673, -690, 101, 690]]}, {"type": "MultiPolygon", "properties": {"ct": [2.34, 46.61]}, "id": "FRA", "arcs": [[[-674, -691, 102]], [[691, 692, 693, 225, 694, 229, 695, 696]], [[85]]]}, {"type": "Polygon", "properties": {"ct": [-78.38, -1.45]}, "id": "ECU", "arcs": [[-677, -110, -679]]}, {"type": "Polygon", "properties": {"ct": [-66.48, 18.24]}, "id": "PRI", "arcs": [[15]]}, {"type": "Polygon", "properties": {"ct": [-77.32, 18.14]}, "id": "JAM", "arcs": [[16]]}, {"type": "Polygon", "properties": {"ct": [-78.96, 21.63]}, "id": "CUB", "arcs": [[17]]}, {"type": "Polygon", "properties": {"ct": [29.79, -18.91]}, "id": "ZWE", "arcs": [[-657, 697, 698, 699]]}, {"type": "Polygon", "properties": {"ct": [23.77, -22.1]}, "id": "BWA", "arcs": [[-656, 700, 701, -698]]}, {"type": "Polygon", "properties": {"ct": [17.16, -22.1]}, "id": "NAM", "arcs": [[-655, 311, 702, 703, -701]]}, {"type": "Polygon", "properties": {"ct": [-14.51, 14.35]}, "id": "SEN", "arcs": [[330, 704, 705, 706, 707, 328, 708]]}, {"type": "Polygon", "properties": {"ct": [-3.54, 17.27]}, "id": "MLI", "arcs": [[-706, 709, 710, 711, 712, 713, 714]]}, {"type": "Polygon", "properties": {"ct": [-10.33, 20.21]}, "id": "MRT", "arcs": [[-561, 715, -710, -705, 331, 195]]}, {"type": "Polygon", "properties": {"ct": [2.34, 9.65]}, "id": "BEN", "arcs": [[320, 716, 717, 718, 719]]}, {"type": "Polygon", "properties": {"ct": [9.32, 17.35]}, "id": "NER", "arcs": [[-633, 720, 721, -719, 722, -712, 723, 724]]}, {"type": "Polygon", "properties": {"ct": [8, 9.55]}, "id": "NGA", "arcs": [[-720, -722, 725, 319]]}, {"type": "Polygon", "properties": {"ct": [12.61, 5.66]}, "id": "CMR", "arcs": [[-632, 726, 727, 728, 729, 318, -726, -721]]}, {"type": "Polygon", "properties": {"ct": [1, 8.44]}, "id": "TGO", "arcs": [[-717, 321, 730, 731]]}, {"type": "Polygon", "properties": {"ct": [-1.24, 7.93]}, "id": "GHA", "arcs": [[-731, 322, 732, 733]]}, {"type": "Polygon", "properties": {"ct": [-5.61, 7.55]}, "id": "CIV", "arcs": [[-714, 734, -733, 323, 735, 736]]}, {"type": "Polygon", "properties": {"ct": [-11.06, 10.45]}, "id": "GIN", "arcs": [[-707, -715, -737, 737, 738, 326, 739]]}, {"type": "Polygon", "properties": {"ct": [-15.11, 12.02]}, "id": "GNB", "arcs": [[-708, -740, 327]]}, {"type": "Polygon", "properties": {"ct": [-9.41, 6.43]}, "id": "LBR", "arcs": [[-736, 324, 740, -738]]}, {"type": "Polygon", "properties": {"ct": [-11.8, 8.53]}, "id": "SLE", "arcs": [[-739, -741, 325]]}, {"type": "Polygon", "properties": {"ct": [-1.78, 12.31]}, "id": "BFA", "arcs": [[-713, -723, -718, -732, -734, -735]]}, {"type": "Polygon", "properties": {"ct": [20.37, 6.54]}, "id": "CAF", "arcs": [[-613, 741, -727, -631, -624, 742]]}, {"type": "Polygon", "properties": {"ct": [15.13, -0.84]}, "id": "COG", "arcs": [[-612, 421, -611, 743, 315, 744, -728, -742]]}, {"type": "Polygon", "properties": {"ct": [11.69, -0.65]}, "id": "GAB", "arcs": [[-729, -745, 316, 745]]}, {"type": "Polygon", "properties": {"ct": [10.37, 1.65]}, "id": "GNQ", "arcs": [[-730, -746, 317]]}, {"type": "Polygon", "properties": {"ct": [27.73, -13.4]}, "id": "ZMB", "arcs": [[-554, 746, 747, -699, -702, -704, 748, -608]]}, {"type": "Polygon", "properties": {"ct": [34.19, -13.17]}, "id": "MWI", "arcs": [[-553, 749, -747]]}, {"type": "Polygon", "properties": {"ct": [35.47, -17.23]}, "id": "MOZ", "arcs": [[-552, 307, -660, 750, -658, -700, -748, -750]]}, {"type": "Polygon", "properties": {"ct": [31.4, -26.49]}, "id": "SWZ", "arcs": [[-659, -751]]}, {"type": "MultiPolygon", "properties": {"ct": [17.5, -12.29]}, "id": "AGO", "arcs": [[[-610, 314, -744]], [[-424, -609, -749, -703, 312]]]}, {"type": "Polygon", "properties": {"ct": [29.91, -3.38]}, "id": "BDI", "arcs": [[-556, -617, 751]]}, {"type": "Polygon", "properties": {"ct": [35, 31.48]}, "id": "ISR", "arcs": [[752, 753, 754, 755, 205, 756, 757]]}, {"type": "Polygon", "properties": {"ct": [35.87, 33.91]}, "id": "LBN", "arcs": [[-757, 206, 758]]}, {"type": "Polygon", "properties": {"ct": [46.69, -19.36]}, "id": "MDG", "arcs": [[44]]}, {"type": "Polygon", "properties": {"ct": [35.27, 31.94]}, "id": "PSE", "arcs": [[-754, 759]]}, {"type": "Polygon", "properties": {"ct": [-15.43, 13.48]}, "id": "GMB", "arcs": [[-709, 329]]}, {"type": "Polygon", "properties": {"ct": [9.53, 34.17]}, "id": "TUN", "arcs": [[760, 202, 761]]}, {"type": "Polygon", "properties": {"ct": [2.6, 28.19]}, "id": "DZA", "arcs": [[-560, 762, 200, 201, -761, 763, -724, -711, -716]]}, {"type": "Polygon", "properties": {"ct": [36.78, 31.25]}, "id": "JOR", "arcs": [[-753, 764, 765, 766, 296, -755, -760]]}, {"type": "Polygon", "properties": {"ct": [54.21, 23.87]}, "id": "ARE", "arcs": [[289, 767, 291, 768, 769]]}, {"type": "Polygon", "properties": {"ct": [51.18, 25.32]}, "id": "QAT", "arcs": [[287, 770]]}, {"type": "Polygon", "properties": {"ct": [47.6, 29.31]}, "id": "KWT", "arcs": [[285, 771, 772]]}, {"type": "Polygon", "properties": {"ct": [43.76, 33.04]}, "id": "IRQ", "arcs": [[-766, 773, 774, 775, 284, -773, 776]]}, {"type": "MultiPolygon", "properties": {"ct": [56.1, 20.58]}, "id": "OMN", "arcs": [[[-769, 292, 777, 778]], [[-768, 290]]]}, {"type": "MultiPolygon", "properties": {"ct": [166.91, -15.22]}, "id": "VUT", "arcs": [[[45]], [[46]]]}, {"type": "Polygon", "properties": {"ct": [104.88, 12.68]}, "id": "KHM", "arcs": [[779, 780, 781, 273]]}, {"type": "Polygon", "properties": {"ct": [101.01, 15.02]}, "id": "THA", "arcs": [[-780, 274, 782, 276, 783, 399, 784, 401, 785]]}, {"type": "Polygon", "properties": {"ct": [103.75, 18.44]}, "id": "LAO", "arcs": [[-781, -786, -402, -785, -400, -399, 786, 787]]}, {"type": "Polygon", "properties": {"ct": [96.51, 21.02]}, "id": "MMR", "arcs": [[-784, 277, 788, 789, 790, 397, 398]]}, {"type": "Polygon", "properties": {"ct": [106.29, 16.66]}, "id": "VNM", "arcs": [[-782, -788, 791, 272]]}, {"type": "MultiPolygon", "properties": {"ct": [127.17, 40.14]}, "id": "PRK", "arcs": [[[792, 792, 792]], [[-638, 264, 793, 266, 267, 268, 794]]]}, {"type": "Polygon", "properties": {"ct": [127.82, 36.43]}, "id": "KOR", "arcs": [[-794, 265]]}, {"type": "Polygon", "properties": {"ct": [102.95, 46.82]}, "id": "MNG", "arcs": [[-640, 795]]}, {"type": "Polygon", "properties": {"ct": [79.59, 22.93]}, "id": "IND", "arcs": [[-790, 796, 279, 280, 281, 797, 798, 799, 800, 801, 802]]}, {"type": "Polygon", "properties": {"ct": [90.27, 23.84]}, "id": "BGD", "arcs": [[-789, 278, -797]]}, {"type": "Polygon", "properties": {"ct": [90.47, 27.43]}, "id": "BTN", "arcs": [[-802, 803]]}, {"type": "Polygon", "properties": {"ct": [84.01, 28.24]}, "id": "NPL", "arcs": [[-800, 804]]}, {"type": "Polygon", "properties": {"ct": [69.41, 29.97]}, "id": "PAK", "arcs": [[-798, 282, 805, 806, 807]]}, {"type": "Polygon", "properties": {"ct": [66.09, 33.86]}, "id": "AFG", "arcs": [[-593, 808, 809, -807, 810, 811]]}, {"type": "Polygon", "properties": {"ct": [71.03, 38.58]}, "id": "TJK", "arcs": [[-592, 812, 813, -809]]}, {"type": "Polygon", "properties": {"ct": [74.62, 41.51]}, "id": "KGZ", "arcs": [[-585, 814, -813, -591]]}, {"type": "Polygon", "properties": {"ct": [59.28, 39.09]}, "id": "TKM", "arcs": [[-589, -594, -812, 815, -368]]}, {"type": "Polygon", "properties": {"ct": [54.29, 32.52]}, "id": "IRN", "arcs": [[-776, 816, 817, 818, 819, -369, -816, -811, -806, 283]]}, {"type": "Polygon", "properties": {"ct": [38.54, 35.01]}, "id": "SYR", "arcs": [[-758, -759, 207, 820, -774, -765]]}, {"type": "Polygon", "properties": {"ct": [45, 40.22]}, "id": "ARM", "arcs": [[-819, 821, 822, 823, 824]]}, {"type": "Polygon", "properties": {"ct": [16.6, 62.81]}, "id": "SWE", "arcs": [[-654, 825, 242]]}, {"type": "Polygon", "properties": {"ct": [27.98, 53.51]}, "id": "BLR", "arcs": [[-645, 826, 827, 828, 829]]}, {"type": "Polygon", "properties": {"ct": [31.23, 49.15]}, "id": "UKR", "arcs": [[-644, 211, 830, -650, 831, 215, -413, 832, 833, 834, 835, 836, 837, -827]]}, {"type": "Polygon", "properties": {"ct": [19.31, 52.15]}, "id": "POL", "arcs": [[-828, -838, 838, 839, 840, 235, -636, 841]]}, {"type": "Polygon", "properties": {"ct": [14.08, 47.61]}, "id": "AUT", "arcs": [[842, 843, 844, 845, 846, 847, 848]]}, {"type": "Polygon", "properties": {"ct": [19.36, 47.2]}, "id": "HUN", "arcs": [[-836, 849, 850, 851, 852, -843, 853, 406, 854]]}, {"type": "Polygon", "properties": {"ct": [28.41, 47.2]}, "id": "MDA", "arcs": [[-834, 855]]}, {"type": "Polygon", "properties": {"ct": [24.94, 45.86]}, "id": "ROU", "arcs": [[-833, 412, 216, 856, -411, -410, 857, -850, -835, -856]]}, {"type": "Polygon", "properties": {"ct": [23.88, 55.28]}, "id": "LTU", "arcs": [[-829, -842, -637, 237, 858]]}, {"type": "Polygon", "properties": {"ct": [24.83, 56.81]}, "id": "LVA", "arcs": [[-646, -830, -859, 238, 859]]}, {"type": "Polygon", "properties": {"ct": [25.82, 58.64]}, "id": "EST", "arcs": [[-647, -860, 239]]}, {"type": "Polygon", "properties": {"ct": [10.29, 51.13]}, "id": "DEU", "arcs": [[-841, 860, -847, 861, -692, 862, 863, 864, 232, 865, 234]]}, {"type": "Polygon", "properties": {"ct": [25.2, 42.75]}, "id": "BGR", "arcs": [[410, -857, 217, 866, 867, 868, 869]]}, {"type": "MultiPolygon", "properties": {"ct": [22.56, 39.34]}, "id": "GRC", "arcs": [[[43]], [[-868, 870, 219, 871, 872]]]}, {"type": "MultiPolygon", "properties": {"ct": [35.39, 38.99]}, "id": "TUR", "arcs": [[[-775, -821, 208, 873, -823, -817]], [[-867, 218, -871]]]}, {"type": "Polygon", "properties": {"ct": [20.03, 41.14]}, "id": "ALB", "arcs": [[-872, 220, 874, 875, 876]]}, {"type": "Polygon", "properties": {"ct": [16.57, 45.02]}, "id": "HRV", "arcs": [[-852, 877, 878, 879, 880, 222, 881]]}, {"type": "Polygon", "properties": {"ct": [8.12, 46.79]}, "id": "CHE", "arcs": [[-846, 882, -693, -862]]}, {"type": "Polygon", "properties": {"ct": [5.97, 49.77]}, "id": "LUX", "arcs": [[-863, -697, 883]]}, {"type": "Polygon", "properties": {"ct": [4.58, 50.65]}, "id": "BEL", "arcs": [[-864, -884, -696, 230, 884]]}, {"type": "Polygon", "properties": {"ct": [5.51, 52.3]}, "id": "NLD", "arcs": [[-865, -885, 231]]}, {"type": "Polygon", "properties": {"ct": [-8.06, 39.63]}, "id": "PRT", "arcs": [[885, 227]]}, {"type": "Polygon", "properties": {"ct": [-3.62, 40.35]}, "id": "ESP", "arcs": [[-886, 228, -695, 226]]}, {"type": "Polygon", "properties": {"ct": [-8.01, 53.18]}, "id": "IRL", "arcs": [[1, 886]]}, {"type": "Polygon", "properties": {"ct": [165.53, -21.26]}, "id": "NCL", "arcs": [[54]]}, {"type": "MultiPolygon", "properties": {"ct": [159.1, -7.9]}, "id": "SLB", "arcs": [[[57]], [[58]], [[59]], [[60]], [[61]]]}, {"type": "MultiPolygon", "properties": {"ct": [170.51, -43.99]}, "id": "NZL", "arcs": [[[63]], [[64]]]}, {"type": "MultiPolygon", "properties": {"ct": [134.38, -25.56]}, "id": "AUS", "arcs": [[[65]], [[66]]]}, {"type": "Polygon", "properties": {"ct": [80.67, 7.7]}, "id": "LKA", "arcs": [[67]]}, {"type": "MultiPolygon", "properties": {"ct": [103.87, 36.61]}, "id": "CHN", "arcs": [[[80]], [[-584, -641, -796, -639, -795, 269, 488, 271, -792, -787, -398, -791, -803, -804, -801, -805, -799, -808, -810, -814, -815]]]}, {"type": "Polygon", "properties": {"ct": [120.97, 23.74]}, "id": "TWN", "arcs": [[81]]}, {"type": "MultiPolygon", "properties": {"ct": [12.22, 43.47]}, "id": "ITA", "arcs": [[[-845, 887, 224, -694, -883]], [[192]], [[84]]]}, {"type": "MultiPolygon", "properties": {"ct": [9.31, 56.22]}, "id": "DNK", "arcs": [[[-866, 233]], [[86]]]}, {"type": "MultiPolygon", "properties": {"ct": [-2.66, 53.88]}, "id": "GBR", "arcs": [[[-887, 2]], [[87, 888, 89, 889, 91]]]}, {"type": "Polygon", "properties": {"ct": [-18.76, 65.07]}, "id": "ISL", "arcs": [[92]]}, {"type": "MultiPolygon", "properties": {"ct": [47.68, 40.28]}, "id": "AZE", "arcs": [[[-642, -370, -820, -825, 890]], [[-818, -822]]]}, {"type": "Polygon", "properties": {"ct": [43.48, 42.16]}, "id": "GEO", "arcs": [[-643, -891, -824, -874, 209]]}, {"type": "MultiPolygon", "properties": {"ct": [121.54, 15.75]}, "id": "PHL", "arcs": [[[77]], [[78]], [[79]], [[94]], [[95]], [[96]], [[97]]]}, {"type": "MultiPolygon", "properties": {"ct": [114.68, 3.55]}, "id": "MYS", "arcs": [[[-783, 275]], [[-597, 8, 891, 6]]]}, {"type": "Polygon", "properties": {"ct": [114.92, 4.69]}, "id": "BRN", "arcs": [[-892, 5]]}, {"type": "Polygon", "properties": {"ct": [14.94, 46.13]}, "id": "SVN", "arcs": [[-844, -853, -882, 223, -888]]}, {"type": "Polygon", "properties": {"ct": [26.21, 64.5]}, "id": "FIN", "arcs": [[-648, 241, -826, -653]]}, {"type": "Polygon", "properties": {"ct": [19.51, 48.73]}, "id": "SVK", "arcs": [[-837, -855, -407, -854, -849, 892, -839]]}, {"type": "Polygon", "properties": {"ct": [15.33, 49.78]}, "id": "CZE", "arcs": [[-840, -893, -848, -861]]}, {"type": "Polygon", "properties": {"ct": [38.68, 15.43]}, "id": "ERI", "arcs": [[-628, 299, 893, 894]]}, {"type": "MultiPolygon", "properties": {"ct": [136.88, 36.02]}, "id": "JPN", "arcs": [[[82]], [[83]], [[194]]]}, {"type": "Polygon", "properties": {"ct": [-58.39, -23.25]}, "id": "PRY", "arcs": [[415, -666, -665, -604, -603, -602, -675]]}, {"type": "Polygon", "properties": {"ct": [47.54, 15.91]}, "id": "YEM", "arcs": [[-778, 293, 294, 895]]}, {"type": "Polygon", "properties": {"ct": [44.52, 24.12]}, "id": "SAU", "arcs": [[-767, -777, -772, 286, -771, 288, -770, -779, -896, 295]]}, {"type": "MultiPolygon", "properties": {"ct": [21.28, -80.52]}, "id": "ATA", "arcs": [[[48]], [[49]], [[50]], [[51]], [[52]], [[53]], [[0]], [[337, 443, 896, 897, 340, 341, 898]]]}, {"type": "Polygon", "arcs": [[455, 42, 38, 452, 899]]}, {"type": "Polygon", "properties": {"ct": [33.04, 34.91]}, "id": "CYP", "arcs": [[-900, 453, 40, 454]]}, {"type": "Polygon", "properties": {"ct": [-8.42, 29.89]}, "id": "MAR", "arcs": [[-763, -562, 197, 900, 199]]}, {"type": "Polygon", "properties": {"ct": [29.84, 26.51]}, "id": "EGY", "arcs": [[-627, 901, 204, -756, 297]]}, {"type": "Polygon", "properties": {"ct": [17.97, 27]}, "id": "LBY", "arcs": [[-626, -634, -725, -764, -762, 203, -902]]}, {"type": "Polygon", "properties": {"ct": [39.55, 8.65]}, "id": "ETH", "arcs": [[-619, -623, 902, -629, -895, 903, 904]]}, {"type": "Polygon", "properties": {"ct": [42.5, 11.77]}, "id": "DJI", "arcs": [[-894, 300, 905, -904]]}, {"type": "Polygon", "arcs": [[-620, -905, -906, 301]]}, {"type": "Polygon", "properties": {"ct": [32.36, 1.3]}, "id": "UGA", "arcs": [[-559, -558, 906, -615, 907, -621]]}, {"type": "Polygon", "properties": {"ct": [29.92, -2.01]}, "id": "RWA", "arcs": [[-557, -752, -616, -907]]}, {"type": "Polygon", "properties": {"ct": [17.82, 44.18]}, "id": "BIH", "arcs": [[-880, 908, 909]]}, {"type": "Polygon", "properties": {"ct": [21.7, 41.61]}, "id": "MKD", "arcs": [[-869, -873, -877, 910, 911]]}, {"type": "Polygon", "properties": {"ct": [20.82, 44.23]}, "id": "SRB", "arcs": [[-851, -858, 409, -870, -912, 912, 913, -909, -879, -878]]}, {"type": "Polygon", "properties": {"ct": [19.29, 42.79]}, "id": "MNE", "arcs": [[-875, 221, -881, -910, -914, 914]]}, {"type": "Polygon", "arcs": [[-876, -915, -913, -911]]}, {"type": "Polygon", "properties": {"ct": [-61.33, 10.43]}, "id": "TTO", "arcs": [[353]]}, {"type": "Polygon", "properties": {"ct": [30.2, 7.29]}, "id": "SSD", "arcs": [[-614, -743, -630, -903, -622, -908]]}]}, "subunits": {"type": "GeometryCollection", "geometries": [{"type": "Polygon", "properties": {"ct": [-94.3, 46.32], "gu": "USA"}, "id": "MN", "arcs": [[915, 916, 917, 918, -576, -519]]}, {"type": "Polygon", "properties": {"ct": [-109.66, 47.05], "gu": "USA"}, "id": "MT", "arcs": [[919, 920, 921, 922, 923]]}, {"type": "Polygon", "properties": {"ct": [-100.48, 47.46], "gu": "USA"}, "id": "ND", "arcs": [[-919, 924, -921, 925]]}, {"type": "MultiPolygon", "properties": {"ct": [-155.52, 19.6], "gu": "USA"}, "id": "HI", "arcs": [[[354]], [[355]], [[356]], [[357]], [[358]]]}, {"type": "Polygon", "properties": {"ct": [-114.65, 44.39], "gu": "USA"}, "id": "ID", "arcs": [[-924, 926, 927, 928, 929, 930, -578]]}, {"type": "Polygon", "properties": {"ct": [-120.4, 47.38], "gu": "USA"}, "id": "WA", "arcs": [[-931, 931, 932, 140, 933]]}, {"type": "Polygon", "properties": {"ct": [-111.66, 34.3], "gu": "USA"}, "id": "AZ", "arcs": [[934, 581, 935, 936, 937]]}, {"type": "Polygon", "properties": {"ct": [-119.64, 37.26], "gu": "USA"}, "id": "CA", "arcs": [[-936, 582, 136, 938, 939]]}, {"type": "Polygon", "properties": {"ct": [-105.55, 39], "gu": "USA"}, "id": "CO", "arcs": [[940, 941, 942, 943, 944, 945]]}, {"type": "Polygon", "properties": {"ct": [-116.65, 39.35], "gu": "USA"}, "id": "NV", "arcs": [[-929, 946, -937, -940, 947]]}, {"type": "Polygon", "properties": {"ct": [-106.09, 34.42], "gu": "USA"}, "id": "NM", "arcs": [[-935, -943, 948, 949, 580]]}, {"type": "Polygon", "properties": {"ct": [-120.54, 43.94], "gu": "USA"}, "id": "OR", "arcs": [[-930, -948, -939, 137, 477, 950, -932]]}, {"type": "Polygon", "properties": {"ct": [-111.67, 39.33], "gu": "USA"}, "id": "UT", "arcs": [[-928, 951, -944, -938, -947]]}, {"type": "Polygon", "properties": {"ct": [-107.55, 43.03], "gu": "USA"}, "id": "WY", "arcs": [[-923, 952, 953, -945, -952, -927]]}, {"type": "Polygon", "properties": {"ct": [-92.44, 34.92], "gu": "USA"}, "id": "AR", "arcs": [[954, 438, 439, 955, 956, 957, 958]]}, {"type": "Polygon", "properties": {"ct": [-93.51, 42.08], "gu": "USA"}, "id": "IA", "arcs": [[-917, 959, 960, 961, -431, 962]]}, {"type": "Polygon", "properties": {"ct": [-98.38, 38.48], "gu": "USA"}, "id": "KS", "arcs": [[-941, 963, 432, 964, 965]]}, {"type": "Polygon", "properties": {"ct": [-92.48, 38.38], "gu": "USA"}, "id": "MO", "arcs": [[-959, 966, -965, -433, -432, -962, 967, 434, 435, 436]]}, {"type": "Polygon", "properties": {"ct": [-99.82, 41.53], "gu": "USA"}, "id": "NE", "arcs": [[-946, -954, 968, 429, 430, 431, -964]]}, {"type": "Polygon", "properties": {"ct": [-97.5, 35.58], "gu": "USA"}, "id": "OK", "arcs": [[-942, -966, -967, -958, 969, -949]]}, {"type": "Polygon", "properties": {"ct": [-100.23, 44.45], "gu": "USA"}, "id": "SD", "arcs": [[-918, -963, -430, -969, -953, -922, -925]]}, {"type": "Polygon", "properties": {"ct": [-91.96, 31.05], "gu": "USA"}, "id": "LA", "arcs": [[-956, 440, 970, 971, 173, 174, 175, 972]]}, {"type": "Polygon", "properties": {"ct": [-99.33, 31.46], "gu": "USA"}, "id": "TX", "arcs": [[-950, -970, -957, -973, 176, 579]]}, {"type": "Polygon", "properties": {"ct": [-72.74, 41.61], "gu": "USA"}, "id": "CT", "arcs": [[973, 974, 151, 975, 976]]}, {"type": "Polygon", "properties": {"ct": [-71.74, 42.24], "gu": "USA"}, "id": "MA", "arcs": [[-974, 977, 978, 979, 149, 980]]}, {"type": "Polygon", "properties": {"ct": [-71.56, 43.69], "gu": "USA"}, "id": "NH", "arcs": [[-980, 981, -565, 982, 148]]}, {"type": "Polygon", "properties": {"ct": [-71.53, 41.68], "gu": "USA"}, "id": "RI", "arcs": [[-975, -981, 150]]}, {"type": "Polygon", "properties": {"ct": [-72.66, 44.07], "gu": "USA"}, "id": "VT", "arcs": [[-979, 983, -566, -982]]}, {"type": "Polygon", "properties": {"ct": [-86.83, 32.77], "gu": "USA"}, "id": "AL", "arcs": [[984, 985, 986, 170, 987]]}, {"type": "Polygon", "properties": {"ct": [-82.5, 28.62], "gu": "USA"}, "id": "FL", "arcs": [[-987, 988, 169]]}, {"type": "Polygon", "properties": {"ct": [-83.45, 32.65], "gu": "USA"}, "id": "GA", "arcs": [[-986, 989, 990, 991, 168, -989]]}, {"type": "Polygon", "properties": {"ct": [-89.66, 32.77], "gu": "USA"}, "id": "MS", "arcs": [[-440, 992, -988, 171, 172, -972, -971, -441]]}, {"type": "Polygon", "properties": {"ct": [-80.88, 33.9], "gu": "USA"}, "id": "SC", "arcs": [[-992, 993, 994, 167]]}, {"type": "Polygon", "properties": {"ct": [-89.2, 40.06], "gu": "USA"}, "id": "IL", "arcs": [[-961, 995, -545, 996, 997, -435, -968]]}, {"type": "Polygon", "properties": {"ct": [-86.28, 39.9], "gu": "USA"}, "id": "IN", "arcs": [[-997, -544, 998, 999, 1000]]}, {"type": "Polygon", "properties": {"ct": [-85.28, 37.51], "gu": "USA"}, "id": "KY", "arcs": [[-436, -998, -1001, 1001, 1002, 1003, 1004, 1005]]}, {"type": "Polygon", "properties": {"ct": [-79.25, 35.54], "gu": "USA"}, "id": "NC", "arcs": [[-991, 1006, 1007, -1008, 1008, 165, 1009, -994]]}, {"type": "Polygon", "properties": {"ct": [-82.79, 40.28], "gu": "USA"}, "id": "OH", "arcs": [[-1000, 1010, -513, 1011, 1012, -1002]]}, {"type": "Polygon", "properties": {"ct": [-86.32, 35.84], "gu": "USA"}, "id": "TN", "arcs": [[-439, -955, -437, -1006, -1005, 1004, 1013, -1008, -1007, -990, -985, -993]]}, {"type": "MultiPolygon", "properties": {"ct": [-78.86, 37.5], "gu": "USA"}, "id": "VA", "arcs": [[[-1004, 1014, 1015, 1016, 1017, 163, 164, -1009, 1007, -1014, -1005]], [[160, 1018]]]}, {"type": "Polygon", "properties": {"ct": [-90, 44.65], "gu": "USA"}, "id": "WI", "arcs": [[-916, -518, 1019, 1020, 1021, 1022, -546, -996, -960]]}, {"type": "Polygon", "properties": {"ct": [-80.61, 38.64], "gu": "USA"}, "id": "WV", "arcs": [[-1003, -1013, 1023, 1024, -1015]]}, {"type": "Polygon", "properties": {"ct": [-75.5, 38.99], "gu": "USA"}, "id": "DE", "arcs": [[1025, 1026, 1027, 157, 1028]]}, {"type": "Polygon", "properties": {"ct": [-77.02, 38.9], "gu": "USA"}, "id": "DC", "arcs": [[-1017, 1029]]}, {"type": "Polygon", "properties": {"ct": [-76.77, 39.03], "gu": "USA"}, "id": "MD", "arcs": [[-1019, 161, 162, -1018, -1030, -1016, -1025, 1030, -1026, 1031, 159]]}, {"type": "Polygon", "properties": {"ct": [-74.67, 40.21], "gu": "USA"}, "id": "NJ", "arcs": [[-1028, 1032, 1033, 156]]}, {"type": "Polygon", "properties": {"ct": [-75.5, 42.93], "gu": "USA"}, "id": "NY", "arcs": [[-977, 1034, 153, 154, 155, -1034, 1035, -511, 1036, 1037, -508, 1038, -567, -984, -978]]}, {"type": "Polygon", "properties": {"ct": [-77.81, 40.87], "gu": "USA"}, "id": "PA", "arcs": [[-1012, -512, -1036, -1033, -1027, -1031, -1024]]}, {"type": "Polygon", "properties": {"ct": [-69.22, 45.34], "gu": "USA"}, "id": "ME", "arcs": [[-983, -564, 146, 147]]}, {"type": "MultiPolygon", "properties": {"ct": [-84.61, 43.48], "gu": "USA"}, "id": "MI", "arcs": [[[-574, -573, -541, -548, 1039, -1022, -1021, 1020, -516, 573]], [[-571, -514, -1011, -999, -543, -539]]]}, {"type": "MultiPolygon", "properties": {"ct": [-152.72, 64.44], "gu": "USA"}, "id": "AK", "arcs": [[[33]], [[32]], [[36]], [[-563, 142]]]}]}}, "arcs": [[[452, 634], [17, 20], [52, -9], [28, -17], [21, -20], [7, -25], [-53, -7], [-36, 19], [-17, 20], [-1, 3], [-18, 16]], [[4827, 7992], [5, -40], [-21, -49], [-49, -33], [-40, 8], [23, 58], [-15, 57], [38, 43], [21, 26]], [[4789, 8062], [23, 2], [30, -34], [-15, -38]], [[8916, 4855], [48, -38], [51, -32], [19, -28], [16, -28], [4, -33], [46, -34], [7, -30], [-25, -6], [6, -37], [25, -36], [18, -59], [15, 2], [-1, -25], [22, -9], [-9, -11], [30, -23], [-3, -16], [-18, -4], [-7, 14], [-24, 6], [-28, 9], [-22, 35], [-16, 31], [-14, 48], [-36, 25], [-24, -16], [-17, -19], [4, -41], [-22, -19], [-16, 10], [-28, 2]], [[8917, 4493], [-25, 46], [-28, 11], [-7, -16], [-35, -2], [12, 45], [17, 16], [-7, 60], [-14, 47], [-53, 47], [-23, 5], [-42, 51], [-8, -27], [-11, -5], [-6, 20], [0, 25], [-21, 27], [29, 20], [20, -1], [-2, 14], [-41, 1], [-11, 33], [-25, 10], [-11, 27], [37, 14], [14, 18], [45, -23], [4, -20], [8, -90], [29, -34], [23, 59], [32, 34], [25, 0], [23, -19], [21, -20], [30, -11]], [[8172, 5251], [11, 21], [23, 30]], [[8206, 5302], [22, 39], [14, 43], [11, 0], [14, -28], [1, -24], [19, -15], [23, -17], [-2, -22], [-19, -3], [5, -27], [-20, -19]], [[8274, 5229], [-16, -50], [20, -52], [-5, -26], [32, -51], [-33, -7], [-10, -38], [2, -50], [-27, -38], [-1, -55], [-10, -85], [-5, 19], [-31, -25], [-11, 34], [-20, 3], [-14, 18], [-33, -20], [-10, 27], [-18, -3], [-23, 7], [-4, 74], [-14, 15], [-13, 48], [-4, 49], [3, 51], [16, 37]], [[8045, 5111], [21, -19], [21, 10], [6, 47], [12, 11], [33, 12], [20, 44], [14, 35]], [[2399, 9165], [-15, -22], [-40, 4], [-34, 15], [15, 25], [40, 15], [24, -20], [10, -17]], [[2393, 9306], [-13, -2], [-52, 4], [-7, 15], [56, -1], [19, -10], [-3, -6]], [[2312, 9375], [33, -19], [-7, -20], [-41, -12], [-23, 13], [-12, 21], [-2, 23], [36, -2], [16, -4]], [[2551, 9132], [-45, 7], [-74, 18], [-9, 30], [-4, 28], [-27, 24], [-58, 7], [-32, 17], [10, 23], [58, -4], [30, -18], [55, 1], [24, -19], [-6, -21], [32, -12], [17, -13], [38, -3], [40, -4], [44, 12], [57, 4], [45, -4], [30, -21], [6, -23], [-17, -14], [-42, -12], [-35, 7], [-80, -9], [-57, -1]], [[1909, 9341], [39, -9], [-9, -16], [-52, -16], [-41, 18], [23, 17], [40, 6]], [[1917, 9377], [37, -11], [-34, -11], [-46, 0], [0, 8], [29, 17], [14, -3]], [[3159, 6028], [14, -5], [5, -11], [-7, -14], [-21, 0], [-17, -2], [-1, 24], [4, 8], [23, 0]], [[2845, 6027], [19, -5], [14, -14], [5, -15], [-19, -1], [-9, -9], [-15, 9], [-16, 20], [3, 13], [12, 4], [6, -2]], [[2715, 6288], [23, -4], [22, -1], [26, -19], [11, -20], [26, 6], [10, -13], [24, -34], [17, -25], [9, 0], [17, -11], [-2, -16], [20, -2], [21, -23], [-3, -13], [-19, -7], [-18, -3], [-19, 5], [-40, -6], [18, 31], [-11, 15], [-18, 4], [-9, 16], [-7, 31], [-16, -2], [-26, 15], [-8, 12], [-36, 8], [-10, 11], [11, 14], [-28, 3], [-20, -29], [-11, -1], [-4, -13], [-14, -6], [-12, 5], [15, 17], [6, 20], [13, 12], [14, 11], [21, 6], [7, 6]], [[3455, 7850], [-15, -35], [-18, -48], [18, 18], [19, -12], [-10, -19], [25, -15], [12, 13], [28, -17], [-8, -40], [19, 9], [4, -29], [8, -35], [-11, -49], [-13, -2], [-18, 11], [6, 45], [-8, 7], [-32, -48], [-17, 2], [20, 26], [-27, 13], [-30, -3], [-54, 2], [-4, 16], [17, 20], [-12, 15], [24, 33], [28, 89], [18, 32], [24, 19], [13, -3], [-6, -15]], [[2670, 8616], [30, -19], [32, -17], [2, -26], [21, 4], [20, -19], [-25, -17], [-43, 13], [-16, 25], [-27, -29], [-40, -29], [-9, 33], [-38, -6], [24, 28], [4, 43], [9, 51], [20, -4], [5, -25], [15, 9], [16, -15]], [[2812, 9019], [26, 22], [62, -28], [38, -27], [3, -24], [52, 12], [29, -35], [67, -22], [24, -22], [26, -52], [-51, -26], [66, -36], [44, -13], [40, -51], [44, -3], [-9, -39], [-49, -65], [-34, 24], [-44, 53], [-36, -7], [-3, -31], [29, -33], [38, -25], [11, -15], [18, -55], [-9, -40], [-35, 15], [-70, 45], [39, -48], [29, -34], [5, -19], [-76, 22], [-59, 32], [-34, 27], [10, 16], [-42, 28], [-40, 27], [0, -16], [-80, -9], [-23, 20], [18, 40], [52, 1], [57, 8], [-9, 19], [10, 28], [36, 54], [-8, 25], [-11, 19], [-42, 27], [-57, 19], [18, 14], [-29, 34], [-25, 3], [-22, 19], [-14, -16], [-51, -7], [-101, 12], [-59, 16], [-45, 9], [-23, 19], [29, 26], [-39, 0], [-9, 56], [21, 50], [29, 23], [72, 14], [-21, -36], [22, -34], [26, 45], [70, 22], [48, -57], [-4, -36], [55, 16]], [[2375, 9118], [58, -2], [53, -14], [-42, -49], [-33, -11], [-30, -42], [-32, 3], [-17, 48], [1, 28], [14, 24], [28, 15]], [[2210, 9038], [-31, 37], [33, 27], [33, -12], [50, 7], [7, -16], [-26, -27], [42, -24], [-5, -50], [-45, -21], [-27, 4], [-19, 22]], [[2222, 8985], [-69, 41], [0, 19]], [[2153, 9045], [57, -7]], [[2005, 9213], [25, 9], [29, -2], [5, -28], [-17, -26], [-94, -9], [-70, -24], [-43, -1], [-3, 18], [57, 25], [-125, -7], [-39, 10], [38, 54], [26, 16], [78, -19], [50, -33], [48, -4], [-40, 53], [26, 20], [29, -6], [9, -26], [11, -20]], [[1587, 9228], [47, 41], [57, 36], [43, 0], [38, 8], [-4, -43], [-21, -19], [-26, -3], [-52, -24], [-44, -8], [-38, 12]], [[1624, 9135], [39, -11], [71, -3], [27, -17], [30, -23], [-35, -14], [-68, -39], [-34, -39]], [[1654, 8989], [0, -24], [-73, -27]], [[1581, 8938], [-15, 24], [-64, 30]], [[1502, 8992], [12, 23], [19, 41]], [[1533, 9056], [24, 36], [-27, 34], [94, 9]], [[376, 8354], [22, -5], [3, -22], [-18, -8], [-18, 10], [-17, 15], [28, 10]], [[744, 8220], [18, -4], [12, -17], [-24, -27], [-28, -21], [-14, 14], [-4, 26], [25, 20], [15, 9]], [[1313, 8001], [27, 5], [-8, -63], [24, -45], [-11, 0], [-17, 26], [-10, 25], [-14, 18], [-5, 24], [1, 18], [13, -8]], [[1516, 7774], [14, -26], [28, -23], [11, -31], [-14, -7], [-46, 25], [-8, 19], [-25, 20], [-5, 16], [-28, 10], [-11, 30], [2, 13], [30, -12], [17, -9], [26, -6], [9, -19]], [[230, 8543], [17, -11], [17, 6], [23, -15], [27, -7], [-2, -6], [-21, -12], [-21, 12], [-11, 10], [-24, -3], [-7, 5], [2, 21]], [[2069, 9405], [55, -8], [75, -20], [21, -27], [11, -23], [-45, 6], [-46, 18], [-62, 3], [27, 16], [-34, 13], [-2, 22]], [[5915, 6965], [20, -1], [25, 17], [-19, -24]], [[5941, 6957], [3, -14]], [[5944, 6943], [-28, -23], [-14, 7], [-7, 22]], [[5895, 6949], [16, 3]], [[5911, 6952], [4, 13]], [[5730, 6960], [-4, -16], [-40, -5], [1, 9], [-34, 11], [5, 24], [15, -19], [22, 3], [20, -4], [0, -9], [15, 6]], [[6376, 4307], [7, -24], [7, -37], [4, -66], [7, -26], [-2, -27], [-5, -16], [-10, 32], [-5, -16], [5, -41], [-2, -24], [-8, -13], [-1, -47], [-11, -65], [-14, -76], [-17, -105], [-11, -78], [-12, -64], [-23, -13], [-24, -24], [-16, 15], [-22, 19], [-8, 30], [-2, 49], [-10, 44], [-2, 40], [5, 40], [13, 10], [0, 18], [13, 42], [2, 36], [-6, 26], [-5, 35], [-2, 51], [9, 31], [4, 36], [14, 2], [15, 11], [11, 10], [12, 1], [16, 32], [23, 34], [8, 28], [-4, 23], [12, -6], [15, 38], [1, 34], [9, 25], [10, -24]], [[9644, 4117], [17, -32], [-9, -8], [-9, 25], [1, 15]], [[9632, 4129], [-4, 15], [0, 43], [13, -17], [4, -45], [-7, 7], [-6, -3]], [[8727, 4616], [-3, 42], [5, 20], [6, 19], [7, -16], [-1, -27], [-14, -38]], [[3648, 664], [14, 0], [41, 12], [42, -12], [35, -24], [12, -34], [3, -24], [1, -28], [-43, -17], [-45, -15], [-52, -13], [-59, -10], [-65, 3], [-37, 18], [5, 23], [59, 15], [24, 19], [18, 24], [12, 21], [17, 19], [18, 23]], [[3158, 541], [63, -2], [60, -5], [20, 23], [15, 19], [29, -23], [-8, -28], [-8, -25], [-59, 8], [-62, -4], [-34, 19], [0, 2], [-16, 16]], [[2946, 1040], [20, 7], [32, -2], [8, 28], [1, 21], [0, 44], [16, 27], [25, 8], [15, -20], [6, -21], [12, -25], [10, -24], [7, -25], [4, -25], [-5, -22], [-8, -21], [-33, -7], [-31, -11], [-36, 1], [14, 22], [-33, -8], [-31, -7], [-21, 16], [-2, 23], [30, 21]], [[2157, 1006], [18, 10], [35, -8], [40, -4], [31, -8], [30, 7], [17, -32], [-22, 4], [-34, -2], [-34, 2], [-38, -3], [-28, 11], [-15, 23]], [[1594, 908], [6, 18], [33, -9], [36, -9], [33, 10], [-16, -20], [-26, -14], [-39, 4], [-27, 20]], [[1464, 919], [20, 12], [28, -13], [43, -22], [-17, 2], [-36, 5], [-38, 16]], [[9604, 3829], [23, -35], [14, -25], [-10, -14], [-16, 15], [-19, 25], [-18, 30], [-19, 39], [-4, 19], [12, -1], [16, -19], [12, -19], [9, -15]], [[9239, 4796], [11, -18], [3, -28], [-9, -15], [-5, 33], [-6, 21], [-13, 18], [-16, 24], [-20, 16], [8, 14], [15, -16], [9, -12], [12, -13], [11, -24]], [[9202, 4675], [-15, -13], [-15, -13], [-14, 0], [-23, 16], [-16, 15], [2, 17], [25, -8], [15, 5], [5, 26], [4, 2], [2, -30], [16, 4], [8, 19], [16, 20], [-4, 33], [17, 1], [6, -9], [-1, -31], [-9, -34], [-15, -4], [-4, -16]], [[9502, 4417], [8, -19], [-19, 0], [-11, 35], [17, -14], [5, -2]], [[9490, 4466], [-4, -10], [-21, 48], [-5, 33], [9, 0], [10, -44], [11, -27]], [[9467, 4451], [-11, -1], [-17, 5], [-5, 9], [1, 22], [19, -9], [9, -11], [4, -15]], [[9434, 4554], [6, -18], [1, -11], [-22, 24], [-15, 20], [-10, 18], [4, 6], [13, -13], [23, -26]], [[9364, 4609], [11, -18], [-5, -3], [-13, 13], [-11, 23], [1, 9], [17, -24]], [[9298, 4703], [8, -13], [14, -35], [13, -19], [-4, -15], [-8, -6], [-12, 21], [-12, 36], [-6, 42], [4, 5], [3, -16]], [[9913, 2774], [-11, -30], [-14, -38], [-21, -22], [-5, 14], [-12, 8], [16, 46], [-9, 31], [-30, 22], [1, 20], [20, 19], [5, 43], [-1, 36], [-12, 37], [1, 10], [-13, 23], [-22, 49], [-12, 39], [11, 5], [15, -31], [21, -14], [8, -50], [20, -58], [1, 37], [13, -15], [4, -42], [22, -18], [19, -4], [16, 21], [14, -6], [-7, -50], [-8, -32], [-22, 1], [-7, -17], [3, -24], [-4, -10]], [[9712, 2580], [24, 29], [16, 29], [13, 41], [10, 14], [5, 31], [19, 26], [6, -24], [6, -22], [20, 22], [8, -23], [0, -24], [-10, -26], [-18, -40], [-14, -23], [10, -27], [-22, 0], [-23, -21], [-8, -37], [-16, -56], [-21, -25], [-14, -15], [-26, 1], [-18, 18], [-30, 4], [-5, 20], [15, 42], [35, 54], [18, 11], [20, 21]], [[9102, 2733], [16, -4], [2, -66], [-9, -19], [-3, -45], [-10, 15], [-19, -38], [-6, 3], [-17, 1], [-17, 48], [-4, 37], [-16, 48], [1, 25], [18, -5], [27, -19], [15, 8], [22, 11]], [[8503, 3210], [-29, -29], [-24, -12], [-6, -30], [-10, -22], [-23, -1], [-18, -5], [-24, 10], [-20, -6], [-19, -3], [-17, -29], [-8, 2], [-14, -16], [-13, -17], [-21, 2], [-18, 0], [-30, 35], [-15, 11], [1, 32], [14, 7], [4, 13], [-1, 20], [4, 39], [-3, 32], [-15, 57], [-4, 31], [1, 32], [-11, 36], [-1, 16], [-12, 23], [-4, 43], [-16, 44], [-4, 24], [13, -24], [-10, 51], [14, -16], [8, -21], [0, 28], [-14, 44], [-3, 17], [-6, 17], [3, 32], [6, 14], [4, 28], [-3, 32], [11, 40], [2, -42], [12, 38], [22, 18], [14, 24], [21, 21], [13, 4], [7, -7], [22, 21], [17, 6], [4, 12], [8, 5], [15, -1], [29, 16], [15, 25], [7, 29], [17, 29], [1, 22], [1, 30], [19, 47], [12, -48], [12, 11], [-10, 27], [9, 27], [12, -13], [3, 43], [15, 27], [7, 22], [14, 9], [0, 16], [13, -6], [0, 13], [12, 8], [14, 8], [20, -26], [16, -32], [17, -1], [18, -5], [-6, 30], [13, 45], [13, 14], [-5, 14], [12, 32], [17, 20], [14, -7], [24, 11], [-1, 28], [-20, 18], [15, 8], [18, -13], [15, -23], [23, -14], [8, 5], [17, -17], [17, 16], [10, -5], [7, 11], [12, -28], [-7, -29], [-11, -23], [-9, -2], [3, -22], [-8, -28], [-10, -27], [2, -16], [22, -30], [21, -18], [15, -19], [20, -33], [8, 0], [14, -14], [4, -17], [27, -19], [18, 19], [6, 29], [5, 25], [4, 31], [8, 44], [-4, 27], [2, 16], [-3, 32], [4, 41], [5, 12], [-4, 18], [7, 30], [5, 30], [1, 16], [10, 21], [8, -27], [2, -35], [7, -7], [1, -23], [10, -28], [2, -32], [-1, -20], [10, -44], [18, 21], [9, -23], [13, -22], [-3, -25], [6, -47], [5, -28], [7, -7], [7, -47], [-3, -29], [9, -38], [31, -29], [19, -26], [19, -24], [-4, -14], [16, -35], [11, -60], [11, 13], [11, -24], [7, 8], [5, -59], [19, -34], [13, -21], [22, -45], [8, -45], [1, -31], [-2, -35], [13, -47], [-2, -49], [-5, -26], [-7, -49], [1, -32], [-6, -40], [-12, -51], [-21, -27], [-10, -43], [-9, -27], [-8, -48], [-11, -28], [-7, -42], [-4, -38], [2, -17], [-16, -20], [-31, -2], [-26, -23], [-13, -21], [-17, -24], [-23, 25], [-17, 9], [5, 29], [-15, -10], [-25, -40], [-24, 15], [-15, 8], [-16, 4], [-27, 17], [-18, 34], [-5, 42], [-7, 28], [-13, 23], [-27, 6], [9, 27], [-7, 41], [-13, -38], [-25, -10], [14, 31], [5, 32], [10, 27], [-2, 41], [-22, -47], [-18, -19], [-10, -45], [-22, 23], [1, 30], [-18, 40], [-14, 21], [5, 13], [-36, 33], [-19, 2], [-27, 27], [-50, -5], [-36, -20], [-31, -19], [-27, 4]], [[7271, 5417], [-4, -57], [-12, -16], [-24, -13], [-13, 44], [-5, 80], [13, 90], [19, -31], [13, -39], [13, -58]], [[8593, 4844], [30, -16], [10, -43], [-23, 23], [-23, 5], [-16, -4], [-19, 2], [6, 31], [35, 2]], [[8523, 4789], [-19, 10], [-5, 24], [28, 3], [7, -19], [-11, -18]], [[8553, 5120], [2, -30], [16, -5], [3, -23], [-2, -48], [-14, 5], [-4, -34], [11, -29], [-8, -6], [-11, 35], [-8, 71], [6, 44], [9, 20]], [[8414, 5048], [32, 2], [27, 41], [5, -13], [-22, -55], [-21, -10], [-27, 10], [-46, -2], [-24, -8], [-4, -42], [24, -50], [15, 25], [52, 19], [-2, -25], [-12, 8], [-12, -33], [-25, -21], [27, -71], [-5, -20], [25, -64], [-1, -36], [-14, -17], [-11, 20], [13, 46], [-27, -22], [-7, 15], [3, 22], [-20, 32], [3, 55], [-19, -17], [2, -65], [1, -80], [-17, -8], [-12, 17], [8, 51], [-4, 53], [-12, 1], [-9, 38], [12, 36], [4, 44], [14, 84], [5, 23], [24, 41], [22, -16], [35, -8]], [[8341, 4430], [-37, 39], [26, 10], [14, -16], [10, -17], [-2, -15], [-11, -1]], [[8370, 4525], [18, 5], [25, 20], [-4, -31], [-42, -16], [-37, 7], [0, 20], [22, 12], [18, -17]], [[8284, 4535], [17, 5], [7, -24], [-32, -11], [-19, -8], [-15, 1], [10, 32], [15, 0], [7, 20], [10, -15]], [[8013, 4643], [4, -20], [53, -6], [6, 23], [51, -26], [10, -36], [42, -10], [34, -33], [-31, -22], [-31, 23], [-25, -2], [-29, 4], [-26, 10], [-32, 22], [-21, 5], [-11, -7], [-51, 23], [-5, 24], [-25, 4], [19, 53], [34, -3], [22, -22], [12, -4]], [[7898, 4939], [5, -39], [10, -31], [20, -4], [14, -36], [-7, -69], [-1, -86], [-31, -1], [-24, 47], [-35, 45], [-12, 34], [-21, 45], [-14, 42], [-21, 77], [-24, 47], [-9, 47], [-10, 44], [-25, 35], [-14, 47], [-21, 31], [-29, 62], [-3, 28], [18, -2], [43, -11], [25, -54], [21, -38], [16, -23], [26, -60], [28, -1], [23, -38], [16, -46], [22, -26], [-12, -45], [16, -19], [10, -2]], [[8356, 5705], [-15, 43], [24, -2], [10, -20], [-7, -48], [-12, 27]], [[8404, 5554], [7, 16], [3, 34], [16, 3], [-5, -37], [21, 53], [-3, -53], [-10, -18], [-9, -35], [-8, -16], [-17, 38], [5, 15]], [[8510, 5467], [2, -37], [2, -31], [-9, -51], [-11, 57], [-13, -29], [9, -40], [-8, -26], [-32, 32], [-8, 40], [8, 26], [-17, 27], [-9, -23], [-13, 2], [-21, -31], [-4, 16], [11, 47], [17, 15], [15, 21], [10, -25], [21, 15], [5, 25], [19, 2], [-1, 43], [22, -27], [3, -28], [2, -20]], [[8040, 6010], [-23, 18], [0, 47], [13, 26], [31, 15], [16, -1], [6, -21], [-12, -25], [-7, -32], [-24, -27]], [[8382, 6355], [-17, -89], [-12, -46], [-14, 47], [-4, 41], [17, 55], [22, 42], [13, -17], [-5, -33]], [[8940, 7176], [-25, -56], [0, -57], [-10, -45], [4, -27], [-14, -40], [-35, -26], [-49, -3], [-40, -64], [-19, 22], [-1, 41], [-48, -12], [-33, -26], [-32, -1], [28, -41], [-19, -94], [-18, -24], [-13, 22], [7, 50], [-18, 16], [-11, 38], [26, 17], [15, 35], [28, 29], [20, 38], [55, 16], [30, -11], [29, 99], [19, -27], [40, 56], [16, 21], [18, 68], [-5, 63], [11, 35], [30, 10], [15, -77], [-1, -45]], [[9016, 7442], [20, 23], [6, -62], [-41, -15], [-25, -56], [-43, 38], [-15, -60], [-31, -1], [-4, 55], [14, 43], [29, 3], [8, 77], [9, 43], [32, -58], [22, -19], [19, -11]], [[5241, 7271], [14, 18], [17, -40], [-4, -73], [-13, 3], [-11, -18], [-10, 14], [-2, 68], [-6, 31], [15, -3]], [[5242, 7367], [18, 22], [5, -48], [-9, -43], [-13, 11], [-6, 38], [5, 20]], [[5343, 8116], [9, -27], [-17, -45], [-29, 31], [-4, 23], [41, 18]], [[4883, 8252], [33, 5], [-30, -60], [29, 7], [30, 0], [-7, -45], [-25, -50], [29, -4]], [[4942, 8105], [27, -71]], [[4969, 8034], [19, -9], [17, -63], [8, -22], [33, -11], [-3, -35], [-14, -17], [11, -28], [-25, -29], [-37, 0], [-48, -15], [-13, 11], [-18, -26], [-26, 6], [-19, -21], [-15, 11], [41, 58], [25, 12]], [[4905, 7856], [-44, 10]], [[4861, 7866], [-8, 22], [29, 17], [-15, 30], [5, 36], [42, -5], [4, 32], [-19, 35], [-34, 10], [-7, 15], [10, 25], [-9, 15], [-15, -26], [-1, 54], [-14, 28], [10, 57], [21, 45], [23, -4]], [[4597, 8691], [-7, -36], [31, -38], [-36, -42], [-80, -38], [-24, -10], [-36, 8], [-78, 17], [28, 25], [-61, 27], [49, 11], [-1, 16], [-58, 13], [19, 36], [42, 9], [43, -38], [42, 30], [35, -16], [45, 30], [47, -4]], [[8969, 7983], [10, -54], [-1, -54], [11, -56], [28, -99], [-41, 18], [-17, -80], [27, -57], [-1, -39], [-21, 34], [-18, -43], [-5, 47], [3, 54], [-3, 60], [6, 42], [2, 74], [-17, 55], [3, 75], [25, 26], [-11, 26], [13, 8], [7, -37]], [[8291, 5517], [-37, -53], [14, 39], [20, 34], [16, 39], [15, 55], [5, -45], [-18, -31], [-15, -38]], [[8397, 6012], [-4, -23], [9, -40], [-7, -46], [-16, -19], [-5, -44], [7, -45], [14, -6], [13, 7], [34, -31], [-2, -30], [9, -13], [-3, -26], [-22, 27], [-10, 29], [-7, -20], [-18, 33], [-25, -8], [-14, 12], [1, 23], [9, 14], [-8, 13], [-4, -20], [-14, 32], [-4, 24], [-1, 54], [11, -19], [3, 87], [9, 50], [17, 0], [17, -15], [9, 14], [2, -14]], [[8389, 5634], [-4, 26], [16, -17], [18, 0], [0, -23], [-13, -24], [-18, -17], [-1, 26], [2, 29]], [[8485, 5675], [8, -62], [-21, 15], [0, -19], [7, -34], [-13, -13], [-1, 40], [-9, 2], [-4, 34], [16, -4], [0, 21], [-17, 42], [27, -1], [7, -21]], [[2851, 5481], [14, -2], [21, 39], [12, 6], [0, 18], [5, 47], [16, 26], [17, 1], [3, 12], [21, -5], [22, 28], [11, 13], [14, 26], [9, -3], [8, -15], [-6, -18]], [[3018, 5654], [-1, -13], [-16, -7], [9, -25], [0, -29], [-12, -32], [10, -45], [12, 4], [6, 40], [-8, 20], [-2, 42], [35, 22], [-4, 27], [10, 17], [10, -39], [19, -1], [18, -31], [1, -18], [25, -1], [30, 6], [16, -25], [21, -7], [16, 18], [0, 14], [34, 3], [34, 1], [-24, -17], [10, -26], [22, -4], [21, -27], [4, -45], [15, 1], [11, -13]], [[3340, 5464], [18, -20], [17, -36], [1, -29], [10, -1], [15, -27], [11, -20]], [[3412, 5331], [34, -11], [2, 10], [23, 4], [30, -15]], [[3501, 5319], [9, -6], [21, -13], [29, -47], [5, -23]], [[3565, 5230], [10, -2], [6, -26]], [[3581, 5202], [16, -97], [14, -9], [1, -38], [-21, -46], [9, -17], [49, -9], [1, -55], [21, 36], [35, -20], [46, -34], [14, -32], [-5, -31], [33, 17], [54, -29], [41, 2], [41, -46], [36, -62], [21, -16], [24, -3], [10, -17], [9, -71], [5, -33], [-11, -92], [-14, -37], [-39, -77], [-18, -63], [-21, -48], [-7, -1], [-7, -41], [2, -104], [-8, -85], [-3, -37], [-9, -22], [-5, -74], [-28, -73], [-5, -57], [-22, -24], [-7, -33], [-30, 0], [-44, -22], [-19, -24], [-31, -17], [-33, -44], [-23, -55], [-5, -41], [5, -31], [-5, -56], [-6, -27], [-20, -31], [-31, -98], [-24, -44], [-19, -26], [-13, -53], [-18, -31]], [[3517, 3124], [-12, -35], [-31, -31], [-21, 11], [-15, -6], [-26, 24], [-18, -2], [-17, 31]], [[3377, 3116], [-2, -29], [35, -48], [-4, -38], [18, -24], [-2, -27], [-26, -72], [-42, -29], [-55, -12], [-31, 6], [6, -34], [-6, -41], [5, -28], [-16, -20], [-29, -7], [-26, 20], [-11, -15], [4, -55], [18, -17], [16, 18], [8, -29], [-26, -17], [-22, -35], [-4, -56], [-7, -29], [-26, 0], [-22, -29], [-8, -42], [28, -40], [26, -11], [-9, -50], [-33, -32], [-18, -65], [-25, -22], [-12, -26], [9, -57], [19, -33], [-12, 3]], [[3095, 2094], [-25, 1], [-13, -14], [-25, -20], [-5, -52], [-11, -1], [-32, 18], [-32, 39], [-34, 31]], [[2836, 5401], [12, -30], [4, -47], [-6, -14], [6, -51], [-5, -32], [10, -14], [-10, -29], [-12, -35], [-14, -3], [-6, -21], [1, -27], [-10, -5], [3, -17]], [[2809, 5076], [-19, -22], [-15, -12], [2, -22], [-11, -36], [-5, -35], [-9, -8], [4, -51], [-5, -15], [16, -25], [11, 26], [6, -24], [-15, -42]], [[2769, 4810], [-22, -35], [-9, -39], [14, -53], [-9, -24], [20, -23], [21, -36], [9, -41], [11, -25], [26, -111], [28, -102], [23, -73], [-4, -16], [11, -46], [22, -35], [50, -60], [55, -56], [2, -23], [28, -32]], [[3045, 3980], [6, -78], [2, -91], [-9, -124], [-9, -115], [-5, -108], [-16, -68], [3, -68], [-8, -46], [6, -83], [-11, -83], [-20, -89], [-17, -90], [-11, -2], [2, -62], [8, -54], [-13, -38], [-9, -103], [-9, -80], [17, -7], [9, 69], [19, -15], [-15, -115], [-31, 20], [-9, -93], [-27, -49], [43, -16], [-30, -43], [-12, -53], [4, -95], [14, -37], [-8, -33], [9, -35]], [[2926, 2064], [23, -11], [39, -37], [36, -20], [15, 25], [9, 38], [25, 23], [20, -6]], [[3093, 2076], [11, -26], [14, -42], [36, -33], [39, -14], [-13, -28], [-26, -3], [-14, 20]], [[3140, 1950], [-10, -22], [-23, -18]], [[3107, 1910], [-14, 2], [-16, 5]], [[3077, 1917], [-21, 16], [-29, 8], [-35, 31], [-28, 30], [-38, 62]], [[6245, 9476], [54, 10], [43, 1], [5, -15], [16, 13], [26, 10], [42, -13], [-11, -8], [-37, -7], [-25, -5], [-4, -9], [-33, -9], [-30, 13], [16, 18], [-62, 1]], [[6486, 9096], [66, 49], [-7, 25], [62, 30], [91, 35], [93, 11], [48, 20], [54, 8], [19, -22], [-19, -18], [-98, -27], [-85, -27], [-86, -53], [-42, -54], [-43, -53], [5, -46], [54, -46], [-17, -5], [-91, 7], [-7, 25], [-50, 15], [-4, 30], [28, 12], [-1, 30], [55, 48], [-25, 6]], [[5761, 9447], [-41, -30], [-81, -7], [-82, 9], [-5, 16], [-40, 1], [-30, 25], [86, 16], [40, -14], [28, 17], [70, -14], [55, -19]], [[5686, 9324], [-62, -22], [-49, 12], [19, 15], [-16, 18], [57, 11], [11, -21], [40, -13]], [[5420, 9425], [11, 19], [40, 2], [35, -19], [92, -41], [-70, -22], [-15, -41], [-25, -11], [-13, -46], [-34, -2], [-59, 34], [25, 20], [-42, 16], [-54, 47], [-21, 43], [75, 20], [16, -19], [39, 0]], [[2836, 5401], [-9, 16], [-6, 30], [7, 15], [-7, 3], [-5, 19], [-14, 15], [-12, -3], [-6, -20], [-11, -14], [-6, -2], [-3, -11], [13, -30], [-7, -7], [-4, -9], [-13, -2], [-5, 33], [-4, -10], [-9, 4], [-5, 22], [-12, 4], [-7, 6]], [[2711, 5460], [-6, 0], [-6, 0]], [[2699, 5460], [-1, -12], [-3, 8]], [[2695, 5456], [-15, 13], [-6, 11], [4, 10], [-1, 12], [-8, 14], [-11, 11], [-10, 7], [-1, 16], [-8, 10], [2, -16], [-5, -14], [-7, 16], [-9, 5], [-4, 11], [1, 17], [3, 18]], [[2620, 5597], [-4, 4], [-4, 4]], [[2612, 5605], [7, 10]], [[2619, 5615], [-10, 18], [-13, 22], [-6, 19], [-12, 18], [-13, 25], [3, 8], [4, -8], [2, 4]], [[2574, 5721], [-5, 17]], [[2569, 5738], [-4, 3], [-4, 2]], [[2561, 5743], [-3, -13], [-16, 1], [-10, 5], [-12, 11], [-15, 4]], [[2505, 5751], [-8, 11]], [[2497, 5762], [-14, 10]], [[2483, 5772], [-17, 1], [-13, 11], [-15, 23]], [[2438, 5807], [-32, 60], [-14, 18], [-23, 14], [-15, -4], [-22, -20], [-14, -6], [-20, 15], [-21, 10], [-26, 26], [-21, 8], [-31, 25], [-23, 27], [-7, 15], [-16, 3], [-28, 18], [-12, 25], [-30, 32], [-14, 35], [-6, 27], [9, 5], [-3, 16], [7, 14], [0, 19], [-10, 25], [-2, 23], [-9, 28], [-25, 55], [-28, 43], [-13, 35], [-24, 23], [-5, 13], [4, 34], [-14, 13], [-17, 27], [-7, 39], [-14, 5], [-17, 29], [-13, 27], [-1, 17], [-15, 42], [-10, 43], [1, 21], [-20, 22], [-10, -2], [-15, 15], [-5, -23], [5, -26], [2, -42], [10, -23], [21, -38], [4, -13], [4, -4], [4, -19], [5, 1], [6, -36], [8, -14], [6, -20], [17, -28], [10, -52], [8, -24], [8, -26], [1, -30], [13, -2], [12, -25], [10, -25], [-1, -10], [-12, -20], [-5, 0], [-7, 34], [-18, 32], [-20, 27], [-14, 14], [1, 40], [-5, 30], [-13, 18], [-19, 24], [-4, -7], [-7, 15], [-17, 13], [-16, 32], [2, 5], [11, -3], [11, 20], [1, 25], [-22, 40], [-16, 15], [-10, 35], [-11, 36], [-12, 45], [-12, 50]], [[1746, 6807], [-4, 28], [-18, 32], [-13, 7], [-3, 16], [-16, 3], [-10, 15], [-26, 5], [-7, 9], [-3, 30], [-27, 56], [-23, 78], [1, 12], [-13, 19], [-21, 46], [-4, 46], [-15, 30], [6, 46], [-1, 48]], [[1549, 7333], [-8, 42], [10, 53]], [[1551, 7428], [4, 50], [3, 50]], [[1558, 7528], [-5, 75]], [[1553, 7603], [-9, 47], [-8, 26], [4, 11], [40, -19], [15, -52], [7, 14], [-5, 46], [-9, 45]], [[1588, 7721], [-4, 1], [-54, 54], [-20, 24], [-50, 23], [-15, 49], [3, 34], [-35, 24], [-5, 45], [-34, 40], [0, 29]], [[1374, 8044], [-15, 21], [-25, 17], [-8, 49], [-36, 45], [-15, 52], [-26, 4], [-44, 1], [-33, 16], [-57, 58], [-27, 10], [-49, 20], [-38, -5], [-55, 26], [-33, 24], [-30, -12], [5, -39], [-15, -3], [-32, -12], [-25, -19], [-30, -11], [-4, 32], [12, 55], [30, 17], [-8, 14], [-35, -31], [-19, -37], [-40, -40], [20, -27], [-26, -39], [-30, -24], [-28, -17], [-7, -24], [-43, -29], [-9, -26], [-32, -24], [-20, 5], [-25, -16], [-29, -19], [-23, -18], [-47, -16], [-5, 9], [31, 26], [27, 17], [29, 30], [35, 7], [14, 23], [38, 33], [6, 11], [21, 20], [5, 42], [14, 32], [-32, -16], [-9, 9], [-15, -20], [-18, 28], [-8, -20], [-10, 28], [-28, -22], [-17, 0], [-3, 33], [5, 20], [-17, 20], [-37, -11], [-23, 26], [-19, 14], [0, 31], [-22, 24], [11, 32], [23, 31], [10, 28], [22, 4], [19, -9], [23, 27], [20, -5], [21, 18], [-5, 25], [-16, 10], [21, 22], [-17, -1], [-30, -12], [-8, -12], [-22, 12], [-39, -6], [-41, 13], [-12, 22], [-35, 32], [39, 24], [62, 27], [23, 0], [-4, -28], [59, 2], [-23, 35], [-34, 21], [-20, 28], [-26, 23], [-38, 18], [15, 29], [49, 2], [35, 25], [7, 27], [28, 27], [28, 6], [52, 25], [26, -4], [42, 29], [42, -11], [21, -25], [12, 10], [47, -3], [-2, -13], [43, -9], [28, 5], [59, -17], [53, -5], [21, -8], [37, 9], [42, -16], [31, -8]], [[1084, 8872], [51, -13], [44, -27], [29, -5], [24, 23], [34, 17], [41, -6], [42, 24], [45, 14], [20, -23], [20, 13], [6, 26], [20, -6], [47, -50], [37, 38], [3, -42], [34, 9], [11, 16], [34, -3], [42, -24], [65, -20], [38, -9], [28, 3], [37, -28], [-39, -28], [50, -11], [75, 6], [24, 10], [29, -33], [31, 28], [-29, 23], [18, 19], [34, 3], [22, 5], [23, -13], [28, -30], [31, 4], [49, -25], [43, 9], [40, -1], [-3, 34], [25, 10], [43, -19], [0, -52], [17, 44], [23, -2], [12, 56], [-30, 35], [-32, 22], [2, 61], [33, 41], [37, -9], [28, -25], [38, -62], [-25, -28], [52, -11], [-1, -57], [38, 44], [33, -36], [-9, -41], [27, -38], [29, 41], [21, 47], [1, 61], [40, -4], [41, -8], [37, -28], [2, -27], [-21, -30], [20, -29], [-4, -27], [-54, -39], [-39, -9], [-29, 17], [-8, -28], [-27, -47], [-8, -24], [-32, -38], [-40, -3], [-22, -24], [-2, -36], [-32, -7], [-34, -45], [-30, -63], [-11, -43], [-1, -65], [40, -9], [13, -52], [13, -42], [39, 11], [51, -24]], [[2526, 8158], [28, -22], [20, -26]], [[2574, 8110], [35, -15], [29, -23], [46, -4], [30, -5], [-4, -48], [8, -56], [21, -62], [41, -53], [21, 18], [15, 57], [-14, 88], [-20, 29], [45, 26], [31, 39], [16, 39], [-3, 37], [-19, 47], [-33, 42], [32, 58], [-12, 51], [-9, 86], [19, 13], [48, -15], [29, -5], [23, 14], [25, -19], [35, -32], [8, -21], [50, -5], [-1, -46], [9, -70], [25, -9], [21, -33], [40, 31], [26, 61], [19, 26], [21, -49], [36, -71], [31, -67], [-11, -35], [37, -31], [25, -32], [44, -14], [18, -18], [11, -47], [22, -7], [11, -21], [2, -63], [-20, -21], [-20, -19], [-46, -20], [-35, -46], [-47, -9], [-59, 12], [-42, 1], [-29, -4], [-23, -40], [-35, -25], [-40, -73], [-32, -52], [23, 10], [45, 73], [58, 46], [42, 5], [24, -27], [-26, -37], [9, -60], [9, -42], [36, -28], [46, 8], [28, 63], [2, -40], [17, -21], [-34, -36], [-61, -33], [-28, -23], [-31, -40], [-21, 5], [-1, 47], [48, 45], [-44, -1], [-31, -7]], [[3135, 7507], [5, -18], [-30, -27], [-29, -19], [-29, -17]], [[3052, 7426], [-15, -33]], [[3037, 7393], [-4, -12]], [[3033, 7381], [-1, -30], [10, -29], [11, -2], [-3, 21], [8, -13], [-2, -16], [-19, -9], [-13, 2]], [[3024, 7305], [-20, -10]], [[3004, 7295], [-12, -3], [-17, -3]], [[2975, 7289], [-23, -16]], [[2952, 7273], [41, 11], [8, -11]], [[3001, 7273], [-39, -17], [-17, 0]], [[2945, 7256], [0, 7]], [[2945, 7263], [-8, -15], [8, -3], [-6, -40], [-20, -42], [-2, 14], [-6, 3], [-9, 14]], [[2902, 7194], [5, -30], [7, -10]], [[2914, 7154], [1, -21]], [[2915, 7133], [-9, -22]], [[2906, 7111], [-16, -44], [-2, 2], [8, 38]], [[2896, 7107], [-14, 21], [-3, 46], [-5, -24], [5, -35]], [[2879, 7115], [-18, 9]], [[2861, 7124], [19, -18]], [[2880, 7106], [1, -53], [8, -4], [3, -19]], [[2892, 7030], [4, -56], [-17, -41], [-29, -16], [-18, -33]], [[2832, 6884], [-14, -4], [-14, -20]], [[2804, 6860], [-4, -19], [-31, -36], [-16, -26]], [[2753, 6779], [-13, -33], [-4, -39]], [[2736, 6707], [5, -39], [9, -48], [13, -39], [0, -24], [13, -64], [-1, -38], [-1, -21], [-7, -34], [-8, -7], [-14, 6], [-4, 25], [-11, 13], [-15, 47], [-13, 43], [-4, 22], [6, 36], [-8, 31], [-22, 46], [-10, 9], [-28, -25], [-5, 3], [-14, 25], [-17, 14], [-32, -7]], [[2568, 6681], [-24, 6]], [[2544, 6687], [-21, -3]], [[2523, 6684], [-12, -9]], [[2511, 6675], [5, -15]], [[2516, 6660], [0, -22], [5, -11], [-5, -8]], [[2516, 6619], [-10, 9], [-11, -11], [-20, 2], [-20, 29], [-25, -7], [-20, 13], [-17, -4]], [[2393, 6650], [-24, -13], [-25, -41], [-27, -24], [-16, -27], [-6, -25], [0, -38], [1, -27], [5, -18]], [[2301, 6437], [-10, -49]], [[2291, 6388], [-5, -40], [-2, -75], [-3, -27], [5, -30], [9, -27], [5, -43], [19, -42], [6, -31], [11, -28], [29, -14], [12, -24], [24, 16], [21, 6], [21, 10], [18, 9], [17, 23], [7, 32], [2, 47], [5, 16], [19, 15], [29, 12], [25, -1], [17, 4], [6, -12], [-1, -26], [-15, -33], [-6, -34], [5, -10], [-4, -24], [-7, -43], [-7, 14], [-6, -1]], [[2547, 6027], [0, -8], [5, 0], [0, -15], [-5, -24], [3, -9], [-3, -20], [2, -5], [-4, -28], [-5, -15], [-5, -2], [-6, -19]], [[2529, 5882], [10, -10], [2, 8]], [[2541, 5880], [9, -7], [2, -2]], [[2552, 5871], [6, 10], [8, 1], [3, -5], [4, 3], [13, -5], [13, 1], [9, 6], [3, 7], [9, -3], [6, -4], [8, 1], [5, 5], [13, -8], [4, -1], [9, -10], [8, -13], [10, -8], [7, -15]], [[2690, 5833], [-2, -6], [-2, -12], [3, -21], [-6, -18], [-3, -23], [-1, -24], [1, -15], [1, -25], [-4, -5], [-3, -24], [2, -15], [-6, -14], [2, -15], [4, -9]], [[2676, 5607], [2, -8], [5, -22]], [[2683, 5577], [11, -22], [13, -24]], [[2707, 5531], [10, -20], [-1, -12], [11, -2], [3, 4], [8, -13], [13, 4], [12, 14], [17, 11], [9, 16], [16, -3], [-1, -5], [15, -2], [12, -10]], [[2831, 5513], [10, -17], [10, -15]], [[3008, 6095], [3, 9]], [[3011, 6104], [6, 1], [16, -1]], [[3033, 6104], [16, -14], [8, 1], [5, -20], [15, 1], [-1, -16], [12, -2], [14, -21], [-10, -22], [-14, 12], [-12, -2], [-9, 2], [-5, -10], [-11, -3], [-4, 13], [-10, -8], [-11, -38], [-7, 9], [-1, 16]], [[3008, 6002], [-19, 9], [-13, -4], [-17, 5], [-13, -11], [-15, 17], [3, 18], [25, -7], [21, -5], [10, 13], [-12, 24], [0, 21], [-18, 8], [7, 16], [17, -3], [24, -8]], [[5409, 7118], [22, 5], [-10, -43], [4, -18], [-6, -28], [-21, 21], [-14, 6], [-39, 28], [4, 28], [32, -5], [28, 6]], [[6042, 7480], [32, -21], [35, -47]], [[8676, 6858], [15, 34], [16, -7], [12, 23], [20, -12], [4, -19], [-16, -33], [-11, 18], [-15, -13], [-7, -33], [-18, 16], [0, 26]], [[4548, 6060], [-3, 28], [2, 28], [-7, 26], [-14, 24]], [[4526, 6166], [1, 24]], [[4527, 6190], [1, 25], [11, 15], [9, 29], [-2, 19], [10, 39], [15, 36], [9, 9], [8, 32], [0, 30], [10, 34], [19, 20], [18, 57]], [[4635, 6535], [14, 22]], [[4649, 6557], [26, 6], [22, 38], [14, 15], [23, 46], [-7, 69], [10, 48], [4, 29], [18, 38], [28, 25], [21, 23], [18, 58], [9, 34], [20, 0], [17, -24], [26, 4], [29, -12], [12, -1]], [[4939, 6953], [27, 30], [30, 10], [17, 23], [27, 17], [47, 10], [46, 4], [14, -8], [26, 22], [30, 0]], [[5203, 7061], [11, -12], [19, 3]], [[5233, 7052], [31, 22], [19, -6], [-1, -28], [24, 20], [2, -11], [-14, -27], [0, -26], [9, -13], [-3, -48], [-19, -28], [6, -31], [14, -1], [7, -26], [11, -9]], [[5319, 6840], [32, -19], [12, 5], [23, -9], [37, -25], [13, -50], [25, -10], [39, -24], [30, -27], [13, 14], [13, 26], [-6, 42], [9, 27], [20, 26], [19, 8], [37, -11], [10, -25], [10, 0], [9, -10], [28, -6], [6, -19]], [[5698, 6753], [37, 1], [27, -15], [28, -16], [13, -9], [21, 18], [11, 16], [25, 4], [20, -7], [7, -27], [7, 18], [22, -13], [22, -3], [13, 14]], [[5951, 6734], [8, 18], [-2, 3], [8, 26], [5, 42], [4, 14], [1, 1]], [[5975, 6838], [10, 45], [14, 39], [0, 2]], [[5999, 6924], [-2, 43], [7, 22]], [[6004, 6989], [-11, 26], [11, 20], [-17, -4], [-23, 12], [-19, -31], [-43, -7], [-22, 30], [-30, 2], [-6, -23], [-20, -7], [-26, 30], [-31, -1], [-16, 55], [-21, 31], [14, 43], [-18, 27], [31, 53], [43, 2], [12, 42], [53, -7], [33, 36], [32, 16], [46, 1], [49, -39], [40, -22], [32, 9], [24, -5], [33, 29]], [[6154, 7307], [4, 24], [-7, 37], [-16, 21], [-16, 6], [-10, 17]], [[6042, 7480], [-24, 33], [20, 9], [23, 46], [-15, 22], [41, 23], [-1, 12], [-25, -9]], [[6061, 7616], [-22, -4], [-18, -18], [-26, -3], [-24, -21]], [[5971, 7570], [1, -35]], [[5972, 7535], [14, -13], [28, 3], [-5, -19], [-31, -10], [-37, -32], [-16, 11], [6, 26], [-30, 16], [5, 11], [26, 19]], [[5932, 7547], [-8, 12]], [[5924, 7559], [-43, 14], [-2, 21], [-25, -7], [-11, -30], [-21, -41]], [[5822, 7516], [0, -15], [-13, -12], [-9, 5], [-7, -67]], [[5793, 7427], [-15, -23], [-10, -39], [9, -32]], [[5777, 7333], [3, -21], [25, -18], [-5, -14], [-33, -3], [-12, -17], [-23, -30], [-9, 26], [0, 11]], [[5723, 7267], [-17, 2], [-14, 5], [-34, -14], [19, -32], [-14, -9], [-15, 0], [-15, 29], [-5, -12], [6, -33], [14, -26], [-10, -13], [15, -25], [14, -16], [0, -32], [-25, 15], [8, -28], [-18, -6], [11, -49], [-19, -1], [-23, 24], [-10, 45], [-5, 37], [-11, 25], [-14, 32], [-2, 16]], [[5559, 7201], [-5, 4], [0, 12], [-15, 18], [-3, 27], [2, 38], [4, 17], [-4, 9]], [[5538, 7326], [-6, 4], [-8, 18], [-12, 11]], [[5512, 7359], [-26, 21], [-16, 20], [-26, 16], [-23, 41], [6, 4], [-13, 24], [-1, 18], [-17, 9], [-9, -24], [-8, 19], [0, 19], [1, 1]], [[5380, 7527], [7, 5]], [[5387, 7532], [-22, 8], [-23, -20], [1, -27], [-3, -16], [9, -28], [26, -28], [14, -46], [31, -45], [22, 0], [7, -12], [-8, -11], [25, -20], [20, -17], [24, -29], [3, -10], [-5, -20], [-16, 26], [-24, 9], [-12, -36], [20, -20], [-3, -29], [-11, -4], [-15, -47], [-12, -5], [0, 17], [6, 30], [6, 12], [-11, 32], [-8, 28], [-12, 7], [-8, 24], [-18, 10], [-12, 23], [-21, 3], [-21, 25], [-26, 36], [-19, 32], [-8, 55], [-14, 7], [-23, 18], [-12, -7], [-16, -26], [-12, -4]], [[5206, 7427], [-25, -32], [-55, 15], [-40, -18], [-4, -33]], [[5082, 7359], [2, -32], [-26, -37], [-36, -12], [-2, -19], [-18, -31], [-10, -45], [11, -32], [-16, -24], [-6, -36], [-21, -12], [-20, -42], [-35, -1], [-27, 1], [-17, -20], [-11, -21], [-13, 5], [-11, 19], [-8, 32], [-26, 8]], [[4792, 7060], [-11, -14], [-14, 8], [-15, -6], [5, 43], [-3, 34], [-12, 5], [-7, 21], [2, 37], [11, 20], [2, 22], [6, 34], [-1, 23], [-5, 20], [-1, 19]], [[4749, 7326], [1, 40], [-11, 24], [39, 40], [34, -10], [37, 0], [30, -9], [23, 2], [45, -1]], [[4947, 7412], [14, 33], [5, 111], [-28, 58], [-21, 28], [-42, 21], [-3, 41], [36, 12], [47, -14], [-9, 63], [26, -24], [65, 43], [8, 46], [24, 11]], [[5069, 7841], [23, 11]], [[5092, 7852], [14, 15], [24, 82], [38, 23], [23, -2]], [[5191, 7970], [6, 12], [23, 3], [5, -12], [19, 27], [-6, 21], [-2, 32]], [[5236, 8053], [-11, 31], [-1, 56], [5, 15], [8, 17], [24, 3], [10, 16], [22, 15], [-1, -28], [-8, -18], [4, -16], [15, -8], [-7, -21], [-8, 6], [-20, -40], [7, -27]], [[5275, 8054], [1, -22], [28, -13], [-1, -19], [29, 10], [15, 15], [32, -22], [13, -17]], [[5392, 7986], [19, 16], [43, 26], [35, 19], [28, -10], [2, -13], [27, -1]], [[5546, 8023], [6, 24], [38, 18]], [[5590, 8065], [-6, 47]], [[5584, 8112], [1, 42], [14, 35], [26, 19], [22, -42], [22, 1], [6, 43]], [[5675, 8210], [3, 33], [-10, -7], [-18, 19], [-2, 32], [35, 16], [35, 8], [30, -9], [29, 1]], [[5777, 8303], [31, 31], [-29, 26]], [[5779, 8360], [-50, -4], [-49, -20], [-45, -12], [-16, 30], [-27, 19], [6, 54], [-14, 50], [14, 33], [25, 35], [63, 60], [19, 11], [-3, 24], [-39, 26]], [[5663, 8666], [-47, -16], [-27, -38], [4, -34], [-44, -45], [-54, -48], [-20, -78], [20, -39], [26, -31], [-25, -63], [-29, -13], [-11, -93], [-15, -52], [-34, 5], [-16, -44], [-32, -2], [-9, 52], [-23, 63], [-21, 79]], [[5306, 8269], [-19, 34], [-55, -64], [-37, -13], [-38, 28], [-10, 60], [-9, 128], [26, 36], [73, 46], [55, 58], [51, 77], [66, 107], [47, 42], [76, 70], [61, 24], [46, -3], [42, 46], [51, -2], [50, 11], [87, -41], [-36, -15], [30, -35]], [[5863, 8863], [29, 20], [46, -34], [76, -13], [105, -63], [21, -26], [2, -37], [-31, -29], [-45, -15], [-124, 42], [-21, -7], [45, -41]], [[5966, 8660], [2, -26], [2, -56]], [[5970, 8578], [36, -17], [22, -15], [3, 27]], [[6031, 8573], [-17, 24], [18, 21]], [[6032, 8618], [67, -34], [24, 13], [-19, 41], [65, 54], [25, -3], [26, -19], [16, 38], [-23, 33]], [[6213, 8741], [13, 34], [-20, 34]], [[6206, 8809], [78, -18], [16, -31], [-35, -7], [0, -31], [22, -19], [43, 12], [7, 35]], [[6337, 8750], [155, 75]], [[6492, 8825], [20, -3], [-27, -34], [35, -6], [19, 19], [52, 2], [42, 23], [31, -34], [32, 37], [-29, 32], [14, 19], [82, -17]], [[6763, 8863], [39, -18], [100, -63]], [[6902, 8782], [19, 29], [-28, 29], [-1, 12], [-34, 6], [10, 26], [-15, 43], [-1, 18], [51, 50], [18, 51], [21, 11], [74, -15], [5, -31], [-26, -45], [17, -17], [9, -39], [-6, -76], [31, -34], [-12, -38], [-55, -78], [32, -9], [11, 20], [31, 15], [7, 27], [24, 27], [-16, 31], [13, 37], [-31, 4], [-6, 31], [22, 56], [-36, 45], [50, 38], [-7, 39], [14, 2], [15, -31], [-11, -54], [29, -10], [-12, 40], [46, 22], [58, 3], [51, -32], [-25, 46], [-2, 60], [48, 11], [67, -3], [60, 8], [-23, 29], [33, 36], [31, 2], [54, 27], [74, 8], [9, 15], [73, 5], [23, -12], [62, 29], [51, -1], [8, 24], [26, 24], [66, 23], [48, -18], [-38, -14], [63, -9], [7, -27], [25, 13], [82, 0], [62, -27], [23, -21], [-7, -29], [-31, -16], [-73, -31], [-21, -17], [35, -8], [41, -14], [25, 11], [14, -36], [12, 15], [44, 8], [90, -9], [6, -26], [116, -8], [2, 42], [59, -9], [44, 0], [45, -29], [13, -36], [-17, -23], [35, -44], [44, -22], [27, 58], [44, -25], [48, 15], [53, -17], [21, 15], [45, -7], [-20, 51], [37, 24], [251, -36], [24, -33], [72, -42], [112, 10], [56, -9], [23, -23], [-4, -40], [35, -16], [37, 11], [49, 2], [52, -11]], [[9608, 8859], [54, 5], [48, -49]], [[9710, 8815], [34, 18], [-23, 36], [13, 24], [88, -15], [58, 3], [80, -26]], [[9960, 8855], [39, -25]], [[9999, 8609], [-36, -25]], [[9963, 8584], [-36, 4], [25, -29], [17, -46], [13, -15], [3, -23], [-7, -15], [-52, 13], [-78, -42], [-25, -7], [-42, -39], [-40, -34], [-11, -25], [-39, 38], [-73, -43], [-12, 20], [-27, -23], [-37, 7], [-9, -36], [-33, -54], [1, -23], [31, -12], [-4, -81], [-25, -2], [-12, -46], [11, -24], [-48, -29], [-10, -63], [-41, -14], [-9, -56], [-40, -52], [-10, 38], [-12, 81], [-15, 124], [13, 77], [23, 33], [2, 26], [43, 12], [50, 70], [47, 58], [50, 44], [23, 78], [-34, -4], [-17, -46], [-70, -61], [-23, 68], [-72, -19], [-69, -93], [23, -34]], [[9306, 8285], [-62, -15], [-43, -5]], [[9201, 8265], [2, 40], [-43, 8], [-35, -27], [-85, 10], [-91, -17], [-90, -108], [-106, -131], [43, -7], [14, -35], [27, -12], [18, 27], [30, -3], [40, -61], [1, -48], [-21, -55], [-3, -66], [-12, -89], [-42, -81], [-9, -38], [-38, -65], [-38, -64]], [[8763, 7443], [-18, -33], [-37, -32]], [[8708, 7378], [-17, -1], [-17, 27], [-38, -41], [-4, -18]], [[8632, 7345], [-11, 3], [-12, -19], [-8, -19], [1, -39], [-14, -13], [-5, -10], [-11, -16], [-18, -9], [-12, -15], [-1, -24], [-3, -6], [11, -9], [15, -25]], [[8564, 7144], [24, -65], [7, -36], [0, -64], [-10, -31], [-25, -10], [-22, -23], [-25, -5], [-3, 30], [5, 42], [-13, 58], [21, 9], [-19, 47]], [[8504, 7096], [-13, 11], [-4, -10], [-8, -5], [-1, 10], [-7, 6], [-8, 8], [8, 25], [7, 6], [-3, 11]], [[8475, 7158], [7, 30], [-2, 9]], [[8480, 7197], [-16, 6], [-13, 15]], [[8451, 7218], [-39, -17], [-20, -26], [-30, -15], [15, 26], [-6, 22], [22, 37], [-15, 29], [-24, -20], [-32, -38], [-17, -36], [-27, -3], [-14, -26], [15, -37], [22, -9], [1, -25], [22, -16], [31, 39], [25, -21], [18, -2], [4, -29], [-39, -16], [-13, -30], [-27, -27], [-14, -39], [30, -31], [11, -54], [17, -51], [18, -43], [0, -41], [-17, -15], [6, -30], [17, -17], [-5, -46], [-7, -44], [-15, -5], [-21, -60], [-22, -73], [-26, -66], [-38, -51], [-39, -47], [-31, -6], [-17, -25], [-10, 18], [-15, -28], [-39, -27], [-29, -9]], [[8077, 6188], [-8, -46], [-2, -13]], [[8067, 6129], [-15, -3], [-8, 41], [7, 21], [-37, 18], [-13, -9]], [[8001, 6197], [-37, -48], [-24, -52], [-6, -39], [22, -58], [25, -73], [26, -34], [17, -45], [12, -103], [-3, -97], [-24, -37], [-31, -36], [-23, -46], [-35, -52], [-10, 36], [8, 37], [-21, 32]], [[7897, 5582], [-23, 8], [-11, 29], [-14, 57]], [[7849, 5676], [-25, 26], [-24, -1], [4, 44], [-24, -1], [-2, -61], [-15, -81], [-10, -49], [2, -40], [18, -2], [12, -51], [5, -48], [15, -32], [17, -6], [14, -29]], [[7836, 5345], [7, -5], [16, -34], [12, -37], [2, -37], [-3, -25], [2, -20], [2, -32], [10, -16], [11, -49], [-1, -19], [-19, -3], [-27, 41], [-32, 44], [-4, 28], [-16, 37], [-4, 46], [-10, 31], [4, 40], [-7, 24]], [[7779, 5359], [-11, 21], [-4, 27], [-15, 32], [-14, 26], [-4, -33], [-5, 31], [3, 35], [8, 53]], [[7737, 5551], [-3, 42], [9, 42], [-10, 33], [3, 60], [-12, 29], [-9, 67], [-5, 70], [-12, 46], [-18, -28], [-32, -40], [-15, 5], [-17, 13], [9, 69], [-6, 52], [-21, 64], [3, 20], [-16, 7], [-20, 46]], [[7565, 6148], [-8, 29], [-1, 28], [-6, 27], [-11, 32], [-26, 2], [3, -23], [-9, -30], [-12, 11], [-4, -10], [-8, 6], [-11, 5]], [[7472, 6225], [-4, -21], [-19, 1], [-34, -11], [2, -42], [-15, -33], [-40, -37], [-31, -66], [-21, -35], [-28, -36], [0, -26], [-13, -13]], [[7269, 5906], [-26, -20], [-12, -3]], [[7231, 5883], [-9, -43], [6, -72], [1, -46], [-11, -53], [0, -94], [-15, -3], [-12, -42], [8, -19], [-25, -15], [-10, -38], [-11, -16], [-26, 52], [-13, 78], [-11, 56], [-9, 26], [-15, 53], [-7, 70], [-5, 34], [-25, 77], [-12, 107], [-8, 72], [0, 67], [-5, 52], [-41, -33], [-19, 6], [-36, 68], [13, 20], [-8, 21], [-33, 48]], [[6893, 6316], [-20, 14], [-9, 40], [-21, 42], [-51, -11], [-45, -1], [-39, -7]], [[6708, 6393], [-53, 16], [-30, 13], [-31, 7], [-12, 68], [-13, 10], [-22, -10], [-28, -26], [-34, 18], [-28, 43], [-27, 15], [-18, 53], [-21, 74], [-15, -9], [-17, 19], [-11, -22]], [[6348, 6662], [-16, 3]], [[6332, 6665], [6, -25], [-3, -13], [9, -41]], [[6344, 6586], [11, -48], [14, -13], [5, -20], [18, -23], [2, -23], [-3, -18], [4, -19], [8, -16], [4, -18], [4, -13]], [[6411, 6375], [-2, 40], [7, 29], [8, 6], [8, -17], [1, -33], [-6, -32]], [[6427, 6368], [5, -22]], [[6432, 6346], [5, 3], [1, -15], [22, 9], [23, -2], [17, -2], [19, 38], [20, 36], [18, 34]], [[6557, 6447], [8, 19], [3, -5], [-2, -23], [-4, -10]], [[6562, 6428], [4, -44]], [[6566, 6384], [12, -38], [16, -20], [20, -7], [17, -10], [12, -32], [8, -19], [10, -7], [0, -12], [-10, -33], [-5, -16], [-12, -18], [-10, -38], [-13, 3], [-5, -13], [-5, -28], [4, -37], [-3, -7], [-13, 0], [-17, -21], [-3, -27], [-6, -11], [-18, 0], [-10, -14], [0, -22], [-14, -16], [-15, 5], [-19, -18], [-12, -4]], [[6475, 5924], [-21, -14], [-5, -25]], [[6449, 5885], [-1, -19], [-27, -24], [-45, -25], [-24, -40], [-13, -3], [-8, 4], [-16, -23], [-18, -11], [-23, -3], [-7, -3], [-6, -15], [-8, -4], [-4, -14], [-14, 1], [-9, -7], [-19, 2], [-7, 33], [1, 30], [-5, 17], [-5, 41], [-8, 23], [5, 2], [-2, 26], [3, 10], [-1, 25]], [[6188, 5908], [-4, 23], [-8, 17], [-2, 22], [-15, 20], [-15, 47], [-7, 45], [-20, 38], [-12, 9], [-18, 53], [-4, 39], [2, 33], [-16, 61], [-13, 22], [-15, 12], [-10, 31], [2, 13], [-8, 29], [-8, 12], [-11, 41], [-17, 45], [-14, 38], [-14, 0], [5, 31], [1, 19], [3, 22]], [[5970, 6630], [-1, 8]], [[5969, 6638], [-7, -22], [-6, -42], [-8, -29], [-6, -10], [-10, 18], [-12, 25], [-20, 80], [-3, -5], [12, -59], [17, -56], [21, -86], [10, -30], [9, -32], [25, -61], [-6, -10], [1, -36], [33, -50], [4, -11]], [[6023, 6222], [9, -55], [-6, -10], [4, -57], [11, -66], [10, -14], [15, -21]], [[6066, 5999], [16, -64], [8, -51], [15, -27], [38, -53], [16, -31], [15, -32], [8, -19], [14, -17]], [[6196, 5705], [7, -17], [-1, -23], [-16, -14], [12, -15]], [[6198, 5636], [9, -10], [5, -23], [13, -23], [14, 0], [26, 14], [30, 6], [25, 18], [13, 3], [10, 10], [16, 2]], [[6359, 5633], [9, 1], [13, 9], [14, 5], [14, 19], [10, 0], [1, -15], [-3, -32], [0, -29], [-6, -20], [-7, -61], [-14, -62]], [[6390, 5448], [-17, -71], [-24, -81]], [[6349, 5296], [-23, -62], [-33, -76], [-28, -45], [-42, -55], [-25, -42], [-31, -68], [-6, -29], [-6, -13]], [[6155, 4906], [-20, -22], [-7, -23], [-10, -4], [-4, -40], [-9, -22], [-5, -37], [-12, -18]], [[6088, 4740], [-12, -69], [1, -31], [18, -20], [1, -15], [-8, -33], [2, -17], [-2, -27], [10, -35], [11, -54], [10, -13]], [[6119, 4426], [5, -25], [-1, -55], [3, -49], [1, -86], [5, -28], [-8, -39], [-11, -39], [-18, -34], [-25, -21], [-31, -27], [-32, -60], [-10, -10], [-20, -40], [-11, -13], [-3, -39], [14, -42], [5, -33], [0, -16], [5, 2], [-1, -54], [-4, -26], [6, -9], [-4, -24], [-11, -19], [-23, -19], [-34, -30], [-12, -21], [3, -23], [7, -4], [-3, -29]], [[5911, 3514], [-7, -40], [-3, -47], [-7, -25], [-19, -28], [-5, -8], [-12, -28], [-8, -29], [-16, -39], [-31, -58], [-20, -33], [-21, -25], [-29, -22], [-14, -3], [-3, -15], [-17, 8], [-14, -10], [-30, 10], [-17, -7]], [[5638, 3115], [-12, 3], [-28, -22]], [[5598, 3096], [-24, -8], [-17, -21], [-13, -2], [-11, 20], [-10, 1], [-12, 25], [-1, -8], [-4, 15], [0, 33], [-9, 37], [9, 10], [0, 43], [-19, 52], [-14, 47], [-20, 72]], [[5453, 3412], [-20, 42], [-11, 41], [-6, 54], [-7, 40], [-9, 85], [-1, 67], [-3, 30], [-11, 23], [-15, 46], [-14, 67], [-6, 35], [-23, 54], [-2, 42]], [[5325, 4038], [-2, 35], [4, 49], [9, 51], [2, 24], [9, 50], [6, 23], [16, 36], [9, 25], [3, 41], [-1, 31], [-9, 20], [-7, 34], [-7, 33], [2, 12], [8, 22], [-8, 54], [-6, 37], [-14, 35], [3, 11]], [[5342, 4661], [-4, 17]], [[5338, 4678], [-8, 42]], [[5330, 4720], [-22, 58]], [[5308, 4778], [-29, 57], [-18, 45], [-17, 58], [1, 18], [6, 18], [7, 40], [5, 42]], [[5263, 5056], [-5, 8], [10, 62]], [[5268, 5126], [4, 44], [-11, 37], [-13, 9], [-6, 25], [-7, 8], [1, 16]], [[5236, 5265], [-29, -20], [-11, 3], [-10, -13], [-23, 1], [-15, 35], [-9, 40], [-19, 37], [-21, -1], [-25, 0]], [[5074, 5347], [-23, -6]], [[5051, 5341], [-22, -12]], [[5029, 5329], [-44, -33], [-15, -19], [-25, -16], [-25, 16]], [[4920, 5277], [-12, -1], [-20, 11], [-18, 0], [-33, -10], [-19, -16], [-27, -21], [-6, 2]], [[4785, 5242], [-7, -1], [-29, 27], [-25, 42], [-24, 31], [-18, 35]], [[4682, 5376], [-8, 5], [-20, 22], [-14, 30], [-5, 20], [-3, 41]], [[4632, 5494], [-13, 33], [-10, 22], [-8, 7], [-6, 11], [-4, 24], [-4, 13], [-8, 9]], [[4579, 5613], [-15, 23], [-11, 4], [-7, 15], [1, 9], [-9, 12], [-2, 11]], [[4536, 5687], [-4, 43]], [[4532, 5730], [3, 25]], [[4535, 5755], [-11, 43], [-14, 20], [12, 10], [14, 39], [6, 29]], [[4542, 5896], [-2, 30], [8, 27], [3, 52], [-3, 55]], [[68, 8788], [-68, 42]], [[0, 4107], [6, 3], [-4, -27], [-2, -3]], [[8498, 4531], [19, 2], [9, 7], [10, -7], [-10, -15], [-29, -24], [-23, -16]], [[8474, 4478], [-18, -42], [-24, -12], [-3, 7], [2, 19], [12, 33], [28, 23]], [[8471, 4506], [3, 13], [24, 12]], [[0, 294], [2, -1], [24, 33], [50, -18]], [[76, 308], [33, 20]], [[109, 328], [7, -1]], [[116, 327], [40, -23], [35, 23], [7, 3], [81, 10], [27, -13], [13, -7], [41, -18], [79, -15], [63, -17], [107, -13], [80, 15], [118, -11], [67, -17], [73, 16], [78, 15], [6, 27], [-110, 2], [-89, 13], [-24, 22], [-74, 12], [5, 25], [10, 22], [10, 21], [-5, 23], [-46, 15], [-22, 20], [-43, 17], [68, -3], [64, 9], [40, -19], [50, 17], [45, 20], [23, 19], [-10, 23], [-36, 15], [-41, 16], [-57, 3], [-50, 8], [-54, 5], [-18, 21], [-36, 18], [-21, 19], [-9, 63], [14, -5], [25, -18], [45, 6], [44, 8], [23, -24], [44, 5], [37, 12], [35, 15], [32, 19], [41, 5], [-1, 21], [-9, 21], [8, 19], [36, 10], [16, -19], [42, 11], [32, 14], [40, 2], [38, 5], [37, 13], [30, 12], [34, 12], [22, -3], [19, -5], [41, 8], [37, -10], [38, 1], [37, 8], [37, -6], [41, -5], [39, 2], [40, -1], [42, -1], [38, 2], [28, 17], [34, 8], [35, -12], [33, 10], [30, 20], [18, -18], [9, -19], [18, -19], [29, 16], [33, -20], [38, -7], [32, -15], [39, 3], [36, 10], [41, -2], [38, -8], [38, -10], [15, 24], [-18, 19], [-14, 20], [-36, 4], [-15, 21], [-6, 20], [-10, 42], [21, -8], [36, -3], [36, 3], [33, -9], [28, -16], [12, -20], [38, -3], [36, 8], [38, 11], [34, 6], [28, -13], [37, 4], [24, 43], [23, -25], [32, -10], [34, 5], [23, -21], [37, -2], [33, -7], [34, -12], [21, 21], [11, 19], [28, -21], [38, 5], [28, -12], [19, -19], [37, 6], [29, 12], [29, 14], [33, 8], [39, 6], [36, 8], [27, 12], [16, 17], [7, 24], [-3, 23], [-9, 22], [-10, 22], [-9, 21], [-7, 20], [-1, 22], [2, 21], [13, 21], [11, 23], [5, 22], [-6, 24], [-3, 21], [14, 26], [15, 16], [18, 21], [19, 17], [22, 16], [11, 24], [15, 15], [18, 15], [26, 3], [18, 17], [19, 11], [23, 7], [20, 14], [16, 17], [22, 7], [16, -14], [-10, -19], [-29, -16], [-11, -12], [-21, 9], [-23, -6], [-19, -13], [-20, -14], [-14, -16], [-4, -22], [2, -21], [13, -18], [-19, -13], [-26, -5], [-15, -18], [-17, -18], [-17, -24], [-4, -20], [9, -23], [15, -18], [23, -13], [21, -17], [12, -22], [6, -21], [8, -21], [13, -19], [8, -20], [4, -52], [8, -20], [2, -22], [9, -22], [-4, -29], [-15, -23], [-17, -19], [-37, -7], [-12, -20], [-17, -18], [-42, -21], [-37, -9], [-35, -12], [-37, -12], [-22, -23], [-45, -2], [-49, 2], [-44, -4], [-47, 0], [9, -22], [42, -9], [31, -16], [18, -19], [-31, -18], [-48, 6], [-40, -14], [-2, -23], [-1, -22], [33, -19], [6, -20], [35, -21], [59, -9], [50, -15], [40, -17], [50, -18], [70, -8], [68, -16], [47, -16], [52, -18], [27, -27], [13, -20], [34, 19], [46, 17], [48, 17], [58, 14], [49, 15], [69, 2], [68, -8], [56, -13], [18, 24], [39, 16], [70, 1], [55, 12], [52, 12], [58, 8], [62, 10], [43, 14], [-20, 19], [-12, 20], [0, 21], [-54, -3], [-57, -8], [-54, 0], [-8, 20], [4, 42], [12, 12], [40, 13], [47, 13], [34, 16], [33, 17], [25, 21], [38, 10], [38, 8], [19, 4], [43, 2], [41, 8], [34, 11], [34, 13], [30, 13], [39, 17], [24, 19], [26, 16], [9, 22], [-30, 13], [10, 23], [18, 17], [29, 11], [31, 13], [28, 18], [22, 21], [13, 26], [21, 16], [33, -4], [13, -18], [34, -2], [1, 20], [14, 22], [30, -5], [7, -21], [33, -3], [36, 10], [35, 6], [31, -3], [12, -23], [31, 19], [28, 9], [31, 8], [31, 8], [29, 13], [31, 8], [24, 12], [17, 20], [20, -14], [29, 7], [20, -26], [16, -19], [32, 11], [12, 21], [28, 16], [37, -4], [11, -20], [22, 20], [30, 7], [33, 2], [29, -1], [31, -7], [30, -3], [13, -18], [18, -17], [31, 10], [32, 2], [32, 0], [31, 1], [28, 8], [29, 6], [25, 16], [26, 10], [28, 5], [21, 15], [15, 31], [16, 18], [29, -9], [11, -19], [24, -13], [29, 4], [19, -19], [21, -15], [28, 13], [10, 24], [25, 10], [29, 19], [27, 7], [33, 11], [22, 12], [22, 13], [22, 12], [26, -6], [25, 19], [18, 16], [26, -2], [23, 14], [6, 19], [23, 15], [23, 11], [28, 9], [25, 4], [25, -3], [26, -5], [22, -16], [3, -24], [24, -18], [17, -15], [33, -7], [19, -15], [23, -15], [26, -4], [23, 11], [24, 23], [26, -12], [27, -6], [26, -7], [27, -4], [28, 0], [23, -58], [-1, -14], [-4, -25], [-26, -14], [-22, -21], [4, -22], [31, 1], [-4, -21], [-14, -21], [-13, -23], [21, -17], [32, -6], [32, 10], [15, 22], [10, 20], [15, 18], [17, 16], [7, 20], [15, 27], [18, 5], [31, 3], [28, 6], [28, 9], [14, 22], [8, 20], [19, 21], [27, 14], [23, 11], [16, 19], [15, 9], [21, 9], [27, -5], [25, 5], [28, 7], [30, -4], [20, 16], [14, 37], [11, -16], [13, -26], [23, -11], [27, -4], [26, 6], [29, -4], [26, -1], [17, 5], [24, -3], [21, -12], [25, 8], [30, 0], [25, 7], [29, -7], [19, 18], [14, 19], [19, 15], [35, 41], [18, -7], [21, -15], [18, -20], [36, -34], [27, -1], [25, 0], [30, 7], [30, 7], [23, 16], [19, 16], [31, 2], [21, 12], [22, -11], [14, -17], [19, -18], [31, 3], [19, -15], [33, -14], [35, -5], [29, 4], [21, 18], [19, 17], [25, 4], [25, -7], [29, -6], [26, 9], [25, 0], [24, -5], [26, -6], [25, 10], [30, 9], [28, 2], [32, 0], [25, 5], [25, 5], [8, 27], [1, 23], [17, -16], [5, -25], [10, -23], [11, -18], [23, -10], [32, 3], [36, 2], [25, 3], [37, 0], [26, 1], [36, -2], [31, -5], [20, -17], [-5, -21], [18, -16], [30, -13], [31, -14], [35, -10], [38, -9], [28, -8], [32, -2], [18, 19], [24, -15], [21, -18], [25, -13], [34, -5], [32, -7], [13, -22], [32, -13], [21, -19], [31, -9], [32, 1], [30, -3], [33, 1], [34, -4], [31, -8], [28, -13], [29, -11], [20, -16], [-3, -22], [-15, -20], [-13, -25], [-9, -19], [-14, -23], [-36, -9], [-16, -19], [-36, -12], [-13, -22], [-19, -21], [-20, -17], [-11, -23], [-7, -21], [-3, -25], [0, -20], [16, -22], [6, -21], [13, -20], [52, -7], [11, -24], [-50, -9], [-43, -12], [-52, -2], [-24, -32], [-5, -26], [-12, -20], [-14, -21], [37, -19], [14, -22], [24, -21], [33, -19], [39, -17], [42, -17], [64, -18], [14, -27], [80, -12], [5, -4], [21, -17], [77, 14], [63, -17]], [[9951, 307], [48, -13]], [[68, 8788], [73, -55], [-3, -35], [19, -14], [-6, 41], [75, -8], [55, -52], [-28, -25], [-46, -5], [0, -55], [-11, -11], [-26, 1], [-22, 20], [-36, 16], [-7, 24], [-28, 9], [-31, -7], [-16, 19], [6, 21], [-33, -13], [13, -26], [-16, -24]], [[0, 8972], [4, 3], [23, -1], [40, -15], [-2, -8], [-29, -13], [-36, -4]], [[9999, 8934], [-30, -3]], [[9969, 8931], [-5, 18]], [[9964, 8949], [35, 23]], [[9999, 4080], [-18, -14]], [[9981, 4066], [-17, -12], [-4, 21], [14, 12], [9, 3]], [[9983, 4090], [16, 17]], [[3300, 2119], [33, 34], [24, -14], [16, 22], [22, -25], [-8, -20], [-37, -16], [-13, 19], [-23, -25], [-14, 25]], [[6914, 2298], [18, -17], [26, -7], [1, -11], [-7, -25], [-43, -4], [-1, 30], [4, 23], [2, 11]], [[9947, 4027], [7, 9], [9, -16], [-4, -29], [-17, -7], [-16, 6], [-2, 25], [10, 19], [13, -7]], [[3286, 5597], [16, 7], [6, -2], [-1, -41], [-23, -6], [-5, 5], [8, 15], [-1, 22]], [[683, 6115], [5, -5], [5, -7], [7, -20], [-1, -3], [-11, -12], [-9, -8], [-4, -10], [-7, 8], [1, 16], [-4, 20], [1, 6], [5, 9], [-2, 11], [1, 5], [3, -1], [10, -9]], [[667, 6153], [-3, -7], [-9, -4], [-5, 12], [-3, 5], [0, 3], [3, 5], [9, -6], [8, -8]], [[646, 6176], [-1, -6], [-15, 1], [2, 7], [14, -2]], [[610, 6206], [3, -4], [8, -18], [-2, -3], [-2, 0], [-9, 2], [-4, 13], [-1, 2], [7, 8]], [[573, 6234], [1, -13], [-4, -6], [-9, 10], [1, 4], [5, 6], [6, -1]], [[2828, 6400], [8, -2], [10, -46], [0, -33], [-7, -2], [-7, 32], [-10, 16], [6, 35]], [[2806, 6488], [13, 4], [18, -2], [1, -14], [-30, -9], [-2, 21]], [[2839, 6502], [22, -25], [-5, -40], [-5, 7], [0, 29], [-12, 22], [0, 7]], [[3221, 7612], [10, -27], [20, -7], [26, 1], [-14, -22], [-10, -4], [-35, 24], [-7, 18], [10, 17]], [[6296, 7478], [28, 57], [27, 9], [12, 33]], [[6363, 7577], [26, 12], [32, 24]], [[6421, 7613], [24, -14], [28, 3]], [[6473, 7602], [5, -34], [-5, -54], [-25, 8], [-23, -9], [-1, -41], [-27, 6], [1, -19], [15, -14], [13, -49], [32, -19], [5, -20], [-7, -23], [2, -13]], [[6458, 7321], [8, -36], [3, 40], [23, 14], [8, -31], [20, -34], [-25, -17], [-26, 13], [-6, -47], [19, -3], [-8, -38], [22, -19], [-4, -58], [5, -39]], [[6497, 7066], [-2, -13], [-44, -15], [-39, 10], [-20, 28], [-26, 11], [-9, 41]], [[6357, 7128], [-1, 28], [11, 13], [4, 19], [5, 43], [23, 5], [-8, 15], [-13, 2], [-14, 40], [-15, 29]], [[6349, 7322], [-30, 65], [2, 38], [-25, 53]], [[3207, 7770], [10, 5], [37, -14], [28, -24], [1, -10], [-14, -1], [-36, 18], [-26, 26]], [[2769, 8448], [10, 17], [12, -1], [7, -12], [-11, -29], [-12, 5], [-8, 17], [2, 3]], [[2667, 8469], [20, 25], [38, 0], [0, -11], [-33, -31], [-19, 2], [-6, 15]], [[2910, 8746], [-18, -16], [-31, -3], [-7, 27], [12, 31], [26, 8], [21, -16], [1, -23], [-4, -8]], [[2318, 8870], [25, -32]], [[2343, 8838], [-17, -19], [-38, 17], [-22, -6], [-38, 25], [24, 17], [19, 24], [30, -16], [17, -10]], [[2041, 9059], [31, -23], [17, -54], [9, -39], [47, -27], [50, -27], [-3, -24], [-46, -5], [18, -21]], [[2164, 8839], [-10, -20], [-50, 8]], [[2104, 8827], [-48, 15], [-32, -3], [-52, -19]], [[1972, 8820], [-70, -8], [-50, -5]], [[1852, 8807], [-15, 26], [-38, 15], [-24, -6], [-35, 44], [19, 6], [43, 9], [39, -2], [36, 9], [-54, 13], [-59, -4], [-39, 1], [-15, 21], [64, 22], [-42, -1], [-49, 15], [23, 41], [20, 22], [74, 34], [29, -11], [-14, -26], [61, 17], [39, -28], [31, 28], [26, -18], [23, -54], [14, 23], [-20, 57], [24, 8], [28, -9]], [[2784, 9044], [-31, 29], [1, 20], [14, 4], [63, -6], [48, -31], [3, -15], [-30, 2], [-30, 1], [-30, -8], [-8, 4]], [[8884, 9075], [27, 22], [34, 5], [40, -21], [3, -15], [-42, 0], [-57, 6], [-5, 3]], [[9116, 9185], [70, -15], [-32, -22], [-44, 5], [-52, 22], [7, 18], [51, -8]], [[8856, 9229], [73, -3], [100, -29], [-22, -41], [-102, 1], [-46, -13], [-55, 36], [15, 38], [37, 11]], [[2262, 9254], [2, 7], [21, -25], [1, -29], [-13, -41], [-46, -6], [-30, 9], [1, 32], [-45, -4], [-2, 43], [30, -2], [41, 19], [40, -3]], [[7856, 9404], [70, -32], [-8, -23], [-157, -21], [51, 73], [23, 6], [21, -3]], [[7604, 9500], [60, 13], [54, -28], [64, -54], [-7, -50], [-60, -7], [-78, 16], [-46, 22], [-21, 39], [-38, 11], [72, 38]], [[2333, 9477], [19, 17], [28, 4], [-12, 13], [65, 2], [35, -29]], [[2468, 9484], [93, -23]], [[2561, 9461], [22, -36], [33, -18], [-38, -17], [-51, -42], [-50, -4], [-57, 7], [-30, 23], [0, 20], [22, 15], [-50, 0], [-31, 18], [-18, 25], [20, 25]], [[2456, 9549], [41, 10], [32, 2], [55, 9], [41, 21], [34, -3], [30, -16], [21, 30], [37, 9], [50, 6], [85, 3], [14, -6], [81, 9], [60, -3], [60, -4], [74, -4], [60, -7], [51, -15], [-2, -15], [-67, -24], [-68, -12], [-25, -12], [61, 0], [-66, -34], [-45, -15], [-48, -46], [-57, -9], [-18, -11], [-84, -6], [39, -7], [-20, -10], [23, -27], [-26, -19], [-43, -16], [-13, -22], [-39, -17], [4, -12], [48, 2], [0, -13], [-74, -34], [-73, 16], [-81, -9], [-42, 7], [-52, 3], [-4, 26], [52, 13], [-14, 40], [17, 4], [74, -24], [-38, 35], [-45, 11], [23, 22], [49, 13], [8, 19], [-39, 22], [-12, 28], [76, -2], [22, -6], [43, 20], [-62, 7], [-98, -4], [-49, 19], [-23, 22], [-32, 17], [-6, 19]], [[3701, 9589], [93, 34], [97, -3], [36, 21], [98, 5], [222, -7], [174, -44], [-52, -21], [-106, -3], [-150, -5], [14, -10], [99, 6], [83, -19], [54, 17], [23, -20], [-30, -32], [71, 20], [135, 22], [83, -11], [15, -24], [-113, -39], [-16, -13], [-88, -10], [64, -2], [-32, -41], [-23, -36], [1, -62], [33, -36], [-43, -2], [-46, -18], [52, -29], [6, -47], [-30, -6], [36, -47], [-61, -4], [32, -23], [-9, -20], [-39, -8], [-39, 0], [35, -38], [0, -25], [-55, 23], [-14, -15], [37, -13], [37, -34], [10, -45], [-49, -11], [-22, 22], [-34, 31], [10, -37], [-33, -29], [73, -3], [39, -3], [-75, -48], [-75, -44], [-81, -19], [-31, 0], [-29, -22], [-38, -58], [-60, -39], [-19, -3], [-37, -13], [-40, -13], [-24, -35], [0, -39], [-15, -36], [-45, -44], [11, -44], [-12, -46], [-14, -54], [-39, -3], [-41, 45], [-56, 0], [-27, 31], [-18, 54], [-49, 69], [-14, 36], [-3, 50], [-39, 51], [10, 41], [-18, 20], [27, 65], [42, 20], [11, 24], [6, 43], [-32, -20], [-15, -8], [-25, -8], [-34, 18], [-2, 38], [11, 30], [25, 0], [57, -14], [-48, 35], [-24, 19], [-28, -8], [-23, 14], [31, 52], [-17, 20], [-22, 39], [-34, 59], [-35, 21], [0, 23], [-74, 33], [-59, 4], [-74, -2], [-68, -4], [-32, 17], [-49, 35], [73, 18], [56, 3], [-119, 14], [-62, 23], [3, 21], [106, 27], [101, 27], [11, 20], [-75, 20], [24, 22], [97, 39], [40, 6], [-12, 25], [66, 14], [86, 9], [85, 1], [30, -18], [74, 31], [66, -21], [39, -4], [58, -18], [-66, 30], [4, 23]], [[2039, 9088], [37, 2], [21, -12], [-24, -37], [-44, 39], [10, 8]], [[7288, 6689], [9, -3], [29, -29], [8, -12], [9, -7], [9, -11], [20, -1], [62, -7], [4, 6], [5, 4], [18, 1], [67, -2], [29, -3], [33, -11], [9, 2], [21, 12], [13, 13], [9, 14], [3, 1], [2, -4], [1, -8], [-1, -10], [-12, -25], [0, -8], [4, -18], [2, -16], [8, -12], [0, -5], [-3, -7], [-13, -18], [-12, -23], [-18, -18], [-29, -9], [-10, -5], [-18, -14], [-12, -2], [-18, 0], [-7, -3], [-7, -6], [-5, -8], [-4, -14], [-5, -38], [3, -20], [1, -40], [7, -11], [7, -14], [4, -16], [3, -19]], [[7613, 6841], [10, 1], [13, -4], [25, -26], [12, -6], [19, -31], [6, -26], [3, -22], [16, -46], [12, -34], [10, -39], [11, -80], [3, -27], [1, -56], [2, -20], [4, -18], [8, -18], [7, -10], [10, -6], [4, -4], [2, -7], [0, -8], [-1, -12], [-9, -39], [-1, -9], [2, -11], [22, -70], [1, -3], [4, 3]], [[7809, 6213], [1, -23]], [[7810, 6190], [-24, -36], [-6, -20]], [[7780, 6134], [12, -17]], [[7792, 6117], [1, -6], [0, -6], [1, 0], [14, -3], [10, 1], [20, 10], [-9, -27], [-3, -47], [-8, -31], [0, -10], [2, -9], [1, 0], [15, 16]], [[7836, 6005], [8, -9], [16, 1], [6, 20], [21, -4], [21, -45], [2, -55], [22, -49]], [[7932, 5864], [0, -13], [6, -12], [3, -19], [-1, -31], [3, -20], [-1, -28], [1, -38], [-1, -15], [-4, -6], [-4, -3], [-8, -15], [-9, -9], [-2, -12], [4, -36], [3, -13], [10, -24], [22, -46]], [[7508, 7646], [-3, -13], [-13, -14], [-10, -5], [-9, 1], [-59, 32], [-15, 11], [-31, 8], [-25, -15], [-15, 17], [-6, 9], [-2, 6], [-2, 26], [4, 7], [7, 7], [4, 8], [-4, 10], [-14, 16], [-24, 23], [-10, 7], [-10, 4], [-38, 7], [-52, 22], [-36, 61], [-7, 14], [-11, 32], [-7, 11], [-21, 22], [-8, 11], [-5, 11], [-3, 9], [-1, 7], [-13, 22], [-3, 3], [-8, 6], [-7, 6], [-4, 3], [-2, 2], [-5, 5], [-2, 3], [-9, 20], [1, 4], [6, 8], [9, 7], [17, 10], [6, 6], [4, 9], [3, 10], [-1, 11], [-2, 13], [-4, 12], [-5, 9], [-17, 13], [-22, 11], [-39, 11], [-33, 16], [-40, 5], [-5, 2], [-8, 4], [-5, 2], [1, 4], [1, 5], [6, 11], [3, 12], [5, 13], [5, 23], [5, 9], [5, 5], [13, 12], [0, 7], [-1, 6], [2, 9], [-1, 14], [-2, 8], [-6, 9], [-3, 2], [-6, 6], [-6, 6], [-3, 3], [-1, -1], [-1, -1], [-10, 7], [-6, 13], [-16, 16], [-11, 18], [-27, 21], [-16, 12], [-8, 11], [-3, 12], [4, 13], [7, 16], [4, 24], [2, 5], [1, 6], [-10, 27], [-2, 14], [-3, 4], [-1, 8], [11, 35], [2, 11], [0, 9], [14, 15], [9, 7], [10, 2], [33, -1], [22, 12], [26, -3], [25, -5], [18, 1]], [[1532, 8152], [18, -28], [8, -9], [10, -2], [23, 3], [14, -5], [7, 0], [17, 10], [31, -2], [20, 5], [9, 0], [14, -13], [9, -2], [16, 4], [8, 6], [8, 14], [1, 8], [-2, 17], [3, 23], [1, 21], [2, 15], [5, 8], [5, 3], [4, 9], [6, 5], [9, 3], [28, -1], [10, 4], [4, 2], [7, 4], [8, 4], [5, 2], [5, 2], [8, 4], [4, 3], [14, 13], [9, 2], [7, -2], [4, -7], [14, 2], [-1, 30], [-3, 17], [-23, 21], [-3, 5], [0, 8], [-2, 5], [-17, 17], [-3, 18], [-5, 4], [-3, 0], [-4, 0], [-3, 0], [-9, 1], [-8, 2], [-4, 0], [-49, -12], [-10, 2], [-27, 10], [-13, 6], [-26, -6], [-19, 2], [-15, 7], [-7, 6], [-7, 11], [-22, 11], [-39, 15], [-7, 6], [-1, 9], [2, 20], [-3, 10], [-17, 38], [-11, 15], [-4, 12], [-8, 11], [-7, 15], [-8, 6], [-24, 14], [-21, 13], [-31, 18], [-17, 6], [-7, 8], [-2, 9], [4, 17], [-5, 7], [-21, 15], [-7, 9], [-9, 18], [-5, 5], [-6, 3], [-13, 6], [-40, -9], [-10, 0], [-10, 3], [-13, 9], [-10, 13], [-5, 17], [-1, 7], [7, 15], [-2, 14], [-17, 14], [-13, 23]], [[5228, 7669], [9, -4], [24, 9], [36, 28], [27, 8], [10, 13], [3, -2], [6, -2], [4, -2], [4, -2], [9, -7], [10, -7], [4, -3], [34, -19], [8, -1], [18, 9], [8, -1], [26, -11], [17, -16]], [[5485, 7659], [10, -7], [24, 7]], [[5519, 7659], [6, -4], [3, -8], [-3, -27], [1, -22], [-2, -28], [-3, -13], [0, -4], [1, -4], [2, -5], [3, -10], [2, -6]], [[5529, 7528], [3, -4], [4, -7], [2, -4], [3, -1], [8, -2], [7, -3], [3, -1], [11, -18], [6, -3], [17, 5], [5, -4]], [[5598, 7486], [17, -16], [8, 13], [7, -7], [-6, -10], [5, -9]], [[5629, 7457], [8, -23], [11, 4], [21, -9], [41, -3], [13, 15], [33, 12]], [[5756, 7453], [1, -1], [1, -1], [2, 2], [6, 4], [6, 4], [3, 1], [4, 4], [-1, 5], [-4, 14], [3, 26], [1, 4], [4, 6], [1, 4], [4, -2], [6, -4], [3, -3]], [[5796, 7516], [13, 9], [13, -9]], [[3470, 4204], [-10, -2], [-19, -18], [-6, -10], [-2, -12], [9, -43], [4, -13], [0, -7], [-2, -7], [-12, -18], [-3, -8], [-1, -15], [-11, -29], [-6, -8], [-4, -10], [-3, -30], [-4, -23], [7, -19], [-1, -10], [-2, -5], [-5, -11], [-4, -11], [-2, -5]], [[3393, 3890], [-3, -3], [-2, -3], [-4, -5]], [[3384, 3879], [8, -31]], [[3392, 3848], [0, -21], [-2, -55]], [[3390, 3772], [-1, -4], [4, -21], [0, -15], [14, -49], [4, -19], [2, -16], [0, -17], [-4, -19], [-9, -16], [-1, -9], [0, -5], [0, -5]], [[3399, 3577], [-2, -6], [-5, -16], [-7, -20], [-7, -20], [-5, -16], [-2, -6]], [[3371, 3493], [-6, -27], [-1, -20], [-5, -21], [-5, -40], [-4, -7], [-4, -12], [-2, -17], [-1, -56], [-12, -31], [-9, -14], [-5, -14], [-3, -15], [-1, -16], [0, -12], [2, -13], [4, -13], [7, -12], [11, -14], [18, -18], [15, -10], [7, 0]], [[5729, 4347], [-9, 5], [-8, 26], [2, 25], [-7, 20], [-1, 16], [2, 35], [4, 12], [8, 8], [4, 19], [10, 27], [8, 6], [3, 7], [2, 7], [-1, 15], [4, 36], [0, 21], [-3, 56], [2, 20], [-1, 13], [-4, 15], [-12, 23], [-7, 15], [-3, 18], [-1, 8], [-1, 19], [-1, 19], [-1, 9], [0, 8], [-1, 19], [0, 23], [-1, 19], [0, 8], [-3, 19], [-4, 13], [-2, 14], [1, 26], [-3, 23], [-13, 10], [-18, 14], [-43, 65], [-11, 10], [-17, 0], [-41, -12], [-10, -4], [-21, -15], [-12, -21], [-6, -16], [-4, -18], [-4, -27], [-17, -42], [-2, -5]], [[5486, 4958], [-18, -27], [-13, -28], [-12, -54], [1, -46], [-7, -18], [-16, -27], [-16, -35]], [[5405, 4723], [-11, 0], [-4, -2], [-6, -14], [-6, -5], [-4, -22], [-2, -1], [-1, -5]], [[5371, 4674], [-10, -7], [-8, 1], [-11, -7]], [[7999, 7999], [1, 6], [0, 4], [-7, 7], [-17, -21], [-4, -3], [-3, 1], [-22, 0], [-13, 7], [-10, 21], [-4, 27], [1, 10], [15, 44], [2, 21], [12, 14], [-1, 5], [-6, 4], [-3, 7], [4, 7], [9, 6], [13, 6], [6, 9], [10, 2], [9, 7], [10, 18], [15, 16], [9, 7], [15, 6], [15, 16], [30, 12], [21, 20], [14, 9], [8, 13], [24, 22], [26, 31], [14, 13], [15, 1], [19, -8], [19, -14], [17, -19], [9, -7], [15, -5], [9, 5], [20, 16], [4, 1], [8, 3], [11, 4], [9, 4], [4, 1], [4, 3], [7, 5], [4, 2], [65, 0], [40, 10], [37, 16], [60, 13], [20, 12], [15, 23], [5, 13], [1, 15], [-3, 16], [-13, 43], [-30, 6], [-10, 7], [-30, 28], [-37, 25], [-20, 19], [-8, 12], [-9, 35], [-14, 36], [-13, 47], [-2, 16], [1, 14], [15, 29], [10, 31], [7, 12], [8, 8], [12, 25], [12, 11], [12, 14], [1, 11], [32, 12], [6, 6], [4, 6], [0, 8], [-5, 18], [-5, 28], [-5, 13], [-7, 12], [-19, 2], [-38, 18], [-52, 14]], [[7521, 6905], [10, 1], [51, -12], [24, 12], [9, 13], [6, 6], [8, 1], [9, -4], [11, -11], [17, -29], [11, -12], [18, -24], [11, -26], [20, -27], [6, -15], [4, -18], [7, -18], [-2, -12], [6, -26], [2, -19], [3, -61], [4, -45], [1, -4], [2, -9], [1, -4], [1, -6], [2, -13], [3, -13], [1, -6], [4, -15], [5, -5], [5, 9], [3, 14], [3, 9], [3, -5], [1, -12], [-2, -31], [1, -23], [1, -8], [3, -1], [11, 2], [18, 15], [5, 0], [1, -6], [1, -11], [2, -6], [13, 9], [9, 2], [3, 5], [3, 11], [-1, 35], [2, 8], [3, 3], [9, 26], [2, 14], [7, 10], [2, 9], [0, 10], [13, 0], [12, 6], [10, 0], [12, 6], [8, 0], [2, 7], [20, 30], [27, 21], [8, 12], [12, 24], [2, 9], [5, 7], [8, 4], [17, 4], [22, 1], [16, -6], [10, -11], [9, -19], [14, -1], [5, -8], [7, -22], [4, -4], [5, 0], [4, -2], [1, -9], [5, -5], [14, 24], [8, 10], [0, 8], [3, 5], [7, 16], [7, 0], [7, -4], [16, -28], [8, -9], [9, -3], [8, 2], [12, 12], [7, 20], [10, 16], [8, 6], [8, 16], [11, 16], [7, 24], [5, 11], [8, 7], [9, 3], [11, -3], [24, -29], [9, -6], [26, -5]], [[5922, 5014], [-1, 9], [-8, 49], [-2, 3], [-5, 6], [-4, 4], [-4, 4], [-3, 3], [-2, 1], [0, 2], [3, 12], [1, 8], [-3, 7], [-6, 4], [-6, 1], [-3, -2], [-5, -2], [-3, -2], [1, 6], [2, 6], [-1, 7], [0, 8], [0, 15], [3, 16], [5, 11], [8, 10], [-8, 23], [-4, 18], [0, 19], [4, 41], [-2, 28], [-14, 50], [-10, 23], [-6, 17], [-4, 18], [-4, 39], [-1, 18], [2, 17], [5, 11], [8, 3], [9, -2], [5, -1], [6, 3], [7, 10], [11, 22], [2, 10], [-2, 12], [1, 8], [11, 31], [2, 19], [2, 29], [1, 35], [-1, 20], [-3, 20], [-9, 43], [-2, 19], [7, 65], [2, 17], [4, 15], [7, 13], [10, 11], [7, 13], [6, 16], [3, 22], [0, 20], [-1, 15], [-7, 34], [-6, 34], [-7, 2], [-6, -3], [-8, -8], [-15, -26], [-17, -35], [-5, -6], [-4, -1], [-5, 3], [-4, 7], [-4, 11], [-6, 34], [-4, 45], [6, 14], [1, 6], [-6, 19], [-1, 7], [8, 22], [13, 31], [10, 27], [9, 19], [6, 10], [13, 6], [4, 7], [4, 12], [3, 16], [1, 17], [0, 18], [0, 32], [-1, 18], [-7, 24], [-2, 16], [5, 15], [1, 7], [-2, 5], [-15, 2], [-10, 20], [-13, 34], [-6, 11], [-4, 12], [-3, 14], [0, 15], [-2, 13], [1, 10], [10, 47], [2, 20], [0, 26], [-2, 49], [-4, 29]], [[3009, 4148], [5, 30], [-2, 28], [-3, 11], [-5, 10], [-8, 9], [-17, 14], [-13, 4], [-10, 22], [-8, 29], [-3, 17], [-5, 41], [0, 10], [10, 5], [1, 9], [-1, 12], [-6, 23], [-1, 12], [1, 18], [-4, 10], [-9, 36], [0, 16], [4, 13], [-4, 6], [-5, 18], [-7, 13], [-3, 21], [-6, 16], [1, 5], [2, 4], [-1, 27], [2, 16], [4, 10], [5, 5], [13, 18], [6, 28], [3, 5], [5, 4], [8, 30], [4, 15], [4, 7], [2, 20], [3, 7], [4, 3], [11, 1], [10, 4], [7, -1], [9, -17], [8, -7], [10, -5], [8, 4], [7, -2], [7, -8], [7, -14], [6, 2], [4, 8], [7, 28], [8, 10], [25, 8], [3, 5], [1, 6], [7, 13], [8, 9], [15, 3], [8, 9], [15, 0], [7, -6], [4, 0], [5, -5], [24, -39], [16, -19], [23, -17], [12, 10], [9, 4], [17, -4], [9, 5], [24, 23], [19, 8], [3, 4], [5, 1], [26, -12], [5, 6], [6, 1], [15, 31], [8, 9], [22, 1], [16, 19], [8, 7], [9, 3], [10, -10], [9, -2], [8, -15], [9, 7], [11, 19], [9, 8], [29, 22], [18, 19], [22, 34], [12, 30]], [[1924, 7484], [-6, -3], [-4, 5], [-3, 3], [-3, 3], [-3, 2], [-4, 2], [-2, 8], [-1, 12], [6, 40], [0, 11], [-1, 4], [-1, 4], [-1, 4], [-3, 8], [-1, 5], [-2, 4], [-3, 4], [-1, 7], [2, 9], [6, 11], [13, 16], [18, 18], [8, 3], [11, -9], [25, -2], [3, -2], [7, -3], [7, -3], [3, -1], [3, -1], [4, -2], [3, 0], [27, 11], [3, 4], [7, 7], [3, 4], [5, 1], [11, 1], [14, 2], [11, 1], [5, 1], [27, -6], [15, 4], [10, -2], [21, -23], [9, -5], [3, -1], [5, -1], [3, -1], [1, -4], [2, -4], [8, -10], [2, -7], [3, -12], [1, -7], [3, -6], [2, -6], [1, -19], [6, -44], [0, -15], [-3, -10], [-5, -9], [1, -6], [2, -6], [2, -2], [2, -3], [3, -4], [7, -6], [3, -3], [8, -2], [2, -3], [4, -4], [2, -3], [-1, -4], [0, -8], [-1, -4], [1, -6], [4, -4], [8, -11], [7, -11], [3, -5], [0, -2]], [[2261, 7388], [7, -7], [10, -4], [3, 2], [6, 0], [10, 1], [8, -8], [7, -4], [1, -5], [3, -2], [4, -1]], [[2320, 7360], [2, -6], [1, -9], [0, -5], [5, -10], [2, -9], [0, -13], [2, -2], [2, -6], [3, -16], [1, -10], [-1, -10], [2, -10]], [[2339, 7254], [0, -5], [5, -8], [4, -8], [4, -11]], [[2352, 7222], [7, -8], [3, 0], [1, -8], [-4, -10], [2, -5], [4, -12], [7, -5]], [[2372, 7174], [39, 10], [6, -3], [4, -13], [8, -9], [7, -12], [7, -2], [16, 3], [16, -2], [13, 9], [2, 0], [4, 0], [2, 0], [-1, -7], [-1, -5]], [[2494, 7143], [-3, -8], [-2, -10], [4, -8], [6, -8], [3, 0], [7, -13], [3, -1], [2, -14], [-1, -8], [4, -14], [3, 2], [5, -9]], [[2525, 7052], [-1, -5], [0, -9], [-4, -5], [-6, -6]], [[2514, 7027], [-1, -5], [-2, -8], [-2, -13]], [[2509, 7001], [-1, 0], [1, -3], [0, -2]], [[2509, 6996], [-3, -8], [-5, -5], [-1, -9], [-4, -8], [0, -16], [-3, -5]], [[2493, 6945], [-1, -5], [-5, -4], [0, -8], [-3, -15], [-4, -3], [-5, -7], [-3, -12], [-6, -19], [0, -13], [3, -15], [-1, -11]], [[2468, 6833], [2, -3], [-3, -8], [4, -11], [-1, -7], [4, -10], [-4, -6], [-2, -10], [-5, -9], [-2, -11], [-3, -14], [-3, -6], [1, -14]], [[2456, 6724], [1, -13], [6, -9], [6, -21], [3, -8], [6, -4], [20, -9], [3, -5], [2, -9], [13, -27]], [[8227, 6652], [0, 2]], [[76, 308], [3, 2]], [[79, 310], [8, 5], [10, 5], [8, 5], [4, 3]], [[109, 328], [4, 0], [3, -1]], [[9951, 307], [-9951, -13]], [[3107, 1910], [-30, 7]], [[0, 4080], [9981, -14]], [[9983, 4090], [-9983, 17]], [[0, 4107], [0, -27]], [[3011, 6104], [22, 0]], [[5941, 6957], [2, -10]], [[5943, 6947], [1, -4]], [[5895, 6949], [14, 3]], [[5909, 6952], [2, 0]], [[0, 8609], [0, 221]], [[2318, 8870], [8, -10], [17, -22]], [[9964, 8949], [-9964, 23]], [[0, 8972], [9999, -38]], [[0, 8934], [0, 38]], [[2526, 8158], [28, -21], [20, -27]], [[3052, 7426], [-16, -36], [-3, -9]], [[3001, 7273], [-39, -16], [-17, -1]], [[2879, 7115], [-17, 8], [18, -17]], [[2523, 6684], [-12, -8]], [[2511, 6676], [5, -16]], [[2301, 6437], [0, -1], [-10, -48]], [[2541, 5880], [8, -7]], [[2549, 5873], [3, -2]], [[2676, 5607], [7, -30]], [[2831, 5513], [10, -16], [10, -16]], [[3565, 5230], [9, 3], [7, -31]], [[2711, 5460], [-12, 0]], [[2620, 5597], [-8, 8]], [[2569, 5738], [-8, 5]], [[2505, 5751], [-8, 12], [-14, 9]], [[1551, 7428], [7, 100]], [[2164, 8839], [-9, -20], [-51, 8]], [[1972, 8820], [-82, -10], [-38, -3]], [[2222, 8985], [-69, 42], [0, 18]], [[1502, 8992], [9, 18], [22, 46]], [[1654, 8989], [0, -23], [-73, -28]], [[6473, 7602], [-28, -2], [-24, 13]], [[0, 8609], [9963, -25]], [[9306, 8285], [-62, -14], [-43, -6]], [[8763, 7443], [-17, -33], [-38, -32]], [[8475, 7158], [7, 29], [-2, 10]], [[8077, 6188], [-10, -59]], [[7269, 5906], [-25, -20], [-13, -3]], [[6475, 5924], [-20, -14], [-6, -25]], [[6390, 5448], [-17, -70], [-24, -82]], [[5638, 3115], [-11, 3], [-29, -22]], [[5203, 7061], [11, -13], [19, 4]], [[5966, 8660], [4, -82]], [[6031, 8573], [-17, 25], [18, 20]], [[6213, 8741], [14, 33], [-21, 35]], [[6337, 8750], [58, 27], [97, 48]], [[6763, 8863], [39, -17], [100, -64]], [[9608, 8859], [53, 6], [49, -50]], [[9960, 8855], [-9960, -25]], [[2468, 9484], [47, -12], [46, -11]], [[9969, 8931], [-9969, 3]], [[0, 8972], [9999, 0]], [[7960, 7933], [-1, 7], [15, 14], [6, 11], [8, 8], [12, 18], [10, 23], [18, 42], [4, 29], [9, 10], [12, -1], [-7, -40], [-1, -15], [-5, -21], [-4, -29], [-3, -11], [-6, 9], [-11, -19], [5, -3], [-1, -10], [-8, -4], [-8, -18], [-10, -13], [-14, -9], [-19, -5], [-15, -16], [-10, -15], [-13, -14], [-12, -3], [-15, 2], [-15, 6], [-3, 8], [6, 6], [15, 0], [19, 8], [11, 16], [14, 13], [8, 6], [9, 10]], [[2251, 7995], [28, 23], [4, -16], [4, -35], [35, -112], [4, -40], [-13, -13], [-5, 17], [-9, 41], [-27, 38], [-29, 58], [8, 39]], [[1805, 8442], [-22, 32], [-7, 13], [6, -2], [39, -18], [30, -21], [44, 22], [20, 27], [24, 9], [22, -12], [9, -3], [-1, -7], [-28, -10], [-32, -9], [-38, -44], [-28, -27], [-47, -11], [-31, -1], [-45, 25], [-8, 3], [-1, 9], [6, 2], [38, -13], [32, 20], [18, 16]], [[2882, 7443], [2, -22], [-21, -18], [-23, 4], [-22, 2]], [[2818, 7409], [-14, -7], [-9, -3], [-11, 6], [8, 19], [9, 6], [20, 8], [23, 8], [12, -11], [8, 13], [9, 7], [6, -4], [3, -8]], [[2691, 7337], [15, -3], [21, 17], [12, 16], [8, 2], [15, -5], [8, 8], [13, 7], [24, 7]], [[2807, 7386], [1, -5], [-24, -33]], [[2784, 7348], [-21, -16]], [[2763, 7332], [-14, -8], [-17, -15], [-20, -8], [-14, 3], [-17, 12]], [[2681, 7316], [10, 21]], [[2511, 7666], [11, 22], [16, 9], [6, 14], [6, 7], [26, -11], [19, -2], [7, -7], [9, -32], [31, -8], [-5, -16], [11, -16], [-4, -21], [11, -7], [-5, -19]], [[2650, 7579], [-9, 2], [-6, 16], [-27, -5], [-25, -12], [-19, 21], [-16, 7], [9, 29], [-25, -18], [-22, -18], [-21, -14]], [[2489, 7587], [-17, 19]], [[2472, 7606], [-28, -12]], [[2444, 7594], [0, 8], [19, 24], [20, 22], [28, 18]], [[5946, 4941], [-14, -25], [-9, -25], [11, -19], [-16, -14], [-3, 6], [-16, -3], [-13, -12], [-7, 21], [5, 39], [1, 33]], [[5885, 4942], [-2, 22], [13, 32], [17, 8], [12, 14], [15, -11], [8, -25], [-2, -41]], [[5828, 8401], [28, 30], [47, -36], [12, -27], [-4, -9], [-6, 3], [0, -18], [-25, 2], [-5, -18], [-12, 0], [1, 13], [-16, 27], [-1, 11], [-19, 22]], [[7193, 7596], [8, -5], [-10, -16], [-13, -4], [-34, 6], [-44, 6], [-5, 9], [-1, -11], [-14, -4], [-4, -16], [-13, -6], [-5, -56], [-19, 34], [0, 11], [9, 12], [-2, 9], [9, 1], [2, 13], [24, 21], [35, -2], [27, -7], [19, 0], [12, -10], [3, 10], [16, 5]], [[5855, 4523], [-9, 4], [2, 22], [-8, 15], [-3, 30], [-17, 30], [-10, 40], [5, 24], [-7, 31], [5, 89], [10, -54], [-1, -26], [5, -9], [-1, -23], [5, -22], [-6, -21], [22, -38], [3, -34], [16, -66], [-5, -4], [-6, 12]], [[5979, 4206], [-1, -6], [-10, 21], [-5, -14], [-4, 12], [0, 21], [-6, 16], [0, 24], [-8, 41], [8, 31], [-2, 67], [-10, 36], [3, 17], [14, -30], [3, -58], [9, -21], [-7, -54], [5, -71], [5, -2], [6, -30]], [[6630, 7527], [2, 22], [12, 2], [9, -13], [1, 34], [6, -3], [9, -9], [0, -11], [-4, -5], [-3, -16], [7, 3], [4, -44]], [[6673, 7487], [-6, -29], [-7, -5], [-20, 11], [8, 35], [0, 11], [-11, 10], [-1, -15], [0, -20], [-12, -30], [-6, 15], [0, 23], [12, 34]], [[5388, 8288], [0, -6], [-2, -10], [-18, -17], [-12, -5], [-10, 0], [2, 15], [-1, 5], [5, 4], [9, 3], [5, 7], [11, 12], [11, -8]], [[2758, 6488], [-6, 2], [0, 13], [6, -2], [0, -13]], [[2643, 5619], [-12, 1], [-14, 19], [-3, 22], [9, 2], [15, -23], [5, -21]], [[6031, 5658], [-3, 10], [6, 11], [8, -4], [-1, -19], [-4, -6], [-6, 8]], [[3072, 4104], [-9, 11], [-7, 10], [3, 4], [0, 7], [-1, 11], [8, -4], [17, -26], [1, -1], [6, -25], [-4, -8], [-3, -2], [-2, 4], [-3, 3], [-2, 10], [-4, 6]], [[2239, 7951], [-4, -1], [-8, 2], [-17, 7], [-5, 14], [8, 12], [-3, 9], [1, 2], [2, 8], [2, 8], [7, -1], [-1, -6], [-4, -10], [9, 0], [-2, -6], [-4, -21], [11, -8], [8, -1], [0, -8]], [[5991, 8459], [10, -31], [9, -25], [-8, -14], [-21, -4], [-13, 10], [9, -1], [11, -1], [-12, 17], [-8, 9], [-11, 17], [-6, 20], [1, 4], [10, -4], [-1, 13], [6, -9], [7, -9], [4, 3], [7, 4], [-9, 13], [-15, 15], [10, -1], [7, -4], [13, -22]], [[1884, 7296], [1, -11], [-1, -16], [-14, 15], [-1, 2], [-5, 26], [8, -11], [5, -5], [6, 5], [1, -5]], [[1729, 8678], [-6, -20], [-4, -5], [-45, -2], [0, -4], [2, -7], [-1, -9], [-46, -27], [0, 6], [11, 21], [-3, 4], [-42, -23], [-18, 8], [1, 8], [24, 26], [-1, 6], [-72, 9], [1, 5], [151, 46], [3, -6], [-22, -22], [71, 5], [0, -8], [-4, -11]], [[1954, 8279], [-40, -26], [-3, 6], [1, 5], [57, 44], [71, -13], [0, -2], [-86, -14]], [[2169, 8222], [10, -8], [-12, -29], [1, -6], [-35, -50], [-2, 4], [4, 16], [1, 1], [2, -8], [12, 20], [-6, 20], [0, 10], [19, 30], [6, 0]], [[2710, 7387], [-1, 23], [-5, 32], [-11, 6], [-17, -25], [-5, 1], [-2, 14], [15, 22], [3, 25], [-2, 25], [-21, 22], [-23, 11]], [[2641, 7543], [4, 8]], [[2645, 7551], [1, -1], [25, 5], [-14, 23]], [[2657, 7578], [5, 8], [6, -13], [21, -7], [21, 0], [22, -6], [25, -11], [9, -17], [18, -42], [-9, -19], [-22, 8], [-14, 34], [3, -35], [-13, -31], [1, -26], [-2, -16], [-18, -18]], [[2641, 7543], [-4, -21], [-6, -6], [-5, -27], [-3, 19], [-11, -14], [-7, -18], [-7, -28], [-2, -23], [10, -35], [-1, -36], [-11, -27], [-6, -8]], [[2588, 7319], [-8, -6], [-9, 0], [-3, 3]], [[2568, 7316], [-7, 30], [0, 14]], [[2561, 7360], [1, 14], [-4, 27], [5, 31], [7, 39], [14, 43], [-4, 0], [-21, -36], [-3, 6], [11, 20]], [[2567, 7504], [16, 36]], [[2583, 7540], [19, 5], [22, 11], [21, -5]], [[9999, 4107], [-9999, 0]], [[5941, 4947], [5, -6]], [[5946, 4941], [101, -114], [1, -32], [40, -55]], [[6119, 4426], [-22, -32], [-30, -21], [-17, 1], [-10, -17], [-19, -2], [-7, -7], [-34, 16], [-21, -4]], [[5959, 4360], [-7, 75], [-10, 26], [-5, 15], [-28, 11]], [[5909, 4487], [-15, 16], [-18, 10], [-11, 9], [-12, 14]], [[5853, 4536], [-15, 70], [-16, 31], [-5, 33], [2, 28], [-5, 52]], [[5814, 4750], [12, 2], [10, 20], [11, 29], [7, 12], [-1, 18], [-6, 13], [-1, 21]], [[5846, 4865], [8, 7], [1, 33], [-11, 31]], [[5844, 4936], [10, 7], [31, -1]], [[5885, 4942], [56, 5]], [[4759, 6536], [0, -4], [-1, -11]], [[4758, 6521], [0, -84], [-91, 3], [1, -142], [-26, -5], [-7, -29], [5, -80], [-108, 1], [-6, -19]], [[4527, 6190], [0, -1], [63, 5], [3, 20], [12, 25], [9, 77], [38, 59], [13, 71], [9, 4], [9, 43], [23, 6], [10, -7], [13, 0], [9, 12], [17, 2], [0, 30], [4, 0]], [[1374, 8044], [15, 27], [0, 35], [-48, 35], [-28, 63], [-17, 40], [-26, 25], [-19, 23], [-14, 29], [-28, -18], [-27, -31], [-25, 36], [-19, 25], [-27, 15], [-28, 2], [0, 316], [1, 206]], [[3135, 7507], [-18, 31], [0, 76], [-13, 16], [-18, -9], [-10, 14], [-21, -42], [-8, -43], [-10, -25], [-12, -9]], [[3025, 7516], [-9, -3], [-3, -13]], [[3013, 7500], [-51, 0]], [[2962, 7500], [-42, -1], [-12, -10]], [[2908, 7489], [-30, -40], [-3, -4], [-9, -22], [-26, 0], [-27, 0], [-12, -9], [4, -11]], [[2805, 7403], [2, -17]], [[2807, 7386], [0, -5], [-36, -28], [-29, -9], [-32, -29], [-7, 0], [-10, 8], [-3, 8], [1, 6]], [[2691, 7337], [6, 19], [13, 31]], [[2710, 7387], [8, 33], [-5, 48], [-6, 51], [-29, 26], [3, 10], [-4, 6], [-8, 0], [-5, 9], [-2, 13], [-5, -5]], [[2657, 7578], [-7, 1]], [[2650, 7579], [1, 6]], [[2651, 7585], [-6, 5], [-3, 15], [-21, 18], [-23, 18], [-27, 22], [-26, 20], [-25, -16], [-9, -1]], [[2511, 7666], [-34, 15], [-23, -7], [-27, 17], [-28, 9], [-19, 3], [-9, 10], [-5, 30], [-9, 0], [-1, -22], [-57, 0]], [[2299, 7721], [-95, 0], [-94, 0], [-84, 0], [-83, 0], [-82, 0], [-85, 0]], [[1776, 7721], [-27, 0]], [[1749, 7721], [-82, 0], [-79, 0]], [[2301, 6437], [-10, -2], [-20, 12], [-22, 17], [-8, 26], [-6, 39], [-16, 32], [-10, 33], [-14, 38], [-19, 22], [-23, -1], [-17, -44], [-23, 16], [-15, 17], [-7, 31], [-9, 29], [-16, 24], [-15, 18], [-10, 19]], [[2041, 6763], [-48, 0], [0, -22], [-22, 0]], [[1971, 6741], [-55, -1], [-64, 39], [-41, 27], [2, 11]], [[1813, 6817], [-35, -6], [-32, -4]], [[7426, 7733], [-21, -37], [-23, -5], [-2, -55], [-15, -26], [-55, 19], [-20, -100], [-14, -12], [-55, -22], [25, -97], [-19, -14], [2, -32]], [[7229, 7352], [-17, 8], [-14, 20], [-42, 6], [-46, 1], [-10, -6], [-39, 24], [-16, -12], [-4, -33], [-46, 20], [-18, -8], [-7, -25]], [[6970, 7347], [-15, -10], [-37, -39], [-12, -39], [-11, -1], [-7, 27], [-36, 1], [-5, 46], [-14, 0], [2, 56], [-33, 41], [-48, -5], [-32, -8], [-27, 50], [-22, 21]], [[6673, 7487], [-43, 40]], [[6630, 7527], [-6, 5], [-71, -33], [1, -205]], [[6554, 7294], [-14, -3], [-20, 44], [-18, 16], [-32, -12], [-12, -18]], [[6363, 7577], [-14, 9], [3, 29], [-18, 37], [-20, -2], [-24, 38], [16, 42], [-8, 11], [22, 61], [29, -32], [3, 41], [58, 60], [43, 1], [61, -38], [33, -22], [30, 23], [44, 1], [35, -29], [8, 17], [39, -3], [7, 27], [-45, 38], [27, 27], [-5, 15], [26, 15], [-20, 38], [13, 19], [104, 19], [13, 14], [70, 20], [25, 23], [50, -12], [9, -57], [29, 13], [35, -19], [-2, -30], [27, 3], [69, 52], [-10, -17], [35, -43], [62, -141], [15, 29], [39, -32], [39, 14], [16, -10], [13, -32], [20, -10], [11, -24], [36, 7], [15, -34]], [[6970, 7347], [9, -5], [-24, -36], [21, -21], [20, 14], [33, -29], [-36, -40], [-21, 5]], [[6972, 7235], [-12, -1], [-4, 15], [6, 26], [-37, -13], [-9, -36], [-13, -30], [-23, 2], [-7, -24], [20, -14], [6, -41], [-16, -56]], [[6883, 7063], [-20, 12], [-16, 0]], [[6847, 7075], [1, 34], [-37, 24], [-29, 27], [-18, 26], [-32, 38], [-14, 58], [-9, 10], [-30, -3], [-11, 12], [-3, 44], [-37, 29], [-23, -32], [-24, -19], [4, -28], [-31, -1]], [[8917, 4493], [-1, 181], [0, 181]], [[8471, 4506], [2, -11], [1, -17]], [[8045, 5111], [5, -37], [19, -32], [18, 12], [18, -4], [16, 28], [13, 5], [26, -16], [23, 12], [14, 77], [11, 20], [10, 63], [32, 0], [24, -10]], [[3140, 1950], [-17, 1], [-30, 0], [0, 125]], [[3399, 3321], [-7, -44], [-7, -58], [0, -55], [-6, -12], [-2, -36]], [[3095, 2094], [-26, 9], [-67, 7], [-11, 33], [0, 41], [-18, -3], [-10, 20], [-3, 59], [22, 24], [9, 36], [-4, 28], [15, 47], [10, 74], [-3, 32], [12, 11], [-3, 21], [-13, 11], [10, 23], [-13, 21], [-6, 64], [11, 12], [-5, 67], [7, 57], [7, 50], [17, 20], [-9, 54], [0, 51], [21, 36], [-1, 47], [16, 54], [0, 51], [-7, 10], [-13, 96], [17, 57], [-2, 54], [10, 51], [18, 52], [20, 34], [-9, 22], [6, 18], [-1, 92], [30, 28], [10, 58], [-3, 14]], [[3136, 3737], [23, 50], [36, -14], [16, -40], [11, 45], [32, -3], [4, -11]], [[3258, 3764], [51, -91], [23, -9], [34, -41], [29, -21], [4, -25]], [[3399, 3577], [-28, -84]], [[3371, 3493], [28, -15], [32, -9], [22, 9], [25, 43], [4, 49]], [[3482, 3570], [14, 10], [14, -32], [-1, -44], [-23, -31], [-19, -22], [-31, -54], [-37, -76]], [[3067, 4023], [13, -38], [4, -40], [15, -23], [-9, -54], [15, -63], [11, -76], [20, 8]], [[3045, 3980], [14, 14], [8, 29]], [[5853, 4536], [-11, 6], [-37, -10], [-7, -6], [-8, -36], [6, -24], [-5, -66], [-3, -56], [7, -10], [19, -21], [8, 10], [2, -60], [-21, 1], [-11, 30], [-10, 24], [-22, 8], [-6, 29], [-17, -18], [-22, 8], [-10, 25], [-17, 5], [-13, -1], [-2, 17], [-9, 2]], [[5664, 4393], [-13, 3], [-17, -9], [-12, 2], [-7, -5], [1, 66], [-9, 20], [-2, 35], [4, 33], [-5, 22], [-1, 34], [-34, 0], [3, 20], [-14, 0], [-2, -10], [-17, -2], [-7, -32], [-4, -14], [-16, 8], [-9, -8], [-18, -5], [-11, 29], [-6, 18], [-8, 34], [-7, 41], [-82, 1]], [[5338, 4678], [7, 6], [1, 24], [4, 14], [10, 12]], [[5360, 4734], [8, -6], [9, 22], [15, -1], [2, -16], [11, -10]], [[5486, 4958], [3, 18], [1, 20], [5, 20], [-2, 31], [4, 49], [5, 35], [8, 30], [2, 33]], [[5512, 5194], [3, 39], [10, 28], [15, 18], [23, -19], [18, -20], [20, -6], [21, -11], [8, 34], [4, 4], [13, -5], [31, 27], [10, -11], [9, 1], [5, 14], [10, 4], [21, -5], [18, -2], [9, 6]], [[5760, 5290], [17, -46], [12, -6], [8, 9], [12, -4], [16, 12], [6, -24], [25, -37]], [[5856, 5194], [-2, -65], [11, -7], [-9, -20], [-10, -15], [-11, -28], [-6, -26], [-1, -45], [-7, -21], [0, -42]], [[5821, 4925], [-8, -15], [-1, -34], [-4, -4], [-2, -30]], [[5806, 4842], [7, -25], [1, -67]], [[6155, 4906], [-17, 46], [0, 202], [24, 63]], [[6162, 5217], [8, 18], [17, 1], [25, 39], [36, 2], [79, 167]], [[6327, 5444], [19, 46], [13, 35], [0, 29], [0, 56], [0, 22], [0, 1]], [[5941, 4947], [0, 59], [8, 22], [14, 37], [10, 40], [-13, 64], [-3, 28], [-13, 39]], [[5944, 5236], [17, 33], [19, 36]], [[5980, 5305], [14, -9], [0, -31], [10, -18], [19, 0], [35, -48], [9, 0], [7, 1], [6, -6], [18, -4], [8, 23], [26, 23], [11, -19], [19, 0]], [[5682, 5457], [-21, 24], [-10, 16], [-2, 17], [5, 23], [0, 23], [-16, 35], [-3, 23]], [[5635, 5618], [0, 14], [-10, 16], [-1, 33], [-5, 21], [-10, -3], [3, 20], [7, 23], [-3, 23], [9, 17], [-6, 13], [7, 35], [13, 41], [24, -4], [-1, 220]], [[5662, 6087], [0, 24], [32, 0], [0, 111]], [[5694, 6222], [112, 0], [107, 0], [110, 0]], [[6066, 5999], [-14, -31], [-20, -10], [-9, -17], [-3, -36], [-12, -82], [3, -22]], [[6011, 5801], [-4, -48], [-11, -55], [-17, -27], [-12, -43], [-3, -22], [-13, -16], [-8, -58], [0, -50]], [[5943, 5482], [0, 43], [-4, 1], [0, 28], [-3, 19], [-14, 22], [-4, 40], [4, 41], [-13, 4], [-2, -13], [-17, -2], [7, -17], [2, -33], [-15, -30], [-14, -41], [-14, -5], [-23, 32], [-11, -11], [-3, -16], [-14, -11], [-1, -11], [-28, 0], [-3, 11], [-20, 2], [-10, -10], [-8, 5], [-14, 33], [-5, 15], [-20, -8], [-8, -26], [-7, -49], [-10, -11], [-8, -6], [19, -21]], [[5635, 5618], [-18, -9], [-14, -23], [-20, -60], [-26, -26], [-27, 4], [-8, -5], [3, -20], [-15, -19], [-12, -22], [-34, -21], [-7, 12], [-5, 1], [-5, -14], [-23, -4]], [[5424, 5412], [4, 15], [-9, 38], [-3, 23], [-13, 10], [-16, 32], [6, 26], [13, -5], [8, 4], [15, -1], [-15, 51], [1, 36], [-2, 37], [-11, 36]], [[5402, 5714], [3, 26], [-18, 1], [0, 36], [-11, 21], [12, 73], [35, 52], [1, 72], [11, 113], [6, 24], [-11, 19], [-1, 18], [-10, 14], [-7, 87]], [[5412, 6270], [28, 30], [111, -106], [111, -107]], [[3008, 6095], [2, -31], [-2, -21], [-7, -9], [7, -17], [0, -15]], [[5631, 8017], [-51, 0], [-34, 6]], [[5590, 8065], [29, -9], [13, -9], [-3, -15], [2, -15]], [[8632, 7345], [-4, 10]], [[8628, 7355], [0, 28], [14, 1], [4, 66], [-7, 47], [24, 20], [33, -10], [19, 54], [9, 61], [11, 20], [15, 50], [-46, -16], [-24, -22], [-42, 0], [-12, 52], [-32, 40], [-49, 18], [-10, 54], [-10, 34], [-10, 24], [-17, 56], [-25, 21], [-41, 16], [-37, -1], [-35, -10], [-23, -28], [16, -13], [0, -31], [-15, -18], [-26, -59], [1, -24], [-39, -35], [-34, 21]], [[8240, 7771], [-33, -5], [-14, 19], [-17, 6], [-41, -39], [-36, -10], [-26, -13], [-35, 9], [-26, -1], [-16, 29], [-28, 26], [-27, 8], [-36, -8], [-26, -10], [-39, 23], [-6, 42], [-32, 14], [-26, 7], [-31, 23], [-28, -58], [11, -33], [-27, -38], [-40, 14], [-28, 2], [-19, 26], [-29, 1], [-24, 17], [-42, -27], [-53, -47], [-29, -10]], [[7437, 7738], [-11, -5]], [[6349, 7322], [-17, -22], [-4, -15], [-13, 4], [-19, 34], [-8, 2]], [[6288, 7325], [-17, 13], [-9, 23], [-25, 11], [-17, -9], [-5, 11], [-38, 26], [-41, 9], [-23, 10], [-4, -7]], [[6061, 7616], [1, 25], [14, 15], [27, 4], [5, 19], [-7, 30], [12, 30], [-1, 16], [-41, 18], [-16, -1], [-17, 26], [-21, -8], [-35, 19], [0, 11], [-10, 24], [-22, 3], [-2, 17], [7, 11], [-18, 32], [-29, -6], [-8, 3], [-7, -12], [-11, 2]], [[5882, 7894], [-6, 35], [-7, 19], [5, 5], [23, -2], [11, 12], [-8, 15], [-19, 10], [2, 10], [-12, 10], [-17, 36], [6, 15], [-3, 26], [-27, 14], [-15, -7], [-4, 14], [-29, 14]], [[5782, 8120], [-9, 32], [-2, 27], [-14, 13]], [[5757, 8192], [12, 18], [-8, 52], [20, 32], [-4, 9]], [[5779, 8360], [60, 71], [25, 32], [11, 29], [-41, 38], [11, 36], [-25, 41], [19, 48], [-33, 63], [26, 42], [-42, 37], [4, 39]], [[5794, 8836], [22, 5], [47, 22]], [[5928, 7553], [8, 14], [19, -12], [9, -2], [4, -11], [4, -2]], [[5972, 7540], [0, -5]], [[5932, 7547], [-4, 6]], [[5794, 8836], [11, 39], [-35, 22], [-43, -19], [-14, -40], [-26, -25], [-30, 13], [-37, -2], [-30, 29], [-17, -15]], [[5573, 8838], [-17, -2], [-4, -36], [-53, 8], [-7, -31], [-27, 1], [-18, -40], [-28, -61], [-43, -79], [10, -19], [-10, -22], [-27, 1], [-18, -52], [2, -73], [17, -29], [-9, -65], [-23, -38], [-12, -32]], [[5453, 3412], [14, 28], [11, -16], [4, -23], [13, -4], [17, -11], [15, 4], [25, 28], [0, 206]], [[5552, 3624], [8, -9], [16, -52], [-2, -34], [6, -20], [20, 6], [13, 25], [14, 16], [6, 27], [14, 13], [12, -7], [13, -15], [23, -3], [17, 13], [3, 17], [5, 27], [15, 4], [8, 21], [10, 37], [25, 41], [39, 41]], [[5817, 3772], [11, 0], [14, -10], [9, 7], [15, -6]], [[5866, 3763], [13, -78], [7, -39], [-5, -62], [3, -20]], [[5884, 3564], [-14, 10], [-8, -4], [-3, -16], [-7, -21], [0, -19], [16, -30], [17, 6], [5, 24]], [[5890, 3514], [21, 0]], [[5804, 3391], [-12, 17], [-13, -11], [-15, -22], [-15, -35], [21, -43], [10, 6], [5, 17], [16, 9], [4, 18], [9, 27], [-10, 17]], [[2547, 6027], [-5, -1], [-10, -33], [-5, 6], [-4, -2], [1, -8]], [[2524, 5989], [-26, 0], [-26, 0], [0, -31], [-13, 0], [11, -19], [10, -13], [3, -12], [5, -3], [-1, -19], [-36, 0], [-13, -45], [4, -11], [-3, -13], [-1, -16]], [[3399, 3321], [18, 6], [28, -43], [10, 2], [29, -36], [22, -31], [16, -38], [-13, -26], [8, -31]], [[3482, 3570], [6, 32], [3, 33], [1, 30], [-10, 10], [-11, -9], [-10, 3], [-4, 21], [-2, 51], [-5, 17], [-19, 15], [-11, -11], [-30, 10]], [[3390, 3772], [2, 76]], [[3384, 3879], [9, 11]], [[3393, 3890], [-3, 32], [8, 24], [4, 44], [-6, 34], [-15, 16], [-3, 22], [4, 32], [-53, 2], [-11, 65], [8, 1], [0, 24], [-6, 16], [-1, 32], [-16, 17], [-18, -1], [-11, 16], [-19, 11], [-11, 21], [-31, 9], [-30, 50], [2, 37], [-3, 22], [3, 41], [-37, -9], [-14, -21], [-25, -23], [-6, -16], [-14, -2], [-21, 5]], [[3068, 4391], [-15, -9], [-13, 6], [2, 84], [-23, -32], [-24, 1], [-11, 30], [-18, 3], [5, 24], [-15, 34], [-11, 50], [7, 10], [0, 23], [17, 16], [-3, 30], [7, 20], [2, 25], [32, 38], [22, 11], [4, 8], [25, -2]], [[3058, 4761], [13, 152], [0, 24], [-4, 32], [-12, 20], [0, 41], [15, 9], [6, -6], [1, 21], [-16, 6], [-1, 35], [54, -1], [10, 19], [7, -18], [6, -33], [5, 7]], [[3142, 5069], [15, -29], [22, 3], [5, 17], [21, 13], [11, 9], [4, 24], [19, 16], [-1, 11], [-24, 5], [-3, 35], [1, 37], [-13, 15], [5, 5], [21, -7], [22, -14], [8, 13], [20, 9], [31, 21], [10, 21], [-3, 15]], [[3313, 5288], [14, 3], [7, -13], [-4, -24], [9, -9], [7, -26], [-8, -19], [-4, -47], [7, -29], [2, -25], [17, -26], [14, -3], [3, 11], [8, 2], [13, 10], [9, 15], [15, -5], [7, 2]], [[3429, 5105], [15, -5], [3, 12], [-5, 11], [3, 16], [11, -5], [13, 6], [16, -12]], [[3485, 5128], [12, -12], [9, 16], [6, -3], [4, -15], [13, 4], [11, 21], [8, 41], [17, 50]], [[3384, 3879], [-1, 17], [-25, 28], [-26, 1], [-49, -16], [-13, -49], [-1, -30], [-11, -66]], [[3067, 4023], [17, 60], [-12, 47], [7, 18], [-5, 21], [10, 28], [1, 47], [1, 39], [6, 19], [-24, 89]], [[2769, 4810], [3, -23], [-8, -13], [1, -20], [12, 4], [11, -6], [12, -28], [15, 23], [6, 37], [17, 49], [33, 22], [30, 58], [9, 36], [-4, 42]], [[2906, 4991], [7, 5], [19, -26], [9, -26], [13, -15], [16, -58], [21, -7], [15, 15], [10, -10], [17, 5], [21, -26], [-18, -56], [8, -2], [14, -29]], [[2906, 4991], [-12, 13], [-14, 19], [-7, -9], [-24, 7], [-7, 24], [-5, -1], [-28, 32]], [[2836, 5401], [4, 27], [9, -4], [5, 16], [-6, 33], [3, 8]], [[3018, 5654], [-18, -10], [-7, -27], [-10, -16], [-8, -21], [-4, -40], [-8, -32], [15, -4], [3, -25], [6, -13], [3, -22], [-4, -21], [1, -11], [7, -5], [7, -19], [36, 5], [16, -7], [19, -48], [11, 6], [20, -3], [16, 7], [10, -10], [-5, -30], [-6, -18], [-2, -40], [5, -37], [8, -17], [1, -12], [-14, -28], [10, -12], [8, -19], [8, -56]], [[2695, 5456], [2, 11], [2, 12], [-1, 10], [4, 6], [-6, 9], [0, 22], [11, 5]], [[2619, 5615], [4, 8], [18, -15], [7, 7], [9, -4], [4, -12], [8, -4], [7, 12]], [[2574, 5721], [9, 2], [3, 13], [4, 0], [0, 28], [6, 1], [6, -1], [6, 15], [8, -11], [3, 7], [5, 7], [10, 15], [0, 11], [3, 0], [4, 13], [3, 2], [4, -9], [6, -2], [6, 7], [7, 0], [10, 7], [4, 8], [9, -1]], [[2561, 5743], [2, 22], [-4, 6], [-6, 4], [-12, -6], [-1, 7], [-8, 9], [-6, 11], [-8, 5]], [[2518, 5801], [5, 14], [-2, 11], [2, 10], [13, 16], [13, 21]], [[2497, 5762], [1, 9], [9, 14], [6, 6], [-2, 6], [7, 4]], [[2524, 5989], [-1, -44], [-2, -63], [8, 0]], [[3340, 5464], [-22, -32], [-3, -21], [10, -20], [-7, -11], [-17, -9], [0, -25], [-7, -15], [19, -43]], [[3412, 5331], [-4, -50], [-17, -14], [1, -13], [-5, -29], [13, -40], [9, 0], [3, -32], [17, -48]], [[3501, 5319], [-15, -47], [3, -38], [10, -33], [-4, -24], [-3, -26], [-7, -23]], [[5171, 7747], [13, -14], [40, -11], [-14, -38], [-3, -39]], [[5207, 7645], [-8, -10], [-12, 5], [1, -14], [-21, -31], [0, -25], [13, 9], [10, -25]], [[5190, 7554], [-2, -15], [9, -21], [-10, -17], [7, -43], [15, -7], [-3, -24]], [[5082, 7359], [-32, -7], [-31, 25], [-10, -12], [-51, 25], [-11, 22]], [[5069, 7841], [4, -20], [13, -1], [13, -22], [20, -26], [14, 4], [24, -25]], [[5157, 7751], [6, -5], [8, 1]], [[5817, 3772], [-18, 25], [-21, 9], [-8, 35], [0, 20], [-12, 6], [-32, 61], [-9, 32], [-5, 10], [-11, 44]], [[5701, 4014], [31, -6], [9, -6], [10, 1], [15, 36], [24, 46], [10, 4], [4, 19], [15, 22], [21, 8]], [[5840, 4138], [2, -21], [23, 1], [13, -11], [6, -14], [13, -4], [15, -18], [0, -70], [-6, -39], [-1, -41], [5, -17], [-3, -32], [-5, -5], [-7, -41], [-29, -63]], [[5552, 3624], [0, 162], [27, 2], [1, 198], [21, 1], [43, 20], [10, -23], [18, 22], [9, 0], [15, 12]], [[5696, 4018], [5, -4]], [[5325, 4038], [14, 11], [16, 9], [18, -1], [17, -25], [4, 4], [113, 2], [19, -27], [67, -8], [51, 23]], [[5644, 4026], [23, 13], [18, -3], [11, -13], [0, -5]], [[4542, 5896], [10, 18], [14, -5], [13, 12], [16, 1], [13, -17], [18, -15], [17, -40], [18, -39]], [[4661, 5811], [2, -34], [5, -32], [11, -16], [2, -21], [-1, -17]], [[4680, 5691], [-4, -3], [-15, 4], [-3, -6], [-6, -1], [-20, 13], [-13, 1]], [[4619, 5699], [-51, 2], [-8, -6], [-9, 2], [-15, -10]], [[4532, 5730], [25, -1], [7, 8], [5, 0], [10, 13], [12, -12], [12, -1], [12, 13], [-6, 16], [-9, -10], [-8, 1], [-11, 13], [-9, -1], [-6, -13], [-31, -1]], [[4661, 5811], [10, 11], [4, 32], [9, 2], [20, -16], [15, 11], [11, -4], [4, 13], [112, 1], [6, 39], [-5, 6], [-13, 240], [-14, 240], [43, 1]], [[4863, 6387], [93, -121], [94, -122], [7, -26], [17, -15], [13, -10], [0, -35], [31, 6]], [[5118, 6064], [0, -128], [-15, -37], [-2, -35], [-25, -9], [-38, -4], [-10, -20], [-18, -2]], [[5010, 5829], [-18, 0], [-7, 10], [-15, -8], [-26, -23], [-5, -17], [-22, -25], [-4, -14], [-11, -12], [-14, 8], [-7, -14], [-4, -38], [-23, -46], [1, -19], [-7, -23], [1, -32]], [[4849, 5576], [-11, -9], [-7, -7], [-4, 24], [-8, -6], [-5, 1], [-5, -16], [-21, 0], [-8, 9], [-4, -6]], [[4776, 5566], [-8, 16], [1, 17], [-3, 7], [-6, -6], [1, 18], [6, 14], [-12, 24], [-3, 15], [-6, 12], [-6, 2], [-6, -8], [-9, -8], [-8, -12], [-12, 5], [-7, 14], [-5, 2], [-7, -8], [-5, 0], [-1, 21]], [[4758, 6521], [105, -134]], [[5051, 5341], [-7, 38], [2, 128], [-6, 11], [-1, 27], [-10, 20], [-8, 16], [3, 29]], [[5024, 5610], [10, 7], [6, 24], [13, 5], [6, 17]], [[5059, 5663], [10, 16], [10, 0], [21, -32]], [[5100, 5647], [-1, -18], [6, -33], [-6, -23], [3, -14], [-13, -35], [-9, -17], [-5, -35], [1, -35], [-2, -90]], [[5402, 5714], [-8, -3], [-1, -18]], [[5393, 5693], [-5, -1], [-19, 61], [-6, 2], [-22, -31], [-21, 16], [-15, 3], [-8, -8], [-17, 2], [-16, -24], [-14, -1], [-34, 29], [-13, -14], [-14, 1], [-10, 21], [-28, 21], [-30, -7], [-7, -12], [-4, -32], [-8, -22], [-2, -50]], [[5059, 5663], [1, 38], [-32, 12], [-1, 27], [-16, 37], [-3, 25], [2, 27]], [[5118, 6064], [39, 24], [81, 109], [95, 106]], [[5333, 6303], [44, -24], [15, -30], [20, 21]], [[5393, 5693], [11, -22], [-3, -10], [-1, -19], [-24, -43], [-7, -35], [-4, -29], [-6, -12], [-5, -39], [-15, -23], [-4, -28], [-7, -23], [-2, -23], [-19, -18], [-16, 22], [-10, 0], [-17, -33], [-8, -1], [-13, -53], [-7, -39]], [[5424, 5412], [-14, -57], [-7, -10], [-2, -43], [3, -23], [-2, -17], [13, -29], [2, -20], [10, -28], [13, -18], [1, -25], [3, -17]], [[5444, 5125], [-2, -30], [-22, 14], [-22, 14], [-35, 2]], [[5363, 5125], [-4, 3], [-16, -7], [-17, 8], [-13, -4]], [[5313, 5125], [-45, 1]], [[5029, 5329], [-6, 19], [-8, 36], [-2, 27], [6, 50], [-7, 21], [-2, 43], [0, 41], [-12, 28], [2, 18]], [[5000, 5612], [24, -2]], [[4920, 5277], [1, 22], [-12, 48], [8, 62], [11, 47], [-7, 79]], [[4921, 5535], [-4, 42], [1, 31], [48, 3], [12, -4], [9, 9], [13, -4]], [[4849, 5576], [13, -13], [5, -18], [12, -12], [10, 14], [13, 2], [19, -14]], [[4785, 5242], [2, 46], [3, 7], [-1, 22], [-12, 23], [-8, 4], [-8, 15], [6, 24], [-3, 27], [1, 16]], [[4765, 5426], [5, 1], [1, 24], [-2, 10], [3, 8], [10, 7], [-7, 44], [-6, 23], [2, 19], [5, 4]], [[4765, 5426], [-8, 2], [-5, -22], [-8, 0], [-6, 12], [2, 22], [-11, 34], [-8, -6], [-6, -2]], [[4715, 5466], [-7, -3], [0, 21], [-4, 14], [0, 16], [-6, 24], [-7, 20], [-23, 0], [-6, -11], [-8, -1], [-4, -12], [-4, -16], [-14, -24]], [[4579, 5613], [13, 27], [8, -1], [7, 9], [6, 0], [5, 8], [-3, 18], [3, 6], [1, 19]], [[4682, 5376], [6, 18], [2, 16], [12, 31], [13, 25]], [[5512, 5194], [-18, 3], [-19, 10], [-16, -30], [-15, -52]], [[5682, 5457], [15, -23], [0, -18], [19, -29], [12, -24], [7, -33], [20, -22], [5, -18]], [[5360, 4734], [-10, 19], [-8, -9], [-12, -24]], [[5308, 4778], [21, 31], [-11, 37], [10, 14], [19, 7], [2, 24], [15, -26], [24, -3], [9, 26], [3, 37], [-3, 44], [-13, 33], [12, 64], [-7, 11], [-21, -4], [-7, 28], [2, 24]], [[5263, 5056], [10, 3], [40, -1], [0, 67]], [[5909, 4487], [14, -25], [7, -47], [-5, -15], [-6, -45], [6, -46], [-9, -20], [-9, -51], [15, -15]], [[5922, 4223], [-84, -45], [2, -40]], [[5644, 4026], [-18, 35], [-19, 45], [2, 177], [58, -1], [-3, 19], [4, 21], [-5, 26], [4, 27], [-3, 18]], [[5959, 4360], [-7, -43], [7, -72], [10, 1], [10, -18], [12, -40], [2, -72], [-12, -11], [-8, -39], [-19, 35], [-2, 39], [6, 25], [-1, 23], [-11, 14], [-8, -5], [-16, 26]], [[5890, 3514], [-2, 25], [-4, 25]], [[5806, 4842], [17, -5], [8, 32], [15, -4]], [[5992, 6816], [-5, -17]], [[5987, 6799], [-10, 8], [-6, -37], [7, -7], [-7, -7], [-1, -15], [13, 8]], [[5983, 6749], [0, -22], [-14, -89]], [[5969, 6638], [-2, 15], [-16, 81]], [[5975, 6838], [9, 0], [3, 9], [7, 1]], [[5994, 6848], [1, -23], [-4, -8], [1, -1]], [[5999, 6924], [13, -3], [4, -22], [-15, -21], [-7, -30]], [[5987, 6799], [0, -34], [-4, -16]], [[5263, 6683], [-12, 100], [-17, 22], [0, 14], [-23, 33], [-3, 42], [18, 31], [6, 45], [-4, 53], [5, 29]], [[5319, 6840], [-2, -42], [-14, -16], [-8, -18], [-19, -22], [3, -23], [-3, -23], [-13, -13]], [[4759, 6536], [0, 66], [44, 41], [28, 8], [23, 15], [11, 28], [32, 22], [1, 41], [16, 5], [13, 20], [36, 10], [5, 21], [-7, 12], [-10, 59], [-1, 34], [-11, 35]], [[5263, 6683], [9, -49], [1, -26], [-5, -45], [2, -25], [-3, -31], [2, -35], [-11, -23], [17, -40], [1, -24], [10, -31], [13, 10], [22, -26], [12, -35]], [[5992, 6816], [31, -22], [54, 60]], [[6077, 6854], [11, -68]], [[6088, 6786], [-5, -8], [-56, -28], [28, -56], [-9, -9], [-5, -19], [-21, -7], [-7, -20], [-12, -18], [-31, 9]], [[6557, 6447], [5, -19]], [[6566, 6384], [-14, 0], [-3, -36], [5, -8], [-12, -11], [0, -23], [-8, -23], [-1, -22]], [[6533, 6261], [-6, -12], [-83, 28], [-11, 56], [-1, 13]], [[6427, 6368], [-8, -4], [-8, 11]], [[6344, 6586], [-19, -2], [-7, 27], [-25, 5]], [[6293, 6616], [20, 53], [19, -4]], [[6077, 6854], [61, 57], [11, 68], [-3, 40], [16, 14], [14, 35]], [[6176, 7068], [12, 8], [32, -7], [10, -14], [13, 9]], [[6243, 7064], [18, -66], [18, -17], [2, -32], [-14, -19], [-6, -44], [19, -52], [34, -31], [15, -42], [-5, -40], [9, 0], [0, -30], [15, -29]], [[6293, 6616], [-52, 4], [-78, 112], [-41, 39], [-34, 15]], [[6475, 5924], [-9, 39], [-22, 92]], [[6444, 6055], [83, 55], [19, 112], [-13, 39]], [[7849, 5676], [-7, 68], [18, 46], [36, 10], [26, -8]], [[7922, 5792], [23, -21], [12, 38], [25, -21]], [[7982, 5788], [6, -37], [-3, -66], [-47, -43], [13, -34], [-30, -4], [-24, -22]], [[7836, 5345], [-9, -23], [-18, -6], [-2, 28], [-23, 25], [-5, -10]], [[7737, 5551], [13, 57], [16, 52], [-11, 51], [0, 26], [-3, 31], [-19, 44], [-6, 28], [9, 10], [11, 48], [-12, 37], [-17, 40], [-14, 49], [12, 10], [12, 60], [20, 3], [16, 24], [16, 13]], [[7792, 6117], [2, -34], [19, -2], [-7, -59], [0, -50], [30, 33]], [[7932, 5864], [-1, -47], [-9, -25]], [[7810, 6190], [2, -13], [15, -1], [-4, 63], [14, 8]], [[7837, 6247], [17, -43], [12, -51], [34, 0], [11, -49], [-18, -14], [-8, -20], [34, -34], [23, -65], [17, -49], [21, -39], [7, -39], [-5, -56]], [[7565, 6148], [-2, 44], [10, -8], [0, 40]], [[7573, 6224], [14, 13], [-3, 24], [7, 19], [1, 57], [21, -13], [13, 46], [1, 27], [15, 47], [0, 32], [36, 38], [19, -10], [-2, 34], [10, 10], [-2, 21]], [[7703, 6569], [16, 5], [9, -33], [12, -13], [1, -43], [-1, -46], [-26, -46], [-4, -66], [30, 9], [6, -51], [18, -11], [-8, -46], [21, -21], [12, -10], [20, 16]], [[7837, 6247], [15, 14], [22, 0], [27, 6], [24, 30], [13, -21], [26, -10], [-5, -32], [14, -23], [28, -14]], [[8632, 7345], [0, 0]], [[8564, 7144], [-4, -13], [-11, -4], [-20, -2], [-11, -25], [-12, 2], [-2, -6]], [[8451, 7218], [23, 35], [30, 30], [19, 39], [13, -17], [24, -2], [-4, 29], [43, 24], [11, 31], [18, -32]], [[8240, 7771], [-13, -42], [-20, -56], [7, -22], [16, 7], [27, -9], [22, 21], [22, -18], [25, -39], [-3, -20], [-22, 7], [-40, -8], [-20, -16], [-20, -36], [-42, -22], [-28, -29], [-29, 11], [-15, 5], [-15, -36], [9, -21], [5, -19], [-20, -18], [-20, -30], [-32, -20], [-42, -2], [-45, -19], [-32, -30], [-12, 18], [-34, -1], [-41, 34], [-28, 8], [-36, -7], [-58, 12], [-30, -1], [-17, 33], [-12, 51], [-18, 6], [-33, 35], [-37, 8], [-33, 9], [-10, 24], [10, 65], [-19, 45], [-40, 21], [-23, 29], [-7, 39]], [[7573, 6224], [-14, 88], [-8, 0], [-4, -36], [-16, 29], [9, 32], [12, 3], [13, 47], [-16, 9], [-26, 0], [-26, 7], [-2, 39], [-14, 3], [-22, 24], [-9, -38], [20, -29], [-18, -21], [-6, -20], [17, -15], [-5, -34], [10, -42], [4, -45]], [[6893, 6316], [19, 37], [61, 0], [-6, 47], [-15, 28], [-4, 43], [-18, 25], [31, 58], [32, -4], [29, 58], [18, 57], [27, 55], [-1, 40], [24, 32], [-23, 28], [-9, 37], [-10, 49], [14, 24], [42, -14], [31, 9], [26, 46]], [[7161, 6971], [30, -65], [-3, -45], [12, -29], [-1, -28], [-20, 7], [7, -61], [28, -35], [38, -39]], [[7252, 6676], [-17, -25], [-11, -52], [27, -21], [26, -27], [36, -31], [38, -8], [16, -28], [22, -5], [33, -13], [23, 1], [4, 22], [-4, 35], [2, 24]], [[7447, 6548], [17, 12], [2, -44]], [[7466, 6516], [1, -11], [25, -21], [18, 8], [23, -3], [23, 1], [2, 34], [-12, 18]], [[7546, 6542], [23, 7], [25, 41], [32, 36], [23, -14], [20, 24], [13, -35], [-9, -23], [30, -9]], [[7466, 6516], [19, 41], [15, 14], [20, -12], [14, -2], [12, -15]], [[7252, 6676], [12, 13], [22, -17], [28, -36], [16, -8], [9, -26], [22, -11], [22, -25], [32, -13], [32, -5]], [[6708, 6393], [10, 64], [40, 29], [-2, 25], [-13, 9], [-1, 49], [-27, 25], [-11, 33], [-14, 30]], [[6690, 6657], [47, -29], [28, 8], [16, -7], [6, 13], [19, -5], [36, 23], [1, 47], [16, 31], [20, 0], [3, 16], [22, 7], [10, -5], [11, 16], [-2, 33], [12, 34], [18, 14], [-11, 36], [26, -1], [8, 20], [-1, 21], [14, 23], [-4, 28], [-6, 23], [16, 25], [30, 11], [32, 7], [14, 10], [16, 6]], [[7087, 7062], [21, -26], [8, -42], [45, -23]], [[6883, 7063], [9, -7], [20, 18], [9, -11], [9, 26], [17, -1], [4, 8], [3, 22], [12, 19], [15, -12], [-3, -17], [9, -3], [-3, -47], [11, -18], [10, 12], [12, 5], [17, 25], [19, -4], [29, 0]], [[7082, 7078], [5, -16]], [[6690, 6657], [25, 50], [-2, 36], [-21, 9], [-2, 35], [-9, 45], [12, 30], [-12, 8], [7, 41], [12, 69]], [[6700, 6980], [28, -21], [21, 7], [6, 25], [22, 9], [15, 17], [6, 44], [23, 11], [5, 20], [13, -15], [8, -2]], [[6972, 7235], [-10, -17], [-30, 9], [-3, -32], [30, 5], [34, -19], [53, 9]], [[7046, 7190], [7, -52], [9, 6], [17, -13], [-1, -21], [4, -32]], [[7229, 7352], [-4, -13], [-44, -30], [-10, -22], [-35, -6], [-11, -36], [-29, 8], [-20, -11], [-26, -26], [4, -13], [-8, -13]], [[6700, 6980], [-3, 47], [-21, 2], [-31, 49], [-22, 6], [-31, 28], [-20, 5], [-12, -10], [-19, 1], [-19, -31], [-25, -11]], [[6243, 7064], [-15, 45], [5, 17], [-8, 64], [19, 16]], [[6244, 7206], [4, -21], [14, -26], [19, -7]], [[6281, 7152], [10, 1]], [[6291, 7153], [33, 41], [10, 4], [9, -16], [-10, -27], [17, -30], [7, 3]], [[6004, 6989], [7, 13], [7, 12], [2, 31], [9, -11], [31, 15], [14, -10], [23, 0], [32, 21], [15, -1], [32, 9]], [[6281, 7152], [-11, 32], [0, 8], [-12, 0], [-9, 15], [-5, -1]], [[6244, 7206], [-11, 16], [-21, 14], [3, 27], [-5, 19]], [[6210, 7282], [39, 9]], [[6249, 7291], [5, -15], [11, -9], [-6, -14], [15, -19], [-8, -18], [12, -15], [13, -9], [0, -39]], [[5573, 8838], [37, -27], [43, -38], [1, -85], [9, -22]], [[5882, 7894], [-23, -4], [-9, -12], [-2, -28], [-11, 6], [-25, -3], [-7, 13], [-11, -10], [-10, 8], [-22, 1], [-31, 14], [-28, 4], [-22, -1], [-15, -15], [-13, -2]], [[5653, 7865], [-1, 24], [-8, 26], [17, 11], [0, 23], [-8, 21], [-1, 24]], [[5652, 7994], [27, 0], [30, 21], [6, 31], [23, 18], [-3, 25]], [[5735, 8089], [17, 9], [30, 22]], [[5971, 7570], [1, -30]], [[5928, 7553], [-4, 6]], [[5796, 7516], [-12, 10]], [[5784, 7526], [7, 6], [5, 19], [7, 18], [-2, 10], [6, 5], [3, -8], [16, -2], [7, 4], [-5, 6], [2, 8], [-9, 14], [-4, 24], [-11, 9], [2, 19], [-12, 14], [-12, 3], [-20, 17], [-19, -6], [-6, -8]], [[5739, 7678], [-12, 0], [-7, -13], [-20, -5], [-10, -9], [-13, 14], [-18, 0], [-17, 6], [-12, -12]], [[5630, 7659], [-2, 15], [-15, 15]], [[5613, 7689], [5, 23], [8, 14]], [[5626, 7726], [6, -3], [-7, 25], [25, 46], [14, 7], [3, 15], [-14, 49]], [[5626, 7726], [-26, 22], [-20, -8], [-13, 5], [-17, -11], [-14, 19], [-11, -7], [-2, 3]], [[5523, 7749], [-13, 27], [-20, 4], [-3, 17], [-19, 6], [-4, -14], [-15, 11], [2, 16], [-21, 5], [-13, 17]], [[5417, 7838], [-12, 36], [2, 19], [-6, 30], [-11, 20], [8, 14], [-6, 29]], [[5631, 8017], [14, -6], [7, -17]], [[5471, 7673], [-2, -23], [-16, 0], [6, -12], [-9, -36]], [[5450, 7602], [-6, -9], [-24, -2], [-14, -12], [-23, 4]], [[5383, 7583], [-40, 14], [-6, 20], [-27, -10], [-4, -10], [-16, 7]], [[5290, 7604], [-15, 2], [-12, 10], [4, 14], [-1, 10]], [[5266, 7640], [8, 3], [14, -16], [4, 15], [25, -3], [20, 10], [13, -1], [9, -12], [2, 10], [-4, 36], [10, 7], [10, 26]], [[5377, 7715], [21, -18], [15, 22], [10, 5], [22, -17], [13, 3], [13, -11]], [[5471, 7699], [-3, -7], [3, -19]], [[5630, 7659], [-17, -11], [-13, -38], [-17, -38], [-22, -10]], [[5561, 7562], [-17, 2], [-22, -14]], [[5522, 7550], [-10, -9], [-23, 11], [-21, 24], [-8, 7]], [[5460, 7583], [-6, 19], [-4, 0]], [[5471, 7673], [14, -14]], [[5519, 7659], [2, 11], [11, 2], [14, 9], [3, -4], [13, 7], [6, 13], [9, 4], [30, -17], [6, 5]], [[5784, 7526], [-5, 26], [3, 23], [-1, 25], [-16, 33], [-9, 23], [-9, 17], [-8, 5]], [[5793, 7427], [-17, 6], [-20, 20]], [[5598, 7486], [-2, 23], [-17, 13], [-3, 18], [-15, 22]], [[5584, 8112], [32, 17], [47, -3], [27, 5], [4, -12], [15, -3], [26, -27]], [[5675, 8210], [23, 10], [13, -7], [24, -21], [22, 0]], [[5417, 7838], [-13, -5], [-7, 6], [-7, -11], [-20, -10], [-10, -14], [-21, -12], [5, -17], [3, -23], [14, -13], [16, -24]], [[5266, 7640], [-30, 17], [-5, -13], [-24, 1]], [[5171, 7747], [2, 25], [-6, 12]], [[5167, 7784], [4, 38]], [[5171, 7822], [-5, 58], [17, 0], [7, 21], [6, 51], [-5, 18]], [[5236, 8053], [21, -8], [18, 9]], [[5777, 7333], [-24, 7], [-28, -17]], [[5725, 7323], [0, -28], [-26, -5], [-19, 19], [-22, -15], [-21, 2]], [[5637, 7296], [-2, 37], [-14, 17]], [[5621, 7350], [5, 8], [-3, 7], [4, 18], [11, 17], [-14, 24], [-2, 20], [7, 13]], [[5725, 7323], [13, -15], [-8, -34], [-7, -7]], [[5559, 7201], [13, 27], [2, 18], [9, 8], [0, 14]], [[5583, 7268], [18, 5], [11, 12], [15, -1], [5, 10], [5, 2]], [[6154, 7307], [29, 2], [27, -27]], [[5538, 7326], [-2, 17], [12, 28], [1, -11], [8, 5]], [[5557, 7365], [6, -15], [7, -5], [1, -20]], [[5571, 7325], [-3, -19], [4, -24], [11, -14]], [[5522, 7550], [7, -22]], [[5529, 7528], [9, -16], [-11, -21]], [[5527, 7491], [-12, 13], [-19, -1], [-24, 9], [-13, -1], [-6, -12], [-10, 13], [-6, -23], [14, -26], [6, -17], [12, -21], [11, -12], [10, -23], [25, -21]], [[5515, 7369], [-3, -10]], [[5380, 7527], [20, -2], [5, 10], [9, -10], [11, -1], [0, 16], [10, 6], [2, 22], [23, 15]], [[5290, 7604], [-3, -22], [-12, -10], [-20, 7], [-6, -22], [-14, -2], [-5, 9], [-15, -19], [-13, -3], [-12, 12]], [[5157, 7751], [3, 31], [7, 2]], [[5092, 7852], [20, -5], [26, 12], [17, -24], [16, -13]], [[4749, 7326], [10, 14], [11, 8], [7, -27], [16, 0], [5, 7], [16, -2], [8, -28], [-13, -15], [0, -43], [-5, -8], [-1, -27], [-12, -4], [11, -33], [-7, -37], [9, -16], [-4, -15], [-10, -21], [2, -19]], [[4789, 8062], [6, -30], [-6, -29], [17, 0], [21, -11]], [[5383, 7583], [-3, -27], [7, -24]], [[4942, 8105], [2, -6], [25, -65]], [[4905, 7856], [-1, 0], [-43, 10]], [[6249, 7291], [6, 9], [21, -16], [15, -3], [4, 6], [-14, 30], [7, 8]], [[8172, 5251], [12, -29], [6, 19], [13, -2], [2, 36], [1, 27]], [[5471, 7699], [4, 12], [12, -1], [9, 6], [1, 5], [5, 3], [2, 13], [7, 2], [4, 10], [8, 0]], [[6196, 5705], [-8, -14], [-12, 5]], [[6176, 5696], [-10, 18], [-11, 33], [-12, 18], [-8, 19], [-24, 22], [-19, 1], [-7, 11], [-16, -13], [-17, 25], [-8, -41], [-33, 12]], [[6188, 5908], [12, 17], [-3, 24], [7, 27], [12, -14], [7, 5], [32, 1], [5, -6], [27, -5], [11, 3], [7, -19], [13, 9], [20, 59], [26, 25], [80, 21]], [[79, 310], [30, 18]], [[109, 328], [4, -1], [3, 0]], [[9999, 294], [-9999, 0]], [[5943, 6947], [-3, 2], [-5, -4], [-4, 1], [-2, -2], [0, 5], [-2, 4], [-6, 0], [-7, -4], [-5, 3]], [[4635, 6535], [0, 1], [14, 21]], [[5694, 6222], [0, 204], [0, 198], [-8, 44], [7, 35], [-5, 24], [10, 26]], [[5980, 5305], [-17, 61], [-12, 13], [-5, 22], [-14, 27], [-17, 4], [9, 32], [15, 1], [4, 17]], [[6176, 5696], [-10, -24], [-9, -26], [2, -16], [0, -17], [16, -1], [6, 4], [7, -10]], [[6188, 5606], [-6, -19], [10, -31], [10, -27], [11, -19], [90, -66], [24, 0]], [[6198, 5636], [-10, -30]], [[5844, 4936], [-16, -17], [-7, 6]], [[5856, 5194], [11, 16], [18, -13], [22, 13], [20, 0], [17, 26]], [[5527, 7491], [10, 1], [-7, -25], [14, -21], [-4, -26], [-7, -3]], [[5533, 7417], [-5, -5], [-9, -13], [-4, -30]], [[5571, 7325], [4, -1], [1, 11], [17, 9], [6, 2]], [[5599, 7346], [9, 3], [13, 1]], [[5599, 7346], [-1, 4], [3, 7], [3, 14], [-4, -1], [-5, 11], [-5, 2], [-3, 9], [-5, 3], [-4, 8], [-5, -3], [-4, -18], [-7, -4]], [[5562, 7378], [2, 5], [-10, 11], [-9, 6], [-4, 8], [-8, 9]], [[5562, 7378], [-5, -13]], [[2444, 7594], [-7, -3], [0, -31], [-1, 0], [-7, -6], [-6, -5], [-4, -11], [6, -10], [-2, -14], [0, -15], [-1, -12], [8, -11], [3, 0], [9, -8], [4, -4], [2, -6], [7, -10], [9, -8], [1, -4], [0, -14], [1, -6]], [[2466, 7416], [-37, 1], [-40, 0], [-38, -1], [-30, 0]], [[2321, 7416], [0, 52], [-3, 53], [-5, 4], [-3, 9], [1, 8], [7, 6], [0, 8]], [[2318, 7556], [0, 10], [-2, 8], [-2, 9], [-1, 12], [-1, 12], [-1, 3], [-1, 17], [0, 7], [0, 7], [-2, 11], [-3, 11], [-3, 11], [-1, 10], [0, 10], [1, 8], [0, 6], [-3, 8], [0, 5]], [[1776, 7721], [83, 1], [83, 0], [83, 0], [83, 0]], [[2108, 7722], [1, -102], [1, -68]], [[2110, 7552], [-1, -50]], [[2109, 7502], [-47, 0], [-50, 0], [-43, 0], [-55, 0], [1, -28], [-1, -2]], [[1914, 7472], [-3, 3], [-3, 8], [-3, 1], [-4, -11], [-6, -2], [-16, 4], [0, -6], [-9, 2], [-6, -7], [-5, 14], [-3, 8], [-6, 2], [-1, 4], [-2, 14], [-5, 7], [-3, 18], [-3, 7], [-4, 2], [-3, -8], [-5, -7], [-5, 6], [0, 14], [3, 4], [-2, 14], [2, 15], [3, 12], [-8, 0], [-7, 8], [-8, 18], [-5, 8], [-6, 6], [-5, 8], [0, 10], [-8, 15], [-2, 58]], [[2318, 7556], [-53, -3], [-45, 0], [-57, 0], [-53, -1]], [[2108, 7722], [97, 0], [94, -1]], [[1914, 7472], [1, -1], [0, -138]], [[1915, 7333], [-83, -1]], [[1832, 7332], [-83, 1]], [[1749, 7333], [0, 99], [3, 16], [-2, 7], [-6, 4], [0, 9], [4, 13], [6, 11], [4, 18], [4, 14], [3, 7], [-2, 9], [-4, 4], [-7, 11]], [[1752, 7555], [0, 10], [-2, 8], [-1, 78], [0, 70]], [[1752, 7555], [-57, -1], [-10, -5], [-7, 2], [-6, -4], [-10, -7], [-12, 1], [-7, -4], [-6, -2], [-4, 3], [-10, 2], [-6, -2], [-11, -6], [-7, 0], [-6, 2], [-2, 8], [-1, 9], [-5, 11], [-8, 3], [-7, 5], [-8, 0], [-7, 1]], [[1555, 7571], [-2, 32]], [[1588, 7721], [78, 0], [83, 0]], [[1971, 7055], [0, -314]], [[1813, 6817], [4, 0], [3, 11], [-5, 8], [-1, 8], [-1, 10], [5, 13], [2, 25], [9, 11], [-6, 11], [-3, 10], [-3, 10], [-1, 8], [-1, 5]], [[1815, 6947], [2, 11], [-1, 10], [-1, 11], [0, 12], [-2, 8], [2, 7], [5, 0], [6, -4], [3, -2], [2, 8], [1, 2], [0, 45]], [[1832, 7055], [45, 0], [53, 0], [41, 0]], [[1549, 7333], [118, -1]], [[1667, 7332], [-1, -166], [53, -75], [50, -74], [46, -70]], [[2165, 7222], [0, -86], [0, -82]], [[2165, 7054], [-26, 1]], [[2139, 7055], [-34, 0], [-47, 0], [-44, 0], [-43, 0]], [[1971, 7055], [0, 222]], [[1971, 7277], [27, 0], [28, 0], [56, 0], [28, 0]], [[2110, 7277], [55, 0], [0, -54], [0, -1]], [[1832, 7332], [0, -277]], [[1667, 7332], [82, 0], [0, 1]], [[2139, 7055], [0, -28]], [[2139, 7027], [0, -145], [0, -105], [-26, 0], [-50, 0], [-25, 0], [0, -5], [3, -9]], [[1558, 7528], [-3, 43]], [[1915, 7333], [0, -54], [56, -2]], [[2109, 7502], [0, -114]], [[2109, 7388], [1, -111]], [[2509, 7001], [0, -5]], [[2468, 6833], [-24, 2], [-30, -2], [-27, 0]], [[2387, 6833], [2, 32], [-7, 0], [-5, -1], [-2, 4]], [[2375, 6868], [1, 49], [1, 54], [-6, 58]], [[2371, 7029], [34, 0], [31, -1], [29, 0], [32, -3], [2, -7], [-3, -6], [-3, -6], [-2, -5], [18, 0]], [[2466, 7416], [0, -3], [4, -9], [-3, -4], [0, -12], [1, -5], [2, -9], [9, -5], [3, -8]], [[2482, 7361], [2, -5], [3, -2], [1, -6], [5, -5], [2, -5], [-1, -15], [-5, -12], [-2, -4], [-6, -3], [-10, -3], [-2, -10], [3, -4], [1, -9], [-3, -9], [-2, -9], [-7, -8], [-1, -10]], [[2460, 7242], [-4, 5], [-5, 9], [-30, -2], [-32, 0], [-25, 0], [-25, 0]], [[2320, 7360], [0, 5], [-4, 6], [2, 9], [2, 9], [0, 6], [-3, 7], [0, 14], [4, 0]], [[2165, 7222], [49, 0], [36, 0], [64, 0], [38, 0]], [[2372, 7174], [0, -60], [-1, -59]], [[2371, 7055], [-24, 0], [-50, 0], [-50, 0], [-28, 0], [-28, -1], [-26, 0]], [[2371, 7029], [0, 26]], [[2460, 7242], [-2, -14], [2, -16], [5, -12], [5, -9], [6, -8], [3, -2], [2, -11], [1, -9], [3, -3], [5, 4], [5, -9], [-1, -10]], [[2109, 7388], [55, 0], [41, 0], [56, 0]], [[2375, 6868], [-12, 11], [-7, 6], [-7, -4], [-9, 1], [-6, 0], [-5, -5], [-5, -2], [-4, 2], [-9, -3], [-4, 10], [-5, -8], [-7, 3], [-8, 9], [-8, -6], [-4, 14], [-13, -2], [-8, 3], [-9, 4], [-5, 12], [-7, -4], [-4, 5], [-7, 6], [0, 53], [0, 54], [-28, 0], [-28, 0], [-27, 0]], [[2456, 6724], [24, -2], [26, 0], [0, -9], [-2, -9], [2, -7], [3, -7], [1, -9], [1, -5]], [[2511, 6676], [0, -1]], [[2393, 6650], [-2, 6], [3, 8], [4, 8], [0, 11], [-2, 3], [2, 14], [2, 6], [3, 20], [-3, 8], [-3, 12], [-2, 13], [-2, 9], [-4, 6], [-2, 59]], [[2958, 7336], [21, -1], [26, -2]], [[3005, 7333], [0, -30], [-1, -8]], [[2975, 7289], [-21, -15], [0, 1]], [[2954, 7275], [0, 1], [-1, 7], [6, 5], [-2, 5], [1, 43]], [[2958, 7336], [6, 38]], [[2964, 7374], [23, -1]], [[2987, 7373], [34, -1], [2, 6], [6, 4], [4, -1]], [[3024, 7305], [-1, 8], [-4, 6], [-2, 15], [-12, -1]], [[2987, 7373], [-2, 6], [2, 7], [1, 15], [0, 3], [1, 13], [3, 11], [3, 5], [3, 13], [1, 9], [1, 6], [5, 2], [6, 7], [1, 7], [-2, 8], [3, 15]], [[3025, 7516], [4, -102], [-1, -5], [5, -9], [1, -7], [3, 0]], [[2964, 7374], [1, 46], [-4, 0], [0, 2], [2, 8], [-3, 15], [3, 11], [-2, 9], [0, 16], [1, 7], [0, 12]], [[2551, 6944], [35, 0], [35, -1]], [[2621, 6943], [7, -69], [7, -55], [4, -18], [2, -10], [-4, -9], [-2, -18], [2, -10], [-1, -10], [-1, -9], [2, -7], [1, -7]], [[2638, 6721], [-56, 0], [-16, -3], [-1, -5], [7, -13], [-2, -11], [-2, -8]], [[2544, 6687], [-1, 85], [5, 89], [5, 72], [-2, 11]], [[2638, 6721], [5, -15], [28, -2], [45, -9], [2, -10], [3, 5], [0, 20], [4, 2], [5, -4], [6, -1]], [[2621, 6943], [22, -1], [14, 1]], [[2657, 6943], [35, 0]], [[2692, 6943], [-3, -5], [-4, -11], [7, -9], [5, -3], [5, -18], [3, -10], [10, -13], [2, -7], [7, -9], [3, -13], [9, -11], [2, -13], [1, -6], [-1, -4], [5, -6], [3, -11], [0, -10], [3, -2], [4, -3]], [[2493, 6945], [27, 0], [31, -1]], [[2692, 6943], [3, 1], [15, 10], [25, -1], [13, -2], [1, -5], [2, 3], [5, -9], [0, -6], [31, -1], [31, -52]], [[2818, 6881], [-14, -21]], [[2482, 7361], [28, 0], [29, 0], [22, -1]], [[2568, 7316], [0, -64], [0, -64], [-3, -16], [3, -4], [1, -9], [0, -8], [-3, -3], [-2, -9], [-5, -12], [-4, -15], [-1, -12]], [[2554, 7100], [0, -4], [-3, -7], [2, -6], [-5, -4], [-6, -4], [1, -13], [-3, -4], [-7, 5], [-8, 3], [-1, -5], [1, -9]], [[2588, 7319], [30, 0], [26, 0], [0, -4]], [[2644, 7315], [0, -51], [0, -54], [0, -38]], [[2644, 7172], [-2, -3], [2, -11], [-1, -4], [-5, 0], [-4, -5], [-7, 2], [0, -11], [-4, -4], [-4, -9], [-4, -2], [-6, -17], [-5, 5], [-2, 7], [-5, -11], [-3, -6], [-6, 6], [-6, -5], [-3, -6], [-8, 9], [-6, -6], [-7, 4], [-1, -6], [-3, 1]], [[2644, 7172], [9, -1], [5, -6], [7, -12], [6, -4], [4, -5], [7, 2], [5, -3], [6, 3], [5, 1], [2, -8], [6, -6]], [[2706, 7133], [0, -5], [0, -11], [3, -9], [1, -8], [5, -8], [2, -6], [6, -1]], [[2723, 7085], [-3, -5], [-8, -12], [-9, -6], [-1, -5], [-3, -6], [-7, -6], [-1, -1], [-2, -5], [-4, -2]], [[2685, 7037], [-2, -1], [-8, -3]], [[2675, 7033], [-18, -2], [-25, 2], [-8, 0], [-16, 1], [-16, 1], [-15, 0], [-17, -2], [-1, 3], [-6, 0], [0, -9], [-39, 0]], [[2657, 6943], [1, 12], [6, 3], [2, 6], [4, 7], [5, 1], [7, 3], [6, 5], [3, 5], [5, 4], [0, 4], [7, 8], [2, -5], [11, 10], [5, -1], [4, 10], [5, 2], [0, 8], [1, 7]], [[2731, 7032], [-11, -1]], [[2731, 7032], [47, -3], [55, 0], [29, 1], [27, 0], [3, 0]], [[2832, 6884], [-14, -3]], [[2644, 7315], [14, 0], [13, 0], [10, 1]], [[2763, 7332], [0, -75]], [[2763, 7257], [-4, -3], [1, -7], [-1, -13], [-3, -14], [-2, -12], [-1, -6], [-7, -12], [-4, -3], [-3, -2], [-4, 2], [-6, -10], [-1, -10], [-1, -5], [-2, -3], [-1, 7], [-4, 1], [-3, -12], [-1, -12], [-4, -8], [-6, -2]], [[2675, 7033], [42, -2], [3, 0]], [[2723, 7085], [1, -10], [2, -4], [1, -1], [4, -4], [7, 5], [2, 1], [3, -4], [9, 4], [2, 1], [0, 3], [0, 2], [4, -2], [3, 4], [4, -1], [4, 5], [1, 3], [0, 2], [-1, 6], [4, 10], [5, 7], [2, 8], [3, 6], [2, 4], [2, 12], [4, -4], [4, -4], [4, 2], [1, 5], [3, 7], [2, 5], [1, 3], [2, -2], [4, 7], [4, 4], [2, 3], [1, 2], [2, 4], [2, 11], [0, 3], [13, -13], [2, -2], [3, 11]], [[2841, 7184], [4, -2], [4, -4], [-2, -5], [-1, -1], [6, -4], [6, -6]], [[2858, 7162], [2, -4], [0, -4]], [[2860, 7154], [-1, -4], [-1, -1], [-4, -4], [-3, -13], [4, -3], [4, 3], [2, -7], [0, -1]], [[2896, 7107], [3, 3], [7, 1]], [[2472, 7606], [16, -19]], [[2488, 7587], [1, 0]], [[2489, 7587], [1, 1], [5, -2], [2, -10], [24, -10], [16, -10], [8, 0], [6, -1], [1, -9], [7, -3], [2, -8], [-1, -5], [-2, -9], [6, 0], [-2, -9], [4, -7], [1, 0]], [[2567, 7505], [0, -1]], [[2763, 7257], [0, -51], [29, 0]], [[2792, 7206], [0, -28], [4, 5], [5, 6], [5, 2], [4, 6], [8, -2], [3, 4], [6, 4], [8, -4], [4, -8], [2, -7]], [[2915, 7135], [-18, 0], [-3, 71]], [[2894, 7206], [3, 5], [2, 2], [6, -3]], [[2905, 7210], [-4, -6], [1, -10]], [[2914, 7154], [1, -19]], [[2858, 7162], [2, 3], [3, -6], [-3, -5]], [[2792, 7206], [26, 0], [9, 0], [19, 0], [24, 0], [24, 0]], [[2915, 7135], [0, -2]], [[2905, 7210], [6, 5], [2, 4], [6, 7], [4, 6], [-9, 14], [0, 6], [-3, 2], [0, 9], [3, 7], [-1, 7], [4, 5], [5, 12], [3, 3]], [[2925, 7297], [22, -22], [-2, -12]], [[2954, 7275], [-2, -2]], [[2925, 7297], [-4, 4], [-5, 4], [-2, 8], [1, 6], [-3, 5], [-6, 9], [-38, 0], [-41, 0], [-43, 0], [0, 15]], [[2807, 7386], [1, 0], [-3, 17]], [[2805, 7403], [13, 6]], [[2882, 7443], [-3, 7], [29, 39]], [[2583, 7540], [-16, -35]]], "transform": {"scale": [0.036003600360036005, 0.018001800180018002], "translate": [-180, -90]}, "bbox": [-180, -90, 180, 90]}