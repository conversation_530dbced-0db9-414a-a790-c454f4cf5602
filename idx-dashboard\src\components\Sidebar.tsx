import React from 'react';
import { NavLink } from 'react-router-dom';

const Sidebar: React.FC = () => {
  const menuItems = [
    'Dashboard', 'Equipment Status', 'Mission Statistics', 'Simulation Results', 'Settings'
  ];

  return (
    <nav className="bg-drdo-gray w-60 p-3 h-screen fixed z-10 shadow-md">
      <h2 className="text-xl font-bold mb-4 text-drdo-navy">DRDO IDX</h2>

      <ul className="space-y-2">
        {menuItems.map((item) => (
          <li key={item}>
            <NavLink
              to={`/${item.toLowerCase().replace(' ', '-')}`}
              className={({ isActive }) =>
                `block p-2 rounded ${isActive ? 'bg-drdo-navy text-drdo-white' : 'text-drdo-navy hover:bg-drdo-gray'} text-sm`
              }
            >
              {item}
            </NavLink>
          </li>
        ))}
      </ul>


    </nav>
  );
};

export default Sidebar;
