import React from 'react';
import { NavLink } from 'react-router-dom';

interface MenuItem {
  path: string;
  name: string;
  icon: string;
  description: string;
}

const Sidebar: React.FC = () => {
  const menuItems: MenuItem[] = [
    { path: '/dashboard', name: 'Dashboard', icon: '📊', description: 'System Overview' },
    { path: '/equipment-status', name: 'Equipment Status', icon: '⚙️', description: 'Equipment Monitoring' },
    { path: '/mission-statistics', name: 'Mission Statistics', icon: '📈', description: 'Mission Progress' },
    { path: '/simulation-results', name: 'Simulation Results', icon: '🎯', description: 'Simulation Analysis' },
    { path: '/weapons', name: 'Weapons Systems', icon: '🚀', description: 'BrahMos, Agni Telemetry' },
    { path: '/missions', name: 'Missions', icon: '📋', description: 'Mission Tracking' },
    { path: '/analytics', name: 'Analytics', icon: '📊', description: 'Data Analytics' },
    { path: '/geospatial', name: 'Geospatial', icon: '🗺️', description: 'Geospatial Mapping' },
    { path: '/settings', name: 'Setting<PERSON>', icon: '⚙️', description: 'Configuration' },
    { path: '/profile', name: 'Profile', icon: '👤', description: 'User Profile' },
    { path: '/roles', name: 'Roles', icon: '🔐', description: 'Role Management' },
    { path: '/users', name: 'Users', icon: '👥', description: 'User Directory' },
    { path: '/data-transfer', name: 'Data Transfer', icon: '📤', description: 'Data Exchange' },
    { path: '/audit-log', name: 'Audit Log', icon: '📝', description: 'Audit Records' },
    { path: '/visual-sandbox', name: 'Visual Sandbox', icon: '🎨', description: 'Visualization Studio' },
    { path: '/chat', name: 'Chat', icon: '💬', description: 'AI Assistant' }
  ];

  return (
    <nav className="bg-drdo-gray w-64 p-4 h-screen fixed z-10 shadow-lg">
      <h2 className="text-2xl font-bold mb-6 text-drdo-navy">DRDO IDX</h2>

      <ul className="space-y-3">
        {menuItems.map((item) => (
          <li key={item.path}>
            <NavLink
              to={item.path}
              className={({ isActive }) =>
                `block p-3 rounded-lg ${isActive ? 'bg-drdo-navy text-drdo-white' : 'text-drdo-navy hover:bg-drdo-gray'} transition-colors duration-200`
              }
            >
              {item.name}
            </NavLink>
          </li>
        ))}
      </ul>


    </nav>
  );
};

export default Sidebar;
