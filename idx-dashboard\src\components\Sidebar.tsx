import React from 'react';
import { NavLink } from 'react-router-dom';

const Sidebar: React.FC = () => {
  const menuItems = [
    { name: 'Dashboard', icon: '🏠', path: '/' },
    { name: 'Equipment Status', icon: '⚙️', path: '/equipment-status' },
    { name: 'Mission Statistics', icon: '📊', path: '/mission-statistics' },
    { name: 'Simulation Results', icon: '🎯', path: '/simulation-results' },
    { name: 'Settings', icon: '⚙️', path: '/settings' }
  ];

  return (
    <nav className="bg-card w-60 p-4 h-screen fixed z-10 shadow-lg border-r border-border">
      <div className="mb-6">
        <div className="flex items-center gap-2 mb-2">
          <div className="w-8 h-8 bg-primary text-primary-foreground rounded-lg flex items-center justify-center font-bold text-sm">
            D
          </div>
          <div>
            <h2 className="text-lg font-bold text-card-foreground">DRDO IDX</h2>
            <p className="text-xs text-muted-foreground">v2.0 Military</p>
          </div>
        </div>
        <div className="bg-success/10 text-success px-2 py-1 rounded text-xs font-medium">
          🔒 SECURE CONNECTION
        </div>
      </div>

      <ul className="space-y-2">
        {menuItems.map((item) => (
          <li key={item.name}>
            <NavLink
              to={item.path}
              className={({ isActive }) =>
                `flex items-center gap-3 p-3 rounded-lg transition-all duration-200 ${
                  isActive
                    ? 'bg-primary text-primary-foreground shadow-md'
                    : 'text-card-foreground hover:bg-secondary hover:shadow-sm'
                }`
              }
            >
              <span className="text-lg">{item.icon}</span>
              <span className="font-medium text-sm">{item.name}</span>
            </NavLink>
          </li>
        ))}
      </ul>

      <div className="absolute bottom-4 left-4 right-4">
        <div className="bg-muted p-3 rounded-lg text-center">
          <div className="text-xs text-muted-foreground">
            © 2025 DRDO | Classified
          </div>
          <div className="flex items-center justify-center mt-1 gap-1">
            <div className="w-2 h-2 bg-success rounded-full animate-pulse"></div>
            <span className="text-xs text-muted-foreground">Encrypted</span>
          </div>
        </div>
      </div>
    </nav>
  );
};

export default Sidebar;
