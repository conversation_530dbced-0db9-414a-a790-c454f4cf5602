var locale={moduleType:"locale",name:"sv",dictionary:{Autoscale:"Autoskala","Box Select":"V\xe4lj rektangel","Click to enter Colorscale title":"Klicka f\xf6r att ange titel p\xe5 f\xe4rgskala","Click to enter Component A title":"Klicka f\xf6r att ange titel p\xe5 komponent A","Click to enter Component B title":"Klicka f\xf6r att ange titel p\xe5 komponent B","Click to enter Component C title":"Klicka f\xf6r att ange titel p\xe5 komponent C","Click to enter Plot title":"Klicka f\xf6r att ange titel p\xe5 diagram","Click to enter X axis title":"Klicka f\xf6r att ange titel p\xe5 x-axel","Click to enter Y axis title":"Klicka f\xf6r att ange titel p\xe5 y-axel","Click to enter radial axis title":"Klicka f\xf6r att ange titel p\xe5 radiell axel","Compare data on hover":"J\xe4mf\xf6r data n\xe4r muspekaren h\xe5lls \xf6ver","Double-click on legend to isolate one trace":"Dubbelklicka p\xe5 f\xf6rklaringen f\xf6r att visa endast en serie","Double-click to zoom back out":"Dubbelklicka f\xf6r att zooma ut igen","Download plot":"Ladda ner diagram","Download plot as a png":"Ladda ner diagram som png","Edit in Chart Studio":"Editera i Chart Studio","IE only supports svg.  Changing format to svg.":"IE st\xf6der enbart svg. Byter format till svg.","Lasso Select":"V\xe4lj lasso","Orbital rotation":"Orbital rotation",Pan:"Panorera","Produced with Plotly.js":"Skapad med Plotly.js",Reset:"\xc5terst\xe4ll","Reset axes":"\xc5terst\xe4ll axlar","Reset camera to default":"\xc5terst\xe4ll kamera till standard","Reset camera to last save":"\xc5terst\xe4ll kamera till senast sparad","Reset view":"\xc5terst\xe4ll vy","Reset views":"\xc5terst\xe4ll vyer","Show closest data on hover":"Visa n\xe4rmaste v\xe4rde n\xe4r muspekaren h\xe5lls \xf6ver","Snapshot succeeded":"Bild skapad","Sorry, there was a problem downloading your snapshot!":"Tyv\xe4rr gick n\xe5got fel vid nedladdning av bild","Taking snapshot - this may take a few seconds":"Skapar bild - detta kan ta n\xe5gra sekunder","Toggle Spike Lines":"Aktivera/Inaktivera topplinjer","Toggle show closest data on hover":"Aktivera/Inaktivera visa n\xe4rmaste v\xe4rde n\xe4r muspekaren h\xe5lls \xf6ver","Turntable rotation":"Platt rotation",Zoom:"Zooma","Zoom in":"Zooma in","Zoom out":"Zooma ut","close:":"st\xe4ngning:","concentration:":"koncentration:","high:":"h\xf6g:","incoming flow count:":"inkommande fl\xf6de summering:","kde:":"kde:","lat:":"lat:","lon:":"lon:","low:":"l\xe5g:","lower fence:":"undre gr\xe4ns:","max:":"max:","mean \xb1 \u03c3:":"medel \xb1 \u03c3:","mean:":"medel:","median:":"median:","min:":"min:","new text":"ny text","open:":"\xf6ppning:","outgoing flow count:":"utg\xe5ende fl\xf6de summering:","q1:":"q1:","q3:":"q3:","source:":"k\xe4lla:","target:":"m\xe5l:",trace:"serie","upper fence:":"\xf6vre gr\xe4ns:"},format:{days:["S\xf6ndag","M\xe5ndag","Tisdag","Onsdag","Torsdag","Fredag","L\xf6rdag"],shortDays:["S\xf6n","M\xe5n","Tis","Ons","Tor","Fre","L\xf6r"],months:["Januari","Februari","Mars","April","Maj","Juni","Juli","Augusti","September","Oktober","November","December"],shortMonths:["Jan","Feb","Mar","Apr","Maj","Jun","Jul","Aug","Sep","Okt","Nov","Dec"],date:"%Y-%m-%d"}};"undefined"==typeof Plotly?(window.PlotlyLocales=window.PlotlyLocales||[],window.PlotlyLocales.push(locale)):Plotly.register(locale);