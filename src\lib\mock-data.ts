// DRDO IDX - Mock Data Store

import { SystemHealth, Threat<PERSON><PERSON>t, MilitaryAsset, WeaponSystem, Laboratory } from '../types/military';

export class MockDataStore {
  private static data: Map<string, any> = new Map();

  static initialize() {
    // System Health Data
    const systemHealthData: SystemHealth[] = [
      {
        component: 'Radar Network',
        performance: 95,
        status: 'HEALTHY',
        lastCheck: new Date(),
        issues: [],
        location: 'Primary Grid',
        priority: 'HIGH'
      },
      {
        component: 'Communication Array',
        performance: 87,
        status: 'WARNING',
        lastCheck: new Date(Date.now() - 300000), // 5 minutes ago
        issues: ['Signal degradation in sector 3'],
        location: 'Secondary Grid',
        priority: 'MEDIUM'
      },
      {
        component: 'Satellite Uplink',
        performance: 92,
        status: 'HEALTHY',
        lastCheck: new Date(Date.now() - 120000), // 2 minutes ago
        issues: [],
        location: 'Ground Station Alpha',
        priority: 'HIGH'
      },
      {
        component: 'Defense Grid',
        performance: 78,
        status: 'WARNING',
        lastCheck: new Date(Date.now() - 600000), // 10 minutes ago
        issues: ['Maintenance required on unit 7', 'Power fluctuation detected'],
        location: 'Perimeter Defense',
        priority: 'HIGH'
      },
      {
        component: 'Command Center',
        performance: 98,
        status: 'HEALTHY',
        lastCheck: new Date(Date.now() - 60000), // 1 minute ago
        issues: [],
        location: 'Central Command',
        priority: 'CRITICAL'
      },
      {
        component: 'Backup Systems',
        performance: 45,
        status: 'CRITICAL',
        lastCheck: new Date(Date.now() - 1800000), // 30 minutes ago
        issues: ['Generator 2 offline', 'UPS battery low', 'Cooling system failure'],
        location: 'Backup Facility',
        priority: 'CRITICAL'
      }
    ];

    // Threat Alerts Data
    const threatAlertsData: ThreatAlert[] = [
      {
        id: 'THREAT-001',
        type: 'CYBER',
        severity: 'HIGH',
        description: 'Suspicious network activity detected on secure channels',
        timestamp: new Date(Date.now() - 900000), // 15 minutes ago
        location: 'Network Perimeter',
        status: 'INVESTIGATING',
        assignedTo: 'Cyber Security Team Alpha'
      },
      {
        id: 'THREAT-002',
        type: 'PHYSICAL',
        severity: 'MEDIUM',
        description: 'Unauthorized personnel detected near facility perimeter',
        timestamp: new Date(Date.now() - 1800000), // 30 minutes ago
        location: 'Sector 7 Perimeter',
        status: 'RESOLVED',
        assignedTo: 'Security Team Bravo'
      },
      {
        id: 'THREAT-003',
        type: 'COMMUNICATION',
        severity: 'LOW',
        description: 'Signal interference on backup communication channels',
        timestamp: new Date(Date.now() - 3600000), // 1 hour ago
        location: 'Communication Tower 3',
        status: 'ACTIVE',
        assignedTo: 'Communications Team'
      }
    ];

    // Military Assets Data
    const militaryAssetsData: MilitaryAsset[] = [
      {
        id: 'ASSET-001',
        name: 'BrahMos Block III',
        type: 'MISSILE',
        status: 'OPERATIONAL',
        location: 'Launch Pad Alpha',
        readiness: 95,
        lastMaintenance: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000), // 7 days ago
        nextMaintenance: new Date(Date.now() + 23 * 24 * 60 * 60 * 1000), // 23 days from now
        specifications: {
          range: 450,
          speed: 2.8,
          payload: 300,
          accuracy: 98
        }
      },
      {
        id: 'ASSET-002',
        name: 'Agni-V ICBM',
        type: 'MISSILE',
        status: 'STANDBY',
        location: 'Secure Storage Bay 2',
        readiness: 88,
        lastMaintenance: new Date(Date.now() - 14 * 24 * 60 * 60 * 1000), // 14 days ago
        nextMaintenance: new Date(Date.now() + 16 * 24 * 60 * 60 * 1000), // 16 days from now
        specifications: {
          range: 5500,
          speed: 24,
          payload: 1500,
          accuracy: 95
        }
      },
      {
        id: 'ASSET-003',
        name: 'Tejas Mk1A',
        type: 'AIRCRAFT',
        status: 'DEPLOYED',
        location: 'Airbase Charlie',
        readiness: 92,
        lastMaintenance: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000), // 3 days ago
        nextMaintenance: new Date(Date.now() + 27 * 24 * 60 * 60 * 1000), // 27 days from now
        specifications: {
          range: 1850,
          speed: 1.8,
          payload: 4500,
          accuracy: 90
        }
      }
    ];

    // Weapon Systems Data
    const weaponSystemsData: WeaponSystem[] = [
      {
        id: 'WS-001',
        name: 'BrahMos Supersonic Cruise Missile',
        type: 'BRAHMOS',
        status: 'READY',
        readiness: 95,
        location: 'Launch Complex Alpha',
        lastTest: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // 30 days ago
        nextTest: new Date(Date.now() + 60 * 24 * 60 * 60 * 1000), // 60 days from now
        specifications: {
          range: 450,
          speed: 2.8,
          accuracy: 98,
          payload: 300
        },
        testResults: [
          {
            date: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
            accuracy: 98,
            success: true,
            notes: 'Perfect trajectory, target hit with precision'
          },
          {
            date: new Date(Date.now() - 90 * 24 * 60 * 60 * 1000),
            accuracy: 96,
            success: true,
            notes: 'Minor deviation corrected mid-flight'
          }
        ]
      },
      {
        id: 'WS-002',
        name: 'Agni-V Intercontinental Ballistic Missile',
        type: 'AGNI',
        status: 'TESTING',
        readiness: 85,
        location: 'Test Range Bravo',
        lastTest: new Date(Date.now() - 15 * 24 * 60 * 60 * 1000), // 15 days ago
        nextTest: new Date(Date.now() + 45 * 24 * 60 * 60 * 1000), // 45 days from now
        specifications: {
          range: 5500,
          speed: 24,
          accuracy: 95,
          payload: 1500
        }
      }
    ];

    // Store all data
    this.data.set('system_health', systemHealthData);
    this.data.set('threats', threatAlertsData);
    this.data.set('assets', militaryAssetsData);
    this.data.set('weapons', weaponSystemsData);
  }

  static retrieveSecure<T>(key: string): T | null {
    return this.data.get(key) || null;
  }

  static storeSecure<T>(key: string, data: T): void {
    this.data.set(key, data);
  }

  static clearAll(): void {
    this.data.clear();
  }
}

// Initialize mock data
MockDataStore.initialize();
