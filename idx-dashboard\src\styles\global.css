@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --drdo-navy: #003087;
  --drdo-saffron: #FF9933;
  --drdo-white: #FFFFFF;
  --drdo-gray: #F5F5F5;
}

body {
  margin: 0;
  font-family: 'Inter', sans-serif;
  background-color: var(--drdo-white);
  color: var(--drdo-navy);
  overflow-x: hidden;
  transition: background-color 0.3s;
}

.light-theme {
  background-color: #F9FAFB;
  color: #1F2937;
}

.dark-theme {
  background-color: #1A202C;
  color: #E2E8F0;
}

.drdo-formal-theme {
  background-color: var(--drdo-white);
  color: var(--drdo-navy);
}

.card {
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  padding: 16px;
  background: rgba(255, 255, 255, 0.95);
}

h2 {
  font-size: 1.5rem;
  font-weight: 600;
}

@media (max-width: 768px) {
  .ml-60 {
    margin-left: 0;
  }
  .w-[calc(100%-240px)] {
    width: 100%;
  }
  .fixed {
    position: relative;
  }
  .h-[calc(100vh-60px)] {
    height: calc(100vh - 48px);
  }
  .sidebar {
    transform: translateX(-100%);
    transition: transform 0.3s ease-in-out;
  }
  .sidebar.open {
    transform: translateX(0);
  }
}
