@tailwind base;
@tailwind components;
@tailwind utilities;

/* DRDO IDX Military-Grade Design System - All colors MUST be HSL */

@layer base {
  :root {
    /* DRDO Formal White Theme - Default */
    --background: 0 0% 100%;          /* Pure White */
    --foreground: 222 47% 11%;        /* Deep Navy Text */

    --card: 0 0% 100%;                /* White Cards */
    --card-foreground: 222 47% 11%;

    --popover: 0 0% 100%;
    --popover-foreground: 222 47% 11%;

    /* DRDO Official Colors */
    --primary: 221 83% 53%;           /* DRDO Blue #2563eb */
    --primary-foreground: 0 0% 100%;

    --secondary: 210 40% 96%;         /* Light Blue Background */
    --secondary-foreground: 222 47% 11%;

    --muted: 210 40% 94%;             /* Light Gray */
    --muted-foreground: 215 16% 47%;

    --accent: 38 92% 50%;             /* Orange Accent */
    --accent-foreground: 0 0% 100%;

    /* Status Colors */
    --destructive: 0 85% 60%;         /* Alert Red */
    --destructive-foreground: 0 0% 100%;

    --success: 142 71% 45%;           /* Success Green */
    --success-foreground: 0 0% 100%;

    --warning: 38 92% 50%;            /* Warning Orange */
    --warning-foreground: 0 0% 100%;

    /* Interface Elements */
    --border: 214 32% 91%;            /* Light Border */
    --input: 0 0% 100%;               /* White Input */
    --ring: 221 83% 53%;              /* DRDO Blue Ring */

    /* Military Grid & Tactical Elements */
    --grid-primary: 195 80% 25%;
    --grid-secondary: 158 60% 20%;
    --hud-overlay: 195 100% 45%;
    --classified-red: 0 100% 50%;
    --secure-green: 120 100% 40%;

    /* Gradients for Advanced UI */
    --gradient-tactical: linear-gradient(135deg, hsl(195 100% 45%), hsl(158 80% 35%));
    --gradient-command: linear-gradient(180deg, hsl(210 25% 8%), hsl(210 20% 12%));
    --gradient-alert: linear-gradient(90deg, hsl(0 85% 60%), hsl(38 100% 50%));
    --gradient-hud: radial-gradient(circle, hsl(195 100% 45% / 0.2), transparent);

    /* Shadows & Effects */
    --shadow-tactical: 0 8px 32px hsl(195 100% 45% / 0.3);
    --shadow-command: 0 4px 16px hsl(210 20% 5% / 0.8);
    --shadow-alert: 0 0 20px hsl(45 100% 55% / 0.6);
    --glow-hud: 0 0 30px hsl(195 100% 45% / 0.5);

    /* Typography & Spacing */
    --font-mono: 'JetBrains Mono', 'Fira Code', 'Courier New', monospace;
    --font-tactical: 'Inter', 'Roboto', sans-serif;
    --radius: 0.375rem;

    /* DRDO Sidebar - Formal Style */
    --sidebar-background: 0 0% 100%;
    --sidebar-foreground: 222 47% 11%;
    --sidebar-primary: 221 83% 53%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 210 40% 96%;
    --sidebar-accent-foreground: 222 47% 11%;
    --sidebar-border: 214 32% 91%;
    --sidebar-ring: 221 83% 53%;

    /* Animations & Transitions */
    --transition-tactical: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-command: all 0.2s ease-out;
    --animation-pulse: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground font-tactical antialiased;
    font-family: var(--font-tactical);
  }

  /* Military Grid Overlay */
  .military-grid {
    background-image:
      linear-gradient(hsl(var(--grid-primary) / 0.1) 1px, transparent 1px),
      linear-gradient(90deg, hsl(var(--grid-primary) / 0.1) 1px, transparent 1px);
    background-size: 20px 20px;
  }

  /* HUD Style Elements */
  .hud-border {
    position: relative;
  }

  .hud-border::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: var(--gradient-tactical);
    border-radius: var(--radius);
    z-index: -1;
  }

  /* Tactical Animations */
  @keyframes tactical-pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
  }

  @keyframes radar-sweep {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }

  @keyframes data-stream {
    0% { transform: translateY(100%); opacity: 0; }
    50% { opacity: 1; }
    100% { transform: translateY(-100%); opacity: 0; }
  }

  .animate-tactical-pulse {
    animation: tactical-pulse 2s ease-in-out infinite;
  }

  .animate-radar-sweep {
    animation: radar-sweep 4s linear infinite;
  }

  .animate-data-stream {
    animation: data-stream 3s linear infinite;
  }

  /* Scrollbar Styling */
  ::-webkit-scrollbar {
    width: 8px;
  }

  ::-webkit-scrollbar-track {
    background: hsl(var(--muted));
  }

  ::-webkit-scrollbar-thumb {
    background: hsl(var(--primary));
    border-radius: 4px;
  }

  ::-webkit-scrollbar-thumb:hover {
    background: hsl(var(--accent));
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .mobile-layout .ml-60 {
    margin-left: 0;
  }

  .mobile-layout .header-width {
    width: 100%;
    left: 0 !important;
  }

  .mobile-layout .sidebar {
    transform: translateX(-100%);
    transition: transform 0.3s ease-in-out;
    z-index: 30;
  }

  .mobile-layout .sidebar.open {
    transform: translateX(0);
  }
}
