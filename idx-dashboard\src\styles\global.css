@import "tailwindcss";

:root {
  --drdo-navy: #003087;
  --drdo-saffron: #FF9933;
  --drdo-white: #FFFFFF;
  --drdo-gray: #F5F5F5;
}

body {
  margin: 0;
  font-family: 'Inter', sans-serif;
  background-color: var(--drdo-white);
  color: var(--drdo-navy);
  overflow: hidden;
}

.minimal-theme {
  background-color: var(--drdo-white);
  color: #333333;
}

.dark-theme {
  background-color: #1A202C;
  color: #E2E8F0;
}

.formal-theme {
  background-color: var(--drdo-white);
  color: var(--drdo-navy);
}

.card {
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  padding: 16px;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: var(--drdo-navy);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--drdo-saffron);
}
