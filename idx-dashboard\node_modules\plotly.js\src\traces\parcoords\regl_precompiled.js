'use strict';
var v0 = require('../../generated/regl-codegen/453a70fefa48db31713162aeb1ac438cb8579f54504f3b23acf32128df3dfd45');
var v1 = require('../../generated/regl-codegen/30680f8f6712ef1af5cf7547e0af35b036fb300c67b07967cf448492ff4de4d0');
var v2 = require('../../generated/regl-codegen/a3970baf1d8cac9305ee830c7026550387343d4dde2353dd86a4d082c97d3470');
var v3 = require('../../generated/regl-codegen/3fd666968f3ce90d1c048b7a9aab515f3ce387a5401a10f8b66121c9469d1c0d');

/* eslint-disable quote-props */
module.exports = {
    '453a70fefa48db31713162aeb1ac438cb8579f54504f3b23acf32128df3dfd45': v0,
    '30680f8f6712ef1af5cf7547e0af35b036fb300c67b07967cf448492ff4de4d0': v1,
    'a3970baf1d8cac9305ee830c7026550387343d4dde2353dd86a4d082c97d3470': v2,
    '3fd666968f3ce90d1c048b7a9aab515f3ce387a5401a10f8b66121c9469d1c0d': v3
};
