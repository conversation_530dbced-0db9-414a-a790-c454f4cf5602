{"type": "Topology", "objects": {"coastlines": {"type": "GeometryCollection", "geometries": [{"type": "LineString", "arcs": [0]}, {"type": "LineString", "arcs": [1]}, {"type": "LineString", "arcs": [2]}, {"type": "LineString", "arcs": [3]}, {"type": "LineString", "arcs": [4]}, {"type": "LineString", "arcs": [5]}, {"type": "LineString", "arcs": [6]}, {"type": "LineString", "arcs": [7]}, {"type": "LineString", "arcs": [8]}, {"type": "LineString", "arcs": [9]}, {"type": "LineString", "arcs": [10]}, {"type": "LineString", "arcs": [11]}, {"type": "LineString", "arcs": [12]}, {"type": "LineString", "arcs": [13, 14, 15]}, {"type": "LineString", "arcs": [16]}, {"type": "LineString", "arcs": [17]}, {"type": "LineString", "arcs": [18, 19, 20, 21, 22]}, {"type": "LineString", "arcs": [23]}, {"type": "LineString", "arcs": [24]}, {"type": "LineString", "arcs": [25]}, {"type": "LineString", "arcs": [26]}, {"type": "LineString", "arcs": [27]}, {"type": "LineString", "arcs": [28]}, {"type": "MultiLineString", "arcs": [[29, 30, 31], [32]]}, {"type": "LineString", "arcs": [33]}, {"type": "LineString", "arcs": [34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 123, 124, 125, 126, 127, 128, 129, 130, 131, 132, 133, 134, 135, 136, 137, 138, 139, 140, 141, 142, 143, 144, 145, 146, 147, 148, 149, 150, 151, 152, 153, 154, 155, 156, 157, 158, 159, 160, 161, 162, 163, 164, 165, 166, 167, 168, 169, 170, 171, 172, 173, 174, 175, 176, 177, 178, 179, 180, 181, 182, 183, 184, 185, 186, 187, 188, 189, 190, 191, 192, 193, 194, 195, 196, 197, 198, 199, 200, 201, 202, 203, 204, 205, 206, 207, 208, 209, 210, 211, 212, 213, 214, 215, 216, 217, 218, 219, 220, 221, 222, 223, 224, 225, 226, 227]}, {"type": "LineString", "arcs": [228]}, {"type": "LineString", "arcs": [229, 230, 231]}, {"type": "LineString", "arcs": [232]}, {"type": "LineString", "arcs": [-233, 233]}, {"type": "LineString", "arcs": [234]}, {"type": "LineString", "arcs": [235]}, {"type": "LineString", "arcs": [236]}, {"type": "LineString", "arcs": [237]}, {"type": "LineString", "arcs": [238]}, {"type": "LineString", "arcs": [239]}, {"type": "LineString", "arcs": [240]}, {"type": "LineString", "arcs": [241]}, {"type": "LineString", "arcs": [242]}, {"type": "LineString", "arcs": [243]}, {"type": "LineString", "arcs": [244]}, {"type": "LineString", "arcs": [245]}, {"type": "LineString", "arcs": [246]}, {"type": "LineString", "arcs": [247]}, {"type": "LineString", "arcs": [248]}, {"type": "LineString", "arcs": [249, 250]}, {"type": "LineString", "arcs": [251, 252, 253]}, {"type": "LineString", "arcs": [254]}, {"type": "LineString", "arcs": [255]}, {"type": "LineString", "arcs": [256, 257, 258]}, {"type": "LineString", "arcs": [259]}, {"type": "LineString", "arcs": [260]}, {"type": "LineString", "arcs": [261]}]}, "land": {"type": "GeometryCollection", "geometries": [{"type": "Polygon", "arcs": [[236]]}, {"type": "Polygon", "arcs": [[237]]}, {"type": "Polygon", "arcs": [[238]]}, {"type": "Polygon", "arcs": [[239]]}, {"type": "Polygon", "arcs": [[240]]}, {"type": "Polygon", "arcs": [[24]]}, {"type": "Polygon", "arcs": [[23]]}, {"type": "Polygon", "arcs": [[27]]}, {"type": "MultiPolygon", "arcs": [[[141, 142, 143, 279, 145, 146, 147, 148, 280, 150, 151, 152, 281, 282, 155, 156, 157, 283, 284, 160, 285, 286, 163, 164, 287, 288, 289, 290, 169, 170, 171, 291, 292, 173, 174, 175, 176, 177, 293, 294, 180, 295, 182, 296, 297, 185, 298, 299, 300, 301, 302, 190, 191, 303, 193, 194, 195, 196, 197, 304, 199, 305, 306, 201, 307, 308, 204, 309, 310, 311, 312, 209, 313, 314, 315, 213, 316, 215, 216, 317, 318, 219, 319, 320, 321, 322, 323, 324, 325, 326, 327, 328, 329, 330, 331, 332, 333, 334, 335, 336, 45, 337, 338, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 339, 60, 61, 340, 341, 64, 342, 66, 343, 344, 345, 346, 347, 348, 349, 350, 351, 352, 353, 354, 355, 356, 357, 358, 359, 360, 361, 362, 363, 364, 365, 366, 367, 368, 369, 370, 371, 372, 373, 374, 375, 376, 377, 378, 139, 379]], [[380, 70, 381, 382, 73, 383, 384, 76, 385, 78, 79, 80, 386, 387, 388, 84, 389, 390, 87, 391, 392, 90, 393, 92, 394, 94, 395, 396, 97, 397, 99, 398, 399, 102, 400, 104, 401, 402, 107, 403, 109, 404, 405, 406, 112, 407, 408, 115, 409, 410, 118, 411, 412, 413, 122, 414, 415, 125, 416, 127, 417, 418, 130, 131, 419, 420, 421, 422, 423, 424, 425, 426, 427, 428, 68, 429]]]}]}, "ocean": {"type": "GeometryCollection", "geometries": [{"type": "MultiPolygon", "arcs": [[[-12], [-255], [-248], [-247], [-11], [-249], [-16, 430, -14], [-13], [-19, -23, 431, -21, 432], [433, -252, -254], [-262], [434, -251], [-17], [-256], [-4], [-260], [-18], [-5], [-29], [-6], [435, -257, -259], [-3], [-2], [-1], [-10], [-245], [-246], [-244], [-243], [-242], [-9], [436, -230, -232], [-7], [-8], [-236], [-237], [-238], [-239], [-240], [-241], [-27], [-26], [-25], [-24], [-28], [-234, 232, 437], [-235, 438], [-261, 439, -32, 440, -30, -229, -228, 441, -226, 442, -224, -223, -222, -221, -220, -219, -218, -217, -216, -215, -214, -213, -212, -211, -210, 443, -312, -207, -206, -205, -309, -203, -202, -307, 444, -199, -198, -197, -196, -195, -194, -193, -192, 445, 446, -189, -188, -187, -186, -185, -184, -183, -182, -181, -180, -179, -178, -177, -176, 447, -292, -172, -171, -170, -291, 448, 449, -165, -164, -163, -162, -161, -160, -159, -158, -157, -156, -155, -154, -153, -152, -151, -150, -149, -148, -147, -146, -145, 450, -142, -141, -140, -139, 451, -137, 452, -135, -134, -133, -132, 453, -130, -129, -128, -127, -126, -125, -124, -123, 454, -120, -119, -118, -117, -116, -115, -114, -113, -112, 455, -405, -110, -109, -108, -107, -106, -105, -104, -103, -102, -101, -100, -99, -98, -97, -96, -95, -94, -93, -92, -91, -90, -89, -88, -87, -86, -85, -84, -83, -82, -81, 456, -79, -78, -77, -76, -75, -74, -73, -72, -71, -381, 457, -69, -68, -67, -66, -65, -64, -63, -62, -61, 458, -59, -58, -57, -56, -55, -54, -53, -52, -51, -50, -49, -48, -47, -46, -45, 459, -43, 460, -41, 461, -39, 462, -37, 463, -35, 33, 464]], [[-33, 465]]]}]}, "lakes": {"type": "GeometryCollection", "geometries": [{"type": "Polygon", "arcs": [[466, 366, 467]]}, {"type": "Polygon", "arcs": [[364, 468, 469, 470, 471, 472]]}, {"type": "Polygon", "arcs": [[359, 473, 474, 475, 476]]}, {"type": "Polygon", "arcs": [[477]]}, {"type": "Polygon", "arcs": [[478]]}, {"type": "Polygon", "arcs": [[479, 480, 361, 481]]}, {"type": "Polygon", "arcs": [[-480, 482, 483, 484, 485, 486, 487]]}]}, "rivers": {"type": "GeometryCollection", "geometries": [{"type": "LineString", "arcs": [262, 263, 264, 265, 266, 267, 268, 269, 270, 271, 272, 273, 274, 275, 276, 277, 278]}]}, "countries": {"type": "GeometryCollection", "geometries": [{"type": "MultiPolygon", "properties": {"ct": [-99.06, 39.5]}, "id": "USA", "arcs": [[[343, 344, 345, 346, 347, 348, 349, 350, 351, 352, 353, 354, 355, 356, 357, 358, 359, 360, 361, 362, 363, 364, 365, 366, 367, 368, 369, 370, 371, 372, 373, 374, 375, 376, 377, 378, 139, 140, 141, 142, 143, 144, 145, 146, 147, 148, 149, 150, 151, 152, 153, 154, 155, 156, 157, 158, 159, 160, 161, 162, 163, 164, 165, 166, 167, 168, 169, 170, 171, 172, 173, 174, 175, 176, 177, 178, 179, 180, 181, 182, 183, 184, 185, 186, 187, 188, 189, 190, 191, 192, 193, 194, 195, 196, 197, 198, 199, 200, 201, 202, 203, 204, 205, 206, 207, 208, 209, 210, 211, 212, 213, 214, 215, 216, 217, 218, 219, 220, 221, 222, 322, 323, 324, 325, 326, 327, 328, 329, 330, 331, 332, 333, 334, 335, 336, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66]], [[236]], [[237]], [[238]], [[239]], [[240]], [[23]], [[24]], [[421, 422, 423, 424, 425, 426, 427, 428, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 123, 124, 125, 126, 127, 128, 129, 130, 131, 132, 133]], [[27]]]}]}, "subunits": {"type": "GeometryCollection", "geometries": [{"type": "Polygon", "properties": {"ct": [-94.3, 46.32], "gu": "USA"}, "id": "MN", "arcs": [[488, 489, 490, 491, 492, 493, 494, 495, 353, 496, 355, 497, 498, 358, -477]]}, {"type": "Polygon", "properties": {"ct": [-109.66, 47.05], "gu": "USA"}, "id": "MT", "arcs": [[499, 500, 501, 502, 346, 503]]}, {"type": "Polygon", "properties": {"ct": [-100.48, 47.46], "gu": "USA"}, "id": "ND", "arcs": [[-492, 504, -500, 505, 348]]}, {"type": "MultiPolygon", "properties": {"ct": [-155.52, 19.6], "gu": "USA"}, "id": "HI", "arcs": [[[236]], [[237]], [[238]], [[239]], [[240]]]}, {"type": "Polygon", "properties": {"ct": [-114.65, 44.39], "gu": "USA"}, "id": "ID", "arcs": [[-503, 506, 507, 508, 509, 510, 511]]}, {"type": "Polygon", "properties": {"ct": [-120.4, 47.38], "gu": "USA"}, "id": "WA", "arcs": [[-511, 512, 513, 61, 62, 63, 64, 514, 66, 343, 515]]}, {"type": "Polygon", "properties": {"ct": [-111.66, 34.3], "gu": "USA"}, "id": "AZ", "arcs": [[516, 332, 517, 334, 518, 519, 520]]}, {"type": "Polygon", "properties": {"ct": [-119.64, 37.26], "gu": "USA"}, "id": "CA", "arcs": [[-519, 335, 521, 45, 46, 47, 522, 523, 50, 524, 525, 526, 54, 527, 528, 57, 529, 530]]}, {"type": "Polygon", "properties": {"ct": [-105.55, 39], "gu": "USA"}, "id": "CO", "arcs": [[531, 532, 533, 534, 535, 536]]}, {"type": "Polygon", "properties": {"ct": [-116.65, 39.35], "gu": "USA"}, "id": "NV", "arcs": [[-509, 537, -520, -531, 538]]}, {"type": "Polygon", "properties": {"ct": [-106.09, 34.42], "gu": "USA"}, "id": "NM", "arcs": [[-517, -534, 539, 540, 331]]}, {"type": "Polygon", "properties": {"ct": [-120.54, 43.94], "gu": "USA"}, "id": "OR", "arcs": [[-510, -539, -530, 58, 59, 541, -513]]}, {"type": "Polygon", "properties": {"ct": [-111.67, 39.33], "gu": "USA"}, "id": "UT", "arcs": [[-508, 542, -535, -521, -538]]}, {"type": "Polygon", "properties": {"ct": [-107.55, 43.03], "gu": "USA"}, "id": "WY", "arcs": [[-502, 543, 544, -536, -543, -507]]}, {"type": "Polygon", "properties": {"ct": [-92.44, 34.92], "gu": "USA"}, "id": "AR", "arcs": [[545, 275, 276, 546, 547, 548, 549]]}, {"type": "Polygon", "properties": {"ct": [-93.51, 42.08], "gu": "USA"}, "id": "IA", "arcs": [[-490, 550, 551, 552, 553, -267, 554]]}, {"type": "Polygon", "properties": {"ct": [-98.38, 38.48], "gu": "USA"}, "id": "KS", "arcs": [[-532, 555, 269, 556, 557]]}, {"type": "Polygon", "properties": {"ct": [-92.48, 38.38], "gu": "USA"}, "id": "MO", "arcs": [[-550, 558, -557, -270, -269, -553, 559, 271, 272, 273]]}, {"type": "Polygon", "properties": {"ct": [-99.82, 41.53], "gu": "USA"}, "id": "NE", "arcs": [[-537, -545, 560, 263, 561, 265, 266, -554, 268, -556]]}, {"type": "Polygon", "properties": {"ct": [-97.5, 35.58], "gu": "USA"}, "id": "OK", "arcs": [[-533, -558, -559, -549, 562, -540]]}, {"type": "Polygon", "properties": {"ct": [-100.23, 44.45], "gu": "USA"}, "id": "SD", "arcs": [[-491, -555, -266, -562, -264, -561, -544, -501, -505]]}, {"type": "Polygon", "properties": {"ct": [-91.96, 31.05], "gu": "USA"}, "id": "LA", "arcs": [[-547, 277, 563, 564, 208, 209, 210, 211, 212, 213, 214, 215, 565]]}, {"type": "Polygon", "properties": {"ct": [-99.33, 31.46], "gu": "USA"}, "id": "TX", "arcs": [[-541, -563, -548, -566, 216, 217, 566, 219, 567, 221, 568, 322, 569, 324, 570, 326, 571, 328, 572, 573]]}, {"type": "Polygon", "properties": {"ct": [-72.74, 41.61], "gu": "USA"}, "id": "CT", "arcs": [[574, 575, 152, 576, 577]]}, {"type": "Polygon", "properties": {"ct": [-71.74, 42.24], "gu": "USA"}, "id": "MA", "arcs": [[-575, 578, 579, 580, 144, 145, 581, 147, 582, 583, 150, 584]]}, {"type": "Polygon", "properties": {"ct": [-71.56, 43.69], "gu": "USA"}, "id": "NH", "arcs": [[-581, 585, 370, 586, 143]]}, {"type": "Polygon", "properties": {"ct": [-71.53, 41.68], "gu": "USA"}, "id": "RI", "arcs": [[-576, -585, 587]]}, {"type": "Polygon", "properties": {"ct": [-72.66, 44.07], "gu": "USA"}, "id": "VT", "arcs": [[-580, 588, 589, -586]]}, {"type": "Polygon", "properties": {"ct": [-86.83, 32.77], "gu": "USA"}, "id": "AL", "arcs": [[590, 591, 592, 205, 593]]}, {"type": "Polygon", "properties": {"ct": [-82.5, 28.62], "gu": "USA"}, "id": "FL", "arcs": [[-593, 594, 186, 187, 188, -447, -446, 191, 192, 193, 595, 195, 596, 197, 198, -445, 306, 201, 202, 203, 597]]}, {"type": "Polygon", "properties": {"ct": [-83.45, 32.65], "gu": "USA"}, "id": "GA", "arcs": [[-592, 598, 599, 600, 185, -595]]}, {"type": "Polygon", "properties": {"ct": [-89.66, 32.77], "gu": "USA"}, "id": "MS", "arcs": [[-277, 601, -594, 206, 207, -565, -564, -278]]}, {"type": "Polygon", "properties": {"ct": [-80.88, 33.9], "gu": "USA"}, "id": "SC", "arcs": [[-601, 602, 181, 182, 183, 184]]}, {"type": "Polygon", "properties": {"ct": [-89.2, 40.06], "gu": "USA"}, "id": "IL", "arcs": [[-552, 603, -485, 604, 605, -272, -560]]}, {"type": "Polygon", "properties": {"ct": [-86.28, 39.9], "gu": "USA"}, "id": "IN", "arcs": [[-605, -484, 606, 607, 608]]}, {"type": "Polygon", "properties": {"ct": [-85.28, 37.51], "gu": "USA"}, "id": "KY", "arcs": [[-273, -606, -609, 609, 610, 611, 612, 613]]}, {"type": "Polygon", "properties": {"ct": [-79.25, 35.54], "gu": "USA"}, "id": "NC", "arcs": [[-600, 614, 615, -616, 616, 617, 618, 178, 179, 180, -603]]}, {"type": "Polygon", "properties": {"ct": [-82.79, 40.28], "gu": "USA"}, "id": "OH", "arcs": [[-608, 619, -472, 620, 621, -610]]}, {"type": "Polygon", "properties": {"ct": [-86.32, 35.84], "gu": "USA"}, "id": "TN", "arcs": [[-276, -546, -274, -614, -613, 612, 622, -616, -615, -599, -591, -602]]}, {"type": "MultiPolygon", "properties": {"ct": [-78.86, 37.5], "gu": "USA"}, "id": "VA", "arcs": [[[-612, 623, 624, 625, 626, 627, 174, 175, -617, 615, -623, -613]], [[628, 170]]]}, {"type": "Polygon", "properties": {"ct": [-90, 44.65], "gu": "USA"}, "id": "WI", "arcs": [[-489, -476, 629, 630, 631, 632, -486, -604, -551]]}, {"type": "Polygon", "properties": {"ct": [-80.61, 38.64], "gu": "USA"}, "id": "WV", "arcs": [[-611, -622, 633, 634, -624]]}, {"type": "Polygon", "properties": {"ct": [-75.5, 38.99], "gu": "USA"}, "id": "DE", "arcs": [[635, 636, 637, 166, 167, 638]]}, {"type": "Polygon", "properties": {"ct": [-77.02, 38.9], "gu": "USA"}, "id": "DC", "arcs": [[-626, 639]]}, {"type": "Polygon", "properties": {"ct": [-76.77, 39.03], "gu": "USA"}, "id": "MD", "arcs": [[-629, 171, 172, -627, -640, -625, -635, 640, -636, 641, 169]]}, {"type": "Polygon", "properties": {"ct": [-74.67, 40.21], "gu": "USA"}, "id": "NJ", "arcs": [[-638, 642, 643, 159, 160, 161, 644, 163, 645, 646]]}, {"type": "Polygon", "properties": {"ct": [-75.5, 42.93], "gu": "USA"}, "id": "NY", "arcs": [[-578, 647, 154, 155, 648, 157, 158, -644, 649, 650, -469, 365, -467, 651, 368, -589, -579]]}, {"type": "Polygon", "properties": {"ct": [-77.81, 40.87], "gu": "USA"}, "id": "PA", "arcs": [[-621, -471, 652, -650, -643, -637, -641, -634]]}, {"type": "Polygon", "properties": {"ct": [-69.22, 45.34], "gu": "USA"}, "id": "ME", "arcs": [[-587, 371, 653, 654, 655, 656, 657, 658, 378, 139, 140, 141, 142]]}, {"type": "MultiPolygon", "properties": {"ct": [-84.61, 43.48], "gu": "USA"}, "id": "MI", "arcs": [[[-632, -631, 630, -474, 360, -481, -488, 659]], [[-620, -607, -483, -482, 660, 661, -473]]]}, {"type": "MultiPolygon", "properties": {"ct": [-152.72, 64.44], "gu": "USA"}, "id": "AK", "arcs": [[[24]], [[23]], [[27]], [[71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 123, 124, 125, 126, 127, 128, 129, 130, 131, 132, 133, 421, 662, 663, 424, 664, 426, 665, 428, 68, 69, 70]]]}]}}, "arcs": [[[6645, 8820], [-42, -45], [-112, 8], [-93, 31], [41, 53], [110, 32], [67, -41], [29, -38]], [[6627, 9119], [-35, -3], [-144, 7], [-21, 33], [155, -2], [54, -21], [-9, -14]], [[6403, 9266], [92, -41], [-21, -43], [-114, -24], [-62, 27], [-33, 44], [-7, 49], [100, -5], [45, -7]], [[7065, 8751], [-125, 15], [-204, 38], [-27, 64], [-9, 58], [-77, 52], [-159, 14], [-89, 37], [29, 48], [158, -8], [85, -37], [152, 0], [66, -39], [-17, -44], [88, -27], [49, -28], [104, -5], [112, -10], [122, 26], [157, 10], [125, -8], [82, -45], [17, -48], [-48, -32], [-114, -25], [-99, 14], [-220, -18], [-158, -2]], [[5287, 9194], [108, -19], [-25, -35], [-144, -34], [-114, 38], [63, 38], [112, 12]], [[5310, 9270], [100, -24], [-94, -23], [-127, 1], [1, 16], [79, 36], [41, -6]], [[8747, 2178], [39, -10], [14, -24], [-20, -29], [-58, 0], [-45, -4], [-4, 50], [11, 18], [63, -1]], [[7878, 2175], [52, -10], [41, -29], [13, -32], [-54, -2], [-24, -20], [-43, 19], [-44, 43], [10, 27], [32, 8], [17, -4]], [[7517, 2728], [67, -9], [60, -1], [72, -40], [31, -43], [72, 13], [27, -27], [65, -73], [48, -53], [25, 1], [46, -24], [-5, -33], [56, -5], [58, -48], [-9, -27], [-51, -15], [-52, -6], [-53, 9], [-110, -11], [52, 65], [-32, 31], [-49, 8], [-27, 34], [-18, 67], [-43, -5], [-72, 32], [-23, 24], [-101, 19], [-26, 23], [28, 29], [-75, 6], [-55, -61], [-32, -2], [-11, -29], [-38, -12], [-33, 11], [40, 36], [17, 43], [35, 26], [39, 23], [58, 11], [19, 13]], [[9568, 6037], [-41, -74], [-51, -103], [51, 39], [51, -25], [-27, -41], [68, -32], [36, 29], [77, -37], [-24, -86], [54, 20], [9, -62], [24, -73], [-32, -104], [-35, -4], [-50, 22], [16, 96], [-21, 15], [-89, -102], [-46, 4], [54, 56], [-74, 28], [-82, -7], [-150, 4], [-12, 35], [48, 41], [-33, 32], [65, 71], [79, 187], [48, 67], [66, 41], [36, -5], [-15, -32]], [[7393, 7659], [84, -40], [88, -37], [7, -56], [57, 9], [55, -39], [-69, -37], [-119, 29], [-44, 53], [-76, -63], [-109, -61], [-27, 69], [-104, -11], [67, 58], [10, 92], [26, 108], [56, -9], [14, -52], [39, 18], [45, -31]], [[7786, 8511], [73, 47], [171, -60], [105, -56], [10, -51], [143, 27], [80, -75], [186, -47], [67, -47], [73, -110], [-141, -55], [181, -77], [122, -26], [110, -108], [121, -8], [-24, -82], [-135, -137], [-94, 50], [-121, 114], [-100, -15], [-9, -68], [81, -68], [104, -54], [32, -31], [50, -117], [-27, -84], [-97, 32], [-193, 94], [109, -102], [80, -71], [13, -41], [-209, 47], [-165, 69], [-93, 57], [27, 33], [-115, 60], [-112, 58], [1, -35], [-222, -18], [-65, 40], [50, 87], [145, 2], [158, 15], [-26, 42], [27, 58], [100, 115], [-21, 52], [-30, 40], [-118, 57], [-156, 40], [50, 30], [-82, 73], [-68, 7], [-60, 40], [-41, -35], [-140, -15], [-280, 27], [-162, 34], [-125, 18], [-64, 41], [80, 54], [-109, 0], [-24, 119], [59, 106], [79, 48], [198, 31], [-56, -76], [60, -73], [71, 94], [195, 49], [132, -122], [-11, -77], [152, 34]], [[6576, 8721], [160, -4], [147, -29], [-115, -105], [-91, -22], [-83, -88], [-88, 4], [-48, 103], [1, 59], [41, 50], [76, 32]], [[6120, 8553], [-85, 77], [91, 57], [92, -25], [137, 15], [20, -34], [-72, -57], [116, -50], [-13, -106], [-127, -46], [-74, 10], [-53, 45]], [[6152, 8439], [-190, 88], [0, 40]], [[5962, 8567], [158, -14]], [[5552, 8922], [68, 20], [81, -5], [13, -58], [-46, -56], [-261, -18], [-194, -51], [-117, -3], [-10, 39], [160, 52], [-347, -14], [-108, 21], [105, 115], [72, 33], [217, -40], [136, -70], [135, -9], [-110, 113], [70, 43], [80, -14], [26, -56], [30, -42]], [[4395, 8954], [131, 88], [158, 76], [118, -1], [105, 17], [-10, -91], [-59, -40], [-72, -6], [-143, -50], [-123, -18], [-105, 25]], [[4497, 8758], [109, -25], [197, -6], [75, -34], [82, -50], [-96, -29], [-189, -83], [-95, -82]], [[4580, 8449], [0, -52], [-203, -56]], [[4377, 8341], [-41, 51], [-177, 62]], [[4159, 8454], [33, 50], [53, 86]], [[4245, 8590], [67, 77], [-75, 72], [260, 19]], [[1041, 7103], [61, -10], [7, -45], [-47, -19], [-50, 22], [-47, 32], [76, 20]], [[2059, 6819], [51, -8], [33, -36], [-67, -56], [-77, -45], [-39, 30], [-12, 55], [70, 42], [41, 18]], [[3637, 6357], [74, 9], [-23, -133], [67, -95], [-31, 0], [-46, 54], [-28, 54], [-39, 37], [-14, 52], [4, 37], [36, -15]], [[4198, 5876], [38, -56], [77, -49], [32, -65], [-39, -16], [-126, 54], [-23, 41], [-69, 41], [-14, 34], [-79, 21], [-30, 64], [7, 27], [81, -25], [47, -18], [72, -13], [26, -40]], [[636, 7503], [47, -22], [48, 12], [63, -31], [76, -16], [-6, -13], [-59, -25], [-58, 26], [-29, 21], [-68, -7], [-19, 11], [5, 44]], [[5731, 9329], [151, -16], [208, -43], [59, -56], [30, -49], [-126, 13], [-127, 38], [-171, 4], [74, 35], [-93, 29], [-5, 45]], [[7895, 1020], [40, -4], [58, 82], [31, 13], [1, 39], [14, 99], [44, 55], [49, 2], [6, 25], [60, -10], [61, 59], [30, 26], [37, 57], [27, -7], [20, -31], [-15, -40], [-2, -27], [-45, -14], [25, -54], [-1, -61], [-34, -68], [29, -94], [33, 8], [18, 85], [-24, 41], [-4, 89], [96, 48], [-11, 56], [27, 37], [28, -83], [54, -2], [50, -65], [3, -39], [69, -1], [82, 12], [44, -53], [59, -15], [43, 37], [1, 30], [95, 7], [92, 2], [-65, -35], [26, -56], [62, -9], [58, -57], [12, -95], [40, 3], [30, -28], [51, -43], [48, -77], [2, -60], [29, -3], [41, -57], [30, -41], [92, -24], [9, 21], [62, 9], [83, -32], [26, -13], [56, -28], [82, -99], [12, -48]], [[9871, 489], [28, -4], [18, -56]], [[9917, 429], [43, -205], [39, -19]], [[9999, 116], [-54, -90], [18, -26]], [[7854, 850], [32, -63], [12, -99], [-17, -31], [18, -108], [-15, -68], [28, -28], [-29, -62], [-32, -74], [-39, -8], [-18, -42], [4, -59], [-29, -9], [11, -37], [-53, -46], [-42, -26], [5, -48], [-16, -42]], [[7854, 850], [-25, 34], [-17, 63], [19, 32], [-19, 8], [-14, 39], [-39, 32], [-33, -7], [-16, -41], [-31, -30], [-17, -4], [-7, -24], [36, -64], [-21, -15], [-11, -18], [-36, -6], [-13, 71], [-10, -20], [-25, 7], [-16, 47], [-31, 8], [-20, 13]], [[7508, 975], [-17, 0], [-16, 0]], [[7475, 975], [-3, -25], [-9, 18], [-41, 26], [-16, 24], [9, 21], [-3, 26], [-21, 28], [-30, 23], [-27, 15], [-5, 35], [-20, 21], [5, -35], [-16, -28], [-17, 33], [-25, 12], [-10, 23], [0, 36], [10, 37]], [[7256, 1265], [-11, 8], [-10, 9]], [[7235, 1282], [17, 22], [-26, 37], [-36, 48], [-17, 40], [-33, 37], [-38, 53], [8, 18], [13, -18], [6, 8], [-14, 37]], [[7115, 1564], [-11, 5], [-12, 5]], [[7092, 1574], [-8, -27], [-45, 2], [-28, 11], [-31, 23], [-43, 8], [-22, 25], [-39, 20], [-48, 2], [-36, 24], [-41, 48], [-87, 127], [-40, 38], [-62, 31], [-43, -9], [-62, -44], [-39, -12], [-54, 31], [-58, 23], [-72, 54], [-57, 16], [-88, 55], [-64, 56], [-19, 32], [-43, 7], [-79, 37]], [[5884, 2152], [-32, 53], [-83, 67]], [[5769, 2272], [-38, 74], [-19, 58], [26, 11], [-8, 34], [18, 30], [0, 41], [-26, 53], [-7, 47], [-25, 59], [-68, 117], [-78, 92], [-37, 73], [-66, 48], [-14, 29], [12, 73], [-40, 27], [-45, 57], [-19, 82], [-42, 10], [-44, 62], [-36, 57], [-4, 37], [-41, 89], [-27, 90], [1, 45], [-56, 46], [-25, -5], [-44, 33], [-13, -48], [13, -56], [8, -89], [26, -48], [57, -81], [13, -28], [11, -8], [11, -41], [13, 2], [16, -76], [23, -30], [16, -42], [49, -60], [25, -109], [23, -51], [21, -56], [5, -62]], [[5334, 2858], [37, -4], [31, -53]], [[5402, 2801], [28, -52], [-2, -22], [-33, -43], [-13, 1], [-21, 71], [-50, 67], [-55, 57], [-40, 30], [3, 86], [-12, 64], [-37, 36], [-52, 53], [-11, -15], [-19, 30], [-47, 29], [-46, 68], [6, 9], [32, -7], [28, 44], [3, 53], [-59, 84], [-45, 33], [-29, 73], [-28, 77], [-36, 94], [-31, 106]], [[4836, 3827], [-13, 60], [-50, 68], [-36, 14], [-8, 34], [-43, 6], [-28, 32]], [[4658, 4041], [-71, 11]], [[4587, 4052], [-20, 19]], [[4567, 4071], [-9, 65]], [[4558, 4136], [-75, 118]], [[4483, 4254], [-64, 163]], [[4419, 4417], [3, 28]], [[4422, 4445], [-34, 39]], [[4388, 4484], [-60, 98]], [[4328, 4582], [-10, 96]], [[4318, 4678], [-41, 64]], [[4277, 4742], [17, 98]], [[4294, 4840], [-3, 101]], [[4291, 4941], [-25, 90], [30, 111]], [[4296, 5142], [10, 106], [9, 107]], [[4315, 5355], [-14, 158]], [[4301, 5513], [-24, 101], [-23, 54]], [[4254, 5668], [10, 23]], [[4264, 5691], [111, -40]], [[4375, 5651], [41, -111], [19, 31]], [[4435, 5571], [-12, 97]], [[4423, 5668], [-27, 96]], [[4396, 5764], [-10, 0], [-149, 116], [-55, 51], [-139, 48], [-43, 105], [11, 72], [-98, 50], [-14, 95], [-93, 85], [-1, 61]], [[3805, 6447], [-43, 44]], [[3762, 6491], [-68, 37], [-21, 103]], [[3673, 6631], [-99, 95], [-42, 111]], [[3532, 6837], [-74, 8]], [[3458, 6845], [-122, 3]], [[3336, 6848], [-90, 34], [-159, 122]], [[3087, 7004], [-74, 22]], [[3013, 7026], [-134, 42]], [[2879, 7068], [-107, -10], [-151, 54], [-92, 50], [-85, -25], [16, -81]], [[2460, 7056], [-43, -8]], [[2417, 7048], [-89, -25], [-67, -39], [-86, -25], [-11, 69]], [[2164, 7028], [35, 115], [82, 37]], [[2281, 7180], [-21, 29], [-98, -65], [-53, -79]], [[2109, 7065], [-111, -83]], [[1998, 6982], [57, -57]], [[2055, 6925], [-73, -85]], [[1982, 6840], [-83, -49], [-77, -36], [-19, -52], [-120, -61]], [[1683, 6642], [-24, -55]], [[1659, 6587], [-90, -50]], [[1569, 6537], [-53, 9]], [[1516, 6546], [-72, -33]], [[1444, 6513], [-78, -40]], [[1366, 6473], [-64, -40], [-132, -33], [-12, 20], [84, 55], [76, 36], [82, 64], [95, 14]], [[1495, 6589], [38, 48]], [[1533, 6637], [106, 70]], [[1639, 6707], [18, 24]], [[1657, 6731], [56, 41], [14, 89], [39, 70], [-89, -36], [-25, 21], [-41, -43], [-51, 59], [-20, -42]], [[1540, 6890], [-29, 59]], [[1511, 6949], [-77, -47]], [[1434, 6902], [-47, 0]], [[1387, 6902], [-7, 70]], [[1380, 6972], [14, 43]], [[1394, 7015], [-49, 42]], [[1345, 7057], [-100, -23]], [[1245, 7034], [-65, 56], [-53, 28], [0, 66], [-60, 51]], [[1067, 7235], [30, 67]], [[1097, 7302], [63, 66], [27, 60], [63, 9], [52, -19]], [[1302, 7418], [62, 57]], [[1364, 7475], [56, -10]], [[1420, 7465], [59, 36]], [[1479, 7501], [-14, 54]], [[1465, 7555], [-43, 21], [56, 45], [-47, -1], [-81, -26]], [[1350, 7594], [-24, -26], [-60, 26]], [[1266, 7594], [-109, -13]], [[1157, 7581], [-113, 28], [-32, 48], [-98, 68]], [[914, 7725], [109, 49]], [[1023, 7774], [171, 58]], [[1194, 7832], [63, 0]], [[1257, 7832], [-10, -59]], [[1247, 7773], [162, 5]], [[1409, 7778], [-62, 72]], [[1347, 7850], [-95, 45]], [[1252, 7895], [-55, 59]], [[1197, 7954], [-73, 50]], [[1124, 8004], [-106, 37]], [[1018, 8041], [43, 62]], [[1061, 8103], [137, 4]], [[1198, 8107], [97, 53], [18, 58], [78, 56]], [[1391, 8274], [75, 13]], [[1466, 8287], [146, 52]], [[1612, 8339], [71, -8]], [[1683, 8331], [118, 63]], [[1801, 8394], [117, -25], [55, -53]], [[1973, 8316], [34, 23], [130, -7], [-4, -27], [118, -20], [78, 12], [162, -37], [148, -11], [59, -16], [103, 19]], [[2801, 8252], [116, -35]], [[2917, 8217], [84, -16]], [[3001, 8201], [143, -29], [121, -56], [81, -11], [67, 49], [93, 37], [114, -15], [116, 52], [126, 29], [52, -48], [58, 27], [17, 55], [53, -12], [130, -106], [103, 80], [10, -89], [94, 19], [30, 34], [93, -6], [117, -50], [180, -43], [106, -20], [76, 8], [103, -60], [-108, -58], [139, -25], [208, 13], [65, 21], [82, -71], [84, 60], [-79, 50], [50, 40], [94, 6], [61, 11], [62, -28], [78, -64], [85, 10], [136, -53], [120, 18], [112, -2], [-9, 73], [68, 20], [120, -40], [-1, -111], [49, 94], [62, -3], [35, 118], [-83, 73], [-89, 47], [6, 130], [91, 85], [101, -18], [78, -52]], [[6701, 8390], [104, -133], [-68, -58]], [[6737, 8199], [143, -24], [0, -120], [103, 92], [92, -75], [-23, -88], [74, -79], [81, 85], [56, 101], [4, 130], [109, -9], [114, -18], [103, -58], [5, -58], [-58, -63], [55, -63], [-10, -57], [-151, -83], [-107, -18], [-79, 36], [-23, -59], [-74, -100], [-23, -51], [-89, -80], [-110, -7], [-61, -50], [-5, -77], [-89, -14], [-94, -96], [-84, -132], [-30, -93], [-4, -137], [113, -19], [35, -110], [36, -90], [107, 24], [143, -51], [77, -45], [55, -56], [97, -32], [81, -49], [127, -7], [84, -12], [-13, -101], [24, -119], [56, -131], [114, -112], [59, 38], [42, 121], [-40, 186], [-54, 62], [123, 55], [87, 82], [43, 82], [-7, 79], [-52, 100]], [[7899, 6829], [-93, 88], [90, 124]], [[7896, 7041], [-33, 106], [-26, 184], [54, 27], [132, -32], [79, -11], [63, 31], [72, -40], [95, -68], [23, -46], [137, -9], [-2, -99], [25, -149], [71, -18], [55, -69], [112, 65], [73, 130], [51, 55], [60, -105], [100, -150], [85, -142], [-30, -73], [102, -67], [69, -67], [123, -30], [49, -38], [30, -100], [60, -15], [31, -45], [6, -132], [-56, -44], [-55, -41], [-127, -42], [-97, -97], [-130, -19], [-164, 25], [-116, 1], [-79, -8], [-65, -85], [-98, -52], [-111, -156], [-88, -108], [65, 19], [124, 155], [161, 98], [115, 12], [68, -58], [-73, -79], [25, -127], [25, -89], [100, -58], [127, 17], [77, 132], [5, -86], [50, -42], [-95, -77], [-170, -70], [-77, -48], [-86, -85], [-58, 9], [-3, 100], [133, 97], [-123, -4], [-85, -14]], [[8681, 5310], [13, -39]], [[8694, 5271], [-82, -57]], [[8612, 5214], [-79, -40], [-81, -35]], [[8452, 5139], [-41, -70]], [[8411, 5069], [-13, -27]], [[8398, 5042], [-1, -62]], [[8397, 4980], [26, -62], [32, -3]], [[8455, 4915], [-9, 43]], [[8446, 4958], [24, -26]], [[8470, 4932], [-7, -34]], [[8463, 4898], [-52, -19]], [[8411, 4879], [-36, 2]], [[8375, 4881], [-57, -20]], [[8318, 4861], [-34, -6], [-45, -6]], [[8239, 4849], [-64, -34]], [[8175, 4815], [113, 22]], [[8288, 4837], [23, -22]], [[8311, 4815], [-108, -35]], [[8203, 4780], [-49, -1]], [[8154, 4779], [3, 15]], [[8157, 4794], [-24, -33]], [[8133, 4761], [23, -5]], [[8156, 4756], [-17, -85]], [[8139, 4671], [-56, -90]], [[8083, 4581], [-5, 30]], [[8078, 4611], [-17, 6]], [[8061, 4617], [-25, 29]], [[8036, 4646], [16, -63]], [[8052, 4583], [19, -21]], [[8071, 4562], [1, -44]], [[8072, 4518], [-25, -46]], [[8047, 4472], [-43, -94], [-7, 5], [24, 80]], [[8021, 4463], [-40, 45], [-9, 97], [-15, -50], [17, -75]], [[7974, 4480], [-51, 18]], [[7923, 4498], [53, -38]], [[7976, 4460], [3, -111]], [[7979, 4349], [22, -9], [8, -40]], [[8009, 4300], [11, -118]], [[8020, 4182], [-49, -87]], [[7971, 4095], [-79, -35]], [[7892, 4060], [-51, -69]], [[7841, 3991], [-38, -8]], [[7803, 3983], [-39, -43]], [[7764, 3940], [-11, -39]], [[7753, 3901], [-85, -77]], [[7668, 3824], [-43, -56]], [[7625, 3768], [-36, -69], [-12, -84]], [[7577, 3615], [13, -82]], [[7590, 3533], [26, -100]], [[7616, 3433], [34, -84]], [[7650, 3349], [1, -51]], [[7651, 3298], [36, -136]], [[7687, 3162], [-2, -79], [-4, -46]], [[7681, 3037], [-19, -72]], [[7662, 2965], [-23, -15], [-38, 15]], [[7601, 2965], [-12, 51]], [[7589, 3016], [-29, 27]], [[7560, 3043], [-41, 101]], [[7519, 3144], [-36, 90]], [[7483, 3234], [-11, 46]], [[7472, 3280], [16, 78]], [[7488, 3358], [-22, 65]], [[7466, 3423], [-60, 99], [-30, 18], [-77, -54]], [[7299, 3486], [-14, 6]], [[7285, 3492], [-38, 55], [-48, 29]], [[7199, 3576], [-87, -15]], [[7112, 3561], [-68, 13]], [[7044, 3574], [-59, -8]], [[6985, 3566], [-31, -18]], [[6954, 3548], [13, -31]], [[6967, 3517], [-1, -48], [17, -23]], [[6983, 3446], [-15, -16]], [[6968, 3430], [-29, 18]], [[6939, 3448], [-29, -23]], [[6910, 3425], [-55, 4], [-58, 62], [-67, -15]], [[6730, 3476], [-56, 28]], [[6674, 3504], [-48, -9]], [[6626, 3495], [-64, -27], [-70, -87]], [[6492, 3381], [-77, -51]], [[6415, 3330], [-42, -56]], [[6373, 3274], [-17, -53]], [[6356, 3221], [-1, -81]], [[6355, 3140], [4, -57]], [[6359, 3083], [14, -40]], [[6373, 3043], [-30, -103], [-13, -85], [-6, -157], [-7, -58], [13, -64], [24, -57], [15, -92], [51, -87], [18, -67], [31, -58], [81, -31], [32, -50], [67, 33], [59, 12], [58, 21], [48, 20], [49, 48], [18, 69], [7, 99], [13, 34], [52, 31], [81, 27], [68, -4], [47, 10], [19, -25], [-3, -56], [-41, -70], [-19, -72], [15, -20], [-12, -51], [-19, -92], [-20, 30], [-16, -2], [0, -17], [15, -1], [-1, -32], [-13, -50], [7, -19], [-8, -42], [5, -11], [-9, -59], [-15, -32], [-14, -3], [-15, -41], [25, -21], [6, 17]], [[7036, 1865], [26, -16], [5, -3]], [[7067, 1846], [17, 20], [22, 2], [7, -10], [12, 6], [36, -10], [35, 3], [25, 13], [9, 13], [24, -6], [19, -8], [20, 2], [15, 11], [35, -17], [13, -2], [23, -22], [22, -27], [28, -18], [20, -32], [-6, -11], [-4, -27], [8, -43], [-18, -40], [-8, -47], [-3, -52], [4, -30], [2, -53], [-11, -12], [-8, -50], [6, -31], [-16, -30], [4, -32], [11, -19]], [[7410, 1287], [5, -18], [15, -46]], [[7430, 1223], [30, -47], [36, -51]], [[7496, 1125], [27, -42], [-1, -25], [30, -5], [8, 9], [21, -28], [38, 8], [32, 30], [47, 24], [26, 35], [43, -7], [-3, -12], [43, -4], [34, -20], [25, -36], [29, -32]], [[8329, 2319], [10, 20]], [[8339, 2339], [16, 3], [44, -3]], [[8399, 2339], [45, -31], [20, 3], [14, -41], [43, 2], [-3, -35], [34, -4], [38, -44], [-28, -47], [-37, 25], [-35, -5], [-26, 6], [-14, -22], [-29, -7], [-12, 29], [-25, -17], [-31, -81], [-20, 19], [-4, 34], [-51, 20], [-36, -8], [-47, 8], [-36, -22], [-41, 37], [7, 38], [70, -17], [58, -9], [28, 26], [-35, 51], [1, 45], [-49, 18], [17, 33], [47, -5], [67, -19]], [[188, 8023], [-188, 90]], [[188, 8023], [202, -117], [-7, -73], [52, -30], [-17, 86], [208, -18], [151, -110], [-76, -51], [-126, -12], [-2, -115], [-31, -25], [-72, 4], [-59, 41], [-102, 34], [-17, 51], [-78, 19], [-88, -15], [-42, 41], [17, 44], [-92, -28], [35, -55], [-44, -50]], [[0, 8413], [10, 5], [65, -1], [111, -33], [-6, -16], [-80, -28], [-100, -8]], [[9101, 1266], [44, 15], [16, -4], [-3, -88], [-64, -13], [-14, 11], [22, 32], [-1, 47]], [[1892, 2362], [14, -10], [12, -16], [20, -41], [-2, -7], [-30, -25], [-25, -18], [-11, -20], [-19, 17], [2, 33], [-13, 43], [4, 13], [14, 19], [-6, 23], [5, 11], [6, -2], [29, -20]], [[1846, 2443], [-6, -15], [-26, -8], [-13, 25], [-9, 9], [-1, 8], [8, 10], [27, -11], [20, -18]], [[1788, 2491], [-3, -13], [-41, 4], [6, 14], [38, -5]], [[1690, 2555], [7, -8], [22, -39], [-4, -7], [-6, 2], [-27, 4], [-9, 27], [-3, 4], [20, 17]], [[1587, 2613], [2, -27], [-9, -12], [-26, 22], [4, 8], [11, 12], [18, -3]], [[7831, 2966], [23, -5], [27, -98], [0, -68], [-19, -6], [-19, 68], [-29, 34], [17, 75]], [[7770, 3151], [36, 10], [51, -4], [2, -30], [-84, -19], [-5, 43]], [[7862, 3181], [60, -53], [-13, -84], [-14, 15], [1, 62], [-34, 46], [0, 14]], [[8921, 5533], [27, -57], [56, -16], [71, 3], [-38, -48], [-28, -8], [-98, 50], [-19, 40], [29, 36]], [[8882, 5867], [27, 10], [101, -30], [79, -49], [2, -22], [-38, -2], [-99, 37], [-72, 56]], [[7667, 7303], [30, 36], [31, -3], [20, -24], [-30, -62], [-34, 10], [-20, 35], [3, 8]], [[7384, 7347], [58, 54], [105, -1], [-1, -23], [-90, -65], [-55, 3], [-17, 32]], [[8060, 7934], [-50, -35], [-87, -6], [-19, 58], [33, 66], [70, 16], [60, -33], [1, -50], [-8, -16]], [[6418, 8197], [70, -67]], [[6488, 8130], [-48, -42], [-104, 36], [-62, -13], [-105, 53], [67, 36], [54, 51], [82, -33], [46, -21]], [[5652, 8596], [86, -47], [48, -115], [24, -83], [129, -58], [139, -56], [-9, -51], [-126, -10], [49, -45], [-26, -43], [-139, 18], [-132, 32], [-90, -7], [-144, -40]], [[5461, 8091], [-195, -18], [-137, -11]], [[5129, 8062], [-41, 56], [-105, 32], [-69, -13], [-94, 93], [51, 12], [118, 20], [109, -5], [100, 21], [-149, 27], [-164, -9], [-109, 2], [-41, 43], [179, 47], [-119, -1], [-134, 31], [64, 88], [54, 47], [206, 71], [79, -22], [-39, -55], [171, 35], [107, -59], [87, 60], [70, -39], [63, -115], [39, 48], [-55, 121], [68, 17], [77, -19]], [[7709, 8564], [-85, 63], [3, 42], [37, 8], [176, -13], [133, -65], [7, -32], [-82, 3], [-83, 3], [-84, -16], [-22, 7]], [[6263, 9010], [6, 15], [58, -55], [3, -60], [-35, -87], [-127, -12], [-83, 18], [2, 69], [-126, -9], [-5, 91], [83, -4], [116, 40], [108, -6]], [[6460, 9482], [53, 36], [79, 8], [-34, 27], [179, 6], [98, -63]], [[6835, 9496], [256, -48]], [[7091, 9448], [61, -77], [92, -38], [-105, -35], [-142, -89], [-136, -8], [-160, 15], [-83, 48], [2, 43], [60, 31], [-140, -1], [-85, 39], [-49, 53], [54, 53]], [[6800, 9634], [115, 22], [90, 4], [150, 19], [114, 44], [95, -6], [83, -33], [58, 63], [102, 19], [138, 13], [235, 5], [41, -13], [222, 20], [166, -7], [167, -8], [206, -9], [165, -15], [141, -32], [-4, -31], [-188, -51], [-186, -24], [-69, -27], [167, 1], [-181, -71], [-126, -34], [-131, -96], [-159, -19], [-49, -24], [-233, -13], [106, -15], [-53, -21], [64, -58], [-73, -40], [-119, -33], [-37, -46], [-107, -35], [11, -27], [131, 5], [2, -29], [-206, -71], [-201, 33], [-226, -19], [-114, 15], [-145, 6], [-10, 56], [142, 27], [-38, 85], [47, 8], [206, -51], [-105, 76], [-125, 22], [62, 46], [137, 28], [22, 41], [-109, 46], [-33, 60], [211, -5], [60, -12], [120, 42], [-173, 14], [-269, -7], [-136, 39], [-64, 48], [-90, 35], [-17, 40]], [[9999, 7347], [-126, 138], [-39, 76], [-10, 106], [-107, 109], [28, 86], [-51, 42], [76, 137], [116, 44], [30, 49], [16, 92], [-88, -42], [-42, -17], [-69, -17], [-94, 39], [-5, 80], [30, 62], [71, 2], [157, -32], [-132, 75], [-69, 40], [-77, -16], [-64, 29], [86, 110], [-47, 44], [-61, 81], [-92, 125], [-98, 45], [1, 49], [-207, 69], [-163, 9], [-206, -5], [-187, -8], [-90, 37], [-133, 74], [202, 37], [155, 6], [-330, 31], [-173, 48], [11, 45], [291, 57], [282, 57], [29, 42], [-207, 43], [67, 47], [266, 82], [112, 12], [-32, 53], [182, 31], [237, 19], [236, 1], [84, -37], [204, 65], [30, -7]], [[5646, 8658], [103, 5], [58, -26], [-68, -78], [-120, 82], [27, 17]], [[5327, 5261], [-14, -5], [-14, 9], [-8, 7], [-8, 6], [-9, 4], [-9, 5], [-7, 16], [-1, 27], [16, 84], [-1, 23], [-2, 9], [-3, 8], [-3, 9], [-7, 17], [-4, 9], [-6, 9], [-7, 9], [-4, 15], [5, 20], [17, 21], [36, 35], [51, 38], [22, 6], [31, -18], [68, -5], [9, -3], [19, -7], [20, -6], [9, -3], [7, -2], [12, -3], [7, -2], [75, 24], [9, 8], [18, 16], [10, 8], [13, 1], [31, 3], [38, 4], [32, 3], [13, 2], [75, -12], [41, 7], [27, -4], [58, -48], [26, -11], [8, -2], [15, -2], [8, -2], [3, -9], [3, -9], [24, -21], [4, -14], [9, -26], [4, -14], [7, -13], [7, -12], [2, -42], [17, -93], [-1, -31], [-7, -21], [-14, -19], [1, -13], [5, -12], [7, -6], [6, -6], [9, -7], [18, -14], [9, -7], [23, -3], [5, -6], [10, -10], [5, -5], [-1, -9], [-2, -17], [-1, -9], [3, -12], [10, -10], [21, -22], [21, -22], [8, -12], [-1, -3], [1, -1]], [[6261, 5058], [20, -15], [29, -9], [6, 5], [18, 0], [28, 1], [20, -15], [21, -10]], [[6403, 5015], [3, -9], [7, -6]], [[6413, 5000], [13, -2]], [[6426, 4998], [3, -12], [5, -19], [0, -10], [14, -22], [5, -19], [0, -27], [6, -4], [5, -14], [8, -34], [2, -21]], [[6474, 4816], [-2, -21], [5, -21]], [[6477, 4774], [1, -10], [13, -18], [12, -15], [10, -25]], [[6513, 4706], [18, -16], [10, 0], [2, -17], [-10, -22], [5, -11], [10, -25], [20, -11]], [[6568, 4604], [110, 21], [14, -5], [12, -28], [24, -19], [18, -25], [19, -6], [46, 7], [43, -4], [35, 19], [6, 0], [11, 1], [5, -1], [-3, -14], [-2, -11]], [[6906, 4539], [-7, -17], [-5, -21], [11, -18], [15, -16], [9, -1], [20, -25], [8, -4], [5, -28], [-2, -18], [9, -29], [9, 3], [13, -18]], [[6991, 4347], [-2, -12], [1, -18], [-12, -10], [-17, -13]], [[6961, 4294], [-2, -11], [-5, -17], [-6, -28]], [[6948, 4238], [-1, 0], [-1, 0], [2, -5], [-1, -5]], [[6947, 4228], [-7, -17], [-14, -11], [-3, -20], [-12, -15], [1, -34], [-9, -11]], [[6903, 4120], [-1, -10], [-14, -8], [0, -17], [-11, -32], [-8, -7], [-14, -16], [-8, -24], [-17, -41], [-2, -28], [9, -31], [-4, -23]], [[6833, 3883], [6, -7], [-7, -17], [11, -24], [-3, -14], [10, -21], [-11, -12], [-3, -22], [-15, -18], [-7, -25], [-7, -28], [-10, -13], [4, -30]], [[6801, 3652], [4, -28], [14, -19], [17, -44], [10, -16], [16, -10], [57, -18], [7, -11], [5, -18], [37, -58]], [[8398, 5042], [0, -25], [-1, -37]], [[8463, 4898], [-23, -9], [-29, -10]], [[8239, 4849], [-59, -32], [-5, -2]], [[8175, 4815], [33, 6], [80, 16]], [[8154, 4779], [1, 4], [2, 11]], [[8157, 4794], [-23, -32], [-1, -1]], [[8156, 4756], [-7, -35], [-10, -50]], [[8139, 4671], [-26, -43], [-30, -47]], [[8061, 4617], [-10, 12], [-15, 17]], [[8036, 4646], [-1, 0]], [[8035, 4646], [10, -37], [7, -26]], [[8052, 4583], [18, -21], [2, -44]], [[7974, 4480], [-49, 18]], [[7925, 4498], [-2, 0]], [[7971, 4095], [-48, -21], [-31, -14]], [[7892, 4060], [-14, -19], [-37, -50]], [[7803, 3983], [-9, -9], [-30, -34]], [[7753, 3901], [-33, -30], [-52, -47]], [[7668, 3824], [-34, -44], [-9, -12]], [[7577, 3615], [10, -62], [3, -20]], [[7590, 3533], [4, -13], [22, -87]], [[7616, 3433], [30, -73], [4, -11]], [[7650, 3349], [0, -2], [1, -48]], [[7651, 3299], [0, -1]], [[7681, 3037], [-16, -60], [-3, -12]], [[7483, 3234], [-10, 44], [-1, 2]], [[7488, 3358], [0, 1]], [[7488, 3359], [-22, 64]], [[7299, 3486], [-13, 6], [-1, 0]], [[7285, 3492], [-37, 55], [-49, 29]], [[7112, 3561], [-65, 13], [-3, 0]], [[7044, 3574], [-7, -1], [-52, -7]], [[6985, 3566], [-32, -16]], [[6953, 3550], [2, -5], [12, -28]], [[6983, 3446], [-6, -6], [-9, -10]], [[6968, 3430], [-25, 15], [-4, 3]], [[6939, 3448], [-1, -2], [-28, -21]], [[6730, 3476], [0, 1], [-56, 27]], [[6492, 3381], [-73, -48], [-4, -3]], [[6415, 3330], [-9, -12], [-33, -44]], [[6356, 3221], [-1, -26], [0, -55]], [[6355, 3140], [2, -28], [2, -29]], [[6359, 3083], [0, -2], [14, -38]], [[6373, 3043], [-30, -3], [-54, 26], [-60, 36], [-22, 55], [-17, 83], [-45, 67]], [[6145, 3307], [-27, 69]], [[6118, 3376], [-38, 80], [-55, 47]], [[6025, 3503], [-63, -2]], [[5962, 3501], [-48, -93], [-64, 35]], [[5850, 3443], [-40, 36]], [[5810, 3479], [-19, 64], [-25, 62], [-46, 52]], [[5720, 3657], [-39, 37]], [[5681, 3694], [-28, 41]], [[5653, 3735], [-134, 0], [0, -48], [-61, 0]], [[5458, 3687], [-153, -1], [-175, 83]], [[5130, 3769], [-116, 57]], [[5014, 3826], [7, 23]], [[5021, 3849], [-98, -13]], [[4923, 3836], [-87, -9]], [[4658, 4041], [-41, 6], [-30, 5]], [[4587, 4052], [-17, 16], [-3, 3]], [[4296, 5142], [16, 175], [3, 38]], [[4254, 5668], [8, 18], [2, 5]], [[4264, 5691], [14, -5], [97, -35]], [[4435, 5571], [-3, 20], [-9, 77]], [[4396, 5764], [219, 0]], [[4615, 5764], [228, 0]], [[4843, 5764], [76, 0]], [[4919, 5764], [234, 0], [227, 0], [231, 0]], [[5611, 5764], [231, 0]], [[5842, 5764], [261, 0], [263, 0]], [[6366, 5764], [160, 0]], [[6526, 5764], [0, 45]], [[6526, 5809], [26, 1]], [[6552, 5810], [13, -65]], [[6565, 5745], [24, -20]], [[6589, 5725], [54, -7]], [[6643, 5718], [79, -19]], [[6722, 5699], [74, -36]], [[6796, 5663], [63, 15]], [[6859, 5678], [94, -30]], [[6953, 5648], [25, 1], [69, 33], [72, -43], [76, -45], [62, -39], [59, -38], [8, -31], [18, -11], [-5, -12]], [[7337, 5463], [21, -4]], [[7358, 5459], [15, 12], [4, -27], [15, -19], [21, 0], [12, -14], [-10, -21], [80, -56], [17, -106], [15, -103], [-22, -69]], [[7505, 5056], [-37, -65]], [[7468, 4991], [-16, -41]], [[7452, 4950], [-2, -12], [8, -17], [27, -19], [19, 0], [89, 63], [80, 19], [100, 58], [2, 12]], [[7775, 5054], [-7, 36]], [[7768, 5090], [-13, 23], [35, 19], [76, 0], [70, 0], [25, 46], [9, 9]], [[7970, 5187], [82, 85]], [[8052, 5272], [34, 22], [117, 0]], [[8203, 5294], [142, 1]], [[8345, 5295], [8, 29], [24, 5]], [[8377, 5329], [33, 19]], [[8410, 5348], [27, 53]], [[8437, 5401], [24, 92]], [[8461, 5493], [58, 89]], [[8519, 5582], [26, -31]], [[8545, 5551], [51, 20], [35, -34]], [[8631, 5537], [0, -161]], [[8631, 5376], [50, -66]], [[8694, 5271], [-22, -15], [-60, -42]], [[3694, 6529], [-21, 102]], [[3532, 6837], [-43, 5], [-31, 3]], [[3458, 6845], [-37, 1], [-85, 2]], [[3087, 7004], [-63, 19], [-11, 3]], [[3013, 7026], [-54, 17], [-80, 25]], [[2460, 7056], [-14, -3], [-29, -5]], [[2109, 7065], [-60, -45], [-51, -38]], [[1998, 6982], [40, -41], [17, -16]], [[2055, 6925], [-30, -34], [-43, -51]], [[1683, 6642], [-5, -12], [-19, -43]], [[1659, 6587], [-85, -48], [-5, -2]], [[1516, 6546], [-42, -20], [-30, -13]], [[1444, 6513], [-26, -14], [-52, -26]], [[1495, 6589], [29, 36], [9, 12]], [[1639, 6707], [2, 2], [16, 22]], [[1540, 6890], [-25, 52], [-4, 7]], [[1511, 6949], [-14, -9], [-63, -38]], [[1387, 6902], [-6, 63], [-1, 7]], [[1394, 7015], [-47, 40], [-2, 2]], [[1345, 7057], [-58, -13], [-42, -10]], [[1067, 7235], [24, 52], [6, 15]], [[1302, 7418], [17, 15], [45, 42]], [[1364, 7475], [29, -5], [27, -5]], [[1479, 7501], [-3, 9], [-11, 45]], [[1350, 7594], [-24, -25]], [[1326, 7569], [-29, 12], [-31, 13]], [[1266, 7594], [-40, -4], [-69, -9]], [[914, 7725], [87, 39], [22, 10]], [[1023, 7774], [39, 13], [132, 45]], [[1257, 7832], [-3, -18], [-7, -41]], [[1247, 7773], [109, 3], [53, 2]], [[1347, 7850], [-39, 19], [-56, 26]], [[1252, 7895], [-51, 55], [-4, 4]], [[1197, 7954], [-10, 7], [-63, 43]], [[1018, 8041], [9, 13], [34, 49]], [[1061, 8103], [124, 4], [13, 0]], [[1391, 8274], [60, 10], [15, 3]], [[1612, 8339], [62, -7], [9, -1]], [[1683, 8331], [18, 10], [100, 53]], [[2801, 8252], [77, -23], [39, -12]], [[2917, 8217], [7, -1], [77, -15]], [[3001, 8201], [-1, -437], [0, -670], [76, -3], [75, -33]], [[3151, 7058], [53, -51]], [[3204, 7007], [69, -78]], [[3273, 6929], [74, 66], [78, 38], [40, -61]], [[3465, 6972], [52, -48]], [[3517, 6924], [71, -53]], [[3588, 6871], [48, -84]], [[3636, 6787], [78, -134], [131, -75], [2, -75], [-42, -56]], [[3762, 6491], [-5, 3], [-63, 35]], [[5962, 8567], [-1, -37], [191, -91]], [[4245, 8590], [-60, -97], [-26, -39]], [[4377, 8341], [203, 58], [0, 50]], [[5129, 8062], [104, 8], [228, 21]], [[6488, 8130], [-47, 44], [-23, 23]], [[7091, 9448], [-126, 23], [-130, 25]], [[8399, 2339], [-60, 0]], [[0, 8113], [0, -469]], [[0, 8413], [0, -81]], [[9999, 7347], [0, -7142]], [[9917, 429], [-19, 65], [-27, -5]], [[7430, 1223], [-20, 64]], [[7067, 1846], [-8, 4], [-23, 15]], [[6967, 3517], [-14, 33]], [[7488, 3359], [-16, -79]], [[7687, 3162], [-36, 137]], [[7651, 3299], [-1, 50]], [[7979, 4349], [-3, 112], [-51, 37]], [[8052, 4583], [-17, 63]], [[8035, 4646], [26, -29]], [[8398, 5042], [10, 20], [44, 77]], [[7896, 7041], [-90, -123], [93, -89]], [[6737, 8199], [69, 58], [-105, 133]], [[1973, 8316], [-55, 54], [-117, 24]], [[1124, 8004], [74, -50], [54, -59]], [[1266, 7594], [60, -25]], [[2281, 7180], [-82, -36], [-35, -116]], [[3694, 6529], [68, -38]], [[4315, 5355], [-19, -213]], [[5402, 2801], [-31, 54], [-37, 3]], [[5769, 2272], [83, -66], [32, -54]], [[7092, 1574], [23, -10]], [[7235, 1282], [21, -17]], [[7475, 975], [33, 0]], [[7674, 0], [-7674, 0], [0, 9999], [9999, 0], [0, -308]], [[9999, 116], [0, -116], [-36, 0]], [[7973, 5190], [8, -16], [4, -46], [-57, -39], [-63, 10], [-61, 4], [-36, -13]], [[7970, 5187], [3, 3]], [[7775, 5054], [1, -11]], [[7776, 5043], [-66, -71]], [[7710, 4972], [-58, -34]], [[7652, 4938], [-40, -15], [-45, -33], [-56, -16], [-38, 6], [-48, 25]], [[7425, 4905], [27, 45]], [[7337, 5463], [-23, 5], [-16, 33], [-76, -11], [-68, -26], [-54, 45], [-44, 15], [25, 62], [-68, -39], [-62, -38], [-59, -30]], [[6892, 5479], [-47, 40]], [[6845, 5519], [-77, -24]], [[6768, 5495], [0, 17], [52, 50], [55, 47], [78, 39]], [[7637, 3151], [-17, 4], [1, 29], [17, -4], [-1, -29]], [[5216, 4863], [4, -23], [-3, -34], [-39, 32], [-2, 5], [-13, 54], [22, -22], [14, -12], [14, 10], [3, -10]], [[7312, 5387], [14, 15]], [[7326, 5402], [2, 0], [68, 10], [-38, 47]], [[7505, 5056], [-4, 48], [-14, 68], [-29, 12], [-47, -52], [-15, 1], [-4, 30], [42, 47], [7, 54], [-6, 53], [-57, 46], [-66, 24]], [[7312, 5387], [-10, -45], [-17, -12], [-14, -58], [-7, 39], [-31, -28], [-19, -39], [-20, -58], [-4, -50], [26, -73], [-3, -77], [-31, -58], [-15, -16]], [[7167, 4912], [-21, -13], [-26, -1], [-7, 8]], [[7113, 4906], [-21, 62], [-1, 31]], [[7091, 4999], [2, 29], [-9, 57], [14, 67], [18, 82], [40, 91], [-12, -1], [-56, -76], [-11, 14], [30, 43]], [[7107, 5305], [46, 76]], [[7153, 5381], [52, 10], [60, 24], [61, -13]], [[6768, 5495], [-21, -7], [1, -66], [-2, 1], [-19, -13], [-17, -12], [-11, -21], [17, -22], [-6, -30], [0, -32], [-3, -26], [23, -22], [9, -1], [25, -17], [8, -8], [6, -13], [19, -20], [26, -17], [3, -10], [0, -28], [2, -14]], [[6828, 5117], [-101, 2], [-112, 0], [-105, -2], [-84, 0]], [[6426, 5117], [1, 110], [-9, 113], [-14, 9], [-7, 18], [4, 16], [17, 13], [1, 17]], [[6419, 5413], [0, 22], [-4, 18], [-7, 19], [-4, 24], [0, 26], [-3, 7], [-3, 34], [-1, 16], [-1, 14], [-4, 24], [-9, 24], [-9, 22], [-1, 21], [-1, 23], [2, 15], [1, 14], [-7, 17], [-2, 11]], [[6366, 5764], [82, 0], [78, 0]], [[6526, 5764], [0, 31], [0, 14]], [[6526, 5809], [13, 1], [13, 0]], [[6552, 5810], [4, -20], [9, -45]], [[6589, 5725], [32, -4], [22, -3]], [[6722, 5699], [50, -24], [24, -12]], [[6796, 5663], [24, 6], [39, 9]], [[5838, 5764], [2, -215], [4, -143]], [[5844, 5406], [-4, -108]], [[5840, 5298], [-129, 2], [-138, -1], [-120, 1], [-151, -1], [0, -59], [-1, -5]], [[5301, 5235], [-9, 7], [-7, 16], [-9, 4], [-11, -24], [-17, -3], [-44, 7], [-2, -12], [-25, 5], [-14, -17], [-14, 31], [-9, 17], [-16, 3], [-4, 8], [-5, 31], [-14, 15], [-9, 37], [-9, 16], [-9, 3], [-8, -16], [-15, -14], [-14, 12], [-1, 30], [9, 7], [-6, 31], [7, 30], [9, 27], [-24, 1], [-19, 17], [-22, 36], [-13, 18], [-17, 11], [-15, 19], [0, 21], [-20, 32], [-6, 123]], [[5611, 5764], [227, 0]], [[6419, 5413], [-146, -6], [-125, 0], [-158, 0], [-146, -1]], [[5838, 5764], [4, 0]], [[5301, 5235], [2, -2], [0, -292]], [[5303, 4941], [-229, -1]], [[5074, 4940], [-231, 1]], [[4843, 4941], [2, 211], [6, 34], [-6, 15], [-14, 8], [0, 19], [11, 26], [16, 24], [11, 38], [11, 30], [7, 15], [-4, 18], [-13, 10], [-18, 22]], [[4852, 5411], [1, 21], [-7, 18], [-2, 164], [-1, 150]], [[4843, 5764], [50, 0], [26, 0]], [[4852, 5411], [-158, -1], [-28, -12], [-20, 5], [-15, -9], [-28, -13], [-35, 1], [-17, -9], [-17, -4], [-12, 6], [-27, 5], [-18, -4], [-29, -12], [-19, -1], [-18, 5], [-6, 16], [-2, 20], [-14, 22], [-22, 7], [-19, 11], [-24, 0], [-17, 1]], [[4307, 5445], [-6, 68]], [[4435, 5571], [-2, 13], [-10, 84]], [[4615, 5764], [128, 0], [100, 0]], [[5458, 4352], [0, -665]], [[5130, 3769], [-81, 40], [-35, 17]], [[5021, 3849], [10, 0], [9, 23], [-14, 16], [-3, 18], [-4, 21], [15, 27], [6, 54], [24, 24], [-15, 22], [-10, 22], [-6, 21], [-4, 16], [-2, 10]], [[5027, 4123], [5, 24], [-4, 23], [-2, 22], [0, 26], [-6, 16], [5, 14], [16, 0], [15, -8], [10, -5], [5, 18], [4, 4], [-1, 95]], [[5074, 4352], [124, 2], [148, -1], [112, -1]], [[4923, 3836], [-80, -8], [-7, -1]], [[4567, 4071], [0, 2], [-9, 63]], [[4558, 4136], [-45, 71], [-30, 47]], [[4419, 4417], [1, 9], [2, 19]], [[4422, 4445], [-33, 37], [-1, 2]], [[4388, 4484], [-26, 41], [-34, 57]], [[4318, 4678], [-15, 24], [-26, 40]], [[4277, 4742], [11, 67], [6, 31]], [[4291, 4941], [324, -1]], [[4615, 4940], [0, -353], [145, -159], [138, -155], [129, -150]], [[5996, 4706], [0, -182], [0, -172]], [[5996, 4352], [-74, 0]], [[5922, 4352], [-92, 0], [-131, 0], [-121, 0], [-120, 0]], [[5458, 4352], [-1, 471]], [[5457, 4823], [77, 0], [77, 0], [154, 0], [77, 1]], [[5842, 4824], [154, 0], [0, -115], [0, -3]], [[5074, 4940], [0, -588]], [[4615, 4940], [228, 0], [0, 1]], [[5922, 4352], [0, -58]], [[5922, 4294], [0, -309], [0, -221], [-71, 0], [-138, 0], [-70, 0], [1, -10], [9, -19]], [[4315, 5355], [-8, 90]], [[5303, 4941], [0, -115], [154, -3]], [[5840, 5298], [2, -240]], [[5842, 5058], [0, -234]], [[6948, 4238], [-1, -10]], [[6833, 3883], [-65, 4], [-84, -4], [-74, 0]], [[6610, 3883], [5, 67], [-18, 1], [-15, -2], [-4, 8]], [[6578, 3957], [2, 103], [2, 114], [-16, 124]], [[6566, 4298], [94, -1], [85, -1], [81, 0], [88, -7], [6, -14], [-9, -13], [-8, -13], [-5, -11], [50, 0]], [[6828, 5117], [1, -6], [10, -19], [-7, -9], [0, -24], [4, -11], [4, -19], [26, -11], [7, -18]], [[6873, 5000], [5, -9], [9, -6], [3, -13], [12, -9], [8, -10], [-4, -32], [-14, -26], [-5, -9], [-18, -7], [-26, -5], [-7, -21], [10, -9], [3, -18], [-10, -20], [-5, -18], [-20, -18], [-2, -21]], [[6812, 4749], [-10, 10], [-15, 19], [-84, -3], [-88, -1], [-69, 0], [-69, 0]], [[6477, 4774], [-6, 21], [3, 21]], [[6426, 4998], [0, 11], [-12, 13], [6, 19], [4, 19], [2, 13], [-10, 16], [0, 28], [10, 0]], [[5996, 4706], [134, 0], [100, 0], [177, 0], [106, 0]], [[6568, 4604], [-1, -126], [0, -126]], [[6567, 4352], [-68, 0], [-138, 0], [-139, 0], [-76, 0], [-77, 0], [-73, 0]], [[6566, 4298], [1, 54]], [[6812, 4749], [-6, -29], [7, -36], [12, -24], [15, -20], [18, -16], [7, -5], [6, -22], [1, -20], [9, -5], [15, 8], [14, -19], [-4, -22]], [[5842, 5058], [150, 0], [115, 0], [154, 0]], [[6403, 5015], [3, -10], [7, -5]], [[6578, 3957], [-33, 23], [-22, 12], [-17, -8], [-27, 2], [-16, -1], [-14, -9], [-12, -5], [-12, 6], [-25, -7], [-12, 20], [-12, -17], [-21, 8], [-22, 18], [-23, -12], [-10, 28], [-36, -2], [-22, 6], [-26, 8], [-12, 25], [-20, -8], [-12, 9], [-19, 13], [0, 112], [0, 116], [-77, 0], [-77, 0], [-77, 0]], [[6801, 3652], [67, -3], [73, -1], [-2, -19], [-5, -20], [5, -14], [10, -14], [2, -20], [2, -11]], [[6953, 3550], [1, -2]], [[6626, 3495], [-5, 13], [8, 17], [11, 16], [1, 23], [-6, 8], [7, 28], [5, 13], [7, 43], [-7, 16], [-9, 27], [-6, 27], [-5, 18], [-12, 14], [-5, 125]], [[6415, 3330], [-40, -54], [-2, -2]], [[6356, 3221], [-1, -40], [0, -41]], [[6359, 3083], [8, -24], [6, -16]], [[6145, 3307], [-20, 51], [-7, 18]], [[6025, 3503], [-29, -1], [-34, -1]], [[5850, 3443], [-13, 12], [-27, 24]], [[5720, 3657], [-4, 3], [-35, 34]], [[5681, 3694], [-25, 37], [-3, 4]], [[8192, 4947], [59, -2], [71, -3]], [[8322, 4942], [1, -64], [-5, -17]], [[8239, 4849], [-14, -8], [-45, -23]], [[8180, 4818], [-1, 3], [-2, 15], [16, 11], [-6, 10], [5, 90]], [[8192, 4947], [16, 81]], [[8208, 5028], [64, -2]], [[8272, 5026], [93, -1], [8, 12], [16, 8], [9, -3]], [[8455, 4915], [-3, 15], [-6, 28]], [[8470, 4932], [-4, -21], [-3, -13]], [[8463, 4898], [-40, -15], [-12, -4]], [[8375, 4881], [-3, 18], [-12, 14], [-5, 31], [-33, -2]], [[8272, 5026], [-6, 12], [6, 16], [1, 31], [3, 7], [2, 28], [9, 23], [6, 10], [9, 28], [2, 19], [3, 12], [14, 5], [18, 14], [3, 15], [-6, 17], [9, 32]], [[8377, 5329], [11, -216], [-3, -11], [14, -18], [4, -16], [8, 1]], [[8375, 4881], [-7, -2], [-50, -18]], [[8208, 5028], [4, 97], [-12, 1], [-1, 4], [5, 18], [-7, 30], [8, 25], [-5, 18], [-1, 34], [3, 16], [1, 23]], [[8203, 5294], [75, 1], [67, 0]], [[7063, 4117], [97, 0], [99, -1]], [[7259, 4116], [20, -146], [18, -117], [11, -37], [7, -21], [-13, -21], [-4, -37], [4, -22], [-2, -21], [-2, -20], [5, -15], [4, -13]], [[7307, 3646], [-157, -1], [-44, -7], [-2, -9], [18, -28], [-4, -24], [-6, -16]], [[7044, 3574], [-2, 180], [13, 188], [14, 152], [-6, 23]], [[7307, 3646], [11, -32], [78, -6], [124, -17], [6, -21], [10, 11], [0, 41], [9, 4], [16, -9], [16, -2]], [[7601, 2965], [-5, 22], [-7, 29]], [[7560, 3043], [-19, 47], [-22, 54]], [[7199, 3576], [-49, -8], [-38, -7]], [[7259, 4116], [59, -1], [41, 1]], [[7359, 4116], [96, -1]], [[7455, 4115], [-9, -10], [-12, -22], [21, -20], [13, -7], [15, -37], [9, -21], [27, -28], [5, -15], [18, -19], [9, -28], [25, -23], [5, -27], [5, -13], [-3, -9], [14, -13], [8, -22], [0, -22], [7, -5], [13, -6]], [[6903, 4120], [76, 0], [84, -3]], [[7455, 4115], [8, 3], [41, 20], [71, -1], [36, -5], [1, -10], [7, 7], [12, -20], [0, -13], [86, -1], [86, -112]], [[6873, 5000], [79, 0], [80, 0], [59, -1]], [[7113, 4906], [0, -136], [-1, -136], [-8, -33], [6, -8], [4, -21], [0, -15], [-7, -8], [-5, -19], [-16, -26], [-11, -31], [-3, -24]], [[7072, 4449], [1, -9], [-9, -16], [7, -11], [-14, -9], [-17, -10], [3, -25], [-10, -10], [-19, 11], [-20, 6], [-6, -11], [3, -18]], [[7167, 4912], [82, -1], [73, 1], [0, -9]], [[7322, 4903], [0, -107], [-1, -114], [0, -82]], [[7321, 4600], [-5, -5], [6, -24], [-3, -9], [-13, 0], [-12, -11], [-18, 5], [-2, -23], [-11, -9], [-10, -20], [-11, -3], [-17, -35], [-15, 10], [-5, 14], [-13, -23], [-9, -13], [-16, 13], [-18, -11], [-6, -11], [-24, 18], [-16, -13], [-20, 9], [-1, -13], [-10, 3]], [[7321, 4600], [26, -2], [13, -12], [21, -26], [16, -9], [12, -9], [18, 3], [14, -7], [16, 7], [15, 2], [6, -17], [14, -11]], [[7492, 4519], [2, -11], [-1, -25], [9, -18], [4, -18], [11, -15], [8, -14], [15, -2]], [[7540, 4416], [-8, -9], [-23, -26], [-24, -14], [-2, -10], [-8, -12], [-20, -13], [-1, -1], [-1, -1], [-6, -11], [-12, -5]], [[7435, 4314], [-4, -2], [-22, -7]], [[7409, 4305], [-52, -3], [-68, 5], [-22, -2], [-44, 3], [-45, 1], [-42, 1], [-48, -3], [-2, 5], [-15, 0], [0, -19], [-110, 1]], [[7359, 4116], [2, 25], [16, 7], [6, 13], [10, 14], [16, 3], [18, 5], [18, 11], [7, 10], [15, 9], [0, 9], [19, 16], [6, -11], [28, 23], [14, -3], [12, 20], [15, 5], [-1, 17], [2, 15]], [[7562, 4304], [-30, -2]], [[7562, 4304], [130, -5], [153, -1], [82, 1], [73, 1], [9, 0]], [[8009, 4300], [3, -28], [8, -90]], [[8020, 4182], [-28, -51], [-21, -36]], [[7322, 4903], [39, 1], [35, 0], [29, 1]], [[7652, 4938], [0, -157]], [[7652, 4781], [-11, -6], [3, -15], [-3, -27], [-8, -31], [-8, -25], [-1, -12], [-21, -27], [-9, -6], [-10, -3], [-10, 2], [-17, -20], [-3, -21], [-2, -11], [-7, -5], [-1, 13], [-10, 3], [-11, -26], [-1, -26], [-11, -16], [-19, -3]], [[7409, 4305], [114, -4], [9, 1]], [[7540, 4416], [3, -20], [7, -9], [2, -2], [12, -10], [17, 11], [7, 4], [9, -9], [24, 9], [5, 1], [1, 7], [0, 4], [9, -4], [9, 8], [1, 0], [10, -2], [13, 10], [1, 6], [0, 4], [-1, 14], [10, 21], [15, 15], [4, 17], [7, 12], [6, 9], [8, 26], [10, -9], [11, -9], [11, 5], [4, 11], [8, 14], [5, 11], [3, 6], [6, -5], [11, 15], [9, 9], [6, 6], [3, 4], [7, 8], [5, 25], [1, 6], [36, -29], [3, -2], [9, 22]], [[7867, 4626], [2, -1], [9, -3], [10, -9], [-4, -10], [-1, -3], [16, -7], [14, -13]], [[7913, 4580], [6, -10], [0, -7]], [[7919, 4563], [-1, -10], [-3, -2], [-10, -9], [-9, -26], [10, -6], [13, 5], [3, -13], [1, -4]], [[7923, 4498], [52, -37], [1, -1]], [[8021, 4463], [8, 7], [18, 2]], [[6845, 5519], [46, -39]], [[6891, 5480], [1, -1]], [[6892, 5479], [5, 2], [12, -4], [6, -21], [67, -21], [45, -21], [21, 0], [15, -2], [5, -19], [18, -8], [7, -16], [-5, -10], [-4, -19], [17, -1], [-5, -19], [10, -14], [0, 1], [2, -1]], [[7108, 5306], [-1, -1]], [[7652, 4781], [0, -108], [80, 0]], [[7732, 4673], [-1, -60], [12, 10], [13, 14], [15, 5], [11, 12], [22, -5], [9, 9], [14, 9], [24, -9], [10, -17], [6, -15]], [[8072, 4523], [-51, 0], [-5, 150]], [[8016, 4673], [5, 9], [7, 5], [17, -6]], [[8045, 4681], [-12, -12], [3, -23]], [[8071, 4562], [1, -39]], [[7913, 4580], [6, 6], [10, -13], [-10, -10]], [[7732, 4673], [71, -1], [24, 1], [55, 0], [66, 0], [68, 0]], [[8072, 4523], [0, -5]], [[8045, 4681], [16, 11], [5, 7], [18, 16], [10, 13], [-24, 30], [-1, 13], [-9, 3], [1, 19], [9, 15], [-4, 15], [12, 10], [13, 27], [10, 5]], [[8101, 4865], [59, -47], [-3, -24]], [[8139, 4671], [-11, -18], [-45, -72]], [[8078, 4611], [-4, 1], [-13, 5]], [[8061, 4617], [-12, 14], [-13, 15]], [[8180, 4818], [-5, -3]], [[8311, 4815], [-76, -25], [-32, -10]], [[8101, 4865], [-13, 8], [-13, 8], [-5, 17], [2, 13], [-9, 11], [-17, 19], [-104, 0], [-112, 0], [-120, 0], [0, 32]], [[7710, 4973], [66, 70]], [[7973, 5190], [79, 82]], [[7710, 4972], [0, 1]], [[8410, 5348], [13, 26], [14, 27]], [[8437, 5401], [9, 35], [15, 57]], [[8461, 5493], [39, 59], [19, 30]], [[8519, 5582], [20, -24], [6, -7]], [[8545, 5551], [52, 20], [34, -34]], [[8631, 5537], [0, -96], [0, -65]], [[7153, 5381], [-45, -75]], [[7505, 5056], [-14, -24], [-23, -41]], [[7468, 4991], [-9, -22], [-7, -19]], [[3151, 7058], [46, -44], [7, -7]], [[3204, 7007], [1, -1], [68, -77]], [[3465, 6972], [31, -28], [21, -20]], [[3588, 6871], [39, -70], [9, -14]]], "transform": {"scale": [0.013001300130013002, 0.008500850085008501], "translate": [-180, 0]}, "bbox": [-180, 0, -50, 85]}