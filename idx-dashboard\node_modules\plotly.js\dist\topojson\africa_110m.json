{"type": "Topology", "objects": {"coastlines": {"type": "GeometryCollection", "geometries": [{"type": "LineString", "arcs": [0, 1, 2, 3, 4]}, {"type": "LineString", "arcs": [5]}, {"type": "LineString", "arcs": [6]}, {"type": "LineString", "arcs": [7]}, {"type": "LineString", "arcs": [8]}, {"type": "LineString", "arcs": [9]}, {"type": "LineString", "arcs": [10]}, {"type": "LineString", "arcs": [11]}, {"type": "LineString", "arcs": [12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, -12, 68, 69, 70, 71, 72, 73, 74]}, {"type": "LineString", "arcs": [75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 123, 124, 125, 126, 127, 128, 129, 130, 131, 132, 133, 134, 135, 136, 137, 138, 139, 140, 141, 142, 143, 144, 145, 146, 147, 148, 149, 150, 151, 152, 153, 154, 155, 156, 157, 158, 159, 160, 161, 162, 163, 164, 165, 166, 167, 168, 169, 170, 171, 172, 173, 174, 175, 176, 177, 178, 179, 180, 181, 182, 183, 184, 185, 186, 187, 188, 189, 190, 191, 192, 193, 194, 195, 196, 197, 198, 199, 200, 201, 202, 203, 204, 205, 206, 207, 208, 209, 210, 211, 212, 213, 214, 215, 216, 217, 218, 219, 220, 221, 222, 223, 224, 225, 226, 227, 228, 229, 230, 231, 232, 233, 234, 235, 236, 237, 238, 239, 240, 241, 242, 243]}, {"type": "LineString", "arcs": [244, 245, 246]}]}, "land": {"type": "GeometryCollection", "geometries": [{"type": "Polygon", "arcs": [[6]]}, {"type": "Polygon", "arcs": [[252, 253, 254, 86, 255, 256, 89, 257, 258, 91, 92, 93, 259, 95, 260, 97, 261, 99, 100, 101, 102, 262, 263, 105, 264, 107, 265, 109, 266, 111, 267, 113, 268, 115, 269, 117, 270, 119, 271, 272, 122, 273, 274, 125, 275, 127, 276, 129, 277, 131, 132, 278, 279, 280, 136, 281, 138, 282, 140, 283, 284, 143, 285, 286, 146, 287, 288, 149, 289, 290, 291, 153, 292, 293, 156, 294, 158, 295, 296, 161, 297, 298, 299, 165, 300, 301, 168, 302, 303, 171, 304, 305, 306, 175, 307, 308, 309, 179, 310, 311, 182, 312, 184, 313, 314, 315, 188, 316, 317, 191, 318, 319, 320, 321, 196, 322, 323, 324, 325, 326, 327, 202, 203, 328, 205, 206, 329, 208, 330, 210, 211, 212, 213, 331, 332, 333, 216, 334, 218, 219, 335, 221, 336, 337, 338, 339, 340, 227, 341, 229, 342, 231, 232, 233, 234, 343, 344, 237, 345, 239, 240, 346, 242, 347, 348, 13, 349, 15, 350, 351, 18, 352, 353, 21, 354, 23, 355, 25, 356, 27, 357, 29, 358, 359, 360, 361, 34, 362, 363, 37, 364, 365, 366, 367, 42, 368, 369, 45, 370, 47, 371, 49, 372, 373, 374, 52, 375, 376, 55, 377, 378, 58, 59, 379, 61, 62, 380, 64, 381, 66, 382, 383]]}]}, "ocean": {"type": "GeometryCollection", "geometries": [{"type": "Polygon", "arcs": [[244, 384, 246]]}, {"type": "Polygon", "arcs": [[-75, 385, -73, 386, -71, 387, -69, 11, -68, -67, -66, -65, -64, -63, -62, 388, -59, -58, -57, -56, -55, -54, -53, 389, 390, -51, -50, -49, -48, -47, -46, -45, -44, -43, -42, -41, -40, -39, -38, -37, -36, -35, -34, -33, -32, -31, -30, -29, -28, -27, -26, -25, -24, -23, -22, -21, -20, -19, -18, -17, -16, -15, -14, -13, -244, -243, -242, -241, -240, -239, -238, -237, -236, -235, -234, -233, -232, -231, -230, -229, -228, -227, -226, -225, -224, -223, -222, -221, -220, -219, -218, -217, -334, 391, -214, -213, -212, -211, -210, -209, -208, -207, -206, -205, -204, -203, 392, 393, -200, -199, -198, -197, -196, -195, -194, -193, -192, -191, -190, -189, -188, -187, -186, -185, -184, -183, -182, -181, -180, -179, -178, -177, -176, -175, -174, -173, -172, -171, -170, -169, -168, -301, -166, -165, -164, -163, -162, -161, -160, -159, -158, -157, -156, -155, -154, -153, -152, -151, -150, -149, -148, -147, -146, -145, -144, -143, -142, -141, -140, -139, -138, -137, -136, -135, -134, -133, -132, -131, -130, -129, -128, -127, -126, -125, -124, -123, -122, -121, -120, -119, -118, -117, -116, -115, -114, -113, -112, -111, -110, -109, -108, -107, -106, -105, -104, -103, 394, -101, -100, -99, -98, -97, -96, -95, -94, -93, 395, 396, -90, -89, -256, -87, -86, -85, -84, -83, -82, 397, -80, 398, -78, 399, -76, 400, -10, 401], [-7], [-11], [-6], [-5, 402, -3, 403, -1], [-8], [-9]]}]}, "lakes": {"type": "GeometryCollection", "geometries": [{"type": "Polygon", "arcs": [[404, 405]]}, {"type": "Polygon", "arcs": [[406]]}, {"type": "Polygon", "arcs": [[407]]}, {"type": "Polygon", "arcs": [[408]]}]}, "rivers": {"type": "GeometryCollection", "geometries": [{"type": "LineString", "arcs": [247, 248, 249, 250]}, {"type": "LineString", "arcs": [251]}]}, "countries": {"type": "GeometryCollection", "geometries": [{"type": "Polygon", "properties": {"ct": [34.75, -6.26]}, "id": "TZA", "arcs": [[409, 410, 128, 129, 130, 131, 411, 412, 413, 414, 415, 416, 417, 418]]}, {"type": "Polygon", "properties": {"ct": [-12.14, 24.29]}, "id": "ESH", "arcs": [[419, 420, 14, 421]]}, {"type": "Polygon", "properties": {"ct": [23.58, -2.85]}, "id": "COD", "arcs": [[-415, 422, 423, 250, 192, 424, 425, -249, 426, 427, 428, 429, 430, 431]]}, {"type": "Polygon", "properties": {"ct": [45.73, 4.75]}, "id": "SOM", "arcs": [[432, 433, 434, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 123]]}, {"type": "Polygon", "properties": {"ct": [37.79, 0.6]}, "id": "KEN", "arcs": [[-411, -410, 435, 436, 437, -433, 124, 125, 126, 127]]}, {"type": "Polygon", "properties": {"ct": [29.86, 15.99]}, "id": "SDN", "arcs": [[438, 439, 440, 441, 93, 442, 443, 444]]}, {"type": "Polygon", "properties": {"ct": [18.58, 15.33]}, "id": "TCD", "arcs": [[-440, 445, 446, 447, 448]]}, {"type": "Polygon", "properties": {"ct": [25.12, -28.96]}, "id": "ZAF", "arcs": [[449, 450, 451, 452, 453, 454, 148, 149, 150, 151, 152, 153, 154, 155, 156, 157, 158, 159, 160, 161, 162, 163, 164, 165, 166, 167, 168, 169, 170], [455]]}, {"type": "Polygon", "properties": {"ct": [28.17, -29.63]}, "id": "LSO", "arcs": [[-456]]}, {"type": "Polygon", "properties": {"ct": [29.79, -18.91]}, "id": "ZWE", "arcs": [[-452, 456, 457, 458]]}, {"type": "Polygon", "properties": {"ct": [23.77, -22.1]}, "id": "BWA", "arcs": [[-451, 459, 460, -457]]}, {"type": "Polygon", "properties": {"ct": [17.16, -22.1]}, "id": "NAM", "arcs": [[-450, 171, 172, 173, 174, 175, 176, 177, 178, 179, 461, 462, -460]]}, {"type": "Polygon", "properties": {"ct": [-14.51, 14.35]}, "id": "SEN", "arcs": [[235, 236, 237, 238, 239, 463, 464, 465, 466, 233, 467]]}, {"type": "Polygon", "properties": {"ct": [-3.54, 17.27]}, "id": "MLI", "arcs": [[-465, 468, 469, 470, 471, 472, 473]]}, {"type": "Polygon", "properties": {"ct": [-10.33, 20.21]}, "id": "MRT", "arcs": [[-421, 474, -469, -464, 240, 241, 242, 243, 12, 13]]}, {"type": "Polygon", "properties": {"ct": [2.34, 9.65]}, "id": "BEN", "arcs": [[211, 475, 476, 477, 478]]}, {"type": "Polygon", "properties": {"ct": [9.32, 17.35]}, "id": "NER", "arcs": [[-448, 479, 480, -478, 481, -471, 482, 483]]}, {"type": "Polygon", "properties": {"ct": [8, 9.55]}, "id": "NGA", "arcs": [[-479, -481, 484, 206, 207, 208, 209, 210]]}, {"type": "Polygon", "properties": {"ct": [12.61, 5.66]}, "id": "CMR", "arcs": [[-447, 485, 486, 487, 488, 203, 204, 205, -485, -480]]}, {"type": "Polygon", "properties": {"ct": [1, 8.44]}, "id": "TGO", "arcs": [[-476, 212, 489, 490]]}, {"type": "Polygon", "properties": {"ct": [-1.24, 7.93]}, "id": "GHA", "arcs": [[-490, 213, 214, 215, 491, 492]]}, {"type": "Polygon", "properties": {"ct": [-5.61, 7.55]}, "id": "CIV", "arcs": [[-473, 493, -492, 216, 217, 218, 494, 495]]}, {"type": "Polygon", "properties": {"ct": [-11.06, 10.45]}, "id": "GIN", "arcs": [[-466, -474, -496, 496, 497, 227, 228, 229, 230, 231, 498]]}, {"type": "Polygon", "properties": {"ct": [-15.11, 12.02]}, "id": "GNB", "arcs": [[-467, -499, 232]]}, {"type": "Polygon", "properties": {"ct": [-9.41, 6.43]}, "id": "LBR", "arcs": [[-495, 219, 220, 221, 499, -497]]}, {"type": "Polygon", "properties": {"ct": [-11.8, 8.53]}, "id": "SLE", "arcs": [[-498, -500, 222, 223, 224, 225, 226]]}, {"type": "Polygon", "properties": {"ct": [-1.78, 12.31]}, "id": "BFA", "arcs": [[-472, -482, -477, -491, -493, -494]]}, {"type": "Polygon", "properties": {"ct": [20.37, 6.54]}, "id": "CAF", "arcs": [[-428, 500, -486, -446, -439, 501]]}, {"type": "Polygon", "properties": {"ct": [15.13, -0.84]}, "id": "COG", "arcs": [[-427, 248, -426, 502, 194, 503, -487, -501]]}, {"type": "Polygon", "properties": {"ct": [11.69, -0.65]}, "id": "GAB", "arcs": [[-488, -504, 195, 196, 197, 198, 199, 200, 504]]}, {"type": "Polygon", "properties": {"ct": [10.37, 1.65]}, "id": "GNQ", "arcs": [[-489, -505, 201, 202]]}, {"type": "Polygon", "properties": {"ct": [27.73, -13.4]}, "id": "ZMB", "arcs": [[-414, 505, 506, -458, -461, -463, 507, -423]]}, {"type": "Polygon", "properties": {"ct": [34.19, -13.17]}, "id": "MWI", "arcs": [[-413, 508, -506]]}, {"type": "Polygon", "properties": {"ct": [35.47, -17.23]}, "id": "MOZ", "arcs": [[-412, 132, 133, 134, 135, 136, 137, 138, 139, 140, 141, 142, 143, 144, 145, 146, 147, -455, 509, -453, -459, -507, -509]]}, {"type": "Polygon", "properties": {"ct": [31.4, -26.49]}, "id": "SWZ", "arcs": [[-454, -510]]}, {"type": "MultiPolygon", "properties": {"ct": [17.5, -12.29]}, "id": "AGO", "arcs": [[[-425, 193, -503]], [[-251, -424, -508, -462, 180, 181, 182, 183, 184, 185, 186, 187, 188, 189, 190, 191]]]}, {"type": "Polygon", "properties": {"ct": [29.91, -3.38]}, "id": "BDI", "arcs": [[-416, -432, 510]]}, {"type": "Polygon", "properties": {"ct": [46.69, -19.36]}, "id": "MDG", "arcs": [[6]]}, {"type": "Polygon", "properties": {"ct": [-15.43, 13.48]}, "id": "GMB", "arcs": [[-468, 234]]}, {"type": "Polygon", "properties": {"ct": [9.53, 34.17]}, "id": "TUN", "arcs": [[511, 45, 46, 47, 48, 49, 50, 51, 512]]}, {"type": "Polygon", "properties": {"ct": [2.6, 28.19]}, "id": "DZA", "arcs": [[-420, 513, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, -512, 514, -483, -470, -475]]}, {"type": "Polygon", "properties": {"ct": [38.68, 15.43]}, "id": "ERI", "arcs": [[-443, 94, 95, 515, 516]]}, {"type": "Polygon", "properties": {"ct": [-8.42, 29.89]}, "id": "MAR", "arcs": [[-514, -422, 15, 16, 17, 18, 19, 517, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33]]}, {"type": "Polygon", "properties": {"ct": [29.84, 26.51]}, "id": "EGY", "arcs": [[-442, 518, 62, 63, 64, 65, 66, 382, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92]]}, {"type": "Polygon", "properties": {"ct": [17.97, 27]}, "id": "LBY", "arcs": [[-441, -449, -484, -515, -513, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, -519]]}, {"type": "Polygon", "properties": {"ct": [39.55, 8.65]}, "id": "ETH", "arcs": [[-434, -438, 519, -444, -517, 520, 521]]}, {"type": "Polygon", "properties": {"ct": [42.5, 11.77]}, "id": "DJI", "arcs": [[-516, 96, 97, 98, 99, 522, -521]]}, {"type": "Polygon", "arcs": [[-435, -522, -523, 100, 101, 102, 103, 104, 105, 106]]}, {"type": "Polygon", "properties": {"ct": [32.36, 1.3]}, "id": "UGA", "arcs": [[-419, -418, 523, -430, 524, -436]]}, {"type": "Polygon", "properties": {"ct": [29.92, -2.01]}, "id": "RWA", "arcs": [[-417, -511, -431, -524]]}, {"type": "Polygon", "properties": {"ct": [30.2, 7.29]}, "id": "SSD", "arcs": [[-429, -502, -445, -520, -437, -525]]}]}, "subunits": {"type": "GeometryCollection", "geometries": []}}, "arcs": [[[6993, 8538], [80, -2], [101, 30], [-75, -42]], [[7099, 8524], [12, -27]], [[7111, 8497], [-114, -41], [-54, 13], [-26, 40]], [[6917, 8509], [60, 5]], [[6977, 8514], [16, 24]], [[6254, 8529], [-14, -29], [-160, -9], [1, 17], [-135, 19], [20, 43], [61, -34], [86, 6], [83, -7], [-3, -18], [61, 12]], [[8837, 3753], [30, -43], [27, -66], [18, -120], [29, -47], [-11, -48], [-20, -29], [-37, 58], [-21, -29], [21, -74], [-10, -43], [-31, -23], [-7, -85], [-43, -116], [-55, -138], [-69, -189], [-42, -139], [-51, -116], [-90, -24], [-97, -42], [-64, 25], [-88, 36], [-31, 53], [-7, 88], [-39, 80], [-10, 72], [19, 72], [52, 17], [0, 34], [53, 75], [10, 64], [-26, 47], [-21, 64], [-9, 92], [39, 56], [15, 63], [55, 4], [62, 20], [41, 18], [49, 2], [64, 57], [91, 61], [33, 51], [-15, 42], [47, -12], [62, 70], [2, 60], [37, 45], [38, -43]], [[4301, 9089], [55, 31], [67, -71], [-16, -132], [-50, 6], [-46, -33], [-42, 26], [-4, 121], [-25, 57], [61, -5]], [[4305, 9262], [71, 38], [19, -86], [-37, -77], [-50, 20], [-26, 68], [23, 37]], [[2758, 9999], [-8, -4], [-12, 4]], [[4973, 8814], [84, 8], [-40, -78], [17, -31], [-23, -52], [-85, 38], [-57, 11], [-155, 50], [16, 52], [130, -9], [113, 11]], [[7504, 9465], [126, -38], [142, -84]], [[1527, 6909], [-14, 50]], [[1513, 6959], [12, 50], [-29, 47], [-59, 43]], [[1437, 7099], [5, 43]], [[1442, 7142], [5, 46]], [[1447, 7188], [43, 27]], [[1490, 7215], [36, 52]], [[1526, 7267], [-7, 34], [38, 71], [62, 63], [38, 16], [29, 59], [3, 53], [40, 62], [74, 36]], [[1803, 7661], [70, 102]], [[1873, 7763], [58, 40]], [[1931, 7803], [103, 11], [88, 68], [56, 27], [92, 84], [-27, 124]], [[2243, 8117], [42, 86]], [[2285, 8203], [15, 53]], [[2300, 8256], [71, 67]], [[2371, 8323], [112, 46]], [[2483, 8369], [82, 41]], [[2565, 8410], [74, 104]], [[2639, 8514], [35, 61]], [[2674, 8575], [82, 0]], [[2756, 8575], [67, -43]], [[2823, 8532], [106, 7]], [[2929, 8539], [115, -22]], [[3044, 8517], [48, -1]], [[3092, 8516], [107, 55]], [[3199, 8571], [120, 17]], [[3319, 8588], [70, 41]], [[3389, 8629], [107, 31]], [[3496, 8660], [188, 18]], [[3684, 8678], [184, 8]], [[3868, 8686], [56, -15]], [[3924, 8671], [105, 39]], [[4029, 8710], [118, 1]], [[4147, 8711], [46, -23]], [[4193, 8688], [76, 6]], [[4269, 8694], [121, 40], [77, -12], [-3, -50]], [[4464, 8672], [94, 36]], [[4558, 8708], [8, -19], [-55, -49], [-1, -46], [38, -25]], [[4548, 8569], [-14, -86]], [[4534, 8483], [-73, -51], [21, -54]], [[4482, 8378], [57, -2]], [[4539, 8376], [28, -47], [42, -16]], [[4609, 8313], [131, -35], [47, 9], [92, -17], [148, -44], [52, -89], [100, -20]], [[5179, 8117], [156, -41]], [[5335, 8076], [119, -50]], [[5454, 8026], [54, 26], [53, 46]], [[5561, 8098], [-26, 76]], [[5535, 8174], [35, 49]], [[5570, 8223], [80, 47]], [[5650, 8270], [76, 13]], [[5726, 8283], [151, -20]], [[5877, 8263], [38, -45], [41, 0], [35, -17], [111, -12], [27, -33]], [[6129, 8156], [148, 2], [107, -27], [110, -29], [51, -16], [86, 32]], [[6631, 8118], [46, 29]], [[6677, 8147], [98, 8], [79, -13], [30, -49], [26, 32]], [[6910, 8125], [89, -23]], [[6999, 8102], [86, -6], [55, 25]], [[7140, 8121], [32, 33], [-7, 6], [29, 46], [23, 76], [15, 25], [4, 1], [39, 82], [55, 70], [2, 4], [-10, 76], [27, 41], [-41, 46], [42, 37], [-67, -8], [-93, 23], [-77, -58], [-168, -11], [-90, 54], [-120, 3], [-26, -42], [-76, -11], [-108, 53], [-121, -2], [-66, 99], [-81, 56], [54, 78], [-70, 47], [123, 96], [171, 4], [47, 76], [211, -13], [134, 65], [129, 28], [184, 2], [194, -70], [159, -39], [130, 15], [95, -9], [132, 53], [16, 42], [-28, 69], [-64, 36], [-61, 12], [-41, 31]], [[7504, 9465], [-96, 59]], [[7408, 9524], [80, 15], [93, 84]], [[7581, 9623], [-62, 40]], [[7519, 9663], [163, 41], [-3, 21]], [[7679, 9725], [-99, -16], [-89, -8], [-74, -32], [-104, -5], [-96, -38], [7, -62], [54, -24], [113, 6], [-21, -36], [-122, -17], [-151, -58], [-61, 21], [24, 47], [-121, 29], [19, 19], [107, 33], [-33, 23], [-172, 25], [-8, 38], [-103, -13], [-41, -55], [-86, -74], [3, -25], [-54, -22], [-34, 9], [-31, -120], [-58, -42], [-40, -71], [35, -57], [14, -39], [97, -32], [-21, -24], [-132, -6], [-47, -31], [-93, -54], [-35, 47], [2, 21], [-68, 2], [-58, 10], [-134, -26], [77, -56], [-57, -17], [-62, 0], [-58, 52], [-21, -22], [25, -60], [55, -47], [-42, -22], [62, -46], [55, -29], [2, -56], [-103, 26], [33, -51], [-71, -10], [42, -89], [-73, -1], [-91, 44], [-42, 80], [-20, 66], [-43, 46], [-57, 57], [-7, 29], [-19, 7], [-2, 22], [-62, 33], [-10, 48], [10, 68], [15, 31], [-19, 16], [-23, 8], [-31, 32], [-48, 20], [-105, 37], [-64, 36], [-102, 30], [-93, 73], [22, 8], [-50, 42], [-2, 34], [-72, 15], [-34, -43], [-33, 34], [3, 34], [4, 2], [24, 9], [-88, 15], [-90, -36], [6, -49], [-14, -29], [37, -51], [104, -50], [56, -83], [123, -80], [87, 0], [27, -22], [-31, -20], [100, -36], [81, -30], [96, -52], [11, -19], [-21, -36], [-61, 47], [-97, 16], [-47, -64], [81, -37], [-13, -53], [-47, -6], [-59, -85], [-46, -8], [0, 31], [23, 53], [24, 22], [-44, 58], [-34, 50], [-46, 12], [-32, 44], [-72, 18], [-48, 40], [-82, 6], [-87, 46], [-102, 65], [-75, 57], [-35, 99], [-55, 12], [-90, 33], [-52, -14], [-64, -46], [-46, -8], [-101, -56], [-219, 27], [-162, -32], [-12, -61], [6, -58], [-106, -66], [-142, -21], [-10, -34]], [[3413, 9067], [-68, -56], [-43, -81]], [[3302, 8930], [43, -57], [-64, -45], [-24, -65], [-84, -20], [-78, -76], [-141, -2], [-106, 2], [-70, -35], [-42, -38], [-55, 8], [-41, 34], [-31, 57], [-104, 16], [-45, -26], [-58, 14], [-58, -11], [17, 78], [-10, 62], [-50, 9], [-26, 38], [8, 65], [45, 37], [8, 40], [23, 60], [-3, 43], [-22, 35], [-5, 34], [6, 71], [-46, 44], [158, 72], [136, -18], [149, 0], [118, -17], [92, 6], [180, -4], [57, 60], [21, 200], [-114, 104], [-82, 51], [-170, 39], [-11, 72], [144, 22], [186, -26], [-35, 114], [105, -43], [217, 65]], [[9999, 7531], [-43, 6], [-121, 23], [-125, 13], [-48, 123], [-53, 18], [-85, -18], [-112, -49], [-136, 33], [-112, 77], [-107, 29], [-74, 95], [-82, 133], [-60, -16], [-71, 33], [-41, -39], [-66, 5], [23, -44], [-10, -23], [36, -76], [44, -86], [54, -23], [19, -35], [76, -42], [7, -41], [-11, -33], [14, -34], [32, -28], [14, -33], [17, -24], [-7, 72], [30, 53], [30, 11], [34, -32], [2, -58], [-25, -59], [21, -38], [20, 5], [4, -28], [87, 16], [92, -3], [67, -3], [76, 68], [83, 64], [70, 62], [33, 34], [14, -9], [-11, -41], [-14, -18], [15, -79]], [[9599, 7492], [49, -69], [63, -36]], [[9711, 7387], [81, -13], [66, -18], [50, -57], [30, -34], [40, -12], [-1, -23], [-40, -59], [-18, -28], [-47, -32], [-41, -69], [-50, 5], [-23, -23], [-18, -51], [13, -67], [-10, -12], [-51, 0], [-70, -37], [-10, -49], [-26, -21], [-69, 1]], [[9517, 6788], [-44, -25], [1, -41]], [[9474, 6722], [-54, -28], [-61, 10], [-74, -34], [-52, -6], [-80, -26]], [[9153, 6638], [-21, -45], [-3, -34]], [[9129, 6559], [-111, -42], [-177, -47], [-100, -70], [-49, -6], [-33, 6], [-65, -41], [-71, -20], [-93, -5], [-28, -6], [-24, -26], [-29, -7], [-18, -26], [-55, 3], [-35, -14], [-77, 5], [-29, 58], [3, 55], [-18, 30], [-22, 74], [-32, 41], [23, 5], [-12, 45], [14, 20], [-5, 43], [-15, 43], [-33, 30], [-9, 40], [-57, 36], [-59, 83], [-32, 82], [-76, 69], [-50, 16], [-74, 95], [-12, 70], [4, 59], [-63, 111], [-53, 39], [-60, 21], [-36, 57], [6, 23], [-31, 52], [-32, 22], [-44, 74], [-67, 81], [-57, 69], [-55, -1], [17, 55], [5, 35], [14, 40], [-4, 14]], [[7213, 7949], [-31, -40]], [[7182, 7909], [-24, -75]], [[7158, 7834], [-30, -52]], [[7128, 7782], [-26, -18]], [[7102, 7764], [-37, 32]], [[7065, 7796], [-51, 45], [-79, 143]], [[6935, 7984], [-11, -9]], [[6924, 7975], [46, -105], [68, -101], [84, -156], [41, -54], [36, -56], [99, -111]], [[7298, 7392], [-22, -18]], [[7276, 7374], [4, -64]], [[7280, 7310], [129, -90], [20, -21]], [[7429, 7199], [36, -98], [-25, -18], [16, -103], [41, -119], [43, -25], [60, -37]], [[7600, 6799], [65, -116]], [[7665, 6683], [30, -91], [61, -49], [152, -95], [62, -57], [60, -57], [35, -35], [54, -30]], [[8119, 6269], [27, -31]], [[8146, 6238], [-4, -41]], [[8142, 6197], [-63, -24]], [[8079, 6173], [47, -27]], [[8126, 6146], [37, -19], [21, -41]], [[8184, 6086], [50, -42], [56, 0]], [[8290, 6044], [104, 25]], [[8394, 6069], [121, 12]], [[8515, 6081], [98, 31]], [[8613, 6112], [55, 7], [40, 18]], [[8708, 6137], [63, 3]], [[8771, 6140], [36, 2]], [[8807, 6142], [51, 15]], [[8858, 6157], [59, 10]], [[8917, 6167], [52, 35]], [[8969, 6202], [42, 0], [3, -28]], [[9014, 6174], [-10, -58]], [[9004, 6116], [0, -53]], [[9004, 6063], [-23, -36]], [[8981, 6027], [-32, -108], [-53, -111]], [[8896, 5808], [-69, -128]], [[8827, 5680], [-95, -147]], [[8732, 5533], [-95, -112]], [[8637, 5421], [-131, -136]], [[8506, 5285], [-111, -81]], [[8395, 5204], [-166, -99]], [[8229, 5105], [-104, -76], [-121, -121], [-26, -53]], [[7978, 4855], [-25, -24]], [[7953, 4831], [-78, -40]], [[7875, 4791], [-27, -41], [-42, -8], [-16, -70]], [[7790, 4672], [-35, -41]], [[7755, 4631], [-22, -66], [-45, -33]], [[7688, 4532], [-51, -123]], [[7637, 4409], [7, -57], [71, -36], [3, -26], [-30, -61], [6, -30]], [[7694, 4199], [-7, -48]], [[7687, 4151], [38, -63], [46, -98], [41, -22]], [[7812, 3968], [18, -45], [-4, -100], [13, -87], [5, -157], [19, -49], [-33, -71]], [[7830, 3459], [-43, -69]], [[7787, 3390], [-71, -62]], [[7716, 3328], [-101, -38]], [[7615, 3290], [-126, -49], [-125, -107]], [[7364, 3134], [-43, -19]], [[7321, 3115], [-77, -71], [-46, -23], [-10, -71], [53, -76]], [[7241, 2874], [22, -58]], [[7263, 2816], [1, -30], [20, 5]], [[7284, 2791], [-3, -98]], [[7281, 2693], [-18, -47]], [[7263, 2646], [26, -17], [-17, -42], [-46, -35], [-92, -34], [-133, -54]], [[7001, 2464], [-49, -37]], [[6952, 2427], [10, -42]], [[6962, 2385], [28, -7]], [[6990, 2378], [-10, -52]], [[6980, 2326], [-27, -73]], [[6953, 2253], [-13, -83], [-29, -45], [-76, -51], [-22, -14]], [[6813, 2060], [-47, -51]], [[6766, 2009], [-31, -52]], [[6735, 1957], [-63, -71]], [[6672, 1886], [-125, -103], [-79, -60]], [[6468, 1723], [-84, -46]], [[6384, 1677], [-116, -39]], [[6268, 1638], [-56, -5], [-15, -28]], [[6197, 1605], [-67, 15]], [[6130, 1620], [-55, -19]], [[6075, 1601], [-121, 19]], [[5954, 1620], [-67, -12]], [[5887, 1608], [-46, 5]], [[5841, 1613], [-115, -39]], [[5726, 1574], [-94, -16]], [[5632, 1558], [-69, -38]], [[5563, 1520], [-51, -2], [-47, 36], [-37, 1], [-48, 45], [-5, -14], [-15, 27], [1, 59]], [[5361, 1672], [-37, 67], [36, 18]], [[5360, 1757], [-3, 77]], [[5357, 1834], [-72, 93]], [[5285, 1927], [-56, 85]], [[5229, 2012], [-80, 130]], [[5149, 2142], [-83, 76], [-43, 73]], [[5023, 2291], [-25, 97]], [[4998, 2388], [-27, 72]], [[4971, 2460], [-37, 154]], [[4934, 2614], [-3, 120], [-14, 55]], [[4917, 2789], [-43, 41]], [[4874, 2830], [-58, 82]], [[4816, 2912], [-58, 120]], [[4758, 3032], [-24, 63], [-91, 98], [-6, 76]], [[4637, 3269], [-11, 63]], [[4626, 3332], [16, 88]], [[4642, 3420], [38, 92], [6, 43], [36, 90], [26, 41]], [[4748, 3686], [64, 65]], [[4812, 3751], [36, 45], [11, 74]], [[4859, 3870], [-5, 56]], [[4854, 3926], [-34, 36]], [[4820, 3962], [-29, 61]], [[4791, 4023], [-28, 60], [6, 21], [35, 39], [-34, 97]], [[4770, 4240], [-23, 67]], [[4747, 4307], [-56, 63]], [[4691, 4370], [11, 20]], [[4702, 4390], [-16, 31]], [[4686, 4421], [-29, 75]], [[4657, 4496], [-91, 106]], [[4566, 4602], [-115, 101]], [[4451, 4703], [-73, 82], [-68, 103]], [[4310, 4888], [4, 34]], [[4314, 4922], [24, 32]], [[4338, 4954], [27, 72]], [[4365, 5026], [23, 75]], [[4388, 5101], [-21, 15]], [[4367, 5116], [38, 112]], [[4405, 5228], [16, 79], [-43, 66]], [[4378, 5373], [-51, 17]], [[4327, 5390], [-22, 45], [-29, 14], [1, 28]], [[4277, 5477], [-115, -36], [-42, 5]], [[4120, 5446], [-43, -22]], [[4077, 5424], [-89, 2], [-59, 62], [-37, 73], [-78, 66]], [[3814, 5627], [-84, -2]], [[3730, 5625], [-98, 0]], [[3632, 5625], [-92, -11]], [[3540, 5614], [-89, -22]], [[3451, 5592], [-174, -58]], [[3277, 5534], [-62, -34]], [[3215, 5500], [-100, -30], [-99, 29]], [[3016, 5499], [-51, -1], [-77, 19], [-72, -1], [-131, -17], [-77, -29]], [[2608, 5470], [-110, -37]], [[2498, 5433], [-22, 3]], [[2476, 5436], [-29, -1], [-114, 48], [-101, 76]], [[2232, 5559], [-95, 55]], [[2137, 5614], [-75, 64]], [[2062, 5678], [-30, 7]], [[2032, 5685], [-80, 41]], [[1952, 5726], [-58, 53]], [[1894, 5779], [-19, 37]], [[1875, 5816], [-14, 74]], [[1861, 5890], [-48, 59], [-44, 39]], [[1769, 5988], [-28, 13]], [[1741, 6001], [-28, 20]], [[1713, 6021], [-12, 44]], [[1701, 6065], [-17, 22], [-32, 16]], [[1652, 6103], [-59, 42], [-47, 7], [-26, 28], [1, 15], [-34, 21], [-7, 22]], [[1480, 6238], [-18, 77]], [[1462, 6315], [14, 44]], [[1476, 6359], [-46, 78]], [[1430, 6437], [-55, 35]], [[1375, 6472], [49, 19]], [[1424, 6491], [54, 70]], [[1478, 6561], [26, 52]], [[1504, 6613], [-10, 54]], [[1494, 6667], [31, 49]], [[1525, 6716], [14, 94]], [[1539, 6810], [-12, 99]], [[8519, 9460], [111, 103], [108, 17], [50, 59], [104, 21], [128, 44]], [[9020, 9704], [95, -25], [111, 5]], [[9226, 9684], [20, -61], [-20, -98], [-97, 15], [-95, -16], [-4, -73], [-108, 9], [4, -33], [61, -25], [50, -90], [129, -34], [21, -35], [-27, -41], [6, -25], [35, -64], [11, 73], [89, 25], [32, -57], [81, -60], [-97, -32], [-105, 25], [-25, -85], [74, -5], [-28, -69], [86, -34], [-16, -104], [21, -71], [-11, -23], [-173, -27], [-158, 17], [-78, 51], [-105, 20], [-35, 74], [-3, 50], [41, 23], [19, 35], [19, 78], [92, 8], [-35, 27], [-51, 4], [-57, 71], [-58, 53], [-122, 118], [11, 67], [-101, 95]], [[6249, 3825], [-34, 9], [-33, 47], [8, 44], [-28, 37], [-2, 28], [6, 64], [16, 21], [31, 15], [18, 33], [40, 49], [32, 11], [14, 12], [7, 13], [-4, 27], [14, 66], [1, 36], [-10, 102], [6, 35], [-4, 25], [-15, 26], [-50, 41], [-25, 27], [-14, 32], [-2, 16], [-5, 34], [-6, 34], [-2, 16], [-1, 14], [-2, 35], [-2, 41], [-3, 35], [0, 14], [-14, 34], [-14, 24], [-8, 24], [4, 48], [-12, 40], [-53, 19], [-71, 25], [-173, 116], [-43, 18], [-71, 2], [-160, -22], [-40, -8], [-85, -27], [-48, -39], [-25, -27], [-17, -33], [-15, -49], [-67, -74], [-8, -10]], [[5280, 4925], [-73, -48], [-51, -52], [-48, -97], [3, -82], [-28, -32], [-65, -49], [-65, -62]], [[4953, 4503], [-43, -1], [-13, -4], [-28, -24], [-24, -11], [-16, -39], [-4, -2], [-6, -9]], [[4819, 4413], [-39, -12], [-32, 2], [-46, -13]], [[7022, 5026], [-2, 16], [-33, 89], [-10, 5], [-19, 11], [-13, 6], [-17, 7], [-13, 6], [-8, 2], [-1, 4], [15, 21], [0, 14], [-12, 13], [-21, 8], [-26, 1], [-11, -3], [-22, -5], [-11, -2], [6, 10], [6, 11], [-2, 13], [-2, 14], [2, 28], [12, 27], [17, 21], [33, 18], [-32, 41], [-14, 32], [0, 34], [16, 73], [-8, 51], [-58, 90], [-40, 42], [-22, 30], [-15, 33], [-19, 70], [-3, 33], [8, 31], [21, 18], [30, 6], [37, -2], [22, -3], [24, 5], [27, 18], [42, 40], [9, 18], [-7, 22], [2, 13], [43, 56], [12, 34], [6, 53], [4, 64], [-4, 36], [-11, 35], [-37, 78], [-7, 34], [27, 117], [7, 31], [16, 27], [28, 22], [40, 21], [30, 23], [21, 29], [12, 39], [4, 36], [-4, 28], [-31, 61], [-25, 62], [-24, 2], [-27, -5], [-30, -15], [-61, -45], [-67, -65], [-20, -11], [-19, 0], [-18, 4], [-18, 13], [-15, 20], [-21, 62], [-16, 79], [23, 27], [3, 11], [-24, 33], [-3, 12], [30, 41], [53, 55], [38, 49], [38, 35], [26, 17], [49, 11], [16, 13], [17, 21], [13, 28], [4, 31], [-2, 33], [1, 58], [-5, 32], [-28, 44], [-8, 28], [23, 27], [2, 13], [-9, 8], [-60, 5], [-38, 35], [-53, 61], [-24, 20], [-18, 22], [-8, 25], [-2, 27], [-7, 23], [3, 19], [41, 84], [6, 35], [1, 47], [-6, 90], [-16, 51]], [[7182, 7909], [-6, -18], [-18, -57]], [[7158, 7834], [-22, -38], [-8, -14]], [[7128, 7782], [-5, -3], [-21, -15]], [[7065, 7796], [-50, 45], [-80, 143]], [[6935, 7984], [-6, -5], [-5, -4]], [[7298, 7392], [-9, -7], [-13, -10]], [[7276, 7375], [0, -1]], [[7600, 6799], [2, -2], [63, -114]], [[8119, 6269], [21, -24], [6, -7]], [[8142, 6197], [-29, -11], [-34, -13]], [[8394, 6069], [91, 9], [30, 3]], [[8515, 6081], [42, 13], [56, 18]], [[8708, 6137], [1, 0], [62, 3]], [[8807, 6142], [48, 14], [3, 1]], [[8917, 6167], [14, 10], [38, 25]], [[9014, 6174], [-4, -25], [-6, -33]], [[9004, 6063], [-6, -9], [-17, -27]], [[8896, 5808], [-16, -30], [-53, -98]], [[8732, 5533], [-54, -63], [-41, -49]], [[8506, 5285], [-70, -51], [-41, -30]], [[8395, 5204], [-145, -86], [-21, -13]], [[7978, 4855], [-14, -14], [-11, -10]], [[7953, 4831], [-45, -23], [-33, -17]], [[7790, 4672], [-34, -39], [-1, -2]], [[7688, 4532], [-10, -25], [-41, -98]], [[7694, 4199], [-7, -46], [0, -2]], [[7830, 3459], [-38, -62], [-5, -7]], [[7787, 3390], [-15, -14], [-56, -48]], [[7716, 3328], [-75, -29], [-26, -9]], [[7364, 3134], [-33, -14], [-10, -5]], [[7241, 2874], [4, -10], [18, -48]], [[7284, 2791], [-2, -69], [-1, -29]], [[7281, 2693], [-7, -19], [-11, -28]], [[7001, 2464], [-10, -7], [-39, -30]], [[6952, 2427], [5, -23], [5, -19]], [[6990, 2378], [-7, -39], [-3, -13]], [[6980, 2326], [-15, -41], [-12, -32]], [[6813, 2060], [-23, -25], [-24, -26]], [[6766, 2009], [-19, -31], [-12, -21]], [[6735, 1957], [-34, -38], [-29, -33]], [[6468, 1723], [-32, -18], [-52, -28]], [[6384, 1677], [-107, -36], [-9, -3]], [[6197, 1605], [-59, 13], [-8, 2]], [[6075, 1601], [-47, 8], [-74, 11]], [[5954, 1620], [-36, -6], [-31, -6]], [[5841, 1613], [-95, -32], [-20, -7]], [[5726, 1574], [-78, -13], [-16, -3]], [[5632, 1558], [-41, -22], [-28, -16]], [[5361, 1672], [-36, 67], [35, 18]], [[5360, 1757], [0, 13], [-3, 64]], [[5285, 1927], [-7, 10], [-49, 75]], [[5229, 2012], [-2, 4], [-78, 126]], [[5023, 2291], [-20, 80], [-5, 17]], [[4998, 2388], [-1, 4], [-26, 68]], [[4971, 2460], [-20, 85], [-17, 69]], [[4917, 2789], [-6, 5], [-37, 36]], [[4874, 2830], [-57, 82], [-1, 0]], [[4816, 2912], [-3, 8], [-55, 112]], [[4637, 3269], [-10, 57], [-1, 6]], [[4626, 3332], [3, 14], [13, 74]], [[4748, 3686], [28, 28], [36, 37]], [[4859, 3870], [0, 4], [-5, 52]], [[4854, 3926], [-31, 33], [-3, 3]], [[4820, 3962], [-1, 4], [-28, 57]], [[4770, 4240], [-17, 49], [-6, 18]], [[4747, 4307], [-7, 8], [-49, 55]], [[4702, 4390], [-12, 24], [-4, 7]], [[4686, 4421], [-3, 8], [-26, 67]], [[4657, 4496], [-66, 76], [-25, 30]], [[4566, 4602], [-40, 34], [-75, 67]], [[4310, 4888], [4, 29], [0, 5]], [[4314, 4922], [12, 16], [12, 16]], [[4338, 4954], [20, 51], [7, 21]], [[4365, 5026], [10, 32], [13, 42]], [[4388, 5100], [0, 1]], [[4388, 5101], [-6, 3], [-15, 12]], [[4378, 5373], [-24, 8], [-27, 9]], [[4120, 5446], [-10, -5], [-33, -17]], [[3814, 5627], [-81, -2], [-3, 0]], [[3277, 5534], [-17, -9], [-45, -25]], [[3215, 5500], [-56, -17], [-44, -12]], [[3115, 5471], [-99, 28]], [[2608, 5470], [-72, -24], [-38, -13]], [[2232, 5559], [-82, 47], [-13, 8]], [[2062, 5678], [-22, 6], [-8, 1]], [[2032, 5685], [-12, 6], [-68, 35]], [[1952, 5726], [-18, 17], [-40, 36]], [[1894, 5779], [-15, 29], [-4, 8]], [[1875, 5816], [-3, 15], [-11, 59]], [[1769, 5988], [-23, 11], [-5, 2]], [[1713, 6021], [-2, 7], [-10, 37]], [[1476, 6359], [-41, 69], [-5, 9]], [[1430, 6437], [-10, 6], [-45, 29]], [[1424, 6491], [22, 30], [32, 40]], [[1494, 6667], [12, 19], [19, 30]], [[1539, 6810], [-9, 73], [-3, 26]], [[1527, 6909], [-5, 18], [-9, 32]], [[1437, 7099], [1, 5], [4, 38]], [[1447, 7188], [24, 15], [19, 12]], [[1490, 7215], [18, 26], [18, 26]], [[1803, 7661], [1, 2], [69, 100]], [[1873, 7763], [31, 21], [27, 19]], [[2243, 8117], [31, 64], [11, 22]], [[2300, 8256], [50, 47], [21, 20]], [[2483, 8369], [22, 11], [60, 30]], [[2639, 8514], [9, 15], [26, 46]], [[2756, 8575], [60, -38], [7, -5]], [[2823, 8532], [35, 2], [71, 5]], [[2929, 8539], [82, -16], [33, -6]], [[3044, 8517], [5, 0], [43, -1]], [[3199, 8571], [115, 16], [5, 1]], [[3319, 8588], [35, 21], [35, 20]], [[3496, 8660], [131, 12], [57, 6]], [[3684, 8678], [50, 2], [134, 6]], [[3868, 8686], [38, -10], [18, -5]], [[3924, 8671], [55, 20], [50, 19]], [[4147, 8711], [45, -23], [1, 0]], [[4193, 8688], [35, 3], [41, 3]], [[4464, 8672], [60, 23], [34, 13]], [[4548, 8569], [-3, -19], [-11, -67]], [[4482, 8378], [40, -1], [17, -1]], [[4539, 8376], [4, -6], [24, -42]], [[4567, 8328], [13, -4], [29, -11]], [[5179, 8117], [106, -28], [50, -13]], [[5335, 8076], [42, -18], [77, -32]], [[5561, 8098], [-16, 48], [-10, 28]], [[5535, 8174], [19, 26], [16, 23]], [[5726, 8283], [99, -13], [52, -7]], [[6631, 8118], [4, 2], [42, 27]], [[6910, 8125], [28, -7], [61, -16]], [[7140, 8121], [62, -146], [11, -26]], [[7213, 7949], [-23, -29], [-8, -11]], [[9020, 9704], [95, -24], [111, 4]], [[3302, 8930], [43, 82], [68, 55]], [[7679, 9725], [3, -22], [-163, -40]], [[7581, 9623], [-92, -84], [-81, -15]], [[5877, 8263], [-151, 21], [-76, -14]], [[4609, 8313], [-42, 15]], [[4567, 8328], [-28, 48]], [[3115, 5471], [100, 28], [62, 35]], [[4367, 5116], [21, -16]], [[4388, 5100], [-23, -74]], [[8290, 6044], [-55, 0], [-51, 42]], [[7280, 7310], [-4, 65]], [[7276, 7375], [22, 17]], [[9129, 6559], [2, 34], [22, 45]], [[9474, 6722], [0, 41], [43, 25]], [[9711, 7387], [-62, 36], [-50, 69]], [[9999, 7531], [0, -7531], [-9999, 0], [0, 9999], [2738, 0]], [[2758, 9999], [682, 0]], [[6977, 8514], [-7, -1], [-53, -4]], [[7111, 8497], [-4, 8], [-8, 19]], [[7118, 4894], [-54, -45], [-37, -45], [44, -35], [-63, -24], [-14, 11], [-64, -5], [-50, -23], [-31, 39], [21, 70], [3, 60]], [[6873, 4897], [-5, 38], [51, 59], [70, 14], [47, 24], [58, -20], [32, -44], [-8, -74]], [[6756, 4142], [-38, 8], [11, 38], [-32, 27], [-15, 55], [-67, 54], [-39, 72], [20, 42], [-30, 56], [20, 160], [41, -96], [-5, -48], [21, -15], [-4, -42], [22, -40], [-26, -38], [90, -68], [8, -62], [65, -118], [-18, -6], [-24, 21]], [[7250, 3572], [-2, -12], [-40, 38], [-19, -25], [-18, 22], [1, 37], [-26, 30], [1, 43], [-33, 74], [32, 55], [-7, 121], [-39, 64], [10, 31], [59, -53], [9, -105], [37, -39], [-28, -96], [20, -127], [21, -5], [22, -53]], [[7460, 6184], [-15, 19], [26, 20], [30, -8], [-4, -33], [-16, -11], [-21, 13]], [[7100, 4905], [18, -11]], [[7118, 4894], [403, -204], [8, -58], [159, -100]], [[7812, 3968], [-88, -58], [-122, -39], [-66, 2], [-40, -30], [-77, -3], [-29, -12], [-134, 28], [-83, -8]], [[7173, 3848], [-31, 136], [-38, 46], [-23, 28], [-108, 19]], [[6973, 4077], [-63, 30], [-71, 16], [-44, 17], [-47, 26]], [[6748, 4166], [-60, 126], [-64, 56], [-22, 58], [11, 52], [-20, 92]], [[6593, 4550], [46, 4], [40, 37], [43, 52], [28, 21], [-1, 32], [-24, 23], [-7, 39]], [[6718, 4758], [32, 13], [7, 59], [-44, 56]], [[6713, 4886], [39, 12], [121, -1]], [[6873, 4897], [227, 8]], [[2370, 7765], [0, -7], [-2, -19]], [[2368, 7739], [0, -152], [-365, 6], [4, -256], [-104, -9], [-27, -52], [21, -144], [-436, 1], [-24, -34]], [[1442, 7142], [2, -1], [250, 8], [14, 36], [45, 45], [37, 138], [154, 108], [52, 126], [35, 8], [36, 78], [94, 10], [40, -13], [50, 0], [36, 23], [69, 3], [-3, 54], [17, 0]], [[6748, 4166], [-44, 10], [-149, -17], [-30, -12], [-31, -64], [25, -44], [-20, -118], [-14, -101], [30, -18], [78, -38], [30, 18], [10, -108], [-85, 1], [-46, 55], [-41, 42], [-85, 14], [-25, 53], [-68, -32], [-89, 14], [-37, 46], [-71, 9], [-52, -3], [-6, 31], [-38, 3]], [[5990, 3907], [-51, 6], [-69, -15], [-48, 2], [-28, -9], [6, 119], [-37, 37], [-8, 62], [17, 60], [-23, 39], [-2, 62], [-135, 0], [10, 36], [-57, -1], [-6, -17], [-69, -4], [-28, -58], [-16, -25], [-62, 14], [-36, -14], [-74, -8], [-42, 52], [-26, 32], [-32, 60], [-27, 75], [-328, 1]], [[4686, 4421], [29, 10], [3, 44], [18, 25], [41, 21]], [[4777, 4521], [29, -10], [38, 39], [61, -1], [7, -29], [41, -17]], [[5280, 4925], [13, 32], [2, 37], [19, 34], [-6, 57], [14, 89], [21, 62], [34, 54], [6, 60]], [[5383, 5350], [10, 70], [43, 50], [60, 33], [91, -34], [71, -37], [81, -10], [83, -20], [33, 61], [16, 7], [50, -10], [124, 50], [44, -21], [36, 3], [16, 24], [42, 9], [83, -10], [72, -3], [36, 11]], [[6374, 5523], [68, -83], [49, -12], [30, 17], [52, -7], [61, 22], [27, -43], [98, -67]], [[6759, 5350], [-7, -117], [44, -13], [-35, -36], [-43, -26], [-42, -52], [-24, -47], [-6, -80], [-26, -38], [-1, -76]], [[6619, 4865], [-32, -27], [-4, -60], [-15, -8], [-10, -54]], [[6558, 4716], [28, -46], [7, -120]], [[7953, 4831], [-66, 83], [-1, 364], [97, 113]], [[7983, 5391], [30, 32], [72, 2], [99, 70], [144, 5], [314, 300]], [[8642, 5800], [78, 83], [50, 62], [0, 52], [0, 101], [0, 41], [1, 1]], [[7100, 4905], [-1, 105], [31, 41], [55, 66], [40, 73], [-48, 115], [-13, 50], [-53, 69]], [[7111, 5424], [68, 60], [76, 66]], [[7255, 5550], [57, -17], [0, -56], [38, -33], [78, 0], [140, -85], [35, -1], [26, 3], [25, -11], [74, -8], [33, 41], [101, 42], [45, -34], [76, 0]], [[6062, 5822], [-84, 44], [-39, 29], [-7, 31], [18, 42], [0, 40], [-64, 63], [-13, 43]], [[5873, 6114], [2, 24], [-41, 29], [-2, 58], [-23, 39], [-39, -6], [11, 37], [29, 42], [-12, 41], [36, 31], [-23, 23], [29, 62], [51, 73], [96, -7], [-6, 397]], [[5981, 6957], [2, 42], [128, 1], [0, 199]], [[6111, 7199], [446, 0], [431, 0], [441, 0]], [[7600, 6799], [-56, -57], [-82, -16], [-35, -31], [-11, -66], [-47, -147], [11, -40]], [[7380, 6442], [-17, -86], [-46, -99], [-67, -49], [-47, -77], [-11, -41], [-53, -28], [-33, -104], [2, -90]], [[7108, 5868], [-2, 78], [-15, 2], [2, 50], [-13, 34], [-58, 39], [-13, 72], [13, 74], [-51, 7], [-8, -22], [-67, -5], [27, -29], [10, -61], [-61, -54], [-56, -73], [-57, -10], [-93, 58], [-42, -20], [-12, -29], [-57, -19], [-4, -21], [-110, 0], [-16, 21], [-80, 3], [-40, -17], [-30, 9], [-58, 58], [-19, 28], [-80, -14], [-30, -47], [-29, -89], [-38, -19], [-34, -11], [75, -39]], [[5873, 6114], [-70, -17], [-56, -41], [-81, -109], [-104, -46], [-108, 6], [-31, -9], [11, -35], [-58, -35], [-47, -39], [-140, -39], [-28, 23], [-18, 2], [-21, -26], [-91, -7]], [[5031, 5742], [17, 27], [-35, 69], [-16, 41], [-48, 17], [-66, 58], [24, 48], [51, -11], [31, 8], [62, -1], [-60, 91], [4, 66], [-7, 66], [-45, 64]], [[4943, 6285], [12, 47], [-72, 3], [1, 64], [-47, 37], [48, 132], [142, 94], [6, 130], [43, 203], [24, 43], [-46, 34], [-2, 32], [-42, 26], [-27, 156]], [[4983, 7286], [112, 54], [443, -191], [443, -192]], [[5149, 2142], [53, 50], [44, -28], [19, -43], [50, -7], [69, -19], [60, 8], [99, 51], [0, 369]], [[5543, 2523], [30, -15], [66, -95], [-10, -61], [25, -35], [79, 10], [56, 45], [53, 30], [27, 48], [54, 23], [47, -12], [53, -28], [90, -5], [71, 23], [12, 31], [19, 48], [61, 8], [33, 38], [37, 66], [100, 75], [157, 74]], [[6603, 2791], [45, -2], [54, -16], [37, 12], [59, -10]], [[6798, 2775], [54, -141], [28, -71], [-19, -112], [9, -36]], [[6870, 2415], [-56, 19], [-32, -7], [-10, -30], [-31, -37], [1, -35], [67, -54], [65, 11], [22, 44]], [[6896, 2326], [84, 0]], [[6552, 2104], [-48, 31], [-52, -20], [-60, -39], [-59, -64], [83, -77], [40, 10], [20, 32], [62, 16], [19, 32], [34, 49], [-39, 30]], [[6603, 2791], [-71, 45], [-86, 15], [-33, 64], [0, 35], [-47, 11], [-126, 109], [-35, 58], [-22, 18], [-43, 80]], [[6140, 3226], [124, -11], [36, -11], [38, 2], [61, 65], [97, 82], [40, 8], [13, 34], [63, 40], [84, 14]], [[6696, 3449], [8, -37], [92, 2], [52, -22], [24, -24], [53, -8], [57, -32], [1, -126], [-22, -70], [-5, -74], [18, -30], [-13, -59], [-16, -9], [-30, -72], [-117, -113]], [[5543, 2523], [0, 292], [110, 3], [3, 356], [83, 4], [171, 35], [43, -41], [71, 39], [33, 0], [63, 22]], [[6120, 3233], [20, -7]], [[4637, 3269], [53, 20], [67, 17], [72, -3], [66, -46], [17, 7], [450, 5], [77, -48], [269, -14], [204, 40]], [[5912, 3247], [91, 23], [72, -6], [44, -22], [1, -9]], [[1504, 6613], [38, 32], [55, -9], [54, 22], [62, 1], [54, -29], [73, -27], [68, -73], [73, -69]], [[1981, 6461], [5, -62], [22, -57], [41, -29], [10, -38], [-5, -31]], [[2054, 6244], [-16, -6], [-61, 8], [-8, -11], [-25, -2], [-79, 24], [-54, 1]], [[1811, 6258], [-205, 4], [-30, -11], [-37, 3], [-59, -16]], [[1462, 6315], [101, -3], [27, 14], [20, 1], [41, 23], [47, -21], [49, -2], [48, 23], [-23, 29], [-36, -17], [-35, 0], [-44, 25], [-35, -2], [-25, -23], [-121, -3]], [[1981, 6461], [37, 18], [19, 59], [35, 2], [78, -27], [62, 19], [43, -6], [17, 22], [446, 2], [24, 70], [-19, 12], [-53, 431], [-54, 432], [170, 2]], [[2786, 7497], [375, -218], [375, -219], [26, -46], [69, -29], [52, -16], [1, -64], [123, 10]], [[3807, 6915], [0, -230], [-60, -67], [-10, -62], [-98, -16], [-152, -8], [-41, -36], [-71, -4]], [[3375, 6492], [-72, 0], [-27, 19], [-61, -14], [-104, -42], [-22, -31], [-86, -45], [-15, -26], [-46, -20], [-54, 14], [-31, -25], [-16, -68], [-88, -83], [3, -34], [-31, -42], [8, -59]], [[2733, 6036], [-46, -14], [-26, -13], [-17, 43], [-32, -11], [-20, 1], [-20, -29], [-86, 1], [-31, 15], [-14, -9]], [[2441, 6020], [-34, 29], [6, 30], [-14, 11], [-24, -10], [5, 33], [22, 26], [-45, 42], [-13, 27], [-25, 22], [-22, 3], [-27, -14], [-36, -14], [-30, -21], [-48, 8], [-31, 25], [-18, 4], [-29, -14], [-18, 0], [-6, 37]], [[2368, 7739], [418, -242]], [[3540, 5614], [-27, 69], [5, 229], [-22, 21], [-5, 49], [-38, 35], [-34, 29], [14, 53]], [[3433, 6099], [38, 11], [23, 44], [54, 9], [24, 30]], [[3572, 6193], [38, 30], [39, 0], [85, -58]], [[3734, 6165], [-4, -33], [25, -59], [-22, -40], [12, -27], [-54, -62], [-34, -31], [-21, -63], [2, -64], [-6, -161]], [[4943, 6285], [-31, -5], [-3, -32]], [[4909, 6248], [-21, -2], [-75, 109], [-26, 4], [-87, -56], [-86, 29], [-60, 6], [-32, -14], [-65, 3], [-66, -43], [-56, -2], [-135, 52], [-53, -25], [-56, 2], [-42, 38], [-111, 37], [-120, -12], [-29, -22], [-15, -57], [-32, -40], [-8, -90]], [[3572, 6193], [3, 69], [-128, 23], [-4, 48], [-62, 65], [-15, 46], [9, 48]], [[3807, 6915], [157, 44], [321, 197], [381, 190]], [[4666, 7346], [176, -43], [62, -55], [79, 38]], [[4909, 6248], [44, -40], [-13, -18], [-5, -33], [-94, -78], [-29, -64], [-16, -52], [-24, -22], [-22, -70], [-59, -41], [-18, -51], [-25, -40], [-10, -41], [-76, -34], [-63, 41], [-42, -2], [-66, -58], [-32, -1], [-53, -97], [-29, -70]], [[5031, 5742], [-56, -102], [-27, -18], [-9, -77], [11, -42], [-8, -30], [52, -53], [10, -35], [41, -52], [50, -32], [5, -46], [12, -29]], [[5112, 5226], [-8, -54], [-88, 24], [-90, 26], [-140, 4]], [[4786, 5226], [-14, 6], [-66, -13], [-67, 13], [-53, -6]], [[4586, 5226], [-181, 2]], [[3451, 5592], [-25, 35], [-30, 64], [-8, 50], [24, 90], [-28, 36], [-10, 79], [0, 73], [-47, 51], [9, 31]], [[3336, 6101], [97, -2]], [[3016, 5499], [5, 39], [-48, 86], [29, 113], [46, 84], [-29, 143]], [[3019, 5964], [-15, 75], [2, 57], [193, 4], [49, -7], [36, 16], [52, -8]], [[2733, 6036], [50, -21], [19, -33], [50, -22], [39, 26], [52, 3], [76, -25]], [[2476, 5436], [9, 82], [10, 13], [-3, 39], [-47, 42], [-35, 7], [-33, 27], [24, 45], [-11, 48], [5, 29]], [[2395, 5768], [18, 0], [7, 44], [-9, 19], [11, 14], [41, 12], [-28, 80], [-25, 41], [9, 34], [22, 8]], [[2395, 5768], [-31, 3], [-23, -41], [-31, 1], [-22, 21], [8, 40], [-47, 62], [-29, -12], [-24, -2]], [[2196, 5840], [-30, -6], [1, 37], [-18, 26], [4, 29], [-24, 42], [-31, 36], [-89, 0], [-26, -19], [-31, -2], [-18, -22], [-13, -27], [-60, -44]], [[1652, 6103], [49, 49], [34, -2], [29, 17], [25, 0], [17, 14], [-9, 33], [12, 10], [2, 34]], [[2062, 5678], [27, 32], [6, 29], [50, 54], [51, 47]], [[5383, 5350], [-71, 5], [-76, 17], [-66, -53], [-58, -93]], [[6062, 5822], [61, -40], [1, -33], [75, -52], [46, -43], [28, -60], [83, -39], [18, -32]], [[4777, 4521], [-42, 35], [-33, -17], [-45, -43]], [[4566, 4602], [84, 55], [-42, 66], [38, 25], [75, 12], [9, 45], [59, -48], [99, -5], [34, 48], [14, 66], [-12, 78], [-53, 59], [48, 116], [-28, 20], [-82, -8], [-31, 52], [8, 43]], [[4388, 5101], [37, 5], [162, -1], [-1, 121]], [[6973, 4077], [52, -45], [28, -85], [-19, -27], [-22, -81], [21, -83], [-35, -35], [-33, -93], [58, -26]], [[7023, 3602], [-337, -82], [10, -71]], [[5912, 3247], [-72, 63], [-75, 82], [5, 318], [231, -1], [-9, 34], [16, 37], [-19, 47], [12, 49], [-11, 31]], [[7173, 3848], [-31, -76], [31, -130], [38, 1], [40, -32], [47, -72], [9, -129], [-48, -21], [-34, -69], [-72, 61], [-8, 71], [23, 46], [-7, 40], [-43, 26], [-31, -10], [-64, 48]], [[6896, 2326], [-9, 45], [-17, 44]], [[6558, 4716], [67, -8], [34, 57], [59, -7]], [[4386, 8030], [-47, 179], [-68, 41], [-1, 24], [-91, 60], [-10, 75], [68, 56], [27, 82], [-18, 95], [23, 52]], [[4609, 8313], [-6, -77], [-54, -29], [-34, -32], [-77, -38], [12, -42], [-9, -42], [-55, -23]], [[2370, 7765], [-1, 118], [180, 74], [111, 15], [91, 27], [42, 50], [130, 40], [5, 74], [64, 9], [50, 37], [146, 16], [20, 39], [-29, 22], [-39, 105], [-6, 61], [-42, 64]], [[4386, 8030], [36, -88], [6, -47], [-19, -81], [8, -46], [-14, -55], [9, -63], [-44, -41], [66, -73], [4, -43], [40, -56], [52, 19], [87, -47], [49, -63]], [[8119, 6269], [-33, -24], [-48, 9]], [[8038, 6254], [-38, 32], [-45, 59], [-50, 32], [-28, 34], [-97, 40], [-76, 2], [-27, 20], [-65, -23], [-68, 45], [-34, -74], [-130, 21]], [[1873, 7763], [2, 2], [56, 38]], [[6111, 7199], [0, 368], [0, 356], [-34, 81], [29, 61], [-17, 43], [40, 48]], [[7255, 5550], [-66, 109], [-51, 23], [-19, 40], [-57, 49], [-68, 7], [38, 57], [59, 2], [17, 31]], [[8038, 6254], [-39, -45], [-37, -46], [8, -28], [2, -31], [62, -1], [27, 7], [25, -18]], [[8086, 6092], [-25, -35], [41, -55], [41, -49], [43, -35], [363, -119], [93, 1]], [[8126, 6146], [-40, -54]], [[6713, 4886], [-67, -31], [-27, 10]], [[6759, 5350], [45, 28], [71, -23], [89, 24], [79, -1], [68, 46]]], "transform": {"scale": [0.009000900090009001, 0.010001000100010001], "translate": [-30, -50]}, "bbox": [-30, -50, 60, 50]}