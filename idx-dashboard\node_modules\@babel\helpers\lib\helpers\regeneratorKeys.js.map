{"version": 3, "names": ["_regeneratorKeys", "val", "object", "Object", "keys", "key", "unshift", "next", "length", "pop", "value", "done"], "sources": ["../../src/helpers/regeneratorKeys.ts"], "sourcesContent": ["/* @minVersion 7.27.0 */\n/* @mangleFns */\n\nexport default function _regeneratorKeys(val: unknown) {\n  var object = Object(val);\n  var keys: string[] = [];\n  var key: string;\n  // eslint-disable-next-line guard-for-in\n  for (var key in object) {\n    keys.unshift(key);\n  }\n\n  // Rather than returning an object with a next method, we keep\n  // things simple and return the next function itself.\n  return function next() {\n    while (keys.length) {\n      key = keys.pop()!;\n      if (key in object) {\n        // @ts-expect-error assign to () => ...\n        next.value = key;\n        // @ts-expect-error assign to () => ...\n        next.done = false;\n        return next;\n      }\n    }\n\n    // To avoid creating an additional object, we just hang the .value\n    // and .done properties off the next function object itself. This\n    // also ensures that the minifier will not anonymize the function.\n    // @ts-expect-error assign to () => ...\n    next.done = true;\n    return next;\n  };\n}\n"], "mappings": ";;;;;;AAGe,SAASA,gBAAgBA,CAACC,GAAY,EAAE;EACrD,IAAIC,MAAM,GAAGC,MAAM,CAACF,GAAG,CAAC;EACxB,IAAIG,IAAc,GAAG,EAAE;EACvB,IAAIC,GAAW;EAEf,KAAK,IAAIA,GAAG,IAAIH,MAAM,EAAE;IACtBE,IAAI,CAACE,OAAO,CAACD,GAAG,CAAC;EACnB;EAIA,OAAO,SAASE,IAAIA,CAAA,EAAG;IACrB,OAAOH,IAAI,CAACI,MAAM,EAAE;MAClBH,GAAG,GAAGD,IAAI,CAACK,GAAG,CAAC,CAAE;MACjB,IAAIJ,GAAG,IAAIH,MAAM,EAAE;QAEjBK,IAAI,CAACG,KAAK,GAAGL,GAAG;QAEhBE,IAAI,CAACI,IAAI,GAAG,KAAK;QACjB,OAAOJ,IAAI;MACb;IACF;IAMAA,IAAI,CAACI,IAAI,GAAG,IAAI;IAChB,OAAOJ,IAAI;EACb,CAAC;AACH", "ignoreList": []}