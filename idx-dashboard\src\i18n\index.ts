// DRDO IDX - Multilingual Support System

import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import LanguageDetector from 'i18next-browser-languagedetector';

// Translation resources
const resources = {
  en: {
    translation: {
      // Navigation
      'nav.dashboard': 'Dashboard',
      'nav.weapons': 'Weapons Systems',
      'nav.missions': 'Mission Control',
      'nav.simulation': 'Simulation Lab',
      'nav.cyber': 'Cyber Defense',
      'nav.strategic': 'Strategic Command',
      'nav.assets': 'Asset Management',
      'nav.labs': 'Research Labs',
      'nav.sandbox': 'Visual Sandbox',
      'nav.equipment': 'Equipment Status',
      'nav.statistics': 'Mission Statistics',
      'nav.results': 'Simulation Results',
      'nav.settings': 'Settings',
      
      // Common
      'common.status': 'Status',
      'common.active': 'Active',
      'common.operational': 'Operational',
      'common.maintenance': 'Maintenance',
      'common.critical': 'Critical',
      'common.healthy': 'Healthy',
      'common.warning': 'Warning',
      'common.offline': 'Offline',
      'common.classified': 'CLASSIFIED',
      'common.restricted': 'RESTRICTED',
      'common.confidential': 'CONFIDENTIAL',
      'common.secret': 'SECRET',
      'common.top_secret': 'TOP SECRET',
      'common.secure': 'SECURE',
      'common.online': 'System Online',
      'common.encrypted': 'Encrypted',
      
      // Dashboard
      'dashboard.title': 'DRDO Integrated Dashboard for eXcellence',
      'dashboard.subtitle': 'Interactive Defense eXcellence',
      'dashboard.activeAssets': 'Active Systems',
      'dashboard.activeMissions': 'Mission Ready',
      'dashboard.threatAlerts': 'Active Alerts',
      'dashboard.systemHealth': 'Success Rate',
      'dashboard.recentAlerts': 'Recent Threat Alerts',
      'dashboard.allSystemsOperational': 'All Systems Operational',
      'dashboard.lastUpdated': 'Last Updated',
      'dashboard.systemOverview': 'System Overview',
      'dashboard.quickActions': 'Quick Actions',
      'dashboard.systemHealthCheck': 'System Health Check',
      'dashboard.missionPlanning': 'Mission Planning',
      'dashboard.securityAudit': 'Security Audit',
      'dashboard.generateReport': 'Generate Report',
      
      // Equipment Status
      'equipment.title': 'Equipment Health',
      'equipment.subtitle': 'Real-time equipment monitoring and status',
      'equipment.distribution': 'Equipment Distribution',
      'equipment.overview': 'Status Overview',
      'equipment.operationalEquipment': 'Operational Equipment',
      'equipment.downForMaintenance': 'Down for Maintenance',
      'equipment.totalUnits': 'Total Units',
      
      // Mission Statistics
      'mission.title': 'Mission Statistics',
      'mission.subtitle': 'Progress tracking and mission analytics',
      'mission.progressTrend': 'Mission Progress Trend',
      'mission.metrics': 'Mission Metrics',
      'mission.activeMissions': 'Active Missions',
      'mission.successRate': 'Success Rate',
      'mission.inProgress': 'In Progress',
      'mission.completed': 'Completed',
      'mission.progress': 'Mission Progress (%)',
      
      // Simulation Results
      'simulation.title': 'Simulation Results',
      'simulation.subtitle': '3D trajectory analysis and accuracy testing',
      'simulation.accuracyComparison': 'Accuracy Comparison',
      'simulation.testResults': 'Test Results',
      'simulation.brahmosTest': 'BrahMos Test',
      'simulation.agniTest': 'Agni-V Test',
      'simulation.prithviTest': 'Prithvi Test',
      'simulation.supersonicCruise': 'Supersonic cruise missile',
      'simulation.intercontinental': 'Intercontinental ballistic missile',
      'simulation.surfaceToSurface': 'Surface-to-surface missile',
      'simulation.accuracy': 'Accuracy (%)',
      
      // Settings
      'settings.title': 'System Configuration',
      'settings.subtitle': 'Dashboard settings and preferences',
      'settings.themeConfig': 'Theme Configuration',
      'settings.securitySettings': 'Security Settings',
      'settings.dashboardTheme': 'Dashboard Theme',
      'settings.drdoFormal': 'DRDO Formal (Default)',
      'settings.lightMode': 'Light Mode',
      'settings.darkMode': 'Dark Mode',
      'settings.tacticalMode': 'Tactical Mode',
      'settings.currentTheme': 'Current theme',
      'settings.changesApply': 'Changes apply immediately across all modules',
      'settings.aesEncryption': 'AES-256 Encryption',
      'settings.dataTransmissions': 'All data transmissions encrypted',
      'settings.sessionTimeout': 'Session Timeout',
      'settings.idleTimeout': '15 minutes idle timeout',
      'settings.auditLogging': 'Audit Logging',
      'settings.actionsLogged': 'All actions logged and monitored',
      'settings.adminAccess': 'ADMIN ACCESS',
      
      // Security
      'security.login': 'Secure Login',
      'security.username': 'Username',
      'security.password': 'Password',
      'security.accessDenied': 'Access Denied',
      'security.insufficientClearance': 'Your clearance level is insufficient for this resource.',
      'security.yourClearance': 'Your Clearance',
      'security.required': 'Required',
      'security.demoCredentials': 'Demo Credentials: admin / drdo123',
      'security.secureConnection': 'SECURE CONNECTION',
      
      // Footer
      'footer.copyright': '© 2025 DRDO | Classified',
      'footer.version': 'v2.0 Military',
      
      // Status Messages
      'status.excellentStatus': 'Excellent status',
      'status.mediumPriority': 'Medium priority',
      'status.aboveTarget': 'Above target',
      'status.operational': 'OPERATIONAL',
      'status.inProgress': 'IN PROGRESS',
      'status.completed': 'COMPLETED',
      'status.fromYesterday': 'from yesterday'
    }
  },
  hi: {
    translation: {
      // Navigation (Hindi)
      'nav.dashboard': 'डैशबोर्ड',
      'nav.weapons': 'हथियार प्रणाली',
      'nav.missions': 'मिशन नियंत्रण',
      'nav.simulation': 'सिमुलेशन लैब',
      'nav.cyber': 'साइबर रक्षा',
      'nav.strategic': 'रणनीतिक कमान',
      'nav.assets': 'संपत्ति प्रबंधन',
      'nav.labs': 'अनुसंधान प्रयोगशाला',
      'nav.sandbox': 'विज़ुअल सैंडबॉक्स',
      'nav.equipment': 'उपकरण स्थिति',
      'nav.statistics': 'मिशन आंकड़े',
      'nav.results': 'सिमुलेशन परिणाम',
      'nav.settings': 'सेटिंग्स',
      
      // Common (Hindi)
      'common.status': 'स्थिति',
      'common.active': 'सक्रिय',
      'common.operational': 'परिचालनात्मक',
      'common.maintenance': 'रखरखाव',
      'common.critical': 'गंभीर',
      'common.healthy': 'स्वस्थ',
      'common.warning': 'चेतावनी',
      'common.offline': 'ऑफलाइन',
      'common.classified': 'वर्गीकृत',
      'common.restricted': 'प्रतिबंधित',
      'common.confidential': 'गोपनीय',
      'common.secret': 'गुप्त',
      'common.top_secret': 'अति गुप्त',
      'common.secure': 'सुरक्षित',
      'common.online': 'सिस्टम ऑनलाइन',
      'common.encrypted': 'एन्क्रिप्टेड',
      
      // Dashboard (Hindi)
      'dashboard.title': 'डीआरडीओ एकीकृत डैशबोर्ड',
      'dashboard.subtitle': 'इंटरैक्टिव रक्षा उत्कृष्टता',
      'dashboard.activeAssets': 'सक्रिय सिस्टम',
      'dashboard.activeMissions': 'मिशन तैयार',
      'dashboard.threatAlerts': 'सक्रिय अलर्ट',
      'dashboard.systemHealth': 'सफलता दर',
      'dashboard.allSystemsOperational': 'सभी सिस्टम परिचालनात्मक',
      'dashboard.lastUpdated': 'अंतिम अपडेट',
      'dashboard.systemOverview': 'सिस्टम अवलोकन',
      'dashboard.quickActions': 'त्वरित कार्य',
      'dashboard.systemHealthCheck': 'सिस्टम स्वास्थ्य जांच',
      'dashboard.missionPlanning': 'मिशन योजना',
      'dashboard.securityAudit': 'सुरक्षा ऑडिट',
      'dashboard.generateReport': 'रिपोर्ट जेनरेट करें',
      
      // Equipment Status (Hindi)
      'equipment.title': 'उपकरण स्वास्थ्य',
      'equipment.subtitle': 'रीयल-टाइम उपकरण निगरानी और स्थिति',
      'equipment.distribution': 'उपकरण वितरण',
      'equipment.overview': 'स्थिति अवलोकन',
      'equipment.operationalEquipment': 'परिचालनात्मक उपकरण',
      'equipment.downForMaintenance': 'रखरखाव के लिए बंद',
      'equipment.totalUnits': 'कुल इकाइयां',
      
      // Mission Statistics (Hindi)
      'mission.title': 'मिशन आंकड़े',
      'mission.subtitle': 'प्रगति ट्रैकिंग और मिशन विश्लेषण',
      'mission.progressTrend': 'मिशन प्रगति रुझान',
      'mission.metrics': 'मिशन मेट्रिक्स',
      'mission.activeMissions': 'सक्रिय मिशन',
      'mission.successRate': 'सफलता दर',
      'mission.inProgress': 'प्रगति में',
      'mission.completed': 'पूर्ण',
      'mission.progress': 'मिशन प्रगति (%)',
      
      // Simulation Results (Hindi)
      'simulation.title': 'सिमुलेशन परिणाम',
      'simulation.subtitle': '3D प्रक्षेपवक्र विश्लेषण और सटीकता परीक्षण',
      'simulation.accuracyComparison': 'सटीकता तुलना',
      'simulation.testResults': 'परीक्षण परिणाम',
      'simulation.brahmosTest': 'ब्रह्मोस परीक्षण',
      'simulation.agniTest': 'अग्नि-V परीक्षण',
      'simulation.prithviTest': 'पृथ्वी परीक्षण',
      'simulation.supersonicCruise': 'सुपरसोनिक क्रूज मिसाइल',
      'simulation.intercontinental': 'अंतरमहाद्वीपीय बैलिस्टिक मिसाइल',
      'simulation.surfaceToSurface': 'सतह से सतह मिसाइल',
      'simulation.accuracy': 'सटीकता (%)',
      
      // Settings (Hindi)
      'settings.title': 'सिस्टम कॉन्फ़िगरेशन',
      'settings.subtitle': 'डैशबोर्ड सेटिंग्स और प्राथमिकताएं',
      'settings.themeConfig': 'थीम कॉन्फ़िगरेशन',
      'settings.securitySettings': 'सुरक्षा सेटिंग्स',
      'settings.dashboardTheme': 'डैशबोर्ड थीम',
      'settings.drdoFormal': 'डीआरडीओ औपचारिक (डिफ़ॉल्ट)',
      'settings.lightMode': 'लाइट मोड',
      'settings.darkMode': 'डार्क मोड',
      'settings.tacticalMode': 'टैक्टिकल मोड',
      'settings.currentTheme': 'वर्तमान थीम',
      'settings.changesApply': 'परिवर्तन तुरंत सभी मॉड्यूल में लागू होते हैं',
      'settings.aesEncryption': 'AES-256 एन्क्रिप्शन',
      'settings.dataTransmissions': 'सभी डेटा ट्रांसमिशन एन्क्रिप्टेड',
      'settings.sessionTimeout': 'सेशन टाइमआउट',
      'settings.idleTimeout': '15 मिनट निष्क्रिय टाइमआउट',
      'settings.auditLogging': 'ऑडिट लॉगिंग',
      'settings.actionsLogged': 'सभी कार्य लॉग और निगरानी',
      'settings.adminAccess': 'एडमिन एक्सेस',
      
      // Security (Hindi)
      'security.secureConnection': 'सुरक्षित कनेक्शन',
      
      // Footer (Hindi)
      'footer.copyright': '© 2025 डीआरडीओ | वर्गीकृत',
      'footer.version': 'v2.0 सैन्य',
      
      // Status Messages (Hindi)
      'status.excellentStatus': 'उत्कृष्ट स्थिति',
      'status.mediumPriority': 'मध्यम प्राथमिकता',
      'status.aboveTarget': 'लक्ष्य से ऊपर',
      'status.operational': 'परिचालनात्मक',
      'status.inProgress': 'प्रगति में',
      'status.completed': 'पूर्ण',
      'status.fromYesterday': 'कल से'
    }
  }
};

i18n
  .use(LanguageDetector)
  .use(initReactI18next)
  .init({
    resources,
    lng: 'en', // default language
    fallbackLng: 'en',
    
    interpolation: {
      escapeValue: false, // react already does escaping
    },
    
    // Detection options
    detection: {
      order: ['localStorage', 'navigator', 'htmlTag'],
      caches: ['localStorage'],
    },
  });

export default i18n;
