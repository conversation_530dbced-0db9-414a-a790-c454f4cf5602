.liquid-effect {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95), rgba(0, 48, 135, 0.03));
  animation: liquidFlow 10s ease-in-out infinite;
  z-index: -1;
  pointer-events: none;
  opacity: 0.5;
}

@keyframes liquidFlow {
  0% { transform: translateY(0) scale(1); }
  50% { transform: translateY(-2%) scale(1.01); }
  100% { transform: translateY(0) scale(1); }
}

@media (max-width: 768px) {
  .liquid-effect {
    display: none;
  }
}
