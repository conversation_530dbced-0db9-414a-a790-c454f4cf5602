.liquid-effect {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, 
    rgba(255, 255, 255, 0.8), 
    rgba(0, 48, 135, 0.1),
    rgba(255, 153, 51, 0.05)
  );
  animation: liquidFlow 8s ease-in-out infinite;
  z-index: -1;
  pointer-events: none;
  border-radius: 20px;
}

@keyframes liquidFlow {
  0% { transform: translateY(0) scale(1); }
  50% { transform: translateY(-3%) scale(1.01); }
  100% { transform: translateY(0) scale(1); }
}

/* Additional liquid effects for different components */
.liquid-card {
  background: linear-gradient(135deg, 
    rgba(255, 255, 255, 0.95), 
    rgba(0, 48, 135, 0.05)
  );
  backdrop-filter: blur(15px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 16px;
  transition: all 0.3s ease;
}

.liquid-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 48, 135, 0.15);
}

@media (max-width: 768px) {
  .liquid-effect {
    display: none;
  }
}
