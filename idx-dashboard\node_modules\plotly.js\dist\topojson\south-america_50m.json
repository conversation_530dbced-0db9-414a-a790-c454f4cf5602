{"type": "Topology", "objects": {"coastlines": {"type": "GeometryCollection", "geometries": [{"type": "LineString", "arcs": [0]}, {"type": "LineString", "arcs": [1]}, {"type": "LineString", "arcs": [2]}, {"type": "LineString", "arcs": [3]}, {"type": "LineString", "arcs": [4]}, {"type": "LineString", "arcs": [5]}, {"type": "LineString", "arcs": [6]}, {"type": "LineString", "arcs": [7]}, {"type": "LineString", "arcs": [8]}, {"type": "LineString", "arcs": [9]}, {"type": "LineString", "arcs": [10]}, {"type": "LineString", "arcs": [11]}, {"type": "LineString", "arcs": [12]}, {"type": "LineString", "arcs": [13]}, {"type": "LineString", "arcs": [14]}, {"type": "LineString", "arcs": [15]}, {"type": "LineString", "arcs": [16, 17, 18]}, {"type": "LineString", "arcs": [19]}, {"type": "LineString", "arcs": [20]}, {"type": "LineString", "arcs": [21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74]}, {"type": "LineString", "arcs": [75]}, {"type": "LineString", "arcs": [76]}, {"type": "LineString", "arcs": [77]}, {"type": "LineString", "arcs": [78]}, {"type": "LineString", "arcs": [79]}, {"type": "LineString", "arcs": [80]}, {"type": "LineString", "arcs": [81]}, {"type": "LineString", "arcs": [82]}, {"type": "LineString", "arcs": [83]}, {"type": "LineString", "arcs": [84]}, {"type": "LineString", "arcs": [85]}, {"type": "LineString", "arcs": [86]}, {"type": "LineString", "arcs": [87]}, {"type": "LineString", "arcs": [88]}, {"type": "LineString", "arcs": [89]}, {"type": "LineString", "arcs": [90]}, {"type": "LineString", "arcs": [91]}, {"type": "LineString", "arcs": [92]}, {"type": "LineString", "arcs": [93]}, {"type": "LineString", "arcs": [94]}, {"type": "LineString", "arcs": [95]}, {"type": "LineString", "arcs": [96]}, {"type": "LineString", "arcs": [97]}, {"type": "LineString", "arcs": [98]}, {"type": "LineString", "arcs": [99]}, {"type": "LineString", "arcs": [100]}, {"type": "LineString", "arcs": [101]}, {"type": "LineString", "arcs": [102]}, {"type": "LineString", "arcs": [103]}, {"type": "LineString", "arcs": [104]}, {"type": "LineString", "arcs": [105]}, {"type": "LineString", "arcs": [106]}, {"type": "LineString", "arcs": [107]}, {"type": "LineString", "arcs": [108]}, {"type": "LineString", "arcs": [109]}, {"type": "LineString", "arcs": [110]}, {"type": "LineString", "arcs": [111]}, {"type": "LineString", "arcs": [112]}, {"type": "LineString", "arcs": [113]}, {"type": "LineString", "arcs": [114]}, {"type": "LineString", "arcs": [115]}, {"type": "LineString", "arcs": [116]}, {"type": "LineString", "arcs": [117]}, {"type": "LineString", "arcs": [118]}, {"type": "LineString", "arcs": [119]}, {"type": "LineString", "arcs": [120]}, {"type": "LineString", "arcs": [121]}, {"type": "LineString", "arcs": [122]}, {"type": "LineString", "arcs": [123]}, {"type": "LineString", "arcs": [124]}, {"type": "LineString", "arcs": [125]}, {"type": "LineString", "arcs": [126]}, {"type": "LineString", "arcs": [127]}, {"type": "LineString", "arcs": [128]}, {"type": "LineString", "arcs": [129]}, {"type": "LineString", "arcs": [130]}, {"type": "LineString", "arcs": [131]}, {"type": "LineString", "arcs": [132]}, {"type": "LineString", "arcs": [133]}, {"type": "LineString", "arcs": [134]}, {"type": "LineString", "arcs": [135]}, {"type": "LineString", "arcs": [136]}, {"type": "LineString", "arcs": [137]}, {"type": "LineString", "arcs": [138]}, {"type": "LineString", "arcs": [139]}, {"type": "LineString", "arcs": [140]}, {"type": "LineString", "arcs": [141]}, {"type": "LineString", "arcs": [142]}, {"type": "LineString", "arcs": [143]}, {"type": "LineString", "arcs": [144]}, {"type": "LineString", "arcs": [145]}, {"type": "LineString", "arcs": [146, 147, 148, 149, 150, 151, 152, 153, 154, 155]}, {"type": "LineString", "arcs": [156, 157, 158, 159, 160, 161, 162]}, {"type": "LineString", "arcs": [163, 164, 165, 166, 167, 168]}, {"type": "LineString", "arcs": [169, 170, 171, 172, 173, 174, 175]}, {"type": "LineString", "arcs": [176]}, {"type": "LineString", "arcs": [177]}, {"type": "LineString", "arcs": [178]}, {"type": "LineString", "arcs": [179]}, {"type": "LineString", "arcs": [180]}, {"type": "LineString", "arcs": [181]}, {"type": "LineString", "arcs": [182]}, {"type": "LineString", "arcs": [183]}, {"type": "LineString", "arcs": [184]}, {"type": "LineString", "arcs": [185]}, {"type": "LineString", "arcs": [186]}, {"type": "LineString", "arcs": [187]}, {"type": "LineString", "arcs": [188]}, {"type": "MultiLineString", "arcs": [[189], [190]]}, {"type": "LineString", "arcs": [191]}, {"type": "LineString", "arcs": [192]}, {"type": "LineString", "arcs": [193]}, {"type": "LineString", "arcs": [194]}, {"type": "LineString", "arcs": [195]}, {"type": "LineString", "arcs": [196]}, {"type": "LineString", "arcs": [197]}, {"type": "LineString", "arcs": [198]}, {"type": "LineString", "arcs": [199]}, {"type": "LineString", "arcs": [200]}, {"type": "LineString", "arcs": [201]}, {"type": "LineString", "arcs": [202]}, {"type": "LineString", "arcs": [203]}, {"type": "LineString", "arcs": [204]}, {"type": "LineString", "arcs": [205]}, {"type": "LineString", "arcs": [206]}, {"type": "LineString", "arcs": [207]}, {"type": "LineString", "arcs": [208]}, {"type": "LineString", "arcs": [209]}, {"type": "LineString", "arcs": [210]}, {"type": "LineString", "arcs": [211]}, {"type": "LineString", "arcs": [212]}, {"type": "LineString", "arcs": [213]}, {"type": "LineString", "arcs": [214]}, {"type": "LineString", "arcs": [215]}, {"type": "LineString", "arcs": [216]}, {"type": "LineString", "arcs": [217]}, {"type": "LineString", "arcs": [218]}, {"type": "LineString", "arcs": [219]}, {"type": "LineString", "arcs": [220]}, {"type": "LineString", "arcs": [221]}, {"type": "LineString", "arcs": [222]}, {"type": "LineString", "arcs": [223]}, {"type": "LineString", "arcs": [224]}, {"type": "LineString", "arcs": [225]}, {"type": "LineString", "arcs": [226]}, {"type": "LineString", "arcs": [227, 228, 229, 230, 231, 232, 233, 234, 235, 236, 237, 238, 239]}, {"type": "LineString", "arcs": [240]}, {"type": "LineString", "arcs": [241]}, {"type": "LineString", "arcs": [242]}, {"type": "LineString", "arcs": [243]}, {"type": "LineString", "arcs": [244, 245, 246, 247, 248, 249, 250]}, {"type": "LineString", "arcs": [251, 252, 253, 254, 255, 256, 257]}, {"type": "LineString", "arcs": [258, 259, 260, 261, 262, 263]}, {"type": "LineString", "arcs": [264, 265, 266]}, {"type": "LineString", "arcs": [267, 268, 269, 270, 271, 272, 273, 274, 275, 276, 277]}, {"type": "LineString", "arcs": [278, 279]}, {"type": "LineString", "arcs": [280, 281, 282, 283, 284, 285, 286, 287, 288]}, {"type": "LineString", "arcs": [289, 290, 291, 292, 293, 294, 295, 296]}, {"type": "LineString", "arcs": [297, 298, 299, 300, 301, 302, 303, 304, 305, 306]}, {"type": "LineString", "arcs": [307, 308, 309, 310, 311, 312, 313, 314]}, {"type": "LineString", "arcs": [315]}, {"type": "LineString", "arcs": [316]}, {"type": "LineString", "arcs": [317]}, {"type": "LineString", "arcs": [318]}, {"type": "LineString", "arcs": [319]}, {"type": "LineString", "arcs": [320]}, {"type": "LineString", "arcs": [321]}, {"type": "LineString", "arcs": [322, 323, 324, 325, 326, 327, 328, 329, 330, 331, 332, 333, 334, 335, 336, 337, 338, 339, 340, 341, 342, 343, 344, 345, 346, 347, 348, 349, 350, 351, 352, 353, 354, 355, 356, 357, 358, 359, 360, 361, 362, 363, 364, 365, 366, 367, 368, 369, 370, 371, 372, 373, 374, 375, 376, 377, 378, 379, 380, 381, 382, 383, 384, 385, 386, 387, 388, 389, 390, 391, 392, 393, 394, 395, 396, 397, 398, 399, 400, 401, 402, 403, 404, 405, 406, 407, 408, 409, 410, 411, 412, 413, 414, 415, 416, 417, 418, 419, 420, 421, 422, 423, 424, 425, 426, 427, 428, 429, 430, 431, 432, 433, 434, 435, 436, 437, 438, 439, 440, 441, 442, 443, 444, 445, 446, 447, 448, 449, 450, 451, 452, 453, 454, 455, 456, 457, 458, 459, 460, 461, 462, 463, 464, 465, 466, 467, 468, 469, 470, 471, 472, 473, 474, 475, 476, 477, 478, 479, 480, 481, 482, 483, 484, 485, 486, 487, 488, 489, 490, 491, 492, 493, 494, 495, 496, 497, 498, 499, 500, 501, 502, 503, 504, 505, 506, 507, 508, 509, 510, 511, 512, 513, 514, 515, 516, 517, 518, 519, 520, 521, 522, 523, 524, 525, 526, 527, 528, 529, 530, 531, 532, 533, 534, 535, 536, 537, 538, 539, 540, 541, 542, 543, 544, 545, 546, 547, 548, 549, 550, 551, 552, 553, 554, 555, 556, 557, 558, 559, 560, 561, 562, 563, 564, 565, 566, 567, 568, 569, 570, 571, 572, 573, 574, 575, 576, 577, 578, 579, 580, 581, 582, 583, 584, 585, 586, 587, 588, 589, 590, 591, 592, 593, 594, 595, 596, 597, 598, 599, 600, 601, 602, 603, 604, 605, 606, 607, 608, 609, 610, 611, 612, 613, 614, 615, 616, 617, 618, 619, 620, 621, 622, 623, 624, 625, 626, 627, 628, 629, 630, 631, 632, 633, 634, 635, 636, 637, 638, 639, 640, 641, 642, 643, 644, 645, 646, 647, 648, 649, 650, 651, 652, 653, 654, 655, 656, 657, 658, 659, 660, 661, 662, 663, 664, 665, 666, 667, 668, 669, 670, 671, 672, 673, 674, 675, 676, 677, 678, 679, 680, 681, 682, 683, 684, 685, 686, 687, 688, 689, 690, 691, 692, 693, 694, 695, 696, 697, 698, 699, 700, 701, 702, 703, 704, 705, 706, 707, 708, 709, 710, 711, 712, 713, 714, 715, 716, 717, 718, 719, 720, 721, 722, 723, 724, 725, 726, 727, 728, 729, 730, 731, 732, 733, 734, 735, 736, 737, 738, 739, 740, 741, 742, 743, 744, 745, 746, 747, 748, 749, 750, 751, 752, 753, 754]}, {"type": "LineString", "arcs": [755]}, {"type": "LineString", "arcs": [756]}, {"type": "LineString", "arcs": [757]}, {"type": "LineString", "arcs": [758]}, {"type": "LineString", "arcs": [759]}, {"type": "LineString", "arcs": [760]}]}, "land": {"type": "GeometryCollection", "geometries": [{"type": "Polygon", "arcs": [[-8]]}, {"type": "Polygon", "arcs": [[-10]]}, {"type": "Polygon", "arcs": [[-11]]}, {"type": "Polygon", "arcs": [[-12]]}, {"type": "Polygon", "arcs": [[-13]]}, {"type": "Polygon", "arcs": [[-17, -19, -18]]}, {"type": "Polygon", "arcs": [[-27, -26, -25, -24, -23, -22, -75, -74, -73, -72, -71, -70, -69, -68, -67, -66, -65, -64, -63, -62, -61, -60, -59, -58, -57, -56, -55, -54, -53, -52, -51, -50, -49, -48, -47, -46, -45, -44, -43, -42, -41, -40, -39, -38, -37, -36, -35, -34, -33, -32, -31, -30, -29, -28]]}, {"type": "Polygon", "arcs": [[-77]]}, {"type": "Polygon", "arcs": [[-79]]}, {"type": "Polygon", "arcs": [[-90]]}, {"type": "Polygon", "arcs": [[-119]]}, {"type": "Polygon", "arcs": [[-120]]}, {"type": "Polygon", "arcs": [[120]]}, {"type": "Polygon", "arcs": [[-122]]}, {"type": "Polygon", "arcs": [[-123]]}, {"type": "Polygon", "arcs": [[-124]]}, {"type": "Polygon", "arcs": [[-125]]}, {"type": "Polygon", "arcs": [[125]]}, {"type": "Polygon", "arcs": [[-127]]}, {"type": "Polygon", "arcs": [[-128]]}, {"type": "Polygon", "arcs": [[-129]]}, {"type": "Polygon", "arcs": [[-130]]}, {"type": "Polygon", "arcs": [[-131]]}, {"type": "Polygon", "arcs": [[-132]]}, {"type": "Polygon", "arcs": [[-133]]}, {"type": "Polygon", "arcs": [[-134]]}, {"type": "Polygon", "arcs": [[-135]]}, {"type": "Polygon", "arcs": [[-136]]}, {"type": "Polygon", "arcs": [[-137]]}, {"type": "Polygon", "arcs": [[-138]]}, {"type": "Polygon", "arcs": [[-139]]}, {"type": "Polygon", "arcs": [[-140]]}, {"type": "Polygon", "arcs": [[-141]]}, {"type": "Polygon", "arcs": [[-142]]}, {"type": "Polygon", "arcs": [[-143]]}, {"type": "Polygon", "arcs": [[-144]]}, {"type": "Polygon", "arcs": [[-145]]}, {"type": "Polygon", "arcs": [[-146]]}, {"type": "Polygon", "arcs": [[-154, -153, -152, -151, -150, -149, -148, -147, -156, -155]]}, {"type": "Polygon", "arcs": [[-160, -159, -158, -157, -163, -162, -161]]}, {"type": "Polygon", "arcs": [[-164, -169, -168, -167, -166, -165]]}, {"type": "Polygon", "arcs": [[-174, -173, -172, -171, -170, -176, -175]]}, {"type": "Polygon", "arcs": [[-210]]}, {"type": "Polygon", "arcs": [[-226]]}, {"type": "Polygon", "arcs": [[-227]]}, {"type": "Polygon", "arcs": [[-234, -233, -232, -231, -230, -229, -228, -240, -239, -238, -237, -236, -235]]}, {"type": "Polygon", "arcs": [[-242]]}, {"type": "Polygon", "arcs": [[-243]]}, {"type": "Polygon", "arcs": [[-244]]}, {"type": "Polygon", "arcs": [[-249, -248, -247, -246, -245, -251, -250]]}, {"type": "Polygon", "arcs": [[-254, -253, -252, -258, -257, -256, -255]]}, {"type": "Polygon", "arcs": [[-263, -262, -261, -260, -259, -264]]}, {"type": "Polygon", "arcs": [[-265, -267, -266]]}, {"type": "Polygon", "arcs": [[-276, -275, -274, -273, -272, -271, -270, -269, -268, -278, -277]]}, {"type": "Polygon", "arcs": [[-279, -280]]}, {"type": "Polygon", "arcs": [[-289, -288, -287, -286, -285, -284, -283, -282, -281]]}, {"type": "Polygon", "arcs": [[-290, -297, -296, -295, -294, -293, -292, -291]]}, {"type": "Polygon", "arcs": [[-306, -305, -304, -303, -302, -301, -300, -299, -298, -307]]}, {"type": "Polygon", "arcs": [[-308, -315, -314, -313, -312, -311, -310, -309]]}, {"type": "Polygon", "arcs": [[-316]]}, {"type": "Polygon", "arcs": [[-317]]}, {"type": "Polygon", "arcs": [[-319]]}, {"type": "Polygon", "arcs": [[-320]]}, {"type": "Polygon", "arcs": [[-321]]}, {"type": "Polygon", "arcs": [[-322]]}, {"type": "Polygon", "arcs": [[-754, -753, -752, -751, -750, -749, -748, -747, -746, 943, 944, 945, 946, 947, 948, 949, 950, 951, 952, 953, 954, 955, 956, 957, 958, 959, 960, 961, 962, 963, 964, 965, 966, 967, 968, 969, 970, 971, 972, 973, 974, 975, 976, 977, 978, 979, 980, 981, 982, 983, 984, 985, -744, -743, -742, -741, -740, -739, -738, -737, -736, -735, -734, -733, -732, -731, -730, -729, -728, -727, -726, -725, -724, -723, -722, -721, -720, -719, -718, -717, -716, -715, -714, -713, -712, -711, -710, -709, -708, -707, -706, -705, -704, -703, -702, -701, -700, -699, -698, -697, -696, -695, -694, -693, -692, -691, -690, -689, -688, -687, -686, -685, -684, -683, -682, -681, -680, -679, -678, -677, -676, -675, -674, -673, -672, -671, -670, -669, -668, -667, -666, -665, -664, -663, -662, -661, -660, -659, -658, -657, -656, -655, -654, -653, -652, -651, -650, -649, -648, -647, -646, -645, -644, -643, -642, -641, -640, -639, -638, -637, -636, -635, -634, -633, -632, -631, -630, -629, -628, -627, -626, -625, -624, -623, -622, -621, -620, -619, -618, -617, -616, -615, -614, -613, -612, -611, -610, -609, -608, -607, -606, -605, -604, -603, -602, -601, -600, -599, -598, -597, -596, -595, -594, -593, -592, -591, -590, -589, -588, -587, -586, -585, -584, -583, -582, -581, -580, -579, -578, -577, -576, -575, -574, -573, -572, -571, -570, -569, -568, -567, -566, -565, -564, -563, -562, -561, -560, -559, -558, -557, -556, -555, -554, -553, -552, -551, -550, -549, -548, -547, -546, -545, -544, -543, -542, -541, -540, -539, -538, -537, -536, -535, -534, -533, -532, -531, -530, -529, -528, -527, -526, -525, -524, -523, -522, -521, -520, -519, -518, -517, -516, -515, -514, -513, -512, -511, -510, -509, -508, -507, -506, -505, -504, -503, -502, -501, -500, -499, -498, -497, -496, -495, -494, -493, -492, -491, -490, -489, -488, -487, -486, -485, -484, -483, -482, -481, -480, -479, -478, -477, -476, -475, -474, -473, -472, -471, -470, -469, -468, -467, -466, -465, -464, -463, -462, -461, -460, -459, -458, -457, -456, -455, -454, -453, -452, -451, -450, -449, -448, -447, -446, -445, -444, -443, -442, -441, -440, -439, -438, -437, -436, -435, -434, -433, -432, -431, -430, -429, -428, -427, -426, -425, -424, -423, -422, -421, -420, -419, -418, -417, -416, -415, -414, -413, -412, -411, -410, -409, -408, -407, -406, -405, -404, -403, -402, -401, -400, -399, -398, -397, -396, -395, -394, -393, -392, -391, -390, -389, -388, -387, -386, -385, -384, -383, -382, -381, -380, -379, -378, -377, -376, -375, -374, -373, -372, -371, -370, -369, -368, -367, -366, -365, -364, -363, -362, -361, -360, -359, -358, -357, -356, -355, -354, -353, -352, -351, -350, -349, -348, -347, -346, -345, -344, -343, -342, -341, -340, -339, -338, -337, -336, -335, -334, -333, -332, -331, -330, -329, -328, -327, -326, -325, -324, 986]]}, {"type": "Polygon", "arcs": [[-759]]}]}, "ocean": {"type": "GeometryCollection", "geometries": [{"type": "Polygon", "arcs": [[-758, 987, 15, 988, 5, 989, 322, 323, 324, 325, 326, 327, 328, 329, 330, 331, 332, 333, 334, 335, 336, 337, 338, 339, 340, 341, 342, 343, 344, 345, 346, 347, 348, 349, 350, 351, 352, 353, 354, 355, 356, 357, 358, 359, 360, 361, 362, 363, 364, 365, 366, 367, 368, 369, 370, 371, 372, 373, 374, 375, 376, 377, 378, 379, 380, 381, 382, 383, 384, 385, 386, 387, 388, 389, 390, 391, 392, 393, 394, 395, 396, 397, 398, 399, 400, 401, 402, 403, 404, 405, 406, 407, 408, 409, 410, 411, 412, 413, 414, 415, 416, 417, 418, 419, 420, 421, 422, 423, 424, 425, 426, 427, 428, 429, 430, 431, 432, 433, 434, 435, 436, 437, 438, 439, 440, 441, 442, 443, 444, 445, 446, 447, 448, 449, 450, 451, 452, 453, 454, 455, 456, 457, 458, 459, 460, 461, 462, 463, 464, 465, 466, 467, 468, 469, 470, 471, 472, 473, 474, 475, 476, 477, 478, 479, 480, 481, 482, 483, 484, 485, 486, 487, 488, 489, 490, 491, 492, 493, 494, 495, 496, 497, 498, 499, 500, 501, 502, 503, 504, 505, 506, 507, 508, 509, 510, 511, 512, 513, 514, 515, 516, 517, 518, 519, 520, 521, 522, 523, 524, 525, 526, 527, 528, 529, 530, 531, 532, 533, 534, 535, 536, 537, 538, 539, 540, 541, 542, 543, 544, 545, 546, 547, 548, 549, 550, 551, 552, 553, 554, 555, 556, 557, 558, 559, 560, 561, 562, 563, 564, 565, 566, 567, 568, 569, 570, 571, 572, 573, 574, 575, 576, 577, 578, 579, 580, 581, 582, 583, 584, 585, 586, 587, 588, 589, 590, 591, 592, 593, 594, 595, 596, 597, 598, 599, 600, 601, 602, 603, 604, 605, 606, 607, 608, 609, 610, 611, 612, 613, 614, 615, 616, 617, 618, 619, 620, 621, 622, 623, 624, 625, 626, 627, 628, 629, 630, 631, 632, 633, 634, 635, 636, 637, 638, 639, 640, 641, 642, 643, 644, 645, 646, 647, 648, 649, 650, 651, 652, 653, 654, 655, 656, 657, 658, 659, 660, 661, 662, 663, 664, 665, 666, 667, 668, 669, 670, 671, 672, 673, 674, 675, 676, 677, 678, 679, 680, 681, 682, 683, 684, 685, 686, 687, 688, 689, 690, 691, 692, 693, 694, 695, 696, 697, 698, 699, 700, 701, 702, 703, 704, 705, 706, 707, 708, 709, 710, 711, 712, 713, 714, 715, 716, 717, 718, 719, 720, 721, 722, 723, 724, 725, 726, 727, 728, 729, 730, 731, 732, 733, 734, 735, 736, 737, 738, 739, 740, 741, 742, 743, 744, 745, 746, 747, 748, 749, 750, 751, 752, 753, 754, 990, 194, 991, 190, 189, 992, 87, 993, 184, 994], [187], [176], [181], [759], [191], [0], [88], [1], [2], [185], [186], [19], [193], [180], [183], [219], [188], [178], [179], [177], [223], [221], [224], [222], [182], [756], [20], [14], [83], [85], [75], [84], [210], [81], [80], [86], [212], [13], [195], [79], [199], [200], [760], [192], [197], [196], [201], [82], [214], [216], [213], [217], [-91], [755], [207], [215], [203], [202], [218], [208], [77], [198], [220], [205], [211], [206], [209], [78], [204], [243], [225], [278, 279], [311, 312, 313, 314, 307, 308, 309, 310], [291, 292, 293, 294, 295, 296, 289, 290], [28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 21, 22, 23, 24, 25, 26, 27], [239, 227, 228, 229, 230, 231, 232, 233, 234, 235, 236, 237, 238], [280, 281, 282, 283, 284, 285, 286, 287, 288], [275, 276, 277, 267, 268, 269, 270, 271, 272, 273, 274], [306, 297, 298, 299, 300, 301, 302, 303, 304, 305], [264, 265, 266], [247, 248, 249, 250, 244, 245, 246], [257, 251, 252, 253, 254, 255, 256], [261, 262, 263, 258, 259, 260], [146, 147, 148, 149, 150, 151, 152, 153, 154, 155], [170, 171, 172, 173, 174, 175, 169], [158, 159, 160, 161, 162, 156, 157], [163, 164, 165, 166, 167, 168], [131], [7], [89], [132], [10], [133], [-126], [18, 16, 17], [8], [130], [135], [118], [123], [137], [136], [142], [134], [122], [140], [143], [138], [121], [139], [11], [124], [-121], [129], [126], [145], [144], [119], [12], [128], [141], [127], [9], [758], [226], [319], [320], [318], [321], [315], [316], [241], [242], [240], [-118], [317], [76], [91], [93], [92], [98], [97], [104], [106], [103], [110], [105], [94], [95], [111], [99], [102], [4], [101], [113], [114], [6], [116], [115], [112], [107], [3], [96], [109], [100], [108]]}]}, "lakes": {"type": "GeometryCollection", "geometries": [{"type": "Polygon", "arcs": [[995]]}, {"type": "Polygon", "arcs": [[996]]}, {"type": "Polygon", "arcs": [[997]]}, {"type": "Polygon", "arcs": [[998]]}, {"type": "Polygon", "arcs": [[999]]}, {"type": "Polygon", "arcs": [[1000]]}, {"type": "Polygon", "arcs": [[1001]]}, {"type": "Polygon", "arcs": [[1002, 1003]]}]}, "rivers": {"type": "GeometryCollection", "geometries": [{"type": "MultiLineString", "arcs": [[761], [762], [763], [764, 765], [766]]}, {"type": "MultiLineString", "arcs": [[767, 768, 769], [770], [771]]}, {"type": "MultiLineString", "arcs": [[772, 773, 774], [775], [776, 777]]}, {"type": "MultiLineString", "arcs": [[778], [779]]}, {"type": "LineString", "arcs": [780]}, {"type": "LineString", "arcs": [781, 782, 783, 784, 785, 786, 787, 788, 789, 790, 791]}, {"type": "LineString", "arcs": [792, 793, 794, 795, 796, 797, 798, 799, 800, 801, 802, 803, 804, 805, 806, 807, 808, 809, 810]}, {"type": "LineString", "arcs": [811, 812]}, {"type": "LineString", "arcs": [813]}, {"type": "LineString", "arcs": [814]}, {"type": "LineString", "arcs": [815]}, {"type": "MultiLineString", "arcs": [[816], [817]]}, {"type": "MultiLineString", "arcs": [[818], [819]]}, {"type": "LineString", "arcs": [820]}, {"type": "LineString", "arcs": [821]}, {"type": "MultiLineString", "arcs": [[822], [823], [824], [825, 826, 827], [828], [829], [830]]}, {"type": "LineString", "arcs": [831, 832]}, {"type": "LineString", "arcs": [833]}, {"type": "LineString", "arcs": [834]}, {"type": "LineString", "arcs": [835]}, {"type": "LineString", "arcs": [836]}, {"type": "LineString", "arcs": [837, 838]}, {"type": "LineString", "arcs": [839]}, {"type": "MultiLineString", "arcs": [[840], [841]]}, {"type": "LineString", "arcs": [842]}, {"type": "LineString", "arcs": [843]}, {"type": "LineString", "arcs": [844]}, {"type": "LineString", "arcs": [845, 846, 847]}, {"type": "MultiLineString", "arcs": [[848], [849, 850, 851, 852, 853, 854, 855]]}, {"type": "LineString", "arcs": [856]}, {"type": "MultiLineString", "arcs": [[857, 858, 859, 860], [861, 862], [863], [864], [865]]}, {"type": "MultiLineString", "arcs": [[866], [867, 868]]}, {"type": "MultiLineString", "arcs": [[869, 870], [871], [872]]}, {"type": "MultiLineString", "arcs": [[873], [874], [875], [876], [877, 878], [879, 880], [881, 882, 883, 884], [885, 886, 887], [888], [889, 890], [891, 892, 893, 894]]}, {"type": "LineString", "arcs": [895, 896, 897, 898]}, {"type": "LineString", "arcs": [899]}, {"type": "LineString", "arcs": [900]}, {"type": "LineString", "arcs": [901]}, {"type": "LineString", "arcs": [902]}, {"type": "MultiLineString", "arcs": [[903, 904, 905], [906, 907, 908, 909, 910, 911, 912, 913, 914, 915, 916, 917, 918, 919, 920]]}, {"type": "LineString", "arcs": [921, 922, 923, 924, 925]}, {"type": "MultiLineString", "arcs": [[926, 927, 928], [929]]}, {"type": "LineString", "arcs": [930]}, {"type": "LineString", "arcs": [931, 932, 933, 934, 935, 936, 937, 938, 939, 940]}, {"type": "MultiLineString", "arcs": [[941], [942]]}]}, "countries": {"type": "GeometryCollection", "geometries": [{"type": "MultiPolygon", "properties": {"ct": [-66.19, 7.12]}, "id": "VEN", "arcs": [[[-244]], [[-210]], [[-79]], [[-226]], [[1004, 1005, 1006, 1007, 1008, -853, 1009, -851, 1010, 858, 1011, -847, 1012, -752, -751, -750, -749, -748]]]}, {"type": "Polygon", "properties": {"ct": [-56.02, -32.8]}, "id": "URY", "arcs": [[-333, -941, 1013, 1014, 1015, 1016]]}, {"type": "MultiPolygon", "properties": {"ct": [-58.76, -51.74]}, "id": "FLK", "arcs": [[[-8]], [[-11]], [[-134]], [[-90]], [[125]], [[-133]]]}, {"type": "Polygon", "properties": {"ct": [-55.91, 4.13]}, "id": "SUR", "arcs": [[943, 1017, 1018, 1019, 1020, -746]]}, {"type": "Polygon", "properties": {"ct": [-74.38, -9.15]}, "id": "PER", "arcs": [[1021, 1022, 1023, 1024, 1025, 1026, 1027, 1028, 1029, 1030, 1031, -326, 1032, 1033, -791, 1034, -789]]}, {"type": "Polygon", "properties": {"ct": [-58.4, -23.23]}, "id": "PRY", "arcs": [[1035, 1036, 1037, 1038, 1039, -775, -774, -773, 1040, 1041, 880, -895, -899, 1042, 1043]]}, {"type": "Polygon", "properties": {"ct": [-58.98, 4.79]}, "id": "GUY", "arcs": [[-1005, -747, -1021, 1044, 1045, 1046, 1047, 1048, 1049, 1050, 1051, 1052, 1053]]}, {"type": "MultiPolygon", "properties": {"ct": [-78.39, -1.45]}, "id": "ECU", "arcs": [[[-1033, -325, 1054]], [[-227]], [[-242]], [[-316]], [[-317]], [[-319]], [[-320]], [[-321]], [[-322]]]}, {"type": "MultiPolygon", "properties": {"ct": [-73.08, 3.91]}, "id": "COL", "arcs": [[[-1013, 846, -1012, -859, -1011, 850, -1010, 852, 1055, 1056, 1057, 788, -1035, 790, -1034, -1055, -324, 986, -754, -753]], [[-243]]]}, {"type": "MultiPolygon", "properties": {"ct": [-71.31, -35.75]}, "id": "CHL", "arcs": [[[-759]], [[-1032, 1058, 1059, -327]], [[1060, -18]], [[-136]], [[-10]], [[-12]], [[120]], [[-123]], [[-130]], [[-135]], [[-141]], [[-124]], [[-143]], [[-145]], [[-13]], [[-120]], [[-122]], [[-125]], [[-127]], [[-128]], [[-129]], [[-139]], [[-140]], [[-142]], [[-144]], [[-146]], [[-77]], [[-119]], [[-137]], [[-138]]]}, {"type": "MultiPolygon", "properties": {"ct": [-53.12, -10.84]}, "id": "BRA", "arcs": [[[-1009, -1008, -1007, -1006, -1054, -1053, -1052, -1051, -1050, -1049, -1048, -1047, -1046, -1045, -1020, -1019, -1018, 944, 945, 946, 947, 948, 949, 950, 951, 952, 953, 954, 955, 956, 957, 958, 959, 960, 961, 962, 963, 964, 965, 966, 967, 968, 969, 970, 971, 972, 973, 974, 975, 976, 977, 978, 979, 980, 981, 982, 983, 984, 985, -744, -743, -742, -741, -740, -739, -738, -737, -736, -735, -734, -733, -732, -731, -730, -729, -728, -727, -726, -725, -724, -723, -722, -721, -720, -719, -718, -717, -716, -715, -714, -713, -712, -711, -710, -709, -708, -707, -706, -705, -704, -703, -702, -701, -700, -699, -698, -697, -696, -695, -694, -693, -692, -691, -690, -689, -688, -687, -686, -685, -684, -683, -682, -681, -680, -679, -678, -677, -676, -675, -674, -673, -672, -671, -670, -669, -668, -667, -666, -665, -664, -663, -662, -661, -660, -659, -658, -657, -656, -655, -654, -653, -652, -651, -650, -649, -648, -647, -646, -645, -644, -643, -642, -641, -640, -639, -638, -637, -636, -635, -634, -633, -632, -631, -630, -629, -628, -627, -626, -625, -624, -623, -622, -621, -620, -619, -618, -617, -616, -615, -614, -613, -612, -611, -610, -609, -608, -607, -606, -605, -604, -603, -602, -601, -600, -599, -598, -597, -596, -595, -594, -593, -592, -591, -590, -589, -588, -587, -586, -585, -584, -583, -582, -581, -580, -579, -578, -577, -576, -575, -574, -573, -572, -571, -570, -569, -568, -567, -566, -565, -564, -563, -562, -561, -560, -559, -558, -557, -556, -555, -554, -553, -552, -551, -550, -549, -548, -547, -546, -545, -544, -543, -542, -541, -540, -539, -538, -537, -536, -535, -534, -533, -532, -531, -530, -529, -528, -527, -526, -525, -524, -523, -522, -521, -520, -519, -518, -517, -516, -515, -514, -513, -512, -511, -510, -509, -508, -507, -506, -505, -504, -503, -502, -501, -500, -499, -498, -497, -496, -495, -494, -493, -492, -491, -490, -489, -488, -487, -486, -485, -484, -483, -482, -481, -480, -479, -478, -477, -476, -475, -474, -473, -472, -471, -470, -469, -468, -467, -466, -465, -464, -463, -462, -461, -460, -459, -458, -457, -456, -455, -454, -453, -452, -451, -450, -449, -448, -447, -446, -445, -444, -443, -442, -441, -440, -439, -438, -437, -436, -435, -434, -433, -432, -431, -430, -429, -428, -427, -426, -425, -424, -423, -422, -421, -420, -419, -418, -417, -416, -415, -414, -413, -412, -411, -410, -409, -408, -407, -406, -405, -404, -403, -402, -401, -400, -399, -398, -397, -396, -395, -394, -393, -392, -391, -390, -389, -388, -387, -386, -385, -384, -383, -382, -381, -380, -379, -378, -377, -376, -375, -374, -373, -372, -371, -370, -369, -368, -367, -366, -365, -364, -363, -362, -361, -360, -359, -358, -357, -356, -355, -354, -353, -352, -351, -350, -349, -348, -347, -346, -345, -344, -343, -342, -341, -340, -339, -338, -337, -336, -335, -334, -1017, -1016, -1015, 1061, -939, 1062, 1063, 1064, -1041, 772, 773, 774, -1040, -1039, -1038, -1037, -1036, 1065, 1066, 1067, 1068, 1069, 832, 837, 1070, 1071, 1072, 1073, 1074, -1030, -1029, -1028, -1027, -1026, -1025, -1024, -1023, -1022, -1058, -1057, -1056]], [[-27, -26, -25, -24, -23, -22, -75, -74, -73, -72, -71, -70, -69, -68, -67, -66, -65, -64, -63, -62, -61, -60, -59, -58, -57, -56, -55, -54, -53, -52, -51, -50, -49, -48, -47, -46, -45, -44, -43, -42, -41, -40, -39, -38, -37, -36, -35, -34, -33, -32, -31, -30, -29, -28]], [[-154, -153, -152, -151, -150, -149, -148, -147, -156, -155]], [[-160, -159, -158, -157, -163, -162, -161]], [[-174, -173, -172, -171, -170, -176, -175]], [[-249, -248, -247, -246, -245, -251, -250]], [[-254, -253, -252, -258, -257, -256, -255]], [[-263, -262, -261, -260, -259, -264]], [[-265, -267, -266]], [[-276, -275, -274, -273, -272, -271, -270, -269, -268, -278, -277]], [[-279, -280]], [[-289, -288, -287, -286, -285, -284, -283, -282, -281]], [[-290, -297, -296, -295, -294, -293, -292, -291]], [[-306, -305, -304, -303, -302, -301, -300, -299, -298, -307]], [[-308, -315, -314, -313, -312, -311, -310, -309]], [[-234, -233, -232, -231, -230, -229, -228, -240, -239, -238, -237, -236, -235]], [[-164, -169, -168, -167, -166, -165]]]}, {"type": "Polygon", "properties": {"ct": [-64.69, -16.71]}, "id": "BOL", "arcs": [[-1031, -1075, -1074, -1073, -1072, -1071, -838, -833, -1070, -1069, -1068, -1067, -1066, -1044, 1075, -897, 1076, -1059]]}, {"type": "MultiPolygon", "properties": {"ct": [-65.16, -35.18]}, "id": "ARG", "arcs": [[[-1014, 940, -332, -331, -330, -329, -328, -1060, -1077, 896, -1076, -1043, 898, 894, -881, -1042, -1065, -1064, -1063, 938, -1062]], [[-1061, -17, -19]], [[-131]], [[-132]]]}]}, "subunits": {"type": "GeometryCollection", "geometries": [{"type": "Polygon", "properties": {"ct": [-70.47, -9.3], "gu": "BRA"}, "id": "AC", "arcs": [[1077, 1072, 1078, 1074, -1030, 1079, -1028, 1080, 1081, 1082]]}, {"type": "Polygon", "properties": {"ct": [-62.84, -10.92], "gu": "BRA"}, "id": "RO", "arcs": [[-1083, 1083, 1084, 1085, 1069, 832, 837, 1070, 1086]]}, {"type": "Polygon", "properties": {"ct": [-61.41, 2.07], "gu": "BRA"}, "id": "RR", "arcs": [[1087, -855, 1088, -1006, -1054, 1089, -1052, 1090, -1050, 1091, -1048, 1092, -1046, 1093]]}, {"type": "Polygon", "properties": {"ct": [-64.72, -4.18], "gu": "BRA"}, "id": "AM", "arcs": [[-1082, 1094, -1026, 1095, -1024, 1096, -1022, -1058, 1097, -1056, -1009, 1098, -1007, -1089, 854, -1088, 1099, 1100, -925, -924, 1101, 1102, -1084]]}, {"type": "MultiPolygon", "properties": {"ct": [-53.27, -4.18], "gu": "BRA"}, "id": "PA", "arcs": [[[-1094, -1045, -1020, 1103, 1104, 1105, -712, 1106, -710, -709, 1107, -707, 1108, 1109, 1110, 1111, 1112, -701, 1113, -699, 1114, 1115, 1116, 1117, -694, 1118, 1119, -691, 1120, 1121, -688, 1122, 1123, -685, 1124, 1125, 1126, 1127, -680, 1128, -678, 1129, -676, 1130, -674, 1131, -672, 1132, -670, 1133, -668, 1134, -666, 1135, -664, 1136, -662, 1137, -660, 1138, -658, 1139, -656, 1140, 1141, 1142, -652, 1143, 1144, -649, 1145, 1146, 1147, -645, 1148, 1149, 1150, -641, 1151, -639, 1152, 1153, -810, 1154, -808, 1155, -806, 1156, 1157, 924, -1101, -1100]], [[1158, 1159, 1160, -238, 1161, -236, 1162, 1163, -233, 1164, 1165, 1166, 1167]], [[-75, 1168, -73, 1169, -71, 1170, -69, 1171, 1172, 1173, 1174, -64, 1175, -62, 1176, -60, 1177, 1178, 1179, -56, 1180, 1181, -53, 1182, -51, 1183, 1184, -48, 1185, -46, 1186, 1187, 1188, 1189, 1190, 1191, -39, 1192, -37, 1193, -35, 1194, 1195, 1196, -31, 1197, 1198, -28, 1199, 1200, 1201, -24, 1202, -22]], [[-281, -289, 1203, 1204, -286, 1205, -284, 1206, 1207]], [[1208, -291, 1209, 1210, 1211, 1212, 1213, 1214]], [[-276, 1215, 1216, -273, 1217, 1218, 1219, 1220, 1221, -278, 1222]], [[-307, 1223, -305, 1224, 1225, 1226, 1227, -300, 1228, 1229]], [[1230, 1231, -308, -315, 1232, 1233, 1234, -311]]]}, {"type": "Polygon", "properties": {"ct": [-54.85, -20.33], "gu": "BRA"}, "id": "MS", "arcs": [[890, 776, 777, 888, 775, 885, 886, 1235, 882, 1236, -1039, 1237, 1238, -1036, 1065, 1239, 1240, -893, 1241, 1242]]}, {"type": "MultiPolygon", "properties": {"ct": [-51.98, 1.46], "gu": "BRA"}, "id": "AP", "arcs": [[[-1105, 1243, -1018, 1244, 945, 1245, 1246, 1247, 949, 1248, 951, 1249, 953, 1250, 1251, 1252, 957, 1253, 1254, 960, 1255, 962, 1256, 1257, 965, 1258, 1259, 968, 1260, 1261, 971, 1262, 973, 1263, 1264, 976, 1265, 1266, 979, 1267, 981, 1268, 1269, 1270, 985, 1271, 1272, -742, 1273, -740, 1274, -738, 1275, -736, 1276, 1277, -733, 1278, 1279, -730, 1280, -728, 1281, 1282, -725, 1283, 1284, -722, 1285, 1286, 1287, 1288, -717, 1289, 1290, -714, 1291]], [[1292, -280]]]}, {"type": "Polygon", "properties": {"ct": [-55.93, -12.96], "gu": "BRA"}, "id": "MT", "arcs": [[-1085, -1103, -1102, 923, -1158, 1293, -804, 1294, -802, -801, 1295, -799, 1296, -797, 1297, -795, 1298, -793, 1299, -1242, 892, -1241, 1300, 1067, 1301]]}, {"type": "Polygon", "properties": {"ct": [-51.63, -24.63], "gu": "BRA"}, "id": "PR", "arcs": [[-1237, -883, -1236, -887, 1302, 1303, -422, 1304, -420, 1305, -418, 1306, -416, 1307, 1308, 1309, 1064, -1041, 772, 1310, 774, -1040]]}, {"type": "Polygon", "properties": {"ct": [-47.79, -15.77], "gu": "BRA"}, "id": "DF", "arcs": [[1311]]}, {"type": "Polygon", "properties": {"ct": [-49.61, -16.02], "gu": "BRA"}, "id": "GO", "arcs": [[-1243, -1300, 792, -1299, 794, -1298, 796, -1297, 798, -1296, 800, 811, 1312, 1313, 1314, 1315, 1316, 1317, 870, 771, 871, 770, 872, -770, 1318, -768, 889]]}, {"type": "Polygon", "properties": {"ct": [-48.33, -10.14], "gu": "BRA"}, "id": "TO", "arcs": [[-1157, 805, -1156, 807, -1155, 809, -929, 1319, 1320, 1321, -1314, -1313, -812, 801, -1295, 803, -1294]]}, {"type": "MultiPolygon", "properties": {"ct": [-48.74, -22.26], "gu": "BRA"}, "id": "SP", "arcs": [[[-886, -776, -889, -778, 764, -829, 1322, -827, 1323, 1324, 1325, 1326, -438, 1327, -436, 1328, -434, 1329, -432, 1330, 1331, -429, 1332, 1333, -426, 1334, -424, 1335, -1303]], [[1336, 1337, -174, 1338, -172, 1339, -170]]]}, {"type": "MultiPolygon", "properties": {"ct": [-45.29, -5.11], "gu": "BRA"}, "id": "MA", "arcs": [[[-1154, 1340, 1341, 1342, 1343, -634, 1344, -632, 1345, -630, 1346, 1347, -627, 1348, -625, 1349, -623, 1350, -621, 1351, -619, 1352, -617, 1353, 1354, -614, 1355, 1356, -611, 1357, 1358, 1359, -607, 1360, -605, 1361, -603, 1362, -601, 1363, -599, 1364, -597, 1365, -595, 1366, 1367, -592, 1368, -590, 1369, 1370, 1371, 1372, -1320, 928]], [[1373, -246, 1374, -251, 1375, 1376, -248]], [[1377, 1378, -265]]]}, {"type": "MultiPolygon", "properties": {"ct": [-42.67, -22.19], "gu": "BRA"}, "id": "RJ", "arcs": [[[-1325, 1379, 1380, 1381, 1382, -476, -475, 1383, 1384, 1385, 1386, -470, 1387, -468, 1388, 1389, 1390, 1391, -463, 1392, 1393, 1394, 1395, 1396, 1397, 1398, 1399, -454, 1400, -452, 1401, 1402, -449, 1403, -447, 1404, 1405, 1406, 1407, -442, 1408, 1409]], [[-152, 1410, 1411, -149, 1412, 1413, 1414, 1415, -154, 1416]]]}, {"type": "Polygon", "properties": {"ct": [-42.99, -7.41], "gu": "BRA"}, "id": "PI", "arcs": [[-1321, -1373, 1417, -586, 1418, 1419, 1420, 1421, 1422, 1423]]}, {"type": "Polygon", "properties": {"ct": [-44.68, -18.45], "gu": "BRA"}, "id": "MG", "arcs": [[-777, -891, -890, 767, -1319, 769, -873, -771, -872, -772, -871, -1318, -1316, 1424, -905, 1425, 1426, -1381, -1380, -1324, 826, -1323, 828, -765]]}, {"type": "Polygon", "properties": {"ct": [-40.67, -19.57], "gu": "BRA"}, "id": "ES", "arcs": [[-1382, -1427, 1427, 1428, 1429, -486, 1430, 1431, -483, 1432, -481, 1433, 1434, -478, 1435]]}, {"type": "MultiPolygon", "properties": {"ct": [-41.71, -12.47], "gu": "BRA"}, "id": "BA", "arcs": [[[-1315, -1322, -1424, 1436, 907, 1437, 909, 1438, 1439, 912, 1440, 914, 1441, 1442, 1443, -522, 1444, 1445, -519, 1446, -517, 1447, 1448, -514, 1449, -512, 1450, 1451, 1452, 1453, -507, 1454, 1455, 1456, -504, 1457, 1458, 1459, 1460, -499, 1461, 1462, 1463, 1464, 1465, -493, 1466, -491, 1467, -489, 1468, -1428, -1426, 904, -1425]], [[1469, -262, 1470, 1471, -259, -264]], [[1472, -253, 1473, -258, 1474, -256, 1475]]]}, {"type": "Polygon", "properties": {"ct": [-39.63, -5.1], "gu": "BRA"}, "id": "CE", "arcs": [[-1422, 1476, 1477, -581, 1478, -579, 1479, -577, 1480, 1481, 1482, -573, 1483, 1484, 1485, 1486]]}, {"type": "Polygon", "properties": {"ct": [-37.44, -10.58], "gu": "BRA"}, "id": "SE", "arcs": [[-1443, 1487, 1488, 917, 1489, 919, 1490, 1491, -537, 1492, -535, 1493, 1494, -532, 1495, 1496, -529, 1497, -527, 1498, -525, 1499]]}, {"type": "Polygon", "properties": {"ct": [-36.62, -9.52], "gu": "BRA"}, "id": "AL", "arcs": [[-1442, 1500, 1501, -549, 1502, 1503, 1504, 1505, 1506, 1507, 1508, 1509, 1510, -539, 1511, -1491, -920, -1490, -918, -1489, -1488]]}, {"type": "Polygon", "properties": {"ct": [-36.68, -5.84], "gu": "BRA"}, "id": "RN", "arcs": [[-1485, 1512, -571, 1513, 1514, -568, 1515, -566, 1516, -564, 1517, 1518, 1519]]}, {"type": "Polygon", "properties": {"ct": [-38, -8.33], "gu": "BRA"}, "id": "PE", "arcs": [[-1423, -1487, 1520, 1521, -555, 1522, -553, 1523, -551, 1524, -1501, -915, -1441, -913, -1440, -1439, -910, -1438, -908, -1437]]}, {"type": "Polygon", "properties": {"ct": [-36.83, -7.12], "gu": "BRA"}, "id": "PB", "arcs": [[-1486, -1520, 1525, -561, 1526, -559, 1527, -557, 1528, -1521]]}, {"type": "MultiPolygon", "properties": {"ct": [-50.5, -27.25], "gu": "BRA"}, "id": "SC", "arcs": [[[-1309, 1529, -414, 1530, 1531, -411, 1532, -409, 1533, -407, 1534, -405, 1535, 1536, -402, 1537, 1538, 931, 1539, 933, 1540, 935, 1541, 937, 1062, 1542]], [[-167, 1543, 1544, -164, -169, 1545]], [[1546, 1547, 1548, 1549, 1550, -160, 1551]]]}, {"type": "Polygon", "properties": {"ct": [-53.32, -29.74], "gu": "BRA"}, "id": "RS", "arcs": [[-938, -1542, -936, -1541, -934, -1540, -932, -1539, 1552, -400, 1553, 1554, -397, 1555, 1556, -394, 1557, -392, 1558, 1559, -389, 1560, -387, 1561, 1562, -384, 1563, 1564, 1565, -380, 1566, -378, 1567, 1568, -375, 1569, -373, 1570, 1571, 1572, -369, 1573, 1574, -366, 1575, 1576, 1577, 1578, 1579, 1580, 1581, 1582, 1583, 1584, 1585, -354, 1586, -352, 1587, 1588, -349, 1589, 1590, 1591, 1592, 1593, -343, 1594, 1595, -340, 1596, -338, 1597, -336, 1598, -334, -1017, 1599, -1015, 1061, -939]]}]}}, "arcs": [[[2668, 9966], [-14, 7], [0, -2], [6, -7], [0, -3], [2, -1], [4, 2], [2, 2], [0, 2]], [[2743, 9983], [-6, -3], [-1, -2], [2, 0], [6, 2], [1, 2], [-2, 1]], [[2767, 9993], [-5, -5], [4, 1], [3, 3], [-2, 1]], [[6100, 593], [-6, 5], [-11, 2], [12, 6], [15, 2], [-7, 6], [4, 7], [-39, 9], [6, 6], [-16, -1], [-17, -5], [-5, -4], [-4, -1], [-5, -1], [-6, 2], [3, 11], [7, 4], [0, 4], [-8, 7], [-13, 0], [-6, -3], [-15, -1], [-10, -4], [-19, -4], [-9, -8], [-12, -8], [-2, -4], [6, -3], [6, 1], [15, 2], [14, 1], [-2, -6], [4, -5], [17, -4], [-41, -8], [13, -5], [6, 0], [21, 5], [8, 0], [7, -1], [-5, -7], [6, -2], [6, 1], [7, 11], [17, 1], [4, -2], [-1, -5], [14, 1], [28, -3], [13, 1]], [[5267, 571], [-21, 0], [13, 11], [-12, 9], [5, 6], [-14, 6], [-22, 1], [-7, -1], [-10, -5], [-11, -3], [2, -4], [-1, -4], [-35, -4], [3, -3], [4, -2], [-43, -8], [-8, -5], [-7, -7], [13, -1], [12, -3], [13, -3], [29, -3], [5, -2], [5, -2], [13, 3], [12, 7], [15, 1], [14, -1], [12, 2], [10, 4], [26, 7], [27, 4], [-14, 6], [-14, -2], [-14, -4]], [[3631, 0], [17, 3], [3, 2], [-4, 4], [-12, 6], [-37, 11], [-25, 3], [-21, -2], [-6, 1], [-13, -2], [-7, -7], [-3, -2], [-49, -5], [-11, -3], [-8, -7], [0, -2]], [[4442, 237], [12, -1], [12, 1], [10, 3], [6, 0], [6, -1], [11, 2], [9, 6], [9, 10], [8, 3], [10, -1], [11, -2], [12, 2], [15, 7], [2, 2], [-5, 4], [-20, 4], [-13, 1], [9, 3], [21, 4], [10, 5], [16, 4], [23, 7], [-11, 3], [-9, 4], [-7, 2], [-11, 2], [-3, 1], [0, 6], [6, 14], [27, 10], [9, 4], [-5, 8], [-17, 1], [-15, -3], [-57, -15], [-12, -6], [-22, -14], [-12, -9], [-11, -9], [-12, -8], [-38, -18], [-7, -5], [-1, -6], [3, -7], [3, -3], [17, -8], [11, -7]], [[6029, 1933], [-11, 3], [-13, 2], [6, 6], [16, 1], [-17, 12], [-7, 2], [-33, -2], [-8, -1], [-4, -4], [2, -5], [5, -5], [1, -3], [-5, 0], [-29, 7], [-5, 3], [-2, 6], [8, 1], [8, 0], [4, 5], [-6, 5], [-12, 1], [-27, -2], [-22, 6], [-10, 0], [5, -9], [-30, -14], [-1, -10], [5, -7], [1, -3], [-5, -2], [-12, -1], [-12, -3], [-7, -4], [-37, -16], [5, -4], [-16, -12], [0, -6], [17, -10], [19, -8], [8, 12], [12, 1], [13, -2], [14, 3], [-18, 17], [9, 1], [68, -10], [2, 8], [0, 3], [-6, 6], [49, 8], [17, 4], [10, 6], [21, 3], [24, 3], [1, 3], [5, 5]], [[9157, 1626], [-10, -2], [-10, 0], [-6, 2], [-6, 10], [-8, 8], [-9, 2], [-7, 8], [-6, 4], [-8, -3], [-3, -3], [-6, 0], [-13, 6], [-16, -2], [6, 8], [-14, 9], [-8, 0], [-6, 0], [-6, 2], [-12, 1], [-11, -4], [-14, 5], [-18, 1], [-20, 5], [-1, 3], [-22, -1], [-59, 0], [-10, -2], [15, -2], [42, -1], [-10, -4], [-1, -6], [9, -4], [19, 2], [49, -13], [10, -4], [11, -3], [17, 0], [5, -2], [4, -5], [13, -6], [15, -3], [18, -2], [5, -2], [3, -4], [19, -13], [9, -9], [18, -8], [6, -1], [21, 3], [10, 5], [10, 3], [-17, 5], [-1, 8], [4, 9]], [[3698, 2804], [17, 0], [11, -1], [10, 0], [10, 2], [5, 6], [-1, 14], [14, 3], [16, 6], [9, 9], [5, 6], [-10, 9], [-8, 9], [-15, 6], [-14, 9], [-3, 3], [11, 5], [8, 1], [3, 3], [12, 1], [12, 3], [-8, 8], [-1, 8], [13, 4], [2, 9], [-4, 3], [-3, 12], [-6, 7], [-2, 9], [-29, 2], [-43, 9], [-4, -3], [5, -4], [1, -3], [-5, -12], [-1, -6], [-1, -5], [-13, -12], [-1, -5], [0, -6], [-1, -6], [-3, -6], [0, -5], [6, -11], [-8, -30], [-11, -22], [-12, -11], [-2, -5], [4, -3], [17, -6], [18, -4]], [[5775, 1928], [8, 6], [11, 3], [7, 4], [5, 5], [9, 3], [3, 6], [-7, 4], [-10, 3], [-11, -5], [-4, 1], [-31, -4], [-11, -1], [-8, 4], [-10, 2], [-14, -2], [-19, -8], [-20, 2], [-23, 7], [-10, 5], [-7, -1], [7, -7], [-1, -4], [2, -2], [13, -7], [16, -3], [9, -6], [-5, -2], [-27, -5], [-9, 1], [-7, -2], [11, -5], [17, 2], [8, 2], [7, 0], [6, -1], [0, -4], [-8, -4], [-6, -4], [-17, -4], [-20, -7], [-24, 0], [-29, -11], [22, -10], [18, -4], [25, -1], [4, 3], [4, 1], [10, 0], [4, 2], [10, 7], [6, 9], [7, 0], [9, -1], [20, 1], [10, 2], [29, 17], [21, 13]], [[3567, 2122], [5, 9], [0, 16], [2, 4], [3, 3], [31, 12], [3, 0], [0, -2], [-1, -1], [-8, -4], [-3, -3], [-1, -9], [-7, -3], [-3, -7], [0, -3], [9, -9], [-1, -7], [2, -6], [7, -8], [8, -1], [16, 1], [4, 2], [3, 8], [7, 6], [3, 8], [2, 10], [-1, 2], [-7, 1], [-1, 4], [6, 19], [2, 15], [-1, 16], [-8, 35], [-2, 5], [-3, 1], [-12, 1], [-14, 4], [-6, 0], [-15, -3], [-11, -6], [-1, -3], [5, -7], [-1, -8], [-12, -6], [-21, -6], [-4, -7], [17, -4], [1, -9], [-19, -3], [-7, 4], [-9, -1], [-15, -6], [-5, -3], [6, -6], [9, -5], [8, -4], [-4, -14], [-26, 1], [-7, -8], [3, -10], [14, 2], [21, -8], [19, -1], [15, 1], [5, 1]], [[3800, 2655], [7, -6], [17, -3], [35, 8], [12, 8], [8, 7], [11, 10], [-1, 4], [-10, 5], [-26, 8], [-26, 5], [-7, -1], [-2, -5], [1, -5], [-3, -5], [-4, -4], [-13, -7], [-6, -5], [4, -9], [3, -5]], [[3378, 9278], [-50, 11], [-13, 5], [-17, 9], [-7, 2], [-8, 0], [-18, 2], [-17, 3], [-13, 1], [-14, 0], [-60, 6], [-8, -2], [-7, -4], [-17, -2], [-17, 0], [-6, -2], [-10, -8], [-2, -7], [7, -7], [31, -3], [4, -1], [12, -14], [11, -3], [5, -3], [12, -12], [14, -2], [29, 0], [15, -2], [11, -6], [11, -7], [7, 14], [5, 4], [7, 2], [5, -5], [13, -1], [7, 6], [6, 7], [9, 1], [6, -1], [-3, -3], [15, -1], [6, -3], [14, -4], [16, 0], [16, 2], [13, 3], [-3, 6], [-17, 19]], [[4910, 9302], [-18, 3], [-18, 4], [-27, 3], [-4, 0], [4, -5], [-4, 0], [-5, 2], [-3, 3], [-5, 0], [-89, 2], [-36, 3], [-7, -1], [-7, -1], [-2, -7], [-6, -4], [-7, -3], [4, -5], [5, -4], [4, -6], [0, -8], [-3, -16], [7, -3], [19, 0], [7, -2], [9, 0], [9, 1], [9, 3], [25, -1], [13, 1], [14, -4], [12, 1], [6, -1], [6, 0], [15, 0], [24, 3], [19, 8], [8, 8], [9, 6], [14, 6], [-1, 14]], [[4405, 0], [-10, 10], [-17, 25], [-9, 9], [-42, 27], [-21, 5], [-8, 1], [-24, -4], [4, 12], [0, 6], [-8, 19], [-7, 3], [-22, 10], [-15, 4], [-139, -8], [-69, -8], [-17, -3], [-10, -3], [-7, -6], [-4, -6], [0, -6], [3, -5], [5, -5], [17, -6], [18, -4], [13, -6], [4, -11], [-7, -13], [-12, -17], [-2, -4], [-2, -6], [4, -7], [5, -3]], [[4893, 1615], [-35, 6], [-35, 7], [-32, 10], [-30, 13], [-28, 10], [-29, 7], [-32, 11], [-30, 13], [-25, 14], [-26, 14], [-11, 4], [-10, 6], [-20, 26], [-2, 1], [-33, 1], [-14, 4], [-4, 4], [0, 5], [5, 6], [7, 6], [14, 4], [14, -6], [-6, 10], [-8, 9], [-34, 21], [-8, 5]], [[4481, 1826], [-4, 2], [-14, 5], [-5, 1], [-41, -10], [-13, 0], [-35, 19], [-12, 0], [-11, -6], [-13, -10], [-14, -9], [-17, -8], [-7, -2], [-9, 0], [-13, 6], [-15, 4], [-21, -1], [-6, -2], [12, -6], [5, -5], [14, -4], [3, -2], [2, -3], [-5, -3], [-5, -2], [-8, -1], [-9, 0], [-11, -2], [-7, -7], [-2, -6], [0, -6], [6, -11], [12, -7], [17, -4], [17, -1], [31, 7], [17, 2], [17, 0], [18, -1], [17, -3], [5, -5], [1, -6], [-6, -2], [-42, -11], [-38, -7], [-20, -6], [-8, -4], [0, -13], [9, -13], [14, -10], [113, -26], [22, -6], [0, -2], [-5, -2], [-7, -1], [-6, -3], [-12, -8], [-8, -1], [-2, 3], [0, 6], [-6, 5], [-8, 3], [-29, 5], [-17, 6], [-9, -2], [-9, -5], [-17, -1], [-26, 0], [-10, 3], [-1, 8], [-19, 10], [-22, 5], [-14, 13], [24, 0], [12, 2], [-9, 10], [-13, 28], [-12, -3], [-11, -7], [7, -10], [-32, -7], [2, -12], [-1, -12], [15, -13], [17, -3], [14, -4], [10, -7], [25, -12], [-2, -5], [-15, 3], [-23, 0], [-18, 2], [2, 7], [-2, 7], [-14, 3], [-14, -2], [-4, -2], [-3, -4], [-3, -2], [-16, -3], [-11, 0], [-28, 5], [-6, 0], [-15, -5], [-10, -5], [-5, 0], [-16, 5], [-12, 1], [-3, -4], [-12, -2], [-3, -4], [4, -7], [10, -3], [55, 1], [31, -8], [43, -2], [27, -4], [34, -6], [31, 6], [3, -1], [3, -2], [15, -4], [15, 0], [19, 4], [18, 4], [7, 3], [19, -11], [15, -4], [57, -6], [34, 4], [6, 2], [22, 0]], [[4478, 1594], [5, 2], [18, 0], [23, 2], [16, 0], [30, -3], [31, -2], [95, -4], [28, -2], [43, -10], [17, -2], [16, 3], [16, 3], [16, 0], [16, 2], [15, 4], [16, 1], [17, -1], [17, -1], [19, 2], [18, 4], [13, 9], [11, 12], [-10, 4], [-17, 1], [-54, -3]], [[3641, 9513], [-22, 15], [-10, 5], [-21, 8], [-11, 3], [-36, 4], [-18, -1], [-27, 1], [-17, 2], [-11, -2], [-4, 2], [-2, 4], [3, 4], [21, 3], [-10, 6], [4, 5], [6, 5], [-5, 7], [-13, 6], [-25, 0], [-25, 2], [-27, 10], [-28, 5], [-14, 0], [-13, 1], [-6, 6], [-5, 2], [-6, 0], [-15, -3], [1, 3], [4, 4], [-13, 6], [-25, 14], [-6, -5], [-9, -7], [-7, 1], [-2, 6], [-14, 7], [17, -2], [9, 1], [6, 5], [-11, 3], [-11, 4], [-6, 4], [-23, 13], [-12, 2], [1, -3], [10, -6], [1, -2], [-6, -1], [-14, 2], [-32, 11], [-15, 7], [-25, 15], [-78, 27], [-4, -1], [-8, 1], [-9, 2], [-9, 1], [-40, -1], [-14, 2], [-11, 4], [-15, 7], [-13, 7], [-18, 17], [-25, 9], [5, 7], [-15, -2], [-5, 1], [-17, 6], [-13, 1], [-14, -1], [-14, 1], [-14, 3], [-13, 4], [-9, 7], [-5, 2], [-51, -1], [-20, -4], [-4, 1], [-14, 7], [2, 3], [-15, -3], [-30, -1], [-38, 5], [-37, 3], [-36, -4], [-34, -10], [-11, -2], [-73, -6], [-11, -2], [-113, -32], [-11, -5], [-23, -15], [-11, -10], [-3, -13], [8, -19], [-7, -4], [-8, 0], [-9, 1], [-6, -1], [-49, -15], [-1, -4], [7, -3], [7, 2], [15, 6], [8, 2], [9, 1], [9, 0], [1, -8], [-1, -8], [7, 1], [30, 12], [15, 3], [15, 1], [5, 4], [5, 12], [4, 6], [5, 2], [30, 1], [6, 1], [6, 2], [9, 0], [8, -2], [15, 4], [13, 8], [14, 6], [7, 3], [5, 4], [14, 9], [21, 9], [11, 7], [7, 3], [119, -1], [9, -1], [7, -2], [7, -2], [6, -4], [2, -6], [-3, -4], [-7, -4], [-31, -4], [-15, -4], [15, -10], [18, -8], [4, -2], [54, -1], [12, -9], [10, 1], [9, 3], [4, 7], [2, 6], [6, -6], [3, -8], [5, -3], [7, -3], [10, -2], [67, 1], [2, 2], [0, 4], [13, -9], [11, -11], [12, -6], [13, -5], [33, -9], [79, -16], [12, -3], [12, -1], [52, 7], [14, -3], [13, -8], [8, -10], [6, -13], [6, -25], [6, -5], [6, -4], [14, -5], [28, -17], [17, -5], [20, 0], [38, -3], [18, 0], [17, -1], [17, -4], [3, -3], [2, -5], [12, -8], [2, -4], [-1, -4], [-7, -7], [-9, -5], [-49, -23], [-23, -23], [36, 0], [36, 4], [30, -1], [16, 3], [16, 2], [38, 2], [37, 3], [14, 1], [56, -3], [15, -3], [15, -5], [38, 0], [10, 4], [6, 3], [3, 6], [5, -6], [-1, -3], [1, -3], [17, 3], [6, 3], [15, 5], [31, 6], [32, 2], [23, 0], [5, 4], [9, 5], [2, 7], [-4, 6], [-4, 3], [-5, 1], [-6, -1], [-16, 2], [-18, 5]], [[4473, 9357], [-31, 9], [-18, 3], [-19, 1], [-17, 3], [-16, 4], [-16, 2], [-17, 1], [0, 4], [3, 5], [12, 1], [28, -1], [8, 2], [5, 5], [-13, 6], [-59, -3], [-12, 7], [-8, 11], [-2, 13], [-10, 8], [-8, 0], [-16, -4], [-9, 1], [-16, 4], [-19, 10], [-6, 0], [-23, 0], [-7, 2], [-14, 6], [-7, 4], [-17, 3], [-18, -3], [-22, -4], [-7, 0], [-23, 5], [-16, 0], [-9, -2], [-7, -3], [-5, -6], [-5, -6], [-6, -2], [-8, -2], [-17, 2], [-38, 3], [-30, 7], [-29, 9], [-35, 3], [-34, -2], [-14, -3], [-14, -3], [-12, -5], [-6, -9], [6, -6], [12, -2], [37, -3], [27, -9], [23, -9], [-5, -11], [-4, -10], [4, -12], [-10, -6], [23, -19], [26, -16], [17, -7], [0, -5], [-4, -5], [-6, -2], [-29, -1], [-6, -4], [-5, -4], [-6, -3], [-7, -1], [-18, 2], [-97, 7], [-13, 5], [-25, 1], [-17, 3], [-17, 4], [-19, 2], [-8, -1], [-14, -3], [-13, -18], [2, -6], [6, -5], [32, -8], [16, -6], [13, -8], [15, -10], [7, 1], [2, 7], [11, 7], [15, 4], [18, 2], [19, 1], [16, -2], [16, -3], [40, -6], [18, 1], [17, 2], [6, 1], [6, 2], [7, 1], [63, 1], [8, -1], [8, -3], [14, -7], [12, -9], [8, -3], [5, -6], [3, -6], [-1, -8], [4, -5], [9, -1], [7, -4], [12, -9], [6, 1], [5, 5], [13, 16], [23, 24], [4, 6], [0, 10], [1, 3], [6, 2], [15, 2], [24, 6], [16, -1], [12, -8], [12, -5], [42, 4], [6, 2], [11, 8], [7, 3], [17, 4], [18, 3], [18, -1], [18, -2], [17, 0], [18, 3], [29, -5], [19, 1], [17, -7], [6, -8], [8, -5], [5, 0], [4, 1], [7, 8], [6, 6], [11, 2], [6, 4], [13, 13], [3, 8], [-7, 6], [-9, 4], [-34, 20]], [[7374, 7331], [-2, 5], [-8, 3], [-10, 3], [-10, 1]], [[7344, 7343], [-29, 2]], [[7315, 7345], [-47, 5]], [[7268, 7350], [-14, 1]], [[7254, 7351], [-14, -1]], [[7240, 7350], [-13, -5]], [[7227, 7345], [-18, -2], [-14, 1], [-88, 11], [-31, -4], [-26, -12]], [[7050, 7339], [-7, -10]], [[7043, 7329], [-3, -11]], [[7040, 7318], [2, -6]], [[7042, 7312], [-3, -6]], [[7039, 7306], [-7, -6]], [[7032, 7300], [-1, -5]], [[7031, 7295], [-3, -23]], [[7028, 7272], [2, -11]], [[7030, 7261], [11, -7]], [[7041, 7254], [16, 1]], [[7057, 7255], [3, -3], [-1, -4], [-2, -1]], [[7057, 7247], [-10, 2]], [[7047, 7249], [-9, 0]], [[7038, 7249], [-4, -12]], [[7034, 7237], [5, -14]], [[7039, 7223], [7, -15]], [[7046, 7208], [8, -13]], [[7054, 7195], [2, -6]], [[7056, 7189], [14, -10], [9, -1]], [[7079, 7178], [15, 5]], [[7094, 7183], [33, 1]], [[7127, 7184], [6, 4]], [[7133, 7188], [8, 0]], [[7141, 7188], [14, -6]], [[7155, 7182], [15, -3], [8, 4]], [[7178, 7183], [14, 2]], [[7192, 7185], [9, 2]], [[7201, 7187], [9, 9]], [[7210, 7196], [3, 13]], [[7213, 7209], [14, -5]], [[7227, 7204], [9, -4]], [[7236, 7200], [16, -1]], [[7252, 7199], [4, 5]], [[7256, 7204], [3, 7], [1, 8]], [[7260, 7219], [13, -10]], [[7273, 7209], [6, -1], [8, 1], [8, 3]], [[7295, 7212], [14, 9]], [[7309, 7221], [4, 7]], [[7313, 7228], [-4, 5]], [[7309, 7233], [-1, 6]], [[7308, 7239], [7, 5], [9, 5]], [[7324, 7249], [3, 2]], [[7327, 7251], [12, 13], [7, 10], [3, 4]], [[7349, 7278], [2, 5]], [[7351, 7283], [-4, 13], [6, -1], [4, 3], [5, 13]], [[7362, 7311], [5, 10]], [[7367, 7321], [7, 10]], [[5022, 9297], [-7, 5], [-7, 1], [-12, -2], [15, -5], [11, 1]], [[4605, 1493], [-10, -1], [-3, -2], [1, -1], [2, -1], [19, -1], [12, -2], [6, 0], [4, 1], [5, 4], [-1, 1], [-4, 1], [-31, 1]], [[4289, 8690], [-9, 5], [-2, -1], [-2, -6], [9, -5], [9, -6], [6, -2], [0, 3], [-2, 3], [-9, 9]], [[4945, 8523], [-5, -4], [7, -3], [14, -3], [8, 3], [-2, 2], [-11, 5], [-11, 0]], [[3832, 9364], [-15, -1], [-1, -6], [30, -12], [36, -8], [3, 7], [-17, 9], [-21, 7], [-15, 4]], [[5269, 9272], [-2, -3], [5, -2], [10, -3], [1, 3], [1, 6], [-2, 1], [-6, 0], [-7, -2]], [[4918, 9277], [2, -3], [8, -1], [11, 0], [18, 3], [-1, 1], [-9, 2], [-16, 1], [-13, -3]], [[5339, 9171], [6, -3], [7, 2], [0, 5], [-6, 3], [-6, 0], [-1, -7]], [[5081, 9316], [-2, -4], [1, -1], [2, -1], [4, 1], [10, 6], [-15, -1]], [[5030, 9298], [2, -1], [7, 0], [9, 3], [-13, 1], [-5, -3]], [[5049, 9309], [-6, -3], [3, -2], [12, 1], [6, 3], [-3, 1], [-12, 0]], [[5015, 9231], [29, 0], [15, 5], [-14, 0], [-12, 5], [-17, -3], [-1, -7]], [[3216, 9999], [4, -1], [5, 1]], [[2702, 9971], [-8, -2], [7, -2], [7, 2], [8, 1], [8, 4], [-22, -3]], [[5928, 1895], [-6, -3], [4, -5], [12, -3], [-1, 9], [-3, 1], [-6, 1]], [[1952, 9098], [11, -1], [-23, -5], [-23, -9], [-7, 0], [10, 7], [17, 5], [15, 3]], [[7745, 991], [10, -6], [44, -1], [33, -9], [0, 4], [-2, 3], [-4, 2], [-2, 1], [-19, 2], [-4, 4], [-16, 4], [-31, 3], [-17, -3], [-14, 2], [-3, -4], [0, -3], [2, -3], [23, 4]], [[6553, 926], [-10, -5], [2, -2], [9, -4], [7, 1], [4, 4], [2, 13], [-3, -1], [-11, -6]], [[6373, 940], [-8, -4], [10, -4], [3, -7], [8, -4], [18, 3], [16, 6], [49, 3], [6, 2], [-102, 5]], [[6361, 716], [-8, 3], [-1, 1], [-8, 3], [-65, -3], [-49, -8], [-11, -5], [0, -2], [-2, -2], [-4, -1], [1, -3], [5, -6], [12, -2], [42, 5], [11, 5], [25, 4], [12, 1], [22, -5], [63, -1], [11, 3], [-4, 8], [-7, 4], [-9, 1], [-36, 0]], [[6319, 692], [-14, 2], [-50, -3], [-4, -4], [9, -4], [31, -7], [12, 4], [22, 5], [-6, 7]], [[6079, 583], [-1, -3], [2, -5], [16, -4], [12, 1], [36, 10], [6, 5], [-1, 4], [8, 5], [-18, -2], [-42, -8], [-7, -1], [-11, -2]], [[5847, 817], [-19, -5], [4, -2], [16, -5], [32, 5], [-6, 4], [-12, 0], [-4, 2], [-11, 1]], [[5919, 816], [8, 2], [6, 10], [18, 1], [22, -5], [7, 3], [-5, 3], [3, 5], [27, -1], [22, 7], [24, -1], [0, 2], [-5, 6], [-9, 2], [-16, -2], [-19, 3], [-40, -4], [-20, 2], [-40, -8], [-4, -4], [-35, -12], [-7, -5], [26, 4], [10, -4], [1, -1], [14, -1], [8, -2], [4, 0]], [[5425, 629], [-3, -6], [13, 0], [6, 3], [3, 3], [13, 6], [-20, -2], [-12, -4]], [[5431, 74], [-20, 9], [-17, 4], [-29, 3], [-11, -1], [-7, -3], [10, -11], [40, -22], [6, -15], [13, -10], [12, 1], [4, 3], [9, 11], [0, 6], [15, 2], [3, 8], [-5, 7], [-23, 8]], [[5260, 554], [-6, 1], [-21, -7], [-13, 0], [-15, -12], [12, 0], [23, 5], [8, 7], [12, 6]], [[5415, 607], [5, 10], [-30, 5], [0, 5], [-9, 3], [-17, 0], [-14, -3], [-6, -3], [-3, -5], [3, -2], [16, -8], [-4, -4], [-20, -15], [-11, -5], [-4, -3], [-5, -1], [8, -2], [21, -2], [10, 7], [8, -2], [8, 1], [10, 4], [4, 2], [17, 4], [1, 7], [12, 7]], [[5369, 740], [10, 5], [4, 5], [-30, -5], [-16, -12], [-3, -4], [6, 0], [29, 11]], [[5782, 795], [19, 0], [5, 4], [-6, 4], [-12, 2], [-26, 0], [6, -5], [14, -5]], [[6235, 719], [13, 0], [29, 10], [1, 2], [-1, 4], [-1, 0], [-11, 1], [-50, 3], [-18, -7], [2, -2], [8, -4], [28, -7]], [[5600, 772], [14, 5], [11, -2], [35, 2], [3, -6], [5, -3], [14, -4], [53, 13], [-22, 0], [-19, 7], [-63, -2], [-7, 1], [-15, 7], [-9, 2], [-6, -6], [-20, -6], [-25, 0], [0, -5], [13, -4], [10, 0], [28, 1]], [[6093, 644], [14, 1], [4, 0], [16, 3], [-8, 3], [-28, -1], [-2, 2], [-10, 1], [-34, -2], [9, -4], [39, -3]], [[5524, 15], [-8, -4], [-3, -4], [4, -2], [10, -3], [21, 1], [7, 2], [-6, 7], [-25, 3]], [[5615, 127], [5, 3], [-7, 5], [-15, 3], [-20, 1], [-9, -3], [17, -5], [11, -2], [18, -2]], [[5623, 748], [-10, -1], [-5, -5], [7, -5], [10, -2], [8, 1], [-8, 2], [0, 2], [9, -1], [8, 1], [-19, 8]], [[5633, 664], [-21, 2], [-12, -5], [-2, -12], [-23, -2], [17, -4], [11, -1], [17, 4], [0, 1], [-5, 5], [5, 5], [13, 7]], [[3971, 27], [31, 5], [-50, 21], [-19, 5], [-37, 4], [-19, -2], [-11, -4], [-3, -6], [26, -13], [61, -6], [21, -4]], [[4848, 434], [31, 4], [1, 3], [-1, 8], [4, 5], [5, 1], [16, 3], [0, 2], [5, 9], [-29, 2], [-19, -5], [-4, -6], [-10, -4], [3, -8], [-15, -3], [-4, -6], [1, -4], [16, -1]], [[4733, 392], [0, -2], [2, -1], [5, -1], [32, 12], [0, 2], [-4, 5], [6, 5], [-8, 2], [-21, -5], [-1, -13], [-11, -4]], [[4692, 247], [-3, 3], [-10, 3], [-25, 1], [-20, -2], [-26, -6], [1, -2], [6, -1], [0, -8], [21, -2], [28, 2], [7, 3], [3, 3], [18, 6]], [[4677, 333], [-2, 2], [-9, 7], [-13, 1], [-12, -2], [1, -5], [-13, -7], [10, -2], [16, -1], [7, 1], [15, 6]], [[3023, 8246], [-2, -4], [-7, -3], [-3, -5], [-6, 10], [1, 3], [-1, 9], [7, 2], [4, 0], [4, -1], [3, -11]], [[4779, 1561], [-8, -3], [-4, -2], [-1, -2], [1, -2], [2, -2], [9, 0], [11, 5], [5, 4], [-12, 2], [-3, 0]], [[3553, 2645], [4, -3], [4, -1], [4, 2], [1, 2], [-2, 5], [-4, 3], [-4, 0], [-5, -2], [2, -6]], [[3539, 2245], [-30, -5], [-11, -5], [-16, 0], [-4, 19], [19, 27], [1, 10], [-6, 8], [-3, 6], [2, 3], [24, 5], [8, -5], [9, -15], [17, -22], [-1, -21], [-9, -5]], [[3528, 2054], [-18, 0], [1, -5], [-3, -7], [-5, -6], [3, -3], [2, -6], [4, -2], [12, -1], [4, 9], [1, 9], [13, 2], [6, 3], [7, 4], [-6, 2], [-21, 1]], [[3812, 1763], [-20, 11], [-7, 1], [-7, 1], [-15, 6], [-18, 1], [-14, 5], [-27, 5], [-29, 2], [-8, 2], [-6, 0], [-2, 0], [-5, 7], [-8, 3], [-14, 6], [-14, 4], [-6, -1], [0, -2], [13, -7], [9, -9], [21, -8], [20, -9], [5, 0], [14, -1], [21, 1], [18, -2], [10, -2], [26, -12], [3, -2], [2, -5], [2, -1], [9, -1], [13, 0], [27, -4], [12, 0], [-1, 1], [-22, 9], [-2, 1]], [[4527, 1541], [-3, 3], [1, 2], [6, 6], [-7, 4], [-8, 3], [-29, 1], [-4, 2], [3, 3], [28, 9], [-8, 9], [-28, 0], [-36, -6], [-114, 10], [-26, 4], [-5, -19], [-4, -5], [-6, -2], [1, -2], [13, -3], [3, -1], [2, -3], [4, -2], [21, 2], [3, -1], [0, -8], [2, -2], [5, -2], [14, -3], [8, -6], [6, -2], [25, -3], [8, 0], [-17, 11], [-8, 7], [0, 3], [9, 11], [15, 0], [6, -2], [14, -6], [6, -1], [14, 2], [3, -1], [0, -2], [-6, -9], [0, -2], [5, -6], [4, -3], [12, 2], [13, -2], [14, 0], [18, -4], [19, -1], [6, -2], [9, -9], [11, -3], [10, -2], [5, 1], [0, 6], [-1, 7], [-5, 4], [-9, 5], [-22, 8]], [[3555, 2219], [1, 9], [-2, 6], [-17, 0], [-8, -4], [-28, -4], [-6, 1], [-7, -2], [-5, -3], [-3, -6], [9, -4], [5, 0], [4, -3], [3, -7], [-12, -6], [-10, -6], [9, -4], [11, 0], [16, 8], [19, 9], [21, 16]], [[5678, 1958], [-4, 4], [0, 8], [15, 1], [15, -4], [-1, -3], [-5, -6], [-20, 0]], [[3559, 2334], [5, 3], [19, 4], [-1, 4], [-11, 3], [-13, 0], [-16, -4], [-8, -4], [9, -5], [2, -4], [10, 2], [4, 1]], [[3619, 2779], [-1, 2], [-4, 3], [-7, 1], [-10, -1], [-3, -2], [0, -3], [4, -3], [20, 2], [1, 1]], [[3767, 2665], [-2, 7], [-6, 7], [-6, 0], [-8, -1], [-5, -6], [0, -4], [2, -3], [10, -8], [1, -4], [5, -4], [4, 0], [4, 1], [3, 7], [-2, 8]], [[3633, 2253], [-7, 18], [2, 3], [0, 3], [-2, 2], [-7, 5], [-5, 10], [-2, 6], [-2, 2], [-11, 5], [-6, 6], [3, 18], [-10, 1], [-11, -8], [-32, -6], [-7, -5], [2, -3], [3, -9], [7, -9], [4, -6], [8, -8], [4, -4], [5, -15], [13, -9], [31, 2], [17, -1], [3, 2]], [[5134, 1607], [-7, 1], [-16, 0], [-31, -2], [-16, 3], [-5, -4], [-6, -2], [-9, 0], [-10, -6], [4, -4], [13, -4], [19, 7], [8, 0], [18, 4], [42, 1], [8, -2], [20, 4], [3, 5], [-10, 0], [-21, -2], [-4, 1]], [[5433, 3251], [-18, 3], [1, -3], [6, -6], [18, -6], [7, -1], [-1, 7], [-5, 4], [-8, 2]], [[5749, 1880], [-6, 0], [1, -2], [0, -5], [3, -4], [3, -1], [9, 2], [0, 6], [-10, 4]], [[5589, 1916], [-11, 0], [-10, 1], [-5, -3], [-13, -3], [4, -3], [12, -7], [12, -1], [5, 5], [6, 11]], [[3932, 1681], [9, 3], [6, 2], [0, 4], [9, 9], [14, 5], [-14, 9], [-10, 4], [-12, 9], [-3, 1], [-29, 4], [-18, -1], [-10, -2], [-2, 2], [-4, 9], [-4, 4], [-3, 2], [-7, 1], [-5, 2], [-3, 0], [-5, -3], [-1, -3], [3, -7], [-39, 5], [-11, 6], [-34, -2], [-23, -12], [29, -3], [9, -9], [15, -8], [16, 1], [5, 0], [2, 0], [3, -7], [0, -5], [-3, -8], [1, -3], [14, -4], [13, -3], [5, 1], [2, 2], [-2, 7], [1, 4], [5, 5], [8, -3], [7, 0], [9, 1], [16, -2], [-3, -9], [-14, -9], [-11, -3], [1, -3], [6, -2], [5, -1], [5, 0], [7, 2], [16, 3], [16, 0], [13, 5]], [[4609, 1552], [6, 1], [15, 6], [7, 1], [6, 0], [5, 0], [2, -2], [2, -4], [5, -4], [8, -2], [12, 1], [12, 4], [9, 6], [4, 4], [0, 4], [-4, 5], [-19, 9], [-26, 1], [-64, 4], [-33, 0], [-28, -5], [18, -10], [6, -10], [5, -4], [4, -1], [43, -5], [5, 1]], [[4245, 1572], [-19, 16], [-17, -1], [-12, -2], [-19, -1], [-8, -2], [-17, 3], [-3, 1], [-6, 7], [-14, 0], [-16, 2], [-25, 1], [-5, 0], [-4, -6], [1, -2], [3, -2], [3, 0], [9, 2], [4, 2], [3, 1], [10, -1], [12, -5], [19, -5], [3, -2], [1, -3], [3, -3], [18, -2], [10, -2], [5, 0], [10, 2], [6, 0], [7, -3], [1, -2], [0, -3], [9, -2], [11, 1], [15, 6], [2, 5]], [[4673, 1497], [3, 2], [0, 2], [-7, 5], [-5, 8], [-4, 3], [-3, 0], [-7, -6], [-9, -2], [-5, -2], [-3, -3], [1, -1], [24, -3], [5, -2], [4, -2], [6, 1]], [[3629, 1959], [5, 3], [0, 9], [-2, 3], [-5, 4], [-18, 0], [-21, -8], [-23, -4], [-16, 4], [-8, -11], [-4, -7], [-9, -11], [2, -7], [14, 6], [6, 5], [14, 13], [16, -3], [12, -1], [8, 2], [10, 5], [5, 0], [10, -3], [4, 1]], [[3576, 2080], [18, 4], [-5, 9], [-19, 3], [-16, 3], [-13, 1], [-17, 4], [-6, -11], [-1, -6], [-3, -2], [-2, -7], [-5, -9], [20, 0], [8, -4], [28, 9], [13, 6]], [[3637, 1898], [-16, 11], [-14, 1], [-5, 3], [-6, 20], [-12, -2], [-1, -9], [-13, 2], [-14, -7], [8, -12], [5, -14], [14, -12], [9, -13], [23, -1], [4, 13], [19, 17], [-1, 3]], [[3723, 2746], [5, 4], [10, -1], [6, 1], [-3, 5], [-4, 4], [-3, 0], [-15, -3], [-25, -1], [-1, -5], [4, -2], [23, -3], [3, 1]], [[4047, 1660], [16, -2], [12, 2], [24, -9], [23, -6], [4, 0], [5, 6], [7, 3], [4, 4], [-3, 9], [0, 5], [-21, 0], [-31, 9], [-24, 8], [-21, 3], [-42, 4], [-10, -4], [-11, -2], [-9, -11], [17, -7], [17, -10], [3, -10], [19, 3], [8, 5], [13, 0]], [[3673, 1903], [16, -2], [5, 1], [3, 2], [0, 2], [-2, 2], [-21, 6], [-7, 5], [-5, 2], [-12, 3], [-4, -1], [5, -6], [2, -6], [13, -6], [7, -2]], [[3753, 2619], [-9, 9], [-1, 6], [1, 3], [-5, 11], [-8, 12], [-5, 8], [-12, 6], [0, 6], [2, 4], [18, 6], [11, 1], [7, 4], [4, 7], [0, 6], [-16, 4], [-7, 5], [-5, 5], [-13, 0], [-13, -5], [-4, -9], [2, -12], [-5, -3], [-11, -1], [-13, 3], [-17, -4], [-12, -4], [3, -12], [-19, -7], [17, -10], [11, -12], [10, -5], [12, -16], [10, -9], [15, -5], [-1, -14], [12, -2], [23, 0], [3, 2], [0, 4], [9, 1], [2, 1], [4, 8], [1, 4], [-1, 4]], [[3681, 2567], [-1, 4], [-11, 11], [4, 20], [-3, 11], [-16, -3], [-4, -5], [-8, -4], [1, -15], [-9, -10], [-12, -8], [-7, -6], [2, -8], [17, 1], [13, -3], [14, 2], [8, 5], [10, 4], [2, 4]], [[7968, 4938], [-3, 1]], [[7965, 4939], [-5, -4]], [[7960, 4935], [-12, -6]], [[7948, 4929], [5, -4]], [[7953, 4925], [15, 2]], [[7968, 4927], [9, 2]], [[7977, 4929], [8, 0]], [[7985, 4929], [-4, 3]], [[7981, 4932], [-9, 3]], [[7972, 4935], [-4, 3]], [[7357, 4608], [-6, 5]], [[7351, 4613], [-18, -12]], [[7333, 4601], [9, -13]], [[7342, 4588], [2, 1]], [[7344, 4589], [3, 2]], [[7347, 4591], [5, 7]], [[7352, 4598], [5, 10]], [[7350, 4465], [-1, -25], [9, 5]], [[7358, 4445], [-1, 6]], [[7357, 4451], [12, 15]], [[7369, 4466], [5, 12], [-5, 6]], [[7369, 4484], [-8, -4]], [[7361, 4480], [-5, -6], [-6, -9]], [[7823, 4860], [-2, 5], [-4, 3], [-4, 2]], [[7813, 4870], [-21, -17]], [[7792, 4853], [5, -5]], [[7797, 4848], [16, 3]], [[7813, 4851], [6, -3], [0, 5]], [[7819, 4853], [2, 4]], [[7821, 4857], [2, 3]], [[3209, 9887], [0, 3], [0, 2], [-4, 16], [-8, 9], [-9, -11], [-10, 5], [14, 7], [-1, 2], [-6, 4], [-8, -2], [-13, -1], [-5, -2], [-9, 1], [-7, -3], [12, -14], [9, -5], [6, -17], [5, -2], [0, -11], [28, -2], [8, 14], [0, 4], [-2, 3]], [[3175, 9692], [-12, 1], [-9, 0], [3, -6], [11, -5], [5, -2], [5, 0], [6, -5], [6, 3], [3, 5], [2, 6], [-20, 3]], [[3139, 9713], [-5, 2], [-5, 9], [-8, 5], [-7, 0], [-11, -1], [1, -2], [6, -3], [7, -6], [11, -2], [8, -3], [3, 1]], [[3102, 9731], [-10, 9], [-7, 1], [-6, -1], [-7, -1], [-19, 2], [-10, -2], [3, -2], [7, -2], [11, -3], [18, 0], [5, -2], [7, 0], [8, 1]], [[3679, 9759], [21, -6], [9, 5], [-3, 1], [-17, 4], [-7, 6], [-12, 3], [-1, -4], [2, -4], [4, -6], [4, 1]], [[3653, 9901], [-3, 6], [-4, 0], [-7, -2], [-4, -15], [6, 0], [12, 11]], [[3762, 9571], [38, 1], [33, 4], [15, 15], [5, 7], [2, 12], [-7, 1], [-11, -11], [-14, -6], [-9, 0], [-18, 5], [-14, -1], [-9, -7], [-13, -2], [1, -2], [0, -3], [-2, -5], [0, -4], [3, -4]], [[3726, 9743], [1, -4], [-4, -5], [-8, -3], [-3, -3], [-6, -3], [-10, -8], [-19, -9], [-3, -5], [10, 3], [17, 9], [17, 6], [19, 19], [-6, 15], [4, 5], [-15, -1], [-3, -4], [0, -5], [9, -7]], [[3404, 9999], [-5, -7], [-1, -12], [-15, 0], [2, -2], [9, -5], [5, -7], [5, -4], [-1, 12], [5, 13], [2, 12]], [[3436, 9857], [-9, 2], [-4, -7], [12, -1], [25, -13], [10, -3], [6, 1], [-13, 4], [-7, 6], [-20, 11]], [[3552, 9817], [-1, -7], [-9, -1], [-3, -3], [13, -5], [23, -5], [18, -21], [1, 2], [-1, 11], [-13, 10], [-18, 6], [-6, 20], [-10, 11], [-6, 11], [-14, 13], [1, -8], [3, -3], [7, -10], [12, -14], [3, -7]], [[3496, 9941], [-18, 24], [-8, 2], [-3, -1], [-2, -3], [12, -7], [3, -7], [6, -4], [15, -16], [12, -7], [-1, -5], [-10, -5], [-3, -4], [5, 0], [14, 3], [10, -2], [-1, 6], [-30, 24], [-1, 2]], [[3160, 9697], [5, 2], [-7, 5], [0, 2], [-11, 3], [-2, 6], [-2, 0], [-1, -6], [-5, -5], [4, -3], [14, -5], [5, 1]], [[3090, 9951], [16, -6], [9, -2], [7, 3], [1, -9], [9, -5], [4, -8], [9, 5], [15, 4], [3, 3], [16, 7], [1, 3], [-1, 9], [0, 13], [-14, 9], [-11, 16], [-7, 6]], [[3119, 9999], [-3, -9], [-16, -17], [3, -6], [2, -1], [3, -3], [-11, -7], [-3, 5], [-14, -1], [10, -9]], [[2634, 9966], [-3, -6], [-7, -4], [9, 1], [5, 1], [0, 3], [-4, 5]], [[2474, 9664], [-5, 8], [-6, 2], [-33, 3], [-13, -11], [0, -5], [15, -21], [-5, -2], [-7, -2], [-8, 2], [-9, 6], [-1, -4], [6, -6], [11, -7], [15, -3], [15, 1], [29, 8], [13, 5], [-1, 5], [-9, 16], [-7, 5]], [[2945, 9755], [-28, 13], [-7, 0], [4, -2], [11, -8], [25, -8], [0, 3], [-5, 2]], [[2777, 9999], [-3, -5], [7, 5]], [[1736, 9277], [-2, 1], [-5, 1], [-8, -17], [-1, -4], [-6, -6], [7, 2], [15, 23]], [[1728, 9202], [-5, -11], [1, -4], [11, 14], [8, 6], [1, 5], [-5, 2], [-1, -4], [-4, -4], [-6, -4]], [[1169, 9333], [3, -2], [16, 3], [4, 3], [17, 5], [-2, 2], [-6, 0], [-32, -11]], [[2536, 8350], [2, 5], [-4, 5], [-2, 0], [-7, -1], [11, -9]], [[2673, 9406], [-1, -3], [-6, -1], [-5, 2], [-3, 4], [-4, -1], [2, -10], [15, -1], [3, 1], [8, 2], [17, 1], [-4, 4], [-22, 2]], [[1867, 9531], [-7, -7], [-6, -11], [4, -12], [8, 4], [18, 17], [8, 9], [-1, 3], [-10, -2], [-14, -1]], [[5326, 9194], [-11, 5], [-5, -1], [-2, -3], [0, -1], [0, -1], [9, -4], [11, -2], [6, -6], [4, 1], [-1, 3], [-6, 3], [-5, 6]], [[5584, 8851], [-5, -2], [-8, -7], [-9, -10], [-2, -5], [2, -9], [16, -7], [8, 11], [1, 20], [-3, 9]], [[5580, 8913], [2, 6], [-1, 2], [-3, 0], [-11, 7], [-14, 5], [-8, 0], [-5, -2], [-1, -5], [12, -16], [5, -3], [13, -2], [-11, -8], [0, -2], [4, -5], [23, 1], [6, -5], [3, 1], [2, 6], [-7, 13], [-2, 3], [-7, 4]], [[5583, 8509], [-23, -1], [-14, -3], [-28, -1], [-14, -3], [-18, -2], [-8, -3], [2, -2], [14, -4], [6, -3], [3, -3], [1, -7], [-4, -29], [-5, -1], [-15, -1], [-4, -6], [-35, -13], [20, 2], [25, -2], [60, 1], [23, 6], [1, 4], [1, 10], [4, 6], [-6, 7], [-3, 10], [2, 8], [-2, 12], [5, 5], [12, 13]], [[4518, 8662], [0, -4], [3, -3], [6, -3], [3, -3], [1, -10], [4, -5], [7, 12], [-2, 9], [-22, 7]], [[5612, 8555], [-13, -8], [-1, -4], [8, 1], [30, 9], [3, 7], [-6, 0], [-21, -5]], [[5403, 9137], [-2, -1], [-5, -5], [0, -6], [10, -2], [1, 7], [-4, 7]], [[5772, 8769], [-8, -1], [1, -16], [4, -5], [13, -4], [4, 2], [9, 7], [-8, 5], [-15, 12]], [[5141, 8533], [-3, -7], [-12, 0], [-10, 4], [-4, 5], [-5, -1], [-14, -3], [-8, -7], [6, -3], [20, -2], [8, 2], [9, -6], [7, -2], [8, 0], [11, 1], [13, 9], [1, 2], [-4, 14], [-6, 4], [-17, -10]], [[5262, 9280], [23, 6], [3, 4], [-7, 1], [-18, -8], [-1, -3]], [[5477, 8656], [-8, -6], [-5, -8], [-1, -6], [-4, -4], [10, 0], [12, 4], [3, 18], [-7, 2]], [[5462, 9215], [2, 2], [0, 10], [-4, 3], [-6, 0], [-5, 2], [-2, -1], [0, -2], [3, -9], [12, -5]], [[5535, 9052], [-3, -1], [-7, -4], [1, -6], [4, -1], [8, 0], [4, 3], [-1, 5], [-6, 4]], [[5448, 9158], [16, -1], [5, 4], [3, 1], [1, 2], [0, 3], [-3, 1], [-5, 4], [-11, 3], [-10, -8], [1, -3], [3, -6]], [[5535, 8986], [-4, 16], [-6, 6], [-20, 5], [-1, -3], [-2, -8], [9, -13], [6, -19], [14, 3], [4, 13]], [[5546, 9079], [-26, 11], [-6, 5], [-1, 6], [-9, 4], [-6, -3], [-3, -5], [4, -7], [-5, -7], [2, -7], [11, -1], [17, 1], [22, 3]], [[5479, 9086], [-15, 3], [-3, -1], [-4, -5], [5, -25], [7, -9], [6, -1], [12, 4], [3, 5], [-1, 19], [3, 4], [-6, 2], [-7, 4]], [[5552, 8757], [1, 10], [-2, 7], [-6, -1], [-6, -2], [-6, -5], [-2, -8], [11, -7], [4, 2], [6, 4]], [[3859, 9722], [14, -1], [15, -7], [5, 4], [-2, 1], [-10, 5], [-16, 3], [-5, 0], [-9, 1], [-12, 3], [-5, -8], [7, -1], [18, 0]], [[4453, 8647], [-27, 8], [-9, 12], [-6, 3], [-6, 1], [1, -9], [23, -16], [27, -10], [8, 1], [-11, 10]], [[4039, 9661], [8, -4], [1, 2], [3, 2], [-4, 5], [-20, 2], [-6, -1], [3, -6], [15, 0]], [[3869, 9631], [-11, 6], [-10, -5], [2, -6], [19, 5]], [[4006, 9669], [8, -4], [3, 1], [-3, 3], [-5, 9], [-11, 0], [-1, -3], [5, -3], [3, 0], [1, -3]], [[3972, 9659], [2, 1], [5, 1], [-1, 2], [-10, -1], [-16, 6], [-1, -6], [1, -4], [5, -1], [15, 2]], [[5571, 8301], [12, 3], [8, 6], [1, 5], [-7, 3], [1, 3], [-3, 1], [-4, -1], [-15, -9], [-3, -3], [1, -10], [9, 2]], [[2825, 7078], [-4, -6], [-3, -15], [0, -5], [4, -1], [13, 0], [3, 4], [6, 13], [3, 1], [8, 2], [15, 10], [-13, 5], [-12, 1], [-9, -3], [-11, -6]], [[6963, 7311], [-24, -3]], [[6939, 7308], [-18, -9]], [[6921, 7299], [-19, -21]], [[6902, 7278], [0, -25]], [[6902, 7253], [-17, -12]], [[6885, 7241], [-20, -26]], [[6865, 7215], [15, 2]], [[6880, 7217], [28, 9]], [[6908, 7226], [25, 14]], [[6933, 7240], [22, 20]], [[6955, 7260], [5, 0]], [[6960, 7260], [16, 38]], [[6976, 7298], [-13, 13]], [[2981, 8236], [3, -4], [6, 4], [-2, 5], [-2, 0], [-5, -5]], [[3001, 7504], [4, -5], [8, 1], [1, 11], [-3, -1], [-10, -6]], [[3117, 7646], [-4, -4], [2, -5], [7, -4], [4, 2], [0, 7], [-3, 3], [-6, 1]], [[5601, 8334], [8, 2], [-4, 2], [-3, 0], [-6, -1], [-3, -2], [-9, -1], [-4, -5], [-1, -3], [17, 4], [5, 4]], [[7930, 7082], [-3, -1]], [[7927, 7081], [-9, -6], [-2, -7]], [[7916, 7068], [2, -8]], [[7918, 7060], [-4, -12]], [[7914, 7048], [14, 10]], [[7928, 7058], [2, 14]], [[7930, 7072], [0, 2], [0, 8]], [[8761, 6012], [-3, -10]], [[8758, 6002], [-14, -8], [0, -7]], [[8744, 5987], [6, 2]], [[8750, 5989], [20, 11]], [[8770, 6000], [1, 2]], [[8771, 6002], [-3, 5]], [[8768, 6007], [-7, 5]], [[8712, 5956], [-2, -4]], [[8710, 5952], [4, -4]], [[8714, 5948], [3, -4]], [[8717, 5944], [5, -1]], [[8722, 5943], [5, 7]], [[8727, 5950], [0, 7], [-11, 0], [-4, -1]], [[7859, 7234], [-2, -8], [-2, 0], [-2, -3], [8, -2], [3, 3], [9, 5]], [[7873, 7229], [-1, 4]], [[7872, 7233], [-13, 1]], [[7124, 7392], [-21, 0]], [[7103, 7392], [-10, -10]], [[7093, 7382], [1, -10]], [[7094, 7372], [7, -1]], [[7101, 7371], [25, 0]], [[7126, 7371], [16, -6]], [[7142, 7365], [12, 0]], [[7154, 7365], [11, 3]], [[7165, 7368], [20, 22], [-5, 6]], [[7180, 7396], [-21, 4]], [[7159, 7400], [-35, -8]], [[7072, 7592], [-3, -11]], [[7069, 7581], [8, -12], [8, -2], [14, 5], [1, 4], [-7, 17], [-3, 1], [-8, 1], [-10, -3]], [[7049, 7354], [0, 3], [-2, 5]], [[7047, 7362], [-14, 1]], [[7033, 7363], [-11, -1]], [[7022, 7362], [-22, -5]], [[7000, 7357], [-4, -7]], [[6996, 7350], [0, -2], [-2, -4]], [[6994, 7344], [3, -4]], [[6997, 7340], [13, -7]], [[7010, 7333], [39, 21]], [[7184, 7353], [38, 3]], [[7222, 7356], [9, 6], [1, 6], [-4, 6]], [[7228, 7374], [-15, 2]], [[7213, 7376], [-14, -2]], [[7199, 7374], [-16, -5]], [[7183, 7369], [-13, -7]], [[7170, 7362], [-4, -4]], [[7166, 7358], [18, -5]], [[7093, 7408], [-1, 21]], [[7092, 7429], [-3, 1]], [[7089, 7430], [-4, -1]], [[7085, 7429], [-4, -3], [0, -14]], [[7081, 7412], [-3, -10]], [[7078, 7402], [-11, -8]], [[7067, 7394], [-12, -5]], [[7055, 7389], [-2, -16]], [[7053, 7373], [26, -6]], [[7079, 7367], [2, 15], [14, 13], [-2, 13]], [[7105, 7405], [15, 4]], [[7120, 7409], [16, 14]], [[7136, 7423], [1, 7]], [[7137, 7430], [-3, 5], [-6, -2], [-2, -2]], [[7126, 7431], [-20, -2]], [[7106, 7429], [-4, -7]], [[7102, 7422], [0, -13]], [[7102, 7409], [3, -4]], [[1390, 7317], [-29, -4], [-9, -7], [-1, -10], [22, -10], [8, 0], [2, 2], [8, 2], [10, 8], [1, 12], [-12, 7]], [[1511, 7292], [-8, -8], [-9, -3], [-10, -7], [1, -2], [4, -3], [6, -2], [16, 5], [9, 9], [9, 4], [5, 6], [-1, 3], [-3, 1], [-4, 1], [-15, -4]], [[2613, 8156], [5, 4], [-8, 10], [-3, 0], [-9, -3], [-6, -9], [-1, -3], [1, -3], [12, -9], [16, -4], [8, 0], [-2, 5], [-11, 5], [-2, 7]], [[1229, 7334], [-9, 7], [-27, -3], [-1, -3], [1, -8], [5, -6], [12, -4], [15, 2], [3, 4], [1, 11]], [[1360, 7239], [-6, -8], [8, -5], [6, 1], [6, 5], [-3, 3], [-4, 2], [-7, 2]], [[1291, 7305], [-1, 2], [3, 5], [-4, 12], [-29, 20], [-4, 20], [-9, 6], [-5, 7], [-8, 4], [-19, -2], [-2, -5], [-13, -6], [1, -2], [6, -3], [12, 4], [5, -2], [9, -28], [17, -9], [7, -13], [11, -6], [-3, -7], [-27, -9], [-18, -9], [-5, -7], [2, -7], [9, -7], [7, -2], [34, -1], [32, 9], [15, 20], [-9, 8], [-14, 8]], [[1333, 7348], [-16, 3], [-6, -4], [-7, -8], [9, -6], [27, -4], [6, 4], [3, 5], [-16, 10]], [[0, 9147], [44, -20], [49, -5], [50, -9], [13, -4], [21, -1], [34, -24], [55, -11], [55, -25], [81, -6], [54, -19], [42, -8], [15, 3], [28, 1], [63, 21], [44, 9], [47, 21], [26, 4], [22, 0], [2, 2], [-9, 2], [-25, 3], [0, 3], [13, 8], [4, 4], [6, 0], [10, -6], [-1, -4], [1, -4], [6, 0], [14, 7], [5, -1], [4, -3], [-13, -9], [3, -3], [27, -2], [67, -17], [-4, 4], [-24, 9], [-8, 2], [-7, 1], [-10, 2], [-8, 4], [3, 6], [5, 0], [9, -5], [10, -4], [23, -6], [23, -10], [26, -17], [28, -15], [53, -31], [20, -15], [16, -8], [15, -10], [40, -31], [38, -29], [4, -2], [60, -34], [25, -12], [38, -13], [33, -7], [77, 1], [18, -3], [55, -18], [18, -5], [23, -13], [41, -5], [35, -4], [58, -20], [51, -11], [4, 2], [-14, 5], [-11, 1], [-4, 3], [14, 0], [25, -8], [33, -5], [23, 1], [13, 1], [8, 5], [8, 6], [-3, 11], [4, 1], [6, -2], [9, -2], [15, 3], [16, -4], [1, -4], [-2, -4], [5, -6], [7, -10], [11, -4], [0, -11], [0, -3], [-8, -3], [-5, 0], [-10, 6], [-7, 6], [-6, 1], [-12, -9], [1, -6], [29, -16], [39, -26], [9, -8], [39, -19], [14, -10], [14, -18], [27, -26], [72, -43], [19, -14], [12, -11], [0, -3], [0, -2], [-1, -6], [-19, -7], [-3, -2], [11, -5], [17, -7], [6, -4], [0, -7], [1, -5], [-6, -7], [-18, -18], [-3, -11], [8, -17], [16, -18], [8, -6], [45, -10], [23, -20], [5, -4], [6, 2], [2, 7], [8, 4], [17, 12], [-3, 7], [-8, 5], [-28, 9], [-11, 10], [-4, 15], [4, -1], [5, -5], [25, -9], [44, -22], [11, -12], [-4, -9], [1, -6], [11, -8], [15, -5], [37, -6], [15, -9], [31, -11], [23, -13], [14, -12], [3, -8], [1, -17], [-4, -8], [-14, -12], [19, -14], [9, -3], [13, -1], [11, -3], [12, -1], [0, 7], [-1, 4], [-18, 12], [-7, 9], [11, 1], [16, -5], [17, -8], [5, -9], [1, -16], [11, -7], [14, -11], [10, -12], [3, 3], [-2, 16], [13, 6], [14, 2], [21, -4], [24, -1], [19, 3], [1, -8], [9, -4], [9, 3], [18, -1], [16, -5], [19, -3], [5, -7], [2, -6], [25, -31], [19, -4], [15, -6], [7, 0], [3, 5], [2, 14], [3, 5], [10, 3], [4, 2], [4, -20], [17, -28], [2, -17], [8, -6], [26, 0], [32, 5], [10, 6], [3, 6], [9, 4], [25, 1], [6, 2], [8, 5], [-4, 11], [-5, 7], [-26, 19], [-15, 15], [-7, 4], [-7, 5], [-1, 6], [1, 8], [8, 5], [5, 3], [24, 3], [11, 4], [54, 25], [-10, 5], [8, 8], [4, 6], [7, 8], [16, 6], [3, 2], [6, 5], [10, 4], [27, 1], [23, -2], [19, -7], [15, -10], [12, -3], [8, -6], [6, -1], [7, -3], [15, -9], [6, -19], [5, -5], [4, -5], [4, 0], [2, 5], [-3, 4], [-1, 7], [3, -2], [4, -3], [13, -1], [1, -3], [4, -3], [4, 3], [4, 3], [9, 5], [6, -10], [7, -8], [23, -12], [13, -8], [-11, 2], [-17, 8], [-13, 6], [-10, 10], [-4, 0], [-5, -6], [-15, -8], [4, -12], [-5, -5], [-4, -2], [-7, 0], [-8, -1], [7, -17], [29, -37], [35, -31], [4, -2]], [[3157, 8129], [14, -10], [17, -19], [5, -9], [6, -4], [11, -15], [13, 0], [10, -12], [1, -8], [-6, -24], [-5, 0], [-5, 1], [0, -11], [18, -19], [14, -23], [-11, -11], [-30, -15], [19, -12], [4, -10], [2, -11], [-1, -15], [4, -25], [5, -6], [2, -6], [-3, -14], [-3, -12], [-3, -8], [-9, -6], [-4, -5], [-10, -4], [-1, -5], [16, 4], [1, -5], [-5, -8], [1, -7], [10, -12], [12, 12], [4, -2], [-2, -16], [7, -2], [7, -1], [5, 5], [7, 1], [-23, -35], [-12, -12], [-5, -13], [-8, -1], [-8, -11], [-7, -8], [-5, -9], [-11, -2], [-9, -1], [-1, -4], [5, -9], [0, -4], [-16, -10], [-4, -4], [-1, -3], [-9, 1], [-3, -3], [-5, -8], [-8, -6], [-6, -3], [-5, -3], [-8, -2], [-25, 2], [-6, -5], [-11, 2], [-6, -1], [-11, -5], [-8, -7], [-4, -6], [-1, -26], [11, -14], [-4, -16], [-31, 8], [-23, -10], [-10, -13], [20, -11], [4, -7]], [[3020, 7521], [4, -17], [-10, -9], [-47, -11], [-34, -5], [-21, -9], [-18, 1], [-8, -6], [-15, -7], [-19, -2], [-8, -6], [4, -20], [5, -19], [-3, -27], [-12, -17], [-15, -11], [-12, -6], [-23, -21], [2, -7], [12, -16], [15, -4], [-11, 0], [-14, 4], [-7, -10], [-7, -18], [-10, -5], [-31, -8], [-9, -11], [12, -22], [2, -10], [-4, -26], [10, -20], [0, -12], [-1, -15], [-14, -7], [-14, -5], [2, -5], [3, -3], [13, -9], [22, -5], [34, -24], [10, -4], [13, -4], [4, 4], [1, 4], [18, 11], [10, 14], [7, 4], [-3, -21], [5, -3], [9, 3], [7, 14], [-2, 29], [7, 8], [2, -4], [-1, -6], [2, -20], [11, -14], [2, -10], [-13, -21], [-14, -33], [-6, -7], [-9, -7], [-10, -5], [-9, -5], [-20, -6], [-4, -1]], [[2810, 7011], [-25, -11], [-21, -15], [-21, -10], [-14, -16], [-48, -37], [-7, -9], [-8, -37], [7, -9], [13, -13], [13, -16], [-7, -7], [-2, -7], [32, -33], [9, -16], [0, -13], [-7, -9], [-9, -2], [-14, 5], [-10, -7], [-3, -7], [6, -12], [12, -7], [35, -17], [100, -38], [17, -13], [12, -14], [21, -17], [20, -24], [5, -13], [30, -44], [9, -9], [21, -13], [22, -17], [12, -21], [24, -22], [1, -13], [13, -25], [12, -19], [19, -23], [13, -29], [11, -17], [13, -29], [13, -18], [51, -61], [10, -19], [4, -18], [1, -10], [46, -26], [13, -14], [9, -27], [1, -15], [12, -5], [4, -7], [6, -4], [23, -14], [11, -19], [17, -21], [12, -10], [8, -17], [10, -13], [29, -28], [6, -15], [-11, -30], [-8, -2], [-9, -4], [12, -9], [1, -20], [16, -10], [6, -10], [18, -18], [11, -15], [28, -15], [29, -12], [19, -21], [18, -9], [12, -15], [12, -9], [79, -31], [26, -14], [32, -8], [46, -25], [14, -6], [47, -10], [19, -9], [44, -14], [23, -10], [47, -10], [15, -7], [13, -11], [23, -13], [20, -6], [14, -10], [14, -5], [34, -10], [14, -7], [5, -6], [5, -21], [4, -6], [40, -21], [17, -6], [18, -12], [46, -24], [11, -7]], [[4226, 5437], [8, -6], [3, -21], [0, -24], [9, -46], [9, -23], [2, -14], [6, -9], [1, -11], [0, -45], [-6, -31], [-1, -21], [16, -55], [1, -11], [-1, -15], [-6, -15], [-4, -24], [-4, -11], [-6, -23], [-5, -38], [-10, -31], [-8, -13], [-9, -7], [-16, -2], [-1, -12], [-3, -9], [0, -12], [11, -12], [13, -5], [4, -4], [-2, -9], [-11, -13], [-3, -11], [-2, -9], [2, -17], [-6, -21], [-4, -33], [2, -14], [17, -42], [-1, -8], [-6, -13], [-12, -12], [-8, -6], [-12, -25], [2, -8], [10, -14], [-4, -25], [2, -11], [-6, -9], [-3, -19], [-13, -25], [-2, -12], [-12, -25], [-2, -13], [0, -20], [-2, -9], [-3, -3], [-15, -12], [-5, -9], [-10, -26], [-4, -33], [-12, -14], [-5, -17], [-11, -11], [-16, -8], [-4, -8], [5, -29], [19, -16], [4, -9], [1, -22], [-4, -30], [-8, -22], [-38, -20], [-6, -31], [1, -14], [7, -24], [-1, -19], [12, -34], [7, -33], [2, -42], [13, -19], [-5, -16], [1, -13], [-20, -32], [-6, -6], [-16, -8], [7, -20], [0, -15], [9, -9], [-4, -14], [-24, -18], [-4, -7], [-10, -14], [-11, -15], [2, -13], [-6, -14], [-3, -21], [-18, -32], [-6, -18], [-24, -15], [-9, -11], [-8, -11], [-8, -6], [-9, -9], [6, -18], [-14, -12], [-14, -11], [-13, -44], [-14, -15], [-5, -11], [-16, -5], [-3, -12], [-2, -8], [-3, -18], [-6, -12], [-8, -4], [-15, -2], [-32, 3], [-5, -7], [-4, -9], [9, -14], [-9, -12], [0, -11], [21, -23], [7, -13], [-1, -10], [-8, -25], [1, -15], [6, -12], [36, -63], [-3, -21], [-23, -38], [-10, -7], [-27, -12], [0, -12], [-10, -19], [-6, -22], [-20, -42], [-9, -11], [3, -15], [13, -21], [3, -14], [6, -7], [14, -6], [13, -1], [-1, -3], [-9, -3], [-4, -6], [-2, -5], [16, -3], [14, -3], [40, 2], [10, 4], [23, 21], [8, 3], [11, 0], [11, -3], [19, -12], [10, -3], [8, 0], [17, 4], [9, 14], [6, 2], [-6, -16], [-18, -8], [-25, -2], [-12, -6], [-5, -5], [-6, -6], [6, -6], [6, -3], [16, -2], [18, 3], [6, -24], [7, -19], [-3, -5], [-17, 19], [-12, 6], [-10, -2], [-10, -4], [-2, -5], [10, -11], [12, -11], [-3, 0], [-17, 1], [-11, -17], [1, -15], [11, -11], [2, -8], [-1, -5], [-17, -1], [-5, -9], [-4, -8], [-19, -12], [-4, -14], [15, -19], [-10, -24], [-22, -4], [-3, -17], [-3, -11], [18, -7], [20, -6], [24, -11], [24, -4], [-3, -17], [-8, -15], [-48, -19], [-26, -5], [-15, -1], [-6, -13], [-6, -15], [31, -2], [24, -11], [12, -3], [5, -3], [1, -3], [-6, 0], [-32, 10], [-9, 1], [-16, -4], [-25, -11], [-26, 1], [-7, -16], [4, -8], [13, -3], [10, -4], [0, -13], [-5, -10], [-3, -18], [-3, -5], [1, -9], [-8, -13], [-7, -9], [-11, -7], [-14, 0], [-1, 4], [2, 3], [18, 13], [15, 33], [0, 11], [1, 11], [-5, 5], [-11, -1], [-10, -3], [-7, -21], [-5, -11], [-18, -7], [-18, -2], [-22, -1], [-3, 3], [5, 1], [39, 8], [9, 8], [-6, 12], [9, 5], [6, 6], [11, 28], [5, 7], [3, 6], [-14, 4], [-5, 1], [-11, -2], [-9, -4], [-4, -4], [4, -11], [2, -5], [0, -3], [-2, -4], [-9, -5], [-20, -4], [-10, -1], [-13, -3], [-24, -1], [-19, 3], [-43, -6], [-2, -13], [11, -10], [11, -7], [-22, -8], [-24, -14], [-19, -6], [-8, -6], [-32, -13], [-7, -10], [0, -8], [10, -9], [20, -8], [10, 1], [4, 3], [2, 4], [-9, 12], [-11, 1], [-6, 1], [4, 4], [9, 3], [20, 2], [27, 5], [18, 9], [5, 0], [-9, -12], [3, -7], [3, -5], [28, -6], [17, -7], [26, -2], [4, 0], [2, 2], [-3, 3], [1, 4], [4, 3], [20, -2], [15, -10], [8, -9], [-8, -12], [7, -10], [-8, -3], [-27, -13], [-11, -10], [23, -11], [11, -3], [8, -1], [8, -2], [-3, -4], [-13, -6], [-11, 2], [-12, 7], [-9, 2], [-9, 1], [-8, -5], [-10, -9], [7, -6], [6, -2], [20, -2], [7, -4], [0, -6], [4, -6], [17, -2], [21, 1], [20, 3], [14, 7], [9, 13], [5, 8], [5, 1], [11, -24], [1, -6], [3, -6], [5, -2], [10, -10], [16, -4], [1, -3], [-21, -2], [-46, 16], [-57, 0], [-21, 3], [-27, 2], [0, -17], [1, -12], [12, -10], [3, -10], [19, -3], [10, 4], [14, 3], [24, -5], [-7, -3], [-11, -1], [-6, 2], [-8, -3], [-16, -8], [-6, -21], [1, -27], [3, -32], [-1, -5], [2, -3], [7, -4], [11, -3], [6, 10], [2, 9], [4, 7], [9, 6], [2, 9], [5, 9], [13, 0], [0, -3], [-7, -1], [-4, -3], [1, -7], [-2, -9], [-4, -7], [-5, -6], [-2, -7], [16, -6], [13, -4], [8, -9], [-8, -1], [-9, 3], [-21, 4], [-18, -3], [-9, -2], [-4, -12], [0, -7], [21, -13], [14, -5], [9, 3], [8, -7], [-9, -3], [-45, 5], [-14, -10], [-28, -13], [16, -8], [13, -9], [7, -1], [10, -4], [39, -7], [12, -5], [-34, 3], [-1, -13], [4, -3], [10, -8], [17, -12], [12, 5], [21, 25], [10, 5], [3, 0], [-5, -8], [11, -6], [-1, -2], [-5, 0], [-12, -5], [-12, -15], [3, -11], [-6, 0], [-42, 13], [-2, 2], [-5, 2], [-20, 23], [-5, 8], [-29, 11], [-11, 2], [-11, -5], [-8, -6], [11, -7], [7, -9], [-5, -5], [-22, -1], [-36, -1], [5, -11], [10, -10], [25, -19], [17, -2], [15, -5], [11, -2], [14, -1], [11, -4], [18, -1], [13, 1], [26, -7], [6, -7], [-5, -30], [-20, 4], [-18, -11], [7, -3], [25, -8], [11, 3], [5, -3], [7, -2], [8, 1], [15, -6], [10, -11], [9, -9], [19, -3], [28, 9], [37, 13], [47, 7], [2, 6], [-9, 5], [-8, 1], [-12, 7], [-47, 12], [-7, 1], [-4, 1], [4, 3], [6, 1], [52, -14], [31, -14], [8, -6], [-1, -8], [-4, -5], [-15, -6], [0, -6], [2, -3], [13, -14], [0, -5], [0, -4], [-2, -3], [-5, -6], [-9, -4], [-6, -1], [-5, 3], [3, 3], [15, 8], [2, 5], [-2, 6], [-9, 5], [-7, 6], [0, 6], [-5, 3], [-9, 1], [-7, -1], [-14, -9], [-28, -9], [-17, -3], [-10, -1], [-19, 2], [-10, -1], [-17, 8], [-5, 0], [0, -6], [-3, -6], [-6, -2], [-12, -2], [-30, 8], [-13, 0], [-9, 4], [-10, 2], [-4, -2], [4, -5], [4, -4], [9, -12], [3, -7], [9, 1], [8, -3], [5, -11], [-5, -7], [3, -7], [14, -5], [29, 3], [18, -3], [29, 10], [20, -3], [9, 6], [0, 8], [8, 0], [7, -5], [-10, -7], [-14, -11], [-15, -5], [-42, -9], [19, -7], [7, -6], [17, -9], [31, -2], [10, 2], [4, 3], [1, 5], [0, 9], [12, 11], [19, 8], [5, 7], [-1, 7], [9, 4], [9, 1], [21, -3], [3, -5], [6, -2], [18, 9], [12, 2], [59, -2], [21, -2], [22, -5], [-6, -4], [-5, -2], [-29, -2], [-26, 4], [-20, -1], [-48, -17], [-25, -1], [-1, 5], [-6, 3], [-7, -2], [-15, -6], [-8, -5], [-3, -7], [-1, -20], [-12, -5], [-8, -7], [8, -5], [39, -14], [25, -4], [3, 9], [5, 9], [5, 4], [26, 12], [21, 8], [33, 6], [73, 25], [23, -5], [10, -3], [-1, -5], [-2, -3], [-16, -12], [-16, -8], [-48, -13], [-8, -27], [-14, -4], [-2, 3], [6, 4], [5, 6], [-3, 12], [-13, 6], [-20, -2], [-24, 0], [-8, 0], [-15, -11], [2, -7], [3, -5], [29, -17], [10, -4], [33, -6], [25, -8], [36, -4], [21, -5], [31, 6], [12, 5], [7, 22], [-5, 13], [0, 8], [4, 15], [19, 28], [-3, 8], [7, 13], [16, 6], [17, 4], [24, 1], [70, 15], [20, 1], [21, 5], [8, 4], [16, 16], [30, 7], [15, -1], [18, -5], [81, -10]], [[4508, 1857], [7, 5], [-15, 12], [-28, 19], [-32, 32], [-7, 4], [-10, 4], [-21, -3], [-19, 7], [-13, -1], [-8, 2], [15, 3], [20, 0], [23, 1], [3, 6], [1, 5], [-5, 15], [-11, 22], [-9, 10], [-9, -1], [-12, -4], [-1, 2], [18, 8], [11, 9], [2, 12], [7, 18], [7, 9], [15, 12], [27, 10], [23, 6], [17, 4], [7, 3], [-8, 7], [-8, 6], [-9, 3], [-23, 2], [-32, -1]], [[4431, 2105], [10, 3], [35, 4], [4, 7], [-5, 8], [1, 4], [14, -12], [11, -12], [12, -6], [21, -7], [16, 1], [33, 12], [13, 7], [6, 6], [17, 54], [-4, 4], [1, 6], [31, 31], [29, 15], [19, 13], [14, 6], [36, 12], [26, 10], [29, 9], [54, 27], [11, 7], [4, 4], [15, 4], [-8, 9], [-10, 3], [-23, -3], [-11, 0], [-8, 3], [27, 4], [22, 9], [10, 7], [6, 7], [5, 24], [-4, 9], [-12, 10], [-21, 7], [-93, 5], [-18, 4], [-87, 48], [-18, 11], [-8, 11], [-3, 8], [-3, 11], [1, 12], [6, 8], [24, 21], [19, 21], [45, 33], [9, 3], [42, 5], [7, 3], [6, 4], [21, 9], [22, 7], [62, -4], [17, -1], [5, 7], [1, 7], [-14, 9], [7, 14], [41, 19], [10, 13], [4, 8], [-7, 13], [10, 11], [-9, 28], [3, 17], [4, 6], [9, 5], [30, 24], [20, 11], [18, 6], [41, 8], [8, 3], [8, 6], [-9, 2], [-9, 0], [-27, 4], [-56, 16], [8, 10], [22, 3], [23, 11], [24, 2], [23, -6], [11, -8], [4, -11], [13, -12], [13, -2], [49, 8], [7, 7], [4, 5], [3, 15], [0, 15], [-5, 13], [-8, 10], [-6, 4], [-9, 4], [-14, -1], [-28, -6], [-20, -4], [-4, -3], [28, -2], [-1, -9], [-5, -5], [-23, -2], [-23, -2], [-21, 2], [-1, 7], [7, 6], [2, 3], [-3, 1], [-13, 0], [-11, 4], [-28, 6], [-13, 6], [-10, 14], [7, 24], [-1, 19], [-16, 34], [-4, 14], [1, 17], [2, 7], [10, 8], [21, 8], [7, -1], [9, -2], [-2, -4], [-4, -2], [33, -4], [34, -8], [37, -8], [50, -15], [21, -1], [59, 0], [36, 5], [23, 6]], [[5314, 3047], [58, 17], [13, 8], [8, 15], [-21, 22], [-5, 11], [4, 17], [11, 26], [5, 6], [5, 1]], [[5392, 3170], [17, 6], [7, 27], [1, 11], [-8, 3], [-7, 6], [18, 0], [-10, 7], [-12, 5], [-12, 2], [-6, 10], [5, 17], [-10, 14], [5, 6], [21, -2], [17, -11], [32, -4], [35, -4], [31, 2], [39, -1], [30, 2], [153, 14], [22, 4], [96, 13], [118, 25], [76, 28], [14, 9], [6, 19], [16, 17], [44, 31], [51, 52], [8, 11], [1, 12], [-5, 33], [-2, 4], [-5, 4], [-27, -1], [-20, 6], [-27, 16], [-10, 13], [-5, 13], [3, 19], [28, 23], [-2, 15], [-19, 18], [-35, 18], [-31, 13], [-74, 22], [-19, 16], [-7, 8], [-9, 17], [8, 3], [5, 2], [6, 6], [-2, 14], [-3, 7]], [[5938, 3790], [-4, 10], [-10, 15], [-3, 10], [13, 39], [5, 19], [7, 4], [9, -1], [9, 0], [7, 7], [4, 6], [-7, 41], [3, 10]], [[5971, 3950], [5, -10], [5, -20], [7, -14], [-2, -8], [-9, -10], [-9, -7], [-10, -1], [-11, -5], [2, -8], [-8, -26], [-4, -22], [5, -21], [28, -20], [35, -21], [8, -9], [5, -6], [6, -3], [41, 3], [53, -1], [45, -23], [56, -11], [11, -9], [19, -4], [8, 0], [11, 0], [37, 10], [27, 3], [43, -3], [19, -9], [20, 0], [28, -4], [77, 21], [13, 7], [15, 0], [22, 16], [32, 14], [6, 14], [30, 24], [9, 18], [8, 7], [7, 4]], [[6661, 3816], [64, 36], [22, 14]], [[6747, 3866], [16, 14]], [[6763, 3880], [21, 27]], [[6784, 3907], [24, 46]], [[6808, 3953], [9, 13]], [[6817, 3966], [12, 10]], [[6829, 3976], [9, 6]], [[6838, 3982], [-5, 8]], [[6833, 3990], [-4, 13]], [[6829, 4003], [0, 9]], [[6829, 4012], [10, 20]], [[6839, 4032], [14, 10]], [[6853, 4042], [4, 11]], [[6857, 4053], [3, 11]], [[6860, 4064], [7, 5]], [[6867, 4069], [30, 10]], [[6897, 4079], [30, 15]], [[6927, 4094], [6, 5]], [[6933, 4099], [-3, 8], [4, 7]], [[6934, 4114], [12, 7]], [[6946, 4121], [2, 18]], [[6948, 4139], [6, -3]], [[6954, 4136], [5, -5]], [[6959, 4131], [-1, 17]], [[6958, 4148], [6, 13]], [[6964, 4161], [9, 6]], [[6973, 4167], [4, 5]], [[6977, 4172], [-18, 12]], [[6959, 4184], [-2, 11]], [[6957, 4195], [0, 11]], [[6957, 4206], [7, -2]], [[6964, 4204], [2, -7]], [[6966, 4197], [8, -9]], [[6974, 4188], [20, -5]], [[6994, 4183], [2, -12]], [[6996, 4171], [13, 0], [41, 14]], [[7050, 4185], [12, -2]], [[7062, 4183], [2, -6]], [[7064, 4177], [-5, -13]], [[7059, 4164], [-5, -2], [-10, 5]], [[7044, 4167], [-4, -2]], [[7040, 4165], [4, -29]], [[7044, 4136], [-12, -12]], [[7032, 4124], [-24, -9]], [[7008, 4115], [-4, -11]], [[7004, 4104], [2, -5]], [[7006, 4099], [-4, -4], [-18, 1]], [[6984, 4096], [-8, -4]], [[6976, 4092], [1, -15]], [[6977, 4077], [-3, -8]], [[6974, 4069], [-14, -14]], [[6960, 4055], [-24, -9]], [[6936, 4046], [-34, -23]], [[6902, 4023], [-17, -2]], [[6885, 4021], [-6, -4]], [[6879, 4017], [-7, -3], [-15, 5]], [[6857, 4019], [-10, -2]], [[6847, 4017], [1, -8]], [[6848, 4009], [2, -7], [-3, -9]], [[6847, 3993], [4, -5]], [[6851, 3988], [17, 13], [17, 9]], [[6885, 4010], [49, 21]], [[6934, 4031], [44, 23]], [[6978, 4054], [33, 24], [24, 20]], [[7035, 4098], [19, 18]], [[7054, 4116], [45, 49]], [[7099, 4165], [38, 66], [41, 46]], [[7178, 4277], [36, 30]], [[7214, 4307], [32, 22], [36, 18]], [[7282, 4347], [32, 13]], [[7314, 4360], [0, 14]], [[7314, 4374], [15, 14], [6, 11], [4, 14], [2, 26], [-5, 28], [10, 20]], [[7346, 4487], [-3, 11]], [[7343, 4498], [6, 7], [-2, 8]], [[7347, 4513], [-4, 7]], [[7343, 4520], [-3, 19], [-9, 18]], [[7331, 4557], [0, 10]], [[7331, 4567], [3, 9], [1, 12]], [[7335, 4588], [-7, 6]], [[7328, 4594], [-7, 9]], [[7321, 4603], [5, 4], [5, 0], [8, 5]], [[7339, 4612], [7, 26], [-6, 6]], [[7340, 4644], [-9, 0], [2, 3]], [[7333, 4647], [17, 3]], [[7350, 4650], [21, 23], [-5, 5]], [[7366, 4678], [-11, 4], [-26, 3]], [[7329, 4685], [-6, 13], [13, -8], [11, -1], [13, 1]], [[7360, 4690], [7, 4]], [[7367, 4694], [-5, 10], [8, 4], [19, -4], [12, 0], [-8, -10], [6, -1]], [[7399, 4693], [25, 18]], [[7424, 4711], [14, 8]], [[7438, 4719], [3, 10]], [[7441, 4729], [-7, 0], [-5, 4]], [[7429, 4733], [11, 3]], [[7440, 4736], [5, 1]], [[7445, 4737], [7, 4], [34, 18]], [[7486, 4759], [65, 31]], [[7551, 4790], [39, 27]], [[7590, 4817], [33, 13], [95, 33], [18, 3], [25, 0]], [[7761, 4866], [20, -4]], [[7781, 4862], [9, 0]], [[7790, 4862], [4, 5]], [[7794, 4867], [2, 8]], [[7796, 4875], [14, 9]], [[7810, 4884], [16, 2]], [[7826, 4886], [37, 21]], [[7863, 4907], [41, 5]], [[7904, 4912], [7, 2]], [[7911, 4914], [7, 4], [-8, 5]], [[7910, 4923], [-7, 2]], [[7903, 4925], [-1, 11]], [[7902, 4936], [6, 5]], [[7908, 4941], [39, 5]], [[7947, 4946], [31, 0]], [[7978, 4946], [14, 7]], [[7992, 4953], [26, 3], [24, -6], [3, -4]], [[8045, 4946], [-16, -4]], [[8029, 4942], [-15, 1]], [[8014, 4943], [-11, -2], [11, -5]], [[8014, 4936], [23, 4]], [[8037, 4940], [29, 2], [23, 5]], [[8089, 4947], [21, 1]], [[8110, 4948], [4, 5]], [[8114, 4953], [-2, 7]], [[8112, 4960], [-4, 5]], [[8108, 4965], [-1, 3]], [[8107, 4968], [2, 5]], [[8109, 4973], [11, 3]], [[8120, 4976], [10, 0]], [[8130, 4976], [3, -5], [-5, -8], [2, -6], [10, -4], [8, -3]], [[8148, 4950], [18, 0]], [[8166, 4950], [36, 3]], [[8202, 4953], [65, 0]], [[8267, 4953], [12, -1]], [[8279, 4952], [8, 11], [6, 6], [-6, 6]], [[8287, 4975], [-2, 9]], [[8285, 4984], [3, 7], [39, 29]], [[8327, 5020], [17, 6]], [[8344, 5026], [66, 17]], [[8410, 5043], [18, 9]], [[8428, 5052], [1, 9]], [[8429, 5061], [-4, 32]], [[8425, 5093], [-1, 2], [-3, 9]], [[8421, 5104], [13, 28]], [[8434, 5132], [18, 22]], [[8452, 5154], [6, 13]], [[8458, 5167], [9, 7]], [[8467, 5174], [18, 6], [29, 23], [11, 15]], [[8525, 5218], [3, 14]], [[8528, 5232], [14, 9], [8, 25], [20, 24], [23, 10], [9, 8]], [[8602, 5308], [7, 12]], [[8609, 5320], [4, 19]], [[8613, 5339], [-6, 45]], [[8607, 5384], [1, 22]], [[8608, 5406], [12, 41]], [[8620, 5447], [24, 27], [10, 8]], [[8654, 5482], [20, 7]], [[8674, 5489], [17, 15], [-2, 7], [-6, 34]], [[8683, 5545], [1, 15]], [[8684, 5560], [6, 14], [6, 29], [8, 28], [15, 33]], [[8719, 5664], [11, 34]], [[8730, 5698], [0, 2]], [[8730, 5700], [-8, 29]], [[8722, 5729], [-8, 33]], [[8714, 5762], [-2, 34], [-7, 29]], [[8705, 5825], [17, 66], [-4, 3], [-6, -11]], [[8712, 5883], [-5, 7]], [[8707, 5890], [1, 5]], [[8708, 5895], [0, 25]], [[8708, 5920], [5, 9]], [[8713, 5929], [2, 6], [-3, 3], [-4, 3]], [[8708, 5941], [-7, -4]], [[8701, 5937], [3, 12]], [[8704, 5949], [5, 12], [10, 10]], [[8719, 5971], [18, 13]], [[8737, 5984], [0, 12]], [[8737, 5996], [10, 13]], [[8747, 6009], [-3, 7]], [[8744, 6016], [-9, 5], [9, 1], [6, 4]], [[8750, 6026], [8, 13]], [[8758, 6039], [5, -2], [18, -13]], [[8781, 6024], [4, -20]], [[8785, 6004], [7, -1]], [[8792, 6003], [7, 0]], [[8799, 6003], [23, 13]], [[8822, 6016], [32, 26]], [[8854, 6042], [8, 13]], [[8862, 6055], [39, 39]], [[8901, 6094], [31, 47]], [[8932, 6141], [8, 17]], [[8940, 6158], [-4, 10]], [[8936, 6168], [12, 15], [5, 4]], [[8953, 6187], [0, -5]], [[8953, 6182], [-1, -5]], [[8952, 6177], [-4, -4]], [[8948, 6173], [0, -6]], [[8948, 6167], [6, 3]], [[8954, 6170], [19, 20]], [[8973, 6190], [0, 13]], [[8973, 6203], [8, -2]], [[8981, 6201], [5, 3]], [[8986, 6204], [22, 25]], [[9008, 6229], [24, 15]], [[9032, 6244], [19, 9]], [[9051, 6253], [32, 11]], [[9083, 6264], [2, 0]], [[9085, 6264], [25, 27]], [[9110, 6291], [24, 16]], [[9134, 6307], [24, 24]], [[9158, 6331], [6, 8]], [[9164, 6339], [-6, 9]], [[9158, 6348], [8, -3]], [[9166, 6345], [10, 1]], [[9176, 6346], [23, 18]], [[9199, 6364], [37, 32]], [[9236, 6396], [26, 32]], [[9262, 6428], [28, 55]], [[9290, 6483], [10, 33]], [[9300, 6516], [8, 13]], [[9308, 6529], [0, 10]], [[9308, 6539], [-6, 13]], [[9302, 6552], [1, 6], [3, 6], [-1, 4]], [[9305, 6568], [0, 7]], [[9305, 6575], [6, 14], [2, 12]], [[9313, 6601], [-4, 27]], [[9309, 6628], [-6, 3]], [[9303, 6631], [-1, 10]], [[9302, 6641], [-7, 13]], [[9295, 6654], [-9, 41]], [[9286, 6695], [-15, 22]], [[9271, 6717], [-6, 28], [-14, 37]], [[9251, 6782], [-22, 33]], [[9229, 6815], [-13, 9], [-10, 4]], [[9206, 6828], [-61, 8]], [[9145, 6836], [-26, -4], [-32, 1], [-29, -2]], [[9058, 6831], [-23, 5]], [[9035, 6836], [-16, 9]], [[9019, 6845], [-13, 3], [-32, 3]], [[8974, 6851], [-18, 21]], [[8956, 6872], [-46, 12], [-25, 20], [-36, 20]], [[8849, 6924], [-32, 28]], [[8817, 6952], [-12, 8]], [[8805, 6960], [-17, 16]], [[8788, 6976], [-30, 7]], [[8758, 6983], [-30, 16]], [[8728, 6999], [-17, 12], [-48, 20], [-23, 8], [-14, 7], [-23, 7]], [[8603, 7053], [-27, 14], [-39, 5]], [[8537, 7072], [-34, 1], [-57, -7]], [[8446, 7066], [-46, -2]], [[8400, 7064], [-18, -5]], [[8382, 7059], [-23, 2]], [[8359, 7061], [-23, 4]], [[8336, 7065], [-11, 7]], [[8325, 7072], [-22, 7]], [[8303, 7079], [-18, -7]], [[8285, 7072], [-36, 2]], [[8249, 7074], [-49, 14]], [[8200, 7088], [-12, 7]], [[8188, 7095], [-22, 6]], [[8166, 7101], [-15, 7]], [[8151, 7108], [-42, 9]], [[8109, 7117], [-21, 1]], [[8088, 7118], [-8, -4]], [[8080, 7114], [-3, -10]], [[8077, 7104], [-39, -1]], [[8038, 7103], [-19, -8]], [[8019, 7095], [-10, 1]], [[8009, 7096], [-12, -6], [-25, -18]], [[7972, 7072], [-5, 6]], [[7967, 7078], [5, 6], [11, 10], [2, 4]], [[7985, 7098], [-1, 7]], [[7984, 7105], [-10, 3]], [[7974, 7108], [-7, 0]], [[7967, 7108], [-12, -7]], [[7955, 7101], [-10, -22]], [[7945, 7079], [-8, -21]], [[7937, 7058], [-27, -21]], [[7910, 7037], [-14, -7]], [[7896, 7030], [0, 7]], [[7896, 7037], [12, 40]], [[7908, 7077], [4, 9], [3, 11], [4, 5], [6, 5]], [[7925, 7107], [0, 8]], [[7925, 7115], [20, 4]], [[7945, 7119], [-2, 10], [-6, 10], [-12, -2]], [[7925, 7137], [-9, -4]], [[7916, 7133], [-11, -15], [-6, 5], [-8, 6]], [[7891, 7129], [7, 3]], [[7898, 7132], [7, 1]], [[7905, 7133], [6, 8]], [[7911, 7141], [5, 4]], [[7916, 7145], [6, 7]], [[7922, 7152], [-1, 11], [-6, 11]], [[7915, 7174], [-9, 10]], [[7906, 7184], [-10, -5], [-8, -1], [8, 7]], [[7896, 7185], [-10, 1]], [[7886, 7186], [-5, 6]], [[7881, 7192], [-13, 8], [-15, 8], [-8, 5], [-15, -4], [-8, -13]], [[7822, 7196], [-6, -7]], [[7816, 7189], [-7, -2]], [[7809, 7187], [-3, 16]], [[7806, 7203], [-15, 22], [-14, 3]], [[7777, 7228], [-13, -2]], [[7764, 7226], [-19, 10]], [[7745, 7236], [-28, 7]], [[7717, 7243], [-10, 9]], [[7707, 7252], [-14, -2]], [[7693, 7250], [-10, 2]], [[7683, 7252], [-1, 7], [-14, -1]], [[7668, 7258], [-15, 1]], [[7653, 7259], [-13, 4]], [[7640, 7263], [-15, 3]], [[7625, 7266], [-4, 5], [-17, 9]], [[7604, 7280], [-6, 6]], [[7598, 7286], [-12, 0]], [[7586, 7286], [-7, 3]], [[7579, 7289], [-12, 0]], [[7567, 7289], [-14, 0]], [[7553, 7289], [-11, 7]], [[7542, 7296], [-10, 4]], [[7532, 7300], [-18, 2]], [[7514, 7302], [-6, -2]], [[7508, 7300], [-3, -4]], [[7505, 7296], [4, -4], [2, -5], [-8, 2]], [[7503, 7289], [-12, 8]], [[7491, 7297], [-13, -5], [-5, -1], [-7, 2]], [[7466, 7293], [-6, 3]], [[7460, 7296], [-5, 2]], [[7455, 7298], [-11, -3]], [[7444, 7295], [-11, -8], [-10, 6]], [[7423, 7293], [-5, 0]], [[7418, 7293], [-7, -3]], [[7411, 7290], [-2, -6]], [[7409, 7284], [-10, -3], [-9, -8], [-8, -6], [2, -9]], [[7384, 7258], [-20, -11]], [[7364, 7247], [5, -9]], [[7369, 7238], [-9, -10]], [[7360, 7228], [1, -7], [2, -4], [15, -5], [-14, -4], [-2, -10]], [[7362, 7198], [-10, 5]], [[7352, 7203], [-10, 8]], [[7342, 7211], [-16, 0]], [[7326, 7211], [-40, -36]], [[7286, 7175], [-23, -5]], [[7263, 7170], [-8, -4], [-28, -45]], [[7227, 7121], [-7, -17]], [[7220, 7104], [-10, -10]], [[7210, 7094], [-7, -3]], [[7203, 7091], [-9, -3]], [[7194, 7088], [5, 8]], [[7199, 7096], [7, 6]], [[7206, 7102], [7, 26]], [[7213, 7128], [6, 9]], [[7219, 7137], [9, 23], [12, 25], [-39, -14], [-19, -6]], [[7182, 7165], [-26, 6]], [[7156, 7171], [-14, 4]], [[7142, 7175], [-17, -3], [-8, -4], [-12, -3]], [[7105, 7165], [-20, -9]], [[7085, 7156], [-27, 17]], [[7058, 7173], [-7, 3]], [[7051, 7176], [-7, 6]], [[7044, 7182], [2, 7]], [[7046, 7189], [-1, 6]], [[7045, 7195], [-15, 16]], [[7030, 7211], [-5, 12]], [[7025, 7223], [-1, 7]], [[7024, 7230], [-2, 9], [-8, 6]], [[7014, 7245], [-3, 5], [11, 8], [0, 4]], [[7022, 7262], [-8, 7]], [[7014, 7269], [-14, -5]], [[7000, 7264], [-5, -5]], [[6995, 7259], [-25, -11]], [[6970, 7248], [-13, -9]], [[6957, 7239], [-34, -14]], [[6923, 7225], [-16, -4]], [[6907, 7221], [-43, -20]], [[6864, 7201], [-36, -6]], [[6828, 7195], [-16, 9], [-50, 0]], [[6762, 7204], [15, 4], [47, 16]], [[6824, 7224], [30, -4]], [[6854, 7220], [5, 4], [7, 5], [2, 14]], [[6868, 7243], [14, 7]], [[6882, 7250], [14, 10], [0, 18]], [[6896, 7278], [3, 9]], [[6899, 7287], [21, 23]], [[6920, 7310], [8, 4]], [[6928, 7314], [14, 12]], [[6942, 7326], [15, 23]], [[6957, 7349], [2, 10]], [[6959, 7359], [26, 5]], [[6985, 7364], [19, 17]], [[7004, 7381], [8, 4]], [[7012, 7385], [14, 1]], [[7026, 7386], [8, 5], [25, 21], [17, 23], [17, 12], [7, 9], [32, 19], [3, 3]], [[7135, 7478], [16, 8]], [[7151, 7486], [6, 4]], [[7157, 7490], [-1, 11], [3, 16], [-11, 25], [-14, 8]], [[7134, 7550], [-19, 6]], [[7115, 7556], [-16, 1], [-22, 3]], [[7077, 7560], [-11, 11]], [[7066, 7571], [-6, 7]], [[7060, 7578], [-5, 11], [-7, 3]], [[7048, 7592], [-8, 0]], [[7040, 7592], [6, 5]], [[7046, 7597], [-1, 3], [-8, 18]], [[7037, 7618], [-8, 10]], [[7029, 7628], [-3, 11]], [[7026, 7639], [-2, 8]], [[7024, 7647], [-24, 45], [-8, 21], [-4, 41]], [[6988, 7754], [-20, 45]], [[6968, 7799], [-15, 13], [-20, 10], [-12, -1]], [[6921, 7821], [-1, -8]], [[6920, 7813], [-14, -18]], [[6906, 7795], [-1, 4], [1, 4], [-2, 10], [-4, 6], [-13, 30], [-6, 7], [-7, -1], [-6, -11], [-1, -9], [-4, -4], [-7, -5], [0, 3], [3, 5], [3, 9], [-7, 14], [-7, 7], [-23, 16], [-15, -10], [5, 11], [0, 7], [-23, 8], [-45, 27], [-19, 16], [-53, 12], [-26, 2], [-56, 23], [-11, -1], [-10, -10], [-13, -28], [-11, -5]], [[6548, 7932], [11, 15], [5, 11], [1, 12], [-2, 9], [-13, 5], [-30, 6], [-68, 8], [-45, 1], [-33, -5], [-39, 4], [-25, -3], [-12, -7], [2, -10], [0, -10], [-6, 10], [-43, 9], [-32, 6], [-72, 5], [-13, -5], [-7, -12], [-4, -9], [-1, -10], [-6, -12], [-2, 2]], [[6114, 7952], [-1, 1], [5, 34], [-3, 22], [-5, 9], [-17, 10], [-28, 6], [-10, 13], [-26, 15], [-27, 20], [-13, 4], [-14, 0], [-18, 6], [-17, -3], [-12, -13], [-10, -11], [-3, -18], [-12, -7], [9, 12], [1, 21], [3, 15], [15, 20], [0, 31], [-5, 7], [-16, 16], [-11, 6], [-16, 14], [-55, 36], [-40, 18], [-27, 12], [-10, 2], [0, -5], [-3, 0], [-11, 4], [-21, 17], [-5, 2]], [[5711, 8268], [-21, 7], [-25, 1], [-9, -2], [-11, -7], [-46, 5], [-9, -1], [-24, -9], [-23, -1], [-16, -8], [-19, 10], [-12, 4], [-14, 6]], [[5482, 8273], [54, 0], [10, 13], [7, 12], [4, 11]], [[5557, 8309], [1, 2], [5, 8], [0, 6], [5, 6], [7, 7], [19, 5], [7, 10], [-12, 10], [-20, 11], [-32, 4], [-10, 4], [-29, 22], [-11, 5], [-5, -8], [-21, 0], [1, -7], [4, -5], [0, -8]], [[5466, 8381], [-4, 5], [-6, 3], [-4, 3], [-1, 5], [-10, 10], [-15, 8], [-9, 3], [-6, 1], [-5, -1], [1, -3], [-3, -7], [2, -7], [-3, 1], [-2, 2], [-5, 4], [-5, -7], [-3, -3], [-3, 0]], [[5385, 8398], [-3, -1], [-11, 15], [-17, 27], [-5, 2], [-7, 2], [-1, -6], [1, -4], [-7, -5], [-13, -2], [7, 5], [5, 10], [-4, 10], [-3, 4], [-11, 7], [-4, 0], [-4, 2], [0, 10], [-10, 2], [31, 3], [45, -1], [21, 9], [27, 1], [17, 4], [6, 6], [-52, -4], [-65, 5], [-35, -4], [-13, 1], [-22, -1], [-44, -7], [-54, 2], [-47, -3], [-13, 0], [7, -10], [13, 4], [42, -2], [19, -6], [-7, -3], [-8, -3], [-51, 1], [-94, -37], [-14, -1], [-11, -2], [-15, 0], [-27, 5], [-24, 4], [-24, 7], [-28, 3], [-34, 23], [-1, 5], [-1, 6], [-21, 6], [-106, -3], [-20, -4], [-64, -5], [-42, -5], [-38, 2], [-13, 8], [-9, 13], [-4, 12], [7, 8], [-7, 7], [-3, 11], [-8, 11], [-31, 16], [-30, 13], [-33, 3], [-25, 6], [-42, -2], [-6, -1], [-9, -1], [-12, 9], [-7, 12], [-7, 17], [-3, 16], [-4, 6], [-7, 7], [-13, 6], [-17, -4], [-12, -4], [-6, -10], [-6, -12], [10, -17], [0, -5], [4, -6], [40, 5], [13, 0], [7, -14], [-5, -7], [-11, -3], [-24, 9], [-7, -1], [-9, -9], [-10, -6], [-45, -12], [-39, -6], [-63, -22], [-30, -3], [2, -14], [-12, -6], [3, -16], [4, -10], [4, -6], [11, -22], [18, -13], [8, -13], [18, -19], [4, -14], [-4, -20], [-1, -17], [-17, -14], [-5, -6], [-8, -4], [-34, -8], [-12, 0], [-10, 3], [-7, 6], [-6, 12], [3, 9], [-7, 6], [-9, 4], [-15, 13], [-2, 9], [-18, 19], [23, 31], [10, 6], [13, 15], [18, 14], [11, 22], [-1, 8], [-13, 11], [-6, 17], [8, 0], [5, 2], [-22, 13], [-6, 5], [-16, 24], [-1, 7], [0, 9], [7, 4], [5, 2], [55, 10], [10, 4], [10, 6], [4, 5]], [[4097, 8616], [5, 6], [21, 14], [-3, 12], [-15, 18], [-33, 10], [-15, 0], [-17, -1], [-29, -12], [-2, -4], [-5, -3], [-12, 0], [-12, -5], [-4, -14], [-16, -18], [-24, -9], [-40, -10], [-84, -43], [-52, -3], [-17, 0], [-16, 4], [-22, 3], [-12, -2], [-8, -6], [-2, -16], [-12, -17], [-7, -14], [-7, -5], [-9, 2], [-8, 8], [4, 8], [11, 3], [9, 1], [3, 2], [-18, -1], [-56, 13]], [[3593, 8537], [-11, -5], [-29, -20], [-17, -9], [-5, -6], [-24, -12], [-6, -9], [-9, -21], [-17, -14], [-5, -6], [9, 3], [12, 7], [3, -3], [-8, -9], [0, -14], [-6, -16], [-6, -11], [6, -8], [5, -12], [-5, -10], [-17, -3], [-21, 1], [-18, -7], [-15, -10], [-20, -29], [-59, -31], [-16, -6], [-13, -2], [-4, -5], [7, -6], [7, -5], [7, -17], [4, -32], [-6, -7], [-12, -2], [-4, 2], [-4, 4], [2, 6], [6, 3], [3, 3], [-6, 4], [-6, 2], [-9, 11], [-19, 16], [-19, 10], [-12, 15], [-4, 2]], [[3232, 8279], [-46, 24], [-19, 19], [-36, 18], [-60, 18], [-28, 3], [-34, -1], [-6, 3], [-6, 6], [-13, 2], [-15, 0], [-20, 4], [-32, 3], [-10, -4], [-11, -9], [-18, -10], [-9, -2], [-9, -2], [-21, -14], [-60, -13], [-19, -7], [-23, -14], [-32, -8], [-20, -2], [-22, -1], [-27, 5], [-24, 20], [-13, 6], [-5, 4], [-8, 3], [-1, -3], [10, -7], [8, -10], [-7, -1], [-10, 1], [-26, -2], [-8, 5], [-16, 5], [2, 12], [5, 3], [1, 2], [-2, 3], [-10, -3], [-9, 2], [-5, 13], [1, 5], [-1, 5], [-18, 10], [-9, 6], [-7, 4], [-24, 5], [-5, 7], [-8, 4], [-23, 23], [-13, 6], [-32, 28], [-15, 16], [-18, 29], [-2, 8], [-4, 7], [-3, 4], [-11, 1], [-7, 9], [-9, 12], [-6, 18], [2, 6], [4, 8], [7, 8], [5, 6], [13, 8], [-2, 9], [-5, 10], [-7, 0], [-6, 1], [-5, 3], [2, 4], [6, 4], [0, 4], [1, 9], [7, 0], [3, -3], [2, -1], [2, 22], [2, 6], [-2, 5], [-7, 8], [-5, 10], [5, 5], [5, 2], [8, 4], [5, 11], [2, -5], [-2, -9], [-5, -7], [0, -6], [4, -7], [5, 0], [7, 2], [-1, 11], [-3, 8], [4, 37], [-8, 40], [11, 44], [11, 27], [10, 6], [9, 10], [10, 12], [4, 8], [-16, 43], [-6, 2], [-5, 0], [-6, 6], [4, 5], [6, 3], [6, -1], [0, -9], [3, 1], [9, 12], [5, 3], [4, 4], [-10, 5], [-9, 4], [-11, 17], [-40, 13], [-4, 0], [18, -8], [7, -7], [-5, 0], [-8, 4], [-12, 0], [-6, -4], [-6, 0], [-6, 7], [-10, 7], [-8, 4], [-12, 2], [-5, -2], [-7, 1], [-2, 3], [0, 6], [4, 2], [15, 1], [30, -12], [-1, 3], [-70, 41], [-37, 5], [4, -2], [10, -2], [-2, -2], [-8, -2], [-9, 1], [-13, 8], [-47, 12], [-10, -2], [-17, -6], [-45, -2], [-43, 11], [-29, 2], [4, -2], [3, -5], [-19, -5], [-16, -2], [-25, -11], [-18, 2], [-39, -1], [-22, -3], [-54, 7], [-13, -1], [-16, -3], [-8, 4], [-10, 8], [-12, 0], [-25, -3], [-5, -2], [-14, -8], [-7, -2], [-11, -7], [-13, 3], [-53, 23], [0, -2], [3, -3], [6, -5], [-10, -9], [-15, 4], [-13, 6], [-6, 1], [-8, 2], [-2, 7], [5, 6], [26, 25], [19, 4], [14, 15], [8, 6], [13, 15], [8, 35], [-5, 24], [1, 13], [3, 8], [9, 13], [-10, 10], [7, 15], [2, 10], [16, 29], [2, 11], [-6, 13], [-17, 1], [-7, -2], [-8, 2], [8, 12], [3, 4], [11, 14], [0, 8], [10, 6], [8, 6], [6, 1], [3, -12], [-7, -22], [3, -4], [11, -4], [11, -18], [4, 0], [7, 9], [6, 9], [4, 22], [12, 15], [8, 26], [13, 26], [-1, 3], [-6, 0], [-10, -7], [-5, 1], [0, 10], [4, 3], [9, 3], [8, 1], [4, 2], [7, 6], [1, 9], [-6, 0], [-6, -1], [-11, 0], [-8, -2], [-7, 4], [1, 5], [14, 15], [12, 5], [5, -1], [4, 4], [1, 4], [-5, 11], [0, 10], [7, 14], [28, 29], [23, 13], [19, 17], [9, 10], [7, 13], [7, 15], [-5, 5], [-2, 4], [-1, 19], [-12, 5], [-18, 13], [-13, 3], [-13, -4], [-8, -1], [-14, 0], [-2, -2], [13, -3], [6, 0], [6, 2], [3, 1], [3, -4], [-3, -4], [-4, -2], [-5, -1], [-33, 3], [-29, 6], [-13, 2], [-33, 6], [-18, 1], [-5, -1], [0, -2], [-2, -1], [-10, -1], [-31, 0], [-16, -3], [-24, -10], [-18, -3], [-135, -15], [-10, -2], [-42, -14], [-24, -12], [-12, -27], [-3, -4], [-4, -17], [1, -18], [-1, -17], [1, -21], [-2, -8], [-2, -4], [-20, -12], [-7, -7], [-6, -39], [-31, -22], [-15, -5], [-11, -7], [-43, -15], [-3, -3], [-1, -3], [3, 0], [15, 7], [1, -2], [-5, -8], [2, -3], [7, 0], [4, -6], [0, -10], [-23, -9], [-14, -9], [-9, -1], [-29, 3], [-16, 6], [-4, 4], [9, 3], [0, 4], [-14, 9], [-18, -2], [-16, -2], [-32, -1], [-7, -1], [-32, -5], [-2, -4], [-6, -6], [-17, -5], [-34, -5], [-15, 2], [-46, -1], [-30, -8], [-16, -6], [-45, -11], [-29, -3], [-10, 0], [-12, 1], [-19, 18], [-17, 17], [-31, 6], [-24, 14], [-54, 2], [-19, 6], [-4, -1], [10, -5], [11, -3], [-7, 0], [-28, 7], [-14, 6], [-1, 4], [16, -5], [5, 0], [-19, 10], [-10, 16], [-13, 6], [-7, 9], [-24, 16], [-4, 13], [-7, 10], [-13, 32], [-36, 34], [-59, 44], [-9, 11], [-1, 9], [-23, 32], [-21, 31], [0, 3], [-2, 5], [-7, 3], [-5, 3], [-6, 8], [-16, 44], [23, -28], [19, -16], [11, -4], [0, -5], [-6, -6], [-1, -11], [3, -9], [3, 10], [7, 7], [4, 13], [-7, 6], [-18, 9], [-14, 11], [-26, 31], [-2, 19], [-9, 24], [0, 5], [-2, 7], [6, 16], [8, 11], [2, 6], [-3, 39], [6, 45], [-3, 3], [2, 2], [2, 21], [7, 43], [23, 64]], [[2005, 9097], [2, -3], [11, 6], [4, 3], [2, 2], [-7, 1], [-6, -3], [-6, -6]], [[3878, 9483], [-8, -1], [-8, -3], [9, -3], [3, 0], [5, 0], [15, -3], [14, -2], [3, 3], [-6, 3], [-18, 5], [-9, 1]], [[4513, 0], [0, 20], [-9, 17], [-24, 13], [-10, 10], [18, 2], [17, 3], [46, 4], [110, -7], [9, 10], [28, 7], [19, 9], [-6, 14], [-24, 6], [-29, 12], [13, 9], [24, 0], [11, 11], [-9, 10], [11, 13], [21, 16], [14, 6], [-26, 10], [-25, 13], [7, 10], [12, 10], [15, 15], [21, 10], [13, 4], [-4, 3], [-31, 4], [-29, 1], [-52, -7], [-8, 1], [-3, 4], [-3, 7], [5, 17], [8, 17], [8, 2], [20, 2], [20, 13], [18, 1], [11, -5], [4, -16], [6, -4], [-2, -8], [9, -3], [11, 5], [22, 3], [8, -6], [7, -3], [4, 5], [-2, 14], [-4, 5], [-3, 9], [5, 4], [5, 7], [-9, 14], [4, 5], [19, 9], [9, 2], [18, 0], [32, -6], [15, 0], [12, 2], [7, 6], [6, 18], [-14, 6], [0, 6], [8, 3], [14, 13], [22, 0], [21, -1], [21, 3], [-7, 5], [-7, 8], [23, 3], [16, 2], [39, -5], [16, -3], [14, 6], [-5, 7], [-15, 3], [-3, 6], [4, 7], [25, -4], [5, 1], [7, 7], [-4, 3], [-3, 4], [32, 1], [5, 1], [6, 5], [10, 2], [29, 0], [6, 2], [3, 6], [-16, 1], [-20, 5], [-3, 15], [5, 10], [18, 9], [22, 7], [39, -6], [31, 2], [13, -6], [17, -1], [4, 6], [-8, 5], [-5, 9], [49, 11], [16, -2], [20, 3], [-7, 8], [10, 10], [14, 2], [10, -9], [13, -2], [15, 2], [37, 11], [18, 1], [18, 1], [18, 6], [5, 7], [10, 6], [32, 6], [13, 5], [28, 18], [-5, 4], [8, 4], [84, 16], [41, 1], [69, 9], [41, 12], [26, 4], [24, 13], [28, 2], [66, 8], [49, 14], [69, 10], [31, -1], [13, -3], [8, -11], [14, -14], [20, -7], [-7, -7], [-20, 1], [-21, -1], [-5, 7], [8, 5], [-8, 4], [-18, -1], [-26, -2], [-17, -4], [-22, -7], [-17, -4], [-58, -12], [-39, -16], [-27, -17], [-16, -11], [-24, -1], [-6, -4], [10, -3], [8, -2], [17, -1], [-3, -5], [-12, -2], [1, -3], [13, -6], [3, -9], [-15, -1], [-23, 9], [-25, 1], [-20, 4], [-13, 6], [-13, -1], [-9, -9], [6, -9], [-11, -6], [-12, 3], [-5, 11], [-12, 2], [-16, 0], [-40, -12], [-14, 0], [-8, -7], [-23, -7], [-15, -5], [-36, -19], [-21, -8], [-39, -4], [-15, 0], [-9, 2], [-14, 2], [-15, 0], [-4, -5], [22, -16], [-12, -6], [-27, 0], [-14, 5], [-10, -4], [-9, -4], [-9, -7], [13, -13], [22, -6], [15, -1], [6, -5], [-34, -2], [-22, -11], [-11, -8], [-12, -7], [2, -8], [18, -12], [23, -9], [24, -1], [31, 3], [7, 2], [31, 2], [13, 8], [10, 1], [8, -2], [14, 0], [8, 5], [10, 2], [15, -1], [27, 0], [8, -5], [-9, -5], [-17, -8], [-16, 4], [-14, -1], [-7, -4], [15, -8], [-6, -8], [-12, -7], [-15, 4], [-3, 9], [-20, 5], [-20, 2], [-13, -9], [-22, -2], [-3, -10], [-8, -9], [-12, 3], [-5, 11], [-35, 9], [-17, 1], [-37, -2], [-12, 0], [-14, -2], [-11, -8], [15, -5], [5, -8], [0, -6], [-3, -2], [-3, -5], [16, -6], [1, -9], [-13, 0], [-11, 2], [-41, 24], [-26, 11], [-12, 9], [-27, 2], [-20, 1], [-23, -4], [9, -5], [5, -6], [-15, -3], [-18, -10], [-12, -9], [-7, -2], [-9, -5], [39, -11], [5, -5], [2, -7], [-12, -4], [-29, -2], [-51, 8], [-22, 0], [-7, 6], [-12, -1], [-7, -9], [-8, -8], [-12, -6], [3, -8], [10, -2], [-7, -4], [-16, -3], [-11, -3], [24, -3], [4, -3], [1, -4], [-37, -3], [-24, 0], [-15, 3], [-13, -2], [-8, -5], [-3, -7], [3, -9], [4, -6], [4, -2], [4, -6], [-22, -13], [-3, -3], [-1, -6], [10, -6], [8, -8], [-11, -5], [-13, -8], [13, -2], [23, 0], [24, 1], [37, 7], [11, 2], [4, -3], [4, -5], [-10, -4], [-67, -13], [-12, -5], [17, -3], [35, 0], [13, -4], [-8, -5], [-13, -4], [-14, -10], [12, -4], [37, -5], [67, -8], [50, -3], [-11, 9], [-2, 11], [35, 9], [18, 3], [83, 5], [23, 0], [18, -2], [-7, -5], [-19, 2], [-33, -3], [-52, -10], [-9, -4], [3, -8], [44, -6], [14, -5], [-19, -15], [3, -9], [22, -11], [30, -11], [14, -8], [22, -5], [36, -11], [20, -11], [6, -26], [25, -18]], [[3023, 3833], [-3, 0], [-3, 1], [-1, 0], [-7, -4], [-6, -3], [-2, -2], [1, -1], [26, 2], [4, 1], [1, 1], [-3, 2], [-7, 3]], [[2602, 9951], [6, 1], [1, 2], [-5, 0], [-6, -2], [0, -1], [4, 0]], [[1896, 9606], [-2, 1], [-2, 0], [3, -4], [3, -4], [2, -1], [-2, 5], [-2, 3]], [[7944, 5108], [-33, 16]], [[7827, 5148], [-7, 0], [-3, 2], [-9, 4], [-15, 2], [-19, 10], [-23, 17], [-16, 7], [-8, -3], [-6, 0], [-5, 3], [-14, 2], [-35, 0]], [[7560, 5233], [48, -21], [17, -9], [5, -6]], [[7000, 5252], [59, 29]], [[7059, 5281], [0, 1]], [[7230, 5265], [-140, 13]], [[7072, 5366], [-1, 7], [6, 8], [13, 11]], [[7090, 5392], [17, 8], [22, 6]], [[7129, 5406], [19, 2], [24, 0]], [[7395, 5437], [-103, 2], [-22, -7]], [[7507, 5455], [-49, -22], [-20, -7], [-13, 1]], [[6484, 4691], [19, 22], [5, 11]], [[6508, 4724], [-2, 6], [6, 20]], [[6512, 4750], [14, 36], [5, 23], [-6, 11], [0, 8], [8, 7]], [[6866, 5113], [-3, -5], [-5, -3], [-8, -1], [-4, -5], [1, -7], [-8, -10], [-18, -13], [-13, -12], [-9, -12], [-23, -15], [-57, -26]], [[7000, 5298], [0, -46]], [[7000, 5252], [-10, -13], [-14, -9], [-33, -14]], [[7844, 5363], [-13, 26], [-4, 22], [-6, 9], [-11, 5], [-2, 8], [11, 19]], [[8121, 6195], [13, 16], [16, 11], [23, 10], [21, 16], [19, 22], [9, 16], [-1, 11], [6, 9], [15, 7], [13, 12], [12, 15], [31, 7], [51, 0], [31, 2], [12, 4], [11, 8], [10, 12], [14, 5], [27, -4]], [[7187, 6964], [7, -40], [13, -35], [22, -44], [16, -27], [10, -8], [14, -4], [25, 4]], [[6762, 7204], [-30, -6], [-18, -6], [-21, -10], [-31, -8], [-40, -5], [-29, -7], [-18, -9], [-11, -11], [-2, -13], [-18, -9], [-40, -9], [-8, 0], [-6, 0], [-11, 2]], [[6479, 7113], [-5, 6], [0, 12], [-2, 7], [-6, 3], [-9, 4], [-11, 3], [-12, -2], [-13, -6], [-14, -2], [-17, 4], [-16, 6], [-12, 11], [-8, 3], [-5, 3], [-8, 1], [-9, -1], [-9, -4], [-15, -3], [-20, -1], [-21, -8], [-22, -14], [-17, -13]], [[6228, 7122], [-11, -13], [-10, -7], [-9, -3], [-10, -2], [-11, -1], [-13, 2], [-15, 4], [-14, 0], [-13, -3], [-15, 3], [-15, 8], [-13, 5], [-9, 1], [-14, -9], [-19, -19], [-21, -13], [-22, -7], [-14, -12], [-7, -16], [-7, -9], [-5, -2], [-7, 1], [-9, 1], [-10, 2], [-14, -3], [-13, -10], [-7, -4]], [[5901, 7016], [-17, -1], [-12, 3], [-13, 5], [-15, 4], [-18, 1], [-24, 5], [-29, 7], [-21, 4], [-15, -2], [-9, -4]], [[5728, 7038], [-1, -4], [-3, -4], [-4, -3], [-21, -7], [-14, -2], [-11, 2], [-19, 0], [-27, -2], [-21, -8], [-16, -15], [-25, -7], [-35, -1], [-27, -6]], [[5504, 6981], [-19, -13], [-17, -9], [-15, -6], [-10, -1], [-17, 6], [-38, 14], [-12, 4], [-4, 1], [-4, -2], [-4, -7], [-14, -6], [-23, -2], [-22, -7], [-22, -10], [-12, -5], [-8, 1], [-3, 4], [-6, 6], [-9, 6], [-24, 5], [-18, 2], [-11, -2], [-14, 1], [-15, 2], [-26, 8], [-36, 14], [-24, 12], [-17, 13], [-31, 19]], [[5029, 7029], [-16, 9], [-23, 17], [-16, 14], [-16, 10], [-15, 4], [-9, 4], [-3, 5], [-11, 3], [-29, -1], [-20, 11], [-11, 3], [-9, -2], [-8, 1], [-8, 4], [-8, -2], [-9, -8], [-9, 0], [-10, 6], [-10, 4], [-8, 1], [-6, -5], [-4, -12], [-6, -7], [-9, -4], [-14, -2], [-17, -1], [-22, 3], [-26, 8], [-18, 0], [-10, -9], [-17, -8], [-23, -6], [-16, -9], [-12, -19], [0, -13], [-2, -6], [-6, -4], [-16, -1], [-24, 3], [-47, -4], [-70, -11], [-38, -11], [-7, -11], [-2, -11], [1, -13], [-3, -10], [-7, -7], [-1, -7], [7, -5], [-3, -7], [-14, -8], [-19, -4], [-23, 1], [-14, 5], [-3, 5]], [[4290, 6922], [-7, 8], [-11, 7], [-11, 4], [-4, 6], [-6, 12], [-8, 4], [-6, 3], [-6, 0]], [[4231, 6966], [-6, -4], [-9, -2]], [[4216, 6960], [-6, 1]], [[4210, 6961], [-4, -1], [-49, -9], [-23, -3], [-11, 3], [-8, 6], [-13, 3], [-18, 1], [-16, 4], [-15, 7], [-9, 12], [-4, 16], [-5, 10], [-5, 4], [-18, -2], [-30, -9], [-24, -4], [-26, 2], [-39, 3], [-21, -1], [-15, -4], [-14, -6], [-12, -9], [-4, -14], [3, -22], [-1, -12], [-4, -3], [-7, -1], [-9, 2], [-4, -1], [0, -6], [-3, -5], [-7, -4], [-4, -7], [-4, -11]], [[6697, 5488], [-14, 47]], [[6683, 5535], [2, 12], [6, 9]], [[6691, 5556], [11, 12], [7, 11], [1, 9], [12, 10], [22, 11], [15, 14], [10, 16]], [[6769, 5639], [10, 10], [10, 5], [5, 7], [-1, 8], [4, 6]], [[6797, 5675], [8, 3], [7, 5], [8, 7], [16, 7], [26, 6], [16, 7], [7, 10], [3, 7], [-1, 3], [2, 4], [6, 3], [5, 9], [4, 15], [11, 13], [17, 10]], [[6932, 5784], [14, 2], [12, 3]], [[6958, 5789], [6, -1], [8, 3], [11, 15], [20, 39], [5, 28], [4, 10], [6, 1], [2, 11]], [[7020, 5895], [-1, 19], [9, 21]], [[7028, 5935], [19, 21], [10, 17], [0, 13], [4, 12], [12, 18]], [[7073, 6016], [-12, 2], [-7, 6], [-5, 10], [-1, 12], [4, 13], [0, 15], [-5, 19], [-1, 13], [3, 7], [-1, 7], [-8, 11], [7, 10], [-1, 9], [-6, 9], [1, 15], [9, 19], [5, 17]], [[7055, 6210], [1, 22], [1, 11]], [[7057, 6243], [6, 12], [14, 19], [8, 14], [1, 9], [8, 15]], [[7094, 6312], [23, 32]], [[7117, 6344], [13, 32], [15, 21], [21, 21], [18, 13], [13, 6]], [[7197, 6437], [16, 15], [19, 22], [13, 20]], [[7245, 6494], [8, 17], [5, 15], [2, 14], [-6, 12], [-17, 11], [-1, 14], [15, 19], [6, 17], [-2, 17], [19, 16], [39, 15], [22, 12], [4, 12], [6, 8], [8, 4], [8, 2], [8, 0], [3, 5], [-3, 10], [3, 7], [10, 3], [3, 5], [-3, 6], [2, 5], [7, 2], [2, 6], [-5, 10], [3, 7], [11, 6], [3, 5], [-5, 6], [-5, 3]], [[7395, 6785], [-7, 1], [-5, 4]], [[7383, 6790], [-6, 7], [-9, 2], [-13, -1], [-13, 1], [-17, 5]], [[7325, 6804], [-31, 6]], [[7073, 6016], [13, 24], [14, 13], [19, 11]], [[7119, 6064], [8, 8], [-2, 5], [1, 6], [8, 12], [3, 3], [5, 1], [8, -2], [4, 4], [3, 9], [-1, 8], [-5, 6], [1, 13], [6, 20], [0, 11], [-4, 2], [-2, 7], [1, 10], [-2, 11], [-5, 12], [-1, 24], [4, 35], [-2, 26], [-6, 16], [-6, 8], [-6, 1], [-5, 3], [-4, 6], [-3, 5], [0, 10]], [[5174, 7667], [11, 17], [0, 7], [-6, 5], [-3, 5], [1, 6], [-3, 25], [5, 14], [12, 12], [17, 1], [21, -9], [15, -2], [8, 5], [6, -3], [4, -10], [7, -8], [11, -7], [25, -10], [39, -11], [29, -6], [18, 1], [13, 3], [8, 5], [9, 3], [11, -1], [10, 3], [11, 5], [15, 2], [19, -2], [12, 1], [8, 3], [13, 0], [8, 3], [7, 4], [22, 3], [39, 1], [23, -1], [7, -3], [6, -11], [7, -18], [-8, -21], [-20, -24], [-12, -19], [-1, -14], [-9, -13], [-14, -11], [-8, -7], [-2, -4], [3, -5], [5, -6], [-6, -11], [-18, -15], [-15, -26], [-11, -38], [-18, -32], [-25, -25], [-17, -34], [-10, -43], [-3, -27], [5, -17], [-8, -23], [-2, -17], [3, -19], [0, -14], [-3, -13]], [[4108, 3287], [16, 8], [0, 10], [-16, 20], [-9, 6], [-4, 8], [-1, 11], [-9, 10], [-18, 7], [-13, 10], [-9, 10], [-10, 7], [-11, 3], [-13, 1], [-14, -2], [-10, 2], [-9, 7], [-15, 6], [-14, 3], [-18, 1], [-14, 2], [-8, 5], [-8, 9], [-7, 13], [-10, 8], [-12, 3], [-7, 6], [-2, 11], [-6, 9], [-9, 9], [-7, 4], [-3, 1]], [[3352, 7508], [0, -20], [4, -9], [12, -6], [23, -2], [17, 1], [18, 3], [14, -2], [8, -9], [25, -8], [39, -7], [23, -10], [6, -13], [10, -7], [14, -1], [8, -6], [2, -11], [11, -8], [20, -6], [11, -9], [1, -11], [5, -7], [7, -3], [9, -1], [9, 2], [8, 0], [7, -2], [7, -4], [6, -6], [14, -6], [22, -8], [18, -4], [17, -1], [11, -2], [9, -6], [16, -6], [11, -1], [12, 1], [10, -2], [7, -5], [12, 0], [15, 4], [11, 0], [8, -4], [9, -1], [10, 3], [11, -2], [13, -7], [12, 0], [10, 7], [9, 1], [7, -4], [9, 1], [11, 4], [10, -3], [9, -11], [12, -7], [15, -3], [12, -6], [7, -7], [11, -3], [13, 0], [6, -3], [-2, -7], [1, -5], [4, -2], [6, 0], [14, 2], [22, 3], [14, -4], [13, -9], [13, -6], [13, -2], [11, -5], [10, -6], [12, -5], [14, -2], [12, -1], [12, 2], [15, -2], [16, -7], [11, -2], [5, 1], [8, 9], [12, 17], [13, 6], [14, -4], [14, 0], [11, 3], [9, -1]], [[4111, 2781], [18, -3], [12, 5], [15, 17]], [[4157, 2800], [12, 32], [10, 12], [11, 3], [6, 5], [1, 7], [6, 7], [8, 8], [8, 5], [8, 2], [23, 1], [40, -1], [26, -2], [14, -2], [26, -16], [36, -31], [17, -20], [-3, -9], [0, -6], [5, -4], [21, -10], [10, -9], [8, -12], [12, -4], [14, 2], [17, -1], [19, -4], [44, 2], [107, 12], [7, 0], [6, -3], [6, -6], [9, -4], [13, -1], [27, 1], [51, 4], [4, 0], [5, 3], [0, 2], [-1, 5], [3, 3], [13, 6], [11, 4], [24, 6], [18, 6], [12, 8], [15, 4], [20, 1], [15, 2], [10, 4], [19, 0], [43, -5]], [[5004, 3283], [53, -3], [30, -4], [23, -5], [34, -12], [45, -19], [31, -10], [17, -2], [18, -5], [17, -8], [25, -5], [32, -2], [25, -9], [30, -23], [4, -3], [4, -3]], [[4242, 3654], [11, -32], [9, -14], [13, -10], [30, -5], [14, -11], [10, -18], [3, -13], [-3, -6], [0, -9], [3, -11], [-3, -9], [-8, -8], [-5, -11], [-3, -16], [1, -10], [6, -4], [8, -5], [11, -4], [23, -3], [34, -2], [21, -5], [9, -9], [21, -6], [32, -4], [20, -5], [7, -5], [26, -4], [44, -5], [26, -6], [7, -9], [-2, -8], [-12, -9], [-5, -7], [1, -5], [5, -4], [9, -1], [10, -6], [13, -10], [20, -3], [27, 2], [17, -4], [8, -10], [15, -8], [33, -8], [15, -5], [7, -5], [4, -7], [8, -4], [13, -1], [52, -2], [25, -2], [19, -4], [15, -1], [9, 2], [11, 0], [14, -4], [21, 0], [42, 2]], [[6484, 5791], [36, 9], [15, 6], [6, 7], [10, 1], [14, -3], [10, 0], [11, 3], [33, 18], [21, 26], [20, 40], [15, 24], [11, 9], [5, 7], [0, 5], [11, 20], [23, 34], [6, 23], [-9, 10], [-4, 7], [2, 3], [-7, 10], [-17, 17], [-13, 9], [-8, 4], [-8, 8], [-9, 12], [-10, 8], [-10, 5], [-7, 8], [-3, 10], [5, 12], [17, 20]], [[4662, 3958], [18, -29], [5, -14], [-2, -8], [1, -17], [5, -27], [18, -31], [28, -33], [14, -20], [0, -6], [-3, -5], [-4, -2], [-2, -5], [0, -8], [3, -10], [6, -13], [8, -12], [12, -12], [8, -14], [5, -14], [-2, -31], [-8, -46], [-12, -31], [-17, -13], [-5, -20], [5, -25], [0, -18], [-5, -9], [0, -11], [5, -11], [0, -8], [-5, -3], [7, -5], [19, -8], [15, -8], [12, -10], [13, -5], [13, 0], [9, -6], [7, -13], [14, -8], [23, -4], [12, -8], [3, -12], [-2, -7], [-8, -2], [-6, -5], [-5, -7], [5, -6], [15, -4], [9, -5], [5, -5], [12, -7], [19, -9], [26, -9], [49, -16]], [[7935, 5036], [21, 11], [14, 11], [13, 13], [7, 9], [1, 5], [-2, 3], [-3, 0], [-5, 5], [-6, 9], [-10, 5], [-21, 1]], [[7911, 5124], [-10, 9], [-7, 3], [-9, 0], [-8, 3], [-8, 5], [-8, 0], [-8, -4], [-8, -1], [-6, 1], [-6, 3], [-6, 5]], [[7667, 5192], [-37, 5]], [[7560, 5233], [-36, 17]], [[7524, 5250], [-14, 9], [-5, 4], [-8, 0], [-11, -2], [-9, -1], [-6, 4], [-7, 1], [-10, 0], [-6, -4], [0, -4], [-7, -3], [-12, 0], [-8, -4], [-9, 0], [-10, 4], [-10, -1], [-16, -5], [-50, -1], [-14, -1], [-7, -8], [-4, -14], [-4, -7], [-5, 2], [-4, 8], [-2, 13], [-7, 3], [-10, -8], [-8, -3], [-5, 0], [-5, 5], [-7, 10], [0, 8], [4, 6], [-3, 3]], [[7245, 5264], [-15, 1]], [[7090, 5278], [-10, 6], [-8, 1], [-13, -4]], [[5177, 5365], [20, -5], [8, 3], [2, 7], [7, 5], [12, 3], [15, 6], [16, 10], [18, 14], [18, 20], [13, 24], [12, 42], [-2, 21], [1, 14], [5, 14], [1, 10], [-4, 5], [-9, 5], [-14, 3], [-11, 9], [-8, 16], [-29, 16], [-49, 15], [-29, 14], [-9, 14], [-14, 9], [-18, 4], [-10, 5], [-2, 5], [-18, 8], [-51, 17]], [[4811, 5518], [43, -30], [8, -10], [7, -3], [7, 1], [10, -4], [22, -12], [36, -26], [14, -13], [6, -8], [8, -6], [12, -2], [8, 2], [5, 5], [9, 4], [13, 4], [15, -3], [18, -9], [15, -3], [13, 3], [14, -7], [14, -18], [11, -10], [10, -4], [6, -6], [4, -8], [6, -4], [6, 0], [5, 4], [3, 7], [6, 3], [13, 0]], [[4995, 6114], [-1, 9]], [[4994, 6123], [-7, 9], [-4, 1], [-4, -2], [-3, -2], [-3, 2], [-1, 4], [2, 7], [-4, 7], [-11, 7], [-5, 8], [-1, 8], [-2, 5], [-5, 2], [-2, 5], [-1, 7], [3, 7], [7, 9], [-1, 14], [-10, 19], [-5, 13], [-1, 9], [1, 6], [6, 6]], [[7642, 5229], [26, -3], [32, 0], [13, 1], [6, 2], [2, 2], [-2, 3], [3, 4], [6, 6], [11, 4], [15, 1], [11, 6], [6, 13], [6, 8], [7, 3], [2, 5], [-3, 5], [2, 6], [8, 7], [7, 11], [6, 15], [10, 10], [14, 6], [9, 7], [5, 12]], [[4362, 7238], [2, -7], [0, -16], [16, -16], [30, -16], [30, -7], [31, 3], [35, 5], [38, 8], [32, 3], [27, -1], [21, -4], [15, -6], [23, -6], [32, -5], [48, 3], [63, 12], [54, -1], [47, -14], [30, -6], [14, 4], [11, -4], [11, -16], [-5, -8], [7, -14], [16, -22], [8, -14], [0, -6], [4, -4], [10, -3], [3, -5], [-2, -6], [4, -9], [11, -11], [4, -9], [-3, -11]], [[8084, 5442], [-18, 20], [-4, 10], [7, 9], [5, 9], [-3, 2], [-9, -1], [-7, 5], [-6, 11], [-2, 7], [3, 4], [11, 7], [11, 1], [18, -1], [9, 3], [10, 6], [3, 9], [4, 3], [9, 3], [10, 7], [11, 11], [10, 7], [8, 1], [10, 8], [11, 14], [21, 11], [29, 7], [24, 0], [19, -7], [18, 2], [18, 9], [16, 5], [19, 1], [31, 3], [14, 4], [7, 6], [9, 2], [11, -2], [20, 8], [27, 18], [28, 7], [28, -1], [22, 0], [16, 4], [10, 4], [4, 4], [17, 3], [47, 5], [16, -9], [11, -2], [13, 3], [10, 6], [9, 10], [11, 4], [11, 0], [9, -2]], [[5847, 5838], [4, 17], [0, 13], [-5, 22], [5, 6], [2, 17], [1, 29], [3, 19], [5, 8], [4, 31], [3, 53], [7, 33], [12, 13], [7, 14], [1, 16], [4, 12], [6, 7], [4, 9], [1, 10], [-2, 5], [-5, 3], [-2, 10], [3, 28]], [[4943, 6274], [12, 15], [2, 11], [-4, 12], [0, 10], [2, 7], [-4, 8]], [[4951, 6337], [-11, 11], [-1, 8], [9, 6], [18, 1], [28, -4], [18, 0], [7, 3], [4, 6], [1, 9], [4, 4], [6, 1], [8, 5], [11, 9], [13, 5], [16, 0], [12, 5], [7, 9], [10, 5], [13, 3], [7, 4], [1, 6], [5, 5], [11, 5], [6, 6], [1, 6], [9, 6], [17, 5], [13, 1], [10, -1], [3, 1], [-2, 5], [2, 5], [8, 3], [8, 8], [13, 18], [14, -3], [16, 4], [35, 15], [3, 9], [-5, 15], [-11, 24], [0, 24], [12, 24], [10, 12], [10, 2], [10, 6], [10, 12], [12, 6], [15, 1], [9, 3], [3, 5], [0, 4], [-3, 4], [2, 7], [9, 8], [5, 10], [3, 9], [7, 5], [8, 1], [5, 0], [5, 0], [22, -2], [8, 3], [3, 7], [6, 6], [11, 6], [5, 6], [0, 5], [3, 3], [6, 0], [1, 2], [-3, 2], [11, 2], [23, 1], [14, 5], [4, 9], [20, 9], [36, 9], [19, 8], [1, 8], [11, 9], [17, 6], [10, 3], [5, 3], [5, 3], [4, 5], [15, 13], [12, 7], [10, -1], [9, 2], [11, 5], [5, 6], [0, 8], [2, 5], [4, 8], [14, 17], [13, 7], [7, 0], [6, 0], [5, 0], [3, 1], [4, 2], [5, 2], [9, 7], [9, 10], [5, 12], [10, 10], [17, 8], [8, 8], [-1, 8], [13, 13], [44, 27]], [[3976, 6086], [10, 12], [0, 11], [-6, 13], [3, 10], [13, 6], [13, 3], [23, -2], [24, -1], [10, -4], [5, -5], [14, -8], [26, -13], [19, -12], [13, -12], [12, -7], [12, -2], [11, -5], [8, -9], [15, -8], [22, -7], [17, -4], [12, -1], [8, 2], [3, 2], [6, 0], [13, -2], [36, -7], [28, -1], [17, 6], [9, 5], [7, 0], [5, -1], [6, -2], [5, 1], [2, 3], [4, 0], [5, -3], [7, 0], [6, 4], [8, 0], [8, -2], [13, 1], [26, 6], [10, 6], [8, 3], [7, -1], [7, 4], [6, 10], [8, 6], [11, 5], [6, 6], [2, 7], [9, 5], [16, 3], [10, 9], [6, 13], [8, 7], [10, 2], [9, 5], [8, 9], [7, 6], [8, 1], [10, -2], [12, -5], [10, 0], [10, 4], [8, 5], [6, 7], [10, 4], [21, 2], [2, 5], [7, 2], [11, 0], [5, 2], [2, 4], [6, -1], [11, 2], [15, 8], [12, 4], [10, 0], [6, 3], [4, 4], [4, 1], [5, 1], [9, 6], [8, 0], [6, -1], [5, 3], [2, 4], [1, 4], [-5, 1], [2, 2], [7, 3], [7, 0], [8, -1], [4, 3], [0, 6], [4, 3], [7, 0], [6, 3], [2, 7], [4, 4], [6, -1], [13, 5], [30, 14]], [[3356, 7572], [32, -5], [15, 0], [11, 6], [44, 18], [14, 8], [0, 5], [5, 5], [9, 6], [3, 5], [-1, 5], [9, 13], [20, 22], [12, 21], [4, 22], [15, 29], [27, 35], [16, 26], [6, 16], [-1, 9], [-6, 0], [1, 15], [13, 45], [2, 36], [5, 19], [7, 13], [4, 13], [-2, 11], [2, 11], [7, 12], [3, 12], [-1, 11], [7, 11], [14, 10], [7, 11], [0, 11], [10, 10], [19, 9], [18, 10], [16, 12], [6, 13], [-3, 12], [2, 20], [9, 28], [2, 20], [-4, 14], [1, 10], [9, 9]], [[3743, 8226], [4, 18], [-2, 22], [-9, 31], [-14, 17], [-20, 4], [-20, 8], [-20, 11], [-13, 5], [-8, -1], [-7, 3], [-7, 8], [-8, 4], [-11, 3], [-6, 2], [-2, 6], [2, 10], [-3, 11], [-7, 12], [-1, 11], [5, 8], [-2, 8], [-10, 7], [2, 15], [16, 23], [8, 20], [0, 17], [-5, 14], [-12, 14]], [[5048, 5698], [-9, 7], [-6, 13], [-9, 22], [-9, 15], [-9, 9], [-6, 8], [-3, 8], [0, 6], [3, 5], [-3, 9], [-9, 15], [-1, 4], [3, 2], [6, 8], [7, 18], [0, 13], [-6, 7], [-3, 6], [1, 5], [-4, 7], [-9, 9], [-6, 5], [-5, 0], [-2, 4], [2, 6], [-5, 8], [-10, 9], [0, 17], [11, 25], [5, 10], [1, 6], [7, 4], [3, 6], [2, 3], [6, 3], [4, 6], [2, 10], [-5, 16], [-12, 20], [-5, 12], [3, 3], [0, 2], [-3, 3], [0, 2], [4, 2], [0, 4], [-4, 7], [2, 4], [5, 2], [1, 3], [-2, 6], [2, 4], [8, 3], [4, 5]], [[3340, 6324], [-13, 29], [-8, 18], [0, 11], [5, 11], [-15, 15], [-34, 19], [-22, 18], [-9, 16], [-8, 10], [-7, 2], [-6, 8], [-7, 13], [-7, 9], [-8, 6], [-4, 11], [-1, 15], [-13, 30], [-28, 44], [-14, 29], [-1, 13], [-6, 15], [-11, 14], [-12, 11], [-12, 5], [-10, 9], [-11, 15], [-17, 7], [-10, 7], [-11, 11], [2, 16], [14, 21], [11, 13], [7, 3], [3, 6], [0, 10], [4, 7], [7, 2], [2, 6], [-3, 10], [1, 6], [5, 4], [2, 6], [0, 7], [4, 5], [9, 1], [5, 4], [1, 6], [5, 2], [11, -1], [19, 5], [27, 11], [20, 6], [13, 0], [9, -2], [6, -3], [3, -5], [0, -6], [3, -3], [7, -1], [1, -2], [-2, -1], [1, -3], [5, -5], [3, 0], [3, 4], [13, -2], [24, -8], [24, -4], [25, 0], [13, -2], [2, -3], [6, -3], [10, -3], [13, -1], [16, 1], [10, -2], [5, -5], [42, -5], [17, 0], [5, 2], [5, -1], [3, -3], [10, 6], [16, 15], [27, 9], [37, 3], [22, 6], [7, 10], [11, 7], [14, 5], [11, 0], [8, -7], [11, -6], [14, -5], [10, 2], [4, 8], [6, 6], [7, 2], [10, 1], [13, -2], [11, -4], [8, -7], [15, 2], [34, 14]], [[4266, 3975], [13, -11], [11, -2], [11, 3], [8, 5], [4, 8], [1, 6], [-2, 4], [4, 8], [12, 17], [16, 10], [10, 9], [9, 12], [4, 13], [0, 16], [1, 2]], [[3732, 7859], [-5, -6], [-1, -7], [1, -10], [3, -9], [8, -11], [13, -7], [3, -5], [-2, -7], [23, -6], [49, -6], [28, -1], [8, 6], [7, 9], [6, 12], [7, 7], [13, 4], [38, 2], [15, 2], [5, 4], [9, 3], [12, 2], [11, 0], [10, -3], [12, 3], [15, 10], [28, 11], [42, 12], [28, 10], [12, 6], [9, 8], [6, 9], [13, 10], [18, 11], [11, 7], [2, 5], [22, 9], [41, 13], [29, 16], [18, 21], [19, 11], [23, 1], [15, 2], [10, 5], [11, 3]], [[4377, 8015], [7, -1], [6, -4], [10, 1], [15, 8], [22, 1], [29, -4], [38, 0], [47, 4], [29, 5], [11, 5], [19, -1]], [[4610, 8029], [43, -9]], [[4569, 3265], [37, -8], [22, -2], [21, 1], [20, -2], [19, -4], [28, -1], [36, 3], [29, 0], [22, -1], [22, -4], [20, -6], [22, -4], [23, -2], [20, -7], [15, -13], [13, -15], [9, -15], [19, -10], [28, -3], [20, -5], [11, -7], [13, -5], [16, -4], [17, -2], [18, 1], [29, -10], [38, -20], [24, -10], [9, -2], [14, -7], [27, -18], [30, -4], [21, -8], [24, -13], [9, -11]], [[4237, 7584], [53, 6], [41, 5], [22, 5], [18, 6], [21, 1], [25, -5], [21, -2], [17, 1], [16, 5], [14, 10], [15, 2], [16, -6], [15, 5], [15, 17], [9, 8], [3, -2], [3, 2], [5, 6], [11, 7], [14, 7]], [[4591, 7662], [13, 4], [15, -4], [6, 0], [4, -3], [4, -8], [4, -2], [7, -3], [14, -9], [11, -9], [9, -5], [6, -4], [2, -6], [-3, -6], [7, -14], [5, -4], [3, -5], [-3, -6], [6, -6]], [[4701, 7572], [15, -27]], [[4716, 7545], [0, -9], [4, -4], [4, -11], [6, -10], [-1, -8], [3, -7]], [[4732, 7496], [2, -21], [-3, -9], [-6, -3], [-12, -2], [-19, 0], [-13, -12], [-13, -39], [6, -26], [11, -15], [18, -14], [13, -6], [9, 1], [21, -5], [34, -13], [23, -4], [13, 4], [16, 2], [20, 0], [13, 2], [6, 3], [9, 1], [9, -3], [13, -1], [17, 1], [17, -3], [26, -8], [37, -6], [29, 0], [53, 9], [40, 6], [25, 1], [25, -2], [24, -7], [22, -11], [24, -10], [41, -12], [21, -18], [21, -12], [42, -17], [72, -24], [7, -2]], [[5445, 7221], [12, 1], [11, -2], [14, -5]], [[5482, 7215], [12, -8], [10, -12], [4, -10], [1, -8], [11, -12], [23, -16], [8, -8], [7, -7], [7, -15], [20, -20], [47, -39], [17, -11], [13, -4], [15, 0], [14, -3], [15, -5], [11, -2], [11, 3]], [[4171, 3523], [14, -40], [8, -18], [0, -7], [-5, -4], [-2, -5], [-1, -5], [4, -5], [7, -3], [16, -2], [27, -1], [18, -4], [10, -6], [7, -6], [6, -8], [7, -5], [8, -1], [7, -10], [4, -19], [7, -14], [9, -9], [6, -9], [2, -9], [12, -11], [22, -10], [18, -6], [14, -1], [18, 6], [20, 12], [16, 6], [11, 1], [24, -9], [36, -18], [25, -16], [23, -22]], [[5223, 7618], [-29, -7], [-13, -6], [-7, -7], [-10, -3], [-20, -1], [-41, 12], [-19, 3], [-10, -2], [-29, 5], [-49, 14], [-25, 14], [0, 14], [-7, 10], [-11, 7], [-10, 9], [-7, 10], [-6, 6], [-7, 2], [-11, 1], [-23, -1], [-24, 3], [-10, -4], [-3, -8], [-14, -3], [-23, 2], [-17, 5], [-13, 7], [-8, 8], [-3, 9], [-10, 11], [-22, 19], [-20, 11], [-8, 9], [-2, 11], [-15, 5], [-26, 0], [-26, 4], [-36, 11]], [[4609, 7798], [-7, 12], [-2, 9], [0, 10], [-3, 8], [-5, 5], [0, 17], [5, 28], [2, 21], [-3, 14], [5, 11], [14, 8], [7, 12], [2, 16], [8, 13], [14, 10], [5, 10], [-5, 10]], [[4646, 8012], [7, 8]], [[4653, 8020], [4, 4], [25, 16], [15, 18], [8, 31], [-1, 19], [16, 12], [32, 12], [24, 14], [15, 15], [23, 10], [32, 3], [23, 6], [21, 13], [18, 5], [18, 1], [22, -2], [20, -2], [16, -4], [15, -5], [11, -7], [3, -3], [3, -1], [2, 0], [2, 0], [3, 0], [7, 2], [6, 2], [22, 5], [12, 7], [-5, 7], [-1, 6], [2, 3], [8, 3], [12, 0], [9, 3], [6, 4], [10, -1], [9, -6], [6, -2], [4, -1], [5, 0], [8, 0], [10, 4], [11, 8], [17, 5], [24, 1], [23, 5], [22, 8], [21, 4], [21, -2], [23, 5], [25, 12], [27, 11], [28, 8], [11, 1]], [[5406, 8277], [9, 1], [13, -5], [17, -2], [33, 2], [4, 0]], [[5482, 8273], [32, 18], [43, 18]], [[5419, 8319], [-2, -7], [2, -5], [5, -3], [-2, -7], [-14, -16], [-2, -4]], [[5466, 8381], [-1, -3], [-15, -33], [-11, -14], [-17, -10], [-3, -2]], [[5385, 8398], [-1, -4], [-2, -3], [-13, -17], [4, -15], [15, -18], [14, -12], [12, -5], [5, -5]], [[7849, 4942], [-65, -28], [-14, -4], [-15, -1], [-20, 1], [-12, -2], [-4, -2], [-4, -1], [-4, 2], [2, 7], [9, 12], [11, 8], [14, 3], [14, 6], [15, 9], [17, 6], [19, 4], [21, 9], [21, 15], [22, 9], [22, 2], [8, 0], [9, -1], [10, 2], [23, 8], [15, 1], [7, -6], [8, -2], [8, 3], [7, 0], [8, -2], [19, 6], [32, 15], [29, 11], [40, 10]], [[8122, 5042], [123, 40]], [[8245, 5082], [41, 12], [20, 2], [11, -4], [13, -1], [15, 1], [16, -3], [18, -7], [18, 0], [28, 11]], [[7669, 5350], [-36, 33], [7, 7], [0, 3], [-3, 1], [-1, 4], [3, 7], [-2, 9], [-7, 12], [-16, 10], [-24, 7], [-18, 3], [-11, -2], [-12, 5], [-12, 12], [-12, 3]], [[7525, 5464], [-18, -9]], [[7425, 5427], [-30, 10]], [[7270, 5432], [-11, -1], [-9, -7], [-13, -12], [-12, -2], [-10, 8], [-9, 1], [-8, -6], [-10, -3], [-16, -2]], [[5831, 3810], [14, -2], [21, -7], [28, -11], [21, -5], [23, 5]], [[5658, 3876], [15, -9], [62, -29], [43, -16], [27, -2], [11, -4], [6, -4], [9, -2]], [[5658, 3876], [40, -8], [23, -2], [18, 2], [23, -7], [27, -15], [21, -15], [21, -21]], [[5658, 3876], [0, 0]], [[5913, 4493], [-30, -13], [-9, -8], [4, -10], [4, -12], [-1, -11], [-5, -15], [-9, -11], [-15, -7], [-8, -16], [0, -24], [-5, -23], [-10, -22], [0, -4], [-2, -5], [-3, -3], [-4, -2], [-7, -3], [-19, -7], [-13, -8], [-5, -9], [-6, -17], [-6, -24], [-1, -18], [6, -11], [0, -12], [-5, -12], [-1, -11], [4, -9], [0, -11], [-1, -8], [-1, -7], [-2, -5], [-3, -5], [-9, -7], [-46, -47], [-34, -29], [-21, -12], [-19, -4], [-12, -7], [-6, -11], [0, -9]], [[5613, 4004], [4, -8], [0, -11], [-7, -21], [-4, -19], [1, -13], [7, -14], [10, -15], [12, -14], [22, -13]], [[6484, 4691], [-3, -60]], [[6481, 4631], [-7, -32], [-11, -24], [-10, -13], [-9, -1], [-7, -4], [-4, -6], [-7, -5], [-11, -4], [-6, -4], [0, -5], [-11, -3], [-19, -1], [-12, -4], [-3, -6], [-7, -5], [-10, -4], [-4, -6], [0, -8], [-5, -8], [-12, -6], [-11, 0], [-10, 6], [-13, 4], [-17, 1], [-13, -1], [-11, -5], [-10, -7], [-9, -11], [-9, -1], [-11, 7], [-13, 2], [-16, -3], [-13, 1], [-9, 4], [-15, 1], [-20, -4], [-40, 5], [-60, 12], [-51, 4], [-62, -4]], [[6601, 4912], [-17, -8]], [[6584, 4904], [-13, -17], [-7, -27], [-11, -16]], [[6553, 4844], [-20, -9]], [[6533, 4835], [28, 6], [11, 18], [11, 27], [8, 16], [10, 10]], [[6719, 5004], [-22, -19]], [[6697, 4985], [-22, -12], [-30, -11], [-20, -15]], [[6625, 4947], [-10, -21], [-14, -14]], [[6943, 5216], [-20, -12], [-8, -12], [-4, -14], [-11, -15], [-17, -14], [-7, -11], [1, -8], [-2, -7], [-9, -10]], [[7072, 5366], [-6, -7], [-6, -3], [-10, -1], [-10, -4], [-11, -8], [-7, -8], [-4, -7], [0, -5], [1, -1], [-3, -2], [-6, -1], [-6, -5]], [[7004, 5314], [-4, -16]], [[6417, 5861], [-51, -4], [-52, -21], [-30, -9], [-24, -4], [-20, -11], [-17, -16], [-9, -11], [0, -7], [8, -7], [15, -7], [9, -10], [2, -11], [8, -10], [12, -8], [4, -8], [-4, -7], [-2, -5], [2, -5], [7, -5], [13, -6], [9, -6], [4, -7], [-2, -6], [-9, -7], [-2, -6], [5, -6], [-3, -5], [-9, -6], [-15, -4], [-20, -3], [-15, -9], [-12, -14], [-9, -9], [-9, -4], [-5, -5], [-1, -7], [1, -6], [5, -8], [-29, -33]], [[6172, 5538], [-14, -14], [-13, -8], [-14, -15], [-17, -8], [-25, -7]], [[6089, 5486], [-9, -18], [7, -29], [-3, -18], [-13, -8], [-7, -10], [-3, -11], [-5, -7], [-9, -4], [-3, -5], [3, -6], [11, -4], [20, -3], [12, -3], [3, -3], [6, -14], [0, -6], [-1, -6], [-5, -7], [-9, -7], [-3, -5], [4, -2], [-2, -4], [-6, -7], [-9, -4], [-12, -3], [-6, -3], [0, -4], [-9, -8], [-18, -11], [-18, -10], [-19, -7], [-8, -5], [3, -3], [0, -2], [-3, -3], [5, -5], [12, -9], [6, -12], [1, -13], [2, -7], [5, 2], [4, -1], [4, -4], [-1, -3], [-5, -3], [0, -3], [5, -2], [1, -3], [-4, -4], [2, -3], [7, -2], [3, -8], [-1, -11], [-3, -9], [-6, -5], [0, -4], [3, -3], [-1, -6], [-7, -9], [-2, -10], [4, -9], [-1, -16], [-9, -31], [2, -14], [5, -7], [10, -7], [7, -11], [4, -14], [-4, -8], [-12, -4], [-1, -5], [11, -7], [5, -8], [0, -9], [11, -11], [22, -13], [12, -15], [3, -16], [7, -12], [13, -8], [3, -9], [-4, -10], [4, -10], [12, -11], [6, -6], [-3, -3], [3, -7], [8, -10], [5, -9], [1, -8], [-2, -7], [-6, -10], [0, -9], [-4, -6], [-9, -8], [-3, -6], [4, -5], [-1, -2], [-6, 0], [-8, -3], [-10, -7], [-9, -3], [-7, 0], [-9, -5], [-10, -9], [-2, -9]], [[6050, 4702], [8, -8], [4, -7], [-1, -7], [-8, -7], [-14, -7], [-5, -3], [0, -3], [-4, -6], [-11, -13], [-3, -6], [-1, -5], [-7, -4], [-20, -9], [-4, -5], [-1, -5], [-3, -2], [-3, -2], [-4, -4], [-3, -8], [0, -10], [3, -12], [-1, -4], [-4, -2], [-3, -3], [-1, -6], [-3, -4], [-7, -3], [-2, -3], [1, -3], [-5, -4], [-11, -3], [-7, -5], [-2, -6], [-7, -6], [-10, -5], [-3, -7], [5, -12]], [[4793, 5329], [8, 7], [2, 4], [-1, 4], [3, 3], [9, 3], [13, -4], [17, -12], [16, -5], [21, 3], [7, 10], [11, 6], [17, 7], [17, -2], [16, -12], [14, -8], [12, -3], [13, -2], [15, 1], [19, -18], [23, -37], [13, -28], [3, -19], [10, -11], [18, -3], [14, -12], [10, -21], [10, -12], [13, -3], [10, -8], [7, -12], [12, -6], [15, 0], [26, -7], [37, -13], [25, -11], [11, -7], [9, -10], [10, -18], [14, -26]], [[5312, 5047], [10, -12], [11, -6]], [[5333, 5029], [6, -4]], [[5339, 5025], [0, -3], [12, -7], [24, -9], [22, -18], [22, -27], [19, -20], [19, -13], [17, -9], [15, -6], [9, -4], [1, -3], [14, -7], [28, -11], [18, -10], [7, -11], [28, -10], [48, -12], [34, -5], [22, 1], [31, -9], [41, -18], [24, -13], [9, -7], [27, -12], [66, -23], [29, -6], [14, -5], [8, -7], [8, -2], [8, 2], [17, -2], [25, -8], [20, -9], [25, -20]], [[3939, 6226], [11, -6], [18, 0], [44, 8], [16, 12], [16, 5], [21, 5], [23, 8], [24, 12], [11, 8], [-1, 3], [2, 3], [8, 3], [5, 9], [4, 15], [12, 13], [20, 13], [11, 10], [2, 7], [5, 4], [9, 2], [7, 6], [4, 11], [9, 9], [15, 6], [8, 6], [1, 5], [9, 4], [18, 5], [15, 0], [13, -3], [8, 1], [5, 4], [7, 1], [15, 1], [6, -2], [3, 2], [4, 3], [4, 1], [7, 0], [10, 4], [14, 10], [19, 8], [25, 6], [13, 1], [5, -2], [3, -2], [6, -2], [8, 2], [8, -3], [11, -9], [6, -8], [1, -5], [4, -3], [6, 2], [4, -2], [3, -3], [9, 0], [16, 2], [11, 0], [5, -3], [11, 3], [26, 10], [58, 18], [20, 10], [1, 8], [-1, 3], [-3, 2], [-1, 2], [2, 5], [-1, 3], [-2, 2], [0, 2], [8, 5], [1, 4], [-2, 2], [1, 3], [2, 4], [11, 11], [5, 8], [-3, 6], [7, 8], [16, 10], [6, 5], [0, 2], [3, 2], [4, 0], [3, 2], [1, 4], [7, 3], [11, 2], [8, 0], [4, -3], [6, 1], [8, 6], [8, 1], [9, -2], [5, 1], [1, 4], [7, 4], [11, 3], [6, -1], [1, -4], [4, -2], [8, 1], [7, -2], [4, -5], [6, -2], [8, 0], [3, -3], [4, -1], [9, 2], [6, -1], [4, -2], [4, 0], [3, 2], [7, 0], [7, -2], [9, 1], [4, 3], [4, 2], [6, 0], [5, 1], [3, 4], [5, 1], [5, -1], [3, 2], [0, 2], [3, 1], [4, 0], [1, 2], [-1, 2], [2, 2], [9, 5], [11, 4], [5, 0], [4, -2], [2, 3], [0, 7], [4, 2], [4, 1], [9, -2], [6, 1], [2, 5], [3, 1], [3, 1], [9, -3], [11, 2], [15, 8], [6, 5], [-1, 2], [1, 2], [4, 1], [1, 1], [0, 1], [4, 2], [0, 3], [-8, 9], [1, 6], [7, 3], [3, 2], [-4, 3], [-1, 3], [2, 5], [4, 2], [9, 0], [4, 3], [2, 4], [5, 4], [10, 4], [7, 9], [5, 24], [-6, 24], [-8, 9], [-11, 4], [-2, 3], [5, 4], [3, 5], [0, 6], [4, 3], [6, 1], [2, 2], [-2, 5], [5, 2], [10, 1], [6, -1], [-1, -3], [3, -2], [6, 0], [4, 1], [3, 4], [8, 1], [13, 0], [10, -3], [6, -7], [10, -3], [12, 1], [4, -2], [0, -3], [1, -4], [5, -2], [7, 1], [9, 5], [7, 1], [7, -2], [11, 3], [16, 8], [5, 0], [2, -2], [3, -4], [7, -1], [3, 2], [-2, 9], [2, 5], [7, 1], [1, 4], [2, 6], [3, 1], [3, 2], [5, 0], [7, 1], [4, 4], [0, 12], [-2, 8], [-3, 2], [3, 3], [8, 5], [3, 5], [-2, 4], [4, 6], [7, 2], [6, 0], [6, 1], [2, 3], [2, 4], [5, 1], [6, 0], [5, 3], [2, 3], [-2, 3], [3, 3], [9, 4], [11, 1], [14, -2], [15, 6], [5, 3], [6, 5], [3, 5], [-4, 5], [3, 5], [4, 2], [10, 1], [7, 4], [3, 6], [9, 4], [16, 4], [16, 10], [18, 19], [5, 14], [-9, 17]], [[4876, 4820], [6, -22], [-5, -12], [4, -12], [10, -16], [16, -14], [21, -12], [11, -10], [0, -6], [6, -4], [13, -3], [12, 3], [12, 9], [11, 3], [10, -4], [7, -5], [4, -7], [10, -5], [15, -4], [18, 3], [19, 7], [14, 8], [8, 7], [8, 3], [8, -1], [12, -14], [16, -24], [7, -16], [0, -7], [8, -12], [17, -18], [6, -14], [-5, -8], [3, -14], [8, -20], [3, -17], [-5, -15], [0, -15], [3, -13], [6, -15], [10, -16], [4, -12], [-2, -15], [-2, -5], [7, -11], [13, -20], [16, -15], [19, -11], [11, -10], [1, -7], [7, -5], [12, -4], [13, -7], [15, -11], [12, -16], [10, -22], [17, -18], [26, -13], [22, -8], [21, -3], [18, 2], [16, 7], [11, 0], [8, -6], [20, -7], [33, -8], [31, -11], [29, -12], [17, -9], [4, -5], [1, -12], [-2, -17], [2, -10], [5, -4], [2, -9], [-2, -14], [2, -11], [5, -6], [2, -6], [-2, -4], [-8, -3], [-13, -3], [-8, -4], [-2, -10], [5, -15], [-3, -11], [-9, -12], [0, -6], [8, -2], [6, -9], [3, -17], [-1, -13], [-6, -9], [1, -7], [15, -8]], [[4368, 4075], [3, 1], [31, 1], [15, -3], [7, -6], [4, -7], [0, -7], [8, -4], [17, -1], [19, 2], [22, 4], [13, 0], [4, -4], [8, -6], [1, -9], [-2, -13], [3, -11], [8, -10], [2, -7], [1, -3], [7, -1], [19, 1], [17, -4], [13, -9], [20, -6], [25, -2], [17, -4], [12, -8]], [[4002, 2082], [89, 1], [35, -1], [13, -2], [12, 1], [13, 3], [25, 0], [40, -4], [22, 0], [5, 3], [7, 2], [14, 0], [32, 0], [16, 3], [9, 6], [20, 2], [32, -3], [22, 1], [18, 9], [5, 2]], [[7819, 5452], [3, 8], [3, 2], [4, 0], [5, 7], [4, 16], [6, 9], [8, 3], [3, 6], [-2, 8], [2, 13], [9, 18], [8, 13], [10, 10], [-7, 20], [-23, 30], [-8, 18], [5, 4], [2, 13], [-2, 35], [18, 3], [9, 4], [6, 7], [11, 6], [26, 9], [41, 27], [17, 15], [6, 13], [8, 14], [12, 14], [10, 18], [6, 23]], [[8019, 5838], [9, 17]], [[8028, 5855], [11, 12], [10, 21], [9, 29], [8, 18], [8, 6], [3, 15], [0, 26], [2, 17], [4, 8], [10, 11], [15, 13], [4, 15], [-7, 16], [-1, 12], [4, 7], [-2, 10], [-10, 12], [-4, 9], [3, 5], [-1, 8], [-5, 12], [1, 8], [7, 4], [9, 13], [15, 33]], [[8454, 6374], [21, -4], [17, 2], [20, 7]], [[8512, 6379], [13, 11], [7, 15], [8, 5]], [[8540, 6410], [9, 0], [10, 1]], [[8559, 6411], [11, 3], [8, 4], [6, 4], [3, 6], [0, 8], [7, 5], [14, 1], [7, 5], [1, 7], [12, 6], [22, 7]], [[8650, 6467], [17, -1], [10, -8]], [[8677, 6458], [23, -8], [37, -9]], [[8737, 6441], [18, -8], [6, -2], [7, 0]], [[8768, 6431], [5, 3], [6, 4]], [[8779, 6438], [5, 0], [3, -5], [0, -8], [7, -5], [14, -1], [6, -3], [-2, -6], [4, -11], [9, -15]], [[8825, 6384], [40, -20]], [[8865, 6364], [108, -38], [23, -6]], [[8996, 6320], [9, -6], [3, -8], [8, -7], [15, -7], [11, -4]], [[9042, 6288], [8, 0], [5, -4]], [[9055, 6284], [4, -8], [6, -4], [8, 0], [6, -2]], [[9079, 6270], [4, -6]], [[5905, 6213], [10, 32], [11, 15], [16, 9], [12, 17], [10, 27], [3, 23], [-4, 20], [-14, 14], [-23, 8], [-14, 11], [-5, 12], [1, 12], [6, 11], [12, 11], [18, 11], [4, 7], [-10, 2], [0, 16]], [[5938, 6471], [11, 28], [3, 21]], [[5952, 6520], [-3, 16], [3, 13], [10, 11], [5, 10], [0, 10], [4, 7], [9, 8]], [[5980, 6595], [-1, 8], [-12, 13], [-23, 20], [-10, 17], [3, 12], [9, 11]], [[5946, 6676], [15, 10], [18, 7], [20, 3], [19, 7], [16, 8], [11, 12], [4, 14], [23, 18], [42, 23], [22, 18], [4, 11], [7, 8], [10, 7], [11, 13], [10, 21], [9, 12], [8, 4], [10, 1], [12, -2], [11, 3], [10, 7], [7, 10], [2, 10], [16, 11], [21, 7], [9, 4], [23, 10], [15, 11], [16, 14], [25, 28], [23, 37], [12, 31], [10, 38], [12, 21], [15, 3], [13, 1], [22, -4]], [[7259, 5691], [-11, 17], [-13, 5], [-12, 9], [-17, 16], [-6, 14], [4, 11], [8, 9], [10, 5], [12, 11], [15, 17], [14, 11], [13, 3], [8, 9], [2, 15], [0, 10], [-3, 6], [1, 5], [5, 5], [25, 12], [12, 1], [8, -3], [8, -1], [8, 3], [23, 14], [6, 7], [-1, 7], [3, 3], [8, -1], [8, 2], [9, 6], [5, 8], [3, 10], [-4, 8], [-9, 5], [-1, 15], [4, 24]], [[7404, 5989], [-2, 21], [-15, 29], [7, 12], [0, 5], [-3, 2], [-1, 3], [1, 5], [-7, 6], [-16, 8], [-15, 22], [-17, 41], [-1, 11], [7, 7], [12, 8], [4, 14], [-7, 15], [2, 14], [12, 12], [3, 14], [-4, 15], [-1, 17], [2, 18], [5, 12], [7, 8], [2, 7], [-4, 5], [1, 5], [3, 3], [-1, 8], [-5, 12], [-2, 14], [3, 16], [10, 22], [18, 29], [8, 21], [-2, 12], [1, 9], [4, 6], [-1, 10], [-6, 15], [-1, 9], [3, 3], [7, 3], [11, 1], [7, 7], [3, 14], [12, 10], [19, 7], [11, 9], [3, 10], [6, 8], [9, 5], [4, 5]], [[7500, 6593], [-2, 6], [-5, 2], [-10, -2], [-8, 2], [-7, 7], [0, 3], [8, 2], [8, 6], [10, 9], [9, 24], [10, 40], [1, 37], [-7, 33], [-6, 20], [-5, 6], [-13, 6], [-21, 5], [-13, 6], [-5, 6], [-9, 3], [-16, 0], [-15, 2], [-16, 5], [-14, 3], [-12, -2], [-9, -4], [-7, -7], [-8, -4], [-13, -3]], [[7187, 6964], [5, 50], [-1, 28], [-7, 24], [-1, 14], [6, 6], [5, 3]], [[4047, 5753], [2, 13], [2, 5], [5, 1], [1, 5], [-3, 9], [3, 10], [14, 15], [6, 10], [0, 13], [-4, 20], [-5, 12], [-4, 6], [-7, 5], [-11, 3], [-9, 10], [-9, 16], [-16, 8], [-24, 2], [-21, 6], [-19, 10], [-27, 10], [-34, 11], [-23, 3], [-14, -3], [-11, -1], [-14, 3], [-37, 26], [-11, 12], [0, 7], [-4, 10], [-8, 15], [-11, 12], [-13, 10], [-15, 16], [-17, 24], [-3, 17], [9, 10], [-6, 20], [-21, 30], [-11, 19], [0, 7], [8, 4], [15, 0], [16, -4], [19, -7], [13, -3], [6, 1], [0, 8], [-6, 16], [-2, 14], [3, 13], [-7, 11], [-18, 9], [-10, 10], [-4, 11], [-1, 9], [3, 9], [-1, 3], [-3, 1], [-1, 2], [1, 5], [3, 3], [2, 2], [0, 2], [-3, 1], [-4, 4], [-2, 5], [2, 3], [6, 0], [4, 3], [3, 5], [-5, 3], [-14, 0], [-8, 7], [-1, 12], [-3, 10], [-4, 6], [-5, 1], [-3, 0], [-2, 1], [-2, 3], [-3, 4], [-9, 5], [-4, 8], [2, 10], [-3, 6], [-5, 2], [-2, 3], [1, 3], [-1, 2], [-2, 1], [-9, 19], [4, 13], [15, 11], [11, 6], [6, -2], [3, 3], [-1, 8], [-4, 3], [-7, -4], [-7, 1], [-6, 3], [-5, 1], [-3, 0], [-1, 3], [-1, 10], [-3, 6], [-7, 3], [-3, 3], [2, 4], [-1, 2], [-5, -1], [-1, 3], [2, 6], [-2, 4], [-4, 4], [-6, 2], [-2, 0], [-3, 2], [2, 2], [-1, 2], [-12, 4], [-6, 0], [-1, -4], [-4, 0], [-8, 4], [-2, 4], [3, 4], [-2, 6], [-8, 8], [0, 2], [4, 1], [1, 2], [-5, 7], [0, 7], [4, 6], [-5, 7], [-14, 10], [-11, 4], [-6, -1], [-2, 1], [1, 4], [-3, 2], [-2, 2], [2, 3], [16, 9], [3, 4], [-1, 2], [-4, 3], [3, 6], [8, 8], [1, 8], [-6, 9], [0, 6], [3, 2], [-3, 6], [-5, 4], [-2, 4], [3, 4], [0, 8], [-5, 11], [1, 9], [8, 5], [4, 0], [3, -1], [2, 2], [2, 2], [4, 2], [8, -2], [11, 3], [13, 7], [6, 0], [5, 0], [3, 1], [1, 3], [7, 9], [13, 11], [10, 3], [7, -5], [5, 0], [2, 3], [4, 1], [3, -1], [4, 1], [3, 7], [6, 4], [7, 3], [3, 3], [-2, 2], [1, 2], [2, 2], [0, 4], [-1, 1], [0, 2], [2, 2], [1, 2], [-1, 2], [2, 2], [6, 3], [2, 4], [-1, 10], [2, 9], [5, 10], [3, 1], [4, 0], [5, -4], [9, 0], [12, 7], [6, 6], [2, 4], [3, 2], [4, -1], [1, 3], [-3, 4], [4, 6], [9, 6], [6, 1], [4, 0], [1, 2], [0, 4], [2, 4], [8, 5], [0, 4], [-2, 1], [-1, 2], [3, 5]], [[7180, 4367], [-15, 5], [-14, 1], [-19, 1], [-10, -1], [-2, -2], [-11, 0], [-31, 5], [-18, 4], [-23, 16], [-33, 29], [-21, 16], [-11, 3], [-14, 6], [-17, 11], [-11, 5], [-6, 0], [-8, 2], [-9, 5], [-8, 1], [-7, -2]], [[6892, 4472], [-7, 0], [-10, 0]], [[6875, 4472], [-6, 4], [-10, 10], [-11, 7], [-11, 3], [-8, 0], [-5, -1], [-5, 2], [-6, -1], [-6, 1], [-10, 4]], [[6797, 4501], [-14, 1], [-17, -3]], [[6766, 4499], [-15, 1], [-13, 6], [-10, 1], [-8, 0], [-6, 2], [-5, 2], [-5, 0], [-5, -1], [-5, 1], [-7, -1], [-6, -2], [-5, 1]], [[6676, 4509], [-3, 3], [-3, 4]], [[6670, 4516], [-6, 1], [-7, 0], [-9, -2], [-4, -3], [-2, -4], [-6, -3], [-10, -1], [-7, 1], [-1, 3], [-7, 1], [-12, 0], [-7, 0], [-12, 0]], [[6580, 4509], [-15, -9], [-10, -3], [-7, 2], [-7, -4], [-7, -9], [-10, -5], [-17, -2], [-5, -1], [-10, 0], [-9, -2], [-7, -6], [-8, -1], [-8, 1], [-8, -2], [-6, -5], [-4, -6], [-1, -6], [-7, -4], [-12, -2], [-4, -3], [1, -4], [-6, -3], [-20, -4], [-15, -6], [-9, -8], [-9, -6], [-15, -3], [-21, -9], [-3, -5], [8, -5], [3, -5], [-2, -4], [-7, 0], [-10, 3], [-8, 0], [-4, -1], [-2, -3], [0, -5], [-4, -3], [-8, -2], [-5, -4], [-2, -6], [-10, -7], [-17, -9], [-14, -12], [-10, -15], [-12, -10], [-13, -5], [-10, -7], [-5, -9], [-14, -14], [-24, -18], [-22, -13], [-19, -7], [-11, -8], [-2, -9], [-13, -9], [-22, -12]], [[6062, 4195], [-12, -9]], [[6050, 4186], [-1, -7], [-9, -9], [-16, -12], [-6, -10], [7, -13], [1, -15], [-3, -6], [-7, -3], [-2, -4], [4, -5], [0, -8], [-3, -10], [-8, -11], [-12, -12], [-3, -8], [6, -4], [3, -5], [0, -4], [-3, -7], [-5, -9], [-7, -7], [-11, -4], [-3, -5], [4, -7], [1, -7], [-3, -7], [2, -7], [6, -7], [0, -7], [-11, -16]], [[6650, 6163], [20, 16], [5, 13], [-5, 15], [6, 12], [14, 6], [9, 16], [4, 24], [6, 14], [7, 5], [2, 5], [-5, 4], [-1, 6], [1, 6], [9, 10], [15, 12], [12, 8], [9, 4], [8, 6], [6, 9], [16, 4], [25, -3], [14, 4], [4, 10], [7, 9], [10, 10], [3, 7], [-4, 7], [-2, 9], [-1, 13], [-6, 9], [-13, 6], [-10, 8], [-7, 11], [-10, 7], [-14, 2], [-9, 4], [-6, 5], [-4, 8], [-1, 10], [-6, 14], [-11, 18], [3, 29], [18, 37], [15, 25], [13, 12], [16, 7], [21, 3], [14, 6], [8, 10], [0, 10], [-8, 9], [-9, 4], [-12, 1], [-13, 6], [-12, 11], [-7, 10], [-2, 12], [-9, 9], [-3, 8], [0, 11], [-6, 16], [-13, 20], [-12, 13], [-11, 5], [-7, 10], [-4, 15], [7, 23], [12, 20], [7, 12], [6, 20], [-5, 8], [0, 7], [7, 7], [5, 13], [3, 26]], [[6770, 6962], [37, 27], [16, 17], [7, 14], [11, 3], [14, -10], [8, -8], [2, -6], [5, -4], [8, -3], [9, 1], [10, 6], [3, 6], [-2, 6], [3, 8], [7, 10], [-2, 7], [-12, 5], [-14, 9], [-17, 14], [-7, 13], [2, 11], [-7, 18], [-8, 11], [-8, 12], [-10, 24], [-4, 31], [2, 7], [5, 4]], [[6548, 7932], [-12, -8], [-13, -10], [-17, -19], [1, -5], [-4, -5], [-1, -8], [1, -9], [5, -6], [2, -12], [-3, -10], [1, -6], [3, -10], [3, -10], [0, -4], [4, -3], [4, -4], [-1, -9], [13, -16], [9, -7], [12, -7], [4, -6], [7, -8], [4, -1], [2, -4], [-2, -6], [-1, -8], [-7, -10], [-18, -19], [-2, -4], [4, -15], [-2, -13], [-1, -6], [-9, -11], [-21, -26], [-12, -5], [-7, -8], [-4, 0], [-6, 0], [-1, -1]], [[6483, 7613], [3, -2]], [[6486, 7611], [6, -2]], [[6492, 7609], [5, -5]], [[6497, 7604], [12, -4]], [[6509, 7600], [20, -6]], [[6529, 7594], [9, 0]], [[6538, 7594], [9, -1]], [[6547, 7593], [5, -2]], [[6552, 7591], [6, 3]], [[6558, 7594], [20, 9], [10, 4]], [[6588, 7607], [7, 4]], [[6595, 7611], [5, 4]], [[6600, 7615], [4, 1]], [[6604, 7616], [2, -3], [3, -2]], [[6609, 7611], [7, -2]], [[6616, 7609], [17, -3]], [[6633, 7606], [8, -1], [11, 3], [9, 4], [5, 2]], [[6666, 7614], [7, -5]], [[6673, 7609], [5, -6], [3, -3]], [[6681, 7600], [7, 0]], [[6688, 7600], [14, -1]], [[6702, 7599], [10, -2], [7, 1], [8, 2], [5, 6]], [[6732, 7606], [13, 6]], [[6745, 7612], [11, 4]], [[6756, 7616], [7, 7], [10, 11]], [[6773, 7634], [4, 5]], [[6777, 7639], [0, 7]], [[6777, 7646], [14, 23], [6, 4], [3, 8]], [[6800, 7681], [6, 8]], [[6806, 7689], [0, 7], [4, 7], [8, 5]], [[6818, 7708], [6, 4]], [[6824, 7712], [9, 10]], [[6833, 7722], [7, 9]], [[6840, 7731], [17, 21]], [[6857, 7752], [1, 5]], [[6858, 7757], [6, 4]], [[6864, 7761], [3, 4]], [[6867, 7765], [7, 6]], [[6874, 7771], [7, 4]], [[6881, 7775], [3, 6]], [[6884, 7781], [6, 7]], [[6890, 7788], [12, 5], [4, 2]], [[3157, 8129], [10, 22], [9, 4], [3, 6], [-3, 14], [1, 3], [2, 1], [2, 0], [3, -2], [7, -6], [6, -7], [5, -2], [6, 2], [27, 15], [-1, 4], [2, 9], [9, 8], [10, 3], [2, 4], [-2, 6], [-10, 16], [-9, 9], [-6, 9], [-3, 8], [-10, 7], [4, 7], [8, 9], [3, 1]], [[4513, 0], [-108, 0]], [[4026, 0], [-395, 0]], [[3455, 0], [-3455, 0], [0, 9147]], [[356, 9999], [2421, 0]], [[2781, 9999], [338, 0]], [[3147, 9999], [69, 0]], [[3225, 9999], [179, 0]], [[3410, 9999], [6589, 0], [0, -9999], [-4604, 0]], [[4376, 5660], [-5, 3], [-5, 1], [-7, 0], [0, 3], [5, 5], [1, 5], [-1, 4], [-6, 1], [-10, 3], [-9, 2], [-6, 3], [-4, 2], [-3, 1], [-5, 3], [-5, 3], [-4, 3], [-3, 1], [-2, -1], [-1, -2], [0, -2], [2, -2], [1, -2], [0, -2], [-2, 0], [-2, 0], [-2, 1], [-3, 1], [-4, 2], [-5, 2], [-5, 2], [-2, 2], [-1, 2], [-1, 4], [1, 4], [2, 3], [3, 2], [4, 4], [5, 1], [7, -3], [7, -3], [3, 0], [-3, 6], [-7, 11], [-9, 11], [-1, 3], [4, 2], [8, 4], [2, 2], [1, 1], [5, 1], [5, 2], [3, 2], [8, 0], [9, -8], [12, -3], [13, -11], [6, -2], [7, -2], [8, -12], [5, 0], [6, -4], [5, -2], [3, -1], [3, -3], [6, -5], [10, -3], [3, -5], [0, -7], [3, -2], [13, -1], [10, -1], [5, -2], [-2, -3], [-2, -2], [-4, -4], [-5, -3], [3, -6], [6, -5], [9, -2], [10, -1], [8, -1], [6, -1], [0, -4], [1, -2], [0, -1], [-3, -1], [-7, -1], [-5, -1], [-2, -1], [-1, -2], [-4, -3], [-7, -2], [-10, -1], [-6, -1], [-4, -3], [5, -1], [4, -4], [1, -3], [1, -2], [-1, -2], [-1, -2], [-5, 0], [-6, 0], [-9, 4], [-6, 2], [-4, 1], [-3, 4], [7, 7], [2, 5], [-1, 5], [0, 4], [5, 3], [9, 3], [6, -2], [3, 0], [-1, 4], [-8, 3], [-10, 7], [-9, 3], [-4, 0], [0, -5], [-2, -5], [-2, -6], [-6, -2], [-7, 0], [-6, 0], [-8, 1], [-6, 2]], [[3861, 3022], [-3, 6], [0, 9], [2, 6], [9, 7], [9, 5], [11, -2], [10, -3], [9, -8], [6, -9], [-4, -3], [-14, 1], [-22, -10], [-13, 1]], [[5395, 4128], [-25, -6], [-24, -3], [-22, -5], [-12, 7], [-7, 5], [-10, 5], [-7, 6], [7, 5], [15, 2], [15, 5], [5, 3], [6, 3], [31, -4], [2, 2], [6, -1], [12, -4], [8, -9], [0, -11]], [[4002, 2082], [-2, -3], [-16, -2], [-24, -4], [-23, 0], [-22, 1], [-11, 2], [-18, -2], [-25, -6], [-5, -7], [6, -5], [15, 0], [9, 1], [2, -3], [-10, -6], [-10, -7], [-5, -1], [-8, 11], [-14, 16], [-8, 3], [-8, 0], [-7, -1], [-2, 1], [10, 7], [27, -2], [8, 3], [-2, 3], [-9, 3], [-17, 6], [-9, -2], [-9, -5], [-1, 3], [3, 6], [-1, 6], [5, 4], [6, 3], [3, 2], [7, -5], [11, -11], [16, -5], [21, -2], [10, 1], [42, 2], [46, 4], [11, -3], [8, -6]], [[6443, 7871], [-4, -4], [-3, -2], [1, -3], [2, -2], [4, -1], [4, -1], [0, -3], [-1, -3], [-4, -1], [-4, -1], [-3, 1], [-5, 2], [-4, -1], [-5, -3], [-2, -4], [-4, -5], [-5, -3], [-3, 1], [-6, 2], [-4, 1], [-2, 2], [-4, 1], [-6, 0], [-5, 0], [1, 5], [4, 4], [3, 1], [1, 4], [-2, 4], [7, 9], [9, 10], [3, 9], [4, 4], [5, 0], [10, -3], [8, -6], [4, -1], [7, 0], [7, 1], [5, -1], [1, -3], [-3, -4], [-3, -5], [-3, -1], [-5, 0]], [[3962, 3130], [0, -3], [-1, -1], [-9, 0], [-18, 1], [-13, 2], [-1, 4], [1, 3], [3, 4], [15, 3], [13, 1], [5, -2], [4, -2], [1, -3], [-1, -3], [1, -4]], [[3956, 2130], [-26, 8], [-19, 5], [-13, 0], [-6, -2], [-10, 0], [-17, -1], [-12, -1], [-1, 2], [4, 2], [5, 2], [0, 3], [-3, 4], [-4, 5], [5, 3], [8, 4], [7, 0], [9, -4], [42, -8], [37, -6], [7, -2], [12, -4], [4, -4], [0, -3], [-2, -3], [-3, -1], [-7, -1], [-17, 2]], [[6484, 4691], [-4, 1], [0, 13], [4, 3], [9, 4], [4, 4], [-2, 4], [-4, 2], [-3, 1], [1, 2], [-7, 1], [-14, 1], [-5, 3], [6, 2], [15, 0], [11, -1], [3, 4], [0, 5], [-2, 3], [-21, 3], [-18, 3], [4, 1], [10, 0], [12, -1], [12, 0], [8, 3], [1, 5], [-8, 6], [5, 4], [13, 2], [2, 3], [0, 4], [-5, 5], [0, 4], [4, 3], [-1, 6], [2, 4], [7, 1], [3, 4], [0, 5], [-5, 6], [-3, 5], [2, 5], [1, 5], [4, 3], [8, 3]], [[6533, 4835], [-3, -9], [2, -8], [3, -6], [-1, -20], [0, -18], [6, -5], [5, -2], [-7, -2], [-9, -2], [-3, -4], [1, -4], [7, -6], [5, -4], [-4, 0], [-9, 3], [-6, 0], [-3, -3], [-2, -3], [0, -4], [8, -5], [2, -5], [-2, -4], [-2, -2], [-4, -2], [0, -3], [13, -3], [12, -3], [-10, -2], [-10, -3], [-6, -1], [-7, -1], [0, -3], [4, -4], [-3, -2], [-9, 1], [-5, -1], [-4, -3], [-4, -2], [-4, 1]], [[5711, 8268], [27, -26], [0, -3], [-3, -3], [-16, -6], [-4, -3], [-6, -12], [-21, -6], [-14, -8], [-10, -7], [-5, -2], [-19, -2], [-6, -4], [-7, -13], [-6, -6], [-10, -6], [0, -4], [14, -14], [2, -5], [-4, -7], [0, -4], [7, -6], [9, -1], [8, 2], [11, 0], [6, -2], [3, -1], [1, -5], [-5, -9], [-6, -6], [-27, -10], [-12, -5], [-7, -4], [-15, 2], [-7, 0], [-6, -3], [-3, -3], [-10, 0], [-14, -2], [-6, -2], [-4, -4], [-4, -7], [3, -8], [4, -7], [0, -6], [3, -18], [-4, -4], [-9, -5], [-11, -9], [-13, -11], [2, -4], [30, -24], [30, -25], [31, -25]], [[5608, 7915], [4, -1], [6, -3], [5, -8], [4, -10], [0, -4], [-3, -6], [-7, -7], [-9, -6], [-13, -5], [-11, -4], [-8, -12], [-6, -4], [-4, -2], [-10, -1], [-15, 0], [-10, 1], [-13, -9], [-16, -3], [-10, -12], [-38, -10], [-38, -7], [-10, -3], [-37, 6], [-8, -2], [-11, -5], [-9, -5], [-8, 0], [-7, -2], [-4, -9], [-3, -28], [-13, -8], [-16, 0], [-11, 10], [-13, 7], [-23, 18], [-6, 2], [-6, 0], [-21, -5], [-10, 2], [-8, 2], [-14, 0], [-23, -1], [-16, 0], [-7, 5], [-7, 10], [-5, 3], [-5, 3], [-9, 1], [-39, 0], [-7, 0], [-5, 2], [-8, 9], [-8, 4], [-9, 0], [-4, -5], [16, -15], [5, -8], [14, -12], [42, -25], [8, -8], [-1, -10], [0, -15], [1, -15], [11, -21], [15, -21], [4, -14], [-3, -10], [-3, -6], [1, -2], [3, -2], [14, -3], [30, -2], [19, 0], [28, -2], [2, -8], [-3, -12]], [[5229, 7602], [-6, -7], [-4, -2], [-15, -2], [-16, -8], [-23, -7], [-14, -1], [-5, -2], [-5, -2], [-4, -3], [-4, -14], [-7, -16], [-13, -9], [-14, -8], [-15, -1], [-11, 1], [-6, -3], [-8, -6], [-12, -8], [-9, -4]], [[5038, 7500], [-12, 0], [-14, -4]], [[5012, 7496], [-16, -6], [-11, -6], [-10, -9], [-13, -9], [-14, -7], [-7, -8], [-9, -11], [-12, 0], [-1, 6], [6, 10], [-6, 9], [-12, 5], [-5, 1], [-5, 0], [-13, -5], [-17, -7], [-10, -6], [-9, -3], [-19, -2], [-15, -1], [-7, 1], [-12, 6], [-27, 18], [-36, 24]], [[4716, 7545], [-8, 15], [-7, 12]], [[4591, 7662], [0, 6], [4, 4], [45, 31], [23, 14], [3, 3], [2, 3], [1, 4], [-5, 5], [-21, 24], [-8, 5], [-7, 3], [-9, 10], [-10, 24]], [[4646, 8012], [-1, 6], [-12, 7], [-23, 4]], [[4377, 8015], [-10, -3], [-2, 1], [-22, 20], [-20, 18], [-24, 22], [-27, 25], [-5, 2], [-9, -1], [-11, 0], [-17, 3], [-12, 3], [-9, 4], [-17, 4], [-12, 1], [-11, -1], [-28, -9], [-17, -1], [-13, 0], [-34, 4], [-23, 1], [-27, -3], [-12, -2], [-16, 5], [-11, 7], [-11, 16], [-7, 12], [-13, 3], [-14, 2], [-7, 4], [-4, 8], [-1, 9], [2, 15], [1, 6], [2, 16], [8, 9], [5, 4], [-1, 7], [-4, 14], [-4, 10], [-15, 11], [-20, 15], [-9, 23], [-10, 27], [-8, 3], [-8, -1], [-8, 1], [-7, 11], [-7, 2], [-11, -4], [-8, -3], [-20, -2], [-5, 2], [2, 4], [9, 10], [10, 13], [12, 11], [11, 12], [8, 13], [5, 25], [4, 18], [10, 31], [19, 25], [7, 11], [17, 15], [8, 8], [10, 6], [28, 9], [34, 43], [8, 7], [34, 6], [26, 5], [19, 5], [7, 3], [5, 1]], [[6050, 4186], [5, 4]], [[6055, 4190], [8, -7], [24, -2], [25, -1], [4, 2], [9, 13], [13, 4], [13, 0], [15, 0], [16, -9], [45, -27], [33, -19], [10, -9], [9, -7], [6, -6], [-3, -16], [1, -7], [1, -2], [8, -1], [11, 2], [10, 3], [7, 5], [7, 4], [6, 3], [2, 3], [3, 4], [4, 1], [6, -3], [16, -9], [12, -9], [3, -5], [4, -5], [5, -5], [4, -4], [11, -6], [12, -3], [8, 3], [20, -11], [44, -10], [8, -6], [8, -9], [15, -13], [21, -11], [17, -5], [17, -3], [9, -2], [6, -5], [10, -5], [7, -1], [2, -5], [6, -9], [7, -12], [8, -11], [16, -10], [18, -9], [19, -4], [10, -6], [5, -6], [-13, -9], [-14, -11], [-12, -9], [-12, -6], [-5, -4], [-2, -7], [0, -34], [-1, -13], [1, -4]], [[6638, 3825], [2, -2], [7, -3]], [[6647, 3820], [10, -3], [4, -1]], [[6483, 7613], [-7, 0], [-5, 3], [-1, 4]], [[6470, 7620], [-2, 5]], [[6468, 7625], [-7, 1], [-12, -2], [-3, 2], [-8, 5], [-6, 5], [-1, 5], [-4, 0], [-9, -5], [-6, -1], [-5, 1], [-6, 0], [-14, -5], [-8, -1], [-6, -5], [-39, -3], [-10, -1], [-24, 9], [-6, 3], [-3, 0], [-2, -1], [-3, -1], [-4, -11], [-3, -3], [-6, -3], [-6, -4], [-2, -5], [10, -2], [7, -8], [9, -7], [6, -6], [-1, -6], [-1, -10], [-5, -3], [-8, -1], [-29, 4], [-23, 4], [-10, 1], [-4, 1]], [[6216, 7572], [-6, 4], [-5, 3], [-10, 1], [-11, 2], [-8, 8], [-8, 12], [-3, 5], [-7, 5], [-6, 8], [-2, 6], [-5, 6], [-2, 2], [-4, 8], [-1, 3], [-2, 1], [-2, 2], [-6, 9], [-1, 2], [-2, 1], [-6, 6], [-5, 2], [-2, 3], [1, 9], [-3, 4], [-1, 8], [0, 3], [-2, 3], [-4, 3], [-1, 5], [-1, 15], [-2, 2], [-18, 0], [-1, -1], [-8, -1], [-8, 0], [-8, 1], [-6, 3], [-1, 3], [0, 10], [-10, 7], [-16, 10], [-5, 11], [-5, 8], [-18, 15], [-3, 10], [0, 8], [6, 7], [9, 12], [3, 10], [3, 6], [4, 7], [4, 10], [-3, 6], [-5, 6], [-2, 4], [5, 6], [6, 5], [5, 0], [8, 3], [6, 4], [9, 1], [11, 0], [22, 0], [12, 2], [4, 3], [-1, 6], [6, 5], [6, 3], [2, 2], [1, 1], [-2, 2], [-2, 2], [-7, 0], [-5, 9], [4, 4], [4, 8], [2, 4], [7, 7]], [[4290, 6922], [-1, -7], [-4, -3], [-7, 0], [-11, 5], [-8, -2], [-8, 0], [-11, 6], [-4, 5], [-9, 5], [-18, -2], [-14, 0], [-13, 1], [-11, -2], [-9, -5], [-8, -7], [-8, -6], [-24, -4], [-13, 0], [-12, -4], [-17, -1], [-12, -4], [-21, -2], [-25, -1], [-14, -6], [-6, -2], [-14, -7], [-25, -11], [-14, -4], [-16, -12], [-20, -12], [-13, -6], [-19, -2]], [[3881, 6832], [-8, -3], [-3, -4]], [[3870, 6825], [2, -4], [-4, -11], [-5, -21], [-2, -10], [-1, -4], [-13, -17]], [[3847, 6758], [-14, -15], [-6, -10]], [[3827, 6733], [-4, -7], [4, -6], [6, -11], [4, -9], [2, -6], [-2, -7], [-6, -6], [-9, -4], [-12, -1], [-25, -11], [-27, -17], [-10, -7], [-2, -7], [-4, -11]], [[3742, 6623], [1, -6]], [[3743, 6617], [5, -4], [5, -10], [1, -5], [-4, -2], [-8, -1], [-7, -1], [-6, -2], [-5, 0], [-5, -1], [0, -4], [2, -5], [-1, -4], [-3, -3], [-3, -3], [3, -3]], [[3717, 6569], [5, -2], [7, -5]], [[3729, 6562], [10, -9], [8, -1], [7, -3], [1, -5], [-3, -5], [-6, -2], [0, -5], [8, -5], [5, -4], [6, -5], [5, -8], [0, -5], [5, -6], [3, -5], [0, -5], [9, -5], [7, -3], [6, -4], [5, -2], [2, -3], [-1, -6], [8, -9], [14, -7], [11, -10], [8, -7], [13, -12], [1, -13], [-17, -16], [-17, -15], [28, 1], [28, -1], [30, -4], [20, -4], [12, -2], [9, -5], [4, -8], [4, -6], [0, -9], [13, -7], [-1, -7], [0, -10], [5, 0], [37, 0], [40, 0], [38, 1], [14, 3], [18, 12], [10, 3], [11, 6], [12, 10], [10, 5], [8, 5], [12, 6], [5, 4], [4, 2], [10, 2], [-4, -5], [-3, -6], [-1, -8], [4, -9], [-4, -6], [-6, -6], [0, -16], [0, -22], [0, -19], [0, -24], [0, -26], [0, -18], [6, 3], [9, 3], [12, -8], [8, -3], [7, -1], [8, 0], [10, 2], [22, 7], [15, 5], [17, 0], [24, -2], [13, 0]], [[4345, 6215], [18, -23], [13, -17], [15, -19], [12, -15], [19, -23], [15, -20], [17, -22], [19, -24], [-6, -6], [-5, -5], [1, -9], [-8, -4], [-8, -3], [-9, -7], [-7, -6], [0, -9], [1, -44], [-2, -12], [-4, -10], [-5, -5], [-3, -4], [7, -11], [7, -20], [5, -4], [7, -9], [3, -8], [-2, -3], [-13, -4], [-5, -3], [-1, -12], [-5, -4], [-10, -5], [-6, -7], [-5, -4], [-5, -3], [-3, -7], [-3, -8], [-12, -6], [-2, -9], [0, -8], [6, -8], [20, -17], [2, -4], [-11, -10], [-7, -7], [-17, -22], [0, -4], [4, -10], [25, -43], [4, -4], [8, -4], [13, 1], [18, -5], [10, -5], [1, -3], [-3, -2], [-10, -3], [-11, -5], [-4, -4], [0, -7], [2, -11], [-5, -3], [-11, -4], [-9, -6], [-10, -10], [-16, -15], [-6, -4], [-2, -5], [-10, -2], [-17, -10], [-3, -5], [3, -5], [9, -4], [6, -6], [1, -7], [0, -5]], [[4355, 5525], [-11, -7], [-14, -8], [-17, -2], [-7, -4], [2, -8], [6, -12], [-1, -10], [-5, -11], [-12, -12], [-19, -8], [-18, -4], [-14, 0], [-14, -1], [-5, -1]], [[2810, 7011], [4, -2], [4, -2], [0, -4], [1, -3], [3, -3], [0, -6], [3, -4], [1, -4], [0, -6], [-2, -3], [2, -5], [5, -9], [-2, -3], [-5, -2], [-5, -3], [-5, -6], [-8, 0], [-12, 3], [-7, -3], [-3, -7], [2, -5], [1, -5], [5, -4], [14, 0], [-13, -14], [-6, -6], [1, -4], [8, -3], [6, 0], [13, 5], [9, 7], [5, 4], [8, 2], [11, -4], [14, -6], [17, -6], [7, -3], [12, 0], [10, 2], [9, -5], [9, -4], [2, -14], [7, -10], [8, -8], [9, -9], [9, -3], [12, 0], [16, -4], [6, 3], [5, 6], [3, 4], [8, 1], [1, 5], [-2, 5], [3, 5], [6, 6], [17, 7], [9, 3], [1, 5], [3, 6], [-1, 4], [-3, 4], [0, 6], [5, 9], [6, 9], [6, 12], [2, 6], [6, 4], [2, 5], [3, 6], [8, 7], [-1, 8], [3, 3], [1, 8], [7, 18], [0, 3], [3, 1], [6, -1], [5, -4], [1, -4], [2, -2], [5, 1], [5, 2], [0, 3], [-4, 4], [-1, 2], [1, 3], [8, 7], [9, 8], [18, 17], [11, 7], [29, 7], [22, 6], [21, 5], [28, 8], [40, 10], [29, 8], [26, 14], [20, 10], [17, 10], [21, 11], [30, 25], [20, 18], [14, 13], [11, 8], [8, 22], [9, 26], [4, 11], [2, 4], [4, -1], [4, -3], [6, 0], [5, 0], [3, 1], [-4, 26], [0, 6], [3, 7], [-1, 3], [-8, 5], [-15, 11], [-5, 9], [-4, 8], [-10, 5], [-10, 4], [1, 4], [6, 0], [15, -4], [11, 1], [8, 1], [8, 3]], [[3530, 7356], [9, 7], [6, 0], [6, -1], [12, -7], [7, -4], [9, -3], [8, -1], [7, -1], [5, 1], [3, -5], [4, -6], [9, -4], [11, -3], [9, -7], [6, -4], [6, -5], [7, -6], [6, -12], [3, -8], [4, -4], [-1, -5], [7, -8], [6, -4], [9, -3], [18, -4], [10, -7], [8, -3], [9, -7], [8, -3], [11, 1], [10, -4], [8, -7], [5, -9], [7, -5], [4, -9], [-4, -11], [4, -6], [8, -4], [13, -5], [12, 1], [6, -1], [4, -5], [2, -5], [5, -13], [3, -8], [-5, -8], [-2, -6], [3, -7], [12, -4], [11, -3], [7, -5], [8, -2], [11, 1], [14, 1], [7, 3], [5, 1], [6, -1], [12, -3], [15, -4], [14, 2], [11, 1], [12, 2], [12, 6], [10, 0], [7, 4], [10, 6], [9, 7], [7, 1], [12, -3], [16, -4], [9, -6], [7, -2], [7, -4], [14, 0], [14, 2], [12, 7], [13, 2], [8, 2], [8, -1], [30, -13], [8, -7], [10, -1], [8, -4], [15, -3], [7, -5], [10, -2], [7, -6], [12, -3], [10, -2], [4, -5], [0, -3], [-1, -2], [-11, -12], [-20, -23], [-19, -21], [-29, -34], [-16, -18], [4, -1], [26, -8]], [[4216, 6960], [9, 3], [6, 3]], [[5977, 5245], [3, -7], [2, -6], [4, -4], [5, -6], [5, -3], [3, -5], [-1, -6], [2, -8], [2, -7], [3, -1], [7, -2], [3, -6], [-3, -3], [1, -4], [3, -3], [-2, -3], [1, -3], [4, -2], [5, -8], [0, -15], [-5, -7], [-3, -7], [-1, -3], [3, -6], [-5, -7], [-6, -8], [2, -5], [0, -5], [1, -6], [1, -5], [-2, -6], [-1, -5], [-1, -5], [2, -6], [-4, -6], [-3, -5], [-1, -4], [5, -6], [11, -3], [8, -1], [8, 4], [6, 1], [12, -3], [10, -6], [13, -1], [12, -1]], [[6086, 5031], [9, -2], [13, 3]], [[6108, 5032], [14, -3], [16, -3]], [[6138, 5026], [13, -2], [13, 0], [10, 1], [11, 3], [10, -1], [7, 6], [4, 5], [4, 3], [11, 3], [8, -2], [6, -9], [11, -5], [4, -4], [8, -1], [17, -1], [11, 1], [13, -3], [8, 0], [7, -5], [6, -6], [1, -11], [6, -8], [8, -3], [5, -6], [-2, -7], [-3, -7], [0, -8], [4, -7], [0, -8], [3, -7], [6, -6], [2, -10], [-1, -8], [3, -4], [2, -6], [-2, -5], [-1, -6], [0, -6], [3, -5], [8, -6], [3, -12], [0, -7], [4, -9], [7, -4], [11, -2], [13, -1], [16, 2], [14, 2], [8, 3], [16, 6], [14, 4], [7, 3], [6, 1], [14, -4], [13, -5], [10, -7], [18, -8]], [[6536, 4837], [-3, -2]], [[6484, 4691], [-1, -15]], [[6483, 4676], [-2, -45]], [[5339, 5025], [-4, 3]], [[5335, 5028], [3, 5], [9, 20], [13, 30], [13, 31], [16, 36], [0, 26], [0, 27], [22, 23], [15, 16], [14, 15], [14, 26], [9, 17], [35, 4], [59, 9], [30, 5], [62, 9], [64, 10], [66, 0], [65, 1], [50, -22], [38, -16], [42, -18], [3, -4], [2, -15], [-2, -18]], [[6216, 7572], [-6, -1], [-5, -3], [-8, 2], [-10, -1], [-11, -2], [-10, -1], [-19, 3], [-6, 1], [-4, 2], [-8, 7], [-4, 1], [-10, -4], [-12, -2], [-6, 0], [-7, -2], [-7, -3], [-12, -15], [-7, -5], [-7, -2], [-14, 0], [-14, 0], [-12, -4], [-10, -2], [-5, 0], [-2, -8], [-2, -3], [-4, -2], [-8, -1], [-7, 0], [-4, 4], [-9, 1], [-7, 1], [-4, 2], [-4, 0], [-3, -3], [-3, -3], [-2, -5], [-11, -2], [-5, -3], [3, -9], [-1, -4], [-3, -3], [-13, -1], [-11, 1], [-7, -4], [-8, -4], [-5, -1], [-6, 0], [-8, 5], [-7, 6]], [[5861, 7505], [-19, 4], [-18, 4], [-13, 9], [-3, 4], [-5, 2]], [[5803, 7528], [-15, 12], [-8, 7]], [[5780, 7547], [-9, 2], [-10, 2], [1, 6], [-1, 5], [-4, 2], [-6, 1], [-2, 3], [0, 6], [1, 17], [-1, 16], [-14, 6], [-6, 3], [-10, 24], [-4, 11], [-1, 8], [4, 23], [3, 11], [11, 20], [6, 7], [0, 5], [0, 7]], [[5738, 7732], [-3, 13], [17, 9]], [[5752, 7754], [8, 3], [1, 6], [9, 7], [4, 6], [4, 6], [-1, 2], [-4, 2], [-5, 5], [-10, 15]], [[5758, 7806], [-4, 3], [-3, 4]], [[5751, 7813], [2, 6], [4, 7], [-1, 3], [-6, 4], [-12, 6], [-11, 0], [-8, 3], [-12, 0]], [[5707, 7842], [-9, 0], [-6, 3]], [[5692, 7845], [2, 4], [2, 3], [8, 7], [5, 8], [1, 7], [2, 10], [2, 9], [1, 10], [-12, 6], [-4, 5], [-6, 5], [-5, 0], [-9, 2], [-13, -6], [-11, 1], [-7, -2], [-17, 0], [-10, 3], [-13, -2]], [[3020, 7521], [4, -2], [13, -8], [8, -8], [14, -5], [10, -4], [29, -16], [19, -8], [20, -8], [30, -7], [18, 1], [4, -6], [4, -6], [6, -4], [11, -3], [7, -1], [2, -1], [6, -23], [4, -3], [15, -3], [18, -2], [7, 1], [16, -6], [12, -3], [13, -2], [9, -1], [4, 1], [1, 2], [7, 0], [11, -3], [16, -1], [9, 3], [2, 5], [0, 8], [4, 2], [11, 5], [6, -1], [29, -10], [5, -3], [8, -7], [13, -10], [15, -7], [23, -3], [22, -10], [25, -8]], [[4732, 7496], [-28, -4], [-2, 0], [-2, 3], [1, 20], [0, 23], [-4, 9], [-13, 15], [-16, 20], [-4, 5], [-7, 3], [-9, 1], [-6, -1], [-8, -4], [-7, -4], [-15, -12], [-15, -14], [-8, -3], [-9, -1], [-8, 0], [-6, 4], [-6, 7], [-8, 11], [-9, 3], [-3, -3], [-3, -6], [-3, -6], [7, -8], [5, -5], [-9, 0], [-29, 0], [-34, 0], [-33, 0], [-31, 0], [-28, 0], [-10, 0], [-11, 4], [-10, 1], [-6, 0], [-10, -3], [-13, -1], [-8, -3], [-7, 1], [0, -18], [0, -25], [-1, -26], [8, 2], [7, 0], [5, -2], [13, 2], [8, -1], [7, -1], [7, 0], [4, -2], [6, 0], [6, 3], [7, -2], [7, -3], [5, -6], [4, -7], [5, -3], [0, -7], [-1, -5], [-1, -4], [2, -3], [1, -3], [0, -2], [-3, 0], [-5, -1], [-6, 0], [-4, 0], [-4, 2], [-3, 1], [-4, -1], [-5, 2], [-4, 3], [-7, 4], [-8, -2], [-6, -2], [-5, -2], [-5, -2], [-5, 1], [-7, -2], [-5, -2], [-7, -2], [-8, -1], [-9, -1], [-9, -1], [-9, 0], [-1, -14], [-1, -27], [-1, -18], [0, -17], [4, -6], [17, -13], [14, -6], [11, -8], [12, -3], [5, -3], [3, -5], [1, -4], [2, -5], [-2, -4], [-3, -4], [2, -5], [4, -3], [1, -4], [3, -5]], [[4349, 7275], [1, -4], [4, -3]], [[4354, 7268], [4, -2], [6, -3], [1, -4], [-1, -3], [0, -3], [5, -7], [2, -4], [-3, -5], [-2, -19], [-6, -21], [-4, -16], [-7, -26], [-7, -31], [-9, -37], [-10, -37], [-8, -35], [-8, -33], [-9, -35], [-5, -21], [-3, -4]], [[4355, 5525], [2, -12], [20, -16], [6, -18], [5, -2], [27, -9], [0, -2], [-4, -4], [-4, -4], [3, -6], [5, -9], [2, -7], [3, -8], [2, -13], [2, -11], [7, -16], [2, -11], [0, -6], [5, -6], [10, -7], [15, -7], [11, -9], [8, -5], [11, -5], [8, -4], [3, -3], [1, -3], [-4, -2], [-12, -11], [-18, -17], [1, -2], [16, -12], [3, -5], [0, -7], [-6, -8], [-18, -3], [-4, -2], [0, -3], [4, -3], [-1, -8], [7, -9], [-4, -3], [-6, -4], [-1, -4], [3, -5], [7, -3], [28, -13], [2, -2], [-1, -1], [-10, -8], [-2, -5], [1, -9], [1, -5], [4, -3], [14, -2], [17, -19], [17, -18], [0, -16], [1, -18], [11, -14], [1, -11], [4, -13], [13, -8], [5, -15], [1, -9], [-1, -5], [10, -17], [1, -14], [-2, -11], [0, -5], [1, -4], [9, -4], [3, -2], [13, -1], [18, -1], [31, 4], [24, 4]], [[4686, 4966], [27, -19], [-12, -26], [-18, -41], [-15, -32], [-2, -4], [-3, -6], [-31, -9], [-45, -13], [-23, -7], [-29, -9], [-7, -7], [-8, -4], [-9, -5], [-4, -5], [-8, -4], [-8, -12], [0, -10], [5, -6], [8, -3], [3, -8], [3, -5], [6, -4], [-1, -4], [-6, -2], [-9, -2], [-6, -8], [-8, -19], [-1, -7], [9, -17], [4, -10], [12, -34], [2, -9], [-17, -13], [-6, -8], [-3, -7], [0, -5], [2, -5], [14, -16], [16, -15], [7, -7], [0, -10], [-3, -6], [-9, -2], [-19, -4], [-8, -6], [-8, -1], [-8, 5], [-9, -1], [-11, -4], [-4, -10], [-10, -17], [-8, -4], [-6, -13], [-11, -18], [-5, -11], [-3, -8], [-11, -6], [-13, -10], [-9, -10], [-4, -3], [-8, -1], [-5, -8], [-19, -14], [-4, -16], [-7, -8], [-1, -15], [-10, -27], [-2, -7], [-10, -4], [-14, -11], [-5, -8], [7, -23], [8, -24], [0, -11], [-3, -15], [-2, -6], [5, -3], [9, -2], [3, -5], [-7, -4], [-2, -8], [-7, -8], [-21, -3], [-8, 3], [-2, -2], [1, -6], [-4, -7], [-11, -18], [-7, -17], [-4, -7], [1, -6], [4, -3], [0, -4], [-6, -4], [-5, -6], [-6, -1], [-6, 2], [-7, -4], [-1, -8], [-4, -10], [-2, -11], [-2, -15], [8, -10], [11, -19], [8, -4], [9, 0], [7, -4], [4, -4], [-5, -8], [-10, -1], [-1, -4], [3, -10], [4, -9], [8, -5], [4, -13], [9, -4], [-1, -16], [9, -19], [9, -6], [4, -3], [-3, -8], [-7, -6], [-2, -11], [3, -8], [9, -7], [8, -1], [10, 3], [11, -4], [2, -6], [1, -6], [-6, -7], [-6, -14], [-2, -14], [2, -21], [3, -16], [1, -10], [0, -5], [-4, -3], [-9, -1], [-8, -1], [-7, -3], [-2, -5], [-5, -8], [-6, -7], [-10, -9], [-6, -10], [-5, -6], [0, -4], [-3, -9], [-4, -7], [-8, -24], [-10, -5], [-9, -2], [-4, -3], [3, -7], [9, -1], [3, -6], [-1, -8], [6, -7], [-1, -9], [6, -17], [-5, -12], [2, -9], [-1, -10], [-7, -7], [-15, -2], [-9, -7], [-14, -7], [-1, -6], [-3, -6], [-6, -2], [-9, 0], [-7, -1], [-10, -7], [-11, -4], [-3, -5], [1, -7], [-6, -5], [-7, -8], [-5, -8], [5, -8], [5, -15], [1, -6], [-7, -12], [-5, -7], [5, -10], [4, -6], [-4, -12], [-3, -7], [3, -14], [10, -15], [9, -14], [2, -16], [2, -13], [5, -14], [10, -5], [7, -5], [-1, -7], [-6, -8], [-8, -6], [-19, -2], [-16, -5], [-12, -4], [-10, -4], [-7, -5], [-3, -6], [2, -23], [-2, -8], [-6, -13], [-6, -9], [-3, -3], [0, -5], [-2, -4], [-6, 0], [-10, 1], [-5, -1], [-4, -3], [3, -8], [3, -13], [5, -6], [-1, -4], [-2, -10], [-6, -7], [-9, 0], [-5, -4], [-2, -5], [2, -7], [11, -6], [4, -4], [-2, -5], [-8, -2], [-5, -4], [-5, -9], [-7, -10], [-7, -7], [-1, -10], [10, -11], [-1, -11], [-1, -31], [-1, -11], [3, -18], [-4, -5], [-2, -4], [10, -13], [10, -21], [3, -8], [-1, -6], [-15, -4], [-11, -3], [-7, 4], [-5, -1], [-6, -7], [-6, -4], [-2, -5], [6, -7], [4, -12], [-7, -5], [-6, -6], [2, -7], [2, -14], [-4, -22], [6, -8], [7, -4], [22, -4], [17, -3], [4, -7], [-2, -6], [-8, -3], [-12, -3], [0, -10], [11, -9], [11, -6], [3, -6], [-1, -6], [-8, -5], [11, -12], [5, -7], [-5, -6], [-7, -8], [-7, -5], [0, -4], [-2, -10], [-1, -9], [2, -6], [71, -4], [16, -2], [9, -5], [-1, -7], [-9, -8], [-6, -14], [-14, -2], [-14, 4], [-15, -2], [-13, -1], [-18, 0], [-25, -2], [-16, 2], [-1, -5], [5, -9], [32, -2], [31, -5], [10, -10], [12, -10], [13, -7], [1, -11], [-21, -11], [-2, -8], [-27, -2], [-7, -5], [-4, -15], [3, -12], [10, -4], [7, -8], [-7, -9], [-18, -7], [-10, -6], [6, -5], [8, -7], [3, -5], [4, -11], [5, -16], [0, -7], [-5, -6], [-18, -9], [-12, -4], [-2, -12], [-1, -8], [1, -7], [8, -6], [-1, -6], [-10, -2], [-9, -3], [-9, -10], [-26, -11], [-9, -5], [1, -8], [-10, -12], [-9, -11], [-6, -9], [1, -11], [14, -4], [12, -10], [5, -13], [-9, -14], [-21, -5], [-12, -6], [-3, -5], [3, -15], [-1, -7], [-3, -7], [-5, -5], [-11, -6], [-20, -5], [-17, -3], [-7, -4], [-9, -9], [-8, -9], [2, -12], [-46, -2], [-3, -9], [-10, -7], [-4, -12], [11, -12], [5, -10], [-9, -13], [3, -12], [1, -10], [17, -11], [10, -14], [6, -12], [3, -9], [4, -5], [7, -6], [3, -8], [10, -2], [18, 7], [13, 4], [9, 2], [26, -1], [16, 4], [7, 0], [10, -3], [7, -5], [6, -11], [3, -13], [-4, -13], [-8, -3], [-2, -3], [2, -8], [8, -6], [0, -8], [-9, -18], [-6, -7], [11, -8], [9, -8], [19, -6], [16, -7], [10, -7], [-2, -9], [7, -2], [29, -1], [43, 0], [68, 0], [65, -1], [75, 0], [35, -7], [32, -7], [41, 0], [40, -7], [30, -5], [18, -2], [18, -2], [3, -7]], [[4481, 1826], [0, -31], [-1, -31], [0, -29], [0, -29], [0, -28], [0, -28], [-2, -32], [0, -24]], [[6055, 4190], [7, 5]], [[6580, 4509], [3, 0], [11, 4], [11, 15], [6, 10], [-1, 8], [-4, 6], [1, 9], [4, 23], [1, 10], [6, 7]], [[6618, 4601], [0, 6]], [[6618, 4607], [-11, 15], [-11, 13], [-6, 23], [-4, 8], [-9, 2], [-8, 8], [-10, 0], [-5, 3], [-5, 2], [-8, 0], [-6, -5], [-12, 0], [-7, -2], [-9, -3], [-8, 1], [-5, 4], [-11, 0]], [[5977, 5245], [9, 2], [4, 4], [6, 6], [9, 1], [10, 2], [4, 5], [-24, 15], [-14, 10], [8, 12], [14, 21], [14, 21], [11, 15], [3, 3], [9, 1], [-2, 8], [0, 6], [-8, 0], [9, 19], [12, 27], [9, 21], [3, 3], [7, 1], [1, 3], [-8, 3], [-5, 6], [-10, 19]], [[6048, 5479], [-17, 29]], [[6031, 5508], [-1, 10], [-7, 6], [-10, -2], [-12, 2], [-31, 16], [-20, 9], [-7, 5], [-3, 16], [-6, 18], [-3, 22], [1, 5], [17, 17], [0, 8], [2, 8], [-1, 6], [-4, 0], [-7, -3], [-11, -2], [-5, 0], [-60, 2], [-68, 1], [-57, 2], [-49, 1], [-2, 15], [-3, 24], [-2, 17], [-3, 27], [-20, 17], [-21, 19], [-8, 5], [26, 0], [19, 1], [-4, 49], [-6, 5], [-5, 16], [-3, 9], [-9, 7], [-2, 9], [2, 5], [5, 4], [3, 8], [-3, 9], [-5, 8], [-7, 7], [-12, 5], [-18, 8]], [[5611, 5929], [-28, 11]], [[5583, 5940], [-23, 8], [-8, -1], [-41, -3], [-13, -2], [-9, 2], [-31, 0], [-12, 6], [-10, 7], [-22, 17], [-3, 9], [-8, 2], [-13, -1], [-12, 1], [-25, 8], [-23, 7], [-11, 0], [-10, 4], [-18, 11], [-8, 5], [-4, 6], [-3, 8], [-7, 2], [-10, -1], [-9, -5], [-14, 3], [-17, 8], [-11, 6], [-6, 3], [-15, 4], [-14, 1], [-22, -6], [-17, 2], [-28, 3], [-24, 4], [-8, 12], [-5, 8], [-14, 5], [-11, 6], [-14, 9], [-6, 3], [-12, 3], [-11, 3], [-2, 6], [-4, 8], [-1, 2]], [[4951, 6337], [-8, 8], [-6, 1], [-8, -3], [-9, -7], [-12, -1], [-9, 5], [-32, -2], [-48, -5], [-19, -4], [-12, -2], [-14, -1]], [[4774, 6326], [-22, -8]], [[4752, 6318], [-54, -31], [-11, -5], [-13, 0], [-8, -5], [-12, -3], [-23, -12], [-12, -10], [-8, -9], [-9, 0], [-7, 2], [-23, -1], [-11, -3], [-13, -8], [-15, -16], [-6, -4], [-13, -5], [-14, -4], [-18, -6], [-8, 0], [-7, -1], [-6, 3], [-2, 5], [-9, 4]], [[4450, 6209], [-22, 2], [-33, 4]], [[4395, 6215], [-33, 0], [-17, 0]], [[5335, 5028], [-2, 1]], [[5312, 5047], [-3, 5], [-1, 0], [-61, 0], [-58, 0], [-6, -3], [-9, 0], [-6, 3], [-6, -1], [-8, -2], [-8, -5], [-22, -30], [-12, -14], [-8, -11], [-6, -21], [-2, -3], [-7, 7], [-10, 19], [-5, 10], [-6, 12], [-12, 15], [-14, 5], [-8, 1], [-12, 3], [-21, 4], [-10, 0], [-61, 1], [-5, 0], [-24, -1], [-12, 1], [-12, 8], [-29, 15], [-5, 4], [-11, 4], [-7, 0], [-4, -3], [-5, -12], [-5, -11], [-7, -7], [-20, -5], [-19, -4], [-10, -2], [-6, -5], [-2, -8], [-5, -7], [-27, -11], [-6, -4], [-3, -10], [-15, -13], [-5, -5]], [[4767, 6323], [-15, -5]], [[4450, 6209], [-22, 1], [-33, 5]], [[3729, 6562], [-8, 5], [-4, 2]], [[3743, 6617], [0, 2]], [[3743, 6619], [162, -50], [327, -60], [81, -31], [425, -146]], [[4738, 6332], [29, -9]], [[4738, 6332], [12, 10], [7, 0], [14, 9], [14, 3], [14, 11], [1, 13], [42, -2], [15, 2], [9, -1], [24, -16], [13, 3], [6, 9], [15, 4], [12, -3], [4, 5], [28, 14], [3, -1], [4, -11], [11, -7], [8, 4], [19, 20], [-3, 10], [6, 6], [15, 8], [13, -4], [13, 0], [17, 8], [9, -3], [5, 4], [23, -1], [14, 1], [-1, 26], [8, -2], [11, 3], [10, 15], [-10, 10], [11, 12], [24, 5], [3, 10], [20, 2], [0, 9], [7, 9], [91, -1], [11, -1], [15, -5], [6, -5], [6, -12], [12, -7], [2, -7], [12, 1], [12, -4], [9, -19], [19, -5], [6, -16], [19, -5], [12, -6], [6, 2], [5, 11], [11, 2], [6, 4], [16, -3], [0, -5]], [[5484, 6445], [14, -8], [4, -8], [-7, -8], [-4, -14], [4, -10], [-9, -2], [-2, -8], [9, -10], [-1, -5], [10, -19], [-5, -7], [-7, -2], [7, -14], [-2, -14], [-6, -7], [1, -21], [14, -19], [-5, -26], [5, -8], [-7, -3], [1, -9], [-1, -8], [7, -5], [147, 0], [0, -4], [8, -7], [15, 3], [14, -5], [16, 1], [9, -3], [14, -25], [-17, -16], [-11, -7], [0, -15], [6, -17], [11, -2], [8, -14], [6, -8], [-1, -12], [10, -15], [-12, -25], [-14, -11], [-5, -15], [-6, -6], [-16, -5], [-11, -10], [-1, -7], [-10, -14], [-5, -16], [-39, -19], [-10, -6]], [[5610, 5930], [-27, 10]], [[4774, 6326], [-7, -3]], [[5875, 7391], [-129, 1], [-36, -1], [-15, -10], [-5, -13], [-7, -5], [-5, -11], [-9, -9], [0, -8], [-11, -16], [0, -6], [11, -11], [1, -5], [-5, -3], [-18, -4], [-7, -10], [-18, -3], [-13, 1], [-12, 17], [-12, 8], [-4, 6], [-19, 3], [-6, 4], [-17, -1], [-3, -5], [-31, -9], [-10, -10], [-7, -23], [3, -8], [-2, -12], [-7, -15], [0, -18]], [[5445, 7221], [0, 3], [-8, 13], [-13, 10], [-16, 8], [-9, 3], [-6, 6], [-10, 4], [-15, 12], [-12, 7], [4, 8], [14, -3], [10, 8], [-2, 13], [-7, 5], [0, 14], [-5, 7], [-10, 5], [-5, 11], [-8, 8], [-2, 7], [8, 8], [-6, 8], [0, 8], [5, 7], [0, 12], [6, 6], [1, 6], [-8, 14], [1, 6], [13, 7], [-10, 17], [-1, 10], [-14, 32], [-1, 6], [-14, 8], [-10, 11], [11, 15], [-2, 9], [5, 9], [-2, 4], [-20, 7], [-22, 0], [-17, 10], [-4, 7], [-23, -1], [-12, 6]], [[5692, 7845], [6, -2], [9, -1]], [[5751, 7813], [3, -5], [4, -2]], [[5752, 7754], [-18, -9], [4, -13]], [[5780, 7547], [8, -8], [15, -11]], [[5861, 7505], [14, -114]], [[3743, 6619], [-1, 4]], [[3827, 6733], [7, 10], [13, 15]], [[3870, 6825], [3, 3], [8, 4]], [[4354, 7268], [-3, 3], [-2, 4]], [[5012, 7496], [13, 4], [13, 0]], [[5875, 7391], [3, -32], [-2, -10], [1, -18], [17, -9], [-2, -20], [7, -7], [11, -8], [8, 0], [18, -10], [8, -20], [10, -7], [9, -1], [14, -11], [11, -7], [14, -5], [12, -8], [10, -2], [25, -15], [12, -4], [17, -7], [16, -5], [9, 2], [4, -4], [11, -2], [12, -4], [11, -13], [26, -10], [12, -2], [-1, -13], [16, -5], [20, 6], [2, -9], [12, -9]], [[6228, 7122], [1, -6], [-52, -84], [-219, -348], [-12, -8]], [[5952, 6520], [-3, -23], [-6, -15], [-5, -11]], [[5938, 6471], [-6, -18], [-11, -6], [-437, -2]], [[6468, 7625], [1, -2]], [[6469, 7623], [-3, -1], [2, -16], [-10, -14], [5, -5], [-5, -8], [4, -4], [-1, -7], [5, -14], [22, 1], [12, -3], [19, 1], [23, -11], [4, 0], [13, -12], [1, -4], [10, 2], [12, -7], [4, -5], [19, -2], [3, 5], [11, -2], [2, -6], [13, 1], [3, -14], [14, 2], [4, -8], [-8, -3], [7, -22], [22, -17], [19, -3], [2, -4], [-4, -19], [2, -16], [12, -14], [6, -12], [-2, -9], [6, -7], [9, -18], [12, 1], [19, -13], [10, -10], [-1, -17], [4, -6], [11, -3], [3, -9], [-1, -15], [16, 0], [5, -5], [-3, -4], [4, -12], [17, -8], [16, 0], [11, -3], [10, 4], [13, -2]], [[6872, 7246], [-3, -2], [-1, -1]], [[6854, 7220], [-15, 2], [-15, 2]], [[6828, 7195], [19, 3], [17, 3]], [[6907, 7221], [9, 2], [7, 2]], [[6923, 7225], [11, 4], [23, 10]], [[6957, 7239], [12, 8], [1, 1]], [[6970, 7248], [7, 3], [18, 8]], [[6995, 7259], [4, 4], [1, 1]], [[7014, 7269], [6, -5], [2, -2]], [[7014, 7245], [7, -6], [1, -2], [2, -7]], [[7024, 7230], [1, -4], [0, -3]], [[7025, 7223], [3, -7], [2, -5]], [[7030, 7211], [0, -1], [15, -15]], [[7046, 7189], [-2, -5], [0, -2]], [[7044, 7182], [6, -5], [1, -1]], [[7058, 7173], [26, -17], [1, 0]], [[7085, 7156], [11, 5], [9, 4]], [[7142, 7175], [4, -1], [10, -3]], [[7156, 7171], [15, -4], [11, -2]], [[7219, 7137], [-2, -2], [-4, -7]], [[7213, 7128], [-1, -4], [-6, -22]], [[7206, 7102], [-1, -1], [-6, -5]], [[7199, 7096], [-3, -5], [-2, -3]], [[7203, 7091], [1, 1], [6, 2]], [[7220, 7104], [2, 5], [5, 12]], [[7263, 7170], [14, 3], [9, 2]], [[7326, 7211], [5, 0], [11, 0]], [[7352, 7203], [7, -4], [3, -1]], [[7360, 7228], [6, 7], [3, 3]], [[7364, 7247], [8, 4], [12, 7]], [[7409, 7284], [2, 5], [0, 1]], [[7418, 7293], [2, 0], [3, 0]], [[7444, 7295], [6, 1], [5, 2]], [[7460, 7296], [4, -2], [2, -1]], [[7491, 7297], [2, -1], [10, -7]], [[7505, 7296], [2, 2], [1, 2]], [[7508, 7300], [2, 0], [4, 2]], [[7514, 7302], [4, -1], [14, -1]], [[7542, 7296], [5, -3], [6, -4]], [[7553, 7289], [12, 0], [2, 0]], [[7579, 7289], [6, -3], [1, 0]], [[7586, 7286], [6, 0], [6, 0]], [[7598, 7286], [2, -3], [4, -3]], [[7625, 7266], [8, -2], [7, -1]], [[7640, 7263], [10, -3], [3, -1]], [[7653, 7259], [4, 0], [11, -1]], [[7683, 7252], [3, -1], [7, -1]], [[7707, 7252], [6, -5]], [[7713, 7247], [-20, -10], [-2, -8], [5, -3], [-11, -15], [3, -9], [-3, -11], [-6, -5], [-11, -1], [2, -7], [13, -1], [0, -12], [-9, -23], [-14, -11], [-6, 1], [1, -16], [-4, -2], [2, -11], [-9, -3], [-3, -8], [-13, -5], [-10, -8], [14, -11], [-11, -7], [2, -8], [-5, -11], [-11, -10], [-11, -15], [-5, 0], [-12, -7], [-4, -13], [-9, -5], [1, -3], [-6, -26], [-32, -25], [-3, 2], [-3, -11], [-7, -11], [-11, -7], [-4, -9], [-14, -13], [-10, -6], [-21, 1], [-64, -38], [-69, -40], [2, -2]], [[7383, 6790], [6, -4], [6, -1]], [[7245, 6494], [-14, -21], [-18, -22], [-16, -14]], [[7117, 6344], [-8, -12]], [[7109, 6332], [-152, 5], [-542, 24], [-196, 10], [-4, 0], [-27, 10], [-13, -2], [-10, 13], [-12, 3], [-18, 1], [-3, 5], [-2, 11], [-2, 3], [-28, 10], [-26, 17], [-14, 4], [-9, 15], [-2, 12], [-4, 7], [6, 21], [-20, 20], [-17, 37], [-7, 8], [-5, 9], [-11, 13], [-11, 7]], [[6939, 7308], [13, 1], [11, 2]], [[6963, 7311], [12, -12], [1, -1]], [[6976, 7298], [-5, -12], [-11, -26]], [[6955, 7260], [-9, -8], [-13, -12]], [[6908, 7226], [-8, -2], [-20, -7]], [[6880, 7217], [-10, -2], [-5, 0]], [[6885, 7241], [14, 10], [3, 2]], [[6902, 7253], [0, 1], [0, 16], [0, 8]], [[6902, 7278], [5, 5], [14, 16]], [[6921, 7299], [10, 5], [8, 4]], [[7367, 7321], [-2, -3], [-3, -7]], [[7351, 7283], [-1, -1], [-1, -4]], [[7327, 7251], [-1, -1], [-2, -1]], [[7308, 7239], [1, -4], [0, -2]], [[7309, 7233], [1, 0], [3, -5]], [[7313, 7228], [-2, -3], [-2, -4]], [[7309, 7221], [-11, -8], [-3, -1]], [[7273, 7209], [-4, 3], [-9, 7]], [[7256, 7204], [-3, -3], [-1, -2]], [[7236, 7200], [-3, 1], [-6, 3]], [[7227, 7204], [-11, 3], [-3, 2]], [[7213, 7209], [-3, -11], [0, -2]], [[7201, 7187], [-5, -1], [-4, -1]], [[7192, 7185], [-6, -1], [-8, -1]], [[7155, 7182], [-9, 4], [-5, 2]], [[7133, 7188], [-2, -1], [-4, -3]], [[7127, 7184], [-12, -1], [-21, 0]], [[7079, 7178], [-2, 0], [-7, 2], [-14, 9]], [[7054, 7195], [-7, 12], [-1, 1]], [[7046, 7208], [-1, 1], [-6, 14]], [[7039, 7223], [-5, 12], [0, 2]], [[7034, 7237], [0, 1], [4, 11]], [[7038, 7249], [4, 0], [5, 0]], [[7047, 7249], [5, -1], [5, -1]], [[7057, 7255], [-9, -1], [-7, 0]], [[7030, 7261], [-1, 6], [-1, 5]], [[7031, 7295], [0, 2], [1, 3]], [[7032, 7300], [5, 5], [2, 1]], [[7039, 7306], [1, 1], [2, 5]], [[7040, 7318], [1, 4], [2, 7]], [[7043, 7329], [6, 9], [1, 1]], [[7227, 7345], [9, 4], [4, 1]], [[7240, 7350], [4, 0], [10, 1]], [[7254, 7351], [5, 0], [9, -1]], [[7315, 7345], [14, -1], [15, -1]], [[7010, 7333], [-2, 1], [-11, 6]], [[6997, 7340], [-2, 2], [-1, 2]], [[6996, 7350], [2, 4], [2, 3]], [[7022, 7362], [4, 1], [7, 0]], [[7033, 7363], [7, -1], [7, 0]], [[7213, 7376], [2, 0], [13, -2]], [[7222, 7356], [-19, -2], [-19, -1]], [[7184, 7353], [-11, 3], [-7, 2]], [[7166, 7358], [2, 1], [2, 3]], [[7170, 7362], [5, 3], [8, 4]], [[7183, 7369], [13, 4], [3, 1]], [[7199, 7374], [4, 1], [10, 1]], [[7165, 7368], [-10, -2], [-1, -1]], [[7154, 7365], [-11, 0], [-1, 0]], [[7126, 7371], [-9, 0], [-16, 0]], [[7101, 7371], [-6, 1], [-1, 0]], [[7094, 7372], [-1, 4], [0, 6]], [[7093, 7382], [1, 1], [9, 9]], [[7103, 7392], [15, 0], [6, 0]], [[7159, 7400], [17, -3], [4, -1]], [[7079, 7367], [-7, 2], [-19, 4]], [[7055, 7389], [7, 3], [5, 2]], [[7067, 7394], [6, 4], [5, 4]], [[7078, 7402], [2, 6], [1, 4]], [[7081, 7412], [0, 5], [1, 9], [3, 3]], [[7089, 7430], [1, -1], [2, 0]], [[7092, 7429], [0, -6], [1, -15]], [[7137, 7430], [0, -3], [-1, -4]], [[7136, 7423], [-1, -1], [-15, -13]], [[7102, 7409], [0, 10], [0, 3]], [[7102, 7422], [1, 1], [3, 6]], [[7106, 7429], [9, 1], [11, 1]], [[6625, 4947], [-8, -18], [-14, -16], [-19, -9]], [[6553, 4844], [-17, -7]], [[6138, 5026], [-16, 4], [-14, 2]], [[6108, 5032], [-13, -2], [-9, 1]], [[6048, 5479], [-13, 22]], [[6035, 5501], [17, 0], [4, -8], [16, -6], [3, -3], [7, 2], [7, 0]], [[6172, 5538], [7, 7], [11, 0], [25, 2], [8, -2], [15, 3], [12, 7], [21, 4], [11, -3], [5, -6], [22, -4], [6, -5], [26, -4], [11, -12], [28, -8], [22, -11], [15, 1], [25, 3], [13, 5], [4, 5], [12, 4], [25, 0], [14, -7], [5, -9], [10, 0], [12, 3], [13, 1], [9, 4], [6, 8], [13, 6], [19, 16], [10, 6], [9, -1], [-10, -40], [-15, -5], [-19, -22], [2, -4], [16, -1], [20, -7], [17, 2], [16, -2], [27, 1], [23, -3], [11, 1], [2, 0]], [[6706, 5471], [1, -8], [-1, -21], [3, -6], [6, -4], [12, 5], [17, -5], [0, -8], [-8, -7], [-7, -11], [3, -5], [16, -2], [21, -2], [16, 2], [23, -12], [10, 1], [25, -9], [5, -5], [21, -5], [10, -6], [29, -9], [14, -1], [20, -3], [13, -9], [25, -3], [14, -9], [10, -15]], [[6469, 7623], [1, -3]], [[6483, 7613], [2, -1], [1, -1]], [[6492, 7609], [1, -1], [4, -4]], [[6497, 7604], [2, -1], [10, -3]], [[6509, 7600], [13, -4], [7, -2]], [[6538, 7594], [6, -1], [3, 0]], [[6552, 7591], [3, 2], [3, 1]], [[6588, 7607], [5, 3], [2, 1]], [[6595, 7611], [2, 1], [3, 3]], [[6600, 7615], [3, 0], [1, 1]], [[6609, 7611], [6, -2], [1, 0]], [[6616, 7609], [6, -1], [11, -2]], [[6666, 7614], [3, -2], [4, -3]], [[6681, 7600], [3, 0], [4, 0]], [[6688, 7600], [6, 0], [8, -1]], [[6732, 7606], [1, 0], [12, 6]], [[6745, 7612], [4, 1], [7, 3]], [[6773, 7634], [2, 3], [2, 2]], [[6777, 7639], [0, 3], [0, 4]], [[6800, 7681], [4, 5], [2, 3]], [[6818, 7708], [3, 2], [3, 2]], [[6824, 7712], [5, 5], [4, 5]], [[6840, 7731], [1, 2], [16, 19]], [[6857, 7752], [0, 1], [1, 4]], [[6864, 7761], [2, 4], [1, 0]], [[6874, 7771], [2, 1], [5, 3]], [[6881, 7775], [2, 3], [1, 3]], [[6884, 7781], [4, 5], [2, 2]], [[6906, 7795], [4, 5], [10, 13]], [[6920, 7813], [1, 5], [0, 3]], [[6968, 7799], [7, -15], [13, -30]], [[7024, 7647], [1, -6], [1, -2]], [[7029, 7628], [5, -6], [3, -4]], [[7046, 7597], [-2, -2], [-4, -3]], [[7040, 7592], [5, 0], [3, 0]], [[7060, 7578], [1, -2], [5, -5]], [[7066, 7571], [10, -10], [1, -1]], [[7115, 7556], [8, -3], [11, -3]], [[7157, 7490], [-1, -1], [-5, -3]], [[7151, 7486], [-10, -5], [-6, -3]], [[7026, 7386], [-6, -1], [-8, 0]], [[7012, 7385], [-1, -1], [-7, -3]], [[6985, 7364], [-11, -2], [-15, -3]], [[6959, 7359], [-1, -2], [-1, -8]], [[6957, 7349], [-12, -18], [-3, -5]], [[6942, 7326], [-3, -2], [-11, -10]], [[6920, 7310], [-9, -10], [-12, -13]], [[6899, 7287], [-1, -4], [-2, -5]], [[6882, 7250], [-10, -4]], [[7069, 7581], [2, 5], [1, 6]], [[7109, 6332], [-15, -20]], [[7057, 6243], [-2, -33]], [[7028, 5935], [-9, -20], [1, -20]], [[6958, 5789], [-26, -5]], [[6797, 5675], [-17, -9], [0, -6], [-19, -7], [-2, -7], [10, -7]], [[6691, 5556], [-6, -10], [-2, -11]], [[6697, 5488], [9, -17]], [[6035, 5501], [-4, 7]], [[5611, 5929], [-1, 1]], [[6697, 4985], [10, 0], [14, 7], [36, -3], [12, 3], [13, -5], [11, 2], [18, -2], [8, 2], [6, -4], [8, 4], [3, 6], [11, 2], [12, -3], [16, -6], [18, -1], [7, -5], [8, 2], [13, -4], [28, 4], [24, -10], [26, -5], [17, -3], [14, -13], [8, -1], [14, 3], [29, -2], [8, 3], [11, -4], [25, 1], [21, 3], [2, -5], [6, -3], [1, -5], [26, -7], [13, -16], [3, -16], [-6, -11], [7, -14], [8, -7], [-2, -13], [-5, -3], [28, -23], [10, -6], [0, -8], [9, -11], [7, -2], [-2, -8], [-10, -12], [1, -14], [12, -3], [18, 0], [9, 2], [28, 1], [7, -4], [24, 2], [18, -7], [-8, -7], [-3, -26], [8, -2], [14, 13], [21, -8], [5, 2], [6, -19], [14, -4], [0, -5], [2, -1]], [[7417, 4706], [-7, -5], [-11, -8]], [[7367, 4694], [-3, -2], [-4, -2]], [[7329, 4685], [13, -2], [13, -2], [8, -2], [3, -1]], [[7350, 4650], [-3, 0], [-14, -3]], [[7340, 4644], [5, -6], [-1, -5]], [[7344, 4633], [-9, 1], [-38, 0], [-4, -4], [-24, 2], [-14, -4], [-13, -8], [-22, -7], [-5, -5], [-15, 0], [-18, 6], [-5, 4], [-18, 9], [-10, 3], [-34, -4], [-18, 0], [-8, -4], [-12, 6], [-12, 0], [-15, -4], [-13, -17], [-5, 1], [-18, -6], [-7, 3], [-18, 1], [-25, -11], [-6, -8], [1, -8], [6, -12], [-6, -4], [-20, -6], [-12, 11], [-52, 0], [-20, 2], [-27, 14], [-37, 2], [-12, 3], [-14, 0], [-5, 2], [-20, 5], [-14, -3], [-11, 1], [-19, -2], [-22, 12], [-11, 2], [-15, -5], [-8, 0], [-21, 3], [-1, 0]], [[6618, 4604], [0, 3]], [[6508, 4724], [-2, 5], [6, 21]], [[7528, 5680], [-80, -1], [-46, 1], [-10, 0], [2, 8], [-6, 13], [11, 10], [-5, 5], [8, 21], [59, 0], [49, -1], [2, -3], [15, -6], [0, -13], [-6, -13], [-2, -16], [9, -5]], [[7119, 6064], [4, -10], [-10, -9], [1, -9], [-11, -14], [0, -11], [5, -4], [27, -10], [11, 0], [8, -5], [33, -11], [24, -3], [19, -5], [4, 12], [8, 14], [23, 28], [18, 9], [2, -10], [10, -4], [15, -9], [4, -6], [-1, -7], [5, -11], [1, -12], [-2, -15], [5, -3], [8, 5], [4, 17], [10, 2], [15, -4], [16, 1], [21, 10], [8, -1]], [[7404, 5989], [-1, -9], [14, -1], [10, -6], [10, 1], [11, -5], [25, -7], [4, 4], [-9, 18], [11, 3], [12, -13], [17, -1], [21, 5], [29, 11], [17, 3], [20, 0], [11, 4], [15, 12], [8, 3], [22, 4], [19, 2], [10, 4]], [[7680, 6021], [21, -13], [2, -6], [-11, -5], [-4, -14], [0, -7], [14, -3], [-2, -11], [-13, -5], [-12, -27], [1, -22], [6, -14], [7, -8], [6, -11], [13, -8], [4, -4], [13, -6], [-6, -12], [0, -7], [7, -17], [-3, -6], [-13, -12]], [[7710, 5803], [-8, -6], [-16, -1], [-15, 2], [-3, 7], [-10, 8], [-18, 6], [-10, -8], [4, -8], [-3, -5], [4, -13], [-12, -4], [-14, 6], [-14, 1], [-9, -4], [-6, -19], [6, -1], [5, -9], [-11, -12], [0, -11], [9, -5], [6, -27], [-5, -4], [-31, -6], [-7, 2], [-14, -12], [-10, 0]], [[7528, 5680], [-9, 5], [2, 16], [6, 13], [0, 13], [-15, 6], [-2, 3], [-49, 1], [-59, 0], [-8, -21], [5, -5], [-11, -10], [6, -13], [-2, -8], [10, 0], [46, -1], [80, 1]], [[7528, 5680], [-6, -12], [2, -8], [-3, -8], [-12, -11], [-3, -9], [7, -8], [20, -10], [15, -27], [1, -6], [-11, -5], [2, -5], [-14, -8], [-16, -13], [-4, -8], [-7, 2], [-4, -13], [10, -8], [7, 3], [17, -5], [3, -13], [-8, -8], [-4, -8], [1, -6], [10, -19], [-6, -3]], [[7129, 5406], [-22, -5], [-17, -9]], [[7500, 6593], [-1, -9], [6, -9], [7, 0], [9, -14], [6, 0], [4, -7], [18, -13], [10, -13], [9, -7], [5, 1], [16, 8], [18, 4], [25, 2], [12, -6], [3, -12], [-5, -11], [-1, -10], [-5, -5], [6, -8], [-23, 0], [-19, -4], [-6, -5], [-6, -11], [-6, -17], [2, -9], [-23, -15], [-2, -6], [6, -4], [14, 0], [14, -10], [2, -15], [11, -11], [27, -9], [4, -6], [-9, -10], [-7, -1], [-3, -9], [23, -12], [6, -10], [-1, -5], [18, -18], [9, -1], [13, 0], [15, -3], [8, -6], [12, -5]], [[7721, 6282], [27, -2]], [[7748, 6280], [-14, -14], [-26, -11], [-7, -1], [-31, -19], [10, -13], [-2, -3], [-17, -6], [-14, -22], [-9, -6], [-6, -8], [3, -7], [9, -4], [9, -9], [33, -4], [16, -6], [-4, -7], [-19, -7], [-5, -4], [4, -8], [13, 1], [8, -3], [3, -7], [-19, -7], [-5, -5], [-11, -5], [-2, -4], [-1, -22], [4, -9], [23, -8], [0, -11], [-11, -11], [0, -9]], [[7090, 5278], [141, -13], [14, -1]], [[7524, 5250], [12, -6], [1, -3], [-4, -8], [-4, -15], [6, -6], [14, -5], [6, -11], [-15, -18], [-1, -12], [11, -7], [3, -16], [10, -6], [9, -16], [-2, -6], [16, 0], [11, 4], [19, -3], [4, 2], [7, -6], [14, -3], [2, -7], [-10, -15], [-6, -1], [-3, -10], [-6, -4], [1, -10], [6, -8], [-6, -7], [9, -9], [-9, -7], [-5, -13], [8, -9], [13, -3], [1, -4], [13, -4], [8, -11], [-11, -7], [15, -7], [1, -10], [10, -4], [19, 4], [2, -5], [18, 2], [16, 6], [7, -4], [9, 0], [7, 6], [0, 8], [-8, 2], [18, 9], [6, 0], [7, 0], [8, -3], [8, 4], [9, -4], [20, 4], [4, 5], [26, 10], [19, 2], [13, 4]], [[7880, 5009], [9, -1], [17, -15], [2, -4], [14, -2], [46, 3], [7, -12], [-11, -5], [-4, -7], [-12, -4], [-16, 0], [-17, -2], [-19, -6], [-11, -6], [-1, -16], [-8, -7], [11, -15]], [[7887, 4910], [-24, -3]], [[7863, 4907], [-21, -12], [-16, -9]], [[7810, 4884], [-6, -4], [-8, -5]], [[7794, 4867], [-2, -2], [-2, -3]], [[7781, 4862], [-9, 2], [-11, 2]], [[7590, 4817], [-11, -7], [-28, -20]], [[7551, 4790], [-57, -27], [-8, -4]], [[7445, 4737], [-2, 0], [-3, -1]], [[7440, 4736], [-4, -1], [-7, -2]], [[7441, 4729], [-1, -2], [-2, -8]], [[7424, 4711], [-7, -5]], [[7823, 4860], [-1, -1], [-1, -2]], [[7821, 4857], [-1, -2], [-1, -2]], [[7813, 4851], [-7, -1], [-9, -2]], [[7792, 4853], [5, 4], [16, 13]], [[7713, 7247], [2, -2], [2, -2]], [[7717, 7243], [26, -6], [2, -1]], [[7745, 7236], [18, -9], [1, -1]], [[7764, 7226], [7, 1], [6, 1]], [[7806, 7203], [1, -6], [2, -10]], [[7816, 7189], [2, 2], [4, 5]], [[7881, 7192], [3, -4], [2, -2]], [[7886, 7186], [7, 0], [3, -1]], [[7906, 7184], [3, -3], [6, -7]], [[7922, 7152], [-1, -2], [-5, -5]], [[7911, 7141], [-3, -4], [-3, -4]], [[7898, 7132], [-3, -1], [-4, -2]], [[7916, 7133], [1, 0], [8, 4]], [[7945, 7119], [-11, -3], [-9, -1]], [[7925, 7115], [0, -3], [0, -5]], [[7908, 7077], [-9, -30], [-3, -10]], [[7896, 7037], [0, -5], [0, -2]], [[7910, 7037], [9, 7], [18, 14]], [[7937, 7058], [2, 6], [6, 15]], [[7945, 7079], [9, 19], [1, 3]], [[7967, 7108], [5, 0], [2, 0]], [[7984, 7105], [1, -5], [0, -2]], [[7967, 7078], [3, -4], [2, -2]], [[8009, 7096], [4, -1], [6, 0]], [[8038, 7103], [37, 1], [2, 0]], [[8080, 7114], [5, 2], [3, 2]], [[8109, 7117], [18, -4], [24, -5]], [[8151, 7108], [11, -5], [4, -2]], [[8188, 7095], [1, 0], [11, -7]], [[8249, 7074], [24, -1], [12, -1]], [[8285, 7072], [5, 2], [13, 5]], [[8303, 7079], [4, -2]], [[8307, 7077], [-3, -9], [7, -9], [-4, -11], [-11, -8], [-12, -12], [-14, -4], [0, -4], [-16, -14], [-21, -1], [-13, -3], [-7, 3], [-10, -13], [-8, -5], [-7, -9], [2, -9], [-10, -14], [-17, -12], [-5, -11], [-14, -11], [5, -16], [8, -1], [4, -10], [-7, -14], [-5, -3], [3, -7], [-3, -5], [7, -6], [7, -11], [3, -17], [4, -6], [-4, -16], [-12, -9], [-19, -21], [-7, -3], [2, -10], [-4, -6], [5, -30], [11, -7], [5, -6], [16, -8], [-2, -19], [2, -4], [-9, -15], [0, -5], [-9, -8], [-22, -4], [-9, 3], [-15, -5], [-23, -5], [-14, 10], [-17, 6], [-19, -1], [-20, -6], [-12, -1], [-3, -6], [-5, 2], [-10, -8], [-1, -4], [-13, -9], [-7, -11], [-20, -3], [-18, -11], [-20, -16], [-14, 4], [-14, -9], [-16, -5], [-39, -7], [-25, -12], [-10, -21], [-5, -29], [-11, -11], [-1, -6], [-11, -13], [-2, -13], [-10, -15], [-15, -9], [-8, -15], [6, -9], [1, -11], [4, -6], [1, -16], [10, -5], [4, -9], [-5, -8], [-3, -31], [0, -18], [-6, -3], [0, -6], [-8, -10], [3, -3], [-2, -11]], [[7918, 7060], [0, 3], [-2, 5]], [[7927, 7081], [2, 0], [1, 1]], [[7930, 7072], [-1, -8], [-1, -6]], [[7928, 7058], [-13, -9], [-1, -1]], [[7859, 7234], [5, 0], [8, -1]], [[7872, 7233], [1, -2], [0, -2]], [[7880, 5009], [15, 5], [9, -1], [9, 6], [10, 1], [13, 5], [21, 2], [11, -1], [18, 8], [28, 7], [12, 4], [35, 0], [15, 1], [17, 6], [30, -3], [10, -4], [112, 37]], [[8245, 5082], [-1, 8], [-12, 1], [1, 4], [9, 11], [0, 9], [11, 7], [6, 18], [12, 17], [-6, 4], [6, 4], [14, 0], [5, 2], [5, 8], [9, 6]], [[8304, 5181], [17, -5], [5, -16], [-3, -7], [3, -7], [39, -10], [7, 2], [18, -5], [28, 2], [14, -7]], [[8432, 5128], [-11, -24]], [[8429, 5061], [0, -2], [-1, -7]], [[8428, 5052], [-12, -6], [-6, -3]], [[8410, 5043], [-29, -7], [-37, -10]], [[8344, 5026], [-15, -6], [-2, 0]], [[8285, 4984], [1, -7], [1, -2]], [[8279, 4952], [-11, 1], [-1, 0]], [[8267, 4953], [-7, 0], [-58, 0]], [[8202, 4953], [-11, -1], [-25, -2]], [[8166, 4950], [-13, 0], [-5, 0]], [[8130, 4976], [-6, 0], [-4, 0]], [[8120, 4976], [-4, -1], [-7, -2]], [[8109, 4973], [0, -1], [-2, -4]], [[8107, 4968], [1, -2], [0, -1]], [[8108, 4965], [1, -1], [3, -4]], [[8112, 4960], [1, -3], [1, -4]], [[8114, 4953], [-2, -2], [-2, -3]], [[8110, 4948], [-3, 0], [-18, -1]], [[8037, 4940], [-13, -2], [-10, -2]], [[8014, 4943], [6, 0], [9, -1]], [[8029, 4942], [15, 3], [1, 1]], [[7992, 4953], [-11, -6], [-3, -1]], [[7947, 4946], [-11, -1], [-28, -4]], [[7908, 4941], [-2, -2], [-4, -3]], [[7902, 4936], [0, -2], [1, -9]], [[7903, 4925], [5, -1], [2, -1]], [[7911, 4914], [-3, -1], [-4, -1]], [[7904, 4912], [-10, -2], [-7, 0]], [[7968, 4927], [-4, -1], [-11, -1]], [[7953, 4925], [-5, 3], [0, 1]], [[7960, 4935], [2, 2], [3, 2]], [[7965, 4939], [1, 0], [2, -1]], [[7968, 4938], [1, -1], [3, -2]], [[7972, 4935], [6, -2], [3, -1]], [[7985, 4929], [-3, 0], [-5, 0]], [[8307, 7077], [18, -5]], [[8336, 7065], [18, -3], [5, -1]], [[8359, 7061], [4, -1], [19, -1]], [[8382, 7059], [2, 0], [5, 2]], [[8389, 7061], [2, -17], [-17, -21], [-7, -5], [-3, -12], [9, -16], [4, -13], [10, -10], [8, -25], [7, -8], [9, -6], [6, -16], [-9, -10], [-14, -12], [-1, -8], [2, -12], [7, -9], [-1, -11], [9, -9], [1, -7], [8, -10], [-1, -15], [6, -6], [11, -6], [4, -19], [0, -8], [4, -24], [0, -11], [12, -26], [2, -13], [3, -12], [8, -8], [1, -9], [14, -5], [19, -3], [9, -8], [-3, -10], [-11, -10], [-10, -21], [7, -13], [0, -7]], [[8494, 6590], [-16, -1], [-6, -3], [0, -9], [10, -13], [-6, -13], [16, -8], [3, -7], [-2, -14], [-7, -9], [-22, -13], [-12, -13], [-8, 2], [-5, -9], [-14, 2], [-9, -12], [-11, -2], [-8, -9], [-22, -8]], [[8375, 6451], [-4, -8], [-10, -8], [-4, -8], [-9, -4], [-7, 1], [-18, -2], [0, -16], [-9, -4], [-8, -8], [-9, 4], [-18, 0], [-16, -8], [-23, -2], [-17, -11], [-8, -9], [-12, 1], [-7, -8], [-19, 5], [-14, -3], [-11, 3], [-1, 4], [-13, 4], [-2, 5], [-12, 2], [-9, -5], [-27, 1], [-5, 8], [-5, 4], [-13, -7], [-9, 3], [-8, -3], [-5, -8], [-17, 1], [-2, -7], [6, -7], [8, -19], [7, -6], [-5, -16], [2, -8], [-7, -8], [-2, -7], [-5, -3], [-16, -24], [-12, -3], [-18, -14], [-14, -4], [-13, 3], [-15, 1], [-18, -4], [-11, -6], [-4, -5], [-15, -5], [-5, -8], [-9, -2], [-12, 2], [-16, -4], [-39, 12], [-18, 17], [-4, 14], [-6, 8], [-13, 8], [-22, 0]], [[7710, 5803], [7, -13], [-14, -27], [15, 6], [6, 6], [23, -1], [8, 2], [8, 8], [15, 12], [12, -1], [21, 9], [13, 12], [18, 3], [31, 12], [7, 9], [39, 18], [29, 8], [6, 2], [15, 1], [7, -3], [46, -5], [8, -3], [-2, -3]], [[8019, 5838], [-3, -10], [3, -3], [5, -4], [17, -4], [31, -6], [6, 1], [9, 8], [22, 7], [40, -4], [9, -8], [37, -20], [7, 1], [21, -11], [24, -7], [13, 0], [12, -8], [21, 1], [20, 6], [63, -40], [4, -24], [25, -6], [18, 4], [13, 7], [7, -2], [9, 1], [10, -6], [13, 2], [14, -4], [5, -4], [9, 2], [21, -5], [13, 1], [10, -8], [5, 0], [18, -11], [10, 0], [10, -12], [-11, -21], [-16, -9], [-11, -11], [-1, -5], [-17, -2], [-3, -5], [-1, -14], [6, -9], [-8, -5], [-25, 1], [-7, -6], [-7, -22], [1, -13], [-6, -5], [-1, -9], [16, -3], [3, -14], [10, 0], [4, -7], [14, -10], [11, -5], [3, -5], [-6, -11], [3, -6]], [[8541, 5476], [-1, 0], [-30, 9], [-7, -4], [-9, 3], [-19, -6], [-8, 1], [-9, -3], [-17, 0], [19, -13], [1, -4], [-10, -2], [-11, 5], [-15, -7], [-6, -1], [-11, -12], [1, -4], [15, -8], [3, -5], [-4, -19], [15, -6], [0, -12], [-23, -2], [-15, 2], [-6, -5], [10, -5], [9, 2], [11, -10], [-4, -6], [16, -12], [2, -15], [-4, -19], [-13, -2], [2, -6], [-18, -12], [-4, -21], [-17, -10], [-8, -24], [-6, -3], [-45, 1], [-19, -17], [8, -6], [0, -12], [-8, -9], [6, -2], [-10, -12], [2, -2]], [[8541, 5476], [3, -4], [73, -33], [1, 0]], [[8618, 5439], [-3, -11], [-7, -22]], [[8608, 5406], [0, -16], [-1, -6]], [[8613, 5339], [-1, -7], [-3, -12]], [[8609, 5320], [-3, -6], [-4, -6]], [[8528, 5232], [-1, -2], [-2, -12]], [[8467, 5174], [-8, -6], [-1, -1]], [[8458, 5167], [-5, -11], [-1, -2]], [[8434, 5132], [-2, -4]], [[8375, 6451], [6, -3], [24, 3], [8, -1], [0, -6], [14, -1], [4, -3], [12, -4], [0, -19], [5, -6], [1, -6], [21, -7], [2, -14], [-8, -8], [9, -5], [17, 1], [22, 7]], [[8540, 6410], [19, 1]], [[8650, 6467], [16, -1], [11, -8]], [[8677, 6458], [22, -8], [38, -9]], [[8768, 6431], [6, 4], [5, 3]], [[8825, 6384], [35, -17]], [[8860, 6367], [-10, -10], [7, -5], [-5, -8], [10, -16], [9, -4], [0, -4], [17, -13], [-1, -24], [-8, -12], [2, -14], [6, -8], [-4, -5], [-23, -8], [-5, 0], [-14, 5], [-12, -1], [-8, -12], [2, -7], [8, -9], [10, -5], [7, -15], [11, -7], [-4, -13], [6, -4], [17, -4], [7, -9], [20, -4], [4, 4], [8, -2], [23, 5]], [[8940, 6158], [-1, -4], [-7, -13]], [[8901, 6094], [-35, -36], [-4, -3]], [[8862, 6055], [0, -1], [-8, -12]], [[8822, 6016], [-2, -2], [-21, -11]], [[8792, 6003], [-5, 1], [-2, 0]], [[8785, 6004], [-2, 13], [-2, 7]], [[8758, 6039], [-5, -7], [-3, -6]], [[8744, 6016], [2, -4], [1, -3]], [[8747, 6009], [-5, -7], [-5, -6]], [[8737, 5996], [0, -4], [0, -8]], [[8737, 5984], [-14, -11], [-4, -2]], [[8704, 5949], [-1, -3], [-2, -9]], [[8701, 5937], [0, 1], [0, -1]], [[8701, 5937], [5, 2], [2, 2]], [[8713, 5929], [-4, -7], [-1, -2]], [[8708, 5920], [0, -3], [0, -22]], [[8708, 5895], [-1, -4], [0, -1]], [[8707, 5890], [4, -6], [1, -1]], [[8705, 5825], [3, -16], [3, -13], [1, -11], [2, -23]], [[8714, 5762], [6, -26], [2, -7]], [[8722, 5729], [1, -6], [7, -23]], [[8730, 5700], [0, -1], [0, -1]], [[8730, 5698], [-5, -16], [-6, -18]], [[8684, 5560], [0, -7], [-1, -8]], [[8674, 5489], [-11, -4], [-9, -3]], [[8620, 5447], [-2, -8]], [[8727, 5950], [-2, -3], [-3, -4]], [[8717, 5944], [-1, 1], [-2, 3]], [[8714, 5948], [-3, 4], [-1, 0]], [[8750, 5989], [-4, -2], [-2, 0]], [[8758, 6002], [2, 5], [1, 5]], [[8768, 6007], [2, -4], [1, -1]], [[8770, 6000], [-14, -8], [-6, -3]], [[8389, 7061], [11, 3]], [[8400, 7064], [3, 0], [43, 2]], [[8537, 7072], [39, -6], [27, -13]], [[8728, 6999], [9, -5], [21, -11]], [[8788, 6976], [7, -6], [10, -10]], [[8805, 6960], [11, -7], [1, -1]], [[8817, 6952], [3, -2], [29, -26]], [[8956, 6872], [7, -9], [3, -3]], [[8966, 6860], [-44, -11], [-8, -3], [-18, -11], [-27, -42], [-20, -16], [-4, -8], [0, -8], [-7, -12], [-25, -21], [-9, -2], [-7, 4], [-11, -7], [-3, -6], [-9, -8], [-4, -15], [11, 2]], [[8781, 6696], [-7, -10], [-11, -23], [5, -7], [-5, -8], [-10, -6], [-1, -11], [6, -3], [2, -14], [10, -6], [8, -2], [2, -6], [-7, -14], [-7, -3], [-1, -8], [-9, -9]], [[8756, 6566], [-17, -5], [-6, -4], [-15, -15], [-4, 3], [-11, -4], [-5, 11], [-19, 7], [-9, 6], [-7, 8], [-26, 8], [-17, 11], [-28, 2], [-15, -1], [-16, -5], [-15, -1], [-13, 3], [-39, 0]], [[8860, 6367], [5, -3]], [[8865, 6364], [73, -26], [33, -11], [25, -7]], [[9042, 6288], [7, 0], [6, -4]], [[9079, 6270], [1, -3], [3, -4]], [[9083, 6263], [-11, -3], [-21, -7]], [[9032, 6244], [-14, -9], [-10, -6]], [[8986, 6204], [-3, -2], [-2, -1]], [[8981, 6201], [-6, 1], [-2, 1]], [[8973, 6190], [-8, -8], [-11, -12]], [[8954, 6170], [-5, -2], [-1, -1]], [[8948, 6173], [2, 2], [2, 2]], [[8953, 6182], [0, 2], [0, 3]], [[8936, 6168], [2, -3], [2, -7]], [[8825, 6384], [9, 8], [9, 9], [18, 4], [22, 18], [1, 8], [8, 4], [10, -12], [7, -1], [16, 4], [7, -6], [11, -4], [30, -21], [26, -7], [9, -5], [5, 7], [10, 3], [36, -3], [20, 8], [13, 1], [12, 6], [4, 6], [16, 8], [1, 5], [15, 7], [6, -1], [13, 4], [11, 0], [12, -5], [16, 6], [19, 3], [12, -5], [13, -1], [21, -3]], [[9263, 6429], [-1, -1]], [[9236, 6396], [-15, -13], [-22, -19]], [[9199, 6364], [-17, -13], [-6, -5]], [[9176, 6346], [-4, 0], [-6, -1]], [[9166, 6345], [-7, 3], [-1, 0]], [[9158, 6348], [1, -2], [5, -7]], [[9164, 6339], [-4, -5], [-2, -3]], [[9158, 6331], [-9, -9], [-15, -15]], [[9134, 6307], [-19, -12], [-5, -4]], [[9110, 6291], [-8, -8], [-17, -19]], [[9083, 6264], [0, -1]], [[8966, 6860], [8, -9]], [[9019, 6845], [14, -8], [2, -1]], [[9035, 6836], [9, -2], [14, -3]], [[9145, 6836], [38, -5], [23, -3]], [[9229, 6815], [16, -24], [6, -9]], [[9271, 6717], [1, -2], [14, -20]], [[9286, 6695], [1, -5], [2, -7]], [[9289, 6683], [-11, -3], [-30, 3], [-7, -3], [-23, 7], [-29, 4], [-7, -2], [-30, -2], [-46, 6], [-6, 11], [-13, 0], [-16, -8], [-3, -10], [10, -15], [-12, -2], [0, -10], [4, -5], [-10, -15], [-12, -1], [-8, -5], [-8, 5], [4, 11], [-7, 6], [-22, 2], [-6, 5], [-8, -5], [-16, -3], [-9, -5], [-8, 9], [-9, 5], [-26, 1], [2, 14], [11, 4], [5, 13], [31, 29], [0, 7], [-12, 2], [-23, -7], [-8, 1], [-23, -4], [-17, -6], [-5, -12], [-7, -5], [-23, -9], [-8, -2], [-11, -8], [-15, 4], [-6, -2], [-22, 10], [-13, 3]], [[8756, 6566], [8, -6], [8, -2], [0, -5], [9, -3], [11, 3], [5, -1], [8, 5], [8, -8], [0, -5], [7, -3], [4, 5], [8, 0], [3, 4], [9, -5], [4, 6], [12, -1], [11, 9], [15, 5], [12, 9], [22, 8], [17, 13], [8, 0], [5, 6], [13, 2], [15, -8], [19, -5], [0, -12], [-26, -7], [-2, -7], [6, -12], [-8, -6], [-17, -19], [19, 4], [9, -2], [3, -20], [9, -7], [14, -5], [19, 6], [7, 0], [22, 15], [-4, 7], [13, 10], [17, 1], [4, 9], [12, 2], [10, -2], [7, 5], [7, -5], [6, 5], [7, -5], [16, 1], [5, -2], [6, 11], [7, -1], [18, 3], [4, 0], [18, 6], [9, 21], [17, 0], [15, 8], [29, -3], [13, -11], [17, -4]], [[9305, 6573], [0, -5]], [[9302, 6552], [2, -4], [4, -9]], [[9308, 6529], [-5, -10], [-3, -3]], [[9290, 6483], [-14, -28], [-13, -26]], [[9289, 6683], [6, -29]], [[9302, 6641], [0, -5], [1, -5]], [[9309, 6628], [3, -23], [1, -4]], [[9305, 6575], [0, -2]], [[7344, 4633], [-2, -9], [-3, -12]], [[7321, 4603], [4, -5], [3, -4]], [[7328, 4594], [1, -1], [6, -5]], [[7331, 4567], [0, -2], [0, -8]], [[7343, 4520], [2, -3], [2, -4]], [[7343, 4498], [0, -1], [3, -10]], [[7314, 4374], [0, -5], [0, -9]], [[7314, 4360], [-5, -2], [-27, -11]], [[7214, 4307], [-9, -7], [-22, -19]], [[7183, 4281], [-14, 6], [-8, 5], [-12, 3], [-18, -5], [6, -8], [-2, -4], [-17, 7], [1, 10], [13, 11], [9, -1], [9, 16], [-1, 16], [15, 10], [7, 8], [14, 3], [-1, 7], [-4, 2]], [[6892, 4472], [-17, 0]], [[6797, 4501], [-13, 1], [-18, -3]], [[6676, 4509], [-6, 7]], [[6618, 4601], [0, 3]], [[7369, 4466], [-7, -8], [-5, -7]], [[7357, 4451], [1, -3], [0, -3]], [[7361, 4480], [2, 1], [6, 3]], [[7333, 4601], [16, 11], [2, 1]], [[7351, 4613], [3, -2], [3, -3]], [[7357, 4608], [-1, -2], [-4, -8]], [[7352, 4598], [-3, -4], [-2, -3]], [[7347, 4591], [-1, -1], [-2, -1]], [[7342, 4588], [-5, 6], [-4, 7]], [[7183, 4281], [-5, -4]], [[7099, 4165], [-23, -25], [-22, -24]], [[7054, 4116], [-5, -4], [-14, -14]], [[6978, 4054], [-22, -11], [-22, -12]], [[6934, 4031], [-41, -17], [-8, -4]], [[6851, 3988], [-3, 3], [-1, 2]], [[6848, 4009], [0, 2], [-1, 6]], [[6847, 4017], [1, 1], [9, 1]], [[6879, 4017], [3, 2], [3, 2]], [[6902, 4023], [23, 16], [11, 7]], [[6936, 4046], [13, 5], [11, 4]], [[6974, 4069], [2, 5], [1, 3]], [[6977, 4077], [-1, 9], [0, 6]], [[6976, 4092], [4, 2], [4, 2]], [[7006, 4099], [0, 1], [-2, 4]], [[7008, 4115], [23, 9], [1, 0]], [[7032, 4124], [6, 6], [6, 6]], [[7040, 4165], [3, 1], [1, 1]], [[7059, 4164], [3, 8], [2, 5]], [[7064, 4177], [-2, 5], [0, 1]], [[7062, 4183], [-5, 1], [-7, 1]], [[6996, 4171], [-2, 8], [0, 4]], [[6994, 4183], [-18, 4], [-2, 1]], [[6966, 4197], [-2, 6], [0, 1]], [[6964, 4204], [-4, 1], [-3, 1]], [[6957, 4206], [0, -8], [0, -3]], [[6957, 4195], [1, -6], [1, -5]], [[6959, 4184], [7, -5], [11, -7]], [[6977, 4172], [-2, -3], [-2, -2]], [[6973, 4167], [-4, -3], [-5, -3]], [[6964, 4161], [-3, -7], [-3, -6]], [[6958, 4148], [1, -9], [0, -8]], [[6959, 4131], [-1, 1], [-4, 4]], [[6954, 4136], [-3, 2], [-3, 1]], [[6946, 4121], [-5, -3], [-7, -4]], [[6933, 4099], [-5, -5], [-1, 0]], [[6927, 4094], [-15, -7], [-15, -8]], [[6867, 4069], [-2, -1], [-5, -4]], [[6860, 4064], [-1, -5], [-2, -6]], [[6857, 4053], [-4, -10], [0, -1]], [[6853, 4042], [-2, -1], [-12, -9]], [[6839, 4032], [-6, -12], [-4, -8]], [[6829, 4003], [2, -9], [2, -4]], [[6833, 3990], [3, -4], [2, -4]], [[6829, 3976], [-4, -4], [-8, -6]], [[6808, 3953], [-23, -43], [-1, -3]], [[6763, 3880], [-2, -2], [-14, -12]], [[6647, 3820], [-8, 3], [-1, 2]]], "transform": {"scale": [0.007000700070007, 0.009500950095009501], "translate": [-100, -70]}, "bbox": [-100, -70, -30, 25]}