var locale={moduleType:"locale",name:"ko",dictionary:{Autoscale:"\uc790\ub3d9 \ud06c\uae30\uc9c0\uc815","Box Select":"\ubc15\uc2a4 \uc120\ud0dd","Click to enter Colorscale title":"Colorscale \uc81c\ubaa9\uc744 \uc9c0\uc815\ud558\ub824\uba74 \ud074\ub9ad\ud558\uc138\uc694","Click to enter Component A title":"A\ub370\uc774\ud130\uc758 \uc81c\ubaa9\uc744 \uc9c0\uc815\ud558\ub824\uba74 \ud074\ub9ad\ud558\uc138\uc694","Click to enter Component B title":"B\ub370\uc774\ud130\uc758 \uc81c\ubaa9\uc744 \uc9c0\uc815\ud558\ub824\uba74 \ud074\ub9ad\ud558\uc138\uc694","Click to enter Component C title":"C\ub370\uc774\ud130\uc758 \uc81c\ubaa9\uc744 \uc9c0\uc815\ud558\ub824\uba74 \ud074\ub9ad\ud558\uc138\uc694","Click to enter Plot title":"\ucc28\ud2b8 \uc81c\ubaa9\uc744 \uc9c0\uc815\ud558\ub824\uba74 \ud074\ub9ad\ud558\uc138\uc694","Click to enter X axis title":"X\ucd95 \uc81c\ubaa9\uc744 \uc9c0\uc815\ud558\ub824\uba74 \ud074\ub9ad\ud558\uc138\uc694","Click to enter Y axis title":"Y\ucd95 \uc81c\ubaa9\uc744 \uc9c0\uc815\ud558\ub824\uba74 \ud074\ub9ad\ud558\uc138\uc694","Click to enter radial axis title":"\uc6d0\ud615 \ucd95 \uc81c\ubaa9\uc744 \uc9c0\uc815\ud558\ub824\uba74 \ud074\ub9ad\ud558\uc138\uc694","Compare data on hover":"\ub9c8\uc6b0\uc2a4\ub97c \uc62c\ub9ac\uba74 \ub370\uc774\ud130\uc640 \ube44\uad50\ud569\ub2c8\ub2e4","Double-click on legend to isolate one trace":"\ubc94\ub840\ub97c \ub354\ube14 \ud074\ub9ad\ud558\uc5ec \ud558\ub098\uc758 \ud2b8\ub808\uc774\uc2a4\ub97c \ubd84\ub9ac\ud569\ub2c8\ub2e4","Double-click to zoom back out":"\ub354\ube14 \ud074\ub9ad\ud558\uc5ec \uc90c\uc744 \ud574\uc81c\ud569\ub2c8\ub2e4","Download plot as a png":".png \uc774\ubbf8\uc9c0 \ud30c\uc77c\ub85c \ucc28\ud2b8\ub97c \ub2e4\uc6b4\ub85c\ub4dc \ud569\ub2c8\ub2e4","Download plot":"\ucc28\ud2b8\ub97c \ub2e4\uc6b4\ub85c\ub4dc \ud569\ub2c8\ub2e4","Edit in Chart Studio":"Chart Studio\ub97c \uc218\uc815\ud569\ub2c8\ub2e4","IE only supports svg.  Changing format to svg.":"IE\ub294 svg\ub9cc\uc744 \uc9c0\uc6d0\ud569\ub2c8\ub2e4. \ud3ec\ub9f7\uc744 svg\ub85c \ubcc0\uacbd\ud558\uc138\uc694","Lasso Select":"\uc62c\uac00\ubbf8 \uc120\ud0dd","Orbital rotation":"\uada4\ub3c4 \uc218\uc815",Pan:"\uc774\ub3d9","Produced with Plotly.js":"Plotly.js \uc81c\uacf5",Reset:"\ucd08\uae30\ud654","Reset axes":"\ucd95 \ucd08\uae30\ud654","Reset camera to default":"camera\ub97c \uae30\ubcf8\uac12\uc73c\ub85c \ucd08\uae30\ud654","Reset camera to last save":"camera\ub97c \ub9c8\uc9c0\ub9c9\uc73c\ub85c \uc800\uc7a5\ud55c \uac12\uc73c\ub85c \ucd08\uae30\ud654","Reset view":"view \ucd08\uae30\ud654","Reset views":"views \ucd08\uae30\ud654","Show closest data on hover":"\ub9c8\uc6b0\uc2a4\ub97c \uc62c\ub9ac\uba74 \uadfc\uc811\ud55c \ub370\uc774\ud130\ub97c \ubcf4\uc774\uae30","Snapshot succeeded":"Snapshot \uc131\uacf5","Sorry, there was a problem downloading your snapshot!":"\uc8c4\uc1a1\ud569\ub2c8\ub2e4, snapshot\uc744 \ub2e4\uc6b4\ub85c\ub4dc \uc911 \ubb38\uc81c\uac00 \ubc1c\uc0dd\ud588\uc2b5\ub2c8\ub2e4!","Taking snapshot - this may take a few seconds":"snapshot \ucc0d\uae30 - \uc218 \ucd08\uac00 \uac78\ub9b4 \uc218 \uc788\uc2b5\ub2c8\ub2e4","Toggle Spike Lines":"\uc2a4\uc704\uce58\ub85c Lines\uc744 \uace0\uc815\ud569\ub2c8\ub2e4","Toggle show closest data on hover":"\uc2a4\uc704\uce58\ub85c \ub9c8\uc6b0\uc2a4\ub97c \uc62c\ub838\uc744 \ub54c \uac00\uc7a5 \uac00\uae4c\uc6b4 \ub370\uc774\ud130\ub97c \ubcf4\uc5ec\uc90d\ub2c8\ub2e4","Turntable rotation":"Turntable \ud68c\uc804",Zoom:"\ud655\ub300(\uc90c)","Zoom in":"\ud655\ub300(\uc90c \uc778)","Zoom out":"\ucd95\uc18c(\uc90c \uc544\uc6c3)",close:"\ub2eb\uae30",high:"\ub192\uc74c",low:"\ub0ae\uc74c",max:"\ucd5c\ub313\uac12","mean \xb1 \u03c3":"\ud3c9\uade0 \xb1 \u03c3",mean:"\ud3c9\uade0",median:"\uc911\uac04 \uac12",min:"\ucd5c\uc19f\uac12","new text":"\uc0c8\ub85c\uc6b4 \ud14d\uc2a4\ud2b8",open:"\uc5f4\uae30",q1:"q1",q3:"q3",source:"\uc18c\uc2a4",target:"\ud0c0\uac9f"},format:{days:["\uc77c\uc694\uc77c","\uc6d4\uc694\uc77c","\ud654\uc694\uc77c","\uc218\uc694\uc77c","\ubaa9\uc694\uc77c","\uae08\uc694\uc77c","\ud1a0\uc694\uc77c"],shortDays:["\uc77c","\uc6d4","\ud654","\uc218","\ubaa9","\uae08","\ud1a0"],months:["1\uc6d4","2\uc6d4","3\uc6d4","4\uc6d4","5\uc6d4","6\uc6d4","7\uc6d4","8\uc6d4","9\uc6d4","10\uc6d4","11\uc6d4","12\uc6d4"],shortMonths:["1\uc6d4","2\uc6d4","3\uc6d4","4\uc6d4","5\uc6d4","6\uc6d4","7\uc6d4","8\uc6d4","9\uc6d4","10\uc6d4","11\uc6d4","12\uc6d4"],date:"%Y-%m-%d"}};"undefined"==typeof Plotly?(window.PlotlyLocales=window.PlotlyLocales||[],window.PlotlyLocales.push(locale)):Plotly.register(locale);