'use strict';

module.exports = {
    moduleType: 'locale',
    name: 'sk',
    dictionary: {
        'Autoscale': 'Auto rozsah',                                                                             // components/modebar/buttons.js:139
        'Box Select': 'Obdĺžnikový výber',                                                                      // components/modebar/buttons.js:103
        'Click to enter Colorscale title': 'Kliknite pre zadanie názvu farebnej š<PERSON>',                         // plots/plots.js:437
        'Click to enter Component A title': 'Kliknite pre zadanie názvu komponentu A',                          // plots/ternary/ternary.js:386
        'Click to enter Component B title': 'Kliknite pre zadanie názvu komponentu B',                          // plots/ternary/ternary.js:400
        'Click to enter Component C title': 'Kliknite pre zadanie názvu komponentu C',                          // plots/ternary/ternary.js:411
        'Click to enter Plot title': 'Kliknite pre zadanie názvu grafu',                                        // plot_api/plot_api.js:579
        'Click to enter X axis title': 'Kliknite pre zadanie názvu osi X',                                      // plots/plots.js:435
        'Click to enter Y axis title': 'Kliknite pre zadanie názvu osi Y',                                      // plots/plots.js:436
        'Click to enter radial axis title': 'Kliknite pre zadanie názvu radiálnej osi',                         // plots/polar/polar.js:376
        'Compare data on hover': 'Porovnať hodnoty pri prejdení myšou',                                         // components/modebar/buttons.js:167
        'Double-click on legend to isolate one trace': 'Dvojklikom na legendu izolujete jednu dátovú sadu',     // components/legend/handle_click.js:90
        'Double-click to zoom back out': 'Dvojklikom vrátite zväčšenie',                                        // plots/cartesian/dragbox.js:299
        'Download plot as a png': 'Uložiť ako PNG',                                                             // components/modebar/buttons.js:52
        'Download plot': 'Uložiť',                                                                              // components/modebar/buttons.js:53
        'Edit in Chart Studio': 'Editovať v Chart Studio',                                                      // components/modebar/buttons.js:76
        'IE only supports svg.  Changing format to svg.': 'IE podporuje iba SVG formát. Zmenené na SVG.',       // components/modebar/buttons.js:60
        'Lasso Select': 'Výber lasom',                                                                          // components/modebar/buttons.js:112
        'Orbital rotation': 'Rotácia (orbitálna)',                                                              // components/modebar/buttons.js:279
        'Pan': 'Posúvanie',                                                                                     // components/modebar/buttons.js:94
        'Produced with Plotly.js': 'Vytvorené pomocou Plotly.js',                                               // components/modebar/modebar.js:256
        'Reset': 'Obnoviť nastavenie',                                                                          // components/modebar/buttons.js:432
        'Reset axes': 'Obnoviť nastavenie osí',                                                                 // components/modebar/buttons.js:148
        'Reset camera to default': 'Obnoviť nastavenie kamery do predvoleného stavu',                           // components/modebar/buttons.js:314
        'Reset camera to last save': 'Obnoviť nastavenie kamery do posledného uloženého stavu',                 // components/modebar/buttons.js:322
        'Reset view': 'Obnoviť nastavenie pohľadu',                                                             // components/modebar/buttons.js:583
        'Reset views': 'Obnoviť nastavenie pohľadov',                                                           // components/modebar/buttons.js:529
        'Show closest data on hover': 'Zobraziť najbližšiu hodnotu při prejdení myšou',                         // components/modebar/buttons.js:157
        'Snapshot succeeded': 'Obrázok vytvorený',                                                              // components/modebar/buttons.js:66
        'Sorry, there was a problem downloading your snapshot!': 'Ospravedlňujeme sa, došlo k chybe pri sťahovaní obrázka!', // components/modebar/buttons.js:69
        'Taking snapshot - this may take a few seconds': 'Snímanie - môže trvať niekoľko sekúnd',               // components/modebar/buttons.js:57
        'Zoom': 'Zväčšenie',                                                                                    // components/modebar/buttons.js:85
        'Zoom in': 'Zväčšiť',                                                                                   // components/modebar/buttons.js:121
        'Zoom out': 'Zmenšiť',                                                                                  // components/modebar/buttons.js:130
        'close:': 'zavrieť:',                                                                                   // traces/ohlc/transform.js:139
        'trace': 'dátová sada',                                                                                 // plots/plots.js:439
        'lat:': 'Lat.:',                                                                                        // traces/scattergeo/calc.js:48
        'lon:': 'Lon.:',                                                                                        // traces/scattergeo/calc.js:49
        'q1:': 'q1:',                                                                                           // traces/box/calc.js:130
        'q3:': 'q3:',                                                                                           // traces/box/calc.js:131
        'source:': 'zdroj:',                                                                                    // traces/sankey/plot.js:140
        'target:': 'cieľ:',                                                                                     // traces/sankey/plot.js:141
        'lower fence:': 'spodná hranica:',                                                                      // traces/box/calc.js:134
        'upper fence:': 'vrchná hranica:',                                                                      // traces/box/calc.js:135
        'max:': 'max.:',                                                                                        // traces/box/calc.js:132
        'mean ± σ:': 'priemer ± σ:',                                                                            // traces/box/calc.js:133
        'mean:': 'priemer:',                                                                                    // traces/box/calc.js:133
        'median:': 'medián:',                                                                                   // traces/box/calc.js:128
        'min:': 'min.:',                                                                                        // traces/box/calc.js:129
        'new text': 'nový text',                                                                                // plots/plots.js:318
        'Turntable rotation': 'Rotácia (otočný stolík)',                                                        // components/modebar/buttons.js:288
        'Toggle Spike Lines': 'Prepnúť zobrazenie vodiacich čiar',                                              // components/modebar/buttons.js:548
        'open:': 'otvoriť:',                                                                                    // traces/ohlc/transform.js:136
        'high:': 'horná:',                                                                                      // traces/ohlc/transform.js:137
        'low:': 'dolná:',                                                                                       // traces/ohlc/transform.js:138
        'Toggle show closest data on hover': 'Prepnúť zobrazovanie najbližšej hodnoty pri prejdení myšou',      // components/modebar/buttons.js:353
        'incoming flow count:': 'počet údajov na vstupe:',                                                      // traces/sankey/plot.js:142
        'outgoing flow count:': 'počet údajov na výstupe:',                                                     // traces/sankey/plot.js:143
        'kde:': 'kde:'                                                                                          // traces/violin/calc.js:73
    },
    format: {
        days: ['Nedeľa', 'Pondelok', 'Utorok', 'Streda', 'Štvrtok', 'Piatok', 'Sobota'],
        shortDays: ['Ned', 'Pon', 'Uto', 'Str', 'Štv', 'Pia', 'Sob'],
        months: [
            'Január', 'Február', 'Marec', 'Apríl', 'Máj', 'Jún',
            'Júl', 'August', 'September', 'Október', 'November', 'December'
        ],
        shortMonths: [
            'Jan', 'Feb', 'Mar', 'Apr', 'Máj', 'Jún',
            'Júl', 'Aug', 'Sep', 'Okt', 'Nov', 'Dec'
        ],
        date: '%d.%m.%Y',
        decimal: ',',
        thousands: ' '
    }
};
