// DRDO IDX - Profile Module

import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';

export const Profile: React.FC = () => {
  const { t } = useTranslation();
  const [isEditing, setIsEditing] = useState(false);

  // Mock user data
  const user = {
    username: 'Dr. <PERSON><PERSON><PERSON>',
    rank: 'Senior Scientist',
    clearance: 'SECRET',
    division: 'DRDL Hyderabad',
    lastLogin: new Date()
  };

  const [profileData, setProfileData] = useState({
    name: user?.username || '',
    email: '<EMAIL>',
    phone: '+91-11-2309-8765',
    division: user?.division || '',
    location: 'Defence Research & Development Laboratory, Hyderabad',
    bio: 'Senior Scientist specializing in advanced missile systems and guidance technology. Leading research in autonomous navigation systems for strategic applications.',
    experience: '15 years',
    projects: '23 Active Projects'
  });

  const auditLogs = [
    { action: 'Login', resource: 'Dashboard', timestamp: '2024-01-08 09:15:32', level: 'INFO' },
    { action: 'View', resource: 'Weapons Systems', timestamp: '2024-01-08 09:22:15', level: 'AUDIT' },
    { action: 'Export', resource: 'Mission Data', timestamp: '2024-01-08 10:45:22', level: 'SECURITY' },
    { action: 'Configure', resource: 'Settings', timestamp: '2024-01-08 11:30:18', level: 'INFO' },
    { action: 'View', resource: 'Simulation Results', timestamp: '2024-01-08 14:22:35', level: 'AUDIT' }
  ];

  const achievements = [
    { title: 'Security Clearance Upgrade', date: '2023-12', type: 'CLEARANCE' },
    { title: 'Project Excellence Award', date: '2023-08', type: 'AWARD' },
    { title: 'Advanced Training Completion', date: '2023-05', type: 'TRAINING' },
    { title: 'Research Publication', date: '2023-02', type: 'RESEARCH' }
  ];

  const handleSave = () => {
    setIsEditing(false);
  };

  const getClassificationBadge = (clearance: string) => {
    switch (clearance) {
      case 'SECRET':
        return 'bg-destructive text-destructive-foreground';
      case 'CONFIDENTIAL':
        return 'bg-warning text-warning-foreground';
      case 'RESTRICTED':
        return 'bg-primary text-primary-foreground';
      default:
        return 'bg-muted text-muted-foreground';
    }
  };

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-foreground">Profile</h1>
          <p className="text-muted-foreground">Manage your account and preferences</p>
        </div>
        <div className="flex items-center gap-2">
          {isEditing ? (
            <>
              <button
                className="px-4 py-2 border border-border rounded-lg hover:bg-secondary transition-colors"
                onClick={() => setIsEditing(false)}
              >
                Cancel
              </button>
              <button
                className="px-4 py-2 bg-primary text-primary-foreground rounded-lg hover:bg-primary/80 transition-colors flex items-center gap-2"
                onClick={handleSave}
              >
                💾 Save Changes
              </button>
            </>
          ) : (
            <button
              className="px-4 py-2 bg-primary text-primary-foreground rounded-lg hover:bg-primary/80 transition-colors flex items-center gap-2"
              onClick={() => setIsEditing(true)}
            >
              ✏️ Edit Profile
            </button>
          )}
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Profile Card */}
        <div className="lg:col-span-1 bg-card p-6 rounded-lg border border-border">
          <h3 className="text-lg font-semibold text-card-foreground mb-4 flex items-center gap-2">
            👤 Profile Information
          </h3>
          <div className="space-y-4">
            {/* Avatar */}
            <div className="flex flex-col items-center space-y-4">
              <div className="w-24 h-24 bg-gradient-to-br from-primary to-primary/70 rounded-full flex items-center justify-center">
                <span className="text-2xl font-bold text-primary-foreground">
                  {user?.username.split(' ').map(n => n[0]).join('').substring(0, 2)}
                </span>
              </div>
              <div className="text-center">
                <h3 className="text-xl font-semibold">{user?.username}</h3>
                <p className="text-muted-foreground">{user?.rank}</p>
                <div className={`mt-2 px-2 py-1 rounded text-xs font-medium inline-flex items-center gap-1 ${getClassificationBadge(user?.clearance || 'PUBLIC')}`}>
                  🛡️ {user?.clearance}
                </div>
              </div>
            </div>

            {/* Quick Info */}
            <div className="space-y-3 pt-4 border-t border-border">
              <div className="flex items-center gap-2 text-sm">
                <span className="text-muted-foreground">📍</span>
                <span>{user?.division}</span>
              </div>
              <div className="flex items-center gap-2 text-sm">
                <span className="text-muted-foreground">📅</span>
                <span>Joined DRDO: Jan 2009</span>
              </div>
              <div className="flex items-center gap-2 text-sm">
                <span className="text-muted-foreground">🕒</span>
                <span>Last Login: {user?.lastLogin?.toLocaleString()}</span>
              </div>
            </div>
          </div>
        </div>

        {/* Profile Details */}
        <div className="lg:col-span-2 bg-card p-6 rounded-lg border border-border">
          <h3 className="text-lg font-semibold text-card-foreground mb-4">Personal Details</h3>
          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium mb-1">Full Name</label>
                <input
                  type="text"
                  value={profileData.name}
                  onChange={(e) => setProfileData({ ...profileData, name: e.target.value })}
                  disabled={!isEditing}
                  className="w-full p-2 border border-border rounded-lg bg-background text-foreground disabled:opacity-50"
                />
              </div>
              <div>
                <label className="block text-sm font-medium mb-1">Email Address</label>
                <input
                  type="email"
                  value={profileData.email}
                  onChange={(e) => setProfileData({ ...profileData, email: e.target.value })}
                  disabled={!isEditing}
                  className="w-full p-2 border border-border rounded-lg bg-background text-foreground disabled:opacity-50"
                />
              </div>
              <div>
                <label className="block text-sm font-medium mb-1">Phone Number</label>
                <input
                  type="text"
                  value={profileData.phone}
                  onChange={(e) => setProfileData({ ...profileData, phone: e.target.value })}
                  disabled={!isEditing}
                  className="w-full p-2 border border-border rounded-lg bg-background text-foreground disabled:opacity-50"
                />
              </div>
              <div>
                <label className="block text-sm font-medium mb-1">Division</label>
                <input
                  type="text"
                  value={profileData.division}
                  onChange={(e) => setProfileData({ ...profileData, division: e.target.value })}
                  disabled={!isEditing}
                  className="w-full p-2 border border-border rounded-lg bg-background text-foreground disabled:opacity-50"
                />
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium mb-1">Location</label>
              <input
                type="text"
                value={profileData.location}
                onChange={(e) => setProfileData({ ...profileData, location: e.target.value })}
                disabled={!isEditing}
                className="w-full p-2 border border-border rounded-lg bg-background text-foreground disabled:opacity-50"
              />
            </div>

            <div>
              <label className="block text-sm font-medium mb-1">Professional Summary</label>
              <textarea
                value={profileData.bio}
                onChange={(e) => setProfileData({ ...profileData, bio: e.target.value })}
                disabled={!isEditing}
                className="w-full p-2 border border-border rounded-lg bg-background text-foreground disabled:opacity-50 min-h-[100px]"
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="p-4 bg-muted rounded-lg">
                <div className="flex items-center gap-2">
                  <span className="text-primary">📊</span>
                  <span className="font-medium">Experience</span>
                </div>
                <p className="text-2xl font-bold text-primary mt-1">{profileData.experience}</p>
              </div>
              <div className="p-4 bg-muted rounded-lg">
                <div className="flex items-center gap-2">
                  <span className="text-primary">🏆</span>
                  <span className="font-medium">Active Projects</span>
                </div>
                <p className="text-2xl font-bold text-primary mt-1">{profileData.projects}</p>
              </div>
            </div>
          </div>
        </div>

        {/* Achievements */}
        <div className="bg-card p-6 rounded-lg border border-border">
          <h3 className="text-lg font-semibold text-card-foreground mb-4 flex items-center gap-2">
            🏆 Recent Achievements
          </h3>
          <div className="space-y-3">
            {achievements.map((achievement, index) => (
              <div key={index} className="flex items-start gap-3 p-3 border border-border rounded-lg">
                <div className="w-2 h-2 bg-primary rounded-full mt-2 flex-shrink-0"></div>
                <div className="flex-1 min-w-0">
                  <p className="font-medium text-sm">{achievement.title}</p>
                  <p className="text-xs text-muted-foreground">{achievement.date}</p>
                  <div className="inline-block px-2 py-1 bg-secondary text-secondary-foreground rounded text-xs mt-1">
                    {achievement.type}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Activity Log */}
        <div className="lg:col-span-2 bg-card p-6 rounded-lg border border-border">
          <h3 className="text-lg font-semibold text-card-foreground mb-4 flex items-center gap-2">
            📊 Recent Activity
          </h3>
          <div className="space-y-2">
            {auditLogs.map((log, index) => (
              <div key={index} className="flex items-center justify-between p-3 border border-border rounded-lg">
                <div className="flex items-center gap-3">
                  <div className="w-2 h-2 bg-primary rounded-full"></div>
                  <div>
                    <span className="font-medium text-sm">{log.action}</span>
                    <span className="text-muted-foreground text-sm ml-2">{log.resource}</span>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <div className="px-2 py-1 bg-secondary text-secondary-foreground rounded text-xs">
                    {log.level}
                  </div>
                  <span className="text-xs text-muted-foreground font-mono">
                    {log.timestamp}
                  </span>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};
