// DRDO IDX - Equipment Health Monitoring Chart

import React, { useEffect, useRef } from 'react';
import * as d3 from 'd3';
import { SystemHealth } from '../../types/military';

interface EquipmentHealthChartProps {
  data: SystemHealth[];
  width?: number;
  height?: number;
}

export const EquipmentHealthChart: React.FC<EquipmentHealthChartProps> = ({
  data,
  width = 600,
  height = 300
}) => {
  const svgRef = useRef<SVGSVGElement>(null);

  useEffect(() => {
    if (!svgRef.current || !data.length) return;

    const svg = d3.select(svgRef.current);
    svg.selectAll("*").remove();

    const margin = { top: 20, right: 30, bottom: 40, left: 100 };
    const innerWidth = width - margin.left - margin.right;
    const innerHeight = height - margin.top - margin.bottom;

    const g = svg.append("g")
      .attr("transform", `translate(${margin.left},${margin.top})`);

    // Create scales
    const xScale = d3.scaleLinear()
      .domain([0, 100])
      .range([0, innerWidth]);

    const yScale = d3.scaleBand()
      .domain(data.map(d => d.component))
      .range([0, innerHeight])
      .padding(0.2);

    // Color scale based on health status
    const colorScale = (status: string) => {
      switch (status) {
        case 'HEALTHY': return '#22c55e';
        case 'WARNING': return '#f59e0b';
        case 'CRITICAL': return '#ef4444';
        case 'OFFLINE': return '#6b7280';
        default: return '#3b82f6';
      }
    };

    // Create bars
    g.selectAll(".bar")
      .data(data)
      .enter().append("rect")
      .attr("class", "bar")
      .attr("x", 0)
      .attr("y", d => yScale(d.component)!)
      .attr("width", 0)
      .attr("height", yScale.bandwidth())
      .attr("fill", d => colorScale(d.status))
      .attr("rx", 4)
      .transition()
      .duration(1000)
      .attr("width", d => xScale(d.performance));

    // Add performance text
    g.selectAll(".bar-text")
      .data(data)
      .enter().append("text")
      .attr("class", "bar-text")
      .attr("x", d => xScale(d.performance) + 5)
      .attr("y", d => yScale(d.component)! + yScale.bandwidth() / 2)
      .attr("dy", "0.35em")
      .style("fill", "#374151")
      .style("font-size", "12px")
      .style("font-weight", "500")
      .text(d => `${d.performance}%`);

    // Add component labels
    g.selectAll(".component-label")
      .data(data)
      .enter().append("text")
      .attr("class", "component-label")
      .attr("x", -10)
      .attr("y", d => yScale(d.component)! + yScale.bandwidth() / 2)
      .attr("dy", "0.35em")
      .style("text-anchor", "end")
      .style("fill", "#374151")
      .style("font-size", "12px")
      .style("font-weight", "500")
      .text(d => d.component);

    // Add grid lines
    g.selectAll(".grid-line")
      .data(xScale.ticks(5))
      .enter().append("line")
      .attr("class", "grid-line")
      .attr("x1", d => xScale(d))
      .attr("x2", d => xScale(d))
      .attr("y1", 0)
      .attr("y2", innerHeight)
      .style("stroke", "#e5e7eb")
      .style("stroke-dasharray", "3,3")
      .style("opacity", 0.5);

    // Add axes
    g.append("g")
      .attr("transform", `translate(0,${innerHeight})`)
      .call(d3.axisBottom(xScale).tickFormat(d => `${d}%`))
      .style("color", "#6b7280");

  }, [data, width, height]);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'HEALTHY': return 'bg-success text-success-foreground';
      case 'WARNING': return 'bg-warning text-warning-foreground';
      case 'CRITICAL': return 'bg-destructive text-destructive-foreground';
      case 'OFFLINE': return 'bg-muted text-muted-foreground';
      default: return 'bg-primary text-primary-foreground';
    }
  };

  return (
    <div className="w-full bg-card p-6 rounded-lg border border-border">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-bold text-card-foreground">System Health Overview</h3>
        <div className="flex gap-2">
          {['HEALTHY', 'WARNING', 'CRITICAL', 'OFFLINE'].map(status => (
            <div 
              key={status} 
              className={`px-2 py-1 rounded text-xs font-medium ${getStatusColor(status)}`}
            >
              {status}
            </div>
          ))}
        </div>
      </div>
      
      <div className="w-full overflow-x-auto">
        <svg
          ref={svgRef}
          width={width}
          height={height}
          className="w-full h-auto"
          style={{ minHeight: height }}
        />
      </div>
      
      {/* System Metrics Summary */}
      <div className="mt-4 grid grid-cols-2 md:grid-cols-4 gap-4">
        {data.map((system) => (
          <div key={system.component} className="p-3 bg-muted/50 rounded-lg">
            <div className="text-sm font-medium text-foreground">
              {system.component}
            </div>
            <div className="text-xs text-muted-foreground">
              Last Check: {new Date(system.lastCheck).toLocaleTimeString()}
            </div>
            {system.issues && system.issues.length > 0 && (
              <div className="text-xs text-destructive mt-1">
                {system.issues.length} issue(s)
              </div>
            )}
          </div>
        ))}
      </div>
    </div>
  );
};
