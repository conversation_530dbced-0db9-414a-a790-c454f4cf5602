{"type": "Topology", "objects": {"coastlines": {"type": "GeometryCollection", "geometries": [{"type": "LineString", "arcs": [0, 1, 2, 3, 4, 5]}, {"type": "LineString", "arcs": [6]}, {"type": "LineString", "arcs": [7, 8]}, {"type": "LineString", "arcs": [9]}, {"type": "LineString", "arcs": [10]}, {"type": "LineString", "arcs": [11]}, {"type": "LineString", "arcs": [12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55]}, {"type": "LineString", "arcs": [56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83]}, {"type": "LineString", "arcs": [84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104, 105, 106, 107]}, {"type": "LineString", "arcs": [108]}, {"type": "LineString", "arcs": [109]}, {"type": "LineString", "arcs": [110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 123, 124, 125, 126, 127, 128, 129, 130, 131, 132, 133, 134, 135, 136, 137, 138, 139, 140, 141, 142, 143, 144, 145, 146, 147, 148, 149, 150, 151, 152, 153, 154, 155, 156, 157, 158, 159, 160, 161, 162, 163, 164, 165, 166, 167, 168, 169, 170, 171, 172, 173, 174, 175, 176, 177, 178, 179, 180, 181, 182, 183, 184, 185, 186, 187, 188, 189, 190, 191, 192, 193, 194, 195, 196, 197, 198]}, {"type": "LineString", "arcs": [199, 200, 201, 202, 203, 204, 205, 206, 207, 208, 209, 210, 211, 212, 213, 214, 215, 216, 217, 218, 219, 220, 221, 222, 223, 224, 225, 226, 227, 228, 229, 230, 231, 232, 233, 234, 235, 236, 237]}, {"type": "LineString", "arcs": [238, 239, 240, 241, 242, 243, 244, 245, 246, 247, 248, 249, 250, 251, 252, 253, 254, 255, 256, 257]}, {"type": "LineString", "arcs": [258, 259, 260, 261, 262, 263, 264, 265, 266, 267, 268, 269, 270, 271, 272, 273, 274, 275, 276, 277, 278, 279, 280, 281, 282, 283, 284, 285, 286, 287, 288, 289, 290, 291, 292, 293, 294, 295, 296, 297, 298, 299, 300, 301, 302, 303, 304, 305, 306, 307, 308, 309, 310, 311, 312, 313, 314, 315, 316, 317, 318, 319, 320]}, {"type": "LineString", "arcs": [321, 322, 323, 324, 325, 326, 327, 328, 329, 330, 331, 332, 333, 334, 335, 336, 337, 338, 339, 340, 341, 342, 343, 344, 345, 346, 347, 348, 349, 350, 351, 352]}, {"type": "LineString", "arcs": [353, 354, 355, 356, 357, 358, 359, 360, 361, 362, 363, 364, 365, 366, 367, 368, 369, 370, 371, 372, 373, 374, 375, 376, 377, 378, 379, 380, 381, 382, 383, 384, 385, 386, 387, 388, 389, 390, 391, 392, 393, 394, 395]}, {"type": "LineString", "arcs": [396, 397, 398, 399, 400, 401, 402, 403, 404, 405, 406, 407, 408, 409, 410, 411, 412, 413, 414, 415, 416, 417, 418, 419, 420, 421, 422, 423, 424, 425, 426, 427, 428, 429, 430, 431, 432, 433, 434, 435, 436, 437, 438, 439, 440, 441, 442, 443]}, {"type": "LineString", "arcs": [444, 445, 446, 447, 448, 449, 450, 451, 452, 453, 454, 455, 456, 457, 458, 459, 460, 461, 462, 463, 464, 465, 466, 467, 468, 469, 470, 471, 472, 473, 474, 475, 476, 477, 478, 479, 480]}, {"type": "LineString", "arcs": [481]}, {"type": "LineString", "arcs": [482]}, {"type": "LineString", "arcs": [483]}, {"type": "LineString", "arcs": [484]}, {"type": "LineString", "arcs": [485, 486, 487, 488, 489, 490, 491, 492, 493, 494, 495, 496, 497, 498, 499, 500, 501, 502, 503, 504, 505, 506, 507, 508, 509, 510, 511, 512, 513, 514, 515, 516, 517, 518, 519, 520]}, {"type": "LineString", "arcs": [521]}, {"type": "LineString", "arcs": [522]}, {"type": "LineString", "arcs": [523]}, {"type": "LineString", "arcs": [524]}, {"type": "LineString", "arcs": [525]}, {"type": "LineString", "arcs": [526]}, {"type": "LineString", "arcs": [527, 528]}, {"type": "LineString", "arcs": [529, 530, 531]}, {"type": "LineString", "arcs": [532]}, {"type": "LineString", "arcs": [533]}, {"type": "LineString", "arcs": [534]}, {"type": "LineString", "arcs": [535]}, {"type": "LineString", "arcs": [536, 537, 538, 539]}, {"type": "LineString", "arcs": [540, 541, 542]}, {"type": "LineString", "arcs": [543, 544, 545, 546]}, {"type": "LineString", "arcs": [547, 548, 549, 550, 551]}, {"type": "LineString", "arcs": [552]}, {"type": "LineString", "arcs": [553]}, {"type": "LineString", "arcs": [554]}, {"type": "LineString", "arcs": [555]}, {"type": "LineString", "arcs": [556]}, {"type": "LineString", "arcs": [557]}, {"type": "LineString", "arcs": [558]}, {"type": "LineString", "arcs": [559]}, {"type": "LineString", "arcs": [560, 561, 562, 563, 564]}, {"type": "LineString", "arcs": [565, 566, 567, 568, 569]}, {"type": "LineString", "arcs": [570, 571, 572, 573, 574, 575]}, {"type": "LineString", "arcs": [576]}, {"type": "LineString", "arcs": [577]}, {"type": "LineString", "arcs": [578]}, {"type": "LineString", "arcs": [579]}, {"type": "LineString", "arcs": [580]}, {"type": "LineString", "arcs": [581]}, {"type": "LineString", "arcs": [582]}, {"type": "LineString", "arcs": [583]}, {"type": "LineString", "arcs": [584]}, {"type": "LineString", "arcs": [585]}, {"type": "LineString", "arcs": [586, 587, 588, 589]}, {"type": "LineString", "arcs": [590]}, {"type": "LineString", "arcs": [591, 592, 593, 594, 595, 596, 597]}, {"type": "LineString", "arcs": [598]}, {"type": "LineString", "arcs": [599]}, {"type": "LineString", "arcs": [600, 601, 602, 603, 604, 605, 606, 607, 608, 609, 610, 611, 612, 613, 614, 615, 616, 617, 618, 619]}, {"type": "LineString", "arcs": [620, 621, 622, 623, 624, 625, 626, 627, 628, 629, 630]}, {"type": "LineString", "arcs": [631, 632, 633, 634, 635, 636, 637, 638, 639, 640, 641, 642, 643, 644, 645, 646]}, {"type": "LineString", "arcs": [647, 648, 649, 650, 651, 652, 653, 654]}, {"type": "LineString", "arcs": [655, 656, 657, 658, 659, 660, 661, 662, 663, 664, 665, 666, 667, 668, 669, 670, 671, 672]}, {"type": "LineString", "arcs": [673, 674, 675, 676, 677, 678, 679, 680]}, {"type": "LineString", "arcs": [681, 682, 683, 684, 685, 686, 687, 688, 689]}, {"type": "LineString", "arcs": [690, 691, 692, 693, 694, 695, 696, 697, 698, 699]}, {"type": "LineString", "arcs": [700, 701, 702]}, {"type": "LineString", "arcs": [703, 704, 705]}, {"type": "LineString", "arcs": [706, 707, 708, 709, 710, 711, 712, 713, 714, 715, 716, 717, 718, 719]}, {"type": "LineString", "arcs": [720, 721, 722, 723, 724, 725, 726]}, {"type": "LineString", "arcs": [727, 728, 729, 730, 731, 732, 733]}, {"type": "LineString", "arcs": [734, 735, 736, 737, 738, 739]}, {"type": "LineString", "arcs": [740, 741, 742, 743, 744, 745, 746, 747, 748, 749, 750]}, {"type": "LineString", "arcs": [751, 752, 753, 754, 755, 756, 757, 758]}, {"type": "LineString", "arcs": [759, 760, 761, 762, 763, 764, 765, 766, 767]}, {"type": "LineString", "arcs": [768, 769, 770, 771]}, {"type": "LineString", "arcs": [772, 773, 774, 775, 776, 777, 778, 779, 780, 781, 782, 783]}, {"type": "LineString", "arcs": [784, 785, 786, 787, 788, 789, 790]}, {"type": "LineString", "arcs": [791, 792, 793, 794, 795, 796, 797, 798, 799, 800, 801, 802, 803, 804, 805, 806, 807, 808, 809, 810, 811, 812, 813, 814, 815, 816, 817, 818, 819]}, {"type": "LineString", "arcs": [820, 821, 822, 823, 824, 825, 826, 827, 828, 829, 830, 831, 832, 833, 834, 835, 836, 837, 838, 839, 840, 841, 842, 843, 844, 845, 846, 847, 848, 849, 850, 851, 852, 853, 854, 855, 856, 857, 858, 859, 860, 861, 862, 863, 864]}, {"type": "LineString", "arcs": [865, 866, 867, 868, 869, 870, 871]}, {"type": "LineString", "arcs": [872, 873, 874, 875, 876, 877]}, {"type": "LineString", "arcs": [878, 879, 880, 881, 882, 883, 884, 885, 886, 887, 888, 889, 890, 891, 892, 893]}, {"type": "LineString", "arcs": [894, 895, 896, 897, 898, 899, 900, 901, 902, 903, 904, 905, 906, 907, 908, 909, 910, 911, 912]}, {"type": "LineString", "arcs": [913, 914, 915, 916]}, {"type": "LineString", "arcs": [917, 918, 919, 920]}, {"type": "LineString", "arcs": [921, 922, 923, 924, 925, 926, 927, 928, 929, 930, 931, 932, 933]}, {"type": "LineString", "arcs": [934, 935, 936, 937, 938, 939, 940, 941]}, {"type": "LineString", "arcs": [942, 943, 944, 945, 946, 947, 948, 949, 950, 951, 952]}, {"type": "LineString", "arcs": [953, 954, 955, 956, 957, 958, 959, 960, 961, 962]}, {"type": "LineString", "arcs": [963]}, {"type": "LineString", "arcs": [964]}, {"type": "LineString", "arcs": [965]}, {"type": "LineString", "arcs": [966]}, {"type": "LineString", "arcs": [967]}, {"type": "LineString", "arcs": [968, 969, 970]}, {"type": "LineString", "arcs": [971, 972, 973, 974, 975, 976, 977]}, {"type": "LineString", "arcs": [978, 979, 980, 981, 982, 983, 984, 985, 986, 987, 988]}, {"type": "LineString", "arcs": [989, 990, 991, 992, 993, 994, 995]}, {"type": "LineString", "arcs": [996, 997, 998, 999, 1000, 1001]}, {"type": "LineString", "arcs": [1002, 1003, 1004, 1005, 1006, 1007, 1008, 1009, 1010, 1011, 1012, 1013, 1014, 1015, 1016, 1017, 1018]}, {"type": "LineString", "arcs": [1019]}, {"type": "LineString", "arcs": [1020]}, {"type": "LineString", "arcs": [1021]}, {"type": "LineString", "arcs": [1022]}, {"type": "LineString", "arcs": [1023]}, {"type": "LineString", "arcs": [1024]}, {"type": "LineString", "arcs": [1025, 1026, 1027, 1028, 1029, 1030]}, {"type": "LineString", "arcs": [1031]}, {"type": "LineString", "arcs": [1032, 1033, 1034, 1035, 1036, 1037]}, {"type": "LineString", "arcs": [1038, 1039, 1040]}, {"type": "LineString", "arcs": [1041, 1042, 1043, 1044, 1045, 1046, 1047, 1048]}, {"type": "LineString", "arcs": [1049, 1050, 1051, 1052, 1053]}, {"type": "LineString", "arcs": [1054, 1055, 1056, 1057]}, {"type": "LineString", "arcs": [1058, 1059, 1060, 1061, 1062]}, {"type": "LineString", "arcs": [1063]}, {"type": "LineString", "arcs": [1064, 1065, 1066, 1067]}, {"type": "LineString", "arcs": [1068, 1069, 1070, 1071]}, {"type": "LineString", "arcs": [1072, 1073, 1074, 1075, 1076, 1077]}, {"type": "LineString", "arcs": [1078, 1079]}, {"type": "LineString", "arcs": [1080]}, {"type": "LineString", "arcs": [1081, 1082, 1083, 1084, 1085, 1086, 1087, 1088, 1089]}, {"type": "LineString", "arcs": [1090, 1091, 1092, 1093, 1094, 1095, 1096, 1097]}, {"type": "LineString", "arcs": [1098, 1099, 1100, 1101, 1102]}, {"type": "LineString", "arcs": [1103, 1104, 1105, 1106, 1107, 1108, 1109, 1110, 1111]}, {"type": "LineString", "arcs": [1112, 1113, 1114, 1115]}, {"type": "LineString", "arcs": [1116, 1117, 1118, 1119, 1120]}, {"type": "LineString", "arcs": [1121, 1122, 1123]}, {"type": "LineString", "arcs": [1124, 1125, 1126, 1127, 1128]}, {"type": "LineString", "arcs": [1129, 1130, 1131]}, {"type": "LineString", "arcs": [1132, 1133, 1134, 1135, 1136, 1137, 1138]}, {"type": "LineString", "arcs": [1139, 1140, 1141, 1142, 1143, 1144, 1145]}, {"type": "LineString", "arcs": [1146, 1147, 1148, 1149, 1150, 1151]}, {"type": "LineString", "arcs": [1152]}, {"type": "LineString", "arcs": [1153]}, {"type": "LineString", "arcs": [1154]}, {"type": "LineString", "arcs": [1155]}, {"type": "LineString", "arcs": [1156]}, {"type": "LineString", "arcs": [1157]}, {"type": "LineString", "arcs": [1158]}, {"type": "LineString", "arcs": [1159]}, {"type": "LineString", "arcs": [1160]}, {"type": "LineString", "arcs": [1161]}, {"type": "LineString", "arcs": [1162]}, {"type": "LineString", "arcs": [1163]}, {"type": "LineString", "arcs": [1164]}, {"type": "LineString", "arcs": [1165]}, {"type": "LineString", "arcs": [1166, 1167, 1168]}, {"type": "LineString", "arcs": [1169]}, {"type": "LineString", "arcs": [1170, 1171, 1172]}, {"type": "LineString", "arcs": [1173, 1174, 1175]}, {"type": "LineString", "arcs": [1176]}, {"type": "LineString", "arcs": [1177, 1178, 1179, 1180]}, {"type": "LineString", "arcs": [1181]}, {"type": "LineString", "arcs": [1182]}, {"type": "LineString", "arcs": [1183, 1184, 1185, 1186, 1187, 1188, 1189, 1190]}, {"type": "LineString", "arcs": [1191]}, {"type": "LineString", "arcs": [1192]}, {"type": "LineString", "arcs": [1193]}, {"type": "LineString", "arcs": [1194]}, {"type": "LineString", "arcs": [1195]}, {"type": "LineString", "arcs": [1196]}, {"type": "LineString", "arcs": [1197]}, {"type": "LineString", "arcs": [1198]}, {"type": "LineString", "arcs": [1199]}, {"type": "LineString", "arcs": [1200]}, {"type": "LineString", "arcs": [1201]}, {"type": "LineString", "arcs": [1202]}, {"type": "LineString", "arcs": [1203]}, {"type": "LineString", "arcs": [1204]}, {"type": "LineString", "arcs": [1205]}, {"type": "LineString", "arcs": [1206]}, {"type": "LineString", "arcs": [1207]}, {"type": "LineString", "arcs": [1208]}, {"type": "LineString", "arcs": [1209]}, {"type": "LineString", "arcs": [1210]}, {"type": "LineString", "arcs": [1211]}, {"type": "LineString", "arcs": [1212]}, {"type": "LineString", "arcs": [1213]}, {"type": "LineString", "arcs": [1214]}, {"type": "LineString", "arcs": [1215]}, {"type": "LineString", "arcs": [1216]}, {"type": "LineString", "arcs": [1217]}, {"type": "LineString", "arcs": [1218]}, {"type": "LineString", "arcs": [1219]}, {"type": "LineString", "arcs": [1220]}, {"type": "LineString", "arcs": [1221, 1222, 1223, 1224, 1225, 1226, 1227, 1228]}, {"type": "LineString", "arcs": [1229, 1230, 1231, 1232, 1233, 1234]}, {"type": "LineString", "arcs": [1235, 1236, 1237, 1238, 1239]}, {"type": "LineString", "arcs": [1240, 1241, 1242, 1243, 1244, 1245, 1246, 1247, 1248]}, {"type": "LineString", "arcs": [1249, 1250, 1251, 1252, 1253, 1254]}, {"type": "LineString", "arcs": [1255, 1256, 1257, 1258, 1259, 1260, 1261]}, {"type": "LineString", "arcs": [1262, 1263, 1264, 1265, 1266, 1267, 1268, 1269]}, {"type": "LineString", "arcs": [1270, 1271, 1272, 1273, 1274, 1275, 1276, 1277]}, {"type": "LineString", "arcs": [1278, 1279, 1280, 1281, 1282, 1283, 1284, 1285, 1286, 1287, 1288]}, {"type": "LineString", "arcs": [1289, 1290, 1291, 1292, 1293, 1294]}, {"type": "LineString", "arcs": [1295, 1296, 1297, 1298, 1299, 1300, 1301]}, {"type": "LineString", "arcs": [1302, 1303, 1304, 1305, 1306, 1307, 1308, 1309, 1310, 1311, 1312, 1313, 1314, 1315, 1316, 1317]}, {"type": "LineString", "arcs": [1318, 1319, 1320, 1321, 1322]}, {"type": "LineString", "arcs": [1323, 1324, 1325, 1326, 1327, 1328, 1329, 1330, 1331, 1332, 1333, 1334, 1335, 1336, 1337]}, {"type": "LineString", "arcs": [1338, 1339, 1340, 1341, 1342, 1343, 1344, 1345]}, {"type": "LineString", "arcs": [1346, 1347, 1348, 1349, 1350, 1351, 1352, 1353, 1354, 1355, 1356, 1357, 1358, 1359]}, {"type": "LineString", "arcs": [1360, 1361, 1362, 1363, 1364, 1365, 1366, 1367, 1368, 1369, 1370, 1371, 1372, 1373, 1374, 1375, 1376, 1377]}, {"type": "LineString", "arcs": [1378, 1379, 1380, 1381, 1382, 1383, 1384, 1385, 1386, 1387, 1388, 1389]}, {"type": "LineString", "arcs": [1390, 1391, 1392, 1393, 1394, 1395, 1396, 1397, 1398, 1399, 1400, 1401]}, {"type": "LineString", "arcs": [1402, 1403, 1404, 1405, 1406, 1407, 1408, 1409, 1410]}, {"type": "LineString", "arcs": [1411, 1412, 1413, 1414, 1415, 1416, 1417, 1418, 1419, 1420, 1421, 1422, 1423]}, {"type": "LineString", "arcs": [1424, 1425, 1426, 1427, 1428, 1429, 1430, 1431, 1432, 1433, 1434]}, {"type": "LineString", "arcs": [1435, 1436, 1437, 1438, 1439]}, {"type": "LineString", "arcs": [1440]}, {"type": "LineString", "arcs": [1441]}, {"type": "LineString", "arcs": [1442]}, {"type": "LineString", "arcs": [1443]}, {"type": "LineString", "arcs": [1444]}, {"type": "LineString", "arcs": [1445]}, {"type": "LineString", "arcs": [1446]}, {"type": "LineString", "arcs": [1447]}, {"type": "LineString", "arcs": [1448]}, {"type": "LineString", "arcs": [1449]}, {"type": "LineString", "arcs": [1450]}, {"type": "LineString", "arcs": [1451]}, {"type": "LineString", "arcs": [1452]}, {"type": "LineString", "arcs": [1453]}, {"type": "LineString", "arcs": [1454]}, {"type": "MultiLineString", "arcs": [[1455], [1456]]}, {"type": "LineString", "arcs": [1457]}, {"type": "MultiLineString", "arcs": [[1458], [1459]]}, {"type": "LineString", "arcs": [1460]}, {"type": "LineString", "arcs": [1461]}, {"type": "LineString", "arcs": [1462]}, {"type": "LineString", "arcs": [1463]}, {"type": "LineString", "arcs": [1464]}, {"type": "LineString", "arcs": [1465]}, {"type": "LineString", "arcs": [1466]}, {"type": "LineString", "arcs": [1467]}, {"type": "LineString", "arcs": [1468]}, {"type": "LineString", "arcs": [1469]}, {"type": "LineString", "arcs": [1470]}, {"type": "LineString", "arcs": [1471]}, {"type": "LineString", "arcs": [1472]}, {"type": "LineString", "arcs": [1473]}, {"type": "LineString", "arcs": [1474]}, {"type": "LineString", "arcs": [1475]}, {"type": "LineString", "arcs": [1476, 1477, 1478]}, {"type": "MultiLineString", "arcs": [[1479, 1480, 1481, 1482, 1483, 1484, 1485, 1486, 1487, 1488, 1489, 1490, 1491, 1492, 1493, 1494, 1495, 1496, 1497, 1498, 1499, 1500, 1501, 1502, 1503, 1504, 1505, 1506, 1507, 1508, 1509, 1510, 1511, 1512, 1513, 1514, 1515, 1516, 1517, 1518, 1519, 1520, 1521, 1522, 1523, 1524, 1525, 1526, 1527, 1528, 1529, 1530, 1531, 1532, 1533, 1534, 1535, 1536, 1537, 1538, 1539, 1540, 1541, 1542, 1543, 1544, 1545, 1546, 1547, 1548, 1549, 1550, 1551, 1552, 1553, 1554, 1555, 1556, 1557, 1558, 1559, 1560, 1561, 1562, 1563, 1564, 1565, 1566, 1567, 1568, 1569, 1570, 1571, 1572, 1573, 1574, 1575, 1576, 1577, 1578, 1579, 1580, 1581, 1582, 1583, 1584, 1585, 1586, 1587, 1588, 1589, 1590, 1591, 1592, 1593, 1594, 1595, 1596, 1597, 1598, 1599, 1600, 1601, 1602, 1603, 1604, 1605, 1606, 1607, 1608, 1609, 1610, 1611, 1612, 1613, 1614, 1615, 1616, 1617, 1618, 1619, 1620, 1621, 1622, 1623, 1624, 1625, 1626, 1627, 1628, 1629, 1630, 1631, 1632, 1633, 1634, 1635, 1636, 1637, 1638, 1639, 1640, 1641, 1642, 1643, 1644, 1645, 1646, 1647, 1648, 1649, 1650, 1651, 1652, 1653, 1654, 1655, 1656, 1657, 1658, 1659, 1660, 1661, 1662, 1663, 1664, 1665, 1666, 1667, 1668, 1669, 1670, 1671, 1672, 1673, 1674, 1675, 1676, 1677, 1678, 1679, 1680, 1681, 1682, 1683, 1684, 1685, 1686, 1687, 1688, 1689, 1690, 1691, 1692, 1693, 1694, 1695, 1696, 1697, 1698, 1699, 1700, 1701, 1702, 1703, 1704, 1705, 1706, 1707, 1708, 1709, 1710, 1711, 1712, 1713, 1714, 1715, 1716, 1717, 1718, 1719, 1720, 1721, 1722, 1723, 1724, 1725, 1726, 1727, 1728, 1729, 1730, 1731, 1732, 1733, 1734, 1735, 1736, 1737, 1738, 1739, 1740, 1741, 1742, 1743, 1744, 1745, 1746, 1747, 1748, 1749, 1750, 1751, 1752, 1753, 1754, 1755, 1756, 1757, 1758, 1759, 1760, 1761, 1762, 1763, 1764, 1765, 1766, 1767, 1768, 1769, 1770, 1771, 1772, 1773, 1774, 1775, 1776, 1777, 1778, 1779, 1780, 1781, 1782, 1783, 1784, 1785, 1786, 1787, 1788, 1789, 1790, 1791, 1792, 1793, 1794, 1795, 1796, 1797, 1798, 1799, 1800, 1801, 1802, 1803, 1804, 1805, 1806, 1807, 1808, 1809, 1810, 1811, 1812, 1813, 1814, 1815, 1816, 1817, 1818, 1819, 1820, 1821, 1822, 1823, 1824, 1825, 1826, 1827, 1828, 1829, 1830, 1831, 1832, 1833, 1834, 1835, 1836, 1837, 1838, 1839, 1840, 1841, 1842, 1843, 1844, 1845, 1846, 1847, 1848, 1849, 1850, 1851, 1852, 1853, 1854, 1855, 1856, 1857, 1858, 1859, 1860, 1861, 1862, 1863, 1864, 1865, 1866, 1867, 1868, 1869, 1870, 1871, 1872, 1873, 1874, 1875, 1876, 1877, 1878, 1879, 1880, 1881, 1882, 1883, 1884, 1885, 1886, 1887, 1888, 1889, 1890, 1891, 1892, 1893, 1894, 1895, 1896, 1897, 1898, 1899, 1900, 1901, 1902, 1903, 1904, 1905, 1906, 1907, 1908, 1909, 1910, 1911, 1912, 1913, 1914, 1915, 1916, 1917, 1918, 1919, 1920, 1921, 1922, 1923, 1924, 1925, 1926, 1927, 1928, 1929, 1930, 1931, 1932, 1933, 1934, 1935, 1936, 1937, 1938, 1939, 1940, 1941, 1942, 1943, 1944, 1945, 1946, 1947, 1948, 1949, 1950, 1951, 1952, 1953, 1954, 1955, 1956, 1957, 1958, 1959, 1960, 1961, 1962, 1963, 1964, 1965, 1966, 1967, 1968, 1969, 1970, 1971, 1972, 1973, 1974, 1975, 1976, 1977, 1978, 1979, 1980, 1981, 1982, 1983, 1984, 1985, 1986, 1987, 1988, 1989, 1990, 1991, 1992, 1993, 1994, 1995, 1996, 1997, 1998, 1999, 2000, 2001, 2002, 2003, 2004, 2005, 2006, 2007, 2008, 2009, 2010, 2011, 2012, 2013, 2014, 2015, 2016, 2017, 2018, 2019, 2020, 2021, 2022, 2023, 2024, 2025, 2026, 2027, 2028, 2029, 2030, 2031, 2032, 2033, 2034, 2035, 2036, 2037, 2038, 2039, 2040, 2041, 2042, 2043, 2044, 2045, 2046, 2047, 2048, 2049, 2050, 2051, 2052, 2053, 2054, 2055, 2056, 2057, 2058, 2059, 2060, 2061, 2062, 2063, 2064, 2065, 2066, 2067, 2068, 2069, 2070, 2071, 2072, 2073, 2074, 2075, 2076, 2077, 2078, 2079, 2080, 2081, 2082, 2083, 2084, 2085, 2086, 2087, 2088, 2089, 2090, 2091, 2092, 2093, 2094, 2095, 2096, 2097, 2098, 2099, 2100, 2101, 2102, 2103, 2104, 2105, 2106, 2107, 2108, 2109, 2110, 2111, 2112, 2113, 2114, 2115, 2116, 2117, 2118, 2119, 2120, 2121, 2122, 2123, 2124, 2125, 2126, 2127, 2128, 2129, 2130, 2131, 2132, 2133, 2134, 2135, 2136, 2137, 2138, 2139, 2140, 2141, 2142, 2143, 2144, 2145, 2146, 2147, 2148, 2149, 2150, 2151, 2152, 2153, 2154, 2155, 2156, 2157, 2158, 2159, 2160, 2161, 2162, 2163, 2164, 2165, 2166, 2167, 2168, 2169, 2170, 2171, 2172, 2173, 2174, 2175, 2176, 2177, 2178, 2179, 2180, 2181, 2182, 2183, 2184, 2185, 2186, 2187, 2188, 2189, 2190, 2191, 2192, 2193, 2194, 2195, 2196, 2197, 2198, 2199, 2200, 2201, 2202, 2203, 2204, 2205, 2206, 2207, 2208, 2209, 2210, 2211, 2212, 2213, 2214, 2215, 2216, 2217, 2218, 2219, 2220, 2221, 2222, 2223, 2224, 2225, 2226, 2227, 2228, 2229, 2230, 2231, 2232, 2233, 2234, 2235, 2236, 2237, 2238, 2239, 2240, 2241, 2242, 2243, 2244, 2245, 2246, 2247, 2248, 2249, 2250, 2251, 2252, 2253, 2254, 2255, 2256, 2257, 2258, 2259, 2260, 2261, 2262, 2263, 2264, 2265, 2266, 2267, 2268, 2269, 2270, 2271, 2272, 2273, 2274, 2275, 2276, 2277, 2278, 2279, 2280, 2281, 2282, 2283, 2284, 2285, 2286, 2287, 2288, 2289, 2290, 2291, 2292, 2293, 2294, 2295, 2296, 2297, 2298, 2299, 2300, 2301, 2302, 2303, 2304, 2305, 2306, 2307, 2308, 2309, 2310, 2311, 2312, 2313, 2314, 2315, 2316, 2317, 2318, 2319, 2320, 2321, 2322, 2323, 2324, 2325, 2326, 2327, 2328, 2329, 2330, 2331, 2332, 2333, 2334, 2335, 2336, 2337, 2338, 2339, 2340, 2341, 2342, 2343, 2344, 2345, 2346, 2347, 2348, 2349, 2350, 2351, 2352, 2353, 2354, 2355, 2356, 2357, 2358, 2359, 2360, 2361, 2362, 2363, 2364, 2365, 2366, 2367, 2368, 2369, 2370, 2371, 2372, 2373, 2374, 2375, 2376, 2377, 2378, 2379, 2380, 2381, 2382, 2383, 2384, 2385, 2386, 2387, 2388, 2389, 2390, 2391, 2392, 2393, 2394, 2395, 2396, 2397, 2398, 2399, 2400, 2401, 2402, 2403, 2404, 2405, 2406, 2407, 2408, 2409, 2410, 2411, 2412, 2413, 2414, 2415, 2416, 2417, 2418, 2419, 2420, 2421, 2422, 2423, 2424, 2425, 2426, 2427, 2428, 2429, 2430, 2431, 2432, 2433, 2434, 2435, 2436, 2437, 2438, 2439, 2440, 2441, 2442, 2443, 2444, 2445, 2446, 2447, 2448, 2449, 2450, 2451, 2452, 2453, 2454, 2455, 2456, 2457, 2458, 2459, 2460, 2461, 2462, 2463, 2464, 2465, 2466, 2467, 2468, 2469, 2470, 2471, 2472, 2473, 2474, 2475, 2476, 2477, 2478, 2479, 2480, 2481, 2482, 2483, 2484, 2485, 2486, 2487, 2488, 2489, 2490, 2491, 2492, 2493, 2494, 2495, 2496, 2497, 2498, 2499, 2500, 2501, 2502, 2503, 2504, 2505, 2506, 2507, 2508, 2509, 2510, 2511, 2512, 2513, 2514, 2515, 2516, 2517, 2518, 2519, 2520, 2521, 2522, 2523, 2524, 2525, 2526, 2527, 2528, 2529, 2530, 2531, 2532, 2533, 2534, 2535, 2536, 2537, 2538, 2539, 2540, 2541, 2542, 2543, 2544, 2545, 2546, 2547, 2548, 2549, 2550, 2551, 2552, 2553, 2554, 2555, 2556, 2557, 2558, 2559, 2560, 2561, 2562, 2563, 2564, 2565, 2566, 2567, 2568, 2569, 2570, 2571, 2572, 2573, 2574, 2575, 2576, 2577, 2578, 2579, 2580, 2581, 2582, 2583, 2584, 2585, 2586, 2587, 2588, 2589, 2590, 2591, 2592, 2593, 2594, 2595, 2596, 2597, 2598, 2599, 2600, 2601, 2602, 2603, 2604, 2605, 2606, 2607, 2608, 2609, 2610, 2611, 2612, 2613, 2614, 2615, 2616, 2617, 2618, 2619, 2620, 2621, 2622, 2623, 2624, 2625, 2626, 2627, 2628, 2629, 2630, 2631, 2632, 2633, 2634, 2635, 2636, 2637, 2638, 2639, 2640, 2641, 2642, 2643, 2644, 2645, 2646, 2647, 2648, 2649, 2650, 2651, 2652, 2653, 2654, 2655, 2656, 2657, 2658, 2659, 2660, 2661, 2662, 2663, 2664, 2665, 2666, 2667, 2668, 2669, 2670, 2671, 2672, 2673, 2674, 2675, 2676, 2677, 2678, 2679, 2680, 2681, 2682, 2683, 2684, 2685, 2686, 2687, 2688, 2689, 2690, 2691, 2692, 2693, 2694, 2695, 2696, 2697, 2698, 2699, 2700, 2701, 2702, 2703, 2704, 2705, 2706, 2707, 2708, 2709, 2710, 2711, 2712, 2713, 2714, 2715, 2716, 2717, 2718, 2719, 2720, 2721, 2722, 2723, 2724, 2725, 2726, 2727, 2728, 2729, 2730, 2731, 2732, 2733, 2734, 2735, 2736, 2737, 2738, 2739, 2740, 2741, 2742, 2743, 2744, 2745, 2746, 2747, 2748, 2749, 2750, 2751, 2752, 2753, 2754, 2755, 2756, 2757, 2758, 2759, 2760, 2761, 2762, 2763, 2764, 2765, 2766, 2767, 2768, 2769, 2770, 2771, 2772, 2773, 2774, 2775, 2776, 2777, 2778, 2779, 2780, 2781, 2782, 2783], [2784], [2785, 2786, 2787, 2788, 2789, 2790, 2791, 2792, 2793, 2794, 2795, 2796, 2797, 2798, 2799, 2800, 2801, 2802, 2803, 2804, 2805, 2806, 2807, 2808, 2809, 2810, 2811, 2812, 2813, 2814, 2815, 2816, 2817, 2818, 2819, 2820, 2821, 2822, 2823, 2824, 2825, 2826, 2827, 2828, 2829, 2830, 2831, 2832, 2833, 2834, 2835, 2836, 2837, 2838, 2839, 2840, 2841, 2842, 2843, 2844, 2845, 2846, 2847, 2848, 2849, 2850, 2851, 2852, 2853, 2854, 2855, 2856, 2857, 2858, 2859, 2860, 2861, 2862, 2863, 2864, 2865, 2866, 2867, 2868, 2869, 2870, 2871, 2872, 2873, 2874, 2875, 2876, 2877, 2878, 2879, 2880, 2881, 2882, 2883, 2884, 2885, 2886, 2887, 2888, 2889, 2890, 2891, 2892, 2893, 2894, 2895, 2896, 2897, 2898, 2899, 2900, 2901, 2902, 2903, 2904, 2905, 2906, 2907, 2908, 2909, 2910, 2911, 2912, 2913, 2914, 2915, 2916, 2917, 2918, 2919, 2920, 2921, 2922, 2923, 2924, 2925, 2926, 2927, 2928, 2929, 2930, 2931, 2932, 2933, 2934, 2935, 2936, 2937, 2938, 2939, 2940, 2941, 2942, 2943, 2944, 2945, 2946, 2947, 2948, 2949, 2950, 2951, 2952, 2953, 2954, 2955, 2956, 2957, 2958, 2959, 2960, 2961, 2962, 2963, 2964, 2965, 2966, 2967, 2968, 2969, 2970, 2971, 2972, 2973, 2974, 2975, 2976, 2977, 2978, 2979, 2980, 2981, 2982, 2983, 2984, 2985, 2986, 2987, 2988, 2989, 2990, 2991, 2992, 2993, 2994, 2995, 2996, 2997, 2998, 2999, 3000, 3001, 3002, 3003, 3004, 3005, 3006, 3007, 3008, 3009, 3010, 3011, 3012, 3013, 3014, 3015, 3016, 3017, 3018, 3019, 3020, 3021, 3022, 3023, 3024, 3025, 3026, 3027, 3028, 3029, 3030, 3031, 3032, 3033, 3034, 3035, 3036, 3037, 3038, 3039, 3040, 3041, 3042, 3043, 3044, 3045, 3046, 3047, 3048, 3049, 3050, 3051, 3052, 3053, 3054, 3055, 3056, 3057, 3058, 3059, 3060, 3061, 3062, 3063, 3064, 3065, 3066, 3067, 3068, 3069, 3070, 3071, 3072, 3073, 3074, 3075, 3076, 3077, 3078, 3079, 3080, 3081, 3082, 3083, 3084, 3085, 3086, 3087, 3088, 3089, 3090, 3091, 3092, 3093, 3094, 3095, 3096, 3097, 3098, 3099, 3100, 3101, 3102, 3103, 3104, 3105, 3106, 3107, 3108, 3109, 3110, 3111, 3112, 3113, 3114, 3115, 3116, 3117, 3118, 3119, 3120, 3121, 3122, 3123, 3124, 3125, 3126, 3127, 3128, 3129, 3130, 3131, 3132, 3133, 3134, 3135, 3136, 3137, 3138, 3139, 3140, 3141, 3142, 3143, 3144, 3145, 3146, 3147, 3148, 3149, 3150, 3151, 3152, 3153, 3154, 3155, 3156, 3157, 3158, 3159, 3160, 3161, 3162, 3163, 3164, 3165, 3166, 3167, 3168, 3169, 3170, 3171, 3172, 3173, 3174, 3175, 3176, 3177, 3178, 3179, 3180, 3181, 3182, 3183, 3184, 3185, 3186, 3187, 3188, 3189, 3190, 3191, 3192, 3193, 3194, 3195, 3196, 3197, 3198, 3199, 3200, 3201, 3202, 3203, 3204, 3205, 3206, 3207, 3208, 3209, 3210, 3211, 3212, 3213, 3214, 3215, 3216, 3217, 3218, 3219, 3220, 3221, 3222, 3223, 3224, 3225, 3226, 3227, 3228, 3229, 3230, 3231, 3232, 3233, 3234, 3235, 3236, 3237, 3238, 3239, 3240, 3241, 3242, 3243, 3244, 3245, 3246, 3247, 3248, 3249, 3250, 3251, 3252, 3253, 3254, 3255, 3256, 3257, 3258, 3259, 3260, 3261, 3262, 3263, 3264, 3265, 3266, 3267, 3268, 3269, 3270, 3271, 3272, 3273, 3274, 3275, 3276, 3277, 3278, 3279, 3280, 3281, 3282, 3283, 3284, 3285, 3286, 3287, 3288, 3289, 3290, 3291, 3292, 3293, 3294, 3295, 3296, 3297, 3298, 3299, 3300, 3301, 3302, 3303, 3304, 3305, 3306, 3307, 3308, 3309, 3310, 3311, 3312, 3313, 3314, 3315, 3316, 3317, 3318, 3319, 3320, 3321, 3322, 3323, 3324, 3325, 3326, 3327, 3328, 3329, 3330, 3331, 3332, 3333, 3334, 3335, 3336, 3337, 3338, 3339, 3340, 3341, 3342, 3343, 3344, 3345, 3346, 3347, 3348, 3349, 3350, 3351, 3352, 3353, 3354, 3355, 3356, 3357, 3358, 3359, 3360, 3361, 3362, 3363, 3364, 3365, 3366, 3367, 3368, 3369, 3370, 3371, 3372, 3373, 3374, 3375, 3376, 3377, 3378, 3379, 3380, 3381, 3382, 3383, 3384, 3385, 3386, 3387, 3388, 3389, 3390, 3391, 3392, 3393, 3394, 3395, 3396, 3397, 3398, 3399, 3400, 3401, 3402, 3403, 3404, 3405, 3406, 3407, 3408, 3409, 3410, 3411, 3412, 3413, 3414, 3415, 3416, 3417, 3418, 3419, 3420, 3421, 3422, 3423, 3424, 3425, 3426, 3427, 3428, 3429, 3430, 3431, 3432, 3433, 3434, 3435, 3436, 3437, 3438, 3439, 3440, 3441, 3442, 3443, 3444, 3445, 3446, 3447, 3448, 3449, 3450, 3451, 3452, 3453, 3454, 3455, 3456, 3457, 3458, 3459, 3460, 3461, 3462, 3463, 3464, 3465, 3466, 3467, 3468, 3469, 3470, 3471, 3472, 3473, 3474, 3475, 3476, 3477, 3478, 3479, 3480, 3481, 3482, 3483, 3484, 3485, 3486, 3487, 3488, 3489, 3490, 3491, 3492, 3493, 3494, 3495, 3496, 3497, 3498, 3499, 3500, 3501, 3502, 3503, 3504, 3505, 3506, 3507, 3508, 3509, 3510, 3511, 3512, 3513, 3514, 3515, 3516, 3517, 3518, 3519, 3520, 3521, 3522, 3523, 3524, 3525, 3526, 3527, 3528, 3529, 3530, 3531, 3532, 3533, 3534, 3535, 3536, 3537, 3538, 3539, 3540, 3541, 3542, 3543, 3544, 3545, 3546, 3547, 3548, 3549, 3550, 3551, 3552, 3553, 3554, 3555, 3556, 3557, 3558, 3559, 3560, 3561, 3562, 3563, 3564]]}, {"type": "LineString", "arcs": [3565]}, {"type": "LineString", "arcs": [3566]}, {"type": "LineString", "arcs": [3567]}, {"type": "LineString", "arcs": [3568]}, {"type": "LineString", "arcs": [3569]}, {"type": "LineString", "arcs": [3570]}, {"type": "LineString", "arcs": [3571]}, {"type": "LineString", "arcs": [3572]}, {"type": "LineString", "arcs": [3573]}, {"type": "LineString", "arcs": [3574]}, {"type": "LineString", "arcs": [3575, 3576, 3577]}, {"type": "LineString", "arcs": [3578, 3579, 3580]}, {"type": "LineString", "arcs": [3581]}, {"type": "LineString", "arcs": [3582]}, {"type": "LineString", "arcs": [3583]}, {"type": "LineString", "arcs": [3584]}, {"type": "LineString", "arcs": [3585]}, {"type": "LineString", "arcs": [3586]}, {"type": "LineString", "arcs": [3587]}, {"type": "LineString", "arcs": [3588]}, {"type": "LineString", "arcs": [3589]}, {"type": "LineString", "arcs": [3590]}, {"type": "LineString", "arcs": [3591]}, {"type": "LineString", "arcs": [3592, 3593, 3594]}, {"type": "LineString", "arcs": [3595, 3596, 3597]}, {"type": "LineString", "arcs": [3598]}, {"type": "LineString", "arcs": [3599, 3600, 3601]}, {"type": "LineString", "arcs": [3602, 3603, 3604]}, {"type": "LineString", "arcs": [3605, 3606, 3607]}, {"type": "LineString", "arcs": [3608, 3609, 3610]}, {"type": "LineString", "arcs": [3611]}, {"type": "LineString", "arcs": [3612]}, {"type": "LineString", "arcs": [3613, 3614, 3615]}, {"type": "LineString", "arcs": [3616, 3617, 3618]}, {"type": "LineString", "arcs": [3619]}, {"type": "LineString", "arcs": [3620]}, {"type": "LineString", "arcs": [3621]}, {"type": "LineString", "arcs": [3622]}, {"type": "LineString", "arcs": [3623]}, {"type": "LineString", "arcs": [3624]}, {"type": "LineString", "arcs": [3625]}, {"type": "LineString", "arcs": [3626]}, {"type": "LineString", "arcs": [3627]}, {"type": "LineString", "arcs": [3628]}, {"type": "LineString", "arcs": [3629]}, {"type": "LineString", "arcs": [3630]}, {"type": "LineString", "arcs": [3631]}, {"type": "LineString", "arcs": [3632]}, {"type": "LineString", "arcs": [3633]}, {"type": "LineString", "arcs": [3634]}, {"type": "LineString", "arcs": [3635]}, {"type": "LineString", "arcs": [3636]}, {"type": "LineString", "arcs": [3637]}, {"type": "LineString", "arcs": [3638]}, {"type": "LineString", "arcs": [3639]}, {"type": "LineString", "arcs": [3640]}, {"type": "LineString", "arcs": [3641]}, {"type": "LineString", "arcs": [3642]}, {"type": "LineString", "arcs": [3643]}, {"type": "LineString", "arcs": [3644]}, {"type": "LineString", "arcs": [3645]}, {"type": "LineString", "arcs": [3646]}, {"type": "LineString", "arcs": [3647]}, {"type": "LineString", "arcs": [3648]}, {"type": "LineString", "arcs": [3649]}, {"type": "LineString", "arcs": [3650]}, {"type": "LineString", "arcs": [3651]}, {"type": "LineString", "arcs": [3652]}, {"type": "LineString", "arcs": [3653]}, {"type": "LineString", "arcs": [3654]}, {"type": "LineString", "arcs": [3655]}, {"type": "LineString", "arcs": [3656]}, {"type": "LineString", "arcs": [3657]}, {"type": "LineString", "arcs": [3658]}, {"type": "LineString", "arcs": [3659]}, {"type": "LineString", "arcs": [3660]}, {"type": "LineString", "arcs": [3661]}, {"type": "LineString", "arcs": [3662]}, {"type": "LineString", "arcs": [3663]}, {"type": "LineString", "arcs": [3664]}, {"type": "LineString", "arcs": [3665]}, {"type": "LineString", "arcs": [3666]}, {"type": "LineString", "arcs": [3667]}, {"type": "LineString", "arcs": [3668]}, {"type": "LineString", "arcs": [3669]}, {"type": "LineString", "arcs": [3670]}, {"type": "LineString", "arcs": [3671]}, {"type": "LineString", "arcs": [3672]}, {"type": "LineString", "arcs": [3673]}, {"type": "LineString", "arcs": [3674]}, {"type": "LineString", "arcs": [3675]}, {"type": "LineString", "arcs": [3676]}, {"type": "LineString", "arcs": [3677]}, {"type": "LineString", "arcs": [3678]}, {"type": "LineString", "arcs": [3679]}, {"type": "LineString", "arcs": [3680]}, {"type": "LineString", "arcs": [3681]}, {"type": "LineString", "arcs": [3682]}, {"type": "LineString", "arcs": [3683]}, {"type": "LineString", "arcs": [3684]}, {"type": "LineString", "arcs": [3685]}, {"type": "LineString", "arcs": [3686]}, {"type": "LineString", "arcs": [3687]}, {"type": "LineString", "arcs": [3688]}, {"type": "LineString", "arcs": [3689]}, {"type": "LineString", "arcs": [3690]}, {"type": "LineString", "arcs": [3691]}, {"type": "LineString", "arcs": [3692]}, {"type": "LineString", "arcs": [3693]}, {"type": "LineString", "arcs": [3694]}, {"type": "LineString", "arcs": [3695]}, {"type": "MultiLineString", "arcs": [[3696], [3697], [3698], [3699]]}, {"type": "LineString", "arcs": [3700]}, {"type": "LineString", "arcs": [3701, 3702, 3703, 3704, 3705, 3706, 3707]}, {"type": "LineString", "arcs": [3708]}, {"type": "LineString", "arcs": [3709]}, {"type": "LineString", "arcs": [3710, 3711, 3712, 3713, 3714]}, {"type": "LineString", "arcs": [3715]}, {"type": "LineString", "arcs": [3716, 3717, 3718, 3719, 3720, 3721, 3722]}]}, "land": {"type": "GeometryCollection", "geometries": [{"type": "Polygon", "arcs": [[-4, -3, -2, -1, -6, -5]]}, {"type": "Polygon", "arcs": [[-7]]}, {"type": "Polygon", "arcs": [[-8, -9]]}, {"type": "Polygon", "arcs": [[-32, -31, -30, -29, -28, -27, -26, -25, -24, -23, -22, -21, -20, -19, -18, -17, -16, -15, -14, -13, -56, -55, -54, -53, -52, -51, -50, -49, -48, -47, -46, -45, -44, -43, -42, -41, -40, -39, -38, -37, -36, -35, -34, -33]]}, {"type": "Polygon", "arcs": [[-63, -62, -61, -60, -59, -58, -57, -84, -83, -82, -81, -80, -79, -78, -77, -76, -75, -74, -73, -72, -71, -70, -69, -68, -67, -66, -65, -64]]}, {"type": "Polygon", "arcs": [[-87, -86, -85, -108, -107, -106, -105, -104, -103, -102, -101, -100, -99, -98, -97, -96, -95, -94, -93, -92, -91, -90, -89, -88]]}, {"type": "Polygon", "arcs": [[-115, -114, -113, -112, -111, -199, -198, -197, -196, -195, -194, -193, -192, -191, -190, -189, -188, -187, -186, -185, -184, -183, -182, -181, -180, -179, -178, -177, -176, -175, -174, -173, -172, -171, -170, -169, -168, -167, -166, -165, -164, -163, -162, -161, -160, -159, -158, -157, -156, -155, -154, -153, -152, -151, -150, -149, -148, -147, -146, -145, -144, -143, -142, -141, -140, -139, -138, -137, -136, -135, -134, -133, -132, -131, -130, -129, -128, -127, -126, -125, -124, -123, -122, -121, -120, -119, -118, -117, -116]]}, {"type": "Polygon", "arcs": [[-210, -209, -208, -207, -206, -205, -204, -203, -202, -201, -200, -238, -237, -236, -235, -234, -233, -232, -231, -230, -229, -228, -227, -226, -225, -224, -223, -222, -221, -220, -219, -218, -217, -216, -215, -214, -213, -212, -211]]}, {"type": "Polygon", "arcs": [[-257, -256, -255, -254, -253, -252, -251, -250, -249, -248, -247, -246, -245, -244, -243, -242, -241, -240, -239, -258]]}, {"type": "Polygon", "arcs": [[-288, -287, -286, -285, -284, -283, -282, -281, -280, -279, -278, -277, -276, -275, -274, -273, -272, -271, -270, -269, -268, -267, -266, -265, -264, -263, -262, -261, -260, -259, -321, -320, -319, -318, -317, -316, -315, -314, -313, -312, -311, -310, -309, -308, -307, -306, -305, -304, -303, -302, -301, -300, -299, -298, -297, -296, -295, -294, -293, -292, -291, -290, -289]]}, {"type": "Polygon", "arcs": [[-349, -348, -347, -346, -345, -344, -343, -342, -341, -340, -339, -338, -337, -336, -335, -334, -333, -332, -331, -330, -329, -328, -327, -326, -325, -324, -323, -322, -353, -352, -351, -350]]}, {"type": "Polygon", "arcs": [[-354, -396, -395, -394, -393, -392, -391, -390, -389, -388, -387, -386, -385, -384, -383, -382, -381, -380, -379, -378, -377, -376, -375, -374, -373, -372, -371, -370, -369, -368, -367, -366, -365, -364, -363, -362, -361, -360, -359, -358, -357, -356, -355]]}, {"type": "Polygon", "arcs": [[-425, -424, -423, -422, -421, -420, -419, -418, -417, -416, -415, -414, -413, -412, -411, -410, -409, -408, -407, -406, -405, -404, -403, -402, -401, -400, -399, -398, -397, -444, -443, -442, -441, -440, -439, -438, -437, -436, -435, -434, -433, -432, -431, -430, -429, -428, -427, -426]]}, {"type": "Polygon", "arcs": [[-468, -467, -466, -465, -464, -463, -462, -461, -460, -459, -458, -457, -456, -455, -454, -453, -452, -451, -450, -449, -448, -447, -446, -445, -481, -480, -479, -478, -477, -476, -475, -474, -473, -472, -471, -470, -469]]}, {"type": "Polygon", "arcs": [[-491, -490, -489, -488, -487, -486, -521, -520, -519, -518, -517, -516, -515, -514, -513, -512, -511, -510, -509, -508, -507, -506, -505, -504, -503, -502, -501, -500, -499, -498, -497, -496, -495, -494, -493, -492]]}, {"type": "Polygon", "arcs": [[-528, -529]]}, {"type": "Polygon", "arcs": [[-531, -530, -532]]}, {"type": "Polygon", "arcs": [[-539, -538, -537, -540]]}, {"type": "Polygon", "arcs": [[-541, -543, -542]]}, {"type": "Polygon", "arcs": [[-545, -544, -547, -546]]}, {"type": "Polygon", "arcs": [[-551, -550, -549, -548, -552]]}, {"type": "Polygon", "arcs": [[-562, -561, -565, -564, -563]]}, {"type": "Polygon", "arcs": [[566, 567, 568, 569, 565]]}, {"type": "Polygon", "arcs": [[570, 571, 572, 573, 574, 575]]}, {"type": "Polygon", "arcs": [[-587, -590, -589, -588]]}, {"type": "Polygon", "arcs": [[-597, -596, -595, -594, -593, -592, -598]]}, {"type": "Polygon", "arcs": [[-614, -613, -612, -611, -610, -609, -608, -607, -606, -605, -604, -603, -602, -601, -620, -619, -618, -617, -616, -615]]}, {"type": "Polygon", "arcs": [[-623, -622, -621, -631, -630, -629, -628, -627, -626, -625, -624]]}, {"type": "Polygon", "arcs": [[-636, -635, -634, -633, -632, -647, -646, -645, -644, -643, -642, -641, -640, -639, -638, -637]]}, {"type": "Polygon", "arcs": [[-650, -649, -648, -655, -654, -653, -652, -651]]}, {"type": "Polygon", "arcs": [[-672, -671, -670, -669, -668, -667, -666, -665, -664, -663, -662, -661, -660, -659, -658, -657, -656, -673]]}, {"type": "Polygon", "arcs": [[-676, -675, -674, -681, -680, -679, -678, -677]]}, {"type": "Polygon", "arcs": [[-686, -685, -684, -683, -682, -690, -689, -688, -687]]}, {"type": "Polygon", "arcs": [[-691, -700, -699, -698, -697, -696, -695, -694, -693, -692]]}, {"type": "Polygon", "arcs": [[-702, -701, -703]]}, {"type": "Polygon", "arcs": [[-705, -704, -706]]}, {"type": "Polygon", "arcs": [[-711, -710, -709, -708, -707, -720, -719, -718, -717, -716, -715, -714, -713, -712]]}, {"type": "Polygon", "arcs": [[-722, -721, -727, -726, -725, -724, -723]]}, {"type": "Polygon", "arcs": [[-731, -730, -729, -728, -734, -733, -732]]}, {"type": "Polygon", "arcs": [[-737, -736, -735, -740, -739, -738]]}, {"type": "Polygon", "arcs": [[-749, -748, -747, -746, -745, -744, -743, -742, -741, -751, -750]]}, {"type": "Polygon", "arcs": [[-759, -758, -757, -756, -755, -754, -753, -752]]}, {"type": "Polygon", "arcs": [[-764, -763, -762, -761, -760, -768, -767, -766, -765]]}, {"type": "Polygon", "arcs": [[-771, -770, -769, -772]]}, {"type": "Polygon", "arcs": [[-780, -779, -778, -777, -776, -775, -774, -773, -784, -783, -782, -781]]}, {"type": "Polygon", "arcs": [[-789, -788, -787, -786, -785, -791, -790]]}, {"type": "Polygon", "arcs": [[-817, -816, -815, -814, -813, -812, -811, -810, -809, -808, -807, -806, -805, -804, -803, -802, -801, -800, -799, -798, -797, -796, -795, -794, -793, -792, -820, -819, -818]]}, {"type": "Polygon", "arcs": [[-833, -832, -831, -830, -829, -828, -827, -826, -825, -824, -823, -822, -821, -865, -864, -863, -862, -861, -860, -859, -858, -857, -856, -855, -854, -853, -852, -851, -850, -849, -848, -847, -846, -845, -844, -843, -842, -841, -840, -839, -838, -837, -836, -835, -834]]}, {"type": "Polygon", "arcs": [[-868, -867, -866, -872, -871, -870, -869]]}, {"type": "Polygon", "arcs": [[-876, -875, -874, -873, -878, -877]]}, {"type": "Polygon", "arcs": [[-879, -894, -893, -892, -891, -890, -889, -888, -887, -886, -885, -884, -883, -882, -881, -880]]}, {"type": "Polygon", "arcs": [[-904, -903, -902, -901, -900, -899, -898, -897, -896, -895, -913, -912, -911, -910, -909, -908, -907, -906, -905]]}, {"type": "Polygon", "arcs": [[-915, -914, -917, -916]]}, {"type": "Polygon", "arcs": [[-921, -920, -919, -918]]}, {"type": "Polygon", "arcs": [[-934, -933, -932, -931, -930, -929, -928, -927, -926, -925, -924, -923, -922]]}, {"type": "Polygon", "arcs": [[-935, -942, -941, -940, -939, -938, -937, -936]]}, {"type": "Polygon", "arcs": [[-948, -947, -946, -945, -944, -943, -953, -952, -951, -950, -949]]}, {"type": "Polygon", "arcs": [[-960, -959, -958, -957, -956, -955, -954, -963, -962, -961]]}, {"type": "Polygon", "arcs": [[-969, -971, -970]]}, {"type": "Polygon", "arcs": [[-976, -975, -974, -973, -972, -978, -977]]}, {"type": "Polygon", "arcs": [[-987, -986, -985, -984, -983, -982, -981, -980, -979, -989, -988]]}, {"type": "Polygon", "arcs": [[-993, -992, -991, -990, -996, -995, -994]]}, {"type": "Polygon", "arcs": [[-1000, -999, -998, -997, -1002, -1001]]}, {"type": "Polygon", "arcs": [[-1019, -1018, -1017, -1016, -1015, -1014, -1013, -1012, -1011, -1010, -1009, -1008, -1007, -1006, -1005, -1004, -1003]]}, {"type": "Polygon", "arcs": [[-1029, -1028, -1027, -1026, -1031, -1030]]}, {"type": "Polygon", "arcs": [[-1035, -1034, -1033, -1038, -1037, -1036]]}, {"type": "Polygon", "arcs": [[-1041, -1040, -1039]]}, {"type": "Polygon", "arcs": [[-1043, -1042, -1049, -1048, -1047, -1046, -1045, -1044]]}, {"type": "Polygon", "arcs": [[-1050, -1054, -1053, -1052, -1051]]}, {"type": "Polygon", "arcs": [[-1055, -1058, -1057, -1056]]}, {"type": "Polygon", "arcs": [[-1063, -1062, -1061, -1060, -1059]]}, {"type": "Polygon", "arcs": [[-1066, -1065, -1068, -1067]]}, {"type": "Polygon", "arcs": [[-1069, -1072, -1071, -1070]]}, {"type": "Polygon", "arcs": [[-1074, -1073, -1078, -1077, -1076, -1075]]}, {"type": "Polygon", "arcs": [[-1080, -1079]]}, {"type": "Polygon", "arcs": [[-1081]]}, {"type": "Polygon", "arcs": [[-1084, 3991, -1090, -1089, -1088, -1087, 3992]]}, {"type": "Polygon", "arcs": [[-1093, -1092, -1091, -1098, -1097, -1096, -1095, -1094]]}, {"type": "Polygon", "arcs": [[-1099, -1103, -1102, -1101, -1100]]}, {"type": "Polygon", "arcs": [[-1106, -1105, -1104, -1112, -1111, -1110, -1109, -1108, -1107]]}, {"type": "Polygon", "arcs": [[-1113, -1116, -1115, -1114]]}, {"type": "Polygon", "arcs": [[-1117, -1121, -1120, -1119, -1118]]}, {"type": "Polygon", "arcs": [[-1124, -1123, -1122]]}, {"type": "Polygon", "arcs": [[-1125, -1129, -1128, -1127, -1126]]}, {"type": "Polygon", "arcs": [[-1130, -1132, -1131]]}, {"type": "Polygon", "arcs": [[-1137, -1136, -1135, -1134, -1133, -1139, -1138]]}, {"type": "Polygon", "arcs": [[-1143, -1142, -1141, -1140, -1146, -1145, -1144]]}, {"type": "Polygon", "arcs": [[-1148, -1147, -1152, -1151, -1150, -1149]]}, {"type": "Polygon", "arcs": [[-1167, -1169, -1168]]}, {"type": "Polygon", "arcs": [[-1173, -1172, -1171]]}, {"type": "Polygon", "arcs": [[-1174, -1176, -1175]]}, {"type": "Polygon", "arcs": [[-1178, -1181, -1180, -1179]]}, {"type": "Polygon", "arcs": [[-1190, -1189, -1188, -1187, -1186, -1185, -1184, -1191]]}, {"type": "Polygon", "arcs": [[-1225, -1224, -1223, -1222, -1229, -1228, -1227, -1226]]}, {"type": "Polygon", "arcs": [[-1231, -1230, -1235, -1234, -1233, -1232]]}, {"type": "Polygon", "arcs": [[-1237, -1236, -1240, -1239, -1238]]}, {"type": "Polygon", "arcs": [[-1249, -1248, -1247, -1246, -1245, -1244, -1243, -1242, -1241]]}, {"type": "Polygon", "arcs": [[-1255, -1254, -1253, -1252, -1251, -1250]]}, {"type": "Polygon", "arcs": [[-1258, -1257, -1256, -1262, -1261, -1260, -1259]]}, {"type": "Polygon", "arcs": [[-1266, -1265, -1264, -1263, -1270, -1269, -1268, -1267]]}, {"type": "Polygon", "arcs": [[-1274, -1273, -1272, -1271, -1278, -1277, -1276, -1275]]}, {"type": "Polygon", "arcs": [[-1279, -1289, -1288, -1287, -1286, -1285, -1284, -1283, -1282, -1281, -1280]]}, {"type": "Polygon", "arcs": [[-1292, -1291, -1290, -1295, -1294, -1293]]}, {"type": "Polygon", "arcs": [[-1297, -1296, -1302, -1301, -1300, -1299, -1298]]}, {"type": "Polygon", "arcs": [[-1310, -1309, -1308, -1307, -1306, -1305, -1304, -1303, -1318, -1317, -1316, -1315, -1314, -1313, -1312, -1311]]}, {"type": "Polygon", "arcs": [[-1322, -1321, -1320, -1319, -1323]]}, {"type": "Polygon", "arcs": [[-1334, -1333, -1332, -1331, -1330, -1329, -1328, -1327, -1326, -1325, -1324, -1338, -1337, -1336, -1335]]}, {"type": "Polygon", "arcs": [[-1346, -1345, -1344, -1343, -1342, -1341, -1340, -1339]]}, {"type": "Polygon", "arcs": [[-1348, -1347, -1360, -1359, -1358, -1357, -1356, -1355, -1354, -1353, -1352, -1351, -1350, -1349]]}, {"type": "Polygon", "arcs": [[-1366, -1365, -1364, -1363, -1362, -1361, -1378, -1377, -1376, -1375, -1374, -1373, -1372, -1371, -1370, -1369, -1368, -1367]]}, {"type": "Polygon", "arcs": [[-1386, -1385, -1384, -1383, -1382, -1381, -1380, -1379, -1390, -1389, -1388, -1387]]}, {"type": "Polygon", "arcs": [[-1394, -1393, -1392, -1391, -1402, -1401, -1400, -1399, -1398, -1397, -1396, -1395]]}, {"type": "Polygon", "arcs": [[-1407, -1406, -1405, -1404, -1403, -1411, -1410, -1409, -1408]]}, {"type": "Polygon", "arcs": [[-1423, -1422, -1421, -1420, -1419, -1418, -1417, -1416, -1415, -1414, -1413, -1412, -1424]]}, {"type": "Polygon", "arcs": [[-1427, -1426, -1425, -1435, -1434, -1433, -1432, -1431, -1430, -1429, -1428]]}, {"type": "Polygon", "arcs": [[-1440, -1439, -1438, -1437, -1436]]}, {"type": "MultiPolygon", "arcs": [[[-3548, -3547, -3546, -3545, -3544, -3543, -3542, -3541, -3540, -3539, -3538, -3537, -3536, -3535, -3534, -3533, -3532, -3531, -3530, -3529, -3528, -3527, -3526, -3525, -3524, -3523, -3522, -3521, -3520, -3519, -3518, -3517, -3516, -3515, -3514, -3513, -3512, -3511, -3510, -3509, -3508, -3507, -3506, -3505, -3504, -3503, -3502, -3501, -3500, -3499, -3498, -3497, -3496, -3495, -3494, -3493, -3492, 3993, -3489, -3488, -3487, -3486, -3485, -3484, -3483, -3482, -3481, -3480, -3479, -3478, -3477, -3476, -3475, -3474, -3473, -3472, -3471, -3470, -3469, -3468, -3467, -3466, -3465, -3464, -3463, -3462, -3461, -3460, -3459, -3458, -3457, -3456, -3455, -3454, -3453, -3452, -3451, -3450, -3449, 3994, -3446, -3445, -3444, -3443, -3442, -3441, -3440, 3995, -3437, -3436, -3435, -3434, 3996, -3431, -3430, -3429, -3428, -3427, -3426, -3425, -3424, -3423, -3422, -3421, -3420, -3419, 3997, -3416, -3415, -3414, -3413, -3412, -3411, -3410, -3409, -3408, -3407, -3406, -3405, -3404, -3403, -3402, -3401, -3400, -3399, -3398, -3397, -3396, -3395, -3394, -3393, -3392, -3391, -3390, -3389, -3388, -3387, -3386, -3385, -3384, -3383, -3382, -3381, 3998, -3378, -3377, -3376, -3375, -3374, -3373, -3372, -3371, -3370, -3369, -3368, -3367, -3366, 3999, -3363, -3362, -3361, -3360, -3359, -3358, -3357, 4000, -3354, -3353, -3352, -3351, -3350, -3349, -3348, -3347, -3346, -3345, -3344, -3343, -3342, -3341, -3340, -3339, -3338, -3337, -3336, -3335, -3334, -3333, -3332, -3331, -3330, -3329, -3328, -3327, -3326, -3325, -3324, -3323, -3322, -3321, -3320, -3319, -3318, -3317, -3316, -3315, -3314, -3313, -3312, -3311, -3310, -3309, -3308, -3307, -3306, -3305, -3304, -3303, -3302, -3301, -3300, -3299, -3298, -3297, -3296, -3295, -3294, -3293, -3292, -3291, -3290, -3289, -3288, -3287, -3286, -3285, -3284, -3283, -3282, -3281, -3280, -3279, -3278, -3277, -3276, -3275, -3274, -3273, -3272, -3271, -3270, -3269, -3268, -3267, -3266, -3265, -3264, -3263, -3262, -3261, -3260, -3259, -3258, -3257, -3256, -3255, -3254, -3253, -3252, -3251, -3250, -3249, -3248, -3247, -3246, -3245, -3244, -3243, -3242, -3241, -3240, -3239, -3238, -3237, -3236, -3235, -3234, -3233, -3232, -3231, -3230, -3229, -3228, -3227, -3226, -3225, -3224, -3223, -3222, -3221, -3220, -3219, -3218, -3217, -3216, -3215, -3214, -3213, 4001, -3210, -3209, -3208, 4002, -3205, 4003, -3202, -3201, -3200, -3199, -3198, -3197, -3196, -3195, -3194, -3193, -3192, -3191, -3190, -3189, -3188, -3187, -3186, -3185, -3184, -3183, -3182, -3181, -3180, -3179, -3178, -3177, -3176, -3175, -3174, -3173, -3172, -3171, -3170, -3169, -3168, -3167, -3166, -3165, -3164, -3163, -3162, -3161, -3160, -3159, -3158, -3157, -3156, -3155, -3154, -3153, -3152, -3151, -3150, -3149, 4004, -3146, -3145, -3144, -3143, -3142, -3141, -3140, -3139, -3138, -3137, -3136, -3135, -3134, -3133, -3132, -3131, -3130, -3129, -3128, -3127, -3126, -3125, -3124, -3123, -3122, -3121, -3120, -3119, -3118, -3117, -3116, -3115, -3114, -3113, -3112, -3111, -3110, -3109, -3108, -3107, -3106, -3105, -3104, -3103, -3102, -3101, -3100, -3099, -3098, -3097, -3096, -3095, -3094, -3093, -3092, -3091, -3090, -3089, -3088, -3087, -3086, -3085, -3084, -3083, -3082, -3081, -3080, -3079, -3078, -3077, -3076, -3075, -3074, -3073, -3072, -3071, -3070, -3069, -3068, -3067, -3066, -3065, -3064, -3063, -3062, -3061, -3060, -3059, -3058, -3057, -3056, -3055, -3054, -3053, -3052, -3051, -3050, -3049, -3048, -3047, -3046, -3045, -3044, -3043, -3042, -3041, -3040, -3039, -3038, -3037, -3036, -3035, -3034, -3033, -3032, -3031, -3030, -3029, -3028, -3027, -3026, -3025, -3024, -3023, -3022, -3021, -3020, -3019, -3018, -3017, -3016, -3015, -3014, -3013, -3012, -3011, -3010, -3009, -3008, -3007, -3006, -3005, -3004, -3003, -3002, -3001, -3000, -2999, -2998, -2997, -2996, -2995, -2994, -2993, -2992, -2991, -2990, -2989, -2988, -2987, -2986, -2985, -2984, -2983, -2982, -2981, -2980, -2979, -2978, -2977, -2976, -2975, -2974, -2973, -2972, -2971, -2970, -2969, -2968, -2967, 4005, -2964, -2963, -2962, -2961, -2960, -2959, -2958, -2957, 4006, -2954, -2953, -2952, -2951, -2950, -2949, 4007, -2946, -2945, -2944, -2943, -2942, -2941, -2940, -2939, -2938, -2937, -2936, -2935, -2934, -2933, -2932, -2931, -2930, -2929, -2928, -2927, -2926, -2925, -2924, -2923, -2922, -2921, -2920, -2919, -2918, -2917, -2916, -2915, -2914, -2913, -2912, -2911, -2910, -2909, -2908, -2907, -2906, -2905, -2904, -2903, -2902, -2901, -2900, -2899, -2898, -2897, -2896, -2895, -2894, -2893, -2892, -2891, -2890, -2889, -2888, -2887, -2886, -2885, -2884, -2883, -2882, -2881, -2880, -2879, -2878, -2877, -2876, -2875, -2874, -2873, -2872, -2871, -2870, -2869, -2868, -2867, -2866, -2865, -2864, -2863, -2862, -2861, -2860, -2859, -2858, -2857, -2856, -2855, -2854, -2853, -2852, -2851, -2850, -2849, -2848, -2847, -2846, -2845, -2844, -2843, -2842, -2841, -2840, -2839, -2838, -2837, -2836, -2835, -2834, -2833, -2832, -2831, -2830, -2829, -2828, -2827, -2826, -2825, -2824, -2823, -2822, -2821, -2820, -2819, -2818, -2817, -2816, -2815, -2814, -2813, -2812, -2811, -2810, -2809, -2808, -2807, -2806, -2805, -2804, -2803, -2802, -2801, -2800, -2799, -2798, -2797, -2796, -2795, -2794, -2793, -2792, -2791, -2790, -2789, -2788, -2787, 4008, 4009, 4010, 4011, 4012, 4013, -3884, 4014, 4015, 4016, 4017, -3879, 4018, 4019, 4020, 4021, 4022, 4023, 4024, 4025, -3900, 4026, 4027, -3898, 4028, 4029, 4030, -3895, -3894, -3893, 4031, 4032, 4033, 4034, -3889, 4035, 4036, 4037, 4038, 4039, -3930, 4040, -3928, 4041, -3926, 4042, 4043, 4044, 4045, -3921, 4046, 4047, -3919, 4048, 4049, 4050, 4051, -3916, 4052, 4053, -3913, 4054, 4055, -3910, 4056, 4057, 4058, 4059, 4060, -3906, 4061, 4062, -3903, 4063, 4064, 4065, 4066, 4067, 4068, 4069, 4070, 4071, 4072, 4073, 4074, 4075, 4076, 4077, 4078, 4079, 4080, 4081, 4082, 4083, 4084, 4085, 4086, 4087, 4088, 4089, 4090, 4091, 4092, 4093, 4094, -2783, -2782, -2781, -2780, -2779, -2778, -2777, -2776, -2775, -2774, -2773, -2772, -2771, -2770, -2769, -2768, -2767, -2766, -2765, -2764, -2763, -2762, -2761, -2760, -2759, -2758, -2757, -2756, -2755, -2754, -2753, -2752, -2751, -2750, -2749, -2748, -2747, -2746, -2745, -2744, -2743, -2742, -2741, -2740, -2739, -2738, -2737, -2736, -2735, -2734, -2733, -2732, -2731, -2730, -2729, -2728, -2727, -2726, -2725, -2724, -2723, -2722, -2721, -2720, -2719, -2718, -2717, -2716, -2715, -2714, -2713, -2712, -2711, -2710, -2709, -2708, -2707, -2706, -2705, -2704, -2703, -2702, -2701, -2700, -2699, -2698, -2697, -2696, -2695, -2694, -2693, -2692, -2691, -2690, -2689, -2688, -2687, -2686, -2685, -2684, -2683, -2682, -2681, -2680, -2679, -2678, 4095, -2675, -2674, -2673, -2672, -2671, -2670, -2669, -2668, -2667, -2666, -2665, -2664, -2663, -2662, -2661, -2660, -2659, -2658, -2657, -2656, -2655, -2654, -2653, -2652, -2651, -2650, -2649, -2648, -2647, -2646, -2645, -2644, -2643, -2642, -2641, -2640, -2639, -2638, -2637, -2636, -2635, -2634, -2633, -2632, -2631, -2630, -2629, -2628, -2627, -2626, -2625, -2624, -2623, -2622, -2621, -2620, -2619, -2618, -2617, -2616, -2615, -2614, -2613, -2612, -2611, -2610, -2609, -2608, -2607, -2606, -2605, -2604, -2603, -2602, -2601, -2600, -2599, -2598, -2597, -2596, -2595, -2594, -2593, -2592, -2591, -2590, -2589, -2588, -2587, -2586, -2585, -2584, -2583, -2582, -2581, -2580, -2579, -2578, -2577, -2576, -2575, -2574, -2573, -2572, -2571, -2570, -2569, -2568, -2567, -2566, -2565, -2564, -2563, -2562, -2561, -2560, -2559, -2558, -2557, -2556, -2555, -2554, -2553, -2552, -2551, -2550, -2549, -2548, -2547, -2546, 4096, 4097, 4098, 4099, 4100, 4101, 4102, 4103, 4104, 4105, 4106, 4107, 4108, 4109, 4110, 4111, 4112, 4113, 4114, 4115, 4116, 4117, 4118, 4119, 3762, 4120, 4121, -3877, 4122, 4123, 4124, 4125, 4126, 4127, 4128, 4129, 4130, -3869, 3745, 4131, 4132, 4133, 4134, 4135, 4136, 4137, 4138, 4139, 4140, 4141, 4142, 4143, 4144, 4145, 4146, 4147, 4148, 4149, 4150, 4151, 4152, 4153, 4154, 4155, 4156, 4157, 4158, 4159, 4160, 4161, 4162, 4163, 4164, -3851, 4165, 4166, 4167, 4168, 4169, 4170, 4171, 4172, 4173, 4174, 4175, 4176, 4177, 4178, 4179, 4180, 4181, 4182, 4183, 4184, 4185, 4186, 4187, 4188, 4189, 4190, 4191, 4192, 4193, 4194, 4195, 4196, 4197, 4198, 4199, 4200, 4201, 4202, 4203, 4204, 4205, 4206, 4207, 4208, 4209, 4210, 4211, 4212, 4213, 4214, 4215, 3940, 4216, 4217, 4218, 3944, 4219, 4220, 4221, 4222, 4223, 4224, 4225, 4226, 4227, 4228, 4229, 4230, 4231, 4232, 4233, 4234, 4235, 4236, 4237, 4238, 4239, 4240, 4241, 4242, 4243, 4244, 4245, 4246, 4247, 4248, 4249, 4250, 4251]], [[-2544, -2543, -2542, -2541, -2540, -2539, -2538, -2537, -2536, -2535, -2534, 4252, 4253, -2532, -2531, -2530, -2529, -2528, -2527, -2526, -2525, -2524, -2523, -2522, -2521, -2520, -2519, -2518, -2517, -2516, -2515, -2514, -2513, -2512, -2511, -2510, -2509, -2508, -2507, -2506, -2505, -2504, -2503, -2502, -2501, -2500, -2499, -2498, -2497, -2496, -2495, -2494, -2493, -2492, -2491, -2490, -2489, -2488, -2487, -2486, -2485, -2484, -2483, -2482, -2481, -2480, -2479, -2478, -2477, -2476, -2475, -2474, -2473, -2472, -2471, -2470, -2469, -2468, -2467, -2466, -2465, -2464, -2463, -2462, -2461, -2460, -2459, -2458, -2457, -2456, -2455, -2454, -2453, -2452, -2451, -2450, -2449, -2448, -2447, -2446, -2445, -2444, -2443, -2442, -2441, -2440, -2439, -2438, -2437, -2436, -2435, -2434, -2433, -2432, -2431, -2430, -2429, -2428, -2427, -2426, -2425, -2424, -2423, -2422, -2421, -2420, -2419, -2418, -2417, -2416, -2415, -2414, -2413, -2412, -2411, -2410, -2409, -2408, -2407, -2406, -2405, -2404, -2403, -2402, -2401, -2400, -2399, -2398, -2397, -2396, -2395, -2394, -2393, -2392, -2391, -2390, -2389, -2388, -2387, -2386, -2385, -2384, -2383, -2382, -2381, -2380, -2379, -2378, -2377, -2376, -2375, -2374, -2373, -2372, -2371, -2370, -2369, -2368, -2367, -2366, -2365, -2364, -2363, -2362, -2361, -2360, -2359, -2358, -2357, -2356, -2355, -2354, -2353, -2352, -2351, -2350, -2349, -2348, -2347, -2346, -2345, -2344, -2343, -2342, -2341, -2340, -2339, -2338, -2337, -2336, -2335, -2334, -2333, -2332, -2331, -2330, -2329, -2328, -2327, -2326, -2325, -2324, -2323, -2322, -2321, -2320, -2319, -2318, -2317, -2316, -2315, -2314, -2313, -2312, -2311, -2310, -2309, -2308, -2307, -2306, -2305, -2304, -2303, -2302, -2301, -2300, -2299, -2298, -2297, -2296, -2295, -2294, -2293, -2292, -2291, -2290, -2289, -2288, -2287, -2286, -2285, -2284, -2283, -2282, -2281, -2280, -2279, -2278, -2277, -2276, -2275, -2274, -2273, -2272, -2271, -2270, -2269, -2268, -2267, -2266, -2265, -2264, -2263, -2262, -2261, -2260, -2259, -2258, -2257, -2256, -2255, -2254, -2253, -2252, -2251, -2250, -2249, -2248, -2247, -2246, -2245, -2244, -2243, -2242, -2241, -2240, -2239, -2238, -2237, -2236, -2235, -2234, -2233, -2232, -2231, -2230, -2229, -2228, -2227, -2226, -2225, -2224, -2223, -2222, -2221, -2220, -2219, -2218, -2217, -2216, -2215, -2214, -2213, -2212, -2211, -2210, -2209, -2208, -2207, -2206, -2205, -2204, -2203, -2202, -2201, -2200, -2199, -2198, -2197, -2196, -2195, -2194, -2193, -2192, -2191, -2190, -2189, -2188, -2187, -2186, -2185, -2184, -2183, -2182, -2181, -2180, -2179, -2178, -2177, -2176, -2175, -2174, -2173, -2172, -2171, -2170, -2169, -2168, -2167, -2166, -2165, -2164, -2163, -2162, -2161, -2160, -2159, -2158, -2157, -2156, -2155, -2154, -2153, -2152, -2151, -2150, -2149, -2148, -2147, -2146, -2145, -2144, -2143, -2142, -2141, -2140, -2139, -2138, -2137, -2136, -2135, -2134, -2133, -2132, -2131, -2130, -2129, -2128, -2127, -2126, -2125, -2124, -2123, -2122, -2121, -2120, -2119, -2118, -2117, -2116, -2115, -2114, -2113, -2112, -2111, -2110, -2109, -2108, -2107, -2106, -2105, -2104, -2103, -2102, -2101, -2100, -2099, -2098, -2097, -2096, -2095, -2094, -2093, -2092, -2091, -2090, -2089, -2088, -2087, -2086, -2085, -2084, -2083, -2082, -2081, -2080, -2079, -2078, -2077, -2076, -2075, -2074, -2073, -2072, -2071, -2070, -2069, -2068, -2067, -2066, -2065, -2064, -2063, -2062, -2061, -2060, -2059, -2058, -2057, -2056, -2055, -2054, -2053, -2052, -2051, -2050, -2049, -2048, -2047, -2046, -2045, -2044, -2043, -2042, -2041, -2040, -2039, -2038, -2037, -2036, -2035, -2034, -2033, -2032, -2031, -2030, -2029, -2028, -2027, -2026, -2025, -2024, -2023, -2022, -2021, -2020, -2019, -2018, -2017, -2016, -2015, -2014, -2013, -2012, -2011, -2010, -2009, -2008, -2007, -2006, -2005, -2004, -2003, -2002, -2001, -2000, -1999, -1998, -1997, -1996, -1995, -1994, -1993, -1992, -1991, -1990, -1989, -1988, -1987, -1986, -1985, -1984, -1983, -1982, -1981, -1980, -1979, -1978, -1977, -1976, -1975, -1974, -1973, -1972, -1971, -1970, -1969, -1968, -1967, -1966, -1965, -1964, -1963, -1962, -1961, -1960, -1959, -1958, -1957, -1956, -1955, -1954, -1953, -1952, -1951, -1950, -1949, -1948, -1947, -1946, -1945, -1944, -1943, -1942, -1941, -1940, -1939, -1938, -1937, -1936, -1935, -1934, -1933, -1932, -1931, -1930, -1929, -1928, -1927, -1926, -1925, -1924, -1923, -1922, -1921, -1920, -1919, -1918, -1917, -1916, -1915, -1914, -1913, -1912, -1911, -1910, -1909, -1908, -1907, -1906, -1905, -1904, -1903, -1902, -1901, -1900, -1899, -1898, -1897, -1896, -1895, -1894, -1893, -1892, -1891, -1890, -1889, -1888, -1887, -1886, -1885, -1884, -1883, -1882, -1881, -1880, -1879, -1878, -1877, -1876, -1875, -1874, -1873, -1872, -1871, -1870, -1869, -1868, -1867, -1866, -1865, -1864, -1863, -1862, -1861, -1860, -1859, -1858, -1857, -1856, -1855, -1854, -1853, -1852, -1851, -1850, -1849, -1848, -1847, -1846, -1845, -1844, -1843, -1842, -1841, -1840, -1839, -1838, -1837, -1836, -1835, -1834, -1833, -1832, -1831, -1830, -1829, -1828, -1827, -1826, -1825, -1824, -1823, -1822, -1821, -1820, -1819, -1818, -1817, -1816, -1815, -1814, -1813, -1812, -1811, -1810, -1809, -1808, -1807, -1806, -1805, -1804, -1803, -1802, -1801, -1800, -1799, -1798, -1797, -1796, -1795, -1794, -1793, -1792, -1791, -1790, -1789, -1788, -1787, -1786, -1785, -1784, -1783, -1782, -1781, -1780, -1779, -1778, -1777, -1776, -1775, -1774, -1773, -1772, -1771, -1770, -1769, -1768, -1767, -1766, -1765, -1764, -1763, -1762, -1761, -1760, -1759, -1758, -1757, -1756, -1755, -1754, -1753, -1752, -1751, -1750, -1749, -1748, -1747, -1746, -1745, -1744, -1743, -1742, -1741, -1740, -1739, -1738, -1737, -1736, -1735, -1734, -1733, -1732, -1731, -1730, -1729, -1728, -1727, -1726, -1725, -1724, -1723, -1722, -1721, -1720, -1719, -1718, -1717, -1716, -1715, -1714, -1713, -1712, -1711, -1710, -1709, -1708, -1707, -1706, -1705, -1704, -1703, -1702, -1701, -1700, -1699, -1698, -1697, -1696, -1695, -1694, -1693, -1692, -1691, -1690, -1689, -1688, -1687, -1686, -1685, -1684, -1683, -1682, -1681, -1680, -1679, -1678, -1677, -1676, -1675, -1674, -1673, -1672, -1671, -1670, -1669, -1668, -1667, -1666, -1665, -1664, -1663, -1662, -1661, -1660, -1659, -1658, -1657, -1656, -1655, -1654, -1653, -1652, -1651, -1650, -1649, -1648, -1647, -1646, -1645, -1644, -1643, -1642, -1641, -1640, -1639, -1638, -1637, -1636, -1635, -1634, -1633, -1632, -1631, -1630, -1629, -1628, -1627, -1626, -1625, -1624, -1623, -1622, -1621, -1620, -1619, -1618, -1617, -1616, -1615, -1614, -1613, -1612, -1611, -1610, -1609, -1608, -1607, -1606, -1605, -1604, -1603, -1602, -1601, -1600, -1599, -1598, -1597, -1596, -1595, -1594, -1593, -1592, -1591, -1590, -1589, -1588, -1587, -1586, -1585, -1584, -1583, -1582, -1581, -1580, -1579, -1578, -1577, -1576, -1575, -1574, -1573, -1572, -1571, -1570, -1569, -1568, -1567, -1566, -1565, -1564, -1563, -1562, -1561, -1560, -1559, -1558, -1557, -1556, -1555, -1554, -1553, -1552, -1551, -1550, -1549, -1548, -1547, -1546, -1545, -1544, -1543, -1542, -1541, -1540, -1539, -1538, -1537, -1536, -1535, -1534, -1533, -1532, -1531, -1530, -1529, -1528, -1527, -1526, -1525, -1524, -1523, -1522, -1521, -1520, -1519, -1518, -1517, -1516, -1515, -1514, -1513, -1512, -1511, -1510, -1509, -1508, -1507, -1506, -1505, -1504, -1503, -1502, -1501, -1500, -1499, -1498, -1497, -1496, -1495, -1494, -1493, -1492, -1491, -1490, -1489, -1488, -1487, -1486, -1485, -1484, -1483, -1482, 4254, 4255, 4256, 4257, 4258, 4259, 4260, 4261, 4262, 4263, 4264, 4265, 4266, 4267, 4268, 4269, 4270, 4271, 4272, 4273, 4274, 4275, 4276, 4277, 4278, 4279, 4280, 4281, 4282, 4283, 4284, 4285, 4286, 4287, 4288, 4289, 4290, 4291, 4292, 4293, 4294, 4295, 4296, 4297, 4298, 4299, 4300, 4301, 4302, 4303, 4304, 4305, 4306, 4307, 4308, 4309, 4310, 4311, 4312, 4313, 4314, 4315, 4316, 4317, 4318, 4319, 4320, 4321, 4322, 4323, 4324, 4325, 4326, 4327, 4328, 4329, 4330, 4331, 4332, 4333, 4334, 4335, 4336, 4337, 4338, 4339, 4340, 4341]]]}, {"type": "Polygon", "arcs": [[-3702, -3708, -3707, -3706, -3705, -3704, -3703]]}, {"type": "Polygon", "arcs": [[-3720, -3719, -3718, -3717, -3723, -3722, -3721]]}, {"type": "Polygon", "arcs": [[-3715, -3714, -3713, -3712, -3711]]}]}, "ocean": {"type": "GeometryCollection", "geometries": [{"type": "MultiPolygon", "arcs": [[[3672], [3676], [3674], [11], [580], [484], [3677], [3620], [3678], [581], [582], [583], [10], [3675], [3673], [3690], [3691], [584], [578], [579], [535], [588, 589, 586, 587], [594, 595, 596, 597, 591, 592, 593], [585], [590], [1069, 1070, 1071, 1068], [1051, 1052, 1053, 1049, 1050], [1066, 1067, 1064, 1065], [1072, 1073, 1074, 1075, 1076, 1077], [494, 495, 496, 497, 498, 499, 500, 501, 502, 503, 504, 505, 506, 507, 508, 509, 510, 511, 512, 513, 514, 515, 516, 517, 518, 519, 520, 485, 486, 487, 488, 489, 490, 491, 492, 493], [1059, 1060, 1061, 1062, 1058], [1057, 1054, 1055, 1056], [-3992, 1083, -3993, 1086, 1087, 1088, 1089], [1080], [1091, 1092, 1093, 1094, 1095, 1096, 1097, 1090], [1079, 1078], [1098, 1099, 1100, 1101, 1102], [-570, -569, -568, -567, -566], [1063], [1129, 1130, 1131], [1175, 1173, 1174], [1156], [1182], [1161], [1164], [1152], [1158], [1169], [3712, 3713, 3714, 3710, 3711], [1171, 1172, 1170], [3, 4, 5, 0, 1, 2], [561, 562, 563, 564, 560], [6], [7, 8], [1190, 1183, 1184, 1185, 1186, 1187, 1188, 1189], [559], [1122, 1123, 1121], [1177, 1178, 1179, 1180], [1166, 1167, 1168], [1114, 1115, 1112, 1113], [536, 537, 538, 539], [1108, 1109, 1110, 1111, 1103, 1104, 1105, 1106, 1107], [1125, 1126, 1127, 1128, 1124], [541, 542, 540], [1148, 1149, 1150, 1151, 1146, 1147], [1137, 1138, 1132, 1133, 1134, 1135, 1136], [546, 543, 544, 545], [550, 551, 547, 548, 549], [1118, 1119, 1120, 1116, 1117], [1144, 1145, 1139, 1140, 1141, 1142, 1143], [1162], [1163], [521], [1181], [1157], [1160], [1215], [1165], [1154], [1155], [1153], [1219], [1217], [1220], [1218], [1159], [3708], [524], [109], [555], [557], [526], [556], [1206], [553], [552], [558], [1208], [108], [1191], [534], [1195], [1196], [3715], [1176], [1193], [1192], [1197], [554], [1210], [1212], [1209], [1213], [-577], [3700], [1203], [1211], [1199], [1198], [1214], [1204], [532], [1194], [1216], [1201], [1207], [1202], [1205], [533], [1200], [1454], [1450], [1457], [1452], [598], [1453], [599], [1451], [-578], [1461], [1464], [604, 605, 606, 607, 608, 609, 610, 611, 612, 613, 614, 615, 616, 617, 618, 619, 600, 601, 602, 603], [632, 633, 634, 635, 636, 637, 638, 639, 640, 641, 642, 643, 644, 645, 646, 631], [686, 687, 688, 689, 681, 682, 683, 684, 685], [620, 621, 622, 623, 624, 625, 626, 627, 628, 629, 630], [670, 671, 672, 655, 656, 657, 658, 659, 660, 661, 662, 663, 664, 665, 666, 667, 668, 669], [1463], [650, 651, 652, 653, 654, 647, 648, 649], [674, 675, 676, 677, 678, 679, 680, 673], [1466], [1465], [1471], [1472], [1467], [1468], [1470], [1473], [1469], [1031], [1025, 1026, 1027, 1028, 1029, 1030], [529, 530, 531], [1044, 1045, 1046, 1047, 1048, 1041, 1042, 1043], [1035, 1036, 1037, 1032, 1033, 1034], [528, 527], [1039, 1040, 1038], [523], [967], [1023], [1024], [1022], [1019], [1020], [1021], [978, 979, 980, 981, 982, 983, 984, 985, 986, 987, 988], [1001, 996, 997, 998, 999, 1000], [992, 993, 994, 995, 989, 990, 991], [1007, 1008, 1009, 1010, 1011, 1012, 1013, 1014, 1015, 1016, 1017, 1018, 1002, 1003, 1004, 1005, 1006], [974, 975, 976, 977, 971, 972, 973], [968, 969, 970], [-575, -574, -573, -572, -571, -576], [1445], [927, 928, 929, 930, 931, 932, 933, 921, 922, 923, 924, 925, 926], [961, 962, 953, 954, 955, 956, 957, 958, 959, 960], [945, 946, 947, 948, 949, 950, 951, 952, 942, 943, 944], [934, 935, 936, 937, 938, 939, 940, 941], [902, 903, 904, 905, 906, 907, 908, 909, 910, 911, 912, 894, 895, 896, 897, 898, 899, 900, 901], [483], [768, 769, 770, 771], [763, 764, 765, 766, 767, 759, 760, 761, 762], [807, 808, 809, 810, 811, 812, 813, 814, 815, 816, 817, 818, 819, 791, 792, 793, 794, 795, 796, 797, 798, 799, 800, 801, 802, 803, 804, 805, 806], [854, 855, 856, 857, 858, 859, 860, 861, 862, 863, 864, 820, 821, 822, 823, 824, 825, 826, 827, 828, 829, 830, 831, 832, 833, 834, 835, 836, 837, 838, 839, 840, 841, 842, 843, 844, 845, 846, 847, 848, 849, 850, 851, 852, 853], [482], [776, 777, 778, 779, 780, 781, 782, 783, 772, 773, 774, 775], [788, 789, 790, 784, 785, 786, 787], [1443], [1444], [1448], [481], [1446], [1440], [1447], [871, 865, 866, 867, 868, 869, 870], [872, 873, 874, 875, 876, 877], [1442], [890, 891, 892, 893, 878, 879, 880, 881, 882, 883, 884, 885, 886, 887, 888, 889], [913, 914, 915, 916], [918, 919, 920, 917], [733, 727, 728, 729, 730, 731, 732], [99, 100, 101, 102, 103, 104, 105, 106, 107, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98], [1359, 1346, 1347, 1348, 1349, 1350, 1351, 1352, 1353, 1354, 1355, 1356, 1357, 1358], [723, 724, 725, 726, 720, 721, 722], [746, 747, 748, 749, 750, 740, 741, 742, 743, 744, 745], [715, 716, 717, 718, 719, 706, 707, 708, 709, 710, 711, 712, 713, 714], [1380, 1381, 1382, 1383, 1384, 1385, 1386, 1387, 1388, 1389, 1378, 1379], [309, 310, 311, 312, 313, 314, 315, 316, 317, 318, 319, 320, 258, 259, 260, 261, 262, 263, 264, 265, 266, 267, 268, 269, 270, 271, 272, 273, 274, 275, 276, 277, 278, 279, 280, 281, 282, 283, 284, 285, 286, 287, 288, 289, 290, 291, 292, 293, 294, 295, 296, 297, 298, 299, 300, 301, 302, 303, 304, 305, 306, 307, 308], [1258, 1259, 1260, 1261, 1255, 1256, 1257], [1376, 1377, 1360, 1361, 1362, 1363, 1364, 1365, 1366, 1367, 1368, 1369, 1370, 1371, 1372, 1373, 1374, 1375], [393, 394, 395, 353, 354, 355, 356, 357, 358, 359, 360, 361, 362, 363, 364, 365, 366, 367, 368, 369, 370, 371, 372, 373, 374, 375, 376, 377, 378, 379, 380, 381, 382, 383, 384, 385, 386, 387, 388, 389, 390, 391, 392], [1267, 1268, 1269, 1262, 1263, 1264, 1265, 1266], [1272, 1273, 1274, 1275, 1276, 1277, 1270, 1271], [171, 172, 173, 174, 175, 176, 177, 178, 179, 180, 181, 182, 183, 184, 185, 186, 187, 188, 189, 190, 191, 192, 193, 194, 195, 196, 197, 198, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 123, 124, 125, 126, 127, 128, 129, 130, 131, 132, 133, 134, 135, 136, 137, 138, 139, 140, 141, 142, 143, 144, 145, 146, 147, 148, 149, 150, 151, 152, 153, 154, 155, 156, 157, 158, 159, 160, 161, 162, 163, 164, 165, 166, 167, 168, 169, 170], [1249, 1250, 1251, 1252, 1253, 1254], [478, 479, 480, 444, 445, 446, 447, 448, 449, 450, 451, 452, 453, 454, 455, 456, 457, 458, 459, 460, 461, 462, 463, 464, 465, 466, 467, 468, 469, 470, 471, 472, 473, 474, 475, 476, 477], [228, 229, 230, 231, 232, 233, 234, 235, 236, 237, 199, 200, 201, 202, 203, 204, 205, 206, 207, 208, 209, 210, 211, 212, 213, 214, 215, 216, 217, 218, 219, 220, 221, 222, 223, 224, 225, 226, 227], [432, 433, 434, 435, 436, 437, 438, 439, 440, 441, 442, 443, 396, 397, 398, 399, 400, 401, 402, 403, 404, 405, 406, 407, 408, 409, 410, 411, 412, 413, 414, 415, 416, 417, 418, 419, 420, 421, 422, 423, 424, 425, 426, 427, 428, 429, 430, 431], [1435, 1436, 1437, 1438, 1439], [352, 321, 322, 323, 324, 325, 326, 327, 328, 329, 330, 331, 332, 333, 334, 335, 336, 337, 338, 339, 340, 341, 342, 343, 344, 345, 346, 347, 348, 349, 350, 351], [1402, 1403, 1404, 1405, 1406, 1407, 1408, 1409, 1410], [1411, 1412, 1413, 1414, 1415, 1416, 1417, 1418, 1419, 1420, 1421, 1422, 1423], [1434, 1424, 1425, 1426, 1427, 1428, 1429, 1430, 1431, 1432, 1433], [1232, 1233, 1234, 1229, 1230, 1231], [242, 243, 244, 245, 246, 247, 248, 249, 250, 251, 252, 253, 254, 255, 256, 257, 238, 239, 240, 241], [1395, 1396, 1397, 1398, 1399, 1400, 1401, 1390, 1391, 1392, 1393, 1394], [703, 704, 705], [756, 757, 758, 751, 752, 753, 754, 755], [734, 735, 736, 737, 738, 739], [1441], [1244, 1245, 1246, 1247, 1248, 1240, 1241, 1242, 1243], [1309, 1310, 1311, 1312, 1313, 1314, 1315, 1316, 1317, 1302, 1303, 1304, 1305, 1306, 1307, 1308], [1318, 1319, 1320, 1321, 1322], [1331, 1332, 1333, 1334, 1335, 1336, 1337, 1323, 1324, 1325, 1326, 1327, 1328, 1329, 1330], [1282, 1283, 1284, 1285, 1286, 1287, 1288, 1278, 1279, 1280, 1281], [1290, 1291, 1292, 1293, 1294, 1289], [1295, 1296, 1297, 1298, 1299, 1300, 1301], [1339, 1340, 1341, 1342, 1343, 1344, 1345, 1338], [1239, 1235, 1236, 1237, 1238], [3706, 3707, 3701, 3702, 3703, 3704, 3705], [700, 701, 702], [1225, 1226, 1227, 1228, 1221, 1222, 1223, 1224], [79, 80, 81, 82, 83, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78], [699, 690, 691, 692, 693, 694, 695, 696, 697, 698], [49, 50, 51, 52, 53, 54, 55, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48], [3722, 3716, 3717, 3718, 3719, 3720, 3721], [3619], [4342, 3609, 4343, 3606, 4344, 3603, 4345, 3600], [3581], [3669], [3598], [4346, 3596, 4347, 3593], [3668], [3588], [3663], [4348, 3579, 4349, 3576], [3589], [3572], [1475], [3695], [3590], [3694], [3633], [3667], [3574], [3587], [3671], [3660], [3662], [3661], [3621], [3666], [3670], [3665], [3570], [3664], [3571], [3573], [3591], [3611], [-1475], [3586], [965], [964], [966], [522], [963], [3647], [3643], [3642], [3654], [3645], [3568], [3569], [3659], [3637], [3567], [3584], [3658], [3656], [3650], [3641], [3651], [3585], [3640], [3638], [3566], [3652], [3653], [3649], [3648], [3655], [3583], [4350, 3614, 4351, 3617], [3681], [3612], [3682], [3685], [3684], [3679], [3680], [3683], [3582], [3634], [3632], [1449], [3689], [3687], [3686], [3688], [3635], [3636], [3657], [3639], [3630], [3628], [3626], [3625], [3631], [3624], [3623], [3627], [3629], [3565], [3622], [3646], [3644], [3693], [3692], [-526, 4352], [-10, 4353], [-3698, 4354, -3697, -3700, 4355, -3699, 4356, 2785, 2786, 2787, 2788, 2789, 2790, 2791, 2792, 2793, 2794, 2795, 2796, 2797, 2798, 2799, 2800, 2801, 2802, 2803, 2804, 2805, 2806, 2807, 2808, 2809, 2810, 2811, 2812, 2813, 2814, 2815, 2816, 2817, 2818, 2819, 2820, 2821, 2822, 2823, 2824, 2825, 2826, 2827, 2828, 2829, 2830, 2831, 2832, 2833, 2834, 2835, 2836, 2837, 2838, 2839, 2840, 2841, 2842, 2843, 2844, 2845, 2846, 2847, 2848, 2849, 2850, 2851, 2852, 2853, 2854, 2855, 2856, 2857, 2858, 2859, 2860, 2861, 2862, 2863, 2864, 2865, 2866, 2867, 2868, 2869, 2870, 2871, 2872, 2873, 2874, 2875, 2876, 2877, 2878, 2879, 2880, 2881, 2882, 2883, 2884, 2885, 2886, 2887, 2888, 2889, 2890, 2891, 2892, 2893, 2894, 2895, 2896, 2897, 2898, 2899, 2900, 2901, 2902, 2903, 2904, 2905, 2906, 2907, 2908, 2909, 2910, 2911, 2912, 2913, 2914, 2915, 2916, 2917, 2918, 2919, 2920, 2921, 2922, 2923, 2924, 2925, 2926, 2927, 2928, 2929, 2930, 2931, 2932, 2933, 2934, 2935, 2936, 2937, 2938, 2939, 2940, 2941, 2942, 2943, 2944, 2945, -4008, 2948, 2949, 2950, 2951, 2952, 2953, -4007, 2956, 2957, 2958, 2959, 2960, 2961, 2962, 2963, -4006, 2966, 2967, 2968, 2969, 2970, 2971, 2972, 2973, 2974, 2975, 2976, 2977, 2978, 2979, 2980, 2981, 2982, 2983, 2984, 2985, 2986, 2987, 2988, 2989, 2990, 2991, 2992, 2993, 2994, 2995, 2996, 2997, 2998, 2999, 3000, 3001, 3002, 3003, 3004, 3005, 3006, 3007, 3008, 3009, 3010, 3011, 3012, 3013, 3014, 3015, 3016, 3017, 3018, 3019, 3020, 3021, 3022, 3023, 3024, 3025, 3026, 3027, 3028, 3029, 3030, 3031, 3032, 3033, 3034, 3035, 3036, 3037, 3038, 3039, 3040, 3041, 3042, 3043, 3044, 3045, 3046, 3047, 3048, 3049, 3050, 3051, 3052, 3053, 3054, 3055, 3056, 3057, 3058, 3059, 3060, 3061, 3062, 3063, 3064, 3065, 3066, 3067, 3068, 3069, 3070, 3071, 3072, 3073, 3074, 3075, 3076, 3077, 3078, 3079, 3080, 3081, 3082, 3083, 3084, 3085, 3086, 3087, 3088, 3089, 3090, 3091, 3092, 3093, 3094, 3095, 3096, 3097, 3098, 3099, 3100, 3101, 3102, 3103, 3104, 3105, 3106, 3107, 3108, 3109, 3110, 3111, 3112, 3113, 3114, 3115, 3116, 3117, 3118, 3119, 3120, 3121, 3122, 3123, 3124, 3125, 3126, 3127, 3128, 3129, 3130, 3131, 3132, 3133, 3134, 3135, 3136, 3137, 3138, 3139, 3140, 3141, 3142, 3143, 3144, 3145, -4005, 3148, 3149, 3150, 3151, 3152, 3153, 3154, 3155, 3156, 3157, 3158, 3159, 3160, 3161, 3162, 3163, 3164, 3165, 3166, 3167, 3168, 3169, 3170, 3171, 3172, 3173, 3174, 3175, 3176, 3177, 3178, 3179, 3180, 3181, 3182, 3183, 3184, 3185, 3186, 3187, 3188, 3189, 3190, 3191, 3192, 3193, 3194, 3195, 3196, 3197, 3198, 3199, 3200, 3201, -4004, 3204, -4003, 3207, 3208, 3209, -4002, 3212, 3213, 3214, 3215, 3216, 3217, 3218, 3219, 3220, 3221, 3222, 3223, 3224, 3225, 3226, 3227, 3228, 3229, 3230, 3231, 3232, 3233, 3234, 3235, 3236, 3237, 3238, 3239, 3240, 3241, 3242, 3243, 3244, 3245, 3246, 3247, 3248, 3249, 3250, 3251, 3252, 3253, 3254, 3255, 3256, 3257, 3258, 3259, 3260, 3261, 3262, 3263, 3264, 3265, 3266, 3267, 3268, 3269, 3270, 3271, 3272, 3273, 3274, 3275, 3276, 3277, 3278, 3279, 3280, 3281, 3282, 3283, 3284, 3285, 3286, 3287, 3288, 3289, 3290, 3291, 3292, 3293, 3294, 3295, 3296, 3297, 3298, 3299, 3300, 3301, 3302, 3303, 3304, 3305, 3306, 3307, 3308, 3309, 3310, 3311, 3312, 3313, 3314, 3315, 3316, 3317, 3318, 3319, 3320, 3321, 3322, 3323, 3324, 3325, 3326, 3327, 3328, 3329, 3330, 3331, 3332, 3333, 3334, 3335, 3336, 3337, 3338, 3339, 3340, 3341, 3342, 3343, 3344, 3345, 3346, 3347, 3348, 3349, 3350, 3351, 3352, 3353, -4001, 3356, 3357, 3358, 3359, 3360, 3361, 3362, -4000, 3365, 3366, 3367, 3368, 3369, 3370, 3371, 3372, 3373, 3374, 3375, 3376, 3377, -3999, 3380, 3381, 3382, 3383, 3384, 3385, 3386, 3387, 3388, 3389, 3390, 3391, 3392, 3393, 3394, 3395, 3396, 3397, 3398, 3399, 3400, 3401, 3402, 3403, 3404, 3405, 3406, 3407, 3408, 3409, 3410, 3411, 3412, 3413, 3414, 3415, -3998, 3418, 3419, 3420, 3421, 3422, 3423, 3424, 3425, 3426, 3427, 3428, 3429, 3430, -3997, 3433, 3434, 3435, 3436, -3996, 3439, 3440, 3441, 3442, 3443, 3444, 3445, -3995, 3448, 3449, 3450, 3451, 3452, 3453, 3454, 3455, 3456, 3457, 3458, 3459, 3460, 3461, 3462, 3463, 3464, 3465, 3466, 3467, 3468, 3469, 3470, 3471, 3472, 3473, 3474, 3475, 3476, 3477, 3478, 3479, 3480, 3481, 3482, 3483, 3484, 3485, 3486, 3487, 3488, -3994, 3491, 3492, 3493, 3494, 3495, 3496, 3497, 3498, 3499, 3500, 3501, 3502, 3503, 3504, 3505, 3506, 3507, 3508, 3509, 3510, 3511, 3512, 3513, 3514, 3515, 3516, 3517, 3518, 3519, 3520, 3521, 3522, 3523, 3524, 3525, 3526, 3527, 3528, 3529, 3530, 3531, 3532, 3533, 3534, 3535, 3536, 3537, 3538, 3539, 3540, 3541, 3542, 3543, 3544, 3545, 3546, 3547, 3548, 4357, 3550, 3551, 4358, 3553, 4359, 3555, 4360, 3557, 4361, 3559, 4362, 3561, 4363, 3563, 4364, 1477, 4365, 1480, 1481, 1482, 1483, 1484, 1485, 1486, 1487, 1488, 1489, 1490, 1491, 1492, 1493, 1494, 1495, 1496, 1497, 1498, 1499, 1500, 1501, 1502, 1503, 1504, 1505, 1506, 1507, 1508, 1509, 1510, 1511, 1512, 1513, 1514, 1515, 1516, 1517, 1518, 1519, 1520, 1521, 1522, 1523, 1524, 1525, 1526, 1527, 1528, 1529, 1530, 1531, 1532, 1533, 1534, 1535, 1536, 1537, 1538, 1539, 1540, 1541, 1542, 1543, 1544, 1545, 1546, 1547, 1548, 1549, 1550, 1551, 1552, 1553, 1554, 1555, 1556, 1557, 1558, 1559, 1560, 1561, 1562, 1563, 1564, 1565, 1566, 1567, 1568, 1569, 1570, 1571, 1572, 1573, 1574, 1575, 1576, 1577, 1578, 1579, 1580, 1581, 1582, 1583, 1584, 1585, 1586, 1587, 1588, 1589, 1590, 1591, 1592, 1593, 1594, 1595, 1596, 1597, 1598, 1599, 1600, 1601, 1602, 1603, 1604, 1605, 1606, 1607, 1608, 1609, 1610, 1611, 1612, 1613, 1614, 1615, 1616, 1617, 1618, 1619, 1620, 1621, 1622, 1623, 1624, 1625, 1626, 1627, 1628, 1629, 1630, 1631, 1632, 1633, 1634, 1635, 1636, 1637, 1638, 1639, 1640, 1641, 1642, 1643, 1644, 1645, 1646, 1647, 1648, 1649, 1650, 1651, 1652, 1653, 1654, 1655, 1656, 1657, 1658, 1659, 1660, 1661, 1662, 1663, 1664, 1665, 1666, 1667, 1668, 1669, 1670, 1671, 1672, 1673, 1674, 1675, 1676, 1677, 1678, 1679, 1680, 1681, 1682, 1683, 1684, 1685, 1686, 1687, 1688, 1689, 1690, 1691, 1692, 1693, 1694, 1695, 1696, 1697, 1698, 1699, 1700, 1701, 1702, 1703, 1704, 1705, 1706, 1707, 1708, 1709, 1710, 1711, 1712, 1713, 1714, 1715, 1716, 1717, 1718, 1719, 1720, 1721, 1722, 1723, 1724, 1725, 1726, 1727, 1728, 1729, 1730, 1731, 1732, 1733, 1734, 1735, 1736, 1737, 1738, 1739, 1740, 1741, 1742, 1743, 1744, 1745, 1746, 1747, 1748, 1749, 1750, 1751, 1752, 1753, 1754, 1755, 1756, 1757, 1758, 1759, 1760, 1761, 1762, 1763, 1764, 1765, 1766, 1767, 1768, 1769, 1770, 1771, 1772, 1773, 1774, 1775, 1776, 1777, 1778, 1779, 1780, 1781, 1782, 1783, 1784, 1785, 1786, 1787, 1788, 1789, 1790, 1791, 1792, 1793, 1794, 1795, 1796, 1797, 1798, 1799, 1800, 1801, 1802, 1803, 1804, 1805, 1806, 1807, 1808, 1809, 1810, 1811, 1812, 1813, 1814, 1815, 1816, 1817, 1818, 1819, 1820, 1821, 1822, 1823, 1824, 1825, 1826, 1827, 1828, 1829, 1830, 1831, 1832, 1833, 1834, 1835, 1836, 1837, 1838, 1839, 1840, 1841, 1842, 1843, 1844, 1845, 1846, 1847, 1848, 1849, 1850, 1851, 1852, 1853, 1854, 1855, 1856, 1857, 1858, 1859, 1860, 1861, 1862, 1863, 1864, 1865, 1866, 1867, 1868, 1869, 1870, 1871, 1872, 1873, 1874, 1875, 1876, 1877, 1878, 1879, 1880, 1881, 1882, 1883, 1884, 1885, 1886, 1887, 1888, 1889, 1890, 1891, 1892, 1893, 1894, 1895, 1896, 1897, 1898, 1899, 1900, 1901, 1902, 1903, 1904, 1905, 1906, 1907, 1908, 1909, 1910, 1911, 1912, 1913, 1914, 1915, 1916, 1917, 1918, 1919, 1920, 1921, 1922, 1923, 1924, 1925, 1926, 1927, 1928, 1929, 1930, 1931, 1932, 1933, 1934, 1935, 1936, 1937, 1938, 1939, 1940, 1941, 1942, 1943, 1944, 1945, 1946, 1947, 1948, 1949, 1950, 1951, 1952, 1953, 1954, 1955, 1956, 1957, 1958, 1959, 1960, 1961, 1962, 1963, 1964, 1965, 1966, 1967, 1968, 1969, 1970, 1971, 1972, 1973, 1974, 1975, 1976, 1977, 1978, 1979, 1980, 1981, 1982, 1983, 1984, 1985, 1986, 1987, 1988, 1989, 1990, 1991, 1992, 1993, 1994, 1995, 1996, 1997, 1998, 1999, 2000, 2001, 2002, 2003, 2004, 2005, 2006, 2007, 2008, 2009, 2010, 2011, 2012, 2013, 2014, 2015, 2016, 2017, 2018, 2019, 2020, 2021, 2022, 2023, 2024, 2025, 2026, 2027, 2028, 2029, 2030, 2031, 2032, 2033, 2034, 2035, 2036, 2037, 2038, 2039, 2040, 2041, 2042, 2043, 2044, 2045, 2046, 2047, 2048, 2049, 2050, 2051, 2052, 2053, 2054, 2055, 2056, 2057, 2058, 2059, 2060, 2061, 2062, 2063, 2064, 2065, 2066, 2067, 2068, 2069, 2070, 2071, 2072, 2073, 2074, 2075, 2076, 2077, 2078, 2079, 2080, 2081, 2082, 2083, 2084, 2085, 2086, 2087, 2088, 2089, 2090, 2091, 2092, 2093, 2094, 2095, 2096, 2097, 2098, 2099, 2100, 2101, 2102, 2103, 2104, 2105, 2106, 2107, 2108, 2109, 2110, 2111, 2112, 2113, 2114, 2115, 2116, 2117, 2118, 2119, 2120, 2121, 2122, 2123, 2124, 2125, 2126, 2127, 2128, 2129, 2130, 2131, 2132, 2133, 2134, 2135, 2136, 2137, 2138, 2139, 2140, 2141, 2142, 2143, 2144, 2145, 2146, 2147, 2148, 2149, 2150, 2151, 2152, 2153, 2154, 2155, 2156, 2157, 2158, 2159, 2160, 2161, 2162, 2163, 2164, 2165, 2166, 2167, 2168, 2169, 2170, 2171, 2172, 2173, 2174, 2175, 2176, 2177, 2178, 2179, 2180, 2181, 2182, 2183, 2184, 2185, 2186, 2187, 2188, 2189, 2190, 2191, 2192, 2193, 2194, 2195, 2196, 2197, 2198, 2199, 2200, 2201, 2202, 2203, 2204, 2205, 2206, 2207, 2208, 2209, 2210, 2211, 2212, 2213, 2214, 2215, 2216, 2217, 2218, 2219, 2220, 2221, 2222, 2223, 2224, 2225, 2226, 2227, 2228, 2229, 2230, 2231, 2232, 2233, 2234, 2235, 2236, 2237, 2238, 2239, 2240, 2241, 2242, 2243, 2244, 2245, 2246, 2247, 2248, 2249, 2250, 2251, 2252, 2253, 2254, 2255, 2256, 2257, 2258, 2259, 2260, 2261, 2262, 2263, 2264, 2265, 2266, 2267, 2268, 2269, 2270, 2271, 2272, 2273, 2274, 2275, 2276, 2277, 2278, 2279, 2280, 2281, 2282, 2283, 2284, 2285, 2286, 2287, 2288, 2289, 2290, 2291, 2292, 2293, 2294, 2295, 2296, 2297, 2298, 2299, 2300, 2301, 2302, 2303, 2304, 2305, 2306, 2307, 2308, 2309, 2310, 2311, 2312, 2313, 2314, 2315, 2316, 2317, 2318, 2319, 2320, 2321, 2322, 2323, 2324, 2325, 2326, 2327, 2328, 2329, 2330, 2331, 2332, 2333, 2334, 2335, 2336, 2337, 2338, 2339, 2340, 2341, 2342, 2343, 2344, 2345, 2346, 2347, 2348, 2349, 2350, 2351, 2352, 2353, 2354, 2355, 2356, 2357, 2358, 2359, 2360, 2361, 2362, 2363, 2364, 2365, 2366, 2367, 2368, 2369, 2370, 2371, 2372, 2373, 2374, 2375, 2376, 2377, 2378, 2379, 2380, 2381, 2382, 2383, 2384, 2385, 2386, 2387, 2388, 2389, 2390, 2391, 2392, 2393, 2394, 2395, 2396, 2397, 2398, 2399, 2400, 2401, 2402, 2403, 2404, 2405, 2406, 2407, 2408, 2409, 2410, 2411, 2412, 2413, 2414, 2415, 2416, 2417, 2418, 2419, 2420, 2421, 2422, 2423, 2424, 2425, 2426, 2427, 2428, 2429, 2430, 2431, 2432, 2433, 2434, 2435, 2436, 2437, 2438, 2439, 2440, 2441, 2442, 2443, 2444, 2445, 2446, 2447, 2448, 2449, 2450, 2451, 2452, 2453, 2454, 2455, 2456, 2457, 2458, 2459, 2460, 2461, 2462, 2463, 2464, 2465, 2466, 2467, 2468, 2469, 2470, 2471, 2472, 2473, 2474, 2475, 2476, 2477, 2478, 2479, 2480, 2481, 2482, 2483, 2484, 2485, 2486, 2487, 2488, 2489, 2490, 2491, 2492, 2493, 2494, 2495, 2496, 2497, 2498, 2499, 2500, 2501, 2502, 2503, 2504, 2505, 2506, 2507, 2508, 2509, 2510, 2511, 2512, 2513, 2514, 2515, 2516, 2517, 2518, 2519, 2520, 2521, 2522, 2523, 2524, 2525, 2526, 2527, 2528, 2529, 2530, 2531, 2532, 2533, 2534, 2535, 2536, 2537, 2538, 2539, 2540, 2541, 2542, 2543, 2544, 2545, 2546, 2547, 2548, 2549, 2550, 2551, 2552, 2553, 2554, 2555, 2556, 2557, 2558, 2559, 2560, 2561, 2562, 2563, 2564, 2565, 2566, 2567, 2568, 2569, 2570, 2571, 2572, 2573, 2574, 2575, 2576, 2577, 2578, 2579, 2580, 2581, 2582, 2583, 2584, 2585, 2586, 2587, 2588, 2589, 2590, 2591, 2592, 2593, 2594, 2595, 2596, 2597, 2598, 2599, 2600, 2601, 2602, 2603, 2604, 2605, 2606, 2607, 2608, 2609, 2610, 2611, 2612, 2613, 2614, 2615, 2616, 2617, 2618, 2619, 2620, 2621, 2622, 2623, 2624, 2625, 2626, 2627, 2628, 2629, 2630, 2631, 2632, 2633, 2634, 2635, 2636, 2637, 2638, 2639, 2640, 2641, 2642, 2643, 2644, 2645, 2646, 2647, 2648, 2649, 2650, 2651, 2652, 2653, 2654, 2655, 2656, 2657, 2658, 2659, 2660, 2661, 2662, 2663, 2664, 2665, 2666, 2667, 2668, 2669, 2670, 2671, 2672, 2673, 2674, -4096, 2677, 2678, 2679, 2680, 2681, 2682, 2683, 2684, 2685, 2686, 2687, 2688, 2689, 2690, 2691, 2692, 2693, 2694, 2695, 2696, 2697, 2698, 2699, 2700, 2701, 2702, 2703, 2704, 2705, 2706, 2707, 2708, 2709, 2710, 2711, 2712, 2713, 2714, 2715, 2716, 2717, 2718, 2719, 2720, 2721, 2722, 2723, 2724, 2725, 2726, 2727, 2728, 2729, 2730, 2731, 2732, 2733, 2734, 2735, 2736, 2737, 2738, 2739, 2740, 2741, 2742, 2743, 2744, 2745, 2746, 2747, 2748, 2749, 2750, 2751, 2752, 2753, 2754, 2755, 2756, 2757, 2758, 2759, 2760, 2761, 2762, 2763, 2764, 2765, 2766, 2767, 2768, 2769, 2770, 2771, 2772, 2773, 2774, 2775, 2776, 2777, 2778, 2779, 2780, 2781, 2782, 2783, 4366, 1462, 4367]], [[2784, 4368, 1456, 1455, 4369, 1459, 1458, 4370], [1460]]]}]}, "lakes": {"type": "GeometryCollection", "geometries": [{"type": "Polygon", "arcs": [[4371, 4372, 4164, 4373, 4374, 4375, 4376, 4377, 4378, 4379], [4380]]}, {"type": "Polygon", "arcs": [[4381, 4166, 4382], [4383], [4384]]}, {"type": "Polygon", "arcs": [[-4372, 4385, 4386, 4162, 4387]]}, {"type": "Polygon", "arcs": [[4388]]}, {"type": "Polygon", "arcs": [[4389], [4390]]}, {"type": "Polygon", "arcs": [[4391, 4392, 4393, 4394, 4395, 4396, 4397, 4398, 4399, 4400], [4401], [4402], [4403], [4404]]}, {"type": "Polygon", "arcs": [[4405, 4406, 4407, 4408, 4409, 4410, 4156, 4411], [4412], [4413], [4414], [4415], [4416], [4417]]}, {"type": "Polygon", "arcs": [[4418, -4401, 4419, -4406, 4420, 4158], [4421], [4422], [4423], [4424]]}, {"type": "Polygon", "arcs": [[4425, 4426, 4117, 4118, 4119, 3762, 4120, 4121]]}, {"type": "Polygon", "arcs": [[4427]]}, {"type": "Polygon", "arcs": [[4428]]}, {"type": "Polygon", "arcs": [[4429]]}, {"type": "Polygon", "arcs": [[4430]]}, {"type": "Polygon", "arcs": [[4431]]}, {"type": "Polygon", "arcs": [[4432, 4433]]}, {"type": "Polygon", "arcs": [[4434]]}, {"type": "Polygon", "arcs": [[4435]]}, {"type": "Polygon", "arcs": [[4436]]}, {"type": "Polygon", "arcs": [[4437]]}, {"type": "Polygon", "arcs": [[4438]]}, {"type": "Polygon", "arcs": [[4439]]}, {"type": "Polygon", "arcs": [[4440]]}, {"type": "Polygon", "arcs": [[4441]]}, {"type": "Polygon", "arcs": [[4442]]}, {"type": "Polygon", "arcs": [[4443]]}, {"type": "Polygon", "arcs": [[4444, 4445]]}, {"type": "Polygon", "arcs": [[4446]]}, {"type": "Polygon", "arcs": [[4447]]}, {"type": "Polygon", "arcs": [[4448]]}, {"type": "Polygon", "arcs": [[4449]]}, {"type": "Polygon", "arcs": [[4450]]}, {"type": "Polygon", "arcs": [[4451]]}, {"type": "Polygon", "arcs": [[4452]]}, {"type": "Polygon", "arcs": [[4453]]}, {"type": "Polygon", "arcs": [[4454]]}, {"type": "Polygon", "arcs": [[4455]]}, {"type": "Polygon", "arcs": [[4456]]}, {"type": "Polygon", "arcs": [[4457]]}, {"type": "Polygon", "arcs": [[4458]]}, {"type": "Polygon", "arcs": [[4459]]}, {"type": "Polygon", "arcs": [[4460]]}, {"type": "Polygon", "arcs": [[4461]]}, {"type": "Polygon", "arcs": [[4462]]}, {"type": "Polygon", "arcs": [[4463]]}, {"type": "Polygon", "arcs": [[4464]]}, {"type": "Polygon", "arcs": [[4465]]}, {"type": "MultiPolygon", "arcs": [[[4466, 4136, 4137, 4467]], [[4468, 3745, 4131, 4132, 4133, 4134, 4469]]]}, {"type": "Polygon", "arcs": [[4470, 4471]]}, {"type": "Polygon", "arcs": [[4472, 4473]]}, {"type": "Polygon", "arcs": [[4474]]}, {"type": "Polygon", "arcs": [[4475]]}, {"type": "Polygon", "arcs": [[4476]]}, {"type": "Polygon", "arcs": [[4477]]}, {"type": "Polygon", "arcs": [[4478]]}, {"type": "Polygon", "arcs": [[4479, 4037]]}]}, "rivers": {"type": "GeometryCollection", "geometries": [{"type": "MultiLineString", "arcs": [[3723], [3724]]}, {"type": "MultiLineString", "arcs": [[3725], [3726], [3727]]}, {"type": "MultiLineString", "arcs": [[3728], [3729], [3730, 3731], [3732], [3733]]}, {"type": "LineString", "arcs": [3734]}, {"type": "LineString", "arcs": [3735]}, {"type": "LineString", "arcs": [3736]}, {"type": "MultiLineString", "arcs": [[3737], [3738]]}, {"type": "MultiLineString", "arcs": [[3739], [3740], [3741], [3742], [3743]]}, {"type": "LineString", "arcs": [3744]}, {"type": "LineString", "arcs": [3745]}, {"type": "MultiLineString", "arcs": [[3746, 3747], [3748, 3749], [3750], [3751]]}, {"type": "LineString", "arcs": [3752]}, {"type": "LineString", "arcs": [3753]}, {"type": "LineString", "arcs": [3754]}, {"type": "MultiLineString", "arcs": [[3755], [3756], [3757]]}, {"type": "MultiLineString", "arcs": [[3758], [3759, 3760]]}, {"type": "LineString", "arcs": [3761]}, {"type": "LineString", "arcs": [3762, 3763]}, {"type": "MultiLineString", "arcs": [[3764], [3765], [3766]]}, {"type": "LineString", "arcs": [3767]}, {"type": "LineString", "arcs": [3768]}, {"type": "LineString", "arcs": [3769]}, {"type": "MultiLineString", "arcs": [[3770], [3771, 3772], [3773, 3774], [3775]]}, {"type": "LineString", "arcs": [3776, 3777]}, {"type": "MultiLineString", "arcs": [[3778], [3779, 3780], [3781, 3782, 3783], [3784], [3785], [3786], [3787], [3788]]}, {"type": "MultiLineString", "arcs": [[3789, 3790], [3791, 3792, 3793], [3794]]}, {"type": "LineString", "arcs": [3795]}, {"type": "LineString", "arcs": [3796]}, {"type": "MultiLineString", "arcs": [[3797], [3798]]}, {"type": "LineString", "arcs": [3799]}, {"type": "LineString", "arcs": [3800]}, {"type": "LineString", "arcs": [3801]}, {"type": "MultiLineString", "arcs": [[3802], [3803]]}, {"type": "LineString", "arcs": [3804]}, {"type": "LineString", "arcs": [3805]}, {"type": "MultiLineString", "arcs": [[3806], [3807]]}, {"type": "MultiLineString", "arcs": [[3808], [3809], [3810], [3811, 3812, 3813, 3814, 3815, 3816, 3817, 3818, 3819, 3820, 3821, 3822, 3823, 3824, 3825, 3826, 3827, 3828, 3829, 3830, 3831, 3832, 3833], [3834], [3835]]}, {"type": "MultiLineString", "arcs": [[3836, 3837, 3838, 3839, 3840, 3841, 3842, 3843, 3844], [3845], [3846], [3847], [3848]]}, {"type": "LineString", "arcs": [3849]}, {"type": "LineString", "arcs": [3850]}, {"type": "MultiLineString", "arcs": [[3851, 3852, 3853], [3854, 3855, 3856, 3857, 3858, 3859]]}, {"type": "LineString", "arcs": [3860]}, {"type": "LineString", "arcs": [3861]}, {"type": "LineString", "arcs": [3862]}, {"type": "LineString", "arcs": [3863, 3864, 3865, 3866, 3867]}, {"type": "MultiLineString", "arcs": [[3868, 3869], [3870], [3871], [3872, 3873], [3874, 3875], [3876]]}, {"type": "MultiLineString", "arcs": [[3877, 3878, 3879], [3880, 3881], [3882, 3883, 3884], [3885], [3886, 3887], [3888], [3889, 3890], [3891, 3892, 3893, 3894, 3895], [3896, 3897, 3898], [3899], [3900], [3901, 3902], [3903], [3904, 3905], [3906, 3907], [3908, 3909, 3910], [3911, 3912, 3913], [3914, 3915, 3916], [3917], [3918], [3919, 3920, 3921], [3922, 3923], [3924, 3925], [3926, 3927], [3928, 3929], [3930], [3931], [3932], [3933]]}, {"type": "LineString", "arcs": [3934, 3935]}, {"type": "MultiLineString", "arcs": [[3936], [3937]]}, {"type": "MultiLineString", "arcs": [[3938], [3939], [3940, 3941], [3942, 3943], [3944], [3945, 3946]]}, {"type": "MultiLineString", "arcs": [[3947], [3948], [3949]]}, {"type": "MultiLineString", "arcs": [[3950], [3951]]}, {"type": "MultiLineString", "arcs": [[3952, 3953, 3954, 3955, 3956, 3957], [3958], [3959], [3960]]}, {"type": "LineString", "arcs": [3961]}, {"type": "MultiLineString", "arcs": [[3962, 3963], [3964]]}, {"type": "MultiLineString", "arcs": [[3965, 3966], [3967]]}, {"type": "LineString", "arcs": [3968]}, {"type": "LineString", "arcs": [3969]}, {"type": "LineString", "arcs": [3970]}, {"type": "MultiLineString", "arcs": [[3971, 3972, 3973], [3974], [3975]]}, {"type": "LineString", "arcs": [3976]}, {"type": "LineString", "arcs": [3977, 3978]}, {"type": "LineString", "arcs": [3979]}, {"type": "LineString", "arcs": [3980]}, {"type": "MultiLineString", "arcs": [[3981, 3982], [3983], [3984], [3985, 3986, 3987], [3988], [3989]]}, {"type": "LineString", "arcs": [3990]}]}, "countries": {"type": "GeometryCollection", "geometries": [{"type": "MultiPolygon", "properties": {"ct": [-99.14, 39.53]}, "id": "USA", "arcs": [[[-1407, -1406, -1405, -1404, -1403, -1411, -1410, -1409, -1408]], [[-1427, -1426, -1425, -1435, -1434, -1433, -1432, -1431, -1430, -1429, -1428]], [[-1440, -1439, -1438, -1437, -1436]], [[-1292, -1291, -1290, -1295, -1294, -1293]], [[-1297, -1296, -1302, -1301, -1300, -1299, -1298]], [[-1322, -1321, -1320, -1319, -1323]], [[-1237, -1236, -1240, -1239, -1238]], [[-705, -704, -706]], [[-737, -736, -735, -740, -739, -738]], [[-759, -758, -757, -756, -755, -754, -753, -752]], [[-868, -867, -866, -872, -871, -870, -869]], [[-915, -914, -917, -916]], [[-3720, -3719, -3718, -3717, -3723, -3722, -3721]], [[-32, -31, -30, -29, -28, -27, -26, -25, -24, -23, -22, -21, -20, -19, -18, -17, -16, -15, -14, -13, -56, -55, -54, -53, -52, -51, -50, -49, -48, -47, -46, -45, -44, -43, -42, -41, -40, -39, -38, -37, -36, -35, -34, -33]], [[-63, -62, -61, -60, -59, -58, -57, -84, -83, -82, -81, -80, -79, -78, -77, -76, -75, -74, -73, -72, -71, -70, -69, -68, -67, -66, -65, -64]], [[-115, -114, -113, -112, -111, -199, -198, -197, -196, -195, -194, -193, -192, -191, -190, -189, -188, -187, -186, -185, -184, -183, -182, -181, -180, -179, -178, -177, -176, -175, -174, -173, -172, -171, -170, -169, -168, -167, -166, -165, -164, -163, -162, -161, -160, -159, -158, -157, -156, -155, -154, -153, -152, -151, -150, -149, -148, -147, -146, -145, -144, -143, -142, -141, -140, -139, -138, -137, -136, -135, -134, -133, -132, -131, -130, -129, -128, -127, -126, -125, -124, -123, -122, -121, -120, -119, -118, -117, -116]], [[-2544, -2543, -2542, -2541, -2540, -2539, -2538, -2537, -2536, -2535, -2534, 4252, 4253, -2532, -2531, -2530, -2529, -2528, -2527, -2526, -2525, -2524, -2523, -2522, -2521, -2520, -2519, -2518, -2517, -2516, -2515, -2514, -2513, -2512, -2511, -2510, -2509, -2508, -2507, -2506, -2505, -2504, -2503, -2502, -2501, -2500, -2499, -2498, -2497, -2496, -2495, -2494, -2493, -2492, -2491, -2490, -2489, -2488, -2487, -2486, -2485, -2484, -2483, -2482, -2481, -2480, -2479, -2478, -2477, -2476, -2475, -2474, -2473, -2472, -2471, -2470, -2469, -2468, -2467, -2466, -2465, -2464, -2463, -2462, -2461, -2460, -2459, -2458, -2457, -2456, -2455, -2454, -2453, -2452, -2451, -2450, -2449, -2448, -2447, -2446, -2445, -2444, -2443, -2442, -2441, -2440, -2439, -2438, -2437, -2436, -2435, -2434, -2433, -2432, -2431, -2430, -2429, -2428, -2427, -2426, -2425, -2424, -2423, -2422, -2421, -2420, -2419, -2418, -2417, -2416, -2415, -2414, -2413, -2412, -2411, -2410, -2409, -2408, -2407, -2406, -2405, -2404, -2403, -2402, -2401, -2400, -2399, -2398, -2397, -2396, -2395, -2394, -2393, -2392, -2391, -2390, -2389, -2388, -2387, -2386, -2385, -2384, -2383, -2382, -2381, -2380, -2379, -2378, -2377, -2376, -2375, -2374, -2373, -2372, -2371, -2370, -2369, -2368, -2367, -2366, -2365, -2364, -2363, -2362, -2361, -2360, -2359, -2358, -2357, -2356, -2355, -2354, -2353, -2352, -2351, -2350, -2349, -2348, -2347, -2346, -2345, -2344, -2343, -2342, -2341, -2340, -2339, -2338, -2337, -2336, -2335, -2334, -2333, -2332, -2331, -2330, -2329, -2328, -2327, -2326, -2325, -2324, -2323, -2322, -2321, -2320, -2319, -2318, -2317, -2316, -2315, -2314, -2313, -2312, -2311, -2310, -2309, -2308, -2307, -2306, -2305, -2304, -2303, -2302, -2301, -2300, -2299, -2298, -2297, -2296, -2295, -2294, -2293, -2292, -2291, -2290, -2289, -2288, -2287, -2286, -2285, -2284, -2283, -2282, -2281, -2280, -2279, -2278, -2277, -2276, -2275, -2274, -2273, -2272, -2271, -2270, -2269, -2268, -2267, -2266, -2265, -2264, -2263, -2262, -2261, -2260, -2259, -2258, -2257, -2256, -2255, -2254, -2253, -2252, -2251, -2250, -2249, -2248, -2247, -2246, -2245, -2244, -2243, -2242, -2241, -2240, -2239, -2238, -2237, -2236, -2235, -2234, -2233, -2232, -2231, -2230, -2229, -2228, -2227, -2226, -2225, -2224, -2223, -2222, -2221, -2220, -2219, -2218, -2217, -2216, -2215, -2214, -2213, -2212, -2211, -2210, -2209, -2208, -2207, -2206, -2205, -2204, -2203, -2202, -2201, -2200, -2199, -2198, -2197, -2196, -2195, -2194, -2193, -2192, -2191, -2190, -2189, -2188, -2187, -2186, -2185, -2184, -2183, -2182, -2181, -2180, -2179, -2178, -2177, -2176, -2175, -2174, -2173, -2172, -2171, -2170, -2169, -2168, -2167, -2166, -2165, -2164, -2163, -2162, -2161, -2160, -2159, -2158, -2157, -2156, -2155, -2154, -2153, -2152, -2151, -2150, -2149, -2148, -2147, -2146, -2145, -2144, -2143, -2142, -2141, -2140, -2139, -2138, -2137, -2136, -2135, -2134, -2133, -2132, -2131, -2130, -2129, -2128, -2127, -2126, -2125, -2124, -2123, -2122, -2121, -2120, -2119, -2118, -2117, -2116, -2115, -2114, -2113, -2112, -2111, -2110, -2109, -2108, -2107, -2106, -2105, -2104, -2103, -2102, -2101, -2100, -2099, -2098, -2097, -2096, -2095, -2094, -2093, -2092, -2091, -2090, -2089, -2088, -2087, -2086, -2085, -2084, -2083, -2082, -2081, -2080, -2079, -2078, -2077, -2076, -2075, -2074, -2073, -2072, -2071, -2070, -2069, -2068, -2067, -2066, -2065, -2064, -2063, -2062, -2061, -2060, -2059, -2058, -2057, -2056, -2055, -2054, -2053, -2052, -2051, -2050, -2049, -2048, -2047, -2046, -2045, -2044, -2043, -2042, -2041, -2040, -2039, -2038, -2037, -2036, -2035, -2034, -2033, -2032, -2031, -2030, -2029, -2028, -2027, -2026, -2025, -2024, -2023, -2022, -2021, -2020, -2019, -2018, -2017, -2016, -2015, -2014, -2013, -2012, -2011, -2010, -2009, -2008, -2007, -2006, -2005, -2004, -2003, -2002, -2001, -2000, -1999, -1998, -1997, -1996, -1995, -1994, -1993, -1992, -1991, -1990, -1989, -1988, -1987, -1986, -1985, -1984, -1983, -1982, -1981, -1980, -1979, -1978, -1977, -1976, -1975, -1974, -1973, -1972, -1971, -1970, -1969, -1968, -1967, -1966, -1965, -1964, -1963, -1962, -1961, -1960, -1959, -1958, -1957, -1956, -1955, -1954, -1953, -1952, -1951, -1950, -1949, -1948, -1947, -1946, -1945, -1944, -1943, -1942, -1941, -1940, -1939, -1938, -1937, -1936, -1935, -1934, -1933, -1932, -1931, -1930, -1929, -1928, -1927, -1926, -1925, -1924, -1923, -1922, -1921, -1920, -1919, -1918, -1917, -1916, -1915, -1914, -1913, -1912, -1911, -1910, -1909, -1908, -1907, -1906, -1905, -1904, -1903, -1902, -1901, -1900, -1899, -1898, -1897, -1896, -1895, -1894, -1893, -1892, -1891, -1890, -1889, -1888, -1887, -1886, -1885, -1884, -1883, -1882, -1881, -1880, -1879, -1878, -1877, -1876, -1875, -1874, -1873, -1872, -1871, -1870, -1869, -1868, -1867, -1866, -1865, -1864, -1863, -1862, -1861, -1860, -1859, -1858, -1857, -1856, -1855, -1854, -1853, -1852, -1851, -1850, -1849, -1848, -1847, -1846, -1845, -1844, -1843, -1842, -1841, -1840, -1839, -1838, -1837, -1836, -1835, -1834, -1833, -1832, -1831, -1830, -1829, -1828, -1827, -1826, -1825, -1824, -1823, -1822, -1821, -1820, -1819, -1818, -1817, -1816, -1815, -1814, -1813, -1812, -1811, -1810, -1809, -1808, -1807, -1806, -1805, -1804, -1803, -1802, -1801, -1800, -1799, -1798, -1797, -1796, -1795, -1794, -1793, -1792, -1791, -1790, -1789, -1788, -1787, -1786, -1785, -1784, -1783, -1782, -1781, -1780, -1779, -1778, -1777, -1776, -1775, -1774, -1773, -1772, -1771, -1770, -1769, -1768, -1767, -1766, -1765, -1764, -1763, -1762, -1761, -1760, -1759, -1758, -1757, -1756, -1755, -1754, -1753, -1752, -1751, -1750, -1749, -1748, -1747, -1746, -1745, -1744, -1743, -1742, -1741, -1740, -1739, -1738, -1737, -1736, -1735, -1734, -1733, -1732, -1731, -1730, -1729, -1728, -1727, -1726, -1725, -1724, -1723, -1722, -1721, -1720, -1719, -1718, -1717, -1716, -1715, -1714, -1713, -1712, -1711, -1710, -1709, -1708, -1707, -1706, -1705, -1704, -1703, -1702, -1701, -1700, -1699, -1698, -1697, -1696, -1695, -1694, -1693, -1692, -1691, -1690, -1689, -1688, -1687, -1686, -1685, -1684, -1683, -1682, -1681, -1680, -1679, -1678, -1677, -1676, -1675, -1674, -1673, -1672, -1671, -1670, -1669, -1668, -1667, -1666, -1665, -1664, -1663, -1662, -1661, -1660, -1659, -1658, -1657, -1656, -1655, -1654, -1653, -1652, -1651, -1650, -1649, -1648, -1647, -1646, -1645, -1644, -1643, -1642, -1641, -1640, -1639, -1638, -1637, -1636, -1635, -1634, -1633, -1632, -1631, -1630, -1629, -1628, -1627, -1626, -1625, -1624, -1623, -1622, -1621, -1620, -1619, -1618, -1617, -1616, -1615, -1614, -1613, -1612, -1611, -1610, -1609, -1608, -1607, -1606, -1605, -1604, -1603, -1602, -1601, -1600, -1599, -1598, -1597, -1596, -1595, -1594, -1593, -1592, -1591, -1590, -1589, -1588, -1587, -1586, -1585, -1584, -1583, -1582, -1581, -1580, -1579, -1578, -1577, -1576, -1575, -1574, -1573, -1572, -1571, -1570, -1569, -1568, -1567, -1566, -1565, -1564, -1563, -1562, -1561, -1560, -1559, -1558, -1557, -1556, -1555, -1554, -1553, -1552, -1551, -1550, -1549, -1548, -1547, -1546, -1545, -1544, -1543, -1542, -1541, -1540, -1539, -1538, -1537, -1536, -1535, -1534, -1533, -1532, -1531, -1530, -1529, -1528, -1527, -1526, -1525, -1524, -1523, -1522, -1521, -1520, -1519, -1518, -1517, -1516, -1515, -1514, -1513, -1512, -1511, -1510, -1509, -1508, -1507, -1506, -1505, -1504, -1503, -1502, -1501, -1500, -1499, -1498, -1497, -1496, -1495, -1494, -1493, -1492, -1491, -1490, -1489, -1488, -1487, -1486, -1485, -1484, -1483, -1482, 4254, 4255, 4256, 4257, 4258, 4259, 4260, 4261, 4262, 4263, 4264, 4265, 4266, 4267, 4268, 4269, 4270, 4271, 4272, 4273, 4274, 4275, 4276, 4277, 4278, 4279, 4280, 4281, 4282, 4283, 4284, 4285, 4286, 4287, 4288, 4289, 4290, 4291, 4292, 4293, 4294, 4295, 4296, 4297, 4298, 4299, 4300, 4301, 4302, 4303, 4304, 4305, 4306, 4307, 4308, 4309, 4310, 4311, 4312, 4313, 4314, 4315, 4316, 4317, 4318, 4319, 4320, 4321, 4322, 4323, 4324, 4325, 4326, 4327, 4328, 4329, 4330, 4331, 4332, 4333, 4334, 4335, 4336, 4337, 4338, 4339, 4340, 4341]], [[-87, -86, -85, -108, -107, -106, -105, -104, -103, -102, -101, -100, -99, -98, -97, -96, -95, -94, -93, -92, -91, -90, -89, -88]], [[-1386, -1385, -1384, -1383, -1382, -1381, -1380, -1379, -1390, -1389, -1388, -1387]], [[-1394, -1393, -1392, -1391, -1402, -1401, -1400, -1399, -1398, -1397, -1396, -1395]], [[-1423, -1422, -1421, -1420, -1419, -1418, -1417, -1416, -1415, -1414, -1413, -1412, -1424]], [[-257, -256, -255, -254, -253, -252, -251, -250, -249, -248, -247, -246, -245, -244, -243, -242, -241, -240, -239, -258]], [[-349, -348, -347, -346, -345, -344, -343, -342, -341, -340, -339, -338, -337, -336, -335, -334, -333, -332, -331, -330, -329, -328, -327, -326, -325, -324, -323, -322, -353, -352, -351, -350]], [[-1348, -1347, -1360, -1359, -1358, -1357, -1356, -1355, -1354, -1353, -1352, -1351, -1350, -1349]], [[-1279, -1289, -1288, -1287, -1286, -1285, -1284, -1283, -1282, -1281, -1280]], [[-1310, -1309, -1308, -1307, -1306, -1305, -1304, -1303, -1318, -1317, -1316, -1315, -1314, -1313, -1312, -1311]], [[-1334, -1333, -1332, -1331, -1330, -1329, -1328, -1327, -1326, -1325, -1324, -1338, -1337, -1336, -1335]], [[-1346, -1345, -1344, -1343, -1342, -1341, -1340, -1339]], [[-1231, -1230, -1235, -1234, -1233, -1232]], [[-1249, -1248, -1247, -1246, -1245, -1244, -1243, -1242, -1241]], [[-1255, -1254, -1253, -1252, -1251, -1250]], [[-1258, -1257, -1256, -1262, -1261, -1260, -1259]], [[-1266, -1265, -1264, -1263, -1270, -1269, -1268, -1267]], [[-1274, -1273, -1272, -1271, -1278, -1277, -1276, -1275]], [[-711, -710, -709, -708, -707, -720, -719, -718, -717, -716, -715, -714, -713, -712]], [[-722, -721, -727, -726, -725, -724, -723]], [[-731, -730, -729, -728, -734, -733, -732]], [[-749, -748, -747, -746, -745, -744, -743, -742, -741, -751, -750]], [[-780, -779, -778, -777, -776, -775, -774, -773, -784, -783, -782, -781]], [[-789, -788, -787, -786, -785, -791, -790]], [[-1225, -1224, -1223, -1222, -1229, -1228, -1227, -1226]], [[-691, -700, -699, -698, -697, -696, -695, -694, -693, -692]], [[-702, -701, -703]], [[-764, -763, -762, -761, -760, -768, -767, -766, -765]], [[-771, -770, -769, -772]], [[-876, -875, -874, -873, -878, -877]], [[-3702, -3708, -3707, -3706, -3705, -3704, -3703]], [[-921, -920, -919, -918]], [[-935, -942, -941, -940, -939, -938, -937, -936]], [[-948, -947, -946, -945, -944, -943, -953, -952, -951, -950, -949]], [[-354, -396, -395, -394, -393, -392, -391, -390, -389, -388, -387, -386, -385, -384, -383, -382, -381, -380, -379, -378, -377, -376, -375, -374, -373, -372, -371, -370, -369, -368, -367, -366, -365, -364, -363, -362, -361, -360, -359, -358, -357, -356, -355]], [[-425, -424, -423, -422, -421, -420, -419, -418, -417, -416, -415, -414, -413, -412, -411, -410, -409, -408, -407, -406, -405, -404, -403, -402, -401, -400, -399, -398, -397, -444, -443, -442, -441, -440, -439, -438, -437, -436, -435, -434, -433, -432, -431, -430, -429, -428, -427, -426]], [[-468, -467, -466, -465, -464, -463, -462, -461, -460, -459, -458, -457, -456, -455, -454, -453, -452, -451, -450, -449, -448, -447, -446, -445, -481, -480, -479, -478, -477, -476, -475, -474, -473, -472, -471, -470, -469]], [[-288, -287, -286, -285, -284, -283, -282, -281, -280, -279, -278, -277, -276, -275, -274, -273, -272, -271, -270, -269, -268, -267, -266, -265, -264, -263, -262, -261, -260, -259, -321, -320, -319, -318, -317, -316, -315, -314, -313, -312, -311, -310, -309, -308, -307, -306, -305, -304, -303, -302, -301, -300, -299, -298, -297, -296, -295, -294, -293, -292, -291, -290, -289]], [[-1366, -1365, -1364, -1363, -1362, -1361, -1378, -1377, -1376, -1375, -1374, -1373, -1372, -1371, -1370, -1369, -1368, -1367]], [[-210, -209, -208, -207, -206, -205, -204, -203, -202, -201, -200, -238, -237, -236, -235, -234, -233, -232, -231, -230, -229, -228, -227, -226, -225, -224, -223, -222, -221, -220, -219, -218, -217, -216, -215, -214, -213, -212, -211]], [[-817, -816, -815, -814, -813, -812, -811, -810, -809, -808, -807, -806, -805, -804, -803, -802, -801, -800, -799, -798, -797, -796, -795, -794, -793, -792, -820, -819, -818]], [[-833, -832, -831, -830, -829, -828, -827, -826, -825, -824, -823, -822, -821, -865, -864, -863, -862, -861, -860, -859, -858, -857, -856, -855, -854, -853, -852, -851, -850, -849, -848, -847, -846, -845, -844, -843, -842, -841, -840, -839, -838, -837, -836, -835, -834]], [[-879, -894, -893, -892, -891, -890, -889, -888, -887, -886, -885, -884, -883, -882, -881, -880]], [[-904, -903, -902, -901, -900, -899, -898, -897, -896, -895, -913, -912, -911, -910, -909, -908, -907, -906, -905]], [[-934, -933, -932, -931, -930, -929, -928, -927, -926, -925, -924, -923, -922]], [[-960, -959, -958, -957, -956, -955, -954, -963, -962, -961]], [[-614, -613, -612, -611, -610, -609, -608, -607, -606, -605, -604, -603, -602, -601, -620, -619, -618, -617, -616, -615]], [[-623, -622, -621, -631, -630, -629, -628, -627, -626, -625, -624]], [[-636, -635, -634, -633, -632, -647, -646, -645, -644, -643, -642, -641, -640, -639, -638, -637]], [[-672, -671, -670, -669, -668, -667, -666, -665, -664, -663, -662, -661, -660, -659, -658, -657, -656, -673]], [[-676, -675, -674, -681, -680, -679, -678, -677]], [[-650, -649, -648, -655, -654, -653, -652, -651]], [[-686, -685, -684, -683, -682, -690, -689, -688, -687]], [[4171, 4172, 4173, 4174, 4175, 4176, 4177, 4178, 4179, 4180, 4181, 4182, 4183, 4184, 4185, 4186, 4187, 4188, 4189, 4190, 4191, 4192, 4193, 4194, 4195, 4196, 4197, 4198, 4199, 4200, 4201, 4202, 4203, 4204, 4205, 4206, 4207, 4208, 4209, 4210, 4211, 4212, 4213, 4214, 4215, 3940, 4216, 4217, 4218, 3944, 4219, 4220, 4221, 4222, 4223, 4224, 4225, 4226, 4227, 4228, 4229, 4230, 4231, 4232, 4233, 4234, 4235, 4236, 4237, 4238, 4239, 4240, 4241, 4242, 4243, 4244, 4245, 4246, 4247, 4248, 4249, 4250, 4251, -3548, -3547, -3546, -3545, -3544, -3543, -3542, -3541, -3540, -3539, -3538, -3537, -3536, -3535, -3534, -3533, -3532, -3531, -3530, -3529, -3528, -3527, -3526, -3525, -3524, -3523, -3522, -3521, -3520, -3519, -3518, -3517, -3516, -3515, -3514, -3513, -3512, -3511, -3510, -3509, -3508, -3507, -3506, -3505, -3504, -3503, -3502, -3501, -3500, -3499, -3498, -3497, -3496, -3495, -3494, -3493, -3492, 3993, -3489, -3488, -3487, -3486, -3485, -3484, -3483, -3482, -3481, -3480, -3479, -3478, -3477, -3476, -3475, -3474, -3473, -3472, -3471, -3470, -3469, -3468, -3467, -3466, -3465, -3464, -3463, -3462, -3461, -3460, -3459, -3458, -3457, -3456, -3455, -3454, -3453, -3452, -3451, -3450, -3449, 3994, -3446, -3445, -3444, -3443, -3442, -3441, -3440, 3995, -3437, -3436, -3435, -3434, 3996, -3431, -3430, -3429, -3428, -3427, -3426, -3425, -3424, -3423, -3422, -3421, -3420, -3419, 3997, -3416, -3415, -3414, -3413, -3412, -3411, -3410, -3409, -3408, -3407, -3406, -3405, -3404, -3403, -3402, -3401, -3400, -3399, -3398, -3397, -3396, -3395, -3394, -3393, -3392, -3391, -3390, -3389, -3388, -3387, -3386, -3385, -3384, -3383, -3382, -3381, 3998, -3378, -3377, -3376, -3375, -3374, -3373, -3372, -3371, -3370, -3369, -3368, -3367, -3366, 3999, -3363, -3362, -3361, -3360, -3359, -3358, -3357, 4000, -3354, -3353, -3352, -3351, -3350, -3349, -3348, -3347, -3346, -3345, -3344, -3343, -3342, -3341, -3340, -3339, -3338, -3337, -3336, -3335, -3334, -3333, -3332, -3331, -3330, -3329, -3328, -3327, -3326, -3325, -3324, -3323, -3322, -3321, -3320, -3319, -3318, -3317, -3316, -3315, -3314, -3313, -3312, -3311, -3310, -3309, -3308, -3307, -3306, -3305, -3304, -3303, -3302, -3301, -3300, -3299, -3298, -3297, -3296, -3295, -3294, -3293, -3292, -3291, -3290, -3289, -3288, -3287, -3286, -3285, -3284, -3283, -3282, -3281, -3280, -3279, -3278, -3277, -3276, -3275, -3274, -3273, -3272, -3271, -3270, -3269, -3268, -3267, -3266, -3265, -3264, -3263, -3262, -3261, -3260, -3259, -3258, -3257, -3256, -3255, -3254, -3253, -3252, -3251, -3250, -3249, -3248, -3247, -3246, -3245, -3244, -3243, -3242, -3241, -3240, -3239, -3238, -3237, -3236, -3235, -3234, -3233, -3232, -3231, -3230, -3229, -3228, -3227, -3226, -3225, -3224, -3223, -3222, -3221, -3220, -3219, -3218, -3217, -3216, -3215, -3214, -3213, 4001, -3210, -3209, -3208, 4002, -3205, 4003, -3202, -3201, -3200, -3199, -3198, -3197, -3196, -3195, -3194, -3193, -3192, -3191, -3190, -3189, -3188, -3187, -3186, -3185, -3184, -3183, -3182, -3181, -3180, -3179, -3178, -3177, -3176, -3175, -3174, -3173, -3172, -3171, -3170, -3169, -3168, -3167, -3166, -3165, -3164, -3163, -3162, -3161, -3160, -3159, -3158, -3157, -3156, -3155, -3154, -3153, -3152, -3151, -3150, -3149, 4004, -3146, -3145, -3144, -3143, -3142, -3141, -3140, -3139, -3138, -3137, -3136, -3135, -3134, -3133, -3132, -3131, -3130, -3129, -3128, -3127, -3126, -3125, -3124, -3123, -3122, -3121, -3120, -3119, -3118, -3117, -3116, -3115, -3114, -3113, -3112, -3111, -3110, -3109, -3108, -3107, -3106, -3105, -3104, -3103, -3102, -3101, -3100, -3099, -3098, -3097, -3096, -3095, -3094, -3093, -3092, -3091, -3090, -3089, -3088, -3087, -3086, -3085, -3084, -3083, -3082, -3081, -3080, -3079, -3078, -3077, -3076, -3075, -3074, -3073, -3072, -3071, -3070, -3069, -3068, -3067, -3066, -3065, -3064, -3063, -3062, -3061, -3060, -3059, -3058, -3057, -3056, -3055, -3054, -3053, -3052, -3051, -3050, -3049, -3048, -3047, -3046, -3045, -3044, -3043, -3042, -3041, -3040, -3039, -3038, -3037, -3036, -3035, -3034, -3033, -3032, -3031, -3030, -3029, -3028, -3027, -3026, -3025, -3024, -3023, -3022, -3021, -3020, -3019, -3018, -3017, -3016, -3015, -3014, -3013, -3012, -3011, -3010, -3009, -3008, -3007, -3006, -3005, -3004, -3003, -3002, -3001, -3000, -2999, -2998, -2997, -2996, -2995, -2994, -2993, -2992, -2991, -2990, -2989, -2988, -2987, -2986, -2985, -2984, -2983, -2982, -2981, -2980, -2979, -2978, -2977, -2976, -2975, -2974, -2973, -2972, -2971, -2970, -2969, -2968, -2967, 4005, -2964, -2963, -2962, -2961, -2960, -2959, -2958, -2957, 4006, -2954, -2953, -2952, -2951, -2950, -2949, 4007, -2946, -2945, -2944, -2943, -2942, -2941, -2940, -2939, -2938, -2937, -2936, -2935, -2934, -2933, -2932, -2931, -2930, -2929, -2928, -2927, -2926, -2925, -2924, -2923, -2922, -2921, -2920, -2919, -2918, -2917, -2916, -2915, -2914, -2913, -2912, -2911, -2910, -2909, -2908, -2907, -2906, -2905, -2904, -2903, -2902, -2901, -2900, -2899, -2898, -2897, -2896, -2895, -2894, -2893, -2892, -2891, -2890, -2889, -2888, -2887, -2886, -2885, -2884, -2883, -2882, -2881, -2880, -2879, -2878, -2877, -2876, -2875, -2874, -2873, -2872, -2871, -2870, -2869, -2868, -2867, -2866, -2865, -2864, -2863, -2862, -2861, -2860, -2859, -2858, -2857, -2856, -2855, -2854, -2853, -2852, -2851, -2850, -2849, -2848, -2847, -2846, -2845, -2844, -2843, -2842, -2841, -2840, -2839, -2838, -2837, -2836, -2835, -2834, -2833, -2832, -2831, -2830, -2829, -2828, -2827, -2826, -2825, -2824, -2823, -2822, -2821, -2820, -2819, -2818, -2817, -2816, -2815, -2814, -2813, -2812, -2811, -2810, -2809, -2808, -2807, -2806, -2805, -2804, -2803, -2802, -2801, -2800, -2799, -2798, -2797, -2796, -2795, -2794, -2793, -2792, -2791, -2790, -2789, -2788, -2787, 4008, 4009, 4010, 4011, 4012, 4013, -3884, 4014, 4015, 4016, 4017, -3879, 4018, 4019, 4020, 4021, 4022, 4023, 4024, 4025, -3900, 4026, 4027, -3898, 4028, 4029, 4030, -3895, -3894, -3893, 4031, 4032, 4033, 4034, -3889, 4035, 4036, 4037, 4038, 4039, -3930, 4040, -3928, 4041, -3926, 4042, 4043, 4044, 4045, -3921, 4046, 4047, -3919, 4048, 4049, 4050, 4051, -3916, 4052, 4053, -3913, 4054, 4055, -3910, 4056, 4057, 4058, 4059, 4060, -3906, 4061, 4062, -3903, 4063, 4064, 4065, 4066, 4067, 4068, 4069, 4070, 4071, 4072, 4073, 4074, 4075, 4076, 4077, 4078, 4079, 4080, 4081, 4082, 4083, 4084, 4085, 4086, 4087, 4088, 4089, 4090, 4091, 4092, 4093, 4094, -2783, -2782, -2781, -2780, -2779, -2778, -2777, -2776, -2775, -2774, -2773, -2772, -2771, -2770, -2769, -2768, -2767, -2766, -2765, -2764, -2763, -2762, -2761, -2760, -2759, -2758, -2757, -2756, -2755, -2754, -2753, -2752, -2751, -2750, -2749, -2748, -2747, -2746, -2745, -2744, -2743, -2742, -2741, -2740, -2739, -2738, -2737, -2736, -2735, -2734, -2733, -2732, -2731, -2730, -2729, -2728, -2727, -2726, -2725, -2724, -2723, -2722, -2721, -2720, -2719, -2718, -2717, -2716, -2715, -2714, -2713, -2712, -2711, -2710, -2709, -2708, -2707, -2706, -2705, -2704, -2703, -2702, -2701, -2700, -2699, -2698, -2697, -2696, -2695, -2694, -2693, -2692, -2691, -2690, -2689, -2688, -2687, -2686, -2685, -2684, -2683, -2682, -2681, -2680, -2679, -2678, 4095, -2675, -2674, -2673, -2672, -2671, -2670, -2669, -2668, -2667, -2666, -2665, -2664, -2663, -2662, -2661, -2660, -2659, -2658, -2657, -2656, -2655, -2654, -2653, -2652, -2651, -2650, -2649, -2648, -2647, -2646, -2645, -2644, -2643, -2642, -2641, -2640, -2639, -2638, -2637, -2636, -2635, -2634, -2633, -2632, -2631, -2630, -2629, -2628, -2627, -2626, -2625, -2624, -2623, -2622, -2621, -2620, -2619, -2618, -2617, -2616, -2615, -2614, -2613, -2612, -2611, -2610, -2609, -2608, -2607, -2606, -2605, -2604, -2603, -2602, -2601, -2600, -2599, -2598, -2597, -2596, -2595, -2594, -2593, -2592, -2591, -2590, -2589, -2588, -2587, -2586, -2585, -2584, -2583, -2582, -2581, -2580, -2579, -2578, -2577, -2576, -2575, -2574, -2573, -2572, -2571, -2570, -2569, -2568, -2567, -2566, -2565, -2564, -2563, -2562, -2561, -2560, -2559, -2558, -2557, -2556, -2555, -2554, -2553, -2552, -2551, -2550, -2549, -2548, -2547, -2546, 4096, 4097, 4098, 4099, 4100, 4101, 4102, 4103, 4104, 4105, 4106, 4107, 4108, 4109, 4110, 4111, 4112, 4113, 4114, 4115, 4116, 4117, 4118, 4119, 3762, 4120, 4121, -3877, 4122, 4123, 4124, 4125, 4126, 4127, 4128, 4129, 4130, -3869, 3745, 4131, 4132, 4133, 4134, 4135, 4136, 4137, 4138, 4139, 4140, 4141, 4142, 4143, 4144, 4145, 4146, 4147, 4148, 4149, 4150, 4151, 4152, 4153, 4154, 4155, 4156, 4157, 4158, 4159, 4160, 4161, 4162, 4163, 4164, -3851, 4165, 4166, 4167, 4168, 4169, 4170]], [[-491, -490, -489, -488, -487, -486, -521, -520, -519, -518, -517, -516, -515, -514, -513, -512, -511, -510, -509, -508, -507, -506, -505, -504, -503, -502, -501, -500, -499, -498, -497, -496, -495, -494, -493, -492]], [[-597, -596, -595, -594, -593, -592, -598]], [[-1063, -1062, -1061, -1060, -1059]], [[-1066, -1065, -1068, -1067]], [[-1069, -1072, -1071, -1070]], [[-1074, -1073, -1078, -1077, -1076, -1075]], [[-1081]], [[-1093, -1092, -1091, -1098, -1097, -1096, -1095, -1094]], [[-1080, -1079]], [[-1099, -1103, -1102, -1101, -1100]], [[-1167, -1169, -1168]], [[-1178, -1181, -1180, -1179]], [[-1190, -1189, -1188, -1187, -1186, -1185, -1184, -1191]], [[-1143, -1142, -1141, -1140, -1146, -1145, -1144]], [[-539, -538, -537, -540]], [[-1106, -1105, -1104, -1112, -1111, -1110, -1109, -1108, -1107]], [[-1113, -1116, -1115, -1114]], [[-1117, -1121, -1120, -1119, -1118]], [[-1124, -1123, -1122]], [[-528, -529]], [[-531, -530, -532]], [[-1029, -1028, -1027, -1026, -1031, -1030]], [[-1035, -1034, -1033, -1038, -1037, -1036]], [[-1041, -1040, -1039]], [[-1043, -1042, -1049, -1048, -1047, -1046, -1045, -1044]], [[-987, -986, -985, -984, -983, -982, -981, -980, -979, -989, -988]], [[-1000, -999, -998, -997, -1002, -1001]], [[-1019, -1018, -1017, -1016, -1015, -1014, -1013, -1012, -1011, -1010, -1009, -1008, -1007, -1006, -1005, -1004, -1003]], [[-1050, -1054, -1053, -1052, -1051]], [[-1055, -1058, -1057, -1056]], [[-1084, 3991, -1090, -1089, -1088, -1087, 3992]], [[566, 567, 568, 569, 565]], [[-4, -3, -2, -1, -6, -5]], [[-7]], [[-8, -9]], [[-562, -561, -565, -564, -563]], [[-1130, -1132, -1131]], [[-1173, -1172, -1171]], [[-1174, -1176, -1175]], [[-3715, -3714, -3713, -3712, -3711]], [[-541, -543, -542]], [[-545, -544, -547, -546]], [[-551, -550, -549, -548, -552]], [[-1137, -1136, -1135, -1134, -1133, -1139, -1138]], [[-1148, -1147, -1152, -1151, -1150, -1149]], [[-1125, -1129, -1128, -1127, -1126]], [[570, 571, 572, 573, 574, 575]], [[-969, -971, -970]], [[-976, -975, -974, -973, -972, -978, -977]], [[-993, -992, -991, -990, -996, -995, -994]], [[-587, -590, -589, -588]]]}]}, "subunits": {"type": "GeometryCollection", "geometries": [{"type": "MultiPolygon", "properties": {"ct": [-135.02, 59.39], "gu": "CAN"}, "id": "BC", "arcs": [[[4480, 4481, -4482, -4481]], [[4482, 4483, -4484, -4483]], [[4484, -4339, 4338, -4485]], [[4485, -4486, 4486]], [[4487, 4488, -4489, -4488]], [[4489, -4333, 4490, -4491, 4332, -4490]], [[4491, -4329, 4492, -4493, 4328, -4492]], [[4493, -4326, 4325, -4494]], [[4494, 4495, -4496, -4495]], [[4496, 4497, -4498, -4497]], [[4498, -4499, 4499]], [[-4319, 4500, -4501, 4318]], [[4501, 4502, -4503, -4502]], [[4503, -4313, 4504, -4505, 4312, -4504]], [[4505, -4310, 4506, -4507, 4309, -4506]], [[4507, 4508, -4509, -4508]], [[4509, 4510, -4511, -4510]], [[4511, -4302, 4301, -4512]], [[4512, -4300, 4299, -4513]], [[-4299, 4513, -4514, 4298]], [[4514, 4515, -4516, -4515]], [[4516, -4294, 4517, -4518, 4293, -4517]], [[4518, -4291, 4519, -4520, 4290, -4519]], [[4520, -4287, 4286, -4521]], [[4521, 4522, -4523, -4522]], [[-4283, 4282]], [[4523, 4524, -4525, -4524]], [[4525, 4526, -4527, -4526]], [[-4277, 4276, 4527]], [[-4275, 4528, -4529, 4274]], [[4529, -4271, 4530, -4531, 4270, -4530]], [[4531, -4267, 4532, -4533, 4266, -4532]], [[2533, -2534, 4533]], [[2534, 4534, -4535, -2535]], [[4535, 2537, 4536, -4537, -2538, -4536]], [[4537, 2541, 4538, -4539, -2542, -4538]], [[4539, -4540, 4533]]]}, {"type": "MultiPolygon", "properties": {"ct": [-95.16, 49.1], "gu": "CAN"}, "id": "MB", "arcs": [[[4540, -4115, 4114, -4541]], [[4541, 4542, -4543, -4542]]]}, {"type": "MultiPolygon", "properties": {"ct": [-67.8, 46.91], "gu": "CAN"}, "id": "NB", "arcs": [[[-4251, 4250, 4543]], [[4544, -4545, 4545]], [[4546, 4547, -4548, -4547]], [[4548, 4549, -4550, -4549]], [[4550, -4242, 4241, -4551]], [[-4240, 4239, 4551]], [[-4238, 4552, -4553, 4237]], [[4553, -4554, 4554]], [[4555, -4234, 4556, -4557, 4233, -4556]], [[4557, -4558, 4558]], [[4559, 4560, -4561, -4560]], [[4561, 4561, 4561]], [[4562, 4563, -4564, -4563]], [[4564, -4223, 4565, -4566, 4222, -4565]], [[4566, -4220, -3945, 3944, 4219, -4567]], [[4567, 4568, -4569, -4568]], [[4569, 4570, -4571, -4570]], [[4571, -4572, 4572]]]}, {"type": "MultiPolygon", "properties": {"ct": [-92.34, 48.3], "gu": "CAN"}, "id": "ON", "arcs": [[[4573, 3966, -3967, -4574]], [[4574, -4575, 4575]], [[3850, -3851, 4576]], [[-4387, 4386, 4577]], [[4578, 4579, -4580, -4579]], [[4580, 4581, -4582, -4581]], [[4582, -4153, 4583, -4584, 4152, -4583]], [[4584, 4585, -4586, -4585]], [[4586, -4587, 4587]], [[4588, -4589, 4589]], [[4590, -4591, 4591]], [[4592, 4593, -4594, -4593]], [[4594, -4140, 4595, -4596, 4139, -4595]], [[4596, -4597, 4597]], [[4598, 4599, -4600, -4599]], [[4600, 4601, -4602, -4601]], [[4602, 3868, 4603, -4604, -3869, -4603]], [[3870, -3871, 4604]], [[3871, -3872, 4605]], [[4606, 4607, -4608, -4607]], [[3874, 3875, -3876, -3875]], [[3876, -3877, 4608]], [[4609, -3763, 4610, -4611, 3762, -4610]], [[4611, -4612, 4612]], [[4613, -4614, 4614]]]}, {"type": "MultiPolygon", "properties": {"ct": [-71.64, 45.07], "gu": "CAN"}, "id": "QC", "arcs": [[[4615, -4213, 4212, -4616]], [[4616, 4617, -4618, -4617]], [[4618, 4619, -4620, -4619]], [[-4207, 4620, -4621, 4206]], [[4621, -4204, 4622, -4623, 4203, -4622]], [[4623, -4201, 4624, -4625, 4200, -4624]], [[4625, -4198, 4626, -4627, 4197, -4626]], [[-4196, 4627, -4628, 4195]], [[-4193, 4628, -4629, 4192]], [[-4191, 4190, 4629]], [[-4189, 4188, 4630]], [[4631, -4632, 4632]], [[4633, -4185, 4634, 4635, -4183, 4636, -4637, 4182, 4183, 4184, -4634]], [[4637, -4180, -4179, 4638, -4639, 4178, 4179, -4638]], [[4639, 4640, 4641, -4642, 4642]], [[4643, 4644, -4645, -4644]]]}, {"type": "Polygon", "properties": {"ct": [-109.05, 48.99], "gu": "CAN"}, "id": "SK", "arcs": [[4645, 4646, -4647, -4646]]}, {"type": "MultiPolygon", "properties": {"ct": [-140.68, 60.25], "gu": "CAN"}, "id": "YT", "arcs": [[[4647, -4265, 4264, -4648]], [[4648, 4649, -4650, -4649]], [[-4261, 4260, 4650]], [[4651, 4652, -4653, -4652]], [[4653, -4257, 4654, -4655, 4256, -4654]]]}, {"type": "MultiPolygon", "properties": {"ct": [-152.73, 64.58], "gu": "USA"}, "id": "AK", "arcs": [[[-4533, 4266, -4532, 4655, 4268, 4656, -4531, 4270, -4530, 4657, 4272, 4658, -4529, 4274, 4275, 4276, 4277, 4659, -4527, -4526, 4660, 4661, -4525, -4524, 4662, 4282, 4663, -4523, -4522, 4664, 4285, 4286, -4521, 4665, 4288, 4666, -4520, 4290, -4519, 4667, 4668, -4518, 4293, -4517, 4669, 4670, -4516, -4515, 4671, 4672, -4514, 4298, 4299, -4513, 4673, 4301, -4512, 4674, 4675, -4511, -4510, 4676, 4677, -4509, -4508, 4678, 4307, 4679, -4507, 4309, -4506, 4680, 4681, -4505, 4312, -4504, 4682, 4683, -4503, -4502, 4684, 4316, 4685, -4501, 4318, 4319, 4686, -4499, 4687, -4498, -4497, 4688, 4689, -4496, -4495, 4690, 4325, -4494, 4691, 4692, -4493, 4328, -4492, 4693, 4330, 4694, -4491, 4332, -4490, 4695, 4696, -4489, -4488, 4697, 4698, -4486, 4337, 4338, -4485, 4699, 4700, -4484, -4483, 4701, -2544, 4702, -4539, -2542, -4538, 4703, -2540, 4704, -4537, -2538, -4536, 4705, 4706, -4535, -2535, -2534, 4539, 4707, 4253, -2532, 4708, 4709, 4710, -2528, 4711, -2526, 4712, -2524, 4713, 4714, 4715, 4716, -2519, 4717, 4718, -2516, 4719, -2514, 4720, -2512, 4721, -2510, 4722, -2508, 4723, -2506, 4724, -2504, 4725, 4726, 4727, 4728, 4729, -2498, 4730, 4731, 4732, -2494, 4733, -2492, 4734, -2490, 4735, 4736, 4737, -2486, 4738, 4739, -2483, 4740, -2481, 4741, -2479, 4742, 4743, -2476, 4744, -2474, 4745, 4746, -2471, 4747, 4748, -2468, 4749, -2466, 4750, 4751, -2463, 4752, -2461, 4753, 4754, -2458, 4755, -2456, 4756, 4757, -2453, 4758, 4759, 4760, -2449, 4761, 4762, 4763, -2445, 4764, 4765, -2442, 4766, -2440, 4767, 4768, 4769, 4770, 4771, 4772, -2433, 4773, -2431, 4774, 4775, -2428, 4776, 4777, -2425, 4778, 4779, -2422, 4780, -2420, 4781, 4782, 4783, -2416, 4784, 4785, 4786, 4787, -2411, 4788, -2409, 4789, 4790, -2406, 4791, -2404, 4792, 4793, -2401, 4794, -2399, 4795, 4796, 4797, 4798, 4799, 4800, -2392, 4801, -2390, 4802, -2388, -2387, 4803, 4804, 4805, -2383, 4806, 4807, -2380, 4808, -2378, 4809, -2376, 4810, 4811, -2373, 4812, 4813, -2370, 4814, 4815, 4816, 4817, -2365, 4818, 4819, -2362, 4820, 4821, 4822, -2358, 4823, -2356, 4824, -2354, 4825, -2352, 4826, 4827, 4828, 4829, -2347, 4830, 4831, -2344, 4832, 4833, 4834, -2340, 4835, 4836, -2337, 4837, 4838, -2334, 4839, -2332, 4840, -2330, 4841, -2328, 4842, -2326, 4843, -2324, 4844, -2322, 4845, -2320, 4846, 4847, -2317, 4848, -2315, 4849, -2313, 4850, -2311, 4851, 4852, -2308, 4853, -2306, 4854, 4855, 4856, -2302, 4857, -2300, 4858, -2298, 4859, -2296, 4860, -2294, 4861, 4862, -2291, 4863, 4864, 4865, -2287, 4866, 4867, -2284, 4868, -2282, 4869, -2280, 4870, -2278, 4871, 4872, 4873, -2274, 4874, -2272, 4875, -2270, 4876, 4877, 4878, -2266, 4879, 4880, -2263, 4881, 4882, -2260, 4883, 4884, 4885, -2256, 4886, -2254, 4887, -2252, 4888, 4889, -2249, 4890, -2247, 4891, -2245, 4892, -2243, 4893, -2241, 4894, 4895, -2238, 4896, -2236, 4897, 4898, 4899, 4900, -2231, 4901, 4902, -2228, 4903, 4904, -2225, 4905, -2223, 4906, 4907, -2220, 4908, -2218, 4909, 4910, -2215, 4911, 4912, 4913, -2211, 4914, -2209, 4915, -2207, 4916, -2205, 4917, 4918, -2202, 4919, -2200, 4920, 4921, 4922, 4923, 4924, -2194, 4925, 4926, 4927, -2190, 4928, 4929, -2187, 4930, 4931, 4932, 4933, -2182, 4934, 4935, 4936, 4937, 4938, -2176, 4939, 4940, 4941, -2172, -2171, 4942, 4943, 4944, 4945, 4946, 4947, 4948, 4949, -2162, 4950, -2160, 4951, -2158, 4952, -2156, 4953, 4954, -2153, 4955, -2151, 4956, 4957, -2148, 4958, 4959, -2145, 4960, -2143, 4961, 4962, -2140, 4963, -2138, 4964, -2136, 4965, 4966, 4967, 4968, -2131, 4969, -2129, 4970, 4971, 4972, 4973, 4974, 4975, -2122, 4976, 4977, 4978, 4979, 4980, 4981, -2115, 4982, -2113, 4983, -2111, 4984, -2109, 4985, -2107, 4986, -2105, 4987, -2103, 4988, -2101, 4989, -2099, 4990, -2097, 4991, 4992, 4993, -2093, 4994, -2091, 4995, -2089, 4996, 4997, 4998, 4999, -2084, 5000, 5001, 5002, 5003, -2079, 5004, -2077, 5005, 5006, 5007, 5008, -2072, 5009, 5010, 5011, -2068, 5012, 5013, 5014, -2064, 5015, -2062, 5016, -2060, 5017, -2058, 5018, -2056, 5019, -2054, 5020, -2052, 5021, 5022, 5023, 5024, 5025, -2046, 5026, -2044, 5027, 5028, -2041, 5029, -2039, 5030, -2037, 5031, -2035, 5032, -2033, 5033, -2031, 5034, 5035, -2028, 5036, -2026, 5037, -2024, 5038, -2022, 5039, -2020, 5040, -2018, 5041, -2016, 5042, 5043, -2013, 5044, -2011, 5045, -2009, 5046, -2007, 5047, -2005, 5048, 5049, -2002, 5050, -2000, 5051, 5052, -1997, 5053, -1995, 5054, 5055, 5056, -1991, 5057, -1989, 5058, -1987, 5059, -1985, 5060, -1983, 5061, -1981, 5062, -1979, 5063, 5064, 5065, -1975, 5066, -1973, 5067, 5068, -1970, 5069, -1968, 5070, -1966, 5071, 5072, -1963, 5073, -1961, 5074, -1959, 5075, -1957, 5076, 5077, -1954, 5078, -1952, 5079, -1950, 5080, 5081, 5082, -1946, 5083, -1944, 5084, 5085, -1941, 5086, -1939, 5087, 5088, -1936, 5089, -1934, 5090, -1932, 5091, 5092, -1929, 5093, -1927, 5094, 5095, -1924, 5096, -1922, 5097, 5098, 5099, -1918, 5100, 5101, -1915, 5102, 5103, 5104, -1911, 5105, 5106, -1908, 5107, -1906, 5108, -1904, 5109, -1902, 5110, -1900, 5111, -1898, 5112, 5113, -1895, 5114, -1893, 5115, -1891, 5116, -1889, 5117, 5118, 5119, -1885, 5120, -1883, 5121, -1881, 5122, -1879, 5123, -1877, 5124, -1875, 5125, -1873, 5126, -1871, 5127, -1869, 5128, -1867, 5129, -1865, 5130, 5131, 5132, -1861, 5133, -1859, 5134, -1857, 5135, 5136, 5137, -1853, 5138, 5139, -1850, 5140, 5141, -1847, 5142, 5143, 5144, 5145, 5146, 5147, 5148, -1839, 5149, -1837, 5150, 5151, 5152, 5153, -1832, 5154, 5155, 5156, -1828, 5157, -1826, 5158, -1824, 5159, -1822, 5160, 5161, 5162, 5163, -1817, 5164, 5165, -1814, -1813, 5166, 5167, -1810, 5168, 5169, -1807, 5170, -1805, 5171, 5172, -1802, 5173, -1800, 5174, 5175, 5176, 5177, 5178, 5179, -1793, 5180, -1791, 5181, -1789, 5182, 5183, -1786, 5184, -1784, 5185, 5186, -1781, 5187, -1779, 5188, 5189, -1776, 5190, -1774, 5191, 5192, 5193, -1770, 5194, 5195, -1767, 5196, -1765, 5197, 5198, -1762, 5199, -1760, 5200, -1758, 5201, -1756, 5202, 5203, 5204, 5205, -1751, 5206, -1749, 5207, 5208, -1746, 5209, 5210, -1743, 5211, 5212, 5213, -1739, 5214, -1737, 5215, -1735, 5216, -1733, 5217, -1731, 5218, -1729, 5219, 5220, 5221, 5222, -1724, 5223, -1722, 5224, 5225, -1719, 5226, 5227, -1716, 5228, -1714, 5229, 5230, -1711, 5231, -1709, 5232, -1707, 5233, -1705, 5234, -1703, 5235, -1701, 5236, 5237, -1698, 5238, -1696, 5239, 5240, -1693, 5241, 5242, -1690, 5243, 5244, -1687, 5245, -1685, 5246, -1683, 5247, -1681, 5248, -1679, 5249, -1677, 5250, 5251, -1674, 5252, 5253, 5254, 5255, 5256, 5257, 5258, 5259, -1665, 5260, 5261, -1662, 5262, 5263, 5264, 5265, -1657, 5266, 5267, -1654, 5268, 5269, -1651, 5270, 5271, 5272, 5273, -1646, 5274, -1644, 5275, -1642, 5276, 5277, 5278, 5279, 5280, -1636, 5281, 5282, 5283, -1632, 5284, -1630, 5285, 5286, -1627, 5287, 5288, 5289, 5290, 5291, 5292, -1620, 5293, 5294, -1617, 5295, -1615, 5296, -1613, 5297, 5298, -1610, 5299, 5300, 5301, 5302, 5303, -1604, 5304, 5305, -1601, 5306, -1599, 5307, -1597, 5308, -1595, 5309, -1593, 5310, 5311, 5312, 5313, -1588, 5314, 5315, -1585, 5316, 5317, 5318, 5319, 5320, 5321, 5322, 5323, 5324, 5325, 5326, -1573, 5327, 5328, -1570, 5329, 5330, -1567, 5331, -1565, 5332, -1563, 5333, 5334, 5335, -1559, 5336, 5337, -1556, 5338, -1554, 5339, -1552, 5340, 5341, 5342, -1548, 5343, 5344, -1545, 5345, 5346, 5347, 5348, 5349, -1539, 5350, -1537, 5351, 5352, 5353, 5354, 5355, 5356, -1530, 5357, 5358, -1527, 5359, -1525, 5360, -1523, 5361, -1521, 5362, 5363, -1518, 5364, 5365, -1515, 5366, -1513, 5367, -1511, 5368, 5369, 5370, 5371, 5372, -1505, 5373, -1503, 5374, -1501, 5375, 5376, 5377, 5378, -1496, 5379, 5380, -1493, 5381, 5382, 5383, -1489, 5384, -1487, 5385, -1485, 5386, -1483, 5387, 4254, 5388, -4655, 4256, -4654, 5389, 5390, -4653, -4652, 5391, 4260, 4261, 5392, -4650, -4649, 5393, 4264, -4648]], [[5394, -3719, 5395, 5396, -3723, 5397, -3721]], [[5398, -31, 5399, -29, 5400, -27, 5401, 5402, -24, 5403, -22, 5404, 5405, 5406, 5407, -17, 5408, 5409, -14, 5410, 5411, 5412, 5413, 5414, 5415, -51, 5416, -49, 5417, -47, 5418, -45, 5419, 5420, 5421, -41, 5422, 5423, -38, 5424, 5425, -35, 5426, -33]], [[5427, -1344, 5428, 5429, -1341, 5430, -1339, 5431]], [[-700, 5432, -698, 5433, -696, 5434, 5435, 5436, -692, 5437]], [[5438, -1236, -1240, 5439, -1238]], [[5440, -1333, 5441, -1331, 5442, -1329, 5443, -1327, 5444, 5445, 5446, 5447, -1337, 5448, -1335]], [[5449, 5450, 5451, -1286, 5452, -1284, 5453, -1282, 5454, -1280, 5455]], [[-1292, 5456, 5457, -1295, 5458, 5459]], [[5460, -61, 5461, -59, 5462, -57, 5463, 5464, 5465, -81, 5466, -79, 5467, 5468, -76, 5469, -74, 5470, 5471, -71, 5472, 5473, 5474, 5475, -66, 5476, -64, 5477]], [[5478, -1309, 5479, 5480, -1306, 5481, 5482, -1303, -1318, 5483, 5484, -1315, 5485, -1313, 5486, -1311]], [[5487, 5488, 5489, 5490, -1322]], [[5491, 5492, 5493, -1301, 5494, 5495, -1298]], [[-1224, 5496, -1222, -1229, 5497, -1227, 5498, 5499]], [[5500, 5501, 5502, -1245, 5503, -1243, 5504, -1241, 5505]], [[-209, 5506, 5507, -206, 5508, -204, 5509, 5510, 5511, 5512, -238, 5513, 5514, -235, 5515, -233, 5516, -231, 5517, 5518, -228, 5519, 5520, 5521, -224, 5522, -222, 5523, 5524, -219, 5525, 5526, -216, 5527, 5528, 5529, -212, 5530, 5531]], [[5532, 5533, 5534, -422, 5535, -420, 5536, 5537, -417, 5538, 5539, 5540, -413, 5541, -411, 5542, -409, 5543, 5544, -406, 5545, 5546, 5547, -402, 5548, 5549, 5550, -398, 5551, -444, 5552, -442, 5553, -440, 5554, 5555, -437, 5556, -435, 5557, -433, 5558, 5559, 5560, -429, 5561, 5562, -426]], [[-1438, 5563, 5564, -1440, 5565]], [[-466, 5566, -464, 5567, 5568, 5569, -460, 5570, 5571, 5572, -456, 5573, 5574, 5575, -452, 5576, 5577, -449, 5578, -447, 5579, -445, -481, 5580, -479, 5581, 5582, -476, 5583, -474, 5584, -472, 5585, -470, 5586, -468, 5587]], [[5588, 5589, -113, 5590, -111, 5591, -198, 5592, 5593, -195, 5594, -193, 5595, -191, 5596, -189, 5597, 5598, -186, 5599, -184, 5600, 5601, -181, 5602, -179, 5603, -177, 5604, -175, 5605, 5606, -172, 5607, -170, 5608, 5609, 5610, -166, 5611, -164, 5612, -162, 5613, -160, 5614, 5615, -157, 5616, -155, 5617, 5618, -152, 5619, -150, 5620, 5621, -147, 5622, 5623, 5624, 5625, 5626, 5627, 5628, 5629, -138, 5630, -136, 5631, -134, 5632, -132, 5633, -130, 5634, 5635, -127, 5636, 5637, 5638, -123, 5639, -121, 5640, -119, 5641, -117, 5642]], [[-1255, 5643, 5644, -1252, 5645, 5646]], [[5647, 5648, 5649, 5650, -392, 5651, -390, 5652, 5653, 5654, 5655, -385, 5656, -383, 5657, -381, 5658, -379, 5659, -377, 5660, 5661, 5662, -373, 5663, 5664, -370, 5665, 5666, 5667, -366, 5668, -364, 5669, 5670, -361, 5671, -359, 5672, -357, 5673, -355, 5674]], [[5675, -701, -703]], [[-1230, 5676, 5677, 5678, -1232, 5679]], [[5680, -348, 5681, 5682, -345, 5683, -343, 5684, -341, 5685, -339, 5686, -337, 5687, -335, 5688, -333, 5689, -331, 5690, 5691, -328, 5692, 5693, 5694, 5695, 5696, 5697, -353, 5698, 5699, -350]], [[5700, 5701, -1364, 5702, 5703, -1361, -1378, 5704, -1376, 5705, 5706, 5707, 5708, -1371, 5709, -1369, 5710, -1367]], [[5711, 5712, -1405, 5713, -1403, 5714, -1410, 5715, -1408]], [[5716, 5717, 5718, 5719, -3705, 5720, -3703]], [[5721, 5722, -1272, 5723, -1278, 5724, 5725, -1275]], [[-1265, 5726, -1263, 5727, 5728, 5729, -1267, 5730]], [[5731, 5732, 5733, 5734, -1418, 5735, 5736, 5737, -1414, 5738, -1412, -1424, 5739]], [[5740, -1426, 5741, 5742, -1434, 5743, 5744, 5745, -1430, 5746, -1428]], [[5747, -287, 5748, 5749, -284, 5750, 5751, -281, 5752, 5753, -278, 5754, 5755, -275, 5756, 5757, 5758, -271, 5759, -269, 5760, 5761, 5762, -265, 5763, -263, 5764, 5765, 5766, -259, -321, 5767, 5768, -318, 5769, -316, 5770, 5771, -313, 5772, 5773, -310, 5774, -308, 5775, -306, 5776, 5777, -303, 5778, -301, 5779, 5780, -298, 5781, 5782, -295, 5783, -293, 5784, 5785, 5786, -289]], [[5787, -255, 5788, 5789, 5790, 5791, 5792, -249, 5793, 5794, 5795, -245, 5796, -243, 5797, -241, 5798, 5799, -258, 5800]], [[5801, -1256, -1262, 5802, -1260, 5803, 5804]], [[5805, -1385, 5806, 5807, -1382, 5808, -1380, 5809, 5810, 5811, 5812, 5813]], [[-711, 5814, 5815, -708, 5816, 5817, 5818, -718, 5819, -716, 5820, -714, 5821, 5822]], [[5823, -704, -706]], [[5824, -1347, -1360, 5825, -1358, 5826, 5827, -1355, 5828, -1353, 5829, -1351, 5830, -1349]], [[-748, 5831, -746, 5832, -744, 5833, -742, 5834, -751, 5835, 5836]], [[-1393, 5837, -1391, 5838, -1401, 5839, -1399, 5840, -1397, 5841, -1395, 5842]], [[5843, 5844, -756, 5845, -754, 5846, 5847, -759]], [[5848, -735, -740, 5849, 5850, -737]], [[-87, 5851, -85, -108, 5852, -106, 5853, -104, 5854, 5855, 5856, 5857, -99, 5858, 5859, -96, 5860, -94, 5861, 5862, -91, 5863, -89, 5864]], [[5865, -721, -727, 5866, -725, 5867, -723]], [[5868, -728, 5869, -733, 5870, -731, 5871]], [[5872, -787, 5873, -785, -791, 5874, -789]], [[5875, 5876, -778, 5877, 5878, -775, 5879, -773, -784, 5880, -782, 5881]], [[-833, 5882, 5883, -830, 5884, -828, 5885, 5886, 5887, 5888, -823, 5889, -821, 5890, 5891, -863, 5892, 5893, -860, 5894, -858, 5895, -856, 5896, 5897, -853, 5898, -851, 5899, 5900, 5901, 5902, -846, 5903, -844, 5904, 5905, -841, 5906, -839, 5907, 5908, 5909, -835, 5910]], [[5911, -866, -872, 5912, -870, 5913, -868]], [[5914, 5915, 5916, -814, 5917, 5918, 5919, 5920, -809, 5921, -807, 5922, -805, 5923, -803, 5924, -801, 5925, -799, 5926, 5927, -796, 5928, -794, 5929, -792, -820, 5930, -818]], [[5931, 5932, -762, 5933, -760, 5934, 5935, 5936, 5937]], [[5938, 5939, -769, -772]], [[-904, 5940, -902, 5941, 5942, -899, 5943, -897, 5944, -895, -913, 5945, 5946, -910, 5947, -908, 5948, 5949, 5950]], [[-876, 5951, -874, 5952, -878, 5953]], [[-894, 5954, 5955, -891, 5956, 5957, -888, 5958, -886, 5959, -884, 5960, -882, 5961, 5962, 5963]], [[-935, -942, 5964, 5965, 5966, 5967, -937, 5968]], [[-934, 5969, -932, 5970, 5971, 5972, 5973, -927, 5974, -925, 5975, 5976, -922]], [[5977, -947, 5978, -945, 5979, -943, -953, 5980, -951, 5981, -949]], [[-959, 5982, -957, 5983, 5984, 5985, -963, 5986, -961, 5987]], [[-915, 5988, 5989, 5990]], [[5991, 5992, -918, -921]]]}, {"type": "MultiPolygon", "properties": {"ct": [-86.83, 32.8], "gu": "USA"}, "id": "AL", "arcs": [[[-2965, 5993, -2963, 5994, -2961, 5995, -2959, 5996, -2957, -2956, 5997, 5998, 5999, 6000, 6001, 3774, -3727, 3771, 6002, 6003]], [[-550, 6004, -548, 6005, 6006]]]}, {"type": "Polygon", "properties": {"ct": [-92.44, 34.9], "gu": "USA"}, "id": "AR", "arcs": [[6007, -3818, 6008, 6009, -3816, -3815, 6010, 6011, 6012, 6013, 6014]]}, {"type": "Polygon", "properties": {"ct": [-111.66, 34.29], "gu": "USA"}, "id": "AZ", "arcs": [[6015, 4073, 6016, 4075, 6017, 6018, 4078, 6019, 6020, 6021, 6022, 4083, 6023, 4085, -3779, -3781, -3780, -3729, -3784, 6024, 6025, -3782, 3729, -3785, 3730, 6026, 6027, 6028]]}, {"type": "MultiPolygon", "properties": {"ct": [-119.6, 37.25], "gu": "USA"}, "id": "CA", "arcs": [[[-6025, 3783, 3728, 3779, 3780, 3778, 6029, 6030, 4088, 6031, 6032, 4091, 6033, 6034, 4094, -2783, 6035, 6036, 6037, -2779, 6038, 6039, 6040, -2775, 6041, -2773, 6042, 6043, -2770, 6044, -2768, 6045, -2766, 6046, 6047, 6048, -2762, 6049, 6050, -2759, 6051, -2757, 6052, 6053, 6054, -2753, 6055, 6056, 6057, -2749, 6058, -2747, 6059, 6060, 6061, 6062, -2742, 6063, 6064, -2739, 6065, -2737, 6066, 6067, -2734, 6068, -2732, 6069, 6070, -2729, 6071, -2727, 6072, -2725, -2724, 6073, -2722, 6074, 6075, -2719, 6076, 6077, 6078, 6079, -2714, 6080, -2712, 6081, -2710, 6082, -2708, 6083, 6084, -2705, 6085, -2703, 6086, -2701, 6087, -2699, 6088, -2697, 6089, 6090, 6091, -2693, 6092, 6093, 6094, -2689, 6095, -2687, 6096, -2685, 6097, 6098, 6099, -2681, 6100, -2679, 6101, -2677, 6102, 6103]], [[-1041, 6104, -1039]], [[-528, 6105]], [[6106, 6107, 6108, -1038, 6109, -1036]], [[6110, -1042, 6111, -1048, 6112, -1046, 6113, -1044]], [[6114, -530, -532]], [[6115, 6116, 6117, -1026, 6118, -1030]]]}, {"type": "Polygon", "properties": {"ct": [-105.53, 39], "gu": "USA"}, "id": "CO", "arcs": [[6119, 6120, 6121, 6122, 6123, 6124]]}, {"type": "Polygon", "properties": {"ct": [-72.73, 41.63], "gu": "USA"}, "id": "CT", "arcs": [[6125, -3438, -3437, 6126, 6127, -3434, -3433, 6128, 6129]]}, {"type": "Polygon", "properties": {"ct": [-77.02, 38.92], "gu": "USA"}, "id": "DC", "arcs": [[-3868, 6130, 6131]]}, {"type": "Polygon", "properties": {"ct": [-75.51, 38.99], "gu": "USA"}, "id": "DE", "arcs": [[6132, 6133, 6134, -3378, 6135, 6136, -3375, 6137, 6138, 6139, 6140, -3370, 6141, 6142, -3367, 6143, -3365]]}, {"type": "MultiPolygon", "properties": {"ct": [-82.5, 28.67], "gu": "USA"}, "id": "FL", "arcs": [[[-6004, 6144, 3725, 6145, 6146, 6147, -3089, 6148, -3087, 6149, -3085, 6150, -3083, 6151, 6152, 6153, 6154, 6155, 6156, 6157, -3075, 6158, -3073, 6159, 6160, -3070, 6161, -3068, 6162, 6163, -3065, 6164, -3063, 6165, -3061, 6166, 6167, -3058, 6168, 6169, -3055, 6170, 6171, 6172, -3051, 6173, 6174, -3048, 6175, -3046, 6176, 6177, -3043, 6178, -3041, 6179, -3039, 6180, 6181, -3036, 6182, 6183, -3033, 6184, -3031, 6185, -3029, 6186, 6187, -3026, 6188, 6189, -3023, 6190, -3021, 6191, -3019, 6192, -3017, 6193, -3015, 6194, 6195, -3012, 6196, 6197, 6198, -3008, 6199, -3006, 6200, 6201, -3003, 6202, 6203, 6204, 6205, -2998, 6206, -2996, -2995, 6207, -2993, 6208, -2991, 6209, 6210, 6211, 6212, -2986, 6213, 6214, 6215, -2982, 6216, 6217, 6218, -2978, 6219, 6220, -2975, 6221, 6222, 6223, -2971, 6224, 6225, 6226, -2967, -2966]], [[-1129, 6227, -1127, 6228, -1125]], [[6229, 6230, -1175]], [[-1181, 6231, -1179, 6232]], [[-1169, 6233, -1167]], [[-1189, 6234, 6235, 6236, 6237, -1184, -1191, 6238]], [[6239, -9]], [[-7]], [[6240, -561, -565, 6241, -563]], [[-3, 6242, 6243, 6244, 6245, 6246]], [[6247, 6248, -1171]], [[-3715, 6249, -3713, 6250, -3711]]]}, {"type": "MultiPolygon", "properties": {"ct": [-83.46, 32.66], "gu": "USA"}, "id": "GA", "arcs": [[[-6003, -3772, 3726, -3775, -6002, 6251, 6252, 6253, 3777, 3976, 3761, 3951, 3754, 3950, -3107, 6254, -3105, 6255, -3103, 6256, -3101, 6257, 6258, -3098, 6259, -3096, 6260, 6261, 6262, 6263, -6146, -3726, -6145]], [[-1132, 6264, 6265]]]}, {"type": "MultiPolygon", "properties": {"ct": [-155.51, 19.61], "gu": "USA"}, "id": "HI", "arcs": [[[6266, -674, -681, 6267, 6268, 6269, 6270, -676]], [[-649, 6271, 6272, 6273, -653, 6274, -651, 6275]], [[6276, -670, 6277, 6278, -667, 6279, 6280, 6281, 6282, -662, 6283, 6284, 6285, 6286, 6287, -656, -673, 6288]], [[6289, 6290, 6291, -629, 6292, -627, 6293, -625, 6294, -623, 6295]], [[-636, 6296, -634, 6297, -632, -647, 6298, 6299, -644, 6300, 6301, 6302, 6303, 6304, 6305, 6306]], [[-684, 6307, -682, 6308, -689, 6309, 6310, -686, 6311]], [[-613, 6312, 6313, -610, 6314, -608, 6315, -606, 6316, -604, 6317, 6318, 6319, -620, 6320, -618, 6321, 6322, -615, 6323]]]}, {"type": "Polygon", "properties": {"ct": [-93.5, 42.09], "gu": "USA"}, "id": "IA", "arcs": [[6324, 6325, -3840, 6326, 6327, 6328, 6329, -3831, 6330, 6331, -3829]]}, {"type": "Polygon", "properties": {"ct": [-114.65, 44.39], "gu": "USA"}, "id": "ID", "arcs": [[6332, 6333, 6334, 6335, 6336, 6337, 3954, 6338, 6339, 3956, 6340, 6341, 4101, 6342]]}, {"type": "Polygon", "properties": {"ct": [-89.2, 40.07], "gu": "USA"}, "id": "IL", "arcs": [[3828, -6332, 6343, 6344, -4396, 6345, 6346, 3978, 3852, 3853, 3826, 3827]]}, {"type": "Polygon", "properties": {"ct": [-86.28, 39.91], "gu": "USA"}, "id": "IN", "arcs": [[-3979, -6347, 6347, -4394, 6348, 6349, 6350, 6351, 3859, 3851]]}, {"type": "Polygon", "properties": {"ct": [-98.37, 38.49], "gu": "USA"}, "id": "KS", "arcs": [[-6123, 6352, 6353, 3843, 6354, 6355]]}, {"type": "MultiPolygon", "properties": {"ct": [-85.28, 37.53], "gu": "USA"}, "id": "KY", "arcs": [[[-3854, -3853, -3852, -3860, -6352, 6356, -3858, 6357, 6358, 6359, 6360, 6361, 6362, 3825]], [[6363, 6364, 3821, 6365]]]}, {"type": "MultiPolygon", "properties": {"ct": [-92.02, 31.09], "gu": "USA"}, "id": "LA", "arcs": [[[-6012, 6366, -3813, 6367, -2947, -2946, 6368, 6369, 6370, -2942, 6371, -2940, 6372, 6373, -2937, 6374, 6375, 6376, 6377, -2932, 6378, -2930, 6379, -2928, 6380, -2926, 6381, 6382, -2923, 6383, -2921, 6384, 6385, -2918, 6386, -2916, 6387, -2914, 6388, 6389, 6390, -2910, 6391, -2908, 6392, -2906, 6393, -2904, 6394, 6395, -2901, 6396, -2899, 6397, -2897, 6398, 6399, 6400, -2893, 6401, 6402, 6403, -2889, 6404, 6405, 6406, 6407, 6408, 6409, 6410, 6411, -2880, 6412, -2878, 6413, 6414, 6415, 6416, -2873, 6417, -2871, 6418, 6419, -2868, 6420, 6421, 6422, -2864, 6423, -2862, 6424, -2860, 6425, 6426, 6427, -2856, 6428]], [[6429, 6430, -1135, 6431, 6432, 6433, -1138]], [[6434, 6435, 6436, 6437, 6438, 6439]], [[-543, 6440, -541]], [[6441, -1142, 6442, 6443, -1146, 6444, -1144]]]}, {"type": "MultiPolygon", "properties": {"ct": [-71.82, 42.27], "gu": "USA"}, "id": "MA", "arcs": [[[-6130, 6445, 6446, 6447, -3490, -3489, 6448, 6449, -3486, 6450, -3484, 6451, -3482, 6452, 6453, -3479, 6454, -3477, 6455, 6456, -3474, 6457, 6458, 6459, 6460, -3469, 6461, -3467, 6462, -3465, 6463, -3463, 6464, 6465, 6466, -3459, 6467, 6468, -3456, 6469, -3454, 6470, 6471, -3451, 6472, -3449, -3448, 6473]], [[-1066, 6474, -1068, 6475]], [[6476, 6477, -1078, 6478, 6479, -1075]]]}, {"type": "MultiPolygon", "properties": {"ct": [-76.8, 39.07], "gu": "USA"}, "id": "MD", "arcs": [[[-6132, 6480, -3866, 6481, 6482, -3864, -3936, 6483, 6484, -6133, -3364, -3363, 6485, 6486, 6487, -3359, 6488, -3357, -3356, 6489, 6490, -3339, 6491, -3337, 6492, -3335, 6493, -3333, 6494, -3331, 6495, -3329, 6496, -3327, 6497, -3325, 6498, -3323, 6499, -3321, 6500, -3319, 6501, -3317, 6502, -3315, 6503, 6504, 6505, 6506, -3310, 6507, -3308, 6508, 6509, -3305, 6510, 6511, -3302, 6512, -3300, 6513, -3298, 6514, -3296, 6515, 6516, 6517, 6518, -3291, 6519, 6520, 6521, 6522, -3286, 6523, -3284, 6524, 6525, 6526, -3280, 6527, -3278, 6528, 6529, -3275, 6530, -3273, 6531, 6532, -3270, 6533, 6534, -3267, 6535, 6536, -3264, 6537, 6538, 6539]], [[6540, 6541, 6542, -1089, 6543, 6544, -1086]]]}, {"type": "MultiPolygon", "properties": {"ct": [-69.24, 45.4], "gu": "USA"}, "id": "ME", "arcs": [[[-4572, 6545, 6546, -4571, -4570, 6547, 6548, -4569, -4568, 6549, 3944, 4219, -4567, 6550, 6551, -4566, 4222, -4565, 6552, 6553, -4564, -4563, 6554, 4226, 4227, 6555, -4561, -4560, 6556, 6557, -4558, 4231, 6558, -4557, 4233, -4556, 6559, -4554, 6560, 6561, -4553, 4237, 4238, 4239, 4240, 4241, -4551, 6562, 4243, 6563, -4550, -4549, 6564, 6565, -4548, -4547, 6566, 4248, -4545, 6567, 4250, 4251, -3548, 6568, 6569, 6570, -3544, 6571, -3542, 6572, 6573, 6574, -3538, 6575, -3536, 6576, -3534, 6577, -3532, 6578, 6579, -3529, 6580, -3527, 6581, -3525, 6582, -3523, 6583, 6584, -3520, 6585, 6586, 6587, 6588, 6589, 6590, -3513, 6591, -3511, 6592, 6593, 6594, 6595, 6596, -3505, 6597, -3503, 6598, -3501, 6599, 6600, -3498, 6601, 6602, -3495, 6603, -3493, 6604, -4635, 4184, -4634, 6605, 6606, -4632, 4187, 4188, 4189, 4190, 6607, -4629, 4192, 4193, 6608, -4628, 4195, 6609, -4627, 4197, -4626, 6610, 6611, -4625, 4200, -4624, 6612, 6613, -4623, 4203, -4622, 6614, 6615, -4621, 4206, 4207, 6616, -4620, -4619, 6617, 6618, -4618, -4617, 6619, 4212, -4616, 6620, 4214]], [[6621, -596, 6622, -594, 6623, -592, -598]], [[-587, -590, 6624, 6625]]]}, {"type": "MultiPolygon", "properties": {"ct": [-84.63, 43.49], "gu": "USA"}, "id": "MI", "arcs": [[[-4580, -4579, 6626, 6627, 4386, -4387, -4386, -4380, 6628, 6629, -6350, 6630, -4392, -4419, 6631]], [[6632, 6633, -4407, -4420, -4400, 6634]], [[-4418]], [[-4417]], [[-4402]], [[-4425]], [[-4422]], [[-4423]], [[-4424]], [[-4403]]]}, {"type": "Polygon", "properties": {"ct": [-94.31, 46.31], "gu": "USA"}, "id": "MN", "arcs": [[4114, -4541, 6635, -4614, 6636, 6637, -4612, 4118, 6638, -4611, 3762, -4610, 6639, 4121, -3877, 4122, 6640, -3876, -3875, 6641, 6642, -4608, -4607, 6643, -3872, 6644, 4128, 6645, -3871, 6646, -4604, -3869, -4603, 6647, 4131, 6648, -4602, -4601, 6649, 6650, -4600, -4599, 6651, 6652, -4597, 4137, 6653, -4596, 4139, -4595, 6654, 6655, -4594, -4593, 6656, 4143, 6657, -4591, 4145, -4589, 6658, 6659, -4587, 4148, 6660, -4586, -4585, 6661, 6662, -4584, 4152, -4583, 6663, 6664, -4582, -4581, 6665, -4410, 6666, -3833, 6667, -6329, 6668, 6669, 6670, 4113]]}, {"type": "Polygon", "properties": {"ct": [-92.49, 38.38], "gu": "USA"}, "id": "MO", "arcs": [[-6015, 6671, -6355, -3844, -6354, 6672, -3842, 6673, -6325, -3828, -3827, -3826, -6363, 6674, -3824, 6675, -6366, -3822, -6365, 6676, -3820, 6677]]}, {"type": "MultiPolygon", "properties": {"ct": [-89.67, 32.77], "gu": "USA"}, "id": "MS", "arcs": [[[-5999, -5998, -2955, 6678, -2953, 6679, -2951, 6680, -2949, -2948, -6368, 3812, -6367, -6011, 3814, 3815, -6010, 6681]], [[6682, -544, 6683, -546]]]}, {"type": "Polygon", "properties": {"ct": [-109.63, 47.03], "gu": "USA"}, "id": "MT", "arcs": [[-4647, -4646, 6684, 4106, 6685, 6686, 6687, 6688, -6333, 6689, 4103, 6690]]}, {"type": "MultiPolygon", "properties": {"ct": [-79.41, 35.56], "gu": "USA"}, "id": "NC", "arcs": [[[-6253, 6691, 6692, 6693, 6694, -3201, 6695, 6696, 6697, 6698, 6699, -3195, 6700, -3193, 6701, -3191, 6702, 6703, -3188, 6704, -3186, 6705, -3184, 6706, 6707, 6708, -3180, 6709, -3178, 6710, 6711, -3175, 6712, 6713, -3172, 6714, 6715, -3169, 6716, -3167, 6717, 6718, -3164, 6719, -3162, 6720, -3160, 6721, -3158, 6722, 6723, 6724, -3154, 6725, 6726, -3151, 6727, -3149, -3148, 6728]], [[6729, 6730, 6731, 6732, -3208, -3207]], [[-1081]], [[-1093, 6733, -1091, 6734, 6735, 6736, -1095, 6737]], [[6738, -1079]], [[-1103, 6739, 6740, 6741, -1099]], [[567, 6742, 569, 6743, 6744]]]}, {"type": "Polygon", "properties": {"ct": [-100.46, 47.44], "gu": "USA"}, "id": "ND", "arcs": [[-4543, -4542, 6745, 4111, 6746, -6670, 6747, -6687, 6748, 4108, 6749]]}, {"type": "Polygon", "properties": {"ct": [-99.79, 41.53], "gu": "USA"}, "id": "NE", "arcs": [[-6122, 6750, 6751, 3837, 6752, -6327, 3839, -6326, -6674, 3841, -6673, -6353]]}, {"type": "Polygon", "properties": {"ct": [-71.58, 43.69], "gu": "USA"}, "id": "NH", "arcs": [[4179, -4638, 6753, 6754, -4637, 4182, -4636, -6605, 6755, -3491, -6448, 6756]]}, {"type": "MultiPolygon", "properties": {"ct": [-74.67, 40.2], "gu": "USA"}, "id": "NJ", "arcs": [[[6757, 6758, -3417, -3416, 6759, 6760, 6761, 6762, -3411, 6763, -3409, 6764, 6765, -3406, 6766, -3404, 6767, 6768, -3401, 6769, 6770, 6771, -3397, 6772, -3395, 6773, -3393, 6774, -3391, 6775, 6776, -3388, 6777, 6778, -3385, 6779, -3383, 6780]], [[6781, 6782, 6783, -1056]]]}, {"type": "Polygon", "properties": {"ct": [-106.11, 34.42], "gu": "USA"}, "id": "NM", "arcs": [[-6029, -6125, 6784, 6785, 6786, 4064, 6787, 4066, 6788, 6789, 4069, 6790, 4071, 6791]]}, {"type": "Polygon", "properties": {"ct": [-116.65, 39.36], "gu": "USA"}, "id": "NV", "arcs": [[-6027, -3731, 3784, -3730, 3781, -6026, -6104, 6792, -6336, 6793]]}, {"type": "MultiPolygon", "properties": {"ct": [-75.59, 43.02], "gu": "USA"}, "id": "NY", "arcs": [[[-3851, 6794, -4383, -4575, 6795, 4168, 6796, -3967, -4574, 6797, 4171, 6798, -4645, -4644, 6799, 6800, -4642, -4641, 6801, -6446, -6129, -3432, 6802, -3430, 6803, -3428, 6804, -3426, 6805, 6806, 6807, 6808, -3421, 6809, 6810, -3418, -6759, 6811, 6812, -4374]], [[6813, 6814, 6815, 6816, -487, 6817, -521, 6818, -519, 6819, -517, 6820, -515, 6821, -513, 6822, -511, 6823, -509, 6824, 6825, -506, 6826, 6827, 6828, 6829, -501, 6830, 6831, -498, 6832, 6833, -495, 6834, 6835, -492]], [[-1063, 6836, -1061, 6837, -1059]], [[-4385]], [[-4384]], [[-4381]]]}, {"type": "Polygon", "properties": {"ct": [-82.79, 40.3], "gu": "USA"}, "id": "OH", "arcs": [[6838, 3855, 6839, -6358, 3857, -6357, -6351, -6630, 6840, -4378, 6841]]}, {"type": "Polygon", "properties": {"ct": [-97.51, 35.59], "gu": "USA"}, "id": "OK", "arcs": [[-6014, 6842, -6785, -6124, -6356, -6672]]}, {"type": "Polygon", "properties": {"ct": [-120.54, 43.94], "gu": "USA"}, "id": "OR", "arcs": [[-6103, 6843, 6844, 6845, -2673, 6846, 6847, -2670, 6848, -2668, 6849, -2666, 6850, 6851, -2663, 6852, 6853, -2660, 6854, 6855, -2657, 6856, 6857, -2654, -3791, -3790, -3794, 6858, -6339, -3955, -6338, -6337, -6793]]}, {"type": "Polygon", "properties": {"ct": [-77.8, 40.88], "gu": "USA"}, "id": "PA", "arcs": [[-6812, -6758, -3381, 6859, -6134, -6485, 6860, -6839, 6861, -4376, 6862]]}, {"type": "MultiPolygon", "properties": {"ct": [-71.6, 41.7], "gu": "USA"}, "id": "RI", "arcs": [[[-6126, -6474, 6863, -3446, 6864, -3444, 6865, 6866, 6867, -3440, 6868]], [[-1069, 6869, -1071, 6870]], [[-1050, 6871, -1053, 6872, 6873]]]}, {"type": "Polygon", "properties": {"ct": [-80.9, 33.92], "gu": "USA"}, "id": "SC", "arcs": [[-3951, -3755, -3952, -3762, -3977, -3778, -6254, -6729, -3147, 6874, 6875, 6876, -3143, 6877, -3141, 6878, 6879, 6880, -3137, 6881, -3135, 6882, -3133, 6883, 6884, 6885, -3129, 6886, 6887, -3126, 6888, -3124, 6889, -3122, 6890, 6891, 6892, 6893, -3117, 6894, -3115, 6895, 6896, -3112, 6897, -3110, 6898, -3108]]}, {"type": "Polygon", "properties": {"ct": [-100.22, 44.44], "gu": "USA"}, "id": "SD", "arcs": [[-6328, -6753, -3838, -6752, 6899, -6688, -6748, -6669]]}, {"type": "Polygon", "properties": {"ct": [-86.34, 35.85], "gu": "USA"}, "id": "TN", "arcs": [[-6001, -6000, -6682, -6009, 3817, -6008, -6678, 3819, -6677, -6364, -6676, 3823, -6675, -6362, -6361, 6900, -6692, -6252]]}, {"type": "MultiPolygon", "properties": {"ct": [-99.36, 31.51], "gu": "USA"}, "id": "TX", "arcs": [[[-6013, -6429, -2855, 6901, 6902, -2852, 6903, 6904, 6905, 6906, -2847, 6907, -2845, 6908, -2843, 6909, -2841, 6910, -2839, 6911, 6912, -2836, 6913, -2834, 6914, 6915, 6916, 6917, 6918, -2828, 6919, 6920, 6921, -2824, 6922, -2822, 6923, 6924, -2819, 6925, 6926, -2816, 6927, -2814, 6928, -2812, 6929, -2810, 6930, 6931, 6932, -2806, 6933, -2804, 6934, -2802, 6935, -2800, 6936, 6937, -2797, 6938, -2795, 6939, 6940, -2792, 6941, 6942, -2789, 6943, 6944, 6945, -3888, 6946, 4010, 6947, -3886, 4012, 6948, -3885, -3884, 6949, 6950, -3881, 6951, 6952, -3879, -3878, 6953, 4019, 6954, -3749, 6955, 6956, -3748, -3747, 6957, 6958, 4025, -3900, 4026, 6959, -3899, -3898, 6960, 4029, 6961, -3896, -3895, 6962, -3893, 6963, 6964, -3891, -3890, 6965, 4034, -3889, 6966, 4036, 4037, 6967, -3931, 4039, -3930, 6968, -3928, 4041, -3926, 6969, 4043, -3923, 6970, 4045, -3921, -3920, 6971, 4047, -3919, 4048, 6972, 6973, 6974, -3916, -3915, 6975, 6976, -3913, 6977, 6978, -3910, -3909, 6979, 6980, 6981, 4059, 6982, -3906, 6983, 4062, 6984, 4063, -6787, -6786, -6843]], [[6985, 6986, -1120, 6987, -1118]], [[6988, -1104, -1112, 6989, -1110, 6990, -1108, 6991, -1106]], [[6992, -538, 6993, -540]], [[6994, 6995, 6996, -1114]], [[-1124, 6997, -1122]]]}, {"type": "Polygon", "properties": {"ct": [-111.67, 39.33], "gu": "USA"}, "id": "UT", "arcs": [[-6028, -6794, -6335, 6998, -6120]]}, {"type": "MultiPolygon", "properties": {"ct": [-78.89, 37.53], "gu": "USA"}, "id": "VA", "arcs": [[[-6131, 3867, -3260, 6999, 7000, -3257, 7001, 7002, 7003, 7004, -3252, 7005, 7006, -3249, 7007, 7008, -3246, 7009, -3244, 7010, -3242, 7011, -3240, 7012, 7013, -3237, 7014, -3235, 7015, 7016, -3232, 7017, 7018, -3229, 7019, -3227, 7020, -3225, 7021, -3223, 7022, -3221, 7023, 7024, 7025, -3217, 7026, -3215, 7027, -3213, 7028, -6730, -3206, -3205, -3204, -6693, -6901, -6360, 7029, -6482, 3865, -6481]], [[-6490, 7030, -3354, 7031, 7032, 7033, 7034, -3349, 7035, 7036, 7037, 7038, -3344, 7039, 7040, 7041]], [[-6541, -1085, -1084, 7042]]]}, {"type": "Polygon", "properties": {"ct": [-72.67, 44.08], "gu": "USA"}, "id": "VT", "arcs": [[-4640, 7043, 4176, 7044, -4639, 4178, -6757, -6447, -6802]]}, {"type": "MultiPolygon", "properties": {"ct": [-120.41, 47.38], "gu": "USA"}, "id": "WA", "arcs": [[[-4482, -4481, 7045, 4099, 7046, -6341, -3957, -6340, -6859, 3793, 3789, 3790, 7047, -2652, 7048, 7049, 7050, -2648, 7051, 7052, -2645, 7053, 7054, -2642, 7055, -2640, 7056, -2638, 7057, -2636, 7058, 7059, -2633, 7060, -2631, 7061, 7062, -2628, 7063, -2626, 7064, 7065, 7066, -2622, 7067, -2620, 7068, 7069, -2617, 7070, -2615, 7071, 7072, -2612, 7073, -2610, 7074, 7075, -2607, 7076, 7077, -2604, 7078, 7079, 7080, 7081, 7082, -2598, 7083, -2596, 7084, 7085, 7086, 7087, -2591, 7088, -2589, 7089, -2587, 7090, -2585, 7091, 7092, -2582, 7093, -2580, 7094, -2578, 7095, 7096, 7097, -2574, 7098, -2572, 7099, 7100, -2569, 7101, -2567, 7102, -2565, 7103, -2563, 7104, -2561, 7105, -2559, 7106, -2557, 7107, -2555, 7108, -2553, 7109, 7110, -2550, 7111, -2548, 7112, -2546, 4096, 7113]], [[7114, 7115, 7116, 7117, -983, 7118, -981, 7119, -979, -989, 7120]], [[7121, 7122, 7123, -997, 7124, 7125]], [[7126, -992, 7127, -990, 7128, 7129, -994]], [[-1019, 7130, 7131, 7132, -1015, 7133, -1013, 7134, -1011, 7135, 7136, 7137, 7138, -1006, 7139, 7140, -1003]], [[7141, -975, 7142, -973, 7143, 7144, -977]], [[-971, 7145, -969]], [[570, 7146, 7147, 573, 7148, 575]]]}, {"type": "MultiPolygon", "properties": {"ct": [-90.03, 44.64], "gu": "USA"}, "id": "WI", "arcs": [[[-6331, 3830, -6330, -6668, 3832, -6667, -4409, 7149, -6633, 7150, -4398, 7151, -6344]], [[-4415]], [[-4413]], [[-4416]], [[-4414]], [[-4405]], [[-4404]]]}, {"type": "Polygon", "properties": {"ct": [-80.61, 38.65], "gu": "USA"}, "id": "WV", "arcs": [[-6359, -6840, -3856, -6861, -6484, 3935, 3863, -6483, -7030]]}, {"type": "Polygon", "properties": {"ct": [-107.54, 43], "gu": "USA"}, "id": "WY", "arcs": [[-6121, -6999, -6334, -6689, -6900, -6751]]}]}}, "arcs": [[[7590, 2904], [-8, 7]], [[7582, 2911], [0, -2]], [[7582, 2909], [3, -7], [0, -4], [2, -1]], [[7587, 2897], [2, 3]], [[7589, 2900], [1, 2]], [[7590, 2902], [0, 2]], [[7630, 2923], [-3, -4], [-1, -1], [2, 0], [3, 2], [0, 2], [-1, 1]], [[7643, 2934], [-3, -5]], [[7640, 2929], [2, 0], [2, 5], [-1, 0]], [[0, 8415], [12, 2], [12, 3], [11, 1], [11, -2], [11, 0], [11, 3], [9, 0], [9, -2], [34, -4], [7, -2], [10, -5], [7, -2], [5, -3], [7, -6], [12, -6], [18, -7], [4, -2], [2, -5], [-2, -6], [-22, -12], [-19, -3], [-36, -3], [-48, -9], [-20, -2], [-7, 0], [-18, 6], [-20, 2]], [[8931, 5841], [8, -6], [8, -8], [4, -9], [5, -7], [40, -20], [19, -7], [19, -3], [25, -8], [32, 2], [5, 2], [3, 3], [-3, 8], [-6, 9], [-17, 13], [-7, 2], [-39, 26], [-17, 9], [-18, 8], [-15, 5], [-36, 7], [-29, 8], [-9, 0], [-10, -2], [-8, -5], [3, -6], [43, -21]], [[9242, 5404], [1, 3], [-8, 7], [4, 4], [2, 7], [0, 3], [-1, 2], [-7, 4], [-10, 1], [-11, -1], [-1, 9], [-4, 5], [-6, -3], [-4, -4], [-6, -6], [-6, -10], [1, -7], [-13, -12], [3, -3], [18, 5], [0, -3], [-18, -20], [-3, -3], [-5, -2], [-6, 2], [-4, 0], [-10, -6], [-2, 6], [3, 5], [6, 5], [0, 2], [-5, 3], [4, 4], [9, 4], [0, 4], [-5, 1], [-11, -6], [-2, 1], [15, 11], [6, 4], [7, 2], [12, 9], [4, 5], [3, 7], [-1, 4], [2, 13], [7, 23], [4, 15], [0, 4], [-7, 18], [0, 4], [1, 5], [-6, 1], [-6, -1], [-4, -3], [-11, -13], [-8, -8], [-5, -8], [-4, -9], [-20, -41], [-4, -7], [-9, -9], [-5, -13], [-1, -14], [3, -26], [4, -6], [6, -8], [3, -3], [4, 0], [4, 1], [7, 0], [9, 3], [8, 0], [8, -2], [7, 0], [22, 8], [14, 10], [7, 9], [8, 7], [13, 7]], [[846, 7452], [-17, 0]], [[829, 7452], [-6, 1]], [[823, 7453], [-10, -1]], [[813, 7452], [-10, 3]], [[803, 7455], [-2, 4]], [[801, 7459], [-3, 3]], [[798, 7462], [-12, 2]], [[786, 7464], [-18, 5]], [[768, 7469], [-3, 4]], [[765, 7473], [-2, 6]], [[763, 7479], [-3, 5]], [[760, 7484], [-4, 2], [-10, 5]], [[746, 7491], [-10, 2]], [[736, 7493], [-9, -1]], [[727, 7492], [-10, -2]], [[717, 7490], [-15, -9]], [[702, 7481], [-12, -1], [-13, 3]], [[677, 7483], [-11, 1]], [[666, 7484], [-8, -1]], [[658, 7483], [-1, 3]], [[657, 7486], [-14, 11]], [[643, 7497], [-8, -3]], [[635, 7494], [-5, -15], [-1, -6], [0, -6], [2, -6], [4, -4], [9, -5]], [[644, 7452], [8, -2]], [[652, 7450], [9, 1]], [[661, 7451], [9, 4], [9, 5], [9, 3]], [[688, 7463], [8, 1]], [[696, 7464], [8, -1]], [[704, 7463], [25, -7], [8, -4], [7, -4], [4, -4], [2, -6], [5, -4], [5, 0]], [[760, 7434], [10, -3]], [[770, 7431], [10, -3]], [[780, 7428], [3, -3]], [[783, 7425], [3, -3]], [[786, 7422], [5, -12]], [[791, 7410], [3, -4]], [[794, 7406], [4, 1]], [[798, 7407], [4, 4], [1, 7]], [[803, 7418], [7, 7]], [[810, 7425], [8, 6]], [[818, 7431], [20, 2]], [[838, 7433], [19, -2]], [[857, 7431], [7, 5]], [[864, 7436], [4, 12]], [[868, 7448], [-22, 4]], [[1105, 7061], [-6, 5], [0, 4]], [[1099, 7070], [0, 8]], [[1099, 7078], [2, 6], [-1, 7], [-2, 4]], [[1098, 7095], [-5, 3]], [[1093, 7098], [-4, 1]], [[1089, 7099], [-12, -2]], [[1077, 7097], [-4, 0]], [[1073, 7097], [-7, 6], [-3, 2]], [[1063, 7105], [-5, -1]], [[1058, 7104], [-9, -3], [-5, 2]], [[1044, 7103], [-4, 0]], [[1040, 7103], [-9, -5]], [[1031, 7098], [-10, -3]], [[1021, 7095], [-5, -2]], [[1016, 7093], [-4, -9]], [[1012, 7084], [-31, 2]], [[981, 7086], [-8, -1]], [[973, 7085], [-7, -3], [11, -13]], [[977, 7069], [12, -10]], [[989, 7059], [12, -3], [28, -14], [21, -3], [7, -3]], [[1057, 7036], [5, -4]], [[1062, 7032], [3, -2]], [[1065, 7030], [4, 2]], [[1069, 7032], [-2, 5]], [[1067, 7037], [2, 3], [12, 5], [14, 1]], [[1095, 7046], [13, 2]], [[1108, 7048], [-1, 7]], [[1107, 7055], [-2, 6]], [[1286, 6442], [-4, 2]], [[1282, 6444], [-4, 4]], [[1278, 6448], [-7, 20]], [[1271, 6468], [-3, 4]], [[1268, 6472], [-3, 2]], [[1265, 6474], [-4, 2]], [[1261, 6476], [-16, 0], [-4, -1], [-22, -10], [-9, -7], [-12, 2], [-4, -1], [-4, -3], [-14, -22], [-3, -4]], [[1173, 6430], [-11, -6]], [[1162, 6424], [-1, -5]], [[1161, 6419], [0, -3]], [[1161, 6416], [3, -9]], [[1164, 6407], [3, -5]], [[1167, 6402], [6, -2]], [[1173, 6400], [12, 0]], [[1185, 6400], [10, 3], [5, 2], [4, 4]], [[1204, 6409], [9, 11]], [[1213, 6420], [4, 3]], [[1217, 6423], [8, 2]], [[1225, 6425], [38, 1]], [[1263, 6426], [4, 1], [13, 12]], [[1280, 6439], [21, -8]], [[1301, 6431], [0, 2], [-4, 4]], [[1297, 6437], [-4, 3]], [[1293, 6440], [-7, 2]], [[7972, 2135], [-27, 13], [-7, 5], [-9, 10], [-4, 2], [-4, 0], [-9, 2], [-10, 4], [-7, 1], [-7, 0], [-33, 7], [-4, -3], [-4, -4], [-9, -2], [-9, 0], [-3, -2], [-6, -9], [-1, -8], [4, -8], [17, -3], [2, -2], [6, -15], [6, -3], [3, -4], [6, -13], [8, -2], [16, 0], [7, -3], [7, -6], [6, -8], [3, 15], [3, 4], [4, 3], [3, -6], [7, 0], [3, 6], [4, 8], [4, 1], [4, -2], [-2, -3], [8, -1], [3, -3], [8, -4], [8, 0], [9, 1], [7, 4], [-2, 7], [-9, 21]], [[8797, 2162], [-10, 3], [-9, 5], [-15, 3], [-2, 0], [2, -5], [-2, -1], [-3, 3], [-1, 3], [-3, 0], [-48, 2], [-19, 4], [-4, -1], [-4, -2], [-1, -7], [-3, -5], [-4, -4], [2, -5], [3, -4], [2, -7], [0, -9], [-2, -18], [4, -3], [10, 0], [4, -2], [5, -1], [5, 1], [5, 4], [13, -1], [7, 1], [8, -4], [6, 1], [3, -1], [4, -1], [8, 0], [13, 3], [10, 10], [4, 9], [5, 6], [8, 7], [-1, 16]], [[2122, 6781], [-5, 7], [-1, 4]], [[2116, 6792], [5, 5]], [[2121, 6797], [1, 3], [-1, 2], [-7, 3], [-8, 0], [-6, -2], [-12, -7]], [[2088, 6796], [-3, 0]], [[2085, 6796], [0, 6]], [[2085, 6802], [3, 9]], [[2088, 6811], [-7, 4], [-17, 5], [-5, -2]], [[2059, 6818], [5, -6]], [[2064, 6812], [-1, -3]], [[2063, 6809], [-2, -2]], [[2061, 6807], [0, -5]], [[2061, 6802], [-1, -3]], [[2060, 6799], [-3, -1], [-8, 2]], [[2049, 6800], [-2, -1]], [[2047, 6799], [-3, -2]], [[2044, 6797], [-2, -4]], [[2042, 6793], [-3, -2]], [[2039, 6791], [-3, 0]], [[2036, 6791], [-3, 4]], [[2033, 6795], [-7, 11], [-3, 2], [-5, 1]], [[2018, 6809], [-3, -1]], [[2015, 6808], [-3, -1], [-5, -5]], [[2007, 6802], [0, -4]], [[2007, 6798], [2, -4]], [[2009, 6794], [6, -5]], [[2015, 6789], [8, -6]], [[2023, 6783], [1, -2]], [[2024, 6781], [-9, 0]], [[2015, 6781], [-3, -1]], [[2012, 6780], [2, -5]], [[2014, 6775], [1, -18]], [[2015, 6757], [3, -9]], [[2018, 6748], [6, -7]], [[2024, 6741], [-5, 2]], [[2019, 6743], [-4, 4]], [[2015, 6747], [-6, 10]], [[2009, 6757], [-5, 11], [-5, 3], [0, 1], [1, 2], [-2, 5], [-7, 3]], [[1991, 6782], [-5, 0]], [[1986, 6782], [-8, -2]], [[1978, 6780], [-8, -5]], [[1970, 6775], [-11, -4]], [[1959, 6771], [-11, -13], [-3, -10], [1, -3], [10, -16]], [[1956, 6729], [5, -19]], [[1961, 6710], [13, -14]], [[1974, 6696], [6, -1]], [[1980, 6695], [4, 6]], [[1984, 6701], [2, 5], [-1, 3]], [[1985, 6709], [-1, 4]], [[1984, 6713], [-3, 2]], [[1981, 6715], [-2, 2]], [[1979, 6717], [-9, 0]], [[1970, 6717], [1, 1], [4, 3], [6, 1]], [[1981, 6722], [8, 0]], [[1989, 6722], [6, -1]], [[1995, 6721], [2, -1]], [[1997, 6720], [1, -2], [-4, -6], [-2, -4], [1, 0], [7, 3], [9, -5], [6, -1]], [[2015, 6705], [1, -1]], [[2016, 6704], [-22, -20]], [[1994, 6684], [0, -2]], [[1994, 6682], [2, -2], [2, -1]], [[1998, 6679], [4, 0]], [[2002, 6679], [16, 10], [9, 12], [1, 2], [0, 3], [-1, 3]], [[2027, 6709], [-7, 2]], [[2020, 6711], [11, 3]], [[2031, 6714], [5, 3]], [[2036, 6717], [2, 4]], [[2038, 6721], [5, 4]], [[2043, 6725], [13, 7]], [[2056, 6732], [17, 1]], [[2073, 6733], [4, 5]], [[2077, 6738], [9, 5]], [[2086, 6743], [7, 0]], [[2093, 6743], [6, 1]], [[2099, 6744], [2, 2]], [[2101, 6746], [-1, 4]], [[2100, 6750], [-2, 4]], [[2098, 6754], [-4, 5]], [[2094, 6759], [-14, 0]], [[2080, 6759], [-3, 1]], [[2077, 6760], [4, 4]], [[2081, 6764], [2, 1]], [[2083, 6765], [7, -1]], [[2090, 6764], [15, -3]], [[2105, 6761], [17, -2]], [[2122, 6759], [2, 0], [4, 3]], [[2128, 6762], [9, 11]], [[2137, 6773], [0, 3]], [[2137, 6776], [-2, 2]], [[2135, 6778], [-13, 3]], [[2141, 6844], [4, -2]], [[2145, 6842], [3, 2]], [[2148, 6844], [7, 8]], [[2155, 6852], [1, 7]], [[2156, 6859], [-2, 1]], [[2154, 6860], [-3, -1]], [[2151, 6859], [-3, 1]], [[2148, 6860], [-4, 7]], [[2144, 6867], [-6, -1]], [[2138, 6866], [-9, 5], [-2, 0], [-3, -7]], [[2124, 6864], [-2, 1]], [[2122, 6865], [-5, 5]], [[2117, 6870], [-5, 3]], [[2112, 6873], [-5, 2]], [[2107, 6875], [-5, 1]], [[2102, 6876], [-13, -4]], [[2089, 6872], [0, -3]], [[2089, 6869], [6, -5]], [[2095, 6864], [-1, -8]], [[2094, 6856], [-3, -1]], [[2091, 6855], [-6, 2]], [[2085, 6857], [-6, 1]], [[2079, 6858], [-11, -7], [-21, -18]], [[2047, 6833], [6, -3]], [[2053, 6830], [25, -7]], [[2078, 6823], [4, -1]], [[2082, 6822], [7, 2]], [[2089, 6824], [5, 1]], [[2094, 6825], [1, 2], [6, 3]], [[2101, 6830], [3, 5]], [[2104, 6835], [4, 7]], [[2108, 6842], [2, 1], [1, -6], [1, -2], [7, 3]], [[2119, 6838], [5, -1]], [[2124, 6837], [6, 2], [0, 2], [-2, 4], [0, 2]], [[2128, 6847], [5, 5]], [[2133, 6852], [1, 0]], [[2134, 6852], [2, -4]], [[2136, 6848], [3, -3]], [[2139, 6845], [2, -1]], [[3769, 6556], [-9, 15]], [[3760, 6571], [-9, 11]], [[3751, 6582], [-3, 0], [-27, -14], [-2, -29]], [[3719, 6539], [-9, -10]], [[3710, 6529], [-6, -10]], [[3704, 6519], [0, -7]], [[3704, 6512], [2, -16]], [[3706, 6496], [4, -7]], [[3710, 6489], [3, 7]], [[3713, 6496], [7, 9]], [[3720, 6505], [9, 5]], [[3729, 6510], [3, 4]], [[3732, 6514], [2, 4]], [[3734, 6518], [2, -5]], [[3736, 6513], [-2, -6]], [[3734, 6507], [2, -5]], [[3736, 6502], [5, -1], [4, 1]], [[3745, 6502], [4, -6]], [[3749, 6496], [5, -2]], [[3754, 6494], [8, 7], [6, 14], [2, 12], [2, 10], [-1, 12], [-2, 7]], [[3656, 6463], [7, -2], [3, -2], [5, -12], [6, -8]], [[3677, 6439], [5, -3]], [[3682, 6436], [5, 0]], [[3687, 6436], [3, 2]], [[3690, 6438], [3, 9], [0, 4]], [[3693, 6451], [-1, 3]], [[3692, 6454], [0, 4], [2, 5], [0, 3]], [[3694, 6466], [-2, 8]], [[3692, 6474], [2, 20]], [[3694, 6494], [-2, 3]], [[3692, 6497], [-13, -1], [-3, 0]], [[3676, 6496], [-1, 2]], [[3675, 6498], [2, 2], [3, 5], [0, 3], [-5, 7], [-4, 2], [-11, 10], [-14, -2]], [[3646, 6525], [-3, 1]], [[3643, 6526], [4, 3]], [[3647, 6529], [3, 5]], [[3650, 6534], [1, 5], [2, 1]], [[3653, 6540], [16, -10]], [[3669, 6530], [8, -4]], [[3677, 6526], [2, 0], [-4, 5], [-5, 5], [-11, 15]], [[3659, 6551], [-5, 13]], [[3654, 6564], [-3, 5]], [[3651, 6569], [-5, 6]], [[3646, 6575], [-12, 12]], [[3634, 6587], [-26, 11]], [[3608, 6598], [-1, 1], [2, 4], [0, 3]], [[3609, 6606], [0, 4]], [[3609, 6610], [-2, 5]], [[3607, 6615], [-3, 5], [-5, 5]], [[3599, 6625], [-13, 0]], [[3586, 6625], [-15, 2], [-2, -2], [0, -12], [4, -5], [1, -3]], [[3574, 6605], [-5, -6]], [[3569, 6599], [-12, -11]], [[3557, 6588], [1, -5]], [[3558, 6583], [4, -2], [12, 6], [12, 5]], [[3586, 6592], [7, -2]], [[3593, 6590], [3, -7], [0, -5]], [[3596, 6578], [-5, -4]], [[3591, 6574], [-1, -5]], [[3590, 6569], [-7, -5]], [[3583, 6564], [-2, 0]], [[3581, 6564], [-7, 4]], [[3574, 6568], [-4, 0], [-6, -4], [-1, -2], [3, -4], [6, -7]], [[3572, 6551], [4, 1]], [[3576, 6552], [11, -1]], [[3587, 6551], [2, -4]], [[3589, 6547], [3, -6]], [[3592, 6541], [4, -1]], [[3596, 6540], [12, 2]], [[3608, 6542], [4, -3], [-3, -6], [0, -4]], [[3609, 6529], [9, -13]], [[3618, 6516], [-1, -2]], [[3617, 6514], [-4, 0]], [[3613, 6514], [-6, -2]], [[3607, 6512], [-1, -4]], [[3606, 6508], [4, -3]], [[3610, 6505], [12, -13]], [[3622, 6492], [10, -16]], [[3632, 6476], [6, -3]], [[3638, 6473], [1, 6], [-1, 6]], [[3638, 6485], [3, 2]], [[3641, 6487], [3, -1]], [[3644, 6486], [0, -3], [3, -7], [-1, -7], [4, -5], [5, 0], [1, -1]], [[3540, 6706], [4, -5]], [[3544, 6701], [8, -5]], [[3552, 6696], [5, -5]], [[3557, 6691], [6, -10]], [[3563, 6681], [-1, -10]], [[3562, 6671], [2, -13]], [[3564, 6658], [1, -10]], [[3565, 6648], [1, -3]], [[3566, 6645], [3, -3]], [[3569, 6642], [9, -1]], [[3578, 6641], [8, 2]], [[3586, 6643], [13, -1], [2, 1]], [[3601, 6643], [2, 3]], [[3603, 6646], [1, 4]], [[3604, 6650], [0, 4]], [[3604, 6654], [-1, 5]], [[3603, 6659], [-2, 4]], [[3601, 6663], [-3, 6], [-1, 4]], [[3597, 6673], [-6, 7]], [[3591, 6680], [-2, 4], [1, 1]], [[3590, 6685], [6, -4]], [[3596, 6681], [9, -13], [7, -7], [3, 0]], [[3615, 6661], [2, 3]], [[3617, 6664], [1, 3], [1, 4], [-1, 9]], [[3618, 6680], [1, 8]], [[3619, 6688], [0, 3]], [[3619, 6691], [-4, 6], [-5, 5], [-10, 4], [-5, 0], [-3, -4]], [[3592, 6702], [-5, 4]], [[3587, 6706], [-26, 7]], [[3561, 6713], [-13, 0]], [[3548, 6713], [-7, -3]], [[3541, 6710], [-1, -4]], [[3470, 6734], [-6, 13]], [[3464, 6747], [-8, 7]], [[3456, 6754], [-5, 2]], [[3451, 6756], [-16, 12]], [[3435, 6768], [-8, 0]], [[3427, 6768], [-3, -2], [-7, -11], [0, -4]], [[3417, 6751], [-4, -4]], [[3413, 6747], [-4, -3]], [[3409, 6744], [-8, -1]], [[3401, 6743], [-3, -5]], [[3398, 6738], [0, -6], [4, -15]], [[3402, 6717], [-1, -5]], [[3401, 6712], [-2, -6]], [[3399, 6706], [11, 3]], [[3410, 6709], [4, 5]], [[3414, 6714], [9, 20]], [[3423, 6734], [3, 1], [6, -8]], [[3432, 6727], [3, -12]], [[3435, 6715], [6, -4]], [[3441, 6711], [4, -1], [1, -2], [-9, -11], [-2, -4]], [[3435, 6693], [0, -5]], [[3435, 6688], [1, -4]], [[3436, 6684], [3, -2]], [[3439, 6682], [10, 3]], [[3449, 6685], [1, -3]], [[3450, 6682], [-1, -9]], [[3449, 6673], [5, -3]], [[3454, 6670], [6, -5], [7, 1]], [[3467, 6666], [3, 1]], [[3470, 6667], [1, -1]], [[3471, 6666], [-5, -6]], [[3466, 6660], [-2, -2], [-1, -4], [0, -5]], [[3463, 6649], [2, -8]], [[3465, 6641], [8, -15]], [[3473, 6626], [3, -5]], [[3476, 6621], [4, -5]], [[3480, 6616], [6, -3], [2, 1], [2, 9], [0, 16], [-2, 10], [0, 4]], [[3488, 6653], [2, 3]], [[3490, 6656], [1, 3], [-1, 13], [-1, 5]], [[3489, 6677], [-3, 10]], [[3486, 6687], [-7, 25]], [[3479, 6712], [-4, 12]], [[3475, 6724], [-5, 10]], [[3530, 6741], [14, 4]], [[3544, 6745], [1, 2]], [[3545, 6747], [0, 2]], [[3545, 6749], [-5, 9]], [[3540, 6758], [4, 5]], [[3544, 6763], [-1, 11]], [[3543, 6774], [-2, 3]], [[3541, 6777], [-9, 12]], [[3532, 6789], [-14, 20]], [[3518, 6809], [-3, 10], [0, 8]], [[3515, 6827], [1, 1]], [[3516, 6828], [3, 1]], [[3519, 6829], [3, -2]], [[3522, 6827], [2, -3]], [[3524, 6824], [0, -3]], [[3524, 6821], [6, -12]], [[3530, 6809], [3, -5]], [[3533, 6804], [3, -2]], [[3536, 6802], [2, -5]], [[3538, 6797], [6, -13]], [[3544, 6784], [7, -6]], [[3551, 6778], [1, 1]], [[3552, 6779], [-4, 9]], [[3548, 6788], [-3, 10]], [[3545, 6798], [-4, 10]], [[3541, 6808], [-8, 14]], [[3533, 6822], [-13, 18]], [[3520, 6840], [-15, -1]], [[3505, 6839], [-19, 3]], [[3486, 6842], [-4, 7]], [[3482, 6849], [-8, 12]], [[3474, 6861], [-7, 4]], [[3467, 6865], [-1, -3]], [[3466, 6862], [2, -8]], [[3468, 6854], [3, -7]], [[3471, 6847], [4, -7]], [[3475, 6840], [3, -8], [2, -10]], [[3480, 6822], [5, -30]], [[3485, 6792], [2, -12]], [[3487, 6780], [5, -8]], [[3492, 6772], [9, -10]], [[3501, 6762], [-1, -7]], [[3500, 6755], [-6, -23]], [[3494, 6732], [-4, -4]], [[3490, 6728], [1, -7]], [[3491, 6721], [2, -5], [2, -4], [3, -2], [7, 2]], [[3505, 6712], [13, 10]], [[3518, 6722], [12, 19]], [[3408, 6755], [6, 7]], [[3414, 6762], [-1, 13]], [[3413, 6775], [1, 7], [4, 2], [5, -1], [21, -10]], [[3444, 6773], [11, -8]], [[3455, 6765], [11, -3]], [[3466, 6762], [5, 13]], [[3471, 6775], [-2, 6]], [[3469, 6781], [-6, 9], [-21, 1]], [[3442, 6791], [-7, 5]], [[3435, 6796], [8, 5]], [[3443, 6801], [6, -2]], [[3449, 6799], [4, 0], [10, 2], [4, 16]], [[3467, 6817], [-2, 8]], [[3465, 6825], [-4, 4]], [[3461, 6829], [-12, 5]], [[3449, 6834], [-14, 3]], [[3435, 6837], [-3, 0]], [[3432, 6837], [-3, -2]], [[3429, 6835], [-12, -11]], [[3417, 6824], [-3, -2], [-4, 2], [-2, 3]], [[3408, 6827], [6, 3]], [[3414, 6830], [2, 7]], [[3416, 6837], [0, 3]], [[3416, 6840], [-11, 12]], [[3405, 6852], [-4, 2]], [[3401, 6854], [-8, -2], [-5, -5], [-3, -1]], [[3385, 6846], [-8, 0]], [[3377, 6846], [-4, -5]], [[3373, 6841], [0, -7]], [[3373, 6834], [-8, 7]], [[3365, 6841], [-5, 8]], [[3360, 6849], [-4, -9], [-7, -4], [-4, -2], [-1, -5]], [[3344, 6829], [-3, -9]], [[3341, 6820], [8, -12]], [[3349, 6808], [6, -4]], [[3355, 6804], [23, -19]], [[3378, 6785], [7, -17], [6, -10], [17, -3]], [[3941, 6187], [1, -5], [1, -10], [0, -13], [4, -8], [5, 6], [3, 13], [5, 8], [1, 12], [-4, 38], [-6, 20], [-8, 8], [-9, 6], [-9, 5], [-5, 1], [-2, -5], [-2, -12], [-2, -6], [-5, -8], [0, -3], [0, -3], [6, -9], [1, -8], [5, -8], [9, -10], [6, 1], [4, 8], [2, 2], [-1, -20]], [[3665, 6257], [26, 9], [4, 5], [2, 8], [1, 25], [2, 15], [6, 15], [9, 17], [1, 4], [2, 14], [-3, 0], [-9, -8], [-10, -4], [-14, -1], [-1, -4], [2, -11], [0, -5], [-3, -6], [-1, -12], [-1, -3], [-22, -3], [-5, -1], [-3, 2], [0, 1], [0, 2], [27, 15], [3, 3], [2, 4], [-1, 9], [-4, 9], [-3, 5], [-3, 3], [-4, 1], [-16, -5], [-7, 7], [-19, 2], [-7, 2], [-5, 0], [-1, -2], [-3, -16], [1, -10], [1, -10], [2, -7], [11, -17], [1, -3], [0, -5], [4, -7], [13, -5], [19, -13], [0, -2], [-3, 0], [-6, 3], [-8, 1], [-3, 0], [-5, -7], [6, -5], [12, -9], [13, 0]], [[3760, 6143], [-2, 8], [-9, 0], [-5, 2], [0, 1], [5, 9], [0, 3], [-1, 1], [-9, -1], [-3, 1], [-1, 3], [-12, 15], [2, 5], [9, 10], [-12, 5], [-9, 1], [-6, 7], [-8, 6], [-5, 2], [4, 3], [3, 1], [7, -4], [12, 5], [1, 11], [-2, 10], [-8, 11], [-8, 4], [-3, 0], [-7, -4], [-4, -2], [-9, -2], [-15, -3], [-13, 1], [-2, -1], [3, -6], [3, -2], [25, -8], [0, -5], [-7, -1], [-2, -2], [0, -3], [2, -5], [5, -10], [6, -3], [22, -25], [14, -12], [5, -5], [4, -9], [6, -10], [16, -10], [8, 8]], [[9063, 5469], [-20, -2], [-19, -2], [-3, -1], [-19, -2], [-13, -1], [-12, 4], [-6, 6], [-4, 0], [-3, 0], [-6, 4], [-11, 2], [-5, -9], [-3, -3], [-2, 1], [-2, 3], [-6, 2], [3, 4], [1, 4], [-2, 4], [-1, 3], [-15, 16], [9, 17], [-2, 8], [1, 9], [-13, -12], [-5, -7], [-4, -7], [-6, -8], [-4, -9], [1, -6], [12, -1], [8, -4], [2, -5], [0, -16], [7, -2], [12, 0], [8, -1], [0, -2], [-1, -1], [-2, -1], [5, -9], [7, -7], [5, -2], [23, -7], [5, 1], [4, 3], [-9, 2], [6, 4], [6, 2], [5, 5], [6, 2], [-2, -2], [-4, -3], [0, -5], [3, -4], [5, 0], [4, -8], [-10, -7], [3, -1], [7, 1], [1, -8], [6, -3], [5, -1], [16, 2], [4, 2], [-2, 3], [-3, 1], [1, 8], [-1, 8], [2, 4], [15, 9], [12, 9], [11, 8], [-1, 3], [-3, 2], [-7, 3]], [[8299, 4825], [-4, 4]], [[8295, 4829], [-2, 0], [-8, -3]], [[8285, 4826], [-10, -6]], [[8275, 4820], [-4, -5]], [[8271, 4815], [-4, -2]], [[8267, 4813], [-5, 1]], [[8262, 4814], [6, 7], [6, 7], [12, 13], [-8, -3], [-13, -12], [-6, -4], [-16, -2]], [[8243, 4820], [-16, -1]], [[8227, 4819], [-6, -1]], [[8221, 4818], [-5, -3]], [[8216, 4815], [-7, -1]], [[8209, 4814], [-8, 2]], [[8201, 4816], [-2, 0]], [[8199, 4816], [-3, -2]], [[8196, 4814], [-4, 0]], [[8192, 4814], [-6, 0], [-3, -2], [-2, -3]], [[8181, 4809], [-1, -5]], [[8180, 4804], [-4, 4]], [[8176, 4808], [-4, -5]], [[8172, 4803], [-10, -4]], [[8162, 4799], [-6, -8]], [[8156, 4791], [-3, -5]], [[8153, 4786], [-2, -5]], [[8151, 4781], [1, -7]], [[8152, 4774], [6, 2]], [[8158, 4776], [5, 6], [4, 1]], [[8167, 4783], [1, -2]], [[8168, 4781], [0, -2], [-7, -6]], [[8161, 4773], [10, 2]], [[8171, 4775], [11, 1], [28, 7], [2, -1], [3, 0], [33, 15], [7, 1]], [[8255, 4798], [9, 5]], [[8264, 4803], [10, 5]], [[8274, 4808], [7, 3]], [[8281, 4811], [33, 19]], [[8314, 4830], [-7, -2]], [[8307, 4828], [-8, -3]], [[8114, 2398], [-12, 16], [-5, 6], [-12, 9], [-6, 3], [-19, 5], [-10, -2], [-14, 2], [-9, 2], [-7, -2], [-2, 2], [0, 5], [1, 4], [11, 3], [-5, 7], [2, 6], [3, 6], [-3, 8], [-6, 5], [-14, 1], [-13, 2], [-15, 11], [-15, 6], [-7, -1], [-8, 2], [-3, 6], [-3, 3], [-3, 0], [-8, -4], [1, 4], [2, 4], [-7, 7], [-13, 16], [-4, -6], [-5, -7], [-3, 0], [-1, 7], [-8, 8], [9, -2], [5, 1], [3, 5], [-6, 3], [-6, 5], [-3, 5], [-12, 14], [-7, 2], [1, -3], [5, -6], [1, -3], [-4, -2], [-7, 3], [-17, 12], [-8, 9], [-14, 16], [-41, 30], [-3, -1], [-4, 1], [-5, 3], [-5, 1], [-22, -1], [-7, 2], [-6, 5], [-8, 7], [-7, 8], [-10, 19], [-13, 10], [2, 7], [-8, -2], [-2, 1], [-9, 8], [-7, 1], [-8, -2], [-8, 1], [-7, 4], [-7, 5], [-5, 7], [-2, 3], [-28, -2], [-11, -4], [-2, 1], [-7, 8], [0, 3], [-7, -3], [-17, -2], [-20, 6], [-20, 3], [-19, -4], [-19, -11], [-6, -2], [-39, -7], [-6, -2], [-61, -36], [-5, -5], [-13, -17], [-6, -11], [-2, -15], [5, -21], [-4, -5], [-4, 0], [-5, 1], [-3, -1], [-27, -16], [0, -5], [3, -3], [4, 1], [8, 7], [5, 3], [5, 1], [4, 0], [1, -9], [-1, -9], [4, 1], [16, 13], [8, 4], [8, 1], [3, 5], [3, 13], [2, 7], [3, 2], [16, 1], [3, 1], [4, 3], [4, 0], [4, -3], [9, 4], [6, 10], [8, 6], [4, 3], [3, 6], [7, 9], [11, 10], [6, 7], [4, 4], [64, -1], [5, -1], [4, -2], [3, -3], [4, -4], [1, -7], [-2, -5], [-4, -3], [-16, -5], [-8, -4], [8, -12], [9, -9], [3, -1], [29, -2], [6, -10], [6, 1], [5, 4], [1, 7], [1, 7], [4, -7], [2, -8], [2, -5], [4, -2], [6, -3], [35, 1], [1, 3], [0, 4], [7, -10], [7, -12], [6, -7], [7, -5], [17, -10], [43, -19], [6, -2], [7, -2], [28, 8], [7, -3], [7, -9], [5, -12], [3, -14], [4, -28], [2, -5], [4, -5], [7, -5], [15, -20], [10, -5], [10, 0], [21, -3], [9, 0], [10, -2], [9, -4], [1, -3], [2, -6], [6, -8], [1, -5], [-1, -5], [-3, -7], [-5, -6], [-26, -26], [-13, -26], [20, 0], [19, 4], [16, 0], [9, 3], [8, 3], [21, 2], [20, 3], [7, 0], [30, -3], [9, -3], [8, -5], [20, 0], [5, 4], [3, 4], [2, 6], [3, -7], [0, -3], [0, -3], [9, 3], [4, 4], [8, 5], [16, 7], [17, 2], [13, 0], [2, 4], [5, 7], [2, 7], [-3, 7], [-2, 2], [-3, 2], [-3, -1], [-9, 2], [-9, 6]], [[9837, 8174], [9, 8], [7, 6], [-1, 4], [-5, 3], [-2, 3], [1, 2], [1, 2], [-1, 2], [-2, 5], [-2, 3], [-27, 6], [-26, 10], [-29, 23], [-15, 8], [-6, 2], [-48, 8], [-28, 3], [-26, -7], [-6, -5], [-2, -3], [-2, -4], [0, -3], [2, -6], [3, -3], [9, -5], [26, -8], [-4, -2], [-23, 4], [-9, -1], [-4, -6], [-6, -22], [4, -6], [4, -4], [6, -2], [18, -4], [28, -2], [11, -1], [5, -2], [8, 0], [3, -4], [5, -1], [5, -3], [-18, -4], [-12, 0], [-9, -1], [-2, -3], [5, -4], [5, -4], [12, -4], [8, -4], [3, -1], [14, 0], [44, 10], [18, 3], [51, 14]], [[4352, 5729], [-2, -1], [-1, -2], [-1, -9], [-2, -2], [-10, 28], [-10, 15], [-5, 16], [-3, 4], [-6, 6], [-4, 6], [-15, 9], [-24, 10], [-11, 6], [-15, 11], [-7, 14], [0, 3], [2, 2], [-3, 5], [-10, 14], [-13, 19], [-6, 11], [-8, 18], [-5, 7], [-4, 3], [-6, 2], [-17, 3], [-28, 8], [-38, 7], [-39, 15], [-39, 21], [-16, 5], [-14, 0], [-11, -4], [-5, -4], [-3, -6], [0, -5], [6, -11], [10, -10], [6, -3], [7, 0], [8, 0], [10, 5], [-1, 5], [0, 3], [17, -1], [5, -2], [-5, -5], [0, -5], [3, -8], [0, -3], [-7, 7], [-5, 2], [-14, -1], [-4, -1], [-2, -2], [-5, -12], [2, -2], [7, -1], [1, -3], [1, -9], [-4, -8], [1, -2], [4, -1], [3, 0], [8, 5], [16, 0], [2, -4], [3, -5], [4, -4], [4, 2], [2, 3], [0, 4], [1, 1], [3, -2], [2, -2], [1, -4], [0, -2], [-2, -7], [0, -5], [3, -5], [4, -3], [5, -1], [5, 1], [3, 2], [1, 4], [2, 1], [4, -2], [8, -2], [5, -4], [7, -13], [2, -4], [3, -1], [5, 0], [4, -5], [5, -2], [16, 1], [-2, -2], [-22, -4], [-7, -3], [-1, -2], [-1, -4], [1, -15], [3, -2], [1, 0], [4, 6], [2, 0], [9, -8], [2, 1], [1, 5], [2, 1], [5, -3], [6, 1], [2, -2], [2, -3], [2, -2], [6, 4], [0, -2], [-3, -11], [2, -5], [3, 0], [6, 4], [3, -2], [5, -7], [3, -1], [3, 0], [1, -1], [-5, -5], [-8, -4], [-1, -2], [13, -7], [9, -9], [4, -3], [2, 1], [7, 7], [15, -1], [19, 3], [2, 2], [2, 5], [3, 7], [1, 8], [1, 1], [1, -8], [-2, -7], [-2, -7], [-7, -8], [-15, -16], [0, -2], [1, -5], [8, -6], [11, -7], [14, -6], [24, -10], [20, -9], [16, -6], [24, -6], [2, -2], [3, 3], [4, 7], [3, 3], [8, -3], [2, 1], [2, 5], [-6, 18], [-2, 7], [-2, 4]], [[8562, 2224], [-17, 10], [-10, 3], [-10, 1], [-9, 3], [-9, 4], [-8, 3], [-9, 1], [0, 5], [1, 5], [7, 1], [15, -1], [4, 3], [3, 5], [-7, 7], [-32, -4], [-7, 8], [-4, 13], [-1, 13], [-5, 10], [-4, 0], [-9, -4], [-5, 0], [-9, 5], [-10, 11], [-3, 0], [-12, 0], [-4, 2], [-8, 7], [-3, 4], [-10, 4], [-10, -3], [-11, -5], [-4, 0], [-12, 5], [-9, 0], [-5, -2], [-4, -3], [-3, -6], [-2, -7], [-3, -2], [-4, -3], [-10, 3], [-20, 3], [-16, 8], [-16, 10], [-19, 3], [-18, -3], [-8, -2], [-7, -3], [-7, -6], [-3, -10], [3, -7], [7, -3], [20, -3], [14, -10], [13, -10], [-3, -12], [-2, -12], [2, -12], [-6, -8], [13, -20], [14, -18], [9, -8], [0, -6], [-2, -6], [-3, -2], [-16, -1], [-3, -4], [-3, -5], [-3, -4], [-4, 0], [-10, 2], [-52, 8], [-7, 5], [-13, 1], [-9, 3], [-10, 5], [-9, 2], [-5, 0], [-8, -4], [-7, -21], [2, -6], [3, -6], [17, -9], [8, -6], [8, -9], [8, -12], [3, 2], [2, 8], [5, 8], [8, 4], [10, 2], [10, 1], [9, -2], [9, -3], [21, -7], [10, 1], [9, 2], [3, 1], [3, 3], [4, 1], [34, 1], [5, -2], [4, -3], [7, -8], [7, -9], [4, -4], [3, -6], [1, -8], [0, -8], [2, -5], [5, -2], [4, -4], [6, -10], [3, 1], [3, 5], [7, 19], [13, 26], [1, 7], [0, 11], [1, 3], [4, 3], [8, 2], [12, 6], [9, -1], [6, -8], [7, -6], [23, 4], [3, 3], [6, 8], [3, 3], [10, 6], [9, 3], [10, -1], [10, -3], [9, 1], [9, 2], [16, -5], [11, 1], [8, -8], [4, -8], [4, -6], [3, 0], [2, 1], [3, 9], [4, 6], [6, 3], [3, 5], [7, 14], [1, 8], [-3, 7], [-5, 5], [-18, 23]], [[0, 8115], [15, -5], [16, -4], [6, 1], [4, 0], [9, -7], [5, -4], [32, -8], [14, -9], [11, -11], [-5, 2], [-11, 7], [1, -8], [4, -6], [16, -5], [18, -4], [11, -5], [4, -4], [2, -8], [-2, -7], [10, 3], [9, 6], [-5, 5], [-32, 16], [-7, 6], [10, -3], [43, -21], [12, -8], [-5, -2], [-3, -4], [3, -2], [6, 1], [8, 1], [9, -3], [10, -5], [20, -7], [120, -52], [3, -9], [3, -4], [2, -5], [1, -9], [-11, -10], [17, 1], [2, 1], [5, 4], [4, 3], [7, -3], [5, -7], [-1, -10], [-5, -8], [-1, -12], [4, -11], [5, -5], [3, -5], [1, -16], [-7, -7], [-5, -12], [5, -1], [15, -1], [4, -2], [9, -6], [2, -5], [2, -8], [2, -6], [2, -4], [2, 1], [9, 9], [4, 3], [9, 3], [5, -11], [-4, -18], [4, 0], [2, 2], [3, 5], [4, 2], [5, 7], [5, 8], [-5, 6], [-6, 4], [-14, 3], [-8, 4], [-2, 6], [7, 3], [6, 4], [4, 11], [-1, 5], [-2, 6], [-3, 8], [-5, 4], [-10, 3], [-5, 4], [-7, 0], [-6, 1], [-3, 2], [0, 3], [8, 2], [43, 0], [16, 5], [7, -2], [7, -3], [26, -4], [0, -2], [-5, -2], [-7, -10], [-2, -5], [0, -7], [6, -1], [7, 3], [-3, 5], [-1, 7], [3, 3], [3, 0], [7, -5], [7, -1], [25, -2], [7, 0], [2, 3], [-5, 3], [-32, 7], [0, 3], [30, -4], [13, -4], [13, -3], [19, 0], [18, -4], [17, -14], [17, -16], [16, -10], [17, -8], [28, -20], [4, -2], [3, -2], [-6, -4], [-4, -5], [9, 4], [9, 2], [5, 0], [4, -3], [3, -4], [1, -4], [-4, -4], [28, -1], [8, -2], [4, -10], [-8, -7], [-5, 1], [-4, 3], [-4, 0], [-12, -3], [-19, -9], [-10, -8], [-2, -5], [2, -13], [-2, -7], [-8, -4], [-18, 3], [-8, 3], [-9, 3], [-9, 5], [-11, 8], [-3, 1], [-2, -2], [3, -5], [8, -7], [13, -8], [6, -9], [-4, -5], [-5, -1], [-3, 0], [-12, 3], [-8, 1], [-25, -3], [-9, -1], [-3, 1], [0, 4], [-14, 3], [-8, 0], [-3, 2], [-3, 4], [-9, 6], [-13, 2], [-9, 1], [-5, -2], [18, -8], [15, -13], [-3, -3], [-2, -3], [9, 0], [5, 1], [2, -4], [-4, -14], [-3, -3], [-28, -3], [7, -3], [7, -1], [8, 1], [7, -3], [5, -9], [1, -9], [-7, -5], [-7, -5], [-15, -7], [-16, -2], [-8, 0], [-8, -1], [-5, -4], [-1, -3], [7, 2], [7, -1], [8, -4], [-1, -4], [-7, -4], [-2, -2], [3, -5], [-1, -4], [4, -3], [8, 0], [10, -3], [10, -4], [4, -4], [3, -5], [1, -5], [-2, -2], [-22, 0], [-4, 0], [-1, 6], [-3, 4], [-8, 4], [-4, -3], [3, -16], [-4, -5], [-3, -3], [-12, -2], [-9, 1], [-7, 7], [0, 7], [5, 4], [0, 5], [-2, 6], [-5, -7], [-6, -6], [-10, -7], [-5, -1], [-5, 1], [-13, 5], [-8, 4], [-15, 16], [-9, 7], [-19, 9], [-20, 7], [-16, 4], [-9, 0], [-8, -2], [-11, 1], [-3, 2], [-4, 3], [-2, 3], [-15, 9], [-11, 8], [-1, 5], [3, 6], [-2, 15], [-5, 14], [-14, 14], [-34, 9], [-29, 6], [-11, 1], [-9, -1], [-24, -11], [-16, -2], [-47, -1], [-8, 2], [-7, 4], [-2, 7], [2, 12], [0, 5], [-2, 2], [-2, 0], [-9, 5], [-9, 8], [-7, 8], [-4, 12], [6, 0], [8, -2], [2, 2], [2, 11], [6, 4], [3, 4], [4, 14], [0, 10], [-7, -5], [-10, -14], [-5, -4], [-4, -2], [-4, -1], [-8, 3], [-6, 3], [0, 14], [-3, 3], [-3, -3], [-1, -4], [-8, -1], [-3, -2], [2, -8], [-1, -7], [-8, -2], [-14, -2], [-6, 7], [-4, -10], [-3, -10], [-1, -14], [5, -11], [7, -6], [14, -8], [7, -6], [1, -7], [0, -7], [-8, -8], [-5, -7], [-9, -17], [-5, -7], [-23, -14]], [[8857, 2156], [-3, 5], [-4, 2], [-7, -2], [8, -6], [6, 1]], [[4588, 4007], [-1, 1], [-4, -2], [-2, -3], [6, -1], [4, 1]], [[4591, 4003], [-3, 4]], [[4651, 3915], [-3, 0]], [[4648, 3915], [2, -7]], [[4650, 3908], [5, -1], [3, 1], [0, 1], [-3, 5], [-4, 1]], [[8463, 1478], [-5, 6], [-1, -2], [-1, -6], [4, -6], [5, -7], [4, -2], [-1, 4], [-1, 3], [-4, 10]], [[8816, 1291], [-3, -4], [4, -4], [8, -3], [4, 3], [-1, 3], [-6, 5], [-6, 0]], [[8217, 2231], [-8, -1], [-1, -7], [16, -13], [20, -9], [1, 8], [-9, 10], [-11, 8], [-8, 4]], [[9240, 5170], [-5, -1], [-14, 1], [0, -1], [6, -4], [9, 0], [10, 4], [5, 7], [-11, -6]], [[6396, 3304], [-1, 5]], [[6395, 3309], [-3, -1], [-6, -13]], [[6386, 3295], [-1, -3]], [[6385, 3292], [-4, -10], [2, 0], [7, 14], [6, 8]], [[7004, 3491], [0, -2], [4, 6]], [[7008, 3495], [1, 5]], [[7009, 3500], [-2, -2], [-3, -7]], [[7021, 3560], [0, -2]], [[7021, 3558], [5, -2]], [[7026, 3556], [6, -3], [1, 1], [-1, 2]], [[7032, 3556], [-11, 4]], [[7068, 3561], [-12, -2]], [[7056, 3559], [-4, -2]], [[7052, 3557], [2, -1], [10, 0]], [[7064, 3556], [7, 3]], [[7071, 3559], [-3, 2]], [[8990, 2128], [0, -3], [2, -3], [5, -2], [1, 3], [0, 7], [-1, 1], [-3, 0], [-4, -3]], [[8801, 2134], [2, -4], [4, -1], [6, 1], [10, 3], [-1, 1], [-5, 2], [-8, 1], [-8, -3]], [[9028, 2015], [3, -3], [4, 2], [0, 6], [-3, 3], [-3, 0], [-1, -8]], [[8889, 2178], [-1, -5], [0, -2], [2, 0], [2, 1], [5, 6], [-8, 0]], [[8862, 2158], [1, -2], [3, 0], [5, 3], [-7, 2], [-2, -3]], [[8872, 2169], [-3, -3], [2, -2], [6, 1], [3, 4], [-2, 1], [-6, -1]], [[8854, 2082], [15, 1], [9, 5], [-8, 0], [-7, 5], [-9, -2], [0, -9]], [[7888, 2950], [-6, -2], [-3, -4], [2, -2], [6, -2], [9, 3], [6, 3], [-1, 1], [-4, 4], [-9, -1]], [[7608, 2910], [-4, -3]], [[7604, 2907], [4, -2]], [[7608, 2905], [3, 3], [4, 1]], [[7615, 2909], [5, 4]], [[7620, 2913], [-12, -3]], [[7951, 4081], [6, -4]], [[7957, 4077], [-2, -1]], [[7955, 4076], [-3, 2], [-4, 2]], [[7948, 4080], [-1, 2]], [[7947, 4082], [4, -1]], [[4395, 5553], [0, -2], [-1, 0]], [[4394, 5551], [-3, 4]], [[4391, 5555], [0, 4]], [[4391, 5559], [2, 2]], [[4393, 5561], [3, -7]], [[4396, 5554], [-1, -1]], [[7204, 1934], [6, -1], [-12, -6], [-13, -10], [-3, 1], [5, 7], [9, 6], [8, 3]], [[7781, 982], [-1, -5], [-4, -4], [-1, -5], [-4, 11], [1, 3], [-1, 10], [4, 3], [2, 0], [3, -1], [1, -12]], [[8177, 5360], [-13, -6], [-8, -8], [3, 0], [11, 3], [10, -3], [6, 3], [2, 2], [0, 7], [5, 18], [-3, 0], [-13, -16]], [[8176, 5362], [10, 13], [-12, -2], [-10, -12], [3, -1], [9, 2]], [[9151, 5362], [-4, -3], [0, -2], [5, -5], [1, -2], [3, 1], [1, 3], [3, 6], [-9, 2]], [[9116, 5595], [7, 9], [-7, 1], [-5, -2], [-6, -4], [-16, -15], [-7, -5], [-7, -23], [5, -1], [9, 0], [5, 4], [-4, 1], [-5, 0], [-2, 2], [2, 7], [4, 6], [6, 5], [5, 8], [8, 7], [8, 0]], [[8879, 5631], [-2, 0], [-6, -1], [0, -8], [-1, -4], [1, -1], [3, 0], [7, 8], [2, 8], [-4, -2]], [[8877, 5636], [9, 6], [-1, 5], [-1, 1], [-2, -1], [-1, -2], [-4, -9]], [[8376, 5517], [-1, -4], [7, 1], [8, 5], [7, 9], [-4, 0], [-7, -4], [-10, -7]], [[8743, 5206], [4, 4], [5, 12], [-3, -1], [-5, -11], [-1, -4]], [[8561, 5205], [-1, -2], [1, -6], [3, 0], [3, 2]], [[8567, 5199], [-3, 5]], [[8564, 5204], [-2, 2]], [[8562, 5206], [-1, -1]], [[8699, 5250], [11, 6], [0, 3], [1, 10], [-4, 2], [-4, -5], [-4, -16]], [[8591, 5230], [-3, -3], [-5, -8]], [[8583, 5219], [0, -8]], [[8583, 5211], [2, -2]], [[8585, 5209], [5, -4]], [[8590, 5205], [1, 3]], [[8591, 5208], [0, 6]], [[8591, 5214], [5, -1], [4, 2], [0, 4], [-4, 9], [-5, 2]], [[1743, 218], [1, 14], [-9, 6], [-4, 1], [10, -10], [-2, -2], [-3, -10], [-6, 5], [-2, 5], [-3, -3], [12, -14], [13, -6], [6, 1], [-13, 13]], [[1584, 456], [2, -4], [1, 4], [1, 0], [2, -2], [2, -2], [-1, -2], [-3, -1], [2, -2], [4, 0], [1, 5], [-6, 10], [-2, -1], [-2, -2], [-1, -3]], [[1927, 2311], [-3, 10]], [[1924, 2321], [-6, 2]], [[1918, 2323], [-2, 15]], [[1916, 2338], [-8, 14], [-33, 20], [-16, 13], [-3, -2]], [[1856, 2383], [-2, -11]], [[1854, 2372], [6, -18]], [[1860, 2354], [-7, -14]], [[1853, 2340], [-6, -7]], [[1847, 2333], [-5, -10]], [[1842, 2323], [7, -18], [5, -25]], [[1854, 2280], [-1, -30]], [[1853, 2250], [2, -7]], [[1855, 2243], [16, -12]], [[1871, 2231], [4, 0]], [[1875, 2231], [3, 5], [4, 12]], [[1882, 2248], [17, 18]], [[1899, 2266], [20, 7]], [[1919, 2273], [15, 15], [4, 9]], [[1938, 2297], [-3, 5]], [[1935, 2302], [-8, 9]], [[1775, 2491], [-2, 3]], [[1773, 2494], [-4, -2]], [[1769, 2492], [-16, 4]], [[1753, 2496], [-3, 1]], [[1750, 2497], [0, -5]], [[1750, 2492], [-2, -4]], [[1748, 2488], [-1, -4], [20, -2], [13, -5]], [[1780, 2477], [8, 6]], [[1788, 2483], [3, 6]], [[1791, 2489], [-2, 1]], [[1789, 2490], [-14, 1]], [[1838, 2452], [-3, 5]], [[1835, 2457], [-10, 8]], [[1825, 2465], [-6, -2]], [[1819, 2463], [-8, -3]], [[1811, 2460], [-2, 2]], [[1809, 2462], [-4, 7]], [[1805, 2469], [-4, 5]], [[1801, 2474], [-6, -1]], [[1795, 2473], [-3, -9]], [[1792, 2464], [1, -5]], [[1793, 2459], [6, -10]], [[1799, 2449], [5, -3]], [[1804, 2446], [5, 1], [2, -11], [1, -11], [3, -1], [7, -1]], [[1822, 2423], [6, 4]], [[1828, 2427], [10, 2]], [[1838, 2429], [7, 8], [2, 5], [-2, 4], [-7, 6]], [[1521, 2576], [-1, -6]], [[1520, 2570], [0, -5], [3, -1]], [[1523, 2564], [1, 5]], [[1524, 2569], [3, 5], [5, 3], [0, 6]], [[1532, 2583], [3, 6]], [[1535, 2589], [-4, 1]], [[1531, 2590], [-5, -9]], [[1526, 2581], [-5, -5]], [[1705, 2526], [-2, 5], [1, 4], [-9, 18]], [[1695, 2553], [-4, -1]], [[1691, 2552], [-8, -11]], [[1683, 2541], [-12, -2]], [[1671, 2539], [3, -6]], [[1674, 2533], [0, -5]], [[1674, 2528], [8, -13], [2, -7], [2, -1]], [[1686, 2507], [8, 1]], [[1694, 2508], [-3, 6]], [[1691, 2514], [3, 1]], [[1694, 2515], [1, -1]], [[1695, 2514], [0, -5]], [[1695, 2509], [5, 1]], [[1700, 2510], [4, -5]], [[1704, 2505], [4, -3], [8, 1], [4, 4], [-1, 3], [-4, 5]], [[1715, 2515], [-1, 9]], [[1714, 2524], [-4, -1]], [[1710, 2523], [-2, 1], [-3, 2]], [[1571, 2614], [-12, -10], [-4, -11], [3, -6], [8, -5], [2, -5], [8, -1]], [[1576, 2576], [4, -3]], [[1580, 2573], [7, 7]], [[1587, 2580], [2, 5]], [[1589, 2585], [1, 9]], [[1590, 2594], [2, 6]], [[1592, 2600], [0, 6]], [[1592, 2606], [-4, 8], [-17, 0]], [[1774, 2462], [-9, -2]], [[1765, 2460], [5, -10]], [[1770, 2450], [1, -8]], [[1771, 2442], [5, -2]], [[1776, 2440], [5, 4]], [[1781, 2444], [3, 6]], [[1784, 2450], [-3, 6]], [[1781, 2456], [-3, 3]], [[1778, 2459], [-4, 3]], [[586, 7105], [-11, -1]], [[575, 7104], [-17, 8], [-9, 6]], [[549, 7118], [-5, 12]], [[544, 7130], [-9, -5]], [[535, 7125], [-2, -9]], [[533, 7116], [9, -3], [24, -16]], [[566, 7097], [19, 0]], [[585, 7097], [12, -4]], [[597, 7093], [-3, 6]], [[594, 7099], [-8, 6]], [[739, 6729], [0, -2]], [[739, 6727], [3, -4]], [[742, 6723], [7, -2], [8, 6], [3, 7], [-21, -5]], [[1500, 6511], [-1, -2]], [[1499, 6509], [2, -3]], [[1501, 6506], [11, -6], [1, 10], [-3, 2], [-10, -1]], [[1493, 6506], [3, 7]], [[1496, 6513], [0, 2], [-2, -1]], [[1494, 6514], [-5, -6]], [[1489, 6508], [-2, -1]], [[1487, 6507], [-1, 0]], [[1486, 6507], [1, 5]], [[1487, 6512], [0, 3]], [[1487, 6515], [-2, 2], [-2, 1], [-5, -3], [-4, -6], [-1, -2], [2, -17]], [[1475, 6490], [2, -3]], [[1477, 6487], [4, 3]], [[1481, 6490], [3, 1]], [[1484, 6491], [7, -2], [10, 3], [0, 1]], [[1501, 6493], [-4, 4]], [[1497, 6497], [-4, 9]], [[1351, 6462], [1, -4], [2, -3]], [[1354, 6455], [6, -4]], [[1360, 6451], [2, 1], [2, 2], [2, 8]], [[1366, 6462], [0, 3]], [[1366, 6465], [-2, 3]], [[1364, 6468], [-2, 0]], [[1362, 6468], [-11, -6]], [[1321, 6410], [1, -5]], [[1322, 6405], [6, -5]], [[1328, 6400], [7, -3]], [[1335, 6397], [7, 3]], [[1342, 6400], [-4, 5]], [[1338, 6405], [-3, 2]], [[1335, 6407], [-14, 3]], [[1580, 6477], [0, -3]], [[1580, 6474], [3, -7]], [[1583, 6467], [2, -1], [2, 1]], [[1587, 6467], [0, 3]], [[1587, 6470], [-2, 5]], [[1585, 6475], [-5, 2]], [[1546, 6496], [2, 5]], [[1548, 6501], [-1, 1], [-3, -1], [-4, -8], [-5, 0], [-5, -7]], [[1530, 6486], [-2, -2]], [[1528, 6484], [-3, 0], [2, -7]], [[1527, 6477], [-1, -3]], [[1526, 6474], [0, -3], [-5, -10], [4, 2]], [[1525, 6463], [10, 12]], [[1535, 6475], [3, 3], [4, 1], [1, 4]], [[1543, 6483], [5, 2]], [[1548, 6485], [2, 2]], [[1550, 6487], [1, 4], [-5, 5]], [[1576, 6488], [-3, 9]], [[1573, 6497], [-2, -1]], [[1571, 6496], [-2, -5], [1, -2]], [[1570, 6489], [-1, -4]], [[1569, 6485], [-3, 0], [0, -3], [-1, -3]], [[1565, 6479], [3, -2]], [[1568, 6477], [4, 2]], [[1572, 6479], [2, -2], [1, 1], [1, 10]], [[770, 6217], [0, -2], [1, -3]], [[771, 6212], [8, 1]], [[779, 6213], [11, -3]], [[790, 6210], [2, 2]], [[792, 6212], [1, 5]], [[793, 6217], [-2, 2]], [[791, 6219], [-3, 2]], [[788, 6221], [-5, 0]], [[783, 6221], [-13, -4]], [[717, 6199], [-9, -8]], [[708, 6191], [-2, -3]], [[706, 6188], [0, -5]], [[706, 6183], [2, -1], [5, 3], [3, 2], [3, 0], [3, 2], [2, 4], [0, 3], [-2, 2], [-5, 1]], [[1095, 6370], [-10, 7], [-6, 0], [-5, -2]], [[1074, 6375], [-4, -3]], [[1070, 6372], [-1, -3], [0, -3], [3, -7]], [[1072, 6359], [2, -1]], [[1074, 6358], [8, 1]], [[1082, 6359], [2, -1]], [[1084, 6358], [2, 1]], [[1086, 6359], [3, 2]], [[1089, 6361], [8, 1]], [[1097, 6362], [3, 2]], [[1100, 6364], [0, 2]], [[1100, 6366], [-5, 4]], [[1120, 6377], [-4, 1], [-3, 7], [-2, 1]], [[1111, 6386], [-3, -1]], [[1108, 6385], [-5, -3], [3, -8]], [[1106, 6374], [0, -5]], [[1106, 6369], [1, -2], [4, 1]], [[1111, 6368], [7, 6]], [[1118, 6374], [4, 1], [-2, 2]], [[938, 6292], [-2, 2], [-12, 6]], [[924, 6300], [-7, 0]], [[917, 6300], [-9, -3], [-7, -3]], [[901, 6294], [-5, -6]], [[896, 6288], [-4, -5], [0, -7]], [[892, 6276], [0, -3]], [[892, 6273], [3, -3]], [[895, 6270], [-1, -2]], [[894, 6268], [-5, -3]], [[889, 6265], [-10, 1], [-5, -1]], [[874, 6265], [-4, -4]], [[870, 6261], [-5, -6]], [[865, 6255], [-2, -3]], [[863, 6252], [0, -8], [-1, -4], [-3, -3]], [[859, 6237], [-6, -8]], [[853, 6229], [-5, -5], [-8, -5]], [[840, 6219], [-1, -4]], [[839, 6215], [2, 0]], [[841, 6215], [25, 15]], [[866, 6230], [3, 0]], [[869, 6230], [8, 7]], [[877, 6237], [4, 2]], [[881, 6239], [3, 1]], [[884, 6240], [5, 5]], [[889, 6245], [6, 8]], [[895, 6253], [7, 10]], [[902, 6263], [24, 12], [7, 5]], [[933, 6280], [2, 6]], [[935, 6286], [3, 6]], [[1037, 6306], [6, 5]], [[1043, 6311], [7, 3]], [[1050, 6314], [3, 3], [-2, 2]], [[1051, 6319], [-4, 0]], [[1047, 6319], [-12, -2]], [[1035, 6317], [0, 3]], [[1035, 6320], [4, 7]], [[1039, 6327], [13, 10], [7, 7]], [[1059, 6344], [-8, 5]], [[1051, 6349], [-3, 3], [-2, -2], [-3, -7]], [[1043, 6343], [-4, -4]], [[1039, 6339], [-6, -4]], [[1033, 6335], [-4, 6]], [[1029, 6341], [0, 11]], [[1029, 6352], [-4, 1], [-5, 0]], [[1020, 6353], [-8, -3]], [[1012, 6350], [-10, -2]], [[1002, 6348], [-5, -2]], [[997, 6346], [-4, -5]], [[993, 6341], [-2, -4]], [[991, 6337], [0, -3], [1, -4], [2, -3], [4, -2]], [[998, 6325], [4, 0]], [[1002, 6325], [6, -1]], [[1008, 6324], [9, -3]], [[1017, 6321], [3, -2]], [[1020, 6319], [-5, -4], [0, -4], [-1, -1]], [[1014, 6310], [-2, 1]], [[1012, 6311], [-4, 6]], [[1008, 6317], [-9, 0]], [[999, 6317], [-2, -5]], [[997, 6312], [-4, -3], [-4, -12], [-5, -4], [-17, -7], [-8, -5], [-8, -1]], [[951, 6280], [-6, -2]], [[945, 6278], [-7, -5], [2, -3]], [[940, 6270], [8, -5]], [[948, 6265], [4, 0]], [[952, 6265], [2, 2], [6, 0]], [[960, 6267], [3, 2]], [[963, 6269], [4, 4], [4, 2], [3, 0], [3, 1]], [[977, 6276], [2, 2]], [[979, 6278], [9, 5]], [[988, 6283], [15, 4]], [[1003, 6287], [8, 1]], [[1011, 6288], [7, 3]], [[1018, 6291], [5, 7]], [[1023, 6298], [14, 8]], [[1059, 6327], [-1, -2], [0, -3]], [[1058, 6322], [2, -3]], [[1060, 6319], [1, 1]], [[1061, 6320], [2, 4]], [[1063, 6324], [3, 3]], [[1066, 6327], [3, 6]], [[1069, 6333], [-1, 1], [-3, -1], [-3, -2], [-3, -4]], [[579, 6163], [-5, -4]], [[574, 6159], [-3, -4], [-3, -6]], [[568, 6149], [6, -2]], [[574, 6147], [6, 2]], [[580, 6149], [11, 7]], [[591, 6156], [-5, 5], [-7, 2]], [[511, 6128], [-15, 5]], [[496, 6133], [-8, 1]], [[488, 6134], [-10, -3]], [[478, 6131], [-1, -2]], [[477, 6129], [-11, 3]], [[466, 6132], [-4, 0]], [[462, 6132], [0, -3]], [[462, 6129], [5, -3]], [[467, 6126], [4, -3]], [[471, 6123], [3, 0], [13, 1]], [[487, 6124], [16, -2]], [[503, 6122], [7, 0]], [[510, 6122], [11, 3], [4, -1]], [[525, 6124], [12, 2]], [[537, 6126], [0, 2]], [[537, 6128], [-7, 1], [-19, -1]], [[449, 6166], [-11, -5]], [[438, 6161], [-5, -4]], [[433, 6157], [-5, -3], [2, -2]], [[430, 6152], [12, -3]], [[442, 6149], [-4, -7]], [[438, 6142], [-13, -3]], [[425, 6139], [-15, -6]], [[410, 6133], [-19, -5], [-15, -5], [-8, -1], [-6, -2]], [[362, 6120], [6, -4]], [[368, 6116], [41, 5]], [[409, 6121], [26, 5]], [[435, 6126], [17, 7]], [[452, 6133], [-4, 8]], [[448, 6141], [1, 2]], [[449, 6143], [8, 3]], [[457, 6146], [2, 5]], [[459, 6151], [1, 5]], [[460, 6156], [-2, 4]], [[458, 6160], [-9, 6]], [[293, 6104], [-1, -6]], [[292, 6098], [8, -6], [7, 3]], [[307, 6095], [-5, 4]], [[302, 6099], [-9, 5]], [[286, 6093], [0, 1]], [[286, 6094], [-7, 7]], [[279, 6101], [-3, -3]], [[276, 6098], [1, -9], [4, -3], [5, 7]], [[262, 6097], [0, 4], [3, 7], [0, 2], [-3, 5], [-8, 0]], [[254, 6115], [-4, -10]], [[250, 6105], [1, -7]], [[251, 6098], [-3, -2]], [[248, 6096], [-8, -4]], [[240, 6092], [-6, -22]], [[234, 6070], [9, 9]], [[243, 6079], [5, -5]], [[248, 6074], [17, 9]], [[265, 6083], [3, 4]], [[268, 6087], [4, -2], [1, 1], [1, 2]], [[274, 6088], [0, 8]], [[274, 6096], [-3, 2], [-9, -1]], [[304, 6114], [2, 3]], [[306, 6117], [4, 3]], [[310, 6120], [-1, 3]], [[309, 6123], [-4, 4]], [[305, 6127], [-3, 2]], [[302, 6129], [-6, 0]], [[296, 6129], [-3, -5]], [[293, 6124], [1, -3], [3, -3], [7, -4]], [[215, 6098], [-4, -4]], [[211, 6094], [-6, -3]], [[205, 6091], [-26, -7], [0, -2]], [[179, 6082], [1, -3]], [[180, 6079], [6, 2], [8, 1], [7, 0]], [[201, 6082], [12, -1]], [[213, 6081], [4, 1], [2, 2], [1, 10]], [[220, 6094], [1, 4]], [[221, 6098], [4, 3], [1, 4]], [[226, 6105], [-4, 4]], [[222, 6109], [-1, 0], [-3, -3], [-3, -8]], [[181, 6097], [-12, 1]], [[169, 6098], [-5, 3]], [[164, 6101], [-7, 6]], [[157, 6107], [-12, 0], [-4, -1], [-2, -3], [3, -4], [8, -5], [5, -5]], [[155, 6089], [1, -3]], [[156, 6086], [-2, -2]], [[154, 6084], [-6, -3]], [[148, 6081], [1, -2], [11, -7], [1, 0], [2, 4], [4, 4]], [[167, 6080], [4, 11]], [[171, 6091], [4, 3], [6, 3]], [[9895, 8217], [3, -6], [-2, -8], [2, -7], [6, -14], [3, -3], [2, -1], [12, 4], [3, 3], [0, 5], [1, 5], [4, 11], [12, 5], [4, 3], [2, 3], [-20, 7], [-12, 2], [-9, -2], [-11, -7]], [[9720, 8356], [7, 1], [4, 2], [2, 3], [2, 4], [0, 4], [-1, 4], [-5, 7], [-6, 6], [-9, -2], [-12, -9], [-7, -6], [0, -3], [1, -3], [3, -2], [21, -6]], [[9574, 8537], [19, 13], [9, 5], [11, 8], [-1, 3], [-13, 3], [-3, -2], [-14, -4], [-7, -1], [-5, 0], [-7, 1], [-25, -1], [-6, -4], [-11, -4], [6, -6], [7, -1], [8, 1], [6, 0], [3, -2], [1, -1], [2, -3], [8, -1], [4, -3], [5, -2], [3, 1]], [[9847, 8349], [-10, -1], [-3, -7], [0, -3], [2, -1], [24, -2], [10, 0], [5, 2], [-1, 2], [-14, 6], [-13, 4]], [[4145, 5807], [-8, 2], [-1, -5], [0, -5], [3, -2], [10, -2], [-3, 11], [-1, 1]], [[4422, 5578], [0, -7], [3, 4], [1, -1], [0, -3], [1, 0], [3, 2], [1, 2]], [[4431, 5575], [-4, 6]], [[4427, 5581], [-2, 5], [-1, 0], [-2, -8]], [[4421, 5610], [-2, 2]], [[4419, 5612], [-1, -1], [-1, -4]], [[4417, 5607], [0, -5]], [[4417, 5602], [1, -3]], [[4418, 5599], [4, -2]], [[4422, 5597], [1, 2], [-1, 10]], [[4422, 5609], [-1, 1]], [[4390, 5730], [-4, -4], [-2, -3], [1, -3]], [[4385, 5720], [7, -4]], [[4392, 5716], [1, 3]], [[4393, 5719], [-1, 6]], [[4392, 5725], [1, -1]], [[4393, 5724], [4, -4]], [[4397, 5720], [2, 1]], [[4399, 5721], [3, 2]], [[4402, 5723], [-1, 3]], [[4401, 5726], [-9, 4]], [[4392, 5730], [-2, 0]], [[4394, 5711], [-1, 0]], [[4393, 5711], [-2, -1]], [[4391, 5710], [-2, -6]], [[4389, 5704], [1, -4]], [[4390, 5700], [3, -2], [4, -2], [1, 1], [0, 3]], [[4398, 5700], [-3, 6]], [[4395, 5706], [-1, 5]], [[4375, 5719], [-3, -1], [-1, -2], [1, -8], [1, -2]], [[4373, 5706], [4, -2]], [[4377, 5704], [8, -2]], [[4385, 5702], [-2, 3]], [[4383, 5705], [-1, 5]], [[4382, 5710], [-7, 9]], [[4422, 5656], [-1, -7], [-4, 16], [-2, 5], [-2, 2]], [[4413, 5672], [-6, 1]], [[4407, 5673], [1, 2]], [[4408, 5675], [11, 6], [1, 3], [-3, 5], [-2, 2], [-2, 1]], [[4413, 5692], [-3, -4]], [[4410, 5688], [-5, -8]], [[4405, 5680], [-1, -5]], [[4404, 5675], [0, -2]], [[4404, 5673], [4, -6]], [[4408, 5667], [3, -2]], [[4411, 5665], [2, -1], [1, -2], [1, -9]], [[4415, 5653], [1, -3]], [[4416, 5650], [2, -4]], [[4418, 5646], [5, -2]], [[4423, 5644], [3, -2]], [[4426, 5642], [1, -4]], [[4427, 5638], [2, -1], [3, 0], [1, 2], [0, 6], [-11, 11]], [[4269, 5843], [3, -4], [7, -6], [18, -9], [-2, 3], [-12, 16], [-8, 7], [-10, 4], [-6, 1], [-2, -2], [2, -2], [8, -6], [2, -2]], [[4355, 5749], [1, 2], [-1, 2], [-8, 6], [-16, 16], [4, -6], [8, -11], [12, -9]], [[4348, 5749], [-6, 6], [-3, 0], [5, -20], [2, -2], [1, -1], [4, 3], [2, 0], [-1, 5], [-5, 6], [1, 3]], [[4232, 5885], [5, 5], [0, 2], [0, 5], [-2, 4], [-4, 6], [0, -2], [0, -3], [-3, -4], [2, -14], [2, 1]], [[4097, 5863], [-7, 4], [-8, -4], [2, -7], [0, -2], [-3, -2], [-1, -1], [1, -2], [10, -9], [5, -4], [5, -1], [3, 0], [1, 3], [0, 6], [-2, 8], [-3, 7], [-3, 4]], [[4221, 5919], [-1, 3], [-5, 6], [-5, 3], [-3, -1], [-3, -7], [-1, -5], [7, -21], [5, -10], [1, 6], [6, 8], [3, 7], [-2, 5], [-2, 6]], [[4730, 3877], [-4, 9]], [[4726, 3886], [-3, -3]], [[4723, 3883], [5, -9]], [[4728, 3874], [4, -11]], [[4732, 3863], [5, -2], [5, 1], [-3, 2]], [[4739, 3864], [-9, 13]], [[4745, 3427], [0, 6], [-5, 0], [-2, -2], [0, -6], [9, -25], [3, 5], [0, 12], [-2, 5], [-3, 5]], [[4596, 4001], [6, -11]], [[4602, 3990], [4, -2]], [[4606, 3988], [6, 2]], [[4612, 3990], [4, 6], [-1, 2]], [[4615, 3998], [-6, 5]], [[4609, 4003], [-13, -2]], [[4640, 4003], [-16, 6], [-3, -1], [2, -5]], [[4623, 4003], [1, -4]], [[4624, 3999], [6, -3], [19, 4], [1, 3], [-2, 3], [-8, -3]], [[4733, 3924], [1, -5], [6, 1]], [[4740, 3920], [6, -1]], [[4746, 3919], [-4, 8], [-3, 4]], [[4739, 3931], [-13, 7]], [[4726, 3938], [-1, -1], [0, -4], [1, 0]], [[4726, 3933], [4, -1]], [[4730, 3932], [1, -1]], [[4731, 3931], [2, -7]], [[8354, 4878], [2, 2]], [[8356, 4880], [1, 7]], [[8357, 4887], [-1, 3]], [[8356, 4890], [-2, 0], [-1, -6]], [[8353, 4884], [1, -6]], [[8134, 4650], [9, 18]], [[8143, 4668], [2, 8]], [[8145, 4676], [-11, -23]], [[8134, 4653], [0, -3]], [[8139, 4778], [-4, -12], [4, 1], [3, 2]], [[8142, 4769], [5, 5]], [[8147, 4774], [1, 4], [0, 4]], [[8148, 4782], [-3, 1]], [[8145, 4783], [-6, -5]], [[8863, 3801], [-7, -4], [1, -2], [2, 0], [7, 4], [5, 10], [-2, 1], [-6, -9]], [[8409, 4876], [-7, -9]], [[8402, 4867], [-5, -2], [3, -3], [21, 5]], [[8421, 4867], [-1, 5]], [[8420, 4872], [-7, 5], [-4, -1]], [[8357, 4878], [4, 0], [4, 3]], [[8365, 4881], [1, 19]], [[8366, 4900], [-3, -2], [-1, -2], [-2, -7], [-1, -6]], [[8359, 4883], [-2, -5]], [[8456, 4862], [-2, -2]], [[8454, 4860], [-11, -3]], [[8443, 4857], [13, -5], [6, 2], [0, 4]], [[8462, 4858], [-4, 12]], [[8458, 4870], [-1, -3]], [[8457, 4867], [-1, -5]], [[8016, 4140], [-6, -2], [-10, -6], [2, -1]], [[8002, 4131], [14, 9]], [[8026, 4216], [1, 2], [0, 3], [-1, 3], [-5, 5], [5, -13]], [[8059, 4479], [-2, -4]], [[8057, 4475], [-10, -20]], [[8047, 4455], [3, 2]], [[8050, 4457], [9, 18]], [[8059, 4475], [1, 4]], [[8060, 4479], [6, 12]], [[8066, 4491], [3, 14]], [[8069, 4505], [-3, -7]], [[8066, 4498], [-7, -19]], [[8037, 4208], [0, -4], [2, -19], [0, -11]], [[8039, 4174], [-4, -24]], [[8035, 4150], [-12, -7], [1, -1], [10, 3]], [[8034, 4145], [3, 5]], [[8037, 4150], [3, 20]], [[8040, 4170], [1, 14]], [[8041, 4184], [-2, 18]], [[8039, 4202], [-2, 6]], [[7966, 4089], [-7, -15], [1, 1]], [[7960, 4075], [12, 19]], [[7972, 4094], [11, 16]], [[7983, 4110], [-4, -3]], [[7979, 4107], [-13, -18]], [[6421, 3333], [-13, -12]], [[6408, 3321], [-5, -3]], [[6403, 3318], [-4, -7], [3, 1]], [[6402, 3312], [17, 14]], [[6419, 3326], [10, 8]], [[6429, 3334], [1, 5]], [[6430, 3339], [-1, -1]], [[6429, 3338], [-3, -4]], [[6426, 3334], [-5, -1]], [[6361, 3238], [-6, -23]], [[6355, 3215], [-1, -10], [3, 6], [8, 29], [14, 33]], [[6379, 3273], [-5, -5]], [[6374, 3268], [-13, -30]], [[6531, 3427], [4, 2]], [[6535, 3429], [13, 12]], [[6548, 3441], [8, 10]], [[6556, 3451], [-5, 1], [-3, -6]], [[6548, 3446], [-17, -19]], [[6354, 3199], [-1, -11], [0, -33], [10, -58], [7, -25], [1, 5], [-3, 17]], [[6368, 3094], [-11, 59]], [[6357, 3153], [-3, 46]], [[7303, 3486], [-5, 0], [8, -3], [8, 4]], [[7314, 3487], [13, 11]], [[7327, 3498], [-6, -2], [-5, -5]], [[7316, 3491], [-9, -6]], [[7307, 3485], [-4, 1]], [[7579, 3615], [3, 28]], [[7582, 3643], [-2, -2]], [[7580, 3641], [-3, -6], [0, -10], [2, -10]], [[6985, 3549], [-7, -7]], [[6978, 3542], [-1, -2]], [[6977, 3540], [-2, -2], [-2, -2]], [[6973, 3536], [6, 0]], [[6979, 3536], [3, -3]], [[6982, 3533], [0, 6], [1, 5]], [[6983, 3544], [2, 5]], [[6784, 3482], [-6, 5]], [[6778, 3487], [-4, 0]], [[6774, 3487], [-6, -4]], [[6768, 3483], [1, -4]], [[6769, 3479], [13, -10], [2, 1], [3, 5]], [[6787, 3475], [0, 3]], [[6787, 3478], [-1, 2], [-2, 2]], [[7013, 3521], [-3, -18]], [[7010, 3503], [3, 3]], [[7013, 3506], [1, 15]], [[7014, 3521], [-1, 8]], [[7013, 3529], [-3, 7]], [[7010, 3536], [3, -15]], [[7881, 2816], [0, 3], [0, 3], [-2, 17], [-4, 10], [-5, -12], [-6, 5], [8, 8], [0, 3], [-4, 4], [-4, -2], [-7, -1], [-3, -2], [-5, 1], [-4, -4], [7, -15], [5, -6], [3, -18], [3, -3], [0, -13], [15, -1], [4, 15], [0, 5], [-1, 3]], [[7863, 2598], [-6, 1], [-5, -1], [1, -6], [6, -5], [3, -3], [2, 0], [4, -5], [3, 3], [2, 6], [1, 6], [-11, 4]], [[7843, 2622], [-2, 2], [-3, 10], [-4, 5], [-4, 0], [-6, -1], [1, -3], [3, -2], [4, -7], [6, -2], [4, -4], [1, 2]], [[7824, 2642], [-6, 9], [-3, 1], [-4, 0], [-4, -2], [-10, 3], [-5, -2], [2, -3], [3, -3], [6, -2], [10, -1], [2, -2], [4, 0], [5, 2]], [[7883, 3158], [14, -27], [7, -6], [2, -9], [-1, -8], [0, -7], [-2, -8], [0, -16], [-3, -7], [-9, -9], [6, -3], [6, -12], [2, 1], [3, 6], [1, 34], [10, 11], [-2, 23], [-7, 8], [-7, 5], [-1, 3], [0, 3], [-2, 2], [-6, 4], [-6, 11], [-7, 8], [-10, 1], [-9, 3], [-6, 0], [-6, -4], [11, 0], [22, -7]], [[8134, 2672], [11, -6], [5, 5], [-1, 2], [-9, 4], [-4, 6], [-7, 4], [0, -4], [1, -5], [2, -6], [2, 0]], [[8120, 2831], [-2, 7], [-1, 0], [-4, -2], [-2, -16], [3, -1], [6, 12]], [[8179, 2463], [20, 1], [18, 4], [8, 16], [3, 9], [1, 13], [-4, 1], [-6, -13], [-7, -5], [-5, 0], [-10, 5], [-7, -1], [-5, -8], [-7, -3], [0, -2], [1, -2], [-2, -7], [1, -4], [1, -4]], [[8160, 2655], [0, -5], [-2, -6], [-5, -3], [-1, -3], [-3, -3], [-6, -9], [-10, -10], [-1, -6], [5, 3], [9, 11], [9, 6], [11, 21], [-4, 17], [3, 6], [-8, -2], [-2, -4], [0, -5], [5, -8]], [[7973, 2980], [-11, 5], [-13, 13], [-4, 9], [-2, -1], [-1, -9], [-3, -6], [7, 2], [5, -1], [10, -11], [10, -3], [6, -11], [10, -12], [0, -11], [-4, -11], [0, -13], [-8, -1], [1, -2], [5, -5], [2, -9], [3, -3], [0, 13], [2, 14], [2, 25], [-1, 5], [-5, 6], [-11, 17]], [[8003, 2782], [-5, 3], [-2, -8], [7, -2], [13, -14], [6, -3], [3, 1], [-7, 4], [-4, 6], [-11, 13]], [[8066, 2737], [-1, -7], [-5, -2], [-1, -3], [7, -6], [12, -5], [10, -24], [0, 3], [0, 13], [-7, 10], [-10, 7], [-3, 22], [-5, 12], [-4, 13], [-7, 14], [0, -9], [2, -3], [4, -11], [6, -16], [2, -8]], [[8036, 2876], [-10, 27], [-4, 2], [-2, -1], [-1, -4], [6, -7], [2, -7], [4, -5], [7, -19], [7, -7], [-1, -6], [-5, -5], [-2, -4], [3, 0], [8, 2], [5, -1], [-1, 6], [-16, 27], [0, 2]], [[7855, 2603], [2, 3], [-3, 6], [0, 1], [-6, 3], [-2, 8], [-1, -1], [0, -6], [-3, -5], [2, -4], [8, -5], [3, 0]], [[7522, 3123], [2, -8], [3, -4], [5, -2], [3, 3]], [[7535, 3112], [-6, 1]], [[7529, 3113], [-2, 2], [-5, 8]], [[7817, 2887], [9, -7], [5, -2], [3, 3], [1, -9], [5, -6], [2, -9], [5, 6], [8, 4], [2, 4], [8, 7], [1, 4], [-1, 10], [0, 14], [-7, 11], [-6, 17], [-5, 7], [0, 10], [-4, 7], [-10, 7], [-4, -2], [4, -19], [-2, -13], [-9, -19], [2, -7], [1, -1], [2, -4], [-6, -7], [-2, 6], [-7, -2], [5, -10]], [[7571, 2904], [-1, -7], [-4, -4], [5, 1], [3, 1]], [[7574, 2895], [0, 4]], [[7574, 2899], [-3, 5]], [[7671, 3220], [7, -20]], [[7678, 3200], [-1, 9], [-13, 47], [-3, 14], [-3, 6], [5, -24]], [[7663, 3252], [8, -32]], [[7485, 2567], [-2, 8], [-3, 2], [-19, 4], [-6, -13], [-1, -5], [9, -23], [-3, -3], [-3, -2], [-5, 3], [-5, 6], [0, -4], [3, -7], [6, -7], [8, -4], [8, 1], [15, 8], [7, 7], [0, 5], [-5, 18], [-4, 6]], [[7531, 3117], [0, 6]], [[7531, 3123], [-3, 14]], [[7528, 3137], [-3, 4]], [[7525, 3141], [2, -13], [4, -11]], [[7739, 2668], [-15, 15], [-4, 0], [2, -2], [6, -9], [14, -9], [0, 3], [-3, 2]], [[7827, 3133], [24, 4], [0, 3], [-1, 6], [-11, -3], [-14, 1], [-3, -3], [-5, 0], [-10, 3], [-8, 8], [-2, -8], [1, -3], [-1, -5], [-6, -7], [-7, -2], [-11, 11], [-3, 2], [14, -19], [4, -4], [6, 1], [12, 6], [21, 9]], [[7672, 2982], [-2, -1], [-5, -5], [-1, -8]], [[7664, 2968], [-4, -6]], [[7660, 2962], [-4, -4]], [[7656, 2958], [-1, -5]], [[7655, 2953], [-6, -12]], [[7649, 2941], [-2, -5]], [[7647, 2936], [15, 22]], [[7662, 2958], [10, 24]], [[7088, 2134], [-1, 2], [-3, 0], [-4, -18], [-1, -5], [-3, -7], [4, 3], [8, 25]], [[7084, 2050], [-3, -12], [1, -5], [6, 17], [4, 6], [0, 5], [-2, 3], [-1, -5], [-2, -5], [-3, -4]], [[6783, 2197], [1, -3], [9, 3], [2, 4], [9, 6], [-1, 1], [-3, 1], [-17, -12]], [[7519, 1098], [1, 6], [-2, 5], [-2, 1], [-3, -2], [6, -10]], [[7593, 2278], [-1, -3], [-3, -1], [-3, 2], [-1, 4], [-3, -1], [1, -11], [8, -1], [2, 1], [4, 3], [9, 0], [-1, 5], [-12, 2]], [[7159, 2418], [-4, -8], [-3, -12], [2, -13], [4, 3], [10, 20], [4, 10], [-1, 3], [-5, -3], [-7, 0]], [[9021, 2041], [-6, 6], [-3, -2], [-1, -2], [0, -2], [0, -1], [5, -5], [6, -2], [4, -7], [2, 2], [-1, 3], [-3, 3], [-3, 7]], [[9160, 1658], [-3, -3], [-4, -7], [-5, -11], [-1, -6], [1, -10], [9, -7], [4, 12], [1, 22], [-2, 10]], [[9158, 1728], [1, 5], [0, 3], [-2, 0], [-6, 8], [-8, 6], [-4, -1], [-2, -2], [-1, -5], [6, -18], [3, -4], [7, -2], [-6, -9], [0, -2], [2, -5], [13, 1], [3, -6], [1, 1], [1, 7], [-3, 14], [-2, 4], [-3, 5]], [[9159, 1275], [-12, -1], [-7, -3], [-16, -1], [-7, -4], [-10, -2], [-4, -3], [1, -2], [7, -4], [4, -3], [1, -5], [1, -7], [-2, -32], [-3, -2], [-8, -1], [-2, -6], [-19, -15], [11, 2], [13, -2], [33, 2], [12, 6], [1, 4], [0, 11], [2, 7], [-3, 8], [-2, 11], [1, 9], [-1, 13], [3, 6], [6, 14]], [[8586, 1447], [0, -5], [2, -4], [3, -2], [1, -4], [1, -11], [2, -6], [4, 14], [-1, 10], [-12, 8]], [[9175, 1327], [-7, -8], [-1, -5], [5, 1], [16, 10], [1, 7], [-2, 0], [-12, -5]], [[9062, 1977], [-1, 0], [-2, -6], [0, -7], [5, -2], [1, 7], [-3, 8]], [[9261, 1567], [-4, -2], [0, -18], [3, -6], [7, -4], [2, 2], [5, 8], [-5, 5], [-8, 15]], [[8922, 1302], [-2, -8], [-6, 1], [-6, 4], [-2, 5], [-3, -1], [-8, -3], [-4, -8], [3, -3], [11, -2], [5, 2], [4, -7], [4, -2], [5, 0], [6, 1], [6, 10], [1, 3], [-2, 15], [-4, 5], [-8, -12]], [[8987, 2138], [12, 6], [2, 5], [-4, 0], [-10, -8], [0, -3]], [[9102, 1440], [-4, -7], [-3, -9], [0, -7], [-2, -4], [5, 0], [7, 5], [1, 20], [-4, 2]], [[9094, 2064], [1, 3], [0, 11], [-2, 3], [-3, 1], [-3, 2], [-1, -1], [0, -3], [2, -10], [6, -6]], [[9134, 1883], [-2, -1], [-4, -5], [1, -7], [2, -1], [4, 0], [2, 4], [0, 4], [-3, 6]], [[9087, 2001], [8, -2], [3, 5], [1, 2], [1, 2], [0, 3], [-2, 1], [-2, 4], [-6, 4], [-5, -9], [0, -4], [2, -6]], [[9134, 1808], [-2, 18], [-4, 7], [-10, 6], [-1, -3], [-1, -10], [5, -14], [3, -21], [7, 3], [3, 14]], [[9140, 1912], [-14, 13], [-4, 6], [0, 6], [-5, 5], [-3, -4], [-2, -5], [2, -8], [-3, -8], [2, -8], [6, -1], [9, 1], [12, 3]], [[9104, 1921], [-9, 3], [-1, -2], [-2, -4], [3, -29], [3, -10], [3, -1], [7, 5], [2, 5], [-1, 21], [1, 5], [-3, 3], [-3, 4]], [[9143, 1553], [0, 11], [-1, 7], [-3, 0], [-3, -3], [-4, -5], [0, -9], [5, -8], [3, 2], [3, 5]], [[8231, 2631], [7, -1], [9, -8], [2, 4], [-1, 2], [-5, 5], [-9, 4], [-2, 0], [-5, 1], [-7, 4], [-3, -9], [4, -2], [10, 0]], [[8551, 1430], [-14, 9], [-5, 13], [-4, 4], [-3, 0], [1, -9], [12, -19], [15, -11], [4, 2], [-6, 11]], [[8328, 2563], [5, -4], [0, 1], [2, 3], [-3, 5], [-10, 3], [-3, -1], [1, -7], [8, 0]], [[8236, 2530], [-6, 6], [-5, -5], [1, -7], [10, 6]], [[8310, 2572], [5, -4], [1, 1], [-1, 3], [-3, 10], [-6, 0], [-1, -4], [3, -3], [2, 0], [0, -3]], [[8292, 2561], [1, 1], [3, 2], [-1, 1], [-5, -1], [-9, 6], [0, -6], [0, -4], [3, -1], [8, 2]], [[1455, 6902], [-4, -1]], [[1451, 6901], [5, -11]], [[1456, 6890], [6, -1]], [[1462, 6889], [6, 2]], [[1468, 6891], [15, 25]], [[1483, 6916], [-4, 0]], [[1479, 6916], [-17, -7]], [[1462, 6909], [-7, -7]], [[2065, 6716], [1, 0]], [[2066, 6716], [10, 4]], [[2076, 6720], [6, 0], [2, 2], [0, 1], [-2, 2], [-27, 2], [-6, -6], [-1, -10]], [[2048, 6711], [6, -6]], [[2054, 6705], [5, 4]], [[2059, 6709], [6, 7]], [[2157, 7117], [-4, -11]], [[2153, 7106], [-5, -6]], [[2148, 7100], [4, 1], [3, 1], [7, 12]], [[2162, 7114], [-1, 2]], [[2161, 7116], [-4, 1]], [[2114, 6879], [2, 1], [7, 6]], [[2123, 6886], [3, 4]], [[2126, 6890], [0, 3]], [[2126, 6893], [-1, 2]], [[2125, 6895], [-2, 1], [-5, 0]], [[2118, 6896], [-11, -7]], [[2107, 6889], [-2, -2]], [[2105, 6887], [3, -4]], [[2108, 6883], [6, -4]], [[2058, 6805], [-4, 6]], [[2054, 6811], [-4, 4]], [[2050, 6815], [-10, 4], [-3, -1], [0, -2]], [[2037, 6816], [4, -4]], [[2041, 6812], [9, -5]], [[2050, 6807], [4, -4], [2, -1], [2, 3]], [[1866, 6568], [1, -4], [4, -1]], [[1871, 6563], [5, 0]], [[1876, 6563], [3, 4]], [[1879, 6567], [1, 7]], [[1880, 6574], [-1, 4]], [[1879, 6578], [-2, 1]], [[1877, 6579], [-2, -2], [-9, -9]], [[1960, 6658], [-8, -4], [-8, -7], [-3, -4]], [[1941, 6643], [-1, -4]], [[1940, 6639], [0, -2]], [[1940, 6637], [2, -1]], [[1942, 6636], [5, 3], [13, 10]], [[1960, 6649], [6, 6]], [[1966, 6655], [-2, 3]], [[1964, 6658], [-4, 0]], [[1983, 6659], [-8, -4]], [[1975, 6655], [-1, -4]], [[1974, 6651], [6, -3]], [[1980, 6648], [4, 0]], [[1984, 6648], [4, 2], [3, 2], [1, 1]], [[1992, 6653], [0, 3]], [[1992, 6656], [-1, 3]], [[1991, 6659], [-8, 0]], [[2587, 7108], [-2, 3]], [[2585, 7111], [-13, 4]], [[2572, 7115], [-3, -2]], [[2569, 7113], [-5, -4], [-3, -3], [0, -1], [2, -4]], [[2563, 7101], [2, -3]], [[2565, 7098], [3, -8], [1, 0], [14, 6], [17, 5]], [[2600, 7101], [5, 3]], [[2605, 7104], [2, 2]], [[2607, 7106], [-1, 2]], [[2606, 7108], [-5, 1]], [[2601, 7109], [-14, -1]], [[2670, 7098], [4, -2]], [[2674, 7096], [6, -1]], [[2680, 7095], [3, 3]], [[2683, 7098], [1, 6]], [[2684, 7104], [-2, 1]], [[2682, 7105], [-12, -7]], [[2727, 7044], [-5, -8]], [[2722, 7036], [3, 1]], [[2725, 7037], [13, 12]], [[2738, 7049], [12, 7]], [[2750, 7056], [1, 4]], [[2751, 7060], [-9, -2], [-7, -6]], [[2735, 7052], [-8, -8]], [[2537, 7097], [-8, 4], [-5, -1], [-2, -5], [-10, -15]], [[2512, 7080], [-10, -10]], [[2502, 7070], [-10, -8]], [[2492, 7062], [-10, -9]], [[2482, 7053], [-3, -1], [-3, -5]], [[2476, 7047], [-5, -9]], [[2471, 7038], [2, -4]], [[2473, 7034], [9, 2]], [[2482, 7036], [5, 2], [2, 3], [3, 1]], [[2492, 7042], [5, 1]], [[2497, 7043], [3, 2]], [[2500, 7045], [1, 5]], [[2501, 7050], [3, 3]], [[2504, 7053], [3, 2]], [[2507, 7055], [2, 2]], [[2509, 7057], [3, 7], [2, 3], [25, 21], [2, 4], [-4, 5]], [[2468, 7069], [-13, 7]], [[2455, 7076], [-11, -5]], [[2444, 7071], [-4, -7]], [[2440, 7064], [16, -2], [4, 4]], [[2460, 7066], [8, 3]], [[2473, 7099], [-1, -3]], [[2472, 7096], [-2, -3]], [[2470, 7093], [1, -8]], [[2471, 7085], [5, 0], [-1, -3]], [[2475, 7082], [0, -2]], [[2475, 7080], [3, -1], [2, 1], [2, 4]], [[2482, 7084], [1, 6]], [[2483, 7090], [4, 10]], [[2487, 7100], [-2, 5]], [[2485, 7105], [3, 3]], [[2488, 7108], [0, 3]], [[2488, 7111], [-3, 5], [-1, -1], [-3, -4], [-1, -1], [-1, 0]], [[2479, 7110], [-1, 3]], [[2478, 7113], [-1, -1], [-3, -10]], [[2474, 7102], [-1, -3]], [[2467, 7155], [-1, 6]], [[2466, 7161], [-2, 3]], [[2464, 7164], [-6, 3], [-5, -1]], [[2453, 7166], [1, -3]], [[2454, 7163], [-2, -6]], [[2452, 7157], [0, -1]], [[2452, 7156], [5, -2]], [[2457, 7154], [10, 1]], [[3640, 6438], [2, 3], [1, 10], [1, 6], [-2, 2], [-4, 1], [-5, 1]], [[3633, 6461], [-4, -4]], [[3629, 6457], [-1, -1], [-2, 2], [-7, 12]], [[3619, 6470], [-3, 4]], [[3616, 6474], [-1, 9], [-5, 6], [-2, 6], [-16, 13], [-10, -2], [-2, -5]], [[3580, 6501], [3, -6]], [[3583, 6495], [7, -3]], [[3590, 6492], [6, -1]], [[3596, 6491], [3, -11], [7, -14], [3, -2]], [[3609, 6464], [0, -3]], [[3609, 6461], [5, -8]], [[3614, 6453], [10, -11]], [[3624, 6442], [6, -6]], [[3630, 6436], [8, -3], [2, 5]], [[3549, 6656], [4, 3], [3, -1]], [[3556, 6658], [2, 6]], [[3558, 6664], [-3, 9]], [[3555, 6673], [-4, 6]], [[3551, 6679], [-7, 0]], [[3544, 6679], [-5, 8]], [[3539, 6687], [-1, 3], [-4, 3]], [[3534, 6693], [-7, 4]], [[3527, 6697], [-10, -1]], [[3517, 6696], [-8, -10]], [[3509, 6686], [-1, -10], [1, -3], [8, -13], [-1, -4]], [[3516, 6656], [11, -8]], [[3527, 6648], [5, -7]], [[3532, 6641], [-9, -5]], [[3523, 6636], [-4, -25]], [[3519, 6611], [5, -14], [5, 0]], [[3529, 6597], [4, 6]], [[3533, 6603], [3, -1], [3, -3], [1, 1], [2, 3], [1, 7], [4, 12], [1, 11], [-1, 12], [2, 11]], [[3566, 6533], [-8, -5]], [[3558, 6528], [8, -9], [-1, -17]], [[3565, 6502], [8, 5]], [[3573, 6507], [4, 5], [2, 2]], [[3579, 6514], [3, 5]], [[3582, 6519], [0, 2]], [[3582, 6521], [11, 8], [0, 2]], [[3593, 6531], [-1, 3]], [[3592, 6534], [-4, 2]], [[3588, 6536], [-8, -5]], [[3580, 6531], [-9, 1]], [[3571, 6532], [-5, 1]], [[3736, 6470], [-2, -8]], [[3734, 6462], [1, -3]], [[3735, 6459], [3, -1], [6, -1], [7, 2], [0, 5]], [[3751, 6464], [-8, 15]], [[3743, 6479], [-5, 16], [-9, 6]], [[3729, 6501], [-4, 0]], [[3725, 6501], [-1, -2]], [[3724, 6499], [0, -5]], [[3724, 6494], [2, -8]], [[3726, 6486], [-3, -5]], [[3723, 6481], [0, -8], [4, 3], [5, -2]], [[3732, 6474], [4, -4]], [[3622, 6663], [-2, -4]], [[3620, 6659], [-1, -5]], [[3619, 6654], [1, -5]], [[3620, 6649], [4, -1]], [[3624, 6648], [10, 0]], [[3634, 6648], [0, 1], [3, 2]], [[3637, 6651], [6, 2]], [[3643, 6653], [5, 2], [-2, 7], [-5, 6], [-14, 13], [-2, -12]], [[3625, 6669], [-3, -6]], [[3663, 6646], [-10, -19], [-3, -1]], [[3650, 6626], [-4, -10]], [[3646, 6616], [-6, -2], [-2, -3], [1, -8]], [[3639, 6603], [2, -6]], [[3641, 6597], [5, -2]], [[3646, 6595], [11, -1]], [[3657, 6594], [4, -3]], [[3661, 6591], [-1, -6]], [[3660, 6585], [1, -2]], [[3661, 6583], [3, -3]], [[3664, 6580], [6, -1]], [[3670, 6579], [6, 3]], [[3676, 6582], [3, 0], [3, -1], [1, 19], [4, 16], [-11, 17], [-8, 12], [-5, 1]], [[3620, 6640], [-6, -6]], [[3614, 6634], [-2, -4], [0, -2], [11, -10]], [[3623, 6618], [5, -2]], [[3628, 6616], [4, 1], [8, 4], [2, 3]], [[3642, 6624], [-1, 3]], [[3641, 6627], [1, 3]], [[3642, 6630], [1, 3]], [[3643, 6633], [1, 3]], [[3644, 6636], [-2, 3]], [[3642, 6639], [-4, 1], [-15, 1]], [[3623, 6641], [-3, -1]], [[3514, 6850], [-7, 7]], [[3507, 6857], [-9, 5]], [[3498, 6862], [-10, -2]], [[3488, 6860], [-1, -3]], [[3487, 6857], [5, -6], [11, -4], [11, 0], [0, 3]], [[3906, 6212], [-2, 0], [-2, -1], [0, -3], [1, -6], [8, -14], [4, -3], [10, -13], [3, 5], [-1, 11], [-4, 7], [-15, 15], [-2, 2]], [[3775, 6425], [-3, -9], [0, -5], [1, -2], [1, 0], [10, 7], [3, 4], [1, 3], [-1, 2], [-1, 0], [-10, 2], [-1, -2]], [[3761, 6133], [-1, -4], [1, -10], [2, -4], [2, -3], [2, 1], [1, 15], [-7, 7], [0, -2]], [[3821, 6358], [-6, 4], [-4, 1], [-3, -2], [-12, -10], [-4, -4], [-1, -4], [1, -3], [1, -4], [2, -2], [1, 0], [1, 2], [-1, 4], [0, 3], [2, 2], [2, 0], [4, -2], [6, -7], [4, -1], [2, -1], [4, 3], [5, 6], [3, 4], [-5, 9], [-2, 2]], [[3843, 6291], [-12, 8], [-16, 9], [-4, 1], [-5, -10], [8, -7], [8, -9], [12, -8], [17, -20], [5, -1], [1, 0], [7, 6], [1, 3], [-15, 23], [-7, 5]], [[4005, 6071], [-4, 8], [-1, 3], [-3, 1], [-7, -5], [-1, -3], [-1, -2], [5, -13], [3, -5], [5, -2], [3, 0], [1, 2], [1, 4], [0, 9], [-1, 3]], [[3977, 6182], [3, 22], [1, 5], [-9, 0], [-6, -10], [0, -16], [1, -7], [1, -3], [-1, -4], [-2, -6], [6, 1], [2, 3], [2, 4], [2, 11]], [[3886, 6256], [-2, 1], [-1, -1], [1, -6], [2, -4], [5, -9], [3, -3], [3, -1], [2, 1], [-3, 7], [-8, 14], [-2, 1]], [[3910, 6249], [-1, 16], [-1, 4], [-4, 4], [-1, -1], [0, -4], [-4, -8], [-2, -5], [1, -4], [1, -2], [2, -1], [8, 0], [1, 1]], [[3131, 8187], [3, -2], [10, -5], [6, 5], [13, 1], [-4, 3], [-11, 4], [-5, 0], [-12, -6]], [[9153, 1043], [6, 4], [5, 6], [1, 5], [-4, 4], [0, 3], [-2, 2], [-2, -2], [-8, -9], [-1, -3], [0, -12], [5, 2]], [[7759, 971], [1, -5], [3, 5], [-1, 5], [-1, 0], [-2, -5]], [[7769, 152], [2, -6], [4, 1], [1, 13], [-2, -1], [-5, -7]], [[7832, 311], [-3, -4], [2, -6], [4, -5], [2, 3], [-1, 7], [-1, 4], [-3, 1]], [[9169, 1080], [4, 3], [-2, 1], [-1, 0], [-3, -1], [-2, -2], [-5, -1], [-2, -6], [-1, -3], [10, 4], [2, 5]], [[9989, 27], [-11, 0], [-6, -11], [1, -11], [4, -2], [13, 1], [5, -4]], [[9999, 31], [-10, -4]], [[9961, 250], [-1, -11], [4, -14], [4, -2], [8, 5], [1, 5], [-4, 19], [-2, 1], [-4, 1], [-6, -4]], [[9973, 45], [-1, 23], [-2, 2], [-2, -2], [-2, -2], [0, -16], [-2, -12], [-5, -9], [-7, -5], [-1, -18], [12, -6]], [[9965, 0], [1, 16], [7, 14], [0, 15]], [[9979, 42], [8, 4], [9, 16], [0, 8], [-2, 5], [-3, -1], [-1, -3], [-10, -2], [-3, -8], [0, -15], [2, -4]], [[7560, 881], [3, 4], [-4, 12], [-2, 0], [-5, -4], [-3, -10], [0, -3], [0, -3], [6, -10], [9, -5], [4, 1], [-1, 5], [-6, 5], [-1, 8]], [[6826, 0], [-1, 3], [-3, 8], [-4, 4], [-10, -3], [-2, -5], [-6, -7]], [[5651, 2547], [-1, 3], [-4, 4], [-3, -2], [0, -10], [2, -6], [6, -3], [2, 9], [-2, 5]], [[5308, 2219], [-4, -4], [-2, -6], [7, -7], [5, 3], [-2, 7], [-4, 7]], [[5330, 2946], [4, -9], [5, -7], [4, -2], [-3, 13], [-2, 5], [-7, 5], [-1, -1], [0, -4]], [[5393, 2864], [-1, -2], [1, -15], [4, -6], [2, 0], [1, 1], [0, 3], [-7, 19]], [[5138, 3447], [-13, 4], [-1, 10], [-3, 8], [-7, 8], [-6, 2], [0, -13], [1, -6], [6, -12], [18, -25], [8, -7], [0, 2], [-1, 9], [-2, 20]], [[5016, 3735], [4, -6], [3, 1], [1, 2], [-2, 4], [-5, 4], [-1, 0], [0, -5]], [[4982, 3337], [-4, -3], [1, -14], [-7, -14], [13, -8], [1, 4], [2, 12], [-4, 18], [-2, 5]], [[5241, 2887], [-6, 1], [-6, -2], [23, -22], [1, 6], [-12, 17]], [[5300, 3067], [-4, 0], [-3, -4], [-3, -24], [1, 2], [6, 17], [3, -3], [0, 2], [-1, 4], [1, 6]], [[5215, 3412], [-4, 14], [-1, 10], [-2, 4], [-10, -5], [-4, -4], [-5, -32], [2, -6], [12, -8], [6, -1], [6, 28]], [[5218, 2917], [-3, 5], [0, 5], [5, 40], [-2, 7], [-5, -39], [-6, -19], [7, -3], [3, -4], [1, -9], [6, -14], [2, 1], [-1, 5], [-4, 8], [-1, 9], [-2, 8]], [[9028, 7766], [10, -5], [4, -3], [2, -5], [2, -5], [1, -4], [-8, -3], [-10, -10], [-4, -9], [-8, -1], [-4, 1], [-12, -2], [-15, 4], [-5, 4], [-17, 19], [-1, -2], [5, -10], [-1, -5], [-18, -4], [0, -2], [11, -3], [13, -2], [-2, -9], [0, -37], [-3, -13], [-6, -11], [-10, -11], [-10, 7], [-4, 7], [-3, 4], [-5, 3], [-6, 2], [-7, 0], [-6, -7], [-8, 6], [-7, 7], [2, 17], [3, 9], [-1, 0], [-4, -4], [-10, -13], [-7, -16], [-8, 6], [-8, 8], [-6, 8], [-10, 8], [-10, 11], [-5, 12], [-3, 3], [-5, 10], [-3, 3], [-2, 1], [-5, 6], [2, 7], [8, 8], [7, 6], [12, 6], [14, 3], [6, 7], [7, 14], [9, 9], [9, 5], [-5, 1], [-11, -4], [-8, -7], [-10, -11], [-9, -7], [-23, -8], [-9, -2], [-10, -1], [-21, 2], [-5, 2], [2, 8], [16, 14], [-3, 1], [-5, -5], [-8, -3], [-6, -2], [-10, 1], [-11, 8], [-5, 3], [-11, 3], [-4, 3], [-18, 21], [-4, 6], [-2, 5], [-6, 5], [-10, 4], [-2, -1], [4, -5], [0, -4], [-8, -2], [-9, 1], [-9, 4], [-1, -6], [10, -10], [0, -13], [-3, -2], [-6, 0], [-5, 1], [-15, 10], [-14, 7], [-9, 4], [-2, -3], [7, -12], [7, -11], [12, -10], [20, -11], [8, -7], [-7, -9], [-6, -3], [-3, -1], [-12, 0], [-21, 5], [-10, 6], [-15, 13], [-24, 14], [-5, 0], [-17, -6], [3, 0], [11, -1], [8, -2], [19, -11], [1, -4], [-5, -6], [1, -6], [5, -8], [6, -5], [11, -4], [6, 0], [2, -3], [-7, -17], [-1, -5], [2, -2], [3, 0], [14, 7], [6, 2], [6, 0], [6, -2], [7, -4], [4, -5], [1, -5], [2, -3], [14, -5], [-1, -2], [-15, -8], [0, -1], [2, 0], [10, -5], [8, -7], [6, -8], [1, -4], [0, -4], [1, -3], [4, 0], [2, 1], [2, 0], [2, -2], [3, -6], [5, -19], [3, -5], [1, -1], [1, 19], [2, 3], [10, -3], [13, -7], [9, -7], [1, -3], [-7, -6], [2, -2], [5, -4], [5, 1], [3, 7], [6, 6], [7, 5], [13, -4], [11, -10], [2, -3], [7, -4], [6, 2], [12, -11], [-6, -5], [-12, -7], [-1, -3], [3, 1], [24, 0], [6, -3], [2, -6], [-11, -16], [-10, 2], [-13, 0], [-6, -1], [1, -2], [18, -7], [5, -6], [6, -6], [4, -5], [0, -2], [-3, -4], [1, -1], [12, -2], [8, 2], [10, 1], [8, -1], [1, -2], [-2, -6], [-8, -5], [2, -1], [10, 1], [5, -2], [6, -13], [7, -10], [-6, -2], [-6, -1], [1, -13], [4, -13], [0, -12], [-1, -11], [-6, -2], [-6, 0], [-2, 3], [-15, 33], [-4, 6], [-4, 5], [-15, 14], [0, -2], [4, -7], [3, -10], [5, -19], [2, -13], [-1, -5], [-3, -1], [-1, -2], [1, -3], [12, -13], [6, -8], [4, -8], [4, -5], [3, -3], [0, -2], [-6, -2], [-9, -1], [-4, 1], [-16, 7], [-2, -2], [9, -27], [-1, -7], [-4, -2], [-6, 3], [-6, 8], [-10, 8], [-14, 10], [-13, 7], [-3, 0], [-2, -2], [-2, -1], [-2, 2], [-5, 5], [-4, 4], [-19, 13], [-2, 0], [2, -4], [2, -8], [-2, -2], [-5, 1], [-10, 3], [-6, 8], [-8, 15], [-4, 5], [0, -3], [2, -14], [-1, -5], [-4, -1], [-2, 1], [-2, 4], [-2, 6], [-5, 5], [-7, 3], [-4, 3], [-2, 6], [-1, 1], [-12, -1], [-7, 4], [-18, 17], [-16, 18], [-10, 9], [-4, 2], [5, -11], [6, -17], [2, -8], [-3, -1], [-6, 4], [-31, 22], [-19, 10], [-11, 2], [-18, 1], [-4, -5], [10, -13], [9, -10], [9, -6], [14, -13], [13, -16], [5, -5], [17, -7], [9, -1], [9, -1], [1, -2], [-4, -5], [-1, -3], [20, -7], [8, -4], [8, -7], [4, -1], [18, -17], [4, -3], [16, -5], [6, -4], [9, -11], [5, -5], [8, -13], [6, -6], [14, -7], [6, -2], [3, -2], [-2, -6], [-2, -2], [-8, -5], [1, -5], [5, -10], [0, -7], [-5, -2], [-10, -3], [-6, 0], [-7, 3], [-10, 4], [-19, 10], [-29, 7], [-11, 4], [-4, 3], [-5, 2], [-73, 10], [-12, 2], [-7, 3], [-7, 5], [-28, 10], [-3, 2], [-19, 18], [-13, 20], [-5, 3], [-15, 2], [-12, -1], [-9, -3], [-12, 1], [-9, 3], [-17, 10], [-18, 4], [-15, 8], [-8, 3], [0, 2], [12, 12], [-4, 0], [-20, -9], [-7, 3], [-12, 7], [-9, 7], [-18, 20], [-11, 7], [2, 2], [12, 0], [9, 0], [7, 1], [12, 8], [5, 5], [0, 3], [-10, 1], [-2, 1], [-2, 4], [-4, 4], [-8, 5], [-9, 2], [-29, -2], [-5, 3], [0, 3], [5, 10], [4, 4], [1, 2], [-2, 0], [-4, 0], [-17, -9], [-3, 1], [-7, 9], [-4, 10], [-3, 4], [-4, 1], [-14, 10], [-20, 20], [-8, 6], [-8, 5], [-6, 3], [1, 2], [13, 17], [0, 2], [-11, -1], [-16, 4], [-8, -4], [-5, -1], [-6, 3], [-3, -1], [-3, -14], [-3, -3], [-3, -2], [-3, 1], [-3, 2], [1, 3], [-3, 16], [-6, 3], [-16, 0], [-3, 1], [-4, 3], [-4, 6], [-3, 8], [-3, 5], [-3, 0], [-3, 0], [-2, -2], [-6, -2], [-7, 0], [-1, -3], [8, -6], [7, -8], [7, -11], [-4, -7], [-16, -4], [-13, -1], [-11, 2], [-9, 2], [-12, 6], [-18, -2], [-4, -15], [-4, -1], [-16, 0], [-7, -1], [-22, -9], [-7, -1], [-5, 1], [-5, -2], [-7, -5], [-11, 0], [-13, 4], [-10, 1], [-9, 0], [-10, 2], [-9, 5], [-8, 2], [-10, 0], [-3, 1], [-14, 11], [-5, 4], [-10, 14], [-2, 6], [0, 6], [1, 4], [3, 7], [4, 15], [3, 5], [5, 5], [9, 6], [33, 10], [7, 4], [-1, 3], [-7, 13], [0, 3], [2, 2], [6, 8], [2, 2], [6, 1], [12, -4], [10, -2], [14, 0], [23, -5], [32, -10], [18, -7], [14, -10], [10, -10], [2, -5], [-5, -8], [-2, -2], [0, -3], [2, -3], [8, -4], [2, 1], [0, 6], [1, 4], [4, 4], [1, 4], [-3, 6], [-4, 5], [-5, 4], [-20, 15], [-2, 4], [7, 2], [30, -4], [11, 1], [5, 5], [4, 4], [6, 2], [10, 1], [14, -3], [7, 0], [6, 1], [8, 3], [12, 10], [7, 3], [12, 1], [8, 0], [16, -4], [10, 0], [-1, 7], [-7, 13], [-7, 13], [-7, 5], [-16, 8], [-18, 16], [-10, 10], [-2, 5], [1, 4], [3, 4], [34, 18], [27, 18], [12, 9], [5, 6], [6, 4], [6, 3], [13, 4], [4, 4], [1, 7], [2, 7], [12, 17], [10, 5], [14, 3], [9, 4], [11, 14], [-1, 4], [-5, 3], [-4, 4], [-17, 37], [-12, 18], [-14, 16], [-12, 19], [-20, 19], [-1, 5], [4, 5], [-2, 1], [-21, -8], [-5, 0], [-8, 3], [-5, 5], [-5, 8], [1, 4], [3, 4], [4, 9], [0, 5], [-1, 5], [-2, 3], [-3, 2], [-6, 1], [-11, 0], [-3, -1], [12, -15], [-2, -3], [-15, -2], [-7, 1], [-6, 2], [-5, 2], [-18, 15], [-3, 6], [1, 4], [-2, 2], [-3, -1], [-5, 0], [-7, 1], [-1, 2], [12, 8], [1, 3], [-6, 2], [-8, 1], [-2, 2], [2, 3], [12, 4], [4, 3], [-7, 2], [-4, 1], [-7, -5], [-12, -10], [-8, -4], [-12, 5], [-7, 1], [-5, -1], [-8, -7], [-17, -6], [-30, -13], [-13, -4], [-14, 0], [-2, 3], [0, 5], [1, 3], [1, 3], [1, 4], [-1, 15], [2, 4], [5, 2], [9, 3], [22, -3], [11, 0], [7, 4], [7, 5], [8, 7], [1, 6], [-7, 10], [-3, 2], [-20, 8], [-11, 3], [-10, 1], [-7, 3], [-5, 3], [-4, 6], [0, 3], [1, 5], [4, 4], [17, 4], [0, 1], [-14, 3], [-7, -1], [-6, -3], [-8, -8], [-4, -2], [-13, 5], [-8, 0], [-6, 3], [-3, 2], [2, 2], [7, 2], [11, 6], [1, 4], [-8, 6], [-4, 1], [-17, 2], [-20, -2], [-7, 1], [-4, 7], [-2, 7], [-1, 9], [-3, 16], [-4, 8], [-6, 1], [-24, -4], [-5, 0], [-4, 1], [-16, 11], [-7, 3], [-3, 1], [-12, 11], [-5, 2], [-5, 5], [-6, 9], [-7, 2], [-7, -3], [-7, -5], [-7, -6], [-4, -5], [-1, -4], [5, -4], [25, -5], [7, -4], [5, -6], [4, -8], [3, -8], [0, -7], [-4, -4], [-5, -4], [-16, -6], [-16, -4], [-16, -1], [-8, 1], [-42, 12], [-7, 0], [-10, 2], [-22, 5], [-12, 0], [-21, 4], [-35, 3], [-8, -2], [10, -6], [8, -3], [7, 0], [10, -5], [14, -9], [7, -6], [7, -7], [0, -2], [-6, -5], [-50, 25], [-30, -9], [-14, -3], [-12, -1], [-15, 4], [-33, 12], [-13, 4], [-5, 1], [-29, -6], [-25, 0], [-51, 5], [-19, 3], [-5, 4], [-6, 2], [-11, 0], [-29, 4], [-27, -9], [-32, 8], [-10, 5], [-3, 3], [-9, 13], [-1, 8], [2, 6], [3, 5], [3, 2], [-18, -7], [-6, -2], [-8, 0], [-24, 3], [-4, -1], [1, -3], [6, -4], [1, -2], [-13, -2], [-21, 2], [-8, -1], [-4, -1], [-10, -6], [-3, -2], [-5, 1], [-22, 14], [-17, 9], [-20, 3], [-9, 3], [-5, 3], [-28, 28], [-4, 6], [-8, 22], [-3, 4], [-4, 3], [7, 1], [26, -3], [26, 0], [13, -1], [16, -6], [21, -4], [15, 0], [24, 1], [27, 4], [3, 2], [-17, 5], [-16, 7], [-14, 8], [-9, 4], [-14, 2], [-41, 1], [-38, 6], [-26, 8], [-21, 8], [-9, 4], [-3, 4], [-3, 11], [-4, 18], [-3, 13], [-3, 6], [-1, 6], [8, 12], [20, 12], [1, 2], [-4, 1], [-9, 3], [-3, 5], [-1, 8], [0, 6], [1, 5], [3, 7], [9, 12], [13, 14], [14, 13], [2, 5], [1, 12], [2, 8], [2, 6], [3, 5], [8, 9], [11, 8], [17, 7], [1, 3], [0, 3], [1, 3], [2, 2], [41, 22], [19, 10], [16, 5], [19, 5], [54, 8], [28, 3], [35, -1], [64, -5], [8, -3], [2, -2], [3, -5], [-2, -3], [-18, -11], [-22, -9], [-14, -7], [-25, -18], [-7, -6], [-30, -35], [-7, -6], [-4, -5], [-3, -13], [1, -4], [4, -7], [17, -17], [4, -7], [0, -7], [-2, -16], [0, -9], [1, -8], [3, -11], [6, -15], [14, -15], [22, -15], [17, -11], [16, -7], [19, -11], [4, -5], [-9, -6], [-20, -9], [-27, -4], [-15, -4], [-18, -8], [-22, -6], [-9, -4], [3, -2], [17, 3], [15, 4], [24, 9], [13, 3], [43, 0], [7, -2], [-3, -5], [-2, -2], [1, -2], [5, -3], [9, -4], [4, 3], [2, 8], [6, 31], [3, 10], [1, 9], [0, 8], [-3, 5], [-11, 4], [-15, -1], [-7, 1], [-9, 2], [-7, 2], [-5, 4], [-8, 10], [-7, 6], [-17, 11], [-7, 3], [4, 4], [15, 5], [9, 5], [11, 13], [6, 2], [24, -2], [31, -10], [20, -9], [5, -1], [0, 2], [-5, 4], [-23, 11], [-10, 8], [-5, 6], [2, 3], [13, 2], [2, 3], [-17, 4], [-9, -1], [-7, -2], [-8, 0], [-14, 4], [-4, 3], [-8, 8], [-5, 7], [-4, 4], [-2, 3], [-1, 11], [0, 6], [2, 5], [4, 4], [9, 9], [5, 2], [10, 1], [21, -4], [56, -15], [-1, 5], [-63, 20], [-23, 5], [-5, 7], [34, 27], [30, 6], [16, 8], [25, 0], [24, -5], [0, 2], [-11, 9], [1, 3], [14, 5], [24, 7], [30, 5], [6, 3], [8, 2], [14, 1], [35, 1], [20, -1], [26, -4], [15, -7], [5, -4], [8, -14], [7, -20], [10, -8], [15, -4], [11, -5], [6, -5], [2, -7], [-3, -8], [2, -8], [7, -8], [5, -5], [12, -6], [0, -3], [-3, -3], [-8, -4], [-19, -15], [-25, -15], [-18, -14], [-1, -4], [37, 22], [12, -1], [0, -3], [-7, -10], [-10, -10], [-9, -6], [2, -2], [17, -10], [-3, -2], [-8, 1], [-4, -1], [-2, -2], [-2, -3], [0, -4], [1, -5], [0, -4], [-1, -2], [1, -1], [6, 0], [4, 2], [7, 7], [25, 19], [16, 7], [5, 1], [14, -5], [3, 1], [-15, 14], [-2, 4], [3, 5], [2, 2], [9, 4], [8, 2], [4, -1], [6, -7], [3, -5], [6, -2], [12, 2], [8, 7], [10, -4], [14, -10], [-1, -10], [0, -10], [1, -7], [18, -13], [12, -6], [2, 0], [0, 2], [-3, 4], [-6, 4], [-6, 7], [-6, 9], [3, 19], [10, 10], [9, -3], [11, -5], [10, -1], [14, 1], [30, -12], [17, 0], [-2, 4], [-12, 3], [-18, 6], [-28, 8], [-12, 9], [-3, 4], [0, 5], [2, 4], [3, 3], [5, 3], [27, 11], [19, 4], [15, 1], [24, 0], [27, -1], [16, -3], [17, -8], [22, -7], [7, -1], [10, 0], [10, 2], [10, -1], [31, -11], [9, -5], [5, -7], [4, -6], [2, -7], [-1, -5], [-26, -22], [-12, -3], [-7, -9], [-11, -15], [-10, -9], [-1, -2], [2, 0], [6, 4], [10, 11], [7, 9], [13, 8], [22, 9], [19, 5], [16, -1], [13, -1], [11, -2], [6, -2], [3, -2], [4, -7], [0, -4], [-3, -6], [-5, -6], [-24, -6], [-13, -6], [-8, -2], [-24, -1], [1, -3], [18, -2], [20, 1], [0, -4], [-10, -9], [-3, -8], [3, -6], [-1, -5], [-7, -11], [-8, -10], [3, -1], [19, 14], [5, 15], [7, 14], [8, 7], [7, 3], [20, 1], [12, 8], [9, 3], [4, 0], [9, -3], [-1, -3], [-12, -14], [-25, -23], [10, 2], [7, 6], [10, 5], [10, 8], [7, -7], [11, -6], [6, -12], [11, -6], [6, -5], [-1, 8], [-9, 16], [3, 6], [7, 4], [21, 13], [16, -5], [9, -4], [5, 2], [12, -1], [19, -2], [18, -4], [18, -5], [14, -6], [10, -7], [6, -5], [2, -3], [3, -7], [-2, -5], [-14, -11], [-7, -5], [-8, -2], [-20, 2], [-6, -1], [-7, -3], [-21, -15], [-12, -7], [-11, -4], [-3, -2], [25, 0], [7, 5], [5, 8], [11, 9], [21, 4], [28, -9], [14, 1], [11, 8], [12, 6], [5, 1], [3, -1], [9, -6], [3, -5], [0, -12], [-2, -4], [-8, -9], [-20, -14], [-13, -5], [-14, -3], [-16, -5], [-6, -4], [-5, -5], [-6, -4], [-7, -3], [9, -4], [4, 0], [3, 3], [9, 10], [7, 4], [4, 1], [4, 0], [4, -2], [4, -4], [-1, -9], [-11, -34], [2, 0], [7, 9], [20, 37], [5, 7], [10, 7], [23, 11], [17, 6], [19, 5], [11, 2], [11, -1], [8, -6], [11, -1], [12, 1], [8, 0], [10, -3], [8, -4], [13, -5], [30, -9], [4, -2], [4, -3], [3, -5], [0, -5], [-5, -5], [-5, -3], [-6, -1], [-6, -2], [-12, -7], [-3, -2], [-19, -3], [-16, -1], [-11, -3], [-20, -7], [-28, -14], [0, -4], [11, -1], [9, 2], [13, 10], [11, 3], [19, 3], [25, 3], [11, -1], [2, 0], [1, -2], [1, -4], [-4, -6], [-5, -2], [-13, -12], [9, -3], [11, -1], [7, 3], [6, 7], [7, 4], [7, 1], [7, 2], [6, 3], [1, 2], [-8, 3], [-1, 3], [4, 5], [6, 7], [6, 3], [5, 1], [16, -4], [10, -8], [27, -21], [4, -5], [9, -16], [2, -7], [-1, -5], [-3, -3], [-3, -1], [-5, 0], [-37, 6], [-16, -1], [-7, -1], [-6, -3], [-5, -4], [-3, -4], [-6, -2], [-23, 0], [-13, -3], [-22, -6], [-8, -3], [-2, -4], [14, 1], [22, 5], [21, 2], [35, -12], [12, -2], [6, 2], [8, 0], [28, -1], [9, -1], [14, -5], [22, -10], [4, -2], [3, -4], [0, -3], [0, -8], [-2, -2], [-7, -2], [-32, 2], [-9, 2], [-12, -3], [-9, 1], [-12, 3], [-14, 6], [-20, -5], [-16, 3], [-16, -3], [-33, -13], [4, -2], [45, 11], [8, -1], [14, -4], [22, -7], [7, -4], [0, -12], [-4, -8], [-6, -10], [-11, 2], [-23, 5], [-10, 1], [-7, -1], [-10, -3], [-4, 0], [-38, 7], [-9, 0], [-1, -1], [2, -1], [34, -11], [26, -2], [16, -2], [9, -3], [5, -3], [0, -7], [9, -8], [7, -3], [5, 0], [9, 3], [8, 0], [7, -2], [8, -4], [11, -1], [9, -3], [7, -1], [20, 2], [8, -2], [2, -1], [-3, -3], [-18, -6], [-3, -5], [10, -8], [5, -5], [0, -5], [-5, -9], [-2, -4], [2, 0], [13, 7], [2, -1], [2, -10], [1, 0], [5, 9], [-2, 12], [8, 5], [24, 3], [-4, -18], [-1, -10], [-10, -16], [-9, -5], [0, -1], [6, -2], [4, -1], [4, 3], [9, 12], [18, 13], [4, 1], [0, -5], [-3, -9], [9, -4], [8, 4], [4, 4], [10, -1], [5, -2], [1, -3], [-4, -16], [1, -4], [10, -11], [1, 1], [-2, 5], [-2, 12], [2, 6], [9, 7], [18, 10], [7, 2], [4, -1], [7, -5], [-2, -3], [-7, -3], [-6, -5], [-3, -9], [4, -4], [14, 0], [15, 6], [9, -3], [10, -8], [18, -14], [11, 4], [13, -11], [-18, -8], [6, -18], [-23, 1], [-13, -1], [-8, 1], [-10, 0], [9, -4], [16, -2], [2, -5], [13, 0], [9, 1], [18, 0], [1, 6], [11, 3], [7, 4], [5, -2], [16, -3], [21, -12], [-9, -7], [-3, -7], [-3, -5], [-2, -6], [-3, -3], [-31, -20], [5, -1], [13, 5], [25, 8], [14, 3], [10, -2], [5, 0], [4, 2], [9, -3], [17, -3], [20, 17], [11, -3], [12, -10], [23, -18], [13, -11], [4, -5], [-1, -4], [-11, -5], [-6, -1], [-15, 9], [-14, 5], [-8, -1], [-8, -3], [2, -2], [34, -15], [6, -11], [0, -4], [-22, -7], [-8, -1], [-15, 4], [-9, 6], [-8, 2], [-10, 1], [-4, -1], [12, -11], [-1, -3], [-6, -2], [-3, -5], [22, -10], [17, -9], [3, -4], [-12, -3], [-8, 0], [-17, 1], [-9, 2], [-3, -2]], [[8559, 9559], [3, 4], [136, 23], [4, 1], [5, 3], [3, 5], [10, 6], [48, 2], [23, 1], [16, 3], [8, 6], [13, 3], [50, -1], [11, 1], [23, 6], [42, 6], [84, 19], [24, 4], [16, 8], [28, 9], [26, 11], [5, 7], [-7, 7], [-7, 5], [-7, 3], [-17, 3], [-60, 3], [-59, -8], [0, 2], [12, 8], [1, 4], [-24, 10], [-15, 5], [-4, 2], [1, 2], [5, 2], [7, 3], [-2, 2], [-11, 3], [-27, 2], [-11, -1], [-23, -5], [-6, 0], [-10, 4], [-11, 7], [-9, 3], [-6, 0], [-10, -1], [-4, -3], [-7, -6], [-4, -2], [-7, 0], [-12, 3], [-14, 2], [-30, -4], [-38, -8], [-19, -2], [-10, -1], [-31, -5], [-26, -2], [-34, -1], [-23, 1], [9, 3], [117, 16], [18, 5], [14, 6], [0, 2], [-13, 2], [-63, 1], [-17, 2], [-23, -1], [-14, 0], [-23, 5], [-20, 0], [-63, 2], [-6, 1], [-17, 8], [-6, 2], [-8, 0], [-69, -2], [-17, -2], [-26, -7], [2, -2], [16, -4], [20, -7], [-1, -1], [-14, 3], [-21, 6], [-45, 15], [-6, 0], [-58, -3], [-40, -9], [-5, -3], [-3, -4], [0, -4], [16, -8], [35, -10], [9, -4], [-47, 6], [-33, 9], [-17, 6], [-10, 6], [-11, 4], [-17, 3], [-102, 4], [-106, -4], [6, -5], [10, -6], [39, -12], [17, -7], [8, -4], [34, -9], [6, -4], [-14, -5], [-21, -4], [-10, 2], [-8, 6], [-7, 5], [-6, 3], [-62, 20], [-20, 5], [-10, 1], [-27, 2], [-43, -2], [-50, 5], [-55, 0], [-20, -3], [1, -2], [13, -4], [10, -5], [15, -4], [34, -6], [31, -4], [4, -2], [-22, -1], [-80, 4], [-45, 8], [-27, 1], [-9, -2], [-4, -2], [2, -4], [26, -15], [-4, -2], [-25, 3], [-30, 6], [-16, 0], [-25, -2], [-1, -3], [13, -5], [21, -5], [-3, -1], [-23, -2], [-19, -3], [-14, -5], [0, -3], [15, -7], [20, -7], [41, -10], [103, -20], [38, -11], [16, -9], [-3, 0], [-17, 4], [-17, 6], [-19, 4], [-31, 4], [-79, 13], [-54, 12], [-20, 3], [-7, 0], [-6, -2], [-3, -4], [7, -4], [26, -8], [-3, -3], [-21, 1], [-11, 2], [-18, 6], [-13, 5], [-32, 16], [-18, 3], [-41, 3], [-15, 3], [-14, 5], [-12, 1], [-29, -5], [-16, -5], [-24, -9], [-10, -1], [-20, -4], [-33, -3], [2, -4], [98, -17], [11, -2], [9, -4], [1, -1], [-28, 0], [-10, -3], [-9, 0], [-17, 2], [-22, 6], [-17, 2], [-19, 1], [-16, -2], [-13, -5], [-1, -4], [-16, 5], [-14, 6], [-18, 4], [-33, 1], [-38, -4], [-24, -5], [-22, -7], [-17, -5], [-19, -2], [-41, 0], [-25, -2], [-35, -6], [-21, -5], [-16, -5], [-17, -7], [-3, -6], [21, -7], [9, 0], [15, 2], [20, 6], [16, 2], [11, -2], [12, -1], [39, 0], [-2, -3], [-54, -17], [-4, -4], [14, -3], [9, 0], [102, 16], [38, 3], [29, -1], [39, -4], [-1, -2], [-40, 1], [-38, -2], [-20, -3], [-42, -10], [-19, -7], [3, -3], [33, -6], [-4, -1], [-23, -2], [-30, -6], [-2, -6], [14, -7], [13, -4], [18, -1], [39, 4], [124, 3], [50, 5], [58, 14], [36, 5], [15, 1], [20, -1], [-10, -4], [-56, -15], [-52, -10], [-34, -4], [-36, -2], [-79, 1], [-58, -6], [-7, -4], [4, -3], [5, -4], [7, -3], [10, -2], [23, -5], [30, -7], [18, -4], [22, -2], [30, 2], [19, 6], [65, 27], [21, 7], [14, 3], [88, 7], [104, 6], [-5, -5], [-102, -8], [-44, -6], [-30, -7], [-47, -16], [-15, -8], [-12, -7], [-1, -4], [6, -3], [22, -5], [12, 0], [28, 2], [32, -6], [13, -1], [56, 1], [15, 1], [11, 2], [15, 6], [18, 8], [19, 5], [48, 2], [21, 4], [22, 1], [-9, -5], [-33, -12], [-9, -6], [21, -2], [18, 0], [63, 7], [19, 1], [23, 3], [67, 13], [29, 9], [12, 5], [5, 3], [5, 6], [6, 9], [7, 6], [8, 3], [10, 1], [11, -1], [15, 4], [29, 13], [29, 8], [84, 12], [-11, -5], [-39, -8], [-58, -18], [-13, -6], [-13, -8], [-4, -5], [-3, -6], [13, -4], [42, -2], [47, 0], [21, -1], [21, -2], [-1, -1], [-24, -3], [-26, -1], [-67, -6], [-96, -16], [-3, -3], [-29, -11], [-72, -10], [-120, -8], [-19, -3], [-15, -3], [2, -5], [21, -13], [27, -12], [36, -13], [17, -8], [22, -12], [14, -7], [13, -4], [23, -2], [32, 0], [13, -1], [-11, -4], [-16, -3], [-15, -1], [-28, 1], [-33, 5], [-17, 4], [-13, 4], [-15, 7], [-25, 15], [-23, 10], [-26, 7], [-26, 11], [-29, 10], [-25, 4], [-48, 2], [-37, -1], [-89, 5], [-14, -7], [-9, -16], [9, -12], [6, -20], [21, -12], [9, -3], [44, -4], [14, -3], [14, -6], [20, -14], [24, -14], [11, -8], [14, -9], [17, -8], [24, -8], [6, -4], [-19, 0], [-17, 5], [-16, 3], [-10, -1], [-11, -2], [-3, -3], [6, -5], [6, -4], [8, -3], [13, -2], [28, -1], [55, -1], [32, -4], [16, 0], [15, 2], [17, 5], [10, 1], [11, 0], [-2, -3], [-16, -6], [-13, -4], [-11, -2], [-11, -1], [-42, 1], [-13, -4], [-9, -5], [-9, 1], [-12, 3], [-28, 4], [-68, 6], [-16, 3], [-18, -1], [-35, -7], [-43, -3], [-43, -5], [-11, -13], [-17, -13], [-15, -9], [-10, -7], [0, -16], [-4, -13], [16, -5], [33, -1], [17, 3], [20, 6], [28, 10], [11, 7], [-11, -18], [-12, -13], [49, 3], [13, 4], [11, 7], [19, 13], [19, 26], [-10, -34], [28, 1], [12, -5], [13, -4], [-23, 3], [-7, 0], [-32, -16], [-18, -6], [-22, -10], [19, -19], [1, -24], [15, -5], [18, -2], [29, 7], [24, -5], [18, 0], [12, 2], [27, 10], [9, 6], [47, 34], [8, 4], [-2, -7], [-3, -6], [-4, -4], [-15, -14], [-26, -17], [-18, -9], [-5, -3], [-5, -5], [-9, -3], [-19, -3], [-40, -3], [-19, -1], [-16, 2], [-49, 10], [-11, 6], [-14, 12], [-20, 16], [-17, 7], [-28, 7], [-20, 3], [-17, 0], [-20, -3], [-20, -4], [-20, -6], [-6, -7], [12, -15], [12, -12], [8, -7], [7, -5], [12, -5], [13, -1], [6, -1], [6, -3], [18, -13], [5, -1], [-4, -2], [-16, -1], [-23, -3], [-19, -1], [-17, 1], [-24, -1], [-19, -3], [-13, -4], [-16, -9], [-56, -19], [-4, -20], [-2, -20], [16, -2], [43, -2], [20, -4], [-1, 15], [-4, 12], [9, 14], [1, -22], [7, -21], [22, 1], [47, -3], [0, 23], [11, -16], [29, -4], [23, -4], [9, 16], [8, 8], [3, -1], [4, -3], [5, -7], [14, -7], [34, -10], [26, -4], [15, -1], [67, 6], [4, 38], [18, -21], [8, -5], [38, -2], [89, 3], [2, 6], [-4, 7], [-8, 7], [-10, 7], [-3, 3], [10, -4], [7, -4], [7, -1], [8, 0], [6, -2], [16, -12], [8, -3], [10, -2], [9, 1], [9, 2], [14, 1], [8, -2], [8, -3], [11, -8], [-1, -4], [-5, -6], [-4, -6], [-3, -6], [2, -4], [13, -1], [8, 0], [39, 8], [18, 1], [34, 7], [17, 5], [12, 6], [16, 5], [50, 14], [12, 9], [11, 13], [-2, 12], [-12, 9], [-10, 5], [-6, 1], [-7, -2], [-15, -7], [-11, -3], [-14, 1], [-19, 5], [-7, 6], [3, 5], [0, 7], [-5, 9], [-12, 4], [-33, 0], [-22, -6], [-5, 1], [-30, 11], [-10, 1], [-24, 2], [-13, -2], [-19, -5], [-18, -1], [-6, 1], [-10, 4], [-7, 4], [0, 2], [23, 0], [18, 1], [17, 4], [2, 3], [-7, 2], [-10, 5], [-12, 9], [0, 3], [11, -2], [11, -3], [38, -15], [23, -5], [23, -2], [29, 0], [59, 4], [20, 0], [13, 1], [16, 3], [17, 6], [8, 5], [8, 7], [2, 11], [-3, 16], [0, 11], [2, 8], [3, 4], [43, 0], [37, -2], [21, 1], [27, 6], [21, 0], [9, 1], [8, 2], [24, 25], [27, 12], [-3, 3], [-19, 6], [-50, 10], [-22, 3], [3, 1], [32, 1], [44, -1], [39, 3], [26, 9], [1, 4], [8, 8], [-4, 3], [-11, 1], [-36, 12], [-24, 2], [-30, 1], [-12, 8], [-10, 3], [-13, 3], [-21, 2], [-23, -1], [-53, -4], [-14, -3], [-15, -2], [-11, 3], [-15, 6], [-15, 4], [-11, 1], [-1, 2], [25, 1], [21, -1], [19, -2], [26, 0], [48, 3], [18, 0], [12, 2], [17, 0], [19, 2], [21, -3], [10, -4], [21, -3], [46, 0], [8, 2], [4, 21], [-19, 1], [-28, -4], [-20, 3], [-19, 1], [-27, 9], [-13, 2], [-14, 10], [-28, 7], [-15, 2], [-3, 2], [40, -2], [24, -2], [23, -5], [20, -2], [19, 1], [36, 4], [30, 0], [17, 1], [13, 3], [11, 1], [31, -1], [8, 1], [5, 3], [4, 3], [3, 5], [-2, 6], [-13, 10], [-5, 3], [-13, 2], [-32, 1], [-38, 4], [-9, 2], [21, 5], [19, 1], [26, -4], [27, -3], [78, -15], [17, -1], [20, 2], [44, 7], [7, 2], [14, 8], [1, 3], [-14, 4], [-6, 0], [46, 11], [16, 8], [-1, 3], [-15, 3], [-9, 0], [-57, -6], [-25, 2], [-9, 2], [1, 2], [9, 2], [11, 1], [10, -1], [15, 1], [28, 5], [64, 5], [4, 5], [-35, 27], [-3, 4], [6, -1], [18, -8], [20, -7], [15, -3], [16, -1], [14, 2], [12, 5], [34, 19], [25, 11], [66, 21], [80, 21], [32, 13], [64, 16], [50, 18], [4, 6], [-35, 2], [-38, -1], [-199, -28], [-17, -1], [-14, 1]], [[4562, 8184], [-10, 5]], [[4552, 8189], [-11, 6], [-29, 9], [-15, 4], [-16, 3], [-25, 2], [-25, -1], [-24, 1], [-6, -1], [-13, 1], [-6, -1], [-4, -3], [-2, -5], [-3, -13], [-5, -10], [-3, -3], [-9, -3], [-7, -9], [-5, -3], [-7, -2], [-34, 0], [-4, -2], [-18, 1], [-7, 1], [-3, 3], [-1, 3], [2, 3], [5, 5], [20, 19], [1, 4], [-18, 5], [-4, 4], [-5, 18], [0, 7], [2, 10], [0, 5], [-4, 4], [-4, 1], [-7, -1], [-5, -3], [-3, -4], [-16, -5], [-3, -1], [2, -2], [3, -1], [5, 0], [7, -2], [-2, -2], [-7, -4], [-6, -5], [-5, -6], [-4, -3], [-2, 0], [-7, 1], [-2, -8], [0, -3], [-9, -8], [-1, -5], [7, -7], [8, -10], [-1, -6], [-16, -9], [-11, 0], [-16, 4], [-13, 4], [-12, 6], [-15, 9], [-28, 22], [-5, 5], [-6, 9], [-6, 13], [-7, 12], [-16, 21], [-7, 6], [-11, 9], [-29, 17], [-9, 4], [-10, 3], [-4, -1], [-6, -5], [-3, -5], [0, -7], [3, -3], [10, -4], [-4, -4], [1, -1], [5, -3], [22, -4], [-6, -4], [-26, -8], [-14, -6], [-6, -14], [-2, -3], [-25, -18], [-11, -7], [-9, -4], [-6, -2], [-4, 2], [-3, 4], [-1, 6], [1, 4], [15, 5], [2, 2], [3, 8], [-2, 0], [-6, -3], [-4, -4], [-6, -2], [-12, -4], [-23, -3], [-42, -12], [-18, -8], [-13, -10], [-11, -11], [-17, -19], [-3, -4], [-4, -9], [-1, 6], [0, 13], [-2, 7], [-4, 3], [-4, 1], [-4, -1], [-3, -2], [-7, -8], [-2, -1], [2, 7], [-4, 2], [-16, 3], [-6, 0], [-12, -3], [-2, -4], [3, -1], [-1, -2], [-3, -5], [-7, -5], [-16, -7], [-7, -3], [-11, -5], [-14, -3], [-13, -7], [-3, -4], [-1, -4], [0, -4], [2, -6], [3, -3], [12, -1], [1, -2], [-3, -3], [-10, -4], [-46, 4], [-3, -2], [1, -1], [10, -5], [4, -4], [-4, -1], [-12, 4], [-2, 2], [-3, 7], [3, 5], [11, 9], [11, 7], [9, 9], [12, 12], [10, 6], [16, 2], [11, 4], [11, 7], [5, 4], [11, 14], [4, 2], [5, 1], [30, 4], [13, 2], [8, 2], [19, 2], [10, 2], [9, 5], [19, 11], [63, 25], [8, 9], [0, 4], [-6, 7], [-4, 3], [-4, 0], [-13, -10], [-4, -2], [-8, 1], [-10, -1], [-7, 1], [-10, 4], [-8, 1], [-12, -2], [-20, -8], [-5, -4], [-4, -5], [-8, -8], [-6, -1], [-8, 3], [-9, -1], [-11, -4], [-27, -16], [-18, -5], [-5, 0], [-8, 5], [-12, -1], [-3, -2], [-3, -2], [0, -1], [7, -1], [5, -2], [1, -2], [-4, -1], [-6, 0], [-24, 0], [-5, -2], [-9, -14], [-4, -5], [-7, -4], [-10, -3], [-14, 0], [-16, -5], [-20, -8], [-17, -5], [0, 3], [12, 13], [5, 4], [5, 10], [-1, 2], [-14, 3], [-4, 4], [-4, 6], [-5, 4], [-12, 1], [-4, -2], [-1, -4], [-2, -7], [0, -3], [3, -8], [-3, -1], [-27, 2], [-23, -2], [-4, -2], [-2, -3], [-2, -12], [-3, -2], [-16, 3], [-9, -5], [-6, 2], [-17, -23], [5, -4], [8, -3], [7, -2], [6, -1], [-1, -2], [-8, -1], [-14, 0], [-4, -1], [-1, -2], [3, -6], [2, -1], [18, -3], [16, -6], [2, -1], [13, -16], [-2, -1], [-8, 1], [-38, 16], [-20, 6]], [[3375, 8103], [-25, 1]], [[3350, 8104], [-4, 1]], [[3346, 8105], [-17, -1], [-27, 7], [-15, 2], [-47, 15], [-19, 7], [-13, 8], [-31, 11], [-37, 23], [-62, 13], [-33, -2], [-35, 4], [-10, 1]], [[3000, 8193], [-7, 1]], [[2993, 8194], [-16, 1], [-3, -2], [-6, 1], [-9, 7], [-13, 6], [-46, 12]], [[2900, 8219], [-10, 8]], [[2890, 8227], [-22, 11]], [[2868, 8238], [-39, 10]], [[2829, 8248], [-4, -2]], [[2825, 8246], [-7, -1]], [[2818, 8245], [-16, 1], [-14, 0], [-24, -5], [-27, -2], [-16, -7], [-44, 3]], [[2677, 8235], [-3, 3]], [[2674, 8238], [-16, 2]], [[2658, 8240], [-29, 13]], [[2629, 8253], [-18, 0]], [[2611, 8253], [-18, 3]], [[2593, 8256], [-35, 1]], [[2558, 8257], [-25, -2], [-49, 5]], [[2484, 8260], [-7, 3]], [[2477, 8263], [-6, 7]], [[2471, 8270], [-13, 2]], [[2458, 8272], [-8, 4]], [[2450, 8276], [-8, 0], [-9, -4]], [[2433, 8272], [-9, 0]], [[2424, 8272], [-16, 11], [-12, 1]], [[2396, 8284], [-32, 9]], [[2364, 8293], [-11, -1]], [[2353, 8292], [-10, 3]], [[2343, 8295], [-26, -1]], [[2317, 8294], [-21, -7]], [[2296, 8287], [-10, -1]], [[2286, 8286], [-10, 1]], [[2276, 8287], [-10, 5]], [[2266, 8292], [-10, 2]], [[2256, 8294], [-24, -5]], [[2232, 8289], [-11, -1]], [[2221, 8288], [-8, -4], [-55, 4]], [[2158, 8288], [10, 7]], [[2168, 8295], [1, 3]], [[2169, 8298], [2, 2]], [[2171, 8300], [-31, 0]], [[2140, 8300], [-6, 1]], [[2134, 8301], [-1, 6], [-10, 0], [-6, 4]], [[2117, 8311], [3, 10]], [[2120, 8321], [16, 9], [-5, 4]], [[2131, 8334], [-15, 4]], [[2116, 8338], [-14, 1]], [[2102, 8339], [-9, -1]], [[2093, 8338], [-34, 6], [-21, -5], [-15, 1], [-17, -2], [-21, -9], [-15, 4], [-16, 1]], [[1954, 8334], [-15, 6]], [[1939, 8340], [5, 4]], [[1944, 8344], [4, 7], [-11, 7], [-10, 4]], [[1927, 8362], [-17, 2]], [[1910, 8364], [-5, -2]], [[1905, 8362], [-6, -8]], [[1899, 8354], [-21, -14]], [[1878, 8340], [-10, -5]], [[1868, 8335], [-12, -2]], [[1856, 8333], [-8, 1], [-5, 7], [-8, 3]], [[1835, 8344], [26, 8]], [[1861, 8352], [13, 7]], [[1874, 8359], [4, 7]], [[1878, 8366], [-5, 8]], [[1873, 8374], [-13, 0]], [[1860, 8374], [-50, 12]], [[1810, 8386], [-8, 6]], [[1802, 8392], [6, 5], [8, 2], [-6, 1], [-24, -10]], [[1786, 8390], [-15, -11]], [[1771, 8379], [-17, -16]], [[1754, 8363], [-10, -6]], [[1744, 8357], [-22, -12]], [[1722, 8345], [-23, -9]], [[1699, 8336], [-7, -2]], [[1692, 8334], [-37, -1]], [[1655, 8333], [-2, -2]], [[1653, 8331], [-9, -3]], [[1644, 8328], [-28, 1]], [[1616, 8329], [-7, -4]], [[1609, 8325], [-13, -2]], [[1596, 8323], [-4, 0]], [[1592, 8323], [-3, 3]], [[1589, 8326], [6, 4]], [[1595, 8330], [6, 2]], [[1601, 8332], [-1, 4]], [[1600, 8336], [-3, 2], [-6, 0], [-28, -11], [-31, -18]], [[1532, 8309], [9, 0]], [[1541, 8309], [17, -12]], [[1558, 8297], [27, -1]], [[1585, 8296], [-22, -5], [-10, 2], [-3, -5]], [[1550, 8288], [-1, -8]], [[1549, 8280], [0, -7]], [[1549, 8273], [0, -6]], [[1549, 8267], [-4, 6]], [[1545, 8273], [-14, 1]], [[1531, 8274], [7, 13]], [[1538, 8287], [-8, 3]], [[1530, 8290], [11, 11]], [[1541, 8301], [-6, 2]], [[1535, 8303], [-6, 1]], [[1529, 8304], [-39, -17]], [[1490, 8287], [-2, -3]], [[1488, 8284], [-26, -14]], [[1462, 8270], [-50, -8]], [[1412, 8262], [-10, -4]], [[1402, 8258], [-17, -4]], [[1385, 8254], [-6, 0]], [[1379, 8254], [2, 1]], [[1381, 8255], [10, 4]], [[1391, 8259], [7, 5]], [[1398, 8264], [5, 1]], [[1403, 8265], [-2, 2]], [[1401, 8267], [-2, 2]], [[1399, 8269], [-5, 4]], [[1394, 8273], [-8, -5]], [[1386, 8268], [-7, -7], [-21, -15]], [[1358, 8246], [-47, -40]], [[1311, 8206], [-11, -17], [-3, -19]], [[1297, 8170], [-2, -8]], [[1295, 8162], [-2, 0], [-1, 1], [-4, -6]], [[1288, 8157], [-22, -20]], [[1266, 8137], [-25, -16], [-22, -9]], [[1219, 8112], [-12, -3]], [[1207, 8109], [-45, -4]], [[1162, 8105], [-12, -2]], [[1150, 8103], [-35, -2]], [[1115, 8101], [-54, 2]], [[1061, 8103], [2, -10]], [[1063, 8093], [-8, -26]], [[1055, 8067], [-7, -18]], [[1048, 8049], [-6, -4]], [[1042, 8045], [-9, -3]], [[1033, 8042], [-6, 1], [8, 6]], [[1035, 8049], [-8, -2]], [[1027, 8047], [-11, -5]], [[1016, 8042], [17, -5]], [[1033, 8037], [12, -2]], [[1045, 8035], [14, -3]], [[1059, 8032], [21, -14]], [[1080, 8018], [44, -13], [97, -52]], [[1221, 7953], [14, -15]], [[1235, 7938], [11, -25]], [[1246, 7913], [6, -8], [15, -11], [40, -9], [19, 1]], [[1326, 7886], [14, -2]], [[1340, 7884], [13, 10]], [[1353, 7894], [0, -5]], [[1353, 7889], [1, -5]], [[1354, 7884], [33, 3]], [[1387, 7887], [19, -3]], [[1406, 7884], [8, -5], [-9, -7], [-11, -13]], [[1394, 7859], [1, -13], [14, -6]], [[1409, 7840], [8, -6]], [[1417, 7834], [14, -5], [26, 12]], [[1457, 7841], [15, 2]], [[1472, 7843], [17, -8], [22, 1]], [[1511, 7836], [7, -5]], [[1518, 7831], [3, -7]], [[1521, 7824], [-1, -11]], [[1520, 7813], [-32, -5]], [[1488, 7808], [-10, 1]], [[1478, 7809], [-20, 11]], [[1458, 7820], [-9, 2]], [[1449, 7822], [-13, 0]], [[1436, 7822], [-20, -4]], [[1416, 7818], [-25, 12], [-10, 12]], [[1381, 7842], [2, 14]], [[1383, 7856], [-9, 2], [-9, 14]], [[1365, 7872], [-8, 3]], [[1357, 7875], [-9, -2]], [[1348, 7873], [-10, -4]], [[1338, 7869], [5, -10]], [[1343, 7859], [6, -9]], [[1349, 7850], [11, 0]], [[1360, 7850], [10, -5], [23, -23]], [[1393, 7822], [-2, -10]], [[1391, 7812], [7, -4]], [[1398, 7808], [22, 4], [32, -9], [4, -4], [3, -13]], [[1459, 7786], [-6, 6]], [[1453, 7792], [-7, -2]], [[1446, 7790], [-11, 3]], [[1435, 7793], [-9, 4]], [[1426, 7797], [-7, -4]], [[1419, 7793], [-20, -23]], [[1399, 7770], [-9, -1]], [[1390, 7769], [-22, 3], [-29, -2]], [[1339, 7770], [-10, 1]], [[1329, 7771], [-13, 5]], [[1316, 7776], [-22, -3]], [[1294, 7773], [-40, 1]], [[1254, 7774], [-26, 15]], [[1228, 7789], [5, 5]], [[1233, 7794], [6, 4]], [[1239, 7798], [-1, 10]], [[1238, 7808], [9, 14]], [[1247, 7822], [1, 4]], [[1248, 7826], [-5, 4]], [[1243, 7830], [-4, 2]], [[1239, 7832], [6, 1]], [[1245, 7833], [13, -1]], [[1258, 7832], [-6, 4]], [[1252, 7836], [-26, 0]], [[1226, 7836], [-31, -3]], [[1195, 7833], [-16, -4]], [[1179, 7829], [-30, -14], [-11, 1], [-19, -4]], [[1119, 7812], [-25, -11]], [[1094, 7801], [-3, -3]], [[1091, 7798], [-2, -5]], [[1089, 7793], [22, -9]], [[1111, 7784], [-3, -3]], [[1108, 7781], [-3, -2], [-7, -2], [-22, 1], [-4, 1]], [[1072, 7779], [-12, 5]], [[1060, 7784], [-14, -3]], [[1046, 7781], [-11, -5]], [[1035, 7776], [-16, -6]], [[1019, 7770], [-11, -11]], [[1008, 7759], [-8, -6], [-6, -4], [-25, -2], [-14, -12]], [[955, 7735], [-25, -9]], [[930, 7726], [-1, 4]], [[929, 7730], [-1, 4], [-6, -3]], [[922, 7731], [-6, -7]], [[916, 7724], [4, -8], [4, -3]], [[924, 7713], [45, -17]], [[969, 7696], [57, -10]], [[1026, 7686], [4, 2], [32, -6]], [[1062, 7682], [3, -2]], [[1065, 7680], [2, -3]], [[1067, 7677], [-12, 2]], [[1055, 7679], [-13, -4], [-6, -11], [-18, -2], [-7, 2], [-4, 2]], [[1007, 7666], [-2, -1]], [[1005, 7665], [8, -7]], [[1013, 7658], [21, -17]], [[1034, 7641], [11, -3]], [[1045, 7638], [0, -12]], [[1045, 7626], [-5, -4], [0, -8]], [[1040, 7614], [12, -12]], [[1052, 7602], [14, -5]], [[1066, 7597], [53, -8]], [[1119, 7589], [24, -6]], [[1143, 7583], [12, -1]], [[1155, 7582], [6, 1]], [[1161, 7583], [4, 2]], [[1165, 7585], [2, 4], [5, 2], [3, -1], [2, -2]], [[1177, 7588], [30, 9]], [[1207, 7597], [46, 1]], [[1253, 7598], [17, -5]], [[1270, 7593], [17, -8]], [[1287, 7585], [9, -6], [4, 6], [4, 5]], [[1304, 7590], [-10, 1]], [[1294, 7591], [-6, 4]], [[1288, 7595], [-4, 5]], [[1284, 7600], [8, 5]], [[1292, 7605], [25, -16]], [[1317, 7589], [5, -16]], [[1322, 7573], [8, 0]], [[1330, 7573], [6, 9]], [[1336, 7582], [23, 19]], [[1359, 7601], [12, 7]], [[1371, 7608], [24, 8]], [[1395, 7616], [8, 9]], [[1403, 7625], [10, -3], [13, 0]], [[1426, 7622], [21, 15]], [[1447, 7637], [4, 1]], [[1451, 7638], [6, -3]], [[1457, 7635], [7, -8]], [[1464, 7627], [6, -5]], [[1470, 7622], [2, -4]], [[1472, 7618], [2, -9]], [[1474, 7609], [-4, -8]], [[1470, 7601], [-3, -4]], [[1467, 7597], [-9, -5]], [[1458, 7592], [-11, -3]], [[1447, 7589], [-17, 2]], [[1430, 7591], [-6, -11]], [[1424, 7580], [8, 0]], [[1432, 7580], [12, -5]], [[1444, 7575], [18, -17]], [[1462, 7558], [7, -26]], [[1469, 7532], [5, -11]], [[1474, 7521], [4, -14], [-3, -10]], [[1475, 7497], [-8, -8]], [[1467, 7489], [-13, -12], [-13, -8], [-18, -3], [-37, -2]], [[1386, 7464], [-6, 2]], [[1380, 7466], [-4, 8], [-6, 1]], [[1370, 7475], [-7, -2]], [[1363, 7473], [-6, -9], [-20, -22], [-15, -7]], [[1322, 7435], [-10, -10]], [[1312, 7425], [-9, -5]], [[1303, 7420], [-18, -4]], [[1285, 7416], [-5, 0]], [[1280, 7416], [-5, 5], [-6, 2]], [[1269, 7423], [-11, -5]], [[1258, 7418], [-7, -5]], [[1251, 7413], [-1, 2]], [[1250, 7415], [2, 2]], [[1252, 7417], [5, 2]], [[1257, 7419], [2, 3]], [[1259, 7422], [1, 4]], [[1260, 7426], [-9, 8]], [[1251, 7434], [-16, 6]], [[1235, 7440], [-13, 2]], [[1222, 7442], [-23, -6]], [[1199, 7436], [-4, -3]], [[1195, 7433], [-5, -7]], [[1190, 7426], [12, -9]], [[1202, 7417], [-1, -2]], [[1201, 7415], [-3, 1]], [[1198, 7416], [-19, -3], [-7, -5]], [[1172, 7408], [-3, -7]], [[1169, 7401], [-3, -13], [2, -15], [2, -6], [8, 6]], [[1178, 7373], [7, 4]], [[1185, 7377], [0, -3]], [[1185, 7374], [-7, -9]], [[1178, 7365], [-12, -3]], [[1166, 7362], [3, -8], [3, -2], [-1, -2], [-9, 4], [-8, 2]], [[1154, 7356], [-9, -2]], [[1145, 7354], [-6, -5], [-20, -20], [-20, -24], [-2, -10]], [[1097, 7295], [1, -6]], [[1098, 7289], [1, -4]], [[1099, 7285], [8, -7]], [[1107, 7278], [-17, -4]], [[1090, 7274], [-13, 0]], [[1077, 7274], [-6, -4]], [[1071, 7270], [4, -6], [17, -6], [-2, -2]], [[1090, 7256], [-21, -4]], [[1069, 7252], [-2, 1], [-3, -1], [0, -7], [1, -5], [5, -5]], [[1070, 7235], [10, 6]], [[1080, 7241], [9, -2]], [[1089, 7239], [3, -5]], [[1092, 7234], [-8, -11]], [[1084, 7223], [3, -8]], [[1087, 7215], [14, -4]], [[1101, 7211], [4, -16]], [[1105, 7195], [5, -7], [7, -1], [7, 1]], [[1124, 7188], [1, 8]], [[1125, 7196], [-1, 5]], [[1124, 7201], [4, 6], [5, 1], [2, -1]], [[1135, 7207], [-5, -4], [-3, -4]], [[1127, 7199], [5, -3]], [[1132, 7196], [6, -2]], [[1138, 7194], [4, 4]], [[1142, 7198], [2, 0]], [[1144, 7198], [-1, -7]], [[1143, 7191], [5, -4]], [[1148, 7187], [10, 2]], [[1158, 7189], [6, 0], [-1, -3]], [[1163, 7186], [-9, -5]], [[1154, 7181], [-14, -9], [5, -4], [4, -2], [24, 2], [24, -8], [34, 0]], [[1231, 7160], [19, 12]], [[1250, 7172], [7, -4]], [[1257, 7168], [5, -4]], [[1262, 7164], [-5, -3]], [[1257, 7161], [-14, 1]], [[1243, 7162], [-5, -3]], [[1238, 7159], [22, -4]], [[1260, 7155], [8, -3]], [[1268, 7152], [7, -5]], [[1275, 7147], [-8, -11]], [[1267, 7136], [-16, -8]], [[1251, 7128], [-7, 9]], [[1244, 7137], [-5, 9], [-3, 1]], [[1236, 7147], [-5, 1]], [[1231, 7148], [-10, -9], [-14, -10]], [[1207, 7129], [-5, -1]], [[1202, 7128], [4, 6]], [[1206, 7134], [4, 9]], [[1210, 7143], [-4, 6]], [[1206, 7149], [-4, 3]], [[1202, 7152], [-11, 2]], [[1191, 7154], [-13, 7]], [[1178, 7161], [-9, 2], [-8, -2]], [[1161, 7161], [-9, -16], [-25, -23]], [[1127, 7122], [9, -2]], [[1136, 7120], [9, 0]], [[1145, 7120], [7, -3]], [[1152, 7117], [-2, -4]], [[1150, 7113], [-1, -6]], [[1149, 7107], [11, -8]], [[1160, 7099], [9, -5]], [[1169, 7094], [11, 0]], [[1180, 7094], [14, -18], [27, -19]], [[1221, 7057], [-1, -5]], [[1220, 7052], [0, -6]], [[1220, 7046], [18, -11]], [[1238, 7035], [17, 0]], [[1255, 7035], [36, 5]], [[1291, 7040], [26, 9]], [[1317, 7049], [11, 8]], [[1328, 7057], [13, 0]], [[1341, 7057], [2, 6]], [[1343, 7063], [3, 10]], [[1346, 7073], [-2, 9]], [[1344, 7082], [-2, 3]], [[1342, 7085], [-10, 5]], [[1332, 7090], [6, 3], [10, 12]], [[1348, 7105], [16, 23]], [[1364, 7128], [5, 5], [5, 6]], [[1374, 7139], [5, 1]], [[1379, 7140], [8, 0], [2, -1]], [[1389, 7139], [-15, -9]], [[1374, 7130], [-12, -18]], [[1362, 7112], [-10, -20]], [[1352, 7092], [14, -13]], [[1366, 7079], [8, -23]], [[1374, 7056], [18, -32], [6, -14]], [[1398, 7010], [-1, -9]], [[1397, 7001], [-3, -10], [-3, -8], [-8, -9]], [[1383, 6974], [3, -16]], [[1386, 6958], [7, -9]], [[1393, 6949], [7, 4]], [[1400, 6953], [12, 0]], [[1412, 6953], [-11, -11]], [[1401, 6942], [0, -7]], [[1401, 6935], [0, -7]], [[1401, 6928], [5, -12]], [[1406, 6916], [-10, -9]], [[1396, 6907], [-12, -4], [-11, -4]], [[1373, 6899], [30, -4]], [[1403, 6895], [31, 7]], [[1434, 6902], [2, 8]], [[1436, 6910], [3, 2]], [[1439, 6912], [3, 5], [3, 0]], [[1445, 6917], [22, 8]], [[1467, 6925], [8, 0]], [[1475, 6925], [13, 10]], [[1488, 6935], [10, 6]], [[1498, 6941], [12, 6]], [[1510, 6947], [8, -10], [9, -8]], [[1527, 6929], [17, -10]], [[1544, 6919], [7, 2], [7, 7]], [[1558, 6928], [6, 2]], [[1564, 6930], [16, -14], [8, -8], [21, -30]], [[1609, 6878], [10, -8]], [[1619, 6870], [12, 5]], [[1631, 6875], [2, 9], [-7, 23], [2, 9], [4, 13], [-2, 8]], [[1630, 6937], [4, -2]], [[1634, 6935], [6, -3]], [[1640, 6932], [7, 7]], [[1647, 6939], [6, 10], [7, 2], [15, -6], [11, -7]], [[1686, 6938], [-8, 0]], [[1678, 6938], [-10, 4]], [[1668, 6942], [-9, -2], [-3, -7]], [[1656, 6933], [-3, -10]], [[1653, 6923], [5, -8]], [[1658, 6915], [4, -4]], [[1662, 6911], [7, -13], [8, -3]], [[1677, 6895], [13, 3]], [[1690, 6898], [28, 13], [40, 15]], [[1758, 6926], [14, 13]], [[1772, 6939], [12, 17]], [[1784, 6956], [-9, -20]], [[1775, 6936], [-9, -22]], [[1766, 6914], [2, -4]], [[1768, 6910], [3, -1]], [[1771, 6909], [-20, -11]], [[1751, 6898], [-17, -16], [-5, -10]], [[1729, 6872], [0, -8]], [[1729, 6864], [2, -11]], [[1731, 6853], [8, -3]], [[1739, 6850], [4, 0]], [[1743, 6850], [11, -4]], [[1754, 6846], [-19, -3]], [[1735, 6843], [-9, -4]], [[1726, 6839], [-4, -10]], [[1722, 6829], [-1, -18]], [[1721, 6811], [-5, -18]], [[1716, 6793], [-1, -8]], [[1715, 6785], [2, -5], [5, -4]], [[1722, 6776], [3, -7]], [[1725, 6769], [3, -2]], [[1728, 6767], [5, -1]], [[1733, 6766], [1, -1]], [[1734, 6765], [-6, -3]], [[1728, 6762], [-9, 2], [-2, 2]], [[1717, 6766], [-2, 3]], [[1715, 6769], [-3, 1]], [[1712, 6770], [-8, -3]], [[1704, 6767], [-4, -2], [-11, -12]], [[1689, 6753], [-7, -5]], [[1682, 6748], [-7, -2]], [[1675, 6746], [-8, -6]], [[1667, 6740], [-11, -11]], [[1656, 6729], [-9, -10], [-6, -9]], [[1641, 6710], [-1, -5]], [[1640, 6705], [-1, -6], [1, -7], [1, -7], [-1, -4], [-2, -1], [-6, 1], [-9, 3]], [[1623, 6684], [-1, 3]], [[1622, 6687], [0, 4], [-6, -2]], [[1616, 6689], [-13, -11]], [[1603, 6678], [-10, -9]], [[1593, 6669], [-38, -15]], [[1555, 6654], [-20, -15]], [[1535, 6639], [-8, -5], [-12, -9], [-6, -9], [-6, -12], [-4, -9]], [[1499, 6595], [-2, -7]], [[1497, 6588], [1, -5], [3, -3], [14, -8], [3, -4], [-2, -3]], [[1516, 6565], [-4, -1]], [[1512, 6564], [-7, 2]], [[1505, 6566], [-5, 3], [-2, 3], [-6, 1], [-8, -1], [-4, -2]], [[1480, 6570], [1, -9]], [[1481, 6561], [-1, -2]], [[1480, 6559], [-3, 0]], [[1477, 6559], [-4, 2]], [[1473, 6561], [-12, 13]], [[1461, 6574], [0, 3]], [[1461, 6577], [8, 4]], [[1469, 6581], [2, 3], [-2, 3], [-5, -3]], [[1464, 6584], [-14, -2]], [[1450, 6582], [-3, 0]], [[1447, 6582], [-3, 3]], [[1444, 6585], [4, 4]], [[1448, 6589], [-3, 1]], [[1445, 6590], [-37, -13]], [[1408, 6577], [-19, -10]], [[1389, 6567], [-17, -12], [-14, -15]], [[1358, 6540], [-13, -17]], [[1345, 6523], [-11, -12], [-10, -6]], [[1324, 6505], [-5, -5]], [[1319, 6500], [-2, -4]], [[1317, 6496], [-2, -3]], [[1315, 6493], [-5, -1], [-3, 0]], [[1307, 6492], [-3, 2]], [[1304, 6494], [-5, -1]], [[1299, 6493], [-13, -9]], [[1286, 6484], [-2, -3]], [[1284, 6481], [0, -4], [2, -6], [-1, -7]], [[1285, 6464], [-3, -9]], [[1282, 6455], [0, -4]], [[1282, 6451], [3, 0]], [[1285, 6451], [6, 3]], [[1291, 6454], [6, 6], [3, 7]], [[1300, 6467], [-2, 7]], [[1298, 6474], [0, 4]], [[1298, 6478], [10, -3]], [[1308, 6475], [10, -10], [3, -1]], [[1321, 6464], [12, 6]], [[1333, 6470], [4, 4], [0, 4], [-2, 18]], [[1335, 6496], [1, 3]], [[1336, 6499], [7, -1], [3, -4]], [[1346, 6494], [6, -7]], [[1352, 6487], [1, -4]], [[1353, 6483], [-3, -2]], [[1350, 6481], [-1, -2]], [[1349, 6479], [3, -2], [3, -1]], [[1355, 6476], [4, 0]], [[1359, 6476], [4, 3]], [[1363, 6479], [5, 5]], [[1368, 6484], [4, 3], [7, -1], [7, 7]], [[1386, 6493], [18, 23]], [[1404, 6516], [2, 3], [3, 13], [2, 4]], [[1411, 6536], [4, 4]], [[1415, 6540], [7, 3], [4, 1], [8, -2], [8, -4], [3, -2], [1, -2], [-9, 2]], [[1437, 6536], [-4, -1]], [[1433, 6535], [-3, -2], [-3, -3]], [[1427, 6530], [-2, -5]], [[1425, 6525], [-1, -8]], [[1424, 6517], [2, -2]], [[1426, 6515], [6, -1]], [[1432, 6514], [16, 2], [6, 2], [6, 4]], [[1460, 6522], [5, 6]], [[1465, 6528], [4, 2]], [[1469, 6530], [10, -3]], [[1479, 6527], [3, 2]], [[1482, 6529], [4, 5]], [[1486, 6534], [4, 1]], [[1490, 6535], [6, -2]], [[1496, 6533], [4, 0], [3, 3], [4, 6]], [[1507, 6542], [3, 3]], [[1510, 6545], [10, 3]], [[1520, 6548], [15, 12]], [[1535, 6560], [6, 3]], [[1541, 6563], [4, 0], [3, 1], [5, 4], [3, 1], [2, 0], [5, -2], [1, -4], [0, -17], [0, -2]], [[1564, 6544], [4, 3]], [[1568, 6547], [4, 5]], [[1572, 6552], [2, 6]], [[1574, 6558], [1, 7]], [[1575, 6565], [7, 4]], [[1582, 6569], [49, 17], [7, 7], [6, 13], [3, 3]], [[1647, 6609], [2, 0]], [[1649, 6609], [1, -2]], [[1650, 6607], [2, -11]], [[1652, 6596], [1, -1]], [[1653, 6595], [3, 1]], [[1656, 6596], [3, 5]], [[1659, 6601], [12, 10]], [[1671, 6611], [-1, 1], [-2, 5]], [[1668, 6617], [-2, 4]], [[1666, 6621], [-4, 2], [-6, 2], [-5, -1], [-1, 0]], [[1650, 6624], [1, 3]], [[1651, 6627], [9, 12], [5, 2], [13, 3]], [[1678, 6644], [5, 3]], [[1683, 6647], [4, 1]], [[1687, 6648], [7, 0]], [[1694, 6648], [4, 1], [0, 1], [-4, 1], [-8, 2], [4, 4], [12, 6], [8, 1], [7, -2], [5, -1]], [[1722, 6661], [3, 1]], [[1725, 6662], [3, 5]], [[1728, 6667], [3, 10]], [[1731, 6677], [4, 4], [4, 2]], [[1739, 6683], [4, 1]], [[1743, 6684], [5, -1]], [[1748, 6683], [5, 0]], [[1753, 6683], [5, 2]], [[1758, 6685], [6, 4]], [[1764, 6689], [6, 6]], [[1770, 6695], [5, 3], [4, 1], [4, 3]], [[1783, 6702], [3, 4]], [[1786, 6706], [5, 1]], [[1791, 6707], [7, -1]], [[1798, 6706], [2, 7]], [[1800, 6713], [7, 3], [2, 2], [6, 11]], [[1815, 6729], [0, 5]], [[1815, 6734], [-3, 6]], [[1812, 6740], [-2, 2]], [[1810, 6742], [-1, 2]], [[1809, 6744], [3, 4], [15, 10]], [[1827, 6758], [7, 2]], [[1834, 6760], [5, -2], [3, 0]], [[1842, 6758], [1, 9]], [[1843, 6767], [3, 2], [14, 2]], [[1860, 6771], [3, 1]], [[1863, 6772], [4, 7]], [[1867, 6779], [8, 5]], [[1875, 6784], [2, 4]], [[1877, 6788], [0, 4]], [[1877, 6792], [5, 2]], [[1882, 6794], [9, 3]], [[1891, 6797], [8, 3], [13, 9], [3, 4], [7, 12]], [[1922, 6825], [33, 4]], [[1955, 6829], [0, 7]], [[1955, 6836], [1, 1]], [[1956, 6837], [12, 3]], [[1968, 6840], [10, 0]], [[1978, 6840], [3, 2]], [[1981, 6842], [1, 8]], [[1982, 6850], [2, 7]], [[1984, 6857], [-6, 0]], [[1978, 6857], [0, 2], [15, 7], [3, 4], [-1, 5], [3, 6]], [[1998, 6881], [12, 11]], [[2010, 6892], [4, 2]], [[2014, 6894], [9, 3]], [[2023, 6897], [6, 3]], [[2029, 6900], [14, 12]], [[2043, 6912], [6, 8]], [[2049, 6920], [2, 4]], [[2051, 6924], [1, 3]], [[2052, 6927], [-1, 3]], [[2051, 6930], [-6, 6]], [[2045, 6936], [-19, 9], [-10, 3], [-8, 2]], [[2008, 6950], [-18, 5]], [[1990, 6955], [-4, 4]], [[1986, 6959], [3, 10]], [[1989, 6969], [6, 11]], [[1995, 6980], [-2, 3]], [[1993, 6983], [21, 13]], [[2014, 6996], [5, 4]], [[2019, 7000], [3, 5]], [[2022, 7005], [7, 6], [1, 2], [-5, 2]], [[2025, 7015], [2, 2]], [[2027, 7017], [13, 8]], [[2040, 7025], [5, 3]], [[2045, 7028], [4, -2]], [[2049, 7026], [0, -1]], [[2049, 7025], [-2, -6]], [[2047, 7019], [2, -1]], [[2049, 7018], [10, 1]], [[2059, 7019], [11, 5], [3, 2], [2, 8]], [[2075, 7034], [-1, 2]], [[2074, 7036], [-14, 4]], [[2060, 7040], [2, 1]], [[2062, 7041], [7, 2]], [[2069, 7043], [19, 3]], [[2088, 7046], [7, 3]], [[2095, 7049], [8, 9]], [[2103, 7058], [2, 5], [0, 5], [-2, 5]], [[2103, 7073], [-7, 6]], [[2096, 7079], [-11, 7]], [[2085, 7086], [-11, 6], [1, 1], [8, 0]], [[2083, 7093], [9, -6]], [[2092, 7087], [6, -1], [5, 0]], [[2103, 7086], [9, 3]], [[2112, 7089], [13, 9]], [[2125, 7098], [6, 5], [3, 3]], [[2134, 7106], [-4, 5]], [[2130, 7111], [0, 3], [3, 6], [21, 18]], [[2154, 7138], [10, 6]], [[2164, 7144], [6, 1], [3, 2]], [[2173, 7147], [-3, 9]], [[2170, 7156], [0, 3]], [[2170, 7159], [4, 6]], [[2174, 7165], [11, 8]], [[2185, 7173], [10, 4]], [[2195, 7177], [14, 4]], [[2209, 7181], [10, 5]], [[2219, 7186], [7, 7]], [[2226, 7193], [9, 6]], [[2235, 7199], [25, 12]], [[2260, 7211], [4, 1]], [[2264, 7212], [2, -1]], [[2266, 7211], [5, -5]], [[2271, 7206], [28, 1]], [[2299, 7207], [10, 2]], [[2309, 7209], [3, 1]], [[2312, 7210], [5, 10]], [[2317, 7220], [4, 4]], [[2321, 7224], [10, 7]], [[2331, 7231], [5, 2]], [[2336, 7233], [15, 2]], [[2351, 7235], [8, -1]], [[2359, 7234], [-20, -9]], [[2339, 7225], [-11, -7]], [[2328, 7218], [-7, -6]], [[2321, 7212], [-4, -5]], [[2317, 7207], [-1, -4], [-3, -2]], [[2313, 7201], [-7, -2]], [[2306, 7199], [-3, -3]], [[2303, 7196], [7, -6]], [[2310, 7190], [29, -15]], [[2339, 7175], [10, -3]], [[2349, 7172], [24, -4]], [[2373, 7168], [6, -6], [0, -1]], [[2379, 7161], [-8, 1]], [[2371, 7162], [-35, 8]], [[2336, 7170], [-17, 1]], [[2319, 7171], [-11, -3]], [[2308, 7168], [-9, 0]], [[2299, 7168], [-7, 2], [-6, 4]], [[2286, 7174], [-5, 4]], [[2281, 7178], [-7, 1]], [[2274, 7179], [-26, -13]], [[2248, 7166], [-14, -9]], [[2234, 7157], [-28, -11]], [[2206, 7146], [-3, -3]], [[2203, 7143], [0, -7]], [[2203, 7136], [3, -13]], [[2206, 7123], [0, -10], [-6, -23]], [[2200, 7090], [-4, -8]], [[2196, 7082], [-13, -13]], [[2183, 7069], [-9, -12]], [[2174, 7057], [-4, -8]], [[2170, 7049], [-5, -17]], [[2165, 7032], [3, -7], [4, -2], [19, -6], [5, 0], [3, 1]], [[2199, 7018], [25, 15]], [[2224, 7033], [2, -1]], [[2226, 7032], [1, -1]], [[2227, 7031], [-11, -16]], [[2216, 7015], [-6, -6]], [[2210, 7009], [-10, -8]], [[2200, 7001], [-9, -4]], [[2191, 6997], [-14, -2]], [[2177, 6995], [-12, -7]], [[2165, 6988], [-2, -2], [-4, -5]], [[2159, 6981], [-3, -7]], [[2156, 6974], [2, -2]], [[2158, 6972], [3, -1]], [[2161, 6971], [13, -8]], [[2174, 6963], [9, 0]], [[2183, 6963], [11, 5], [8, 1], [6, -1]], [[2208, 6968], [5, -1]], [[2213, 6967], [4, 1]], [[2217, 6968], [1, 3]], [[2218, 6971], [-3, 4]], [[2215, 6975], [1, 1], [10, -3], [8, -4], [2, 1], [2, 6]], [[2238, 6976], [4, 5]], [[2242, 6981], [13, 10]], [[2255, 6991], [5, 6]], [[2260, 6997], [0, 6]], [[2260, 7003], [1, 4]], [[2261, 7007], [2, 0]], [[2263, 7007], [4, -3], [3, -1], [11, 6]], [[2281, 7009], [4, 0]], [[2285, 7009], [3, -1]], [[2288, 7008], [4, -1]], [[2292, 7007], [18, 15]], [[2310, 7022], [1, 2]], [[2311, 7024], [-5, 8]], [[2306, 7032], [1, 1]], [[2307, 7033], [3, 0]], [[2310, 7033], [13, -6]], [[2323, 7027], [1, 2]], [[2324, 7029], [-1, 9], [0, 3], [6, 8], [3, -3], [4, -13], [1, -2], [1, 0]], [[2338, 7031], [4, 15]], [[2342, 7046], [7, 8]], [[2349, 7054], [2, 4]], [[2351, 7058], [1, 12]], [[2352, 7070], [2, 1]], [[2354, 7071], [7, -11], [3, -2]], [[2364, 7058], [11, 4]], [[2375, 7062], [4, -4]], [[2379, 7058], [5, -2]], [[2384, 7056], [12, -4]], [[2396, 7052], [8, 0]], [[2404, 7052], [8, 1], [5, 2], [3, 2], [2, 0], [4, -2]], [[2426, 7055], [2, 2]], [[2428, 7057], [8, 15]], [[2436, 7072], [3, 3]], [[2439, 7075], [3, 0]], [[2442, 7075], [3, 1]], [[2445, 7076], [1, 2]], [[2446, 7078], [-2, 12]], [[2444, 7090], [1, 6], [1, 5], [1, 3]], [[2447, 7104], [4, 3]], [[2451, 7107], [7, 2]], [[2458, 7109], [4, 2]], [[2462, 7111], [2, 4], [-1, 5], [-6, 5], [-5, 1]], [[2452, 7126], [-5, -4]], [[2447, 7122], [-9, -1]], [[2438, 7121], [-11, -1]], [[2427, 7120], [-8, -1], [-6, -4], [-1, 1]], [[2412, 7116], [3, 4]], [[2415, 7120], [7, 5], [3, 0]], [[2425, 7125], [10, 0]], [[2435, 7125], [3, 2]], [[2438, 7127], [1, 3], [3, 8]], [[2442, 7138], [-1, 2]], [[2441, 7140], [-6, 3]], [[2435, 7143], [-4, 1]], [[2431, 7144], [-13, 9]], [[2418, 7153], [1, 2]], [[2419, 7155], [6, 1]], [[2425, 7156], [6, 0]], [[2431, 7156], [4, 3]], [[2435, 7159], [4, 10], [6, 10], [-1, 2], [-5, -1]], [[2439, 7180], [-8, -3]], [[2431, 7177], [-1, 0], [1, 3], [4, 3], [6, 2]], [[2441, 7185], [4, 1]], [[2445, 7186], [4, -1]], [[2449, 7185], [8, 0]], [[2457, 7185], [5, 3]], [[2462, 7188], [11, 10]], [[2473, 7198], [6, 3]], [[2479, 7201], [1, 1]], [[2480, 7202], [-16, -24]], [[2464, 7178], [-3, -6]], [[2461, 7172], [1, -2], [8, -7], [6, -1], [12, 3], [2, 3], [3, 5], [2, 2], [3, -3]], [[2498, 7172], [7, -2]], [[2505, 7170], [3, -4]], [[2508, 7166], [2, 0], [4, 1], [2, 2]], [[2516, 7169], [3, 4]], [[2519, 7173], [4, 2]], [[2523, 7175], [7, 1]], [[2530, 7176], [6, -1]], [[2536, 7175], [4, -2]], [[2540, 7173], [8, 3]], [[2548, 7176], [12, 9]], [[2560, 7185], [10, 6]], [[2570, 7191], [16, 1], [7, -3], [-24, -7]], [[2569, 7182], [-3, -7]], [[2566, 7175], [0, -11], [3, -3], [5, -4], [11, -3], [-8, -7], [-4, -1], [-2, -2], [5, -3]], [[2576, 7141], [12, 4]], [[2588, 7145], [8, 1], [5, -1]], [[2601, 7145], [1, -3]], [[2602, 7142], [0, -2], [2, -4], [36, -1]], [[2640, 7135], [-1, -4]], [[2639, 7131], [-5, -7]], [[2634, 7124], [-4, -4]], [[2630, 7120], [-7, -6], [4, -1], [10, 0]], [[2637, 7113], [12, -3]], [[2649, 7110], [14, -6]], [[2663, 7104], [10, -1]], [[2673, 7103], [7, 4], [5, 4]], [[2685, 7111], [8, 10]], [[2693, 7121], [10, 8]], [[2703, 7129], [10, 7], [3, 1]], [[2716, 7137], [-8, -10]], [[2708, 7127], [-2, -6]], [[2706, 7121], [-3, -9], [-3, -14], [3, -5], [9, -3], [5, -3]], [[2717, 7087], [2, -2], [9, -3]], [[2728, 7082], [15, -1]], [[2743, 7081], [12, -5]], [[2755, 7076], [7, -8], [0, -2]], [[2762, 7066], [-5, -2]], [[2757, 7064], [1, -4]], [[2758, 7060], [13, -1], [13, 1], [23, 5]], [[2807, 7065], [43, 5]], [[2850, 7070], [31, -2]], [[2881, 7068], [34, -6]], [[2915, 7062], [33, -7]], [[2948, 7055], [11, 3], [6, 2]], [[2965, 7060], [3, 3]], [[2968, 7063], [-1, 5]], [[2967, 7068], [0, 3]], [[2967, 7071], [1, 1], [4, -1]], [[2972, 7071], [2, -3]], [[2974, 7068], [3, -9]], [[2977, 7059], [0, -3]], [[2977, 7056], [-9, -9]], [[2968, 7047], [6, -4]], [[2974, 7043], [38, -14], [15, -3], [17, -2], [16, 2]], [[3060, 7026], [23, 9]], [[3083, 7035], [5, 3]], [[3088, 7038], [18, 17]], [[3106, 7055], [8, 5]], [[3114, 7060], [6, 0]], [[3120, 7060], [3, -2]], [[3123, 7058], [12, -12]], [[3135, 7046], [19, -7], [-4, -1], [-10, 1], [-3, -2]], [[3137, 7037], [-2, -5]], [[3135, 7032], [-1, -4]], [[3134, 7028], [-1, -10], [-1, -5], [-1, -1], [-2, 15], [0, 13], [-1, 4], [-9, 9], [-3, 1]], [[3116, 7054], [-2, -1]], [[3114, 7053], [-3, -3]], [[3111, 7050], [-1, -2]], [[3110, 7048], [-1, -4]], [[3109, 7044], [0, -4], [2, -7]], [[3111, 7033], [4, -7]], [[3115, 7026], [-1, -3], [-8, -11]], [[3106, 7012], [-4, -2]], [[3102, 7010], [-7, -3]], [[3095, 7007], [-3, -2], [2, -2], [5, -3]], [[3099, 7000], [10, -5]], [[3109, 6995], [18, -10]], [[3127, 6985], [35, -17]], [[3162, 6968], [14, -5], [15, -3]], [[3191, 6960], [-1, -2], [-3, -2], [2, -2]], [[3189, 6954], [7, -1]], [[3196, 6953], [7, -2]], [[3203, 6951], [9, -5]], [[3212, 6946], [16, -12]], [[3228, 6934], [5, -6]], [[3233, 6928], [3, -6]], [[3236, 6922], [5, -7]], [[3241, 6915], [9, -9]], [[3250, 6906], [7, -6]], [[3257, 6900], [4, -1]], [[3261, 6899], [3, -3]], [[3264, 6896], [1, -4]], [[3265, 6892], [1, -1], [36, -22], [16, -7]], [[3318, 6862], [13, -8]], [[3331, 6854], [7, -2]], [[3338, 6852], [1, 0], [10, 9], [25, 3], [4, 2], [2, 2], [0, 8], [-4, 6]], [[3376, 6882], [-9, 12]], [[3367, 6894], [-7, 2]], [[3360, 6896], [-13, 0]], [[3347, 6896], [6, 7]], [[3353, 6903], [0, 2]], [[3353, 6905], [-10, 5]], [[3343, 6910], [-1, 1]], [[3342, 6911], [-1, 4]], [[3341, 6915], [-4, 3]], [[3337, 6918], [-10, 5]], [[3327, 6923], [-10, 4]], [[3317, 6927], [-7, 0], [-6, -2], [-1, 1]], [[3303, 6926], [5, 6]], [[3308, 6932], [3, 1]], [[3311, 6933], [0, 4]], [[3311, 6937], [-4, 6], [1, 2]], [[3308, 6945], [12, -6]], [[3320, 6939], [21, -5]], [[3341, 6934], [4, -4]], [[3345, 6930], [3, -6]], [[3348, 6924], [2, -2]], [[3350, 6922], [5, -2]], [[3355, 6920], [6, -5]], [[3361, 6915], [6, -2]], [[3367, 6913], [3, 0]], [[3370, 6913], [3, 3]], [[3373, 6916], [2, 3], [0, 5], [0, 4], [-3, 6]], [[3372, 6934], [1, 12]], [[3373, 6946], [1, -1]], [[3374, 6945], [3, -5]], [[3377, 6940], [3, -12]], [[3380, 6928], [3, -2]], [[3383, 6926], [7, 3]], [[3390, 6929], [8, -1]], [[3398, 6928], [-17, -9]], [[3381, 6919], [0, -3], [12, -20]], [[3393, 6896], [2, -5]], [[3395, 6891], [-3, -14]], [[3392, 6877], [0, -7]], [[3392, 6870], [2, -1]], [[3394, 6869], [23, 2]], [[3417, 6871], [10, -4]], [[3427, 6867], [6, -9]], [[3433, 6858], [5, -5]], [[3438, 6853], [12, -3], [4, 2], [3, 4], [0, 3], [-1, 4], [-6, 20]], [[3450, 6883], [-3, 9]], [[3447, 6892], [-2, 10]], [[3445, 6902], [-4, 12]], [[3441, 6914], [-6, 16]], [[3435, 6930], [-4, 21]], [[3431, 6951], [-8, 13]], [[3423, 6964], [5, 1]], [[3428, 6965], [2, 0]], [[3430, 6965], [-1, 4], [-5, 8], [5, 1], [1, 4], [3, 8], [1, -1], [0, -10], [2, -10], [8, -20]], [[3444, 6949], [7, -27]], [[3451, 6922], [4, -5]], [[3455, 6917], [7, -4], [2, -3], [2, -11]], [[3466, 6899], [12, -23]], [[3478, 6876], [9, -8]], [[3487, 6868], [14, -2]], [[3501, 6866], [12, -8]], [[3513, 6858], [5, -6], [4, -2], [6, 6], [7, 16], [7, 9], [3, 2], [3, 1], [-1, -2]], [[3547, 6882], [-4, -4]], [[3543, 6878], [-8, -21]], [[3535, 6857], [-2, -9]], [[3533, 6848], [1, -10]], [[3534, 6838], [2, -7]], [[3536, 6831], [10, -9]], [[3546, 6822], [6, -7]], [[3552, 6815], [6, -9], [1, -1]], [[3559, 6805], [5, -1]], [[3564, 6804], [3, 2]], [[3567, 6806], [5, 8]], [[3572, 6814], [5, 0]], [[3577, 6814], [23, -6], [-1, -1]], [[3599, 6807], [-23, 2]], [[3576, 6809], [-2, -2]], [[3574, 6807], [0, -4]], [[3574, 6803], [1, -7]], [[3575, 6796], [7, -5]], [[3582, 6791], [24, -19]], [[3606, 6772], [-2, -1]], [[3604, 6771], [-15, 8]], [[3589, 6779], [-17, 8]], [[3572, 6787], [-3, 0]], [[3569, 6787], [-2, -2]], [[3567, 6785], [-2, -4]], [[3565, 6781], [9, -11], [7, -25], [-2, -20], [43, -14]], [[3622, 6711], [7, 1]], [[3629, 6712], [0, -2], [-2, -9], [1, -4]], [[3628, 6697], [2, -4]], [[3630, 6693], [8, -9]], [[3638, 6684], [5, -3]], [[3643, 6681], [11, -3], [1, -14]], [[3655, 6664], [4, -2]], [[3659, 6662], [5, -1]], [[3664, 6661], [2, -2]], [[3666, 6659], [0, -6]], [[3666, 6653], [2, -4]], [[3668, 6649], [4, -4], [6, -8], [4, -2], [8, -3], [5, -6]], [[3695, 6626], [3, -6]], [[3698, 6620], [3, -4]], [[3701, 6616], [3, -1]], [[3704, 6615], [22, -3]], [[3726, 6612], [-14, -5]], [[3712, 6607], [-8, -1]], [[3704, 6606], [-13, -27]], [[3691, 6579], [-6, -10], [-5, -7], [-4, -3], [-1, -4]], [[3675, 6555], [5, -15]], [[3680, 6540], [3, -3], [10, -4]], [[3693, 6533], [3, 2]], [[3696, 6535], [6, 11], [3, 10]], [[3705, 6556], [2, 4]], [[3707, 6560], [0, 2]], [[3707, 6562], [-2, 6], [1, 2], [3, 3]], [[3709, 6573], [11, 7]], [[3720, 6580], [27, 9]], [[3747, 6589], [19, 9]], [[3766, 6598], [-3, -5]], [[3763, 6593], [-5, -6], [1, -4]], [[3759, 6583], [12, -18]], [[3771, 6565], [4, -8], [3, -15], [1, -7], [-1, -11], [2, -12], [2, -3], [6, -2], [0, -2], [-18, -6]], [[3770, 6499], [-5, -8]], [[3765, 6491], [0, -3], [5, -11]], [[3770, 6477], [4, -13]], [[3774, 6464], [6, -17]], [[3780, 6447], [18, -2]], [[3798, 6445], [4, -2]], [[3802, 6443], [3, -3], [14, 8], [11, 16], [-1, 9]], [[3829, 6473], [0, 4]], [[3829, 6477], [4, 9]], [[3833, 6486], [10, 19]], [[3843, 6505], [0, 5]], [[3843, 6510], [-2, 8], [-4, 14]], [[3837, 6532], [-2, 7]], [[3835, 6539], [-1, 8]], [[3834, 6547], [1, 8]], [[3835, 6555], [2, 7]], [[3837, 6562], [3, 6]], [[3840, 6568], [4, 6]], [[3844, 6574], [-3, -8], [-2, -8], [-1, -6], [1, -8], [1, -8], [2, -11], [5, -13], [-1, -11], [-5, -8], [-2, -10], [3, -6], [5, 6], [8, 16], [3, 8], [2, 12], [0, 14], [2, 3], [2, -7], [2, -5], [3, -2], [2, -1], [3, 2], [6, 1], [-17, -21], [-9, -14], [-4, -10], [-13, -22], [-2, -8], [-6, -11], [-7, -3], [-3, -6], [-2, -4], [-1, -9], [0, -7], [-3, -7], [2, -8], [8, -10], [16, -10], [15, 5], [8, 1], [13, -1], [-13, -7], [-20, -4], [-1, -3], [-2, -16], [-11, -12], [-8, -17], [6, -8], [14, -10], [13, -2], [6, -17], [11, -9], [9, -10], [8, 11], [14, 6], [2, 3], [1, 7], [1, 11], [2, 8], [11, 16], [4, 2], [4, 6], [1, -1], [1, -2], [3, 1], [14, 11], [5, 0], [9, -7], [-3, -2], [-7, -1], [-4, -3], [-3, -4], [-1, -3], [0, -5], [1, -6], [6, -12], [5, -6], [7, -3], [3, -1], [8, 2], [13, 0], [7, -5], [12, -14], [2, -6], [-11, 11], [-5, 6], [-12, 5], [-5, 0], [-9, -6], [-5, 1], [-22, 15], [-6, 1], [4, 13], [0, 5], [-6, 1], [-7, -3], [-7, -6], [-5, -13], [4, -10], [3, -9], [17, -5], [16, -10], [10, -12], [6, -31], [7, -6], [19, 10], [5, 0], [-5, -6], [-2, -6], [-5, -22], [-6, -22], [1, -8], [2, 0], [4, 5], [12, 15], [7, 2], [0, -1], [-6, -6], [-2, -5], [-1, -3], [2, -10], [0, -3], [-25, -18], [13, -19], [7, -25], [8, 19], [7, 24], [5, 12], [4, 4], [6, 4], [11, 2], [4, 2], [31, 32], [3, 3], [3, 3], [1, 5], [1, 4], [-1, 10], [1, 0], [3, -7], [1, -4], [0, -3], [-3, -8], [-15, -14], [-2, -4], [2, -5], [2, -8], [3, -2], [14, -8], [3, -5], [1, -4], [11, -14], [3, -6], [-2, 1], [-7, 7], [-6, 7], [-4, 8], [-4, 4], [-13, 3], [-5, 10], [-15, -5], [-9, -7], [-9, -5], [-10, -7], [-3, -13], [-2, -11], [11, 0], [-8, -13], [-3, -7], [0, -5], [1, -12], [3, -9], [5, -7], [1, -4], [2, -2], [3, -1], [2, 0], [3, 4], [2, 6], [10, 13], [8, 4], [24, 1], [26, -2], [-3, -1], [-18, -3], [-24, -2], [-5, -1], [-6, -4], [-16, -21], [-4, -10], [-2, -9], [0, -14], [9, -7], [12, -12], [6, -5], [7, -3], [16, -6], [3, 0], [5, 3], [25, 2], [5, 6], [4, 11], [2, -11], [-3, -11], [3, -3], [5, 1], [4, -2], [-2, -3], [-6, -5], [-3, -5], [0, -5], [2, -1], [9, -1], [30, 5], [6, 0], [0, -3], [-13, -2], [-13, -5], [-14, -2], [-2, -2], [0, -5], [3, -2], [13, -1], [11, -3], [6, 0], [4, -1], [10, 3], [5, 0], [2, -4], [4, -2], [4, 1], [2, 2], [2, 10], [3, 7], [1, 2], [1, -13], [1, -4], [3, -1], [20, -2], [12, 4], [5, 9], [4, 9], [1, 6], [-2, 6], [1, 5], [6, 7], [-1, -5], [1, -13], [1, -6], [-1, -3], [-6, -12], [-9, -14], [1, -6], [4, -1], [4, -7], [0, -5], [12, -22], [0, -6], [6, -7], [17, -18], [6, -3], [10, -1], [10, 2], [7, 8], [5, 2], [1, 2], [0, 9], [2, 3], [7, 9], [0, 4], [-7, 9], [1, 1], [4, -2], [5, -3], [3, -5], [0, -2], [-3, -5], [-5, -3], [-1, -4], [0, -22], [2, -7], [4, -6], [5, -3], [4, -1], [9, 3], [-2, -3], [-10, -7], [-6, -1], [-2, 6], [-6, 10], [-3, 2], [-2, 0], [-3, -3], [-2, -6], [2, -7], [6, -8], [5, -5], [2, -1], [25, -10], [2, 0], [6, 6], [2, -1], [5, 2], [1, 7], [-1, 3], [1, 4], [11, 12], [0, -1], [0, -3], [-3, -6], [-2, -18], [-1, -6], [-2, -2], [0, -2], [1, -1], [8, 0], [12, -3], [4, 1], [6, 8], [-2, -9], [-3, -3], [-9, -1], [-9, -1], [-3, -2], [2, -5], [0, -8], [1, -3], [3, -1], [5, 1], [0, -1], [-2, -4], [-1, -3], [3, -8], [0, -1], [1, 0], [1, 1], [2, 6], [2, 2], [3, 2], [3, 0], [8, -6], [2, -4]], [[4400, 5763], [6, -16], [2, -7]], [[4408, 5740], [3, -4]], [[4411, 5736], [4, 1], [1, 1]], [[4416, 5738], [2, 0]], [[4418, 5738], [1, -2]], [[4419, 5736], [3, -11]], [[4422, 5725], [-1, -13]], [[4421, 5712], [2, -2]], [[4423, 5710], [0, -4]], [[4423, 5706], [-4, -2]], [[4419, 5704], [-6, 1]], [[4413, 5705], [-2, -1]], [[4411, 5704], [-1, -3]], [[4410, 5701], [0, -2], [2, -2], [4, 0]], [[4416, 5697], [3, -2]], [[4419, 5695], [5, -4], [6, -10], [0, -3], [-5, -1]], [[4425, 5677], [-4, -4]], [[4421, 5673], [-1, -3], [1, -5], [2, -3]], [[4423, 5662], [8, -6]], [[4431, 5656], [0, 1]], [[4431, 5657], [-3, 10]], [[4428, 5667], [1, 1], [2, -2], [3, -6], [3, -4], [4, -5]], [[4441, 5651], [1, -3]], [[4442, 5648], [-5, -9]], [[4437, 5639], [-1, -4]], [[4436, 5635], [-5, -10]], [[4431, 5625], [-1, -4], [2, -4], [0, -4], [-2, -5], [0, -2], [2, -3], [1, -3]], [[4433, 5600], [-1, -9]], [[4432, 5591], [2, -16], [0, -2]], [[4434, 5573], [-5, -7]], [[4429, 5566], [-4, -2]], [[4425, 5564], [-3, 0]], [[4422, 5564], [-3, -3]], [[4419, 5561], [-4, -12]], [[4415, 5549], [-2, -3]], [[4413, 5546], [-6, -4]], [[4407, 5542], [-2, 0]], [[4405, 5542], [-6, 4]], [[4399, 5546], [-8, -2]], [[4391, 5544], [-9, 1], [3, 4], [3, 9]], [[4388, 5558], [2, 5]], [[4390, 5563], [7, 5]], [[4397, 5568], [2, 0]], [[4399, 5568], [2, -13], [1, 0]], [[4402, 5555], [4, 10]], [[4406, 5565], [1, 1], [4, -4], [4, -1], [2, 2]], [[4417, 5563], [2, 13]], [[4419, 5576], [-1, 7]], [[4418, 5583], [-2, 8]], [[4416, 5591], [-7, 10]], [[4409, 5601], [1, 0], [3, -1]], [[4413, 5600], [1, 1]], [[4414, 5601], [-1, 9]], [[4413, 5610], [1, 3]], [[4414, 5613], [7, 6]], [[4421, 5619], [1, 6]], [[4422, 5625], [-2, 12]], [[4420, 5637], [-4, 1]], [[4416, 5638], [0, -1]], [[4416, 5637], [0, -7]], [[4416, 5630], [-2, -3]], [[4414, 5627], [-8, -8]], [[4406, 5619], [-3, -8]], [[4403, 5611], [-5, -5]], [[4398, 5606], [-7, -6], [-5, -5], [-6, -10], [0, -3], [-1, -6], [4, 0]], [[4383, 5576], [8, 2]], [[4391, 5578], [-1, -1], [-8, -6], [-6, -1]], [[4376, 5570], [-2, 1]], [[4374, 5571], [-1, 3]], [[4373, 5574], [1, 6], [6, 14], [18, 28], [1, -1]], [[4399, 5621], [0, -6]], [[4399, 5615], [2, 1]], [[4401, 5616], [10, 17]], [[4411, 5633], [-2, 5]], [[4409, 5638], [-5, 10], [-2, 7], [0, 6], [-1, 2], [-6, -6], [-4, -1]], [[4391, 5656], [-5, -1]], [[4386, 5655], [-4, 1], [-7, 8]], [[4375, 5664], [-3, 1]], [[4372, 5665], [-7, -4]], [[4365, 5661], [-3, 0]], [[4362, 5661], [-53, 5], [-9, 4]], [[4300, 5670], [-6, 5]], [[4294, 5675], [-20, 7], [-15, 9]], [[4259, 5691], [-6, 0]], [[4253, 5691], [2, -11]], [[4255, 5680], [-2, -16]], [[4253, 5664], [3, -21]], [[4256, 5643], [3, -8], [13, -14], [6, -15], [5, -30]], [[4283, 5576], [9, -23]], [[4292, 5553], [2, -14]], [[4294, 5539], [1, -8], [2, -7], [1, 5], [1, 4], [5, -1], [4, -5], [11, -2]], [[4319, 5525], [-20, -12]], [[4299, 5513], [3, -14], [9, -4]], [[4311, 5495], [5, -6]], [[4316, 5489], [-2, -15], [-3, -12]], [[4311, 5462], [-5, 11]], [[4306, 5473], [-2, 9]], [[4304, 5482], [-1, -13]], [[4303, 5469], [1, -14]], [[4304, 5455], [-2, -11], [8, 3]], [[4310, 5447], [5, -4]], [[4315, 5443], [16, 3]], [[4331, 5446], [3, -3]], [[4334, 5443], [14, 0]], [[4348, 5443], [5, -6]], [[4353, 5437], [8, -6]], [[4361, 5431], [4, 0]], [[4365, 5431], [2, -2]], [[4367, 5429], [-8, -1]], [[4359, 5428], [-6, 1]], [[4353, 5429], [-5, 7]], [[4348, 5436], [-4, 1]], [[4344, 5437], [-12, -4]], [[4332, 5433], [-18, 0]], [[4314, 5433], [-4, 5], [-2, -1], [1, -5], [2, -4]], [[4311, 5428], [-1, -35]], [[4310, 5393], [3, -32]], [[4313, 5361], [-3, -11], [1, -9], [-8, -74], [1, -15], [-2, -15]], [[4302, 5237], [2, -11]], [[4304, 5226], [-4, -11]], [[4300, 5215], [-3, -32], [-1, -43], [-3, -5], [-4, -13], [-4, -15], [4, 3], [3, -2]], [[4292, 5108], [-6, -6]], [[4286, 5102], [-3, 0], [-2, -3], [-9, -39]], [[4272, 5060], [-3, -9]], [[4269, 5051], [-3, -15]], [[4266, 5036], [7, -16]], [[4273, 5020], [3, -11]], [[4276, 5009], [-1, -23]], [[4275, 4986], [1, -10]], [[4276, 4976], [4, -21]], [[4280, 4955], [10, -14]], [[4290, 4941], [1, -2]], [[4291, 4939], [0, -11]], [[4291, 4928], [-3, -12]], [[4288, 4916], [7, -8]], [[4295, 4908], [3, -12]], [[4298, 4896], [4, -19]], [[4302, 4877], [0, -9]], [[4302, 4868], [-6, -27]], [[4296, 4841], [1, -22], [-5, -17]], [[4292, 4802], [-2, -4]], [[4290, 4798], [0, -1], [3, -1]], [[4293, 4796], [-2, -3]], [[4291, 4793], [-1, -6], [-2, 1], [1, 3], [-1, 1]], [[4288, 4792], [-3, -3]], [[4285, 4789], [-3, -13]], [[4282, 4776], [-3, -13]], [[4279, 4763], [1, -14]], [[4280, 4749], [2, -14]], [[4282, 4735], [17, -18]], [[4299, 4717], [17, -28]], [[4316, 4689], [4, -10]], [[4320, 4679], [4, -18]], [[4324, 4661], [0, -13]], [[4324, 4648], [-3, -17]], [[4321, 4631], [8, -30]], [[4329, 4601], [1, -24]], [[4330, 4577], [22, -27], [10, -17], [13, -10], [6, -17], [4, -3]], [[4385, 4503], [6, -10]], [[4391, 4493], [3, -8]], [[4394, 4485], [-8, 12]], [[4386, 4497], [1, -15]], [[4387, 4482], [-3, -10]], [[4384, 4472], [0, -3]], [[4384, 4469], [5, 8], [5, -4]], [[4394, 4473], [9, -9]], [[4403, 4464], [6, -5]], [[4409, 4459], [7, -4]], [[4416, 4455], [5, -5]], [[4421, 4450], [4, 1]], [[4425, 4451], [-2, 14]], [[4423, 4465], [1, 18]], [[4424, 4483], [7, 4]], [[4431, 4487], [4, -1], [10, -7], [4, -1]], [[4449, 4478], [10, 7]], [[4459, 4485], [3, -1]], [[4462, 4484], [4, -4], [4, -1]], [[4470, 4479], [10, 1]], [[4480, 4480], [6, -1], [4, 1], [8, -3]], [[4498, 4477], [-4, -1]], [[4494, 4476], [-5, 1]], [[4489, 4477], [-6, -3]], [[4483, 4474], [-29, 2]], [[4454, 4476], [-10, -1], [-7, -4]], [[4437, 4471], [-6, -5]], [[4431, 4466], [2, -5]], [[4433, 4461], [2, -3], [3, -13], [6, -6], [5, -13]], [[4449, 4426], [3, -10]], [[4452, 4416], [2, -3], [2, -4], [-4, 0], [-4, 3]], [[4448, 4412], [-4, 7]], [[4444, 4419], [-6, 3]], [[4438, 4422], [-5, 8], [-2, 10]], [[4431, 4440], [1, 5]], [[4432, 4445], [-5, 1], [-5, -3], [1, -14], [0, -13], [7, -20]], [[4430, 4396], [1, -19]], [[4431, 4377], [17, -26]], [[4448, 4351], [22, -6]], [[4470, 4345], [6, -10]], [[4476, 4335], [1, -6]], [[4477, 4329], [0, -8]], [[4477, 4321], [-3, -9]], [[4474, 4312], [-7, -10]], [[4467, 4302], [1, -16]], [[4468, 4286], [3, -12], [16, -21], [15, -27], [3, -7], [7, -9]], [[4512, 4210], [4, -13]], [[4516, 4197], [11, -8]], [[4527, 4189], [9, -15]], [[4536, 4174], [10, -7], [3, -7]], [[4549, 4160], [-2, -10]], [[4547, 4150], [2, -8]], [[4549, 4142], [12, -6]], [[4561, 4136], [3, -4], [2, -6]], [[4566, 4126], [-2, -15]], [[4564, 4111], [3, -16], [-1, -7], [1, -10], [-2, -10], [7, -4]], [[4572, 4064], [6, -9]], [[4578, 4055], [6, -1]], [[4584, 4054], [18, 2], [9, -1]], [[4611, 4055], [15, -7]], [[4626, 4048], [11, -1]], [[4637, 4047], [8, 2]], [[4645, 4049], [15, -10]], [[4660, 4039], [11, -9]], [[4671, 4030], [3, -11], [7, -6]], [[4681, 4013], [24, -11]], [[4705, 4002], [18, 2], [7, -2], [9, -19], [-2, -14]], [[4737, 3969], [9, -3]], [[4746, 3966], [2, 5]], [[4748, 3971], [8, -1]], [[4756, 3970], [7, -3]], [[4763, 3967], [9, -12]], [[4772, 3955], [13, -10]], [[4785, 3945], [25, -28]], [[4810, 3917], [11, -23]], [[4821, 3894], [4, -19], [1, -8], [-1, -8]], [[4825, 3859], [2, -17]], [[4827, 3842], [5, 3]], [[4832, 3845], [3, -4]], [[4835, 3841], [1, -13], [0, -1]], [[4836, 3827], [5, -22], [2, -5], [9, -12], [5, -24], [18, -17], [0, -11], [-3, -2], [-3, 0], [-2, -1], [4, -4], [1, -16], [4, -8], [11, -16], [10, -18], [2, -9], [0, -9], [1, -10], [18, -19], [2, -12], [0, -17], [3, -17], [10, -7], [4, -6], [2, -26], [-2, -15], [5, -3], [6, -21], [8, -9], [20, -17], [11, -12], [13, -5], [4, -4], [5, -8], [16, -22], [28, -43], [12, -15], [8, -21], [-4, -24], [-7, -25], [-6, -9], [1, -3], [7, 1], [0, -6], [-1, -4], [3, -12], [5, -6], [-5, -1], [-7, 6], [-6, 6], [1, 8], [-1, 4], [-5, -4], [-6, -5], [-9, -2], [-20, 6], [-16, 1], [0, -5], [3, -7], [10, -9], [11, -14], [14, -13], [3, -7], [2, -10], [2, -8], [9, -7], [10, -2], [7, -4], [9, -14], [4, -1], [8, -2], [6, -11], [4, -9], [8, -9], [14, 9], [11, 0], [6, 7], [3, 11], [1, -18], [2, -9], [8, -16], [28, -31], [10, -5], [11, -7], [16, -36], [4, -17], [0, -16], [2, -5], [2, -2], [1, -10], [-2, -19], [-4, -33], [1, -13], [4, -11], [17, -20], [2, -4], [0, -7], [1, -4], [4, 1], [6, 1], [8, -14], [12, -13], [29, -26], [11, -16], [10, -11], [11, -17], [20, -15], [6, -10], [3, -13], [5, -8], [7, -40], [7, -13], [6, -1], [8, 4], [7, 7], [7, 12], [11, 9], [3, 7], [3, 22], [0, 9], [-7, 14], [-13, 7], [-3, 17], [-5, 7], [-2, 9], [-7, 11], [-7, 9], [-3, 8], [-18, 20], [-3, -1], [-2, -4], [0, -5], [2, -8], [-2, -6], [-3, -5], [-3, 4], [0, 4], [-1, 2], [-10, 3], [-9, 15], [-5, 30], [0, 9], [4, 14], [-1, 9], [-5, 15], [-11, 18], [-9, 32], [-1, 13], [-9, 5], [-11, 26], [-3, 16], [0, 23], [-6, 17], [-1, 10], [-4, 7], [0, 11], [-6, 9], [-2, 15], [-17, 20], [-2, -2], [0, -13], [3, -8], [6, -12], [-1, -2], [-3, 1], [-8, 12], [-2, 19], [-9, 15], [-1, 5], [1, 8], [-7, 8], [-7, 5], [-7, 19], [-4, 21], [-17, 15], [-14, 20], [-2, 9], [1, 11], [-5, 12], [1, 13], [-6, 10], [1, 7], [-1, 9], [-6, 3], [-6, 2], [-5, 5], [-8, 34], [-5, 2], [-4, -1], [-1, 3], [0, 4], [-4, 9], [-9, -2], [-3, 11], [-1, 9], [-16, 32], [-5, 8], [-18, 20], [-9, 15], [-15, 11], [-3, 8], [-11, 15], [-6, 15], [-2, 10], [2, 32], [-4, 13], [-2, 17], [-4, 23], [-7, 14], [-2, 9], [2, 45], [5, 13], [-4, 18], [-4, 6], [-3, 6], [14, -11], [4, -4], [7, -2], [4, -3], [22, -21], [9, -6], [5, 1], [6, 1], [2, 8], [2, 5], [15, -9], [4, -4], [6, -6], [0, -15], [11, -6], [20, -4], [3, -3], [8, -3], [3, -3], [0, -11], [-2, -3], [-3, 2], [-1, -4], [1, -2], [2, -11], [-2, -17], [4, -16], [8, -17], [10, -25], [5, -20], [1, -17], [4, -8], [3, -5], [6, -18], [12, -21], [2, -9], [0, -5], [1, -9], [6, -3], [6, -6], [2, -18], [3, -11], [9, -15], [8, -8], [2, -3], [0, -6], [6, -12], [7, -10], [5, -11], [16, -10], [14, -32], [13, -17], [10, -5], [5, -4], [6, 3], [7, 0], [17, -6], [-3, -8], [-3, -17], [1, -13], [3, -11], [3, -6], [3, -9], [8, -10], [8, -9], [25, -10], [2, -5], [0, -6], [2, -12], [5, -13], [6, -8], [6, -1], [15, 2], [16, -21], [2, -10], [0, -5], [2, -6], [6, -6], [2, -6], [-3, 1], [-3, 5], [-6, -7], [-6, -12], [-6, -13], [3, -36], [7, -11], [3, -3], [5, -1], [9, 2], [5, 3], [6, 6], [4, 5], [0, -4], [-7, -13], [-7, -4], [0, -4], [3, -5], [10, 4], [4, 4], [4, -1], [3, -13], [4, -5], [18, -14], [7, -8], [22, -12], [3, -3], [1, -4], [-3, -2], [-5, 0], [-4, 1], [-4, 6], [-3, 0], [6, -12], [15, -23], [0, -10], [5, -9], [10, -9], [8, -2], [3, -3], [6, -1], [4, 2], [2, -2], [2, -8], [0, -6], [-3, -1], [-15, 13], [-3, 0], [52, -54], [12, -16], [16, -32], [12, -19], [13, -29], [13, -16], [16, -27], [6, -7], [12, -17], [11, -36], [0, -39], [9, -20], [5, -18], [2, -6], [18, -15], [-2, -13], [0, -15], [-1, -16], [-5, -11], [-7, -11], [-4, -10], [-5, -4], [2, -4], [5, 0], [7, -3], [6, -10], [1, -4], [-1, -6], [-9, -8], [-13, -2], [-8, -7], [-2, -6], [4, -8], [4, -10], [3, -18], [4, -12], [15, -32], [13, -17], [5, -14], [8, -16], [26, -18], [15, -7], [10, -10], [18, -11], [10, -10], [17, -23], [9, -18], [11, -18], [32, -17], [25, -14], [11, -3], [26, -10], [17, 2], [6, -1], [5, -5], [7, -9], [12, -23], [9, -4], [8, -12], [18, -14], [11, -14], [12, -9], [32, -16], [15, -9], [16, -8], [26, -23], [26, -7], [27, -9], [7, -5], [11, -1], [19, -27], [29, -12], [30, -28], [44, -7], [29, -21], [23, -9], [7, 4], [15, 1], [34, 23], [24, 10], [25, 24], [15, 4], [11, 0], [1, 2], [-4, 2], [-14, 4], [0, 3], [7, 9], [2, 4], [4, 1], [5, -7], [-1, -4], [1, -5], [3, 0], [8, 8], [2, -1], [3, -4], [-8, -10], [2, -3], [15, -2], [36, -20], [-2, 5], [-13, 10], [-4, 3], [-5, 0], [-5, 3], [-4, 4], [1, 7], [3, 0], [5, -6], [5, -4], [13, -7], [12, -11], [14, -19], [15, -16], [29, -36], [11, -16], [8, -9], [8, -11], [22, -35], [20, -32], [2, -3], [32, -37], [14, -14], [20, -14], [18, -8], [42, 1], [10, -4], [29, -19], [10, -6], [12, -15], [22, -6], [19, -4], [32, -22], [27, -12], [2, 1], [-8, 6], [-5, 2], [-3, 2], [8, 0], [13, -8], [18, -5], [12, 0], [8, 2], [4, 5], [4, 7], [-1, 12], [2, 1], [3, -2], [5, -2], [8, 3], [9, -4], [0, -5], [-1, -4], [3, -7], [3, -11], [7, -5], [-1, -12], [0, -4], [-4, -3], [-3, 0], [-5, 7], [-4, 7], [-3, 0], [-6, -9], [0, -7], [16, -17], [21, -30], [5, -8], [21, -22], [7, -11], [8, -21], [14, -28], [39, -48], [10, -16], [7, -13], [0, -3], [0, -2], [-1, -7], [-10, -7], [-2, -3], [6, -6], [9, -7], [4, -5], [-1, -8], [1, -5], [-3, -8], [-10, -20], [-1, -12], [4, -19], [9, -21], [4, -6], [24, -11], [12, -22], [3, -5], [3, 3], [1, 7], [5, 4], [9, 14], [-2, 8], [-4, 6], [-15, 9], [-6, 11], [-2, 18], [2, -2], [3, -6], [13, -9], [24, -25], [5, -13], [-2, -11], [1, -6], [6, -9], [8, -5], [20, -8], [8, -10], [17, -12], [12, -15], [8, -13], [1, -9], [1, -18], [-3, -9], [-7, -14], [10, -15], [5, -4], [7, -1], [6, -3], [6, -1], [1, 7], [-1, 5], [-10, 13], [-3, 10], [6, 2], [8, -7], [9, -9], [3, -9], [0, -18], [7, -8], [7, -13], [5, -13], [2, 4], [-1, 17], [7, 7], [7, 2], [12, -4], [13, -2], [10, 5], [0, -10], [5, -4], [5, 3], [10, -1], [8, -5], [11, -4], [2, -8], [2, -6], [13, -35], [10, -5], [8, -6], [4, -1], [2, 6], [1, 16], [1, 6], [5, 3], [3, 2], [2, -22], [9, -32], [1, -19], [5, -7], [13, 1], [18, 6], [5, 6], [2, 7], [4, 5], [14, 0], [3, 3], [5, 5], [-3, 12], [-2, 8], [-15, 22], [-8, 17], [-3, 3], [-4, 6], [0, 8], [0, 8], [4, 6], [3, 3], [13, 3], [6, 4], [29, 29], [-5, 5], [4, 9], [2, 7], [4, 9], [8, 6], [2, 3], [3, 5], [5, 4], [15, 2], [13, -3], [10, -7], [8, -11], [6, -4], [5, -6], [3, -2], [4, -3], [8, -10], [3, -21], [3, -5], [2, -6], [2, 0], [1, 6], [-1, 4], [-1, 8], [2, -2], [1, -4], [8, -1], [0, -3], [2, -3], [3, 2], [2, 4], [5, 6], [3, -12], [3, -9], [13, -12], [7, -10], [-6, 2], [-9, 9], [-7, 7], [-5, 11], [-3, 0], [-2, -6], [-8, -10], [2, -13], [-3, -5], [-2, -3], [-4, 0], [-4, -1], [3, -19], [16, -42], [19, -33], [2, -4], [7, -10], [10, -21], [3, -11], [3, -4], [6, -17], [7, 0], [5, -13], [1, -9], [-3, -27], [-4, 0], [-2, 1], [0, -12], [10, -22], [7, -25], [-6, -12], [-16, -17], [10, -14], [3, -11], [1, -13], [-1, -16], [2, -28], [3, -6], [1, -8], [-2, -15], [-1, -14], [-2, -9], [-5, -6], [-2, -5], [-5, -5], [-1, -5], [9, 4], [0, -6], [-2, -8], [0, -8], [6, -14], [6, 13], [2, -2], [-1, -17], [4, -3], [3, -1], [4, 5], [3, 1], [-12, -38], [-7, -13], [-2, -15], [-5, -1], [-4, -13], [-4, -8], [-3, -10], [-5, -3], [-5, -1], [-1, -4], [3, -11], [0, -4], [-8, -11], [-3, -5], [0, -3], [-5, 1], [-2, -4], [-2, -8], [-5, -7], [-3, -3], [-3, -4], [-4, -2], [-13, 2], [-4, -6], [-6, 3], [-3, -1], [-6, -6], [-4, -8], [-2, -6], [-1, -29], [6, -16], [-2, -17], [-17, 8], [-12, -11], [-6, -15], [11, -12], [2, -8], [3, -19], [-6, -10], [-25, -12], [-18, -5], [-12, -11], [-10, 1], [-4, -7], [-8, -7], [-10, -3], [-4, -6], [2, -22], [3, -22], [-2, -30], [-6, -18]], [[9916, 0], [9, 15], [4, 4], [7, 1], [5, 6], [13, 23], [9, 26], [10, 13], [3, 10], [18, 21], [1, 5], [4, 3]], [[9999, 199], [-4, 5], [-10, 6], [-9, 1], [-12, 4], [-6, 12], [-3, 8], [-3, 13], [-4, 3], [-4, 0], [3, 5], [0, 4], [-5, 20], [-4, 11], [-2, 12], [-1, 9], [-12, 50], [-5, 24], [-2, 46], [-11, 50], [-8, 15], [-10, 10], [-7, 0], [-1, -9], [-7, -20], [-1, 4], [1, 5], [-1, 10], [-3, 7], [-6, 34], [-4, 7], [-4, 0], [-3, -13], [0, -10], [-2, -5], [-4, -5], [0, 4], [2, 5], [1, 10], [-4, 16], [-3, 8], [-13, 17], [-8, -11], [3, 13], [0, 7], [-13, 10], [-24, 29], [-10, 18], [-29, 14], [-14, 2], [-30, 26], [-6, -1], [-5, -11], [-7, -31], [-6, -7], [6, 17], [3, 13], [0, 13], [-1, 10], [-7, 6], [-16, 6], [-37, 9], [-24, 1], [-18, -5], [-20, 4], [-14, -3], [-7, -8], [2, -11], [-1, -12], [-3, 12], [-23, 10], [-17, 6], [-39, 7], [-7, -6], [-3, -13], [-3, -11], [0, -11], [-3, -14], [-1, 3], [-1, 2], [3, 37], [-2, 25], [-3, 10], [-9, 11], [-15, 7], [-5, 14], [-14, 17], [-15, 22], [-7, 4], [-8, 1], [-9, 6], [-9, -3], [-7, -14], [-5, -12], [-2, -21], [-6, -7], [4, 13], [1, 23], [2, 17], [8, 23], [0, 34], [-3, 8], [-9, 18], [-5, 7], [-9, 15], [-30, 40], [-21, 21], [-14, 13], [-6, 2], [0, -5], [-1, 0], [-7, 4], [-11, 19], [-3, 2], [-11, 8], [-13, 1], [-5, -2], [-6, -8], [-25, 6], [-5, -2], [-13, -10], [-12, -1], [-8, -9], [-11, 12], [-7, 4], [-7, 6], [29, 1], [5, 14], [5, 14], [1, 12], [1, 3], [3, 8], [0, 7], [2, 7], [4, 7], [10, 6], [4, 11], [-6, 11], [-11, 12], [-17, 5], [-6, 4], [-15, 25], [-6, 6], [-3, -9], [-11, -1], [1, -7], [2, -6], [-1, -8], [-1, 5], [-4, 4], [-2, 3], [0, 6], [-6, 10], [-8, 10], [-5, 2], [-3, 2], [-3, -1], [1, -3], [-2, -9], [1, -7], [-1, 1], [-2, 2], [-2, 5], [-3, -8], [-2, -3], [-1, -1], [-2, 0], [-6, 16], [-9, 30], [-2, 3], [-4, 2], [-1, -6], [1, -6], [-4, -5], [-7, -2], [4, 5], [2, 12], [-2, 10], [-1, 6], [-6, 7], [-3, 0], [-2, 3], [0, 10], [-5, 3], [17, 4], [24, -2], [11, 10], [15, 1], [9, 5], [3, 7], [-28, -5], [-35, 6], [-19, -5], [-7, 1], [-12, -1], [-23, -8], [-29, 2], [-25, -3], [-8, 0], [4, -11], [7, 4], [23, -2], [10, -6], [-4, -4], [-4, -3], [-27, 1], [-51, -42], [-7, 0], [-7, -3], [-8, 0], [-14, 6], [-13, 4], [-13, 8], [-15, 4], [-19, 25], [0, 5], [-1, 7], [-11, 7], [-57, -3], [-11, -5], [-34, -5], [-23, -6], [-20, 2], [-7, 9], [-5, 14], [-2, 14], [4, 9], [-4, 8], [-2, 12], [-4, 13], [-17, 17], [-16, 15], [-18, 3], [-13, 7], [-23, -2], [-3, -2], [-5, -1], [-6, 10], [-4, 14], [-4, 18], [-1, 19], [-3, 7], [-4, 7], [-7, 8], [-9, -5], [-6, -5], [-3, -11], [-3, -14], [5, -18], [0, -6], [2, -7], [22, 6], [7, 0], [3, -15], [-2, -8], [-6, -4], [-13, 10], [-4, -1], [-5, -11], [-5, -6], [-24, -13], [-21, -6], [-35, -25], [-15, -4], [0, -15], [-6, -7], [2, -18], [2, -11], [2, -7], [6, -25], [10, -14], [4, -15], [9, -21], [3, -15], [-2, -23], [-1, -19], [-9, -15], [-3, -7], [-4, -5], [-18, -9], [-7, 0], [-5, 3], [-4, 7], [-3, 14], [1, 10], [-3, 6], [-5, 5], [-8, 15], [-2, 10], [-9, 21], [12, 34], [6, 7], [7, 18], [10, 15], [5, 25], [0, 8], [-7, 13], [-3, 18], [4, 1], [2, 2], [-11, 14], [-3, 6], [-9, 27], [-1, 8], [0, 10], [4, 5], [3, 2], [29, 11], [6, 4], [5, 7], [2, 5], [3, 7], [11, 15], [-1, 14], [-8, 20], [-18, 11], [-8, 1], [-9, -2], [-16, -13], [-1, -5], [-3, -3], [-6, 0], [-7, -6], [-2, -15], [-8, -20], [-14, -11], [-21, -10], [-45, -49], [-28, -3], [-9, 0], [-9, 4], [-12, 4], [-6, -2], [-4, -7], [-2, -19], [-6, -18], [-4, -16], [-4, -6], [-4, 3], [-5, 9], [2, 8], [7, 4], [4, 1], [2, 3], [-10, -1], [-30, 14], [-6, -6], [-15, -22], [-10, -11], [-2, -6], [-13, -14], [-4, -10], [-4, -23], [-9, -16], [-3, -6], [5, 4], [6, 7], [2, -4], [-5, -9], [1, -16], [-4, -18], [-3, -12], [3, -9], [3, -14], [-3, -10], [-9, -4], [-11, 1], [-10, -7], [-8, -12], [-11, -33], [-32, -34], [-9, -7], [-6, -2], [-3, -5], [4, -8], [4, -5], [4, -18], [2, -37], [-3, -8], [-6, -2], [-3, 3], [-2, 4], [1, 7], [3, 3], [2, 4], [-3, 4], [-4, 2], [-4, 13], [-11, 17], [-10, 11], [-6, 17], [-2, 3], [-25, 27], [-11, 21], [-19, 20], [-32, 20], [-15, 3], [-18, -1], [-4, 3], [-3, 7], [-7, 3], [-8, -1], [-11, 5], [-17, 3], [-6, -5], [-5, -9], [-10, -12], [-5, -2], [-5, -2], [-11, -16], [-33, -15], [-9, -7], [-13, -16], [-17, -8], [-11, -3], [-12, -1], [-14, 5], [-13, 23], [-7, 6], [-3, 6], [-4, 2], [-1, -3], [6, -8], [4, -10], [-4, -2], [-5, 2], [-14, -3], [-5, 5], [-8, 6], [1, 13], [2, 3], [1, 3], [-1, 3], [-5, -3], [-5, 2], [-3, 15], [1, 6], [-1, 5], [-10, 11], [-5, 7], [-3, 4], [-13, 6], [-3, 8], [-4, 4], [-12, 26], [-8, 6], [-17, 32], [-8, 18], [-9, 32], [-1, 9], [-3, 8], [-2, 4], [-5, 2], [-4, 9], [-5, 14], [-3, 20], [1, 7], [2, 8], [4, 9], [2, 8], [8, 9], [-1, 9], [-3, 12], [-4, 0], [-3, 1], [-3, 3], [1, 4], [4, 5], [-1, 5], [1, 10], [4, -1], [1, -3], [1, -1], [1, 24], [2, 7], [-2, 6], [-3, 8], [-3, 12], [3, 6], [2, 1], [5, 6], [2, 12], [1, -6], [-1, -10], [-2, -8], [0, -6], [2, -8], [2, 0], [4, 2], [0, 12], [-2, 10], [2, 41], [-4, 44], [6, 49], [6, 30], [5, 8], [5, 11], [6, 13], [1, 9], [-8, 48], [-3, 2], [-3, 0], [-3, 7], [2, 5], [3, 4], [3, -1], [1, -11], [1, 2], [5, 14], [3, 2], [2, 5], [-6, 6], [-5, 4], [-6, 19], [-21, 15], [-2, 0], [9, -9], [4, -8], [-2, -1], [-5, 6], [-6, -1], [-3, -5], [-4, 0], [-3, 9], [-5, 7], [-5, 5], [-6, 2], [-3, -2], [-3, 1], [-1, 3], [-1, 7], [3, 3], [8, 1], [16, -14], [-1, 4], [-37, 45], [-20, 6], [2, -3], [5, -2], [-1, -2], [-4, -2], [-5, 1], [-7, 9], [-25, 13], [-6, -2], [-9, -6], [-24, -3], [-23, 12], [-16, 3], [3, -3], [1, -5], [-10, -6], [-9, -2], [-13, -12], [-10, 2], [-21, -1], [-12, -4], [-29, 9], [-7, -1], [-8, -5], [-5, 5], [-5, 10], [-7, 0], [-13, -4], [-3, -2], [-8, -9], [-3, -2], [-6, -8], [-7, 3], [-28, 26], [-1, -2], [2, -3], [3, -7], [-5, -10], [-8, 5], [-7, 7], [-3, 1], [-5, 2], [-1, 8], [3, 7], [14, 27], [10, 5], [8, 17], [4, 7], [7, 17], [4, 38], [-2, 27], [0, 15], [2, 9], [5, 15], [-6, 11], [4, 16], [1, 11], [9, 33], [1, 12], [-4, 15], [-9, 0], [-4, -1], [-4, 2], [4, 13], [2, 5], [6, 15], [0, 9], [5, 6], [5, 8], [3, 0], [1, -13], [-3, -24], [1, -5], [6, -5], [6, -19], [3, -1], [3, 10], [4, 11], [2, 24], [6, 17], [4, 30], [8, 28], [-1, 3], [-3, 1], [-6, -8], [-2, 0], [-1, 12], [3, 3], [5, 4], [4, 1], [2, 2], [4, 7], [0, 10], [-3, 0], [-3, -1], [-6, -1], [-5, -2], [-3, 5], [0, 5], [8, 17], [6, 5], [3, 0], [2, 4], [1, 5], [-3, 12], [0, 12], [4, 15], [15, 32], [13, 15], [10, 18], [5, 12], [3, 14], [4, 17], [-3, 6], [-1, 4], [0, 22], [-7, 5], [-10, 15], [-7, 3], [-7, -4], [-4, -1], [-7, 0], [-2, -3], [7, -3], [4, 0], [3, 2], [2, 1], [1, -4], [-1, -5], [-3, -2], [-2, -1], [-18, 3], [-16, 7], [-6, 2], [-18, 6], [-10, 2], [-3, -2], [0, -1], [-1, -2], [-5, -1], [-17, 0], [-9, -3], [-12, -11], [-10, -4], [-73, -16], [-5, -3], [-23, -15], [-13, -14], [-6, -29], [-2, -5], [-2, -19], [1, -21], [-1, -18], [0, -23], [0, -10], [-2, -4], [-11, -13], [-3, -8], [-3, -44], [-17, -24], [-8, -6], [-6, -8], [-23, -17], [-2, -3], [-1, -4], [2, 0], [8, 8], [1, -2], [-3, -9], [1, -3], [4, -1], [2, -6], [0, -11], [-12, -10], [-8, -10], [-5, -1], [-15, 3], [-9, 7], [-2, 4], [5, 4], [0, 4], [-7, 10], [-10, -2], [-9, -2], [-17, -1], [-4, -1], [-17, -7], [-2, -4], [-3, -6], [-8, -6], [-19, -6], [-8, 3], [-25, -2], [-16, -8], [-9, -7], [-24, -13], [-15, -3], [-6, 0], [-6, 1], [-11, 20], [-9, 20], [-16, 7], [-13, 15], [-29, 2], [-11, 7], [-2, -1], [5, -5], [6, -4], [-3, 0], [-15, 7], [-8, 8], [-1, 3], [9, -5], [3, 0], [-10, 11], [-6, 18], [-7, 7], [-4, 10], [-12, 18], [-2, 15], [-4, 11], [-7, 35], [-20, 38], [-31, 50], [-5, 12], [-1, 10], [-12, 36], [-12, 34], [0, 4], [-1, 6], [-4, 3], [-2, 3], [-3, 8], [-9, 50], [13, -31], [10, -18], [5, -5], [0, -5], [-3, -7], [0, -13], [2, -10], [1, 12], [4, 8], [2, 15], [-4, 6], [-9, 10], [-8, 12], [-14, 35], [-1, 21], [-5, 27], [0, 6], [-1, 7], [3, 18], [5, 13], [1, 7], [-2, 43], [3, 50], [-1, 3], [1, 3], [1, 23], [4, 48], [12, 74], [6, 25], [16, 42], [4, 20], [2, 24]], [[6373, 3054], [0, 8]], [[6373, 3062], [-1, 4]], [[6372, 3066], [-4, 1]], [[6368, 3067], [-15, 38]], [[6353, 3105], [-2, 11]], [[6351, 3116], [-3, 24], [-2, 8]], [[6346, 3148], [-3, 17]], [[6343, 3165], [-2, 7]], [[6341, 3172], [3, 10]], [[6344, 3182], [3, 8]], [[6347, 3190], [0, 6]], [[6347, 3196], [0, 8]], [[6347, 3204], [-16, 6]], [[6331, 3210], [-6, 20], [7, -7]], [[6332, 3223], [12, -10]], [[6344, 3213], [3, 0]], [[6347, 3213], [3, 2]], [[6350, 3215], [5, 10]], [[6355, 3225], [7, 30]], [[6362, 3255], [-11, 20]], [[6351, 3275], [2, 2]], [[6353, 3277], [2, 1]], [[6355, 3278], [10, -1]], [[6365, 3277], [6, 3], [7, 12]], [[6378, 3292], [3, 13]], [[6381, 3305], [-8, -4]], [[6373, 3301], [-1, 5]], [[6372, 3306], [0, 5], [5, 1]], [[6377, 3312], [6, 1]], [[6383, 3313], [4, 3], [2, 4], [1, -4], [2, -4]], [[6392, 3312], [4, 5]], [[6396, 3317], [3, 3]], [[6399, 3320], [1, 12], [1, 11]], [[6401, 3343], [8, -9]], [[6409, 3334], [9, 3]], [[6418, 3337], [5, 5], [6, 6]], [[6429, 3348], [-5, 2]], [[6424, 3350], [-3, 1]], [[6421, 3351], [-9, 26]], [[6412, 3377], [2, 2]], [[6414, 3379], [3, -1]], [[6417, 3378], [1, -4], [2, -4]], [[6420, 3370], [6, -6]], [[6426, 3364], [6, 4]], [[6432, 3368], [0, 3]], [[6432, 3371], [8, 0]], [[6440, 3371], [12, -4]], [[6452, 3367], [8, 1], [-9, -8], [-8, -9], [4, 2], [12, 10], [13, 6]], [[6472, 3369], [9, 9]], [[6481, 3378], [6, 3], [21, 18]], [[6508, 3399], [9, 8]], [[6517, 3407], [9, 14]], [[6526, 3421], [1, 10], [9, 11]], [[6536, 3442], [10, 13]], [[6546, 3455], [-3, 11]], [[6543, 3466], [-5, 8]], [[6538, 3474], [-2, 20]], [[6536, 3494], [3, 1]], [[6539, 3495], [4, -4], [3, 0], [5, 9]], [[6551, 3500], [7, 0]], [[6558, 3500], [1, -11], [-4, -13]], [[6555, 3476], [3, -2]], [[6558, 3474], [10, 4]], [[6568, 3478], [6, -2]], [[6574, 3476], [-17, -15]], [[6557, 3461], [-1, -4]], [[6556, 3457], [15, 11]], [[6571, 3468], [36, 22]], [[6607, 3490], [16, 3], [0, 3], [-4, 11], [8, 20], [4, -1]], [[6631, 3526], [1, -3]], [[6632, 3523], [0, -4]], [[6632, 3519], [-3, -7]], [[6629, 3512], [-3, -4]], [[6626, 3508], [-2, -1]], [[6624, 3507], [1, -7]], [[6625, 3500], [3, -3]], [[6628, 3497], [5, 3]], [[6633, 3500], [5, 2]], [[6638, 3502], [24, 1]], [[6662, 3503], [8, 1]], [[6670, 3504], [8, -1]], [[6678, 3503], [17, -8]], [[6695, 3495], [13, -9]], [[6708, 3486], [9, -4]], [[6717, 3482], [31, -5]], [[6748, 3477], [14, 4]], [[6762, 3481], [2, 3], [-4, 6], [-2, 4], [4, 7], [5, 5]], [[6767, 3506], [10, 4]], [[6777, 3510], [5, -10]], [[6782, 3500], [12, -1]], [[6794, 3499], [8, -16]], [[6802, 3483], [4, -6], [14, -5], [4, 6], [2, 0]], [[6826, 3478], [-1, -7]], [[6825, 3471], [2, -6]], [[6827, 3465], [7, -12]], [[6834, 3453], [0, -4]], [[6834, 3449], [-7, 1]], [[6827, 3450], [-3, -1]], [[6824, 3449], [-1, -4]], [[6823, 3445], [22, -11]], [[6845, 3434], [20, -7]], [[6865, 3427], [5, 2]], [[6870, 3429], [7, 14], [7, 4]], [[6884, 3447], [9, -1]], [[6893, 3446], [6, -4]], [[6899, 3442], [4, -15]], [[6903, 3427], [3, -3], [6, 3], [3, 6], [1, 7], [1, 6], [1, 5]], [[6918, 3451], [0, 11]], [[6918, 3462], [-3, 4]], [[6915, 3466], [-3, 2]], [[6912, 3468], [-2, 3], [0, 4]], [[6910, 3475], [22, -10]], [[6932, 3465], [4, -5]], [[6936, 3460], [2, -4]], [[6938, 3456], [0, -5], [6, -3]], [[6944, 3448], [4, 1]], [[6948, 3449], [4, -2]], [[6952, 3447], [3, -4]], [[6955, 3443], [4, -2]], [[6959, 3441], [6, -7]], [[6965, 3434], [4, -10]], [[6969, 3424], [3, -4], [-2, -11]], [[6970, 3409], [4, 2]], [[6974, 3411], [7, 10]], [[6981, 3421], [3, -3]], [[6984, 3418], [3, -5]], [[6987, 3413], [2, 4]], [[6989, 3417], [2, 6], [7, 5]], [[6998, 3428], [0, 7]], [[6998, 3435], [-4, 2], [-4, 4], [-5, 10]], [[6985, 3451], [-5, 0]], [[6980, 3451], [-20, 10]], [[6960, 3461], [-5, 8]], [[6955, 3469], [-8, 6]], [[6947, 3475], [-3, 9], [2, 3]], [[6946, 3487], [1, 4]], [[6947, 3491], [1, 1]], [[6948, 3492], [4, -1]], [[6952, 3491], [4, 3]], [[6956, 3494], [-2, 3], [4, 5]], [[6958, 3502], [6, 2]], [[6964, 3504], [8, 4], [-1, 2]], [[6971, 3510], [1, 10]], [[6972, 3520], [-4, 6], [-1, 4]], [[6967, 3530], [1, 4]], [[6968, 3534], [-7, 2], [-5, -7], [-2, -10], [-3, -1], [-9, 3]], [[6942, 3521], [-5, 9]], [[6937, 3530], [3, 4]], [[6940, 3534], [4, 4]], [[6944, 3538], [4, 5]], [[6948, 3543], [0, 3]], [[6948, 3546], [-5, 3]], [[6943, 3549], [-3, -4]], [[6940, 3545], [-3, -1]], [[6937, 3544], [-6, 0]], [[6931, 3544], [-8, -8], [-14, -4], [-9, 5], [-9, 9], [6, 16]], [[6897, 3562], [8, 12]], [[6905, 3574], [8, -2]], [[6913, 3572], [6, -2]], [[6919, 3570], [7, -9], [28, -12]], [[6954, 3549], [5, 3]], [[6959, 3552], [6, 3]], [[6965, 3555], [10, 15]], [[6975, 3570], [4, -1]], [[6979, 3569], [3, -1], [13, 4]], [[6995, 3572], [12, 6]], [[7007, 3578], [2, 0], [4, -1]], [[7013, 3577], [10, -6]], [[7023, 3571], [22, 2]], [[7045, 3573], [4, 0]], [[7049, 3573], [8, -1], [9, 0], [1, 6], [3, 18], [4, 13], [1, 2]], [[7075, 3611], [5, -8]], [[7080, 3603], [2, -8], [0, -13]], [[7082, 3582], [2, -4]], [[7084, 3578], [3, -1], [4, -7], [1, -7], [-8, -3]], [[7084, 3560], [-7, -1]], [[7077, 3559], [-1, -3], [29, 4]], [[7105, 3560], [9, 12]], [[7114, 3572], [1, 1]], [[7115, 3573], [4, 2]], [[7119, 3575], [0, -3], [-4, -7], [2, -1], [15, 5], [2, 7], [5, 6], [1, 10]], [[7140, 3592], [4, 0]], [[7144, 3592], [4, -4]], [[7148, 3588], [3, 6]], [[7151, 3594], [2, 2], [3, -8], [-2, -8], [-10, -4], [-3, -3], [-3, -4], [18, 4]], [[7156, 3573], [22, 3]], [[7178, 3576], [5, 3]], [[7183, 3579], [7, 5]], [[7190, 3584], [6, 3]], [[7196, 3587], [5, -1]], [[7201, 3586], [9, 1]], [[7210, 3587], [7, -3], [2, -3], [2, -4], [-9, 3]], [[7212, 3580], [-17, -4]], [[7195, 3576], [22, -8]], [[7217, 3568], [24, -14]], [[7241, 3554], [5, -5], [3, 0], [1, 4], [0, 5]], [[7250, 3558], [5, 4]], [[7255, 3562], [6, 1]], [[7261, 3563], [-3, -6]], [[7258, 3557], [-2, -6]], [[7256, 3551], [4, -4]], [[7260, 3547], [-1, -4]], [[7259, 3543], [-4, 0]], [[7255, 3543], [13, -17]], [[7268, 3526], [12, -12], [3, -9]], [[7283, 3505], [0, -4]], [[7283, 3501], [-2, -3], [-4, 6], [-2, 7], [0, -9]], [[7275, 3502], [3, -9]], [[7278, 3493], [4, -2], [11, 4], [12, 1], [4, 3]], [[7309, 3499], [7, 4]], [[7316, 3503], [6, -1]], [[7322, 3502], [20, 15], [7, 1]], [[7349, 3518], [5, 0]], [[7354, 3518], [2, 3]], [[7356, 3521], [-1, 6]], [[7355, 3527], [2, 5]], [[7357, 3532], [3, 5]], [[7360, 3537], [20, 4]], [[7380, 3541], [27, -21]], [[7407, 3520], [31, -55]], [[7438, 3465], [41, -48]], [[7479, 3417], [9, -19]], [[7488, 3398], [0, -9]], [[7488, 3389], [0, -5]], [[7488, 3384], [-1, -33]], [[7487, 3351], [-7, -29]], [[7480, 3322], [-7, -46]], [[7473, 3276], [3, -8]], [[7476, 3268], [2, -5], [3, -3]], [[7481, 3260], [2, 2]], [[7483, 3262], [4, -1], [3, 3], [1, 4], [1, 11], [-3, 3], [-3, 8], [3, 2]], [[7489, 3292], [4, -3]], [[7493, 3289], [5, -10], [1, -1]], [[7499, 3278], [4, 4]], [[7503, 3282], [3, -4]], [[7506, 3278], [1, -4]], [[7507, 3274], [-2, -7], [-7, -11]], [[7498, 3256], [-9, -18]], [[7489, 3238], [-4, -1]], [[7485, 3237], [-2, -2]], [[7483, 3235], [4, -6]], [[7487, 3229], [3, -6]], [[7490, 3223], [14, -40], [6, -14]], [[7510, 3169], [5, -8]], [[7515, 3161], [4, -3]], [[7519, 3158], [5, -1]], [[7524, 3157], [1, 4]], [[7525, 3161], [-1, 8]], [[7524, 3169], [6, 3]], [[7530, 3172], [7, 0], [-4, -9], [-1, -22]], [[7532, 3141], [3, -18]], [[7535, 3123], [2, -1]], [[7537, 3122], [3, 1]], [[7540, 3123], [4, 10]], [[7544, 3133], [3, 4], [4, 2]], [[7551, 3139], [-5, -10]], [[7546, 3129], [-5, -13], [2, -2], [5, -4], [4, -34], [8, -19], [11, -11], [16, -7]], [[7587, 3039], [1, -12]], [[7588, 3027], [9, -17]], [[7597, 3010], [9, -26], [4, -3]], [[7610, 2981], [6, -3]], [[7616, 2978], [3, -6], [-2, -5]], [[7617, 2967], [-10, 11]], [[7607, 2978], [-3, -1]], [[7604, 2977], [-2, -4]], [[7602, 2973], [0, -5]], [[7602, 2968], [4, -11]], [[7606, 2957], [8, 0]], [[7614, 2957], [11, 5]], [[7625, 2962], [10, -3]], [[7635, 2959], [14, 9]], [[7649, 2968], [5, 0], [9, 12]], [[7663, 2980], [3, 11]], [[7666, 2991], [2, 23]], [[7668, 3014], [7, 14], [4, 16], [2, 0]], [[7681, 3044], [0, -4]], [[7681, 3040], [1, -1]], [[7682, 3039], [1, 35]], [[7683, 3074], [5, 51], [0, 29], [-3, 21], [-3, 11], [-8, 15]], [[7674, 3201], [-32, 114]], [[7642, 3315], [-3, 11]], [[7639, 3326], [-5, 13]], [[7634, 3339], [-3, 21]], [[7631, 3360], [-2, 9], [-2, 14]], [[7627, 3383], [2, 0]], [[7629, 3383], [3, -3]], [[7632, 3380], [0, -6], [1, -6]], [[7633, 3368], [5, -4]], [[7638, 3364], [1, -2]], [[7639, 3362], [-4, -7]], [[7635, 3355], [0, -7]], [[7635, 3348], [3, -14]], [[7638, 3334], [2, 4]], [[7640, 3338], [1, 9]], [[7641, 3347], [2, 8]], [[7643, 3355], [2, 0]], [[7645, 3355], [-1, -24], [1, -16], [8, -29]], [[7653, 3286], [3, -4]], [[7656, 3282], [-5, 20], [-4, 13], [0, 11], [0, 11], [1, 7]], [[7648, 3344], [3, 7]], [[7651, 3351], [-3, 8]], [[7648, 3359], [-26, 58]], [[7622, 3417], [-15, 48], [-12, 40], [-6, 41], [-4, 15]], [[7585, 3561], [-6, 43]], [[7579, 3604], [-3, 11]], [[7576, 3615], [-1, 8]], [[7575, 3623], [0, 9]], [[7575, 3632], [1, 5]], [[7576, 3637], [2, 11]], [[7578, 3648], [2, 9], [-1, 5], [2, 8], [2, -2], [4, -1], [5, 11]], [[7592, 3678], [-3, 6]], [[7589, 3684], [-3, 2]], [[7586, 3686], [-1, 2]], [[7585, 3688], [7, 2]], [[7592, 3690], [3, 8], [3, 4]], [[7598, 3702], [3, 7]], [[7601, 3709], [-1, 1], [-3, -1], [-2, 1]], [[7595, 3710], [1, 4]], [[7596, 3714], [6, 4], [0, 5], [-2, 2]], [[7600, 3725], [-1, 5]], [[7599, 3730], [3, 4], [5, 1], [3, 4], [0, 3], [-3, 4], [-1, 4], [2, 2], [3, 0], [10, 6], [3, 10]], [[7624, 3768], [0, 4]], [[7624, 3772], [2, 6]], [[7626, 3778], [5, 1], [2, 2], [5, 9], [0, 3]], [[7638, 3793], [-3, 3]], [[7635, 3796], [-2, 3]], [[7633, 3799], [-3, 8]], [[7630, 3807], [0, 10]], [[7630, 3817], [7, -13], [2, 1], [0, 4]], [[7639, 3809], [3, 2]], [[7642, 3811], [1, -8], [2, -4]], [[7645, 3799], [2, -1]], [[7647, 3798], [5, 4]], [[7652, 3802], [2, 4]], [[7654, 3806], [1, 8]], [[7655, 3814], [-4, 6]], [[7651, 3820], [-8, 5]], [[7643, 3825], [5, 2], [8, -1]], [[7656, 3826], [8, -3]], [[7664, 3823], [7, 5], [3, 4]], [[7674, 3832], [4, 2]], [[7678, 3834], [4, 0]], [[7682, 3834], [8, 3]], [[7690, 3837], [6, 6]], [[7696, 3843], [4, 7]], [[7700, 3850], [-3, 10]], [[7697, 3860], [10, -3], [5, 4], [9, 10]], [[7721, 3871], [2, 11]], [[7723, 3882], [7, 3], [6, 2]], [[7736, 3887], [11, 11]], [[7747, 3898], [4, 6]], [[7751, 3904], [-4, 15]], [[7747, 3919], [4, 11]], [[7751, 3930], [0, -5]], [[7751, 3925], [-1, -6]], [[7750, 3919], [4, -8]], [[7754, 3911], [4, 19]], [[7758, 3930], [17, 29]], [[7775, 3959], [6, 8]], [[7781, 3967], [20, 18]], [[7801, 3985], [1, 0]], [[7802, 3985], [12, 5]], [[7814, 3990], [30, -1], [4, 10]], [[7848, 3999], [1, 20]], [[7849, 4019], [2, -11]], [[7851, 4008], [0, -10]], [[7851, 3998], [0, -5]], [[7851, 3993], [3, 12]], [[7854, 4005], [2, 12]], [[7856, 4017], [9, 16]], [[7865, 4033], [4, 6]], [[7869, 4039], [3, 3], [10, 11], [11, 9]], [[7893, 4062], [-2, 3]], [[7891, 4065], [0, 4], [0, 17], [2, -5]], [[7893, 4081], [2, -8]], [[7895, 4073], [4, -2], [4, 1], [9, 11], [6, -1], [12, 0], [8, 0], [5, 1], [2, 5]], [[7945, 4088], [7, 2]], [[7952, 4090], [7, 1], [6, 8], [6, 11], [-7, 6], [-22, -6], [-12, 4], [-6, 6], [-7, 15], [16, -17], [6, -2], [12, 10], [1, 3], [0, 6], [4, 8], [4, 6], [-3, 4], [-4, 3], [-29, 15], [-5, 8]], [[7919, 4179], [12, -7]], [[7931, 4172], [11, -4]], [[7942, 4168], [8, 3]], [[7950, 4171], [2, 9]], [[7952, 4180], [3, 0], [3, -3], [2, -8]], [[7960, 4169], [2, -5]], [[7962, 4164], [3, 1]], [[7965, 4165], [4, -1]], [[7969, 4164], [9, -3]], [[7978, 4161], [8, -2]], [[7986, 4159], [5, 3]], [[7991, 4162], [11, 15]], [[8002, 4177], [15, 16]], [[8017, 4193], [2, 14], [-1, 9], [-1, 7], [-3, 7], [-4, 0]], [[8010, 4230], [-9, -7]], [[8001, 4223], [-2, -21], [-3, -3]], [[7996, 4199], [-3, -1]], [[7993, 4198], [0, 12]], [[7993, 4210], [2, 11]], [[7995, 4221], [-1, 10], [-11, 3], [-4, -3], [-7, -2]], [[7972, 4229], [-12, 1]], [[7960, 4230], [-8, -2], [-9, 2]], [[7943, 4230], [1, 9]], [[7944, 4239], [-2, 12]], [[7942, 4251], [1, 11]], [[7943, 4262], [1, -10]], [[7944, 4252], [3, -8], [9, -7], [6, 1], [5, 5]], [[7967, 4243], [3, 8]], [[7970, 4251], [4, 0]], [[7974, 4251], [8, -2]], [[7982, 4249], [-4, 8], [4, -2], [6, -3], [0, 8], [0, 8], [7, -6]], [[7995, 4262], [8, -3]], [[8003, 4259], [5, -3]], [[8008, 4256], [5, -8]], [[8013, 4248], [-4, 18]], [[8009, 4266], [-4, 14]], [[8005, 4280], [-4, 5]], [[8001, 4285], [-1, 6]], [[8000, 4291], [2, 9]], [[8002, 4300], [0, 2]], [[8002, 4302], [-1, 3], [1, 5], [1, 2], [3, -3], [1, -7]], [[8007, 4302], [1, -2]], [[8008, 4300], [6, -33]], [[8014, 4267], [6, -20], [11, -27], [4, -6]], [[8035, 4214], [-2, 7]], [[8033, 4221], [-15, 41]], [[8018, 4262], [-8, 38]], [[8010, 4300], [-2, 12]], [[8008, 4312], [-4, 13]], [[8004, 4325], [-2, 11]], [[8002, 4336], [-3, 6]], [[7999, 4342], [-11, 2]], [[7988, 4344], [-8, 3], [-12, -7]], [[7968, 4340], [-6, 0]], [[7962, 4340], [-2, 8]], [[7960, 4348], [-10, 10]], [[7950, 4358], [-2, 15]], [[7948, 4373], [-8, 1]], [[7940, 4374], [-12, 5], [-6, 5], [-15, 3], [-2, 2], [-2, 2], [19, -1], [23, -12], [6, 1]], [[7951, 4379], [2, -10]], [[7953, 4369], [7, -8]], [[7960, 4361], [4, -5]], [[7964, 4356], [4, -5]], [[7968, 4351], [5, 3]], [[7973, 4354], [4, 5], [-1, 7], [-2, 4], [-6, 8], [-7, 4]], [[7961, 4382], [-9, 8]], [[7952, 4390], [-10, 15]], [[7942, 4405], [-1, 4]], [[7941, 4409], [0, 3]], [[7941, 4412], [17, -23]], [[7958, 4389], [6, -4]], [[7964, 4385], [5, 3]], [[7969, 4388], [-1, 4]], [[7968, 4392], [0, 6]], [[7968, 4398], [11, -3]], [[7979, 4395], [1, 8], [-1, 8]], [[7979, 4411], [-8, 4]], [[7971, 4415], [5, 5], [-14, 6], [-5, 5], [-5, 10], [-8, 7], [-10, 15]], [[7934, 4463], [-6, 11]], [[7928, 4474], [-14, 16], [3, 0], [10, -9]], [[7927, 4481], [8, -15]], [[7935, 4466], [3, -3], [23, -30]], [[7961, 4433], [5, -2]], [[7966, 4431], [7, 1]], [[7973, 4432], [3, 5], [1, 9]], [[7977, 4446], [2, 6]], [[7979, 4452], [0, 6]], [[7979, 4458], [-7, 8], [-9, 5], [-6, 10], [-7, 5]], [[7950, 4486], [-21, 7]], [[7929, 4493], [-10, 19]], [[7919, 4512], [-5, 2]], [[7914, 4514], [-10, -4]], [[7904, 4510], [-3, 2], [-3, 5], [2, 15]], [[7900, 4532], [2, 9]], [[7902, 4541], [8, 9]], [[7910, 4550], [5, 5], [4, 6], [1, 14]], [[7920, 4575], [1, -13]], [[7921, 4562], [-3, -9]], [[7918, 4553], [-6, -6]], [[7912, 4547], [-7, -13]], [[7905, 4534], [-1, -6]], [[7904, 4528], [0, -10]], [[7904, 4518], [6, -1]], [[7910, 4517], [6, 5]], [[7916, 4522], [6, 1]], [[7922, 4523], [1, -7]], [[7923, 4516], [3, -5]], [[7926, 4511], [5, -6]], [[7931, 4505], [1, 5], [0, 6]], [[7932, 4516], [8, -15]], [[7940, 4501], [14, -4]], [[7954, 4497], [10, -6]], [[7964, 4491], [4, -6]], [[7968, 4485], [5, -5]], [[7973, 4480], [1, 7]], [[7974, 4487], [-3, 6], [-3, 9], [-18, 22], [-2, 9], [-1, 9], [1, -4], [1, -5], [6, -12], [5, -3]], [[7960, 4518], [5, -5]], [[7965, 4513], [4, 1]], [[7969, 4514], [-2, 6]], [[7967, 4520], [-3, 6], [-3, 7]], [[7961, 4533], [-3, 25], [1, 5]], [[7959, 4563], [1, 6]], [[7960, 4569], [-1, 7]], [[7959, 4576], [2, 5]], [[7961, 4581], [-2, 7]], [[7959, 4588], [-3, 7]], [[7956, 4595], [1, 1], [6, -5]], [[7963, 4591], [4, 5]], [[7967, 4596], [-1, 7]], [[7966, 4603], [-4, 3]], [[7962, 4606], [-7, 12]], [[7955, 4618], [0, 1]], [[7955, 4619], [12, -5]], [[7967, 4614], [1, 4], [0, 6]], [[7968, 4624], [4, 2]], [[7972, 4626], [1, 5]], [[7973, 4631], [-1, 2]], [[7972, 4633], [2, 2], [4, -9], [1, 3]], [[7979, 4629], [1, 10]], [[7980, 4639], [2, -2]], [[7982, 4637], [1, -5], [5, 3]], [[7988, 4635], [4, 4]], [[7992, 4639], [1, 4]], [[7993, 4643], [0, 7], [1, 4], [5, 1], [3, 2], [-1, -10]], [[8001, 4647], [8, 1]], [[8009, 4648], [-3, -5], [-3, -1], [-4, -6]], [[7999, 4636], [5, -1]], [[8004, 4635], [5, -3]], [[8009, 4632], [-8, -1]], [[8001, 4631], [-8, 0]], [[7993, 4631], [-6, -6], [-6, -15]], [[7981, 4610], [2, -15]], [[7983, 4595], [6, 7], [0, -5]], [[7989, 4597], [-4, -10]], [[7985, 4587], [-5, 2]], [[7980, 4589], [-5, 0]], [[7975, 4589], [-1, -7], [0, -5], [7, 4]], [[7981, 4581], [4, -3]], [[7985, 4578], [1, -8], [-6, -3], [-4, -1]], [[7976, 4566], [-3, -12]], [[7973, 4554], [3, 1], [2, 6]], [[7978, 4561], [5, -2]], [[7983, 4559], [3, -6], [9, -10], [3, 1], [1, -3], [-8, 0], [-7, 2], [-5, -2], [-2, -13]], [[7977, 4528], [2, -7]], [[7979, 4521], [4, -8]], [[7983, 4513], [7, -5]], [[7990, 4508], [5, -5]], [[7995, 4503], [3, 2]], [[7998, 4505], [1, 3], [1, 1]], [[8000, 4509], [2, -5]], [[8002, 4504], [2, 3], [4, 5], [2, 1], [-1, -5], [1, -7], [-2, -6], [-3, -5], [3, -3]], [[8008, 4487], [2, 0]], [[8010, 4487], [5, -7]], [[8015, 4480], [-3, -6]], [[8012, 4474], [-1, -7], [9, 0]], [[8020, 4467], [5, -2]], [[8025, 4465], [-4, -16]], [[8021, 4449], [-6, -8]], [[8015, 4441], [-7, -16]], [[8008, 4425], [-7, -26]], [[8001, 4399], [-2, -15]], [[7999, 4384], [1, -7]], [[8000, 4377], [4, -7]], [[8004, 4370], [6, 17]], [[8010, 4387], [4, 16]], [[8014, 4403], [3, 5]], [[8017, 4408], [5, 5]], [[8022, 4413], [6, 2]], [[8028, 4415], [3, 3]], [[8031, 4418], [-1, 9]], [[8030, 4427], [17, 46]], [[8047, 4473], [2, 5]], [[8049, 4478], [5, 7], [5, 14], [5, 1], [1, 5]], [[8065, 4505], [1, 10]], [[8066, 4515], [1, 3], [2, 2]], [[8069, 4520], [1, -2]], [[8070, 4518], [0, -5]], [[8070, 4513], [2, 2]], [[8072, 4515], [1, 5]], [[8073, 4520], [0, 4]], [[8073, 4524], [0, 5]], [[8073, 4529], [-2, 9]], [[8071, 4538], [-3, 3], [-6, -1], [4, 5], [4, 10], [-1, 7]], [[8069, 4562], [-7, 5]], [[8062, 4567], [-10, 17]], [[8052, 4584], [-6, 15]], [[8046, 4599], [-2, 22]], [[8044, 4621], [-8, 14]], [[8036, 4635], [-4, 9]], [[8032, 4644], [0, 9]], [[8032, 4653], [-1, 4]], [[8031, 4657], [0, 6]], [[8031, 4663], [6, 9]], [[8037, 4672], [3, 8]], [[8040, 4680], [4, 4]], [[8044, 4684], [1, 2]], [[8045, 4686], [6, 3], [12, 4], [7, 10]], [[8070, 4703], [-2, -6]], [[8068, 4697], [-4, -7]], [[8064, 4690], [-15, -5]], [[8049, 4685], [-5, -4]], [[8044, 4681], [-4, -9]], [[8040, 4672], [-4, -13]], [[8036, 4659], [0, -9], [0, -5], [13, -17], [9, -7], [8, -9]], [[8066, 4612], [6, 1]], [[8072, 4613], [6, -3]], [[8078, 4610], [6, -5], [-2, -12], [-2, -11], [2, -1], [10, 7], [11, 24]], [[8103, 4612], [4, 5]], [[8107, 4617], [0, 5], [2, 3], [4, 4], [4, -1], [3, 5], [1, 8], [2, 4]], [[8123, 4645], [-1, 7]], [[8122, 4652], [6, -1]], [[8128, 4651], [5, 9]], [[8133, 4660], [6, 13], [5, 25], [2, 5]], [[8146, 4703], [2, 2]], [[8148, 4705], [-1, -20]], [[8147, 4685], [0, -5]], [[8147, 4680], [2, 16]], [[8149, 4696], [2, 18]], [[8151, 4714], [2, 12]], [[8153, 4726], [2, 9]], [[8155, 4735], [1, 9]], [[8156, 4744], [-1, 9]], [[8155, 4753], [-2, 6]], [[8153, 4759], [-4, -3]], [[8149, 4756], [-15, 3]], [[8134, 4759], [-1, 9]], [[8133, 4768], [3, 9], [3, 5]], [[8139, 4782], [2, 3]], [[8141, 4785], [3, 1]], [[8144, 4786], [4, 4]], [[8148, 4790], [3, 4]], [[8151, 4794], [8, 19]], [[8159, 4813], [1, 9]], [[8160, 4822], [0, 1]], [[8160, 4823], [-1, 16]], [[8159, 4839], [-4, 13]], [[8155, 4852], [4, -3]], [[8159, 4849], [3, -6]], [[8162, 4843], [1, -13]], [[8163, 4830], [-3, -17]], [[8160, 4813], [-3, -9]], [[8157, 4804], [-3, -10]], [[8154, 4794], [3, 3]], [[8157, 4797], [3, 4]], [[8160, 4801], [4, 2]], [[8164, 4803], [6, 6]], [[8170, 4809], [8, 10]], [[8178, 4819], [3, 3]], [[8181, 4822], [4, 4]], [[8185, 4826], [31, 18], [12, 5], [8, 8], [6, -3], [28, 1], [8, 5]], [[8278, 4860], [8, -3]], [[8286, 4857], [15, 4]], [[8301, 4861], [11, 2]], [[8312, 4863], [7, 0]], [[8319, 4863], [6, -1]], [[8325, 4862], [19, 6]], [[8344, 4868], [6, 8]], [[8350, 4876], [1, 22]], [[8351, 4898], [5, 8]], [[8356, 4906], [-2, 11], [2, -1], [2, -3]], [[8358, 4913], [2, -5]], [[8360, 4908], [3, -5]], [[8363, 4903], [3, 3]], [[8366, 4906], [4, 5]], [[8370, 4911], [2, 0]], [[8372, 4911], [-4, -13]], [[8368, 4898], [1, -14], [2, -3]], [[8371, 4881], [7, 5]], [[8378, 4886], [8, 2]], [[8386, 4888], [21, 19], [2, 0], [1, -12], [-1, -6], [1, -3]], [[8410, 4886], [14, 6]], [[8424, 4892], [6, 5]], [[8430, 4897], [26, 6]], [[8456, 4903], [6, 1]], [[8462, 4904], [3, -1]], [[8465, 4903], [1, 4]], [[8466, 4907], [-1, 11]], [[8465, 4918], [-3, 18]], [[8462, 4936], [-10, 14]], [[8452, 4950], [-4, 2]], [[8448, 4952], [-3, 1]], [[8445, 4953], [-3, -2]], [[8442, 4951], [1, -2]], [[8443, 4949], [3, -4]], [[8446, 4945], [1, 3], [5, -4], [2, -6], [6, -12]], [[8460, 4926], [1, -6]], [[8461, 4920], [-11, -6]], [[8450, 4914], [-12, -5]], [[8438, 4909], [-10, 3]], [[8428, 4912], [-7, 6], [-3, 15]], [[8418, 4933], [-8, 6]], [[8410, 4939], [1, 4]], [[8411, 4943], [2, 2]], [[8413, 4945], [-9, 23]], [[8404, 4968], [-6, 4]], [[8398, 4972], [-14, 4]], [[8384, 4976], [-4, 4]], [[8380, 4980], [9, 11], [5, 8]], [[8394, 4999], [3, 7]], [[8397, 5006], [6, 2]], [[8403, 5008], [7, 5]], [[8410, 5013], [4, 1]], [[8414, 5014], [0, 3]], [[8414, 5017], [-1, 3]], [[8413, 5020], [-3, 0], [-3, -1], [-3, 0], [-3, 7], [-2, 6], [-2, 6]], [[8397, 5038], [2, 6]], [[8399, 5044], [2, 7]], [[8401, 5051], [3, 16]], [[8404, 5067], [4, 4]], [[8408, 5071], [3, 3]], [[8411, 5074], [10, 25]], [[8421, 5099], [12, 16]], [[8433, 5115], [12, 17]], [[8445, 5132], [-3, 4], [-2, 1]], [[8440, 5137], [7, 11]], [[8447, 5148], [9, 8], [7, 3]], [[8463, 5159], [0, -4], [0, -4], [3, 1], [4, 3], [3, -4], [2, -2]], [[8475, 5149], [2, 4]], [[8477, 5153], [-1, 7]], [[8476, 5160], [0, 5]], [[8476, 5165], [2, -1], [1, -4], [2, -1], [3, 12], [3, 4]], [[8487, 5175], [2, -5]], [[8489, 5170], [1, -8]], [[8490, 5162], [2, 1]], [[8492, 5163], [3, 11]], [[8495, 5174], [1, -2]], [[8496, 5172], [2, -8]], [[8498, 5164], [3, 1]], [[8501, 5165], [3, 6], [7, 5], [9, -2], [7, 6]], [[8527, 5180], [5, 7]], [[8532, 5187], [1, 9]], [[8533, 5196], [8, 21]], [[8541, 5217], [0, 10]], [[8541, 5227], [8, 6]], [[8549, 5233], [4, 8]], [[8553, 5241], [3, 2]], [[8556, 5243], [0, -7]], [[8556, 5236], [-3, -7]], [[8553, 5229], [2, -1], [3, 1], [2, -1], [-7, -7]], [[8553, 5221], [-1, -5]], [[8552, 5216], [7, 0]], [[8559, 5216], [8, -4]], [[8567, 5212], [3, -4]], [[8570, 5208], [4, -2]], [[8574, 5206], [1, 6]], [[8575, 5212], [-1, 9]], [[8574, 5221], [4, 7]], [[8578, 5228], [2, 8], [2, -5]], [[8582, 5231], [4, -3]], [[8586, 5228], [4, 4]], [[8590, 5232], [3, 4]], [[8593, 5236], [3, 1]], [[8596, 5237], [3, 0]], [[8599, 5237], [4, -2], [3, -1]], [[8606, 5234], [1, -6]], [[8607, 5228], [3, -7]], [[8610, 5221], [3, 2]], [[8613, 5223], [3, 2], [1, 6], [5, -2], [5, 15], [4, 1], [5, -2], [5, -1], [4, 2], [4, 8], [7, 1], [7, 5], [14, -3]], [[8677, 5255], [15, 18]], [[8692, 5273], [0, 3], [-2, 2], [-7, 2]], [[8683, 5280], [0, 7]], [[8683, 5287], [2, 5]], [[8685, 5292], [-1, 12]], [[8684, 5304], [-3, 6], [1, 4]], [[8682, 5314], [3, -3], [8, 1], [5, -1], [0, -6], [3, -3], [13, 1], [15, 7], [6, -5], [6, 4], [8, 7], [8, 4], [3, 4], [-6, 9], [7, 5], [2, 3], [3, 2], [-3, -7], [-3, -5], [12, -11], [5, 0], [26, 13], [21, 16], [17, 9], [12, 9], [9, 2], [15, 20], [-4, 12], [1, 4], [7, -9], [4, -8], [6, 3], [7, 1], [-3, -6], [-3, -3], [-13, -16], [-20, -17], [-7, -12], [3, -3], [3, 0], [7, -3], [5, 7], [6, 3], [20, -3], [19, 3], [14, -4], [23, 2], [19, -3], [-7, -6], [-22, -1], [-27, -11], [-7, -8], [4, -15], [-8, 11], [-9, 3], [-1, 6], [1, 6], [1, 3], [1, 5], [-9, 3], [3, -3], [-3, -6], [-24, -9], [-11, -7], [-58, -43], [-3, -2], [-3, -5], [11, 4], [7, 3], [-2, -3], [-7, -6], [-5, -3], [-8, -1], [-10, -4], [-8, -6], [-6, -7], [-2, -4], [-2, -4], [16, 17], [6, -1], [-18, -24], [-7, -26], [0, -8], [5, -31], [6, -8], [3, 4], [2, 4], [7, -2], [4, -7], [7, -21], [6, -3], [8, 2], [6, -4], [3, 1], [1, 4], [4, 1], [3, -2], [1, 14], [7, 7], [5, 0], [7, 0], [17, 16], [3, 8], [10, 10], [9, 15], [8, 5], [7, 14], [3, -2], [5, 5], [-3, 10], [-2, 3], [4, 13], [9, 4], [5, -12], [5, 7], [0, 5], [3, 7], [6, -5], [3, -7], [5, -4], [5, -3], [11, -1], [3, 4], [2, 4], [-1, 8], [-3, 8], [4, -3], [7, -2], [6, 2], [6, -1], [11, 8], [5, -1], [5, 1], [20, 8], [19, 7], [20, 11], [18, 7], [18, 11], [6, 1], [5, 4], [6, 3], [6, 0], [8, 3], [8, 6], [9, 3], [5, -3], [3, 2], [3, 5], [-4, 4], [-2, 3], [-28, 2], [7, 5], [7, 3], [1, 5], [-6, 11], [-6, 9], [-5, 4], [-13, -5], [-9, 2], [-8, 7], [-2, 10], [-1, 6], [-3, 2], [-20, -16], [-15, -8], [-2, -3], [-3, -2], [-8, 4], [-13, -1], [3, 4], [1, 7], [-16, 4], [-15, 1], [-9, -3], [-5, -1], [-2, 3], [-3, 4], [-12, 7], [-5, 1], [-10, -2], [-13, 11]], [[8932, 5406], [-12, 7], [-2, 1]], [[8918, 5414], [17, 10], [-3, 4], [-3, 3], [-18, 3], [-5, 3], [-25, 2], [-2, 9], [-7, 5], [1, 8], [-4, 10], [-3, 19], [-7, 3], [-5, 15], [-1, 8], [3, 8], [2, 12], [-8, 3], [-8, 0], [-12, -4], [-5, 2], [-4, 4], [18, 15], [13, 16], [5, 24], [6, 12], [5, 6], [-13, 9], [-13, -1], [3, 6], [-17, -4], [-9, -5], [-11, -9], [-9, -2], [-5, 3], [-7, 19], [-7, 6], [-28, 9], [-11, 9], [-6, 0], [-15, -6], [-6, 1], [20, 12], [9, -3], [6, 2], [13, -1], [5, 5], [7, 5], [13, -9], [22, -10], [9, -2], [7, 1], [17, 10], [6, 6], [11, 5], [4, 3], [5, 10], [5, 6], [22, 7], [8, 15], [0, 17], [-13, 13], [-7, 4], [11, 0], [12, -4], [0, 8], [-4, 6], [-23, 21], [-21, 11], [-43, 8], [-10, 0], [-28, -4], [-22, -2], [-33, -10], [-40, -19], [-34, -13], [-25, -15], [-27, -12], [-15, -10], [-9, -10], [-15, -9], [-5, -1], [-13, -11], [-25, -27], [-13, -9], [-8, -17], [-17, -24], [-17, -18], [-4, -11], [-11, -10], [-13, -20], [-10, -10], [-37, -22], [-12, -3], [-8, -8], [-14, -4], [-18, -8], [-18, -2], [-16, -10], [-6, -5], [-4, -8], [-9, -4], [-10, -6], [-19, -20], [-19, -10], [-13, -11], [-8, -17], [-8, -12], [-8, -15], [-1, -5], [-3, -15], [-3, -4], [-16, -4], [-22, -18], [-17, -6], [-23, -18], [-10, -4]], [[8099, 5294], [26, 24]], [[8125, 5318], [2, 1], [18, 13]], [[8145, 5332], [10, 2], [-2, 11], [-19, 7], [-5, 4], [21, -3], [11, 7], [8, 11], [6, 6], [18, 3], [15, 19], [8, 15], [3, 5], [9, 6], [3, 11], [11, 6], [13, 3], [32, 23], [4, 9], [14, 6], [11, 9], [10, -2], [10, 3], [27, 12], [12, 15], [11, 10], [20, 15], [20, 34], [12, 9], [23, 28], [7, 11], [5, 14], [5, 17], [-7, 9], [-8, 4], [-13, 4], [-28, 13], [-13, 0], [-13, 2], [-6, 6], [-8, 4], [49, -10], [21, -11], [9, -1], [11, -7], [7, -2], [7, 1], [9, 6], [14, 16], [11, 22], [23, 30], [20, 13], [3, 8], [7, 6], [10, 5], [9, 2], [6, 4], [-5, 5], [17, 7], [39, 9], [6, 0], [8, 2], [8, 12], [2, 18], [23, 46], [15, 8], [10, 11], [5, 1], [4, 6], [7, 1], [3, -2], [10, 2], [9, -3], [13, 11], [15, -4], [38, 7], [6, -2], [24, -3], [28, 4], [26, -5], [12, 5], [12, 1], [10, -1], [11, -6], [27, -2], [8, 6], [14, 0], [9, 1], [9, 0], [13, -2], [14, -1], [15, -4], [19, -1], [7, -4], [8, -11], [34, 12], [8, -2], [17, 2], [12, 5], [15, -3], [13, 2], [28, 2], [15, 7], [5, 12], [16, 9], [18, 21], [16, 13], [9, 11], [32, 35], [1, 7], [2, 3], [6, 4], [6, 1], [13, -1], [14, 2], [5, 1], [13, 9], [6, 4], [24, 5], [13, 1]], [[9438, 6056], [15, -4], [0, -1]], [[9453, 6051], [6, 1], [3, 1], [33, 27], [21, 13], [20, 16], [25, 24], [2, 6], [-2, 6], [-7, 5], [-4, 4], [-13, 7], [0, 2], [8, -2], [10, -1], [4, 3], [1, 4], [-3, 6], [-7, 4], [-17, 3], [-13, 0], [-7, 1], [2, 4], [10, 0], [24, 5], [4, 3], [-2, 4], [-4, 7], [1, 10], [3, 6], [-5, 15], [6, 16], [1, 9], [-1, 4], [-3, 4], [-1, 3], [0, 4], [-4, 6], [-4, 9], [-11, 14], [-12, 1], [-7, 3], [-7, 11], [-1, 6], [-5, 0], [-13, -1], [-11, -2], [-13, -8], [-16, -17], [-9, -7], [-4, 11], [-3, 2], [-8, 2], [0, 2], [3, 2], [19, 10], [7, 5], [1, 4], [-1, 6], [-4, 9], [-16, 28], [-16, 4], [-44, 4], [-9, -3], [-4, -3], [0, -3], [3, -4], [11, 2], [9, -1], [10, -3], [0, -1], [-12, 0], [-18, -5], [-25, -8], [-21, -12], [-16, -16], [-15, -12], [-14, -8], [-9, -4], [-16, -4], [-4, -3], [-8, -10], [-13, -10], [-13, -5], [-1, 1], [-1, 2], [6, 3], [2, 3], [-5, 2], [2, 4], [10, 7], [4, 4], [-4, 5], [-17, 9], [-2, 6], [11, -6], [9, -1], [2, 1], [1, 3], [2, 8], [1, 4], [4, 3], [11, 6], [3, 3], [6, 1], [8, -2], [12, 1], [22, 11], [13, 4], [2, 2], [2, 4], [5, 2], [6, 2], [9, -1], [7, 1], [6, 6], [9, 15], [6, 3], [11, 4], [4, 4], [1, 4], [20, 3], [15, 1], [10, 6], [6, 9], [6, 6], [0, 3], [-6, 6], [-11, 1], [-7, 3], [-8, 5], [-8, 6], [-2, 12], [-8, 1], [-10, -2], [-2, -6], [-14, -5], [-8, 1], [-21, 7], [-8, 13], [-6, 12], [-3, 12], [-7, 3], [-13, 2], [-5, -5], [-6, -9], [-2, -3], [-25, -19], [-7, -9], [2, 6], [6, 9], [19, 22], [4, 6], [-6, 2], [-7, -3], [-6, 3], [-1, 9], [-5, 4], [-8, -1], [-5, -5], [-22, -2], [-9, -5], [-17, -15], [-5, -1], [8, 8], [5, 8], [2, 5], [5, 15], [11, 9], [2, 5], [-9, 8], [-3, 7], [-5, 4], [4, 7], [2, 9], [-6, 1], [-5, 2], [-6, -10], [-3, 11], [-3, 1], [-8, 7], [0, 7], [-7, 2], [-5, -6], [-8, -6], [-7, 1], [-3, 2], [0, 5], [-5, 3], [-12, 2], [-8, 3], [0, 3], [12, 3], [-2, 4], [-3, 16], [-4, 1], [-11, -2], [-12, 3], [1, 7], [16, 4], [5, 4], [-7, 3], [-13, 1], [-19, 3], [-6, 4], [9, 6], [10, 1], [2, 1], [-6, 6], [-3, 1], [-10, 1], [-10, 9], [-21, 7], [-8, 9], [3, 2], [6, 2], [6, 0], [-6, -6], [1, -2], [23, -8], [41, -5], [12, 3], [0, 11], [-1, 9], [3, 8], [1, 10], [-22, 21], [-7, 1], [-6, -1], [-5, 1], [-7, 4], [-2, 2], [3, 3], [4, 9], [3, 3], [-1, 1], [-5, 5], [-13, 3], [-8, 1], [-8, -2], [-7, 1], [-5, 2], [-3, 3], [9, -2], [3, 1], [7, 5], [6, 1], [7, 3], [8, 6], [3, 7], [-5, 12], [2, 4], [5, 2], [1, 5], [-5, 5], [-12, 7], [-6, -1], [-8, 2], [-14, 21], [-8, 0], [-18, -3], [-12, -4], [-19, -11], [-3, 2], [8, 8], [7, 5], [20, 8], [10, 14], [6, 19], [-1, 2], [-10, 0], [-7, -2], [-19, -7], [-4, -1], [1, 4], [-2, 2], [-5, 1], [-7, -3], [-13, -13], [-5, 0], [7, 8], [4, 6], [13, 8], [9, 3], [18, 15], [-4, 11], [-7, 10], [-3, 3], [-10, -2], [-8, 1], [5, 5], [0, 2], [-4, 9], [-3, 3], [1, 1], [6, 2], [-2, 2], [-6, 1], [-6, 0], [-13, -4], [-17, -2], [-11, 0], [-3, 3], [5, 1], [12, 0], [8, 2], [11, 4], [7, 9], [-9, 17], [-8, 1], [-9, -8], [-2, 0], [2, 5], [-2, 4], [-13, 3], [-2, 4], [10, 4], [7, 8], [-7, 7], [-6, 8], [-3, 7], [-1, 6], [-6, 8], [-7, -3], [-6, -6], [4, 12], [-1, 15], [-8, 11], [-9, 0], [-12, -3], [-13, -5], [-3, 2], [4, 2], [15, 7], [8, 9], [-1, 7], [-5, 5]], [[8884, 7090], [-9, 4], [-7, 4]], [[8868, 7098], [-9, -1], [-2, -3], [-3, -2], [-3, -4], [-11, -23], [-3, -8], [-5, -10], [-1, -5], [2, -4], [4, -3], [6, -4], [-2, -2], [-12, 7], [-6, 1], [-5, -1], [-4, -2], [-2, -2], [-4, -10], [0, -5], [1, -4], [5, -9], [5, -3], [6, -2], [7, -4], [8, -6], [2, -3], [-2, -1], [-16, 10], [-6, 2], [-9, -1], [-1, -3], [6, -14], [0, -1], [-8, 4], [-3, -4], [-2, -9], [-2, -3], [-5, 2], [-2, -1], [-1, -1], [3, -8], [2, -2], [3, -2], [8, -3], [9, -3], [-1, -3], [-10, 0], [-2, -3], [-11, 2], [-2, -1], [0, -2], [1, -4], [-7, 1], [-3, 0], [-7, -8], [1, -2], [6, -4], [-1, -2], [-9, 0], [-6, -3], [0, -3], [2, -7], [5, -10], [2, -4], [0, -5], [0, -4], [-6, -12], [-1, 0], [-1, 16], [-1, 4], [-4, 6], [-6, 8], [-5, 6], [-5, 2], [-5, 0], [-9, -7], [-2, -4], [-4, -7], [-3, -11], [-9, -6], [-14, -4], [-9, -3], [-11, -8], [-17, -8], [-14, -10], [-2, -3], [-2, -6], [-1, -7], [-4, -10], [-1, 2], [1, 11], [0, 4], [-1, 13], [-3, 16], [-2, 2], [-3, -4], [-2, -7], [-2, -4], [-12, -14], [-4, -2], [11, 15], [2, 4], [0, 4], [-7, 15], [-3, 3], [-7, -1], [-5, -9], [-9, -26], [-5, -10], [-4, -5], [-7, -5], [-22, -4], [-20, -9], [1, 3], [7, 5], [9, 4], [18, 4], [10, 5], [8, 10], [3, 8], [7, 20], [0, 10], [-1, 5], [-1, 3], [-6, 5], [-4, 17], [-2, 5], [-5, 5], [-7, 5], [-5, 3], [-5, 1], [-19, -2], [-9, 2], [-9, -1], [-7, -1], [-9, -4], [-20, -14], [-11, -5], [-7, 1], [-12, 6], [-9, 1], [-1, 4], [14, 3], [9, 5], [2, 3], [1, 2], [0, 3], [1, 2], [2, 1], [3, -2], [6, -12], [2, -2], [3, 1], [6, 5], [2, 6], [2, 9], [2, 6], [2, 3], [1, 2], [-4, 7], [1, 4], [8, 11], [0, 3], [-4, 4], [-22, 1], [-2, 6], [1, 11], [3, 9], [4, 7], [2, 6], [-1, 5], [-3, 12], [-3, 6], [-5, 5], [-5, 4], [-40, 3], [-11, 0], [-12, 1], [-3, 5], [12, -1], [42, 0], [12, 2], [10, 5], [3, 5], [1, 3], [-1, 6], [0, 3], [-6, 8], [-2, 5], [-2, 7], [0, 6], [1, 5], [2, 10], [6, 14], [5, 6], [7, 5], [4, 4], [2, 4], [1, 4], [-1, 5], [-5, 10], [-2, 4], [-4, 2], [-5, -1], [-2, -5], [-3, -7], [-2, -4], [-7, -1], [-8, -6], [-7, 0], [-8, 3], [-4, 5], [1, 7], [-1, 4], [-3, 3], [-7, 3], [-8, -1], [-12, -2], [-14, 1], [-24, 9], [-11, 2], [-13, 0], [-6, 1], [-10, 7], [-14, 14], [0, 5], [7, 3], [0, 1], [-11, 1], [-5, 2], [1, 4], [7, 7], [7, 3], [3, 2], [1, 2], [-2, 3], [-18, 9], [-4, -2], [-3, -4], [-5, -3], [-15, -3], [-2, 2], [13, 4], [3, 3], [0, 2], [-3, 5], [-4, 3], [-4, 6], [-4, 4], [-10, 6], [-11, 4], [-5, -2], [-7, -5], [-5, -3], [-3, 1], [8, 13], [2, 9], [0, 3], [-1, 2], [-2, 5], [-1, 1], [-4, 1], [-11, -1], [-8, 7], [-5, 2], [-11, 9], [-8, 6], [-10, 5], [-16, 10], [-5, 2], [-5, 0], [-9, -5], [-13, -7], [-12, -6], [-17, -6], [-17, -7], [-3, -3], [6, -7], [-2, -1], [-21, 13], [-9, 4], [-7, 1], [-17, 5], [-5, 0], [-6, -3], [-24, -12], [-1, 1], [10, 7], [-11, 8], [-61, 17], [-20, 7], [-26, 3], [-12, 3], [-18, -5], [-23, -12], [-13, -9], [-3, -4], [-2, -4], [-1, -9], [1, -12], [4, -22], [5, -10], [5, -9], [5, -4], [6, -4], [9, -8], [14, -8], [-11, -9], [-6, -5], [-1, -5], [1, -19], [0, -3], [-3, -6], [-5, -8], [-8, -10], [-17, -18], [-2, -4], [5, -1], [10, 1], [9, -3], [21, 4], [1, -2], [-6, -2], [-5, -11], [-2, -2], [-3, -5], [6, -7], [6, -1], [10, -1], [1, -2], [-8, -4], [-6, -10], [2, -7], [15, -26], [0, -1], [-9, -4], [-1, -2], [3, -3], [18, -2], [2, -2], [-6, -12], [0, -4], [3, -6], [0, -5], [-11, -9], [-1, -4], [6, -9], [4, -3], [-3, -2], [-15, 14], [-11, -1], [-1, -2], [1, -9], [-1, -3], [-9, -10], [1, -3], [5, -4], [2, -4], [-7, -9], [-11, -7], [-6, -5], [-5, -7], [-8, -12], [-15, -16], [-2, -3], [-2, -6], [-1, -7], [-1, -10], [0, -4], [4, -5], [8, -3], [26, -21], [10, -6], [16, -7], [10, -6], [5, -5], [25, -21], [21, -31], [6, -11], [2, -7], [10, -26], [4, -13], [2, -10], [4, -35], [0, -21], [-1, -25], [-1, -16], [-4, -19], [-4, -11], [-8, -13], [-14, -15], [-10, -13], [-7, -11], [-13, -13], [-29, -25], [-5, -6], [-9, -6], [-19, -10], [-13, -10], [-13, -7], [-29, -12], [-5, -3], [-58, -22], [-3, -3], [0, -2], [3, -1], [3, -2], [2, -3], [6, -13], [4, -11], [3, -7], [6, -9], [5, -5], [6, -4], [5, -2], [1, -1], [-3, -5], [-5, -2], [13, -6], [5, -3], [1, -2], [-6, -6], [0, -3], [3, -6], [7, -4], [0, -2], [-5, 1], [-3, -2], [-3, -9], [-2, -3], [1, -7], [4, -11], [4, -18], [4, -24], [3, -19], [4, -8], [8, -9], [2, -5], [-3, -5], [-1, -7], [2, -12], [2, -3], [9, -11], [4, -5], [2, -11], [0, -10], [1, -3], [5, -3], [-3, -1], [-4, -5], [-4, -9], [-9, -12], [-9, -8], [-4, -6], [-1, -8], [-3, -6], [-4, -2], [0, -5], [16, -20], [3, -5], [0, -3], [-7, -8], [-3, -5], [-3, -14], [0, -8], [-3, 7], [-3, 15], [-2, 4], [-3, 5], [-4, 4], [-4, 3], [-6, 2], [-3, 1], [-2, 9], [-4, 0], [-12, -7], [-2, -3], [-2, -6], [-3, -4], [-4, -5], [-4, -8], [-2, -11], [-1, -8], [0, -4], [2, -4], [6, -8]], [[7720, 6005], [8, -10], [6, -5]], [[7734, 5990], [5, -10], [3, -9], [-9, 14]], [[7733, 5985], [-5, 5], [-10, 10]], [[7718, 6000], [-14, 20], [-9, 7], [-11, 6], [-13, 4], [-8, 1], [-8, -2], [-16, -14], [-9, -7], [-4, -1], [14, 17], [13, 9], [4, 5], [0, 5], [-4, 11], [-7, 17], [-5, 11], [-4, 4], [-20, 21], [-12, 8], [-12, 6], [-9, 6], [-5, 7], [-7, 4], [-7, 0], [-13, -2], [-1, 0], [4, 4], [9, 5], [4, 3], [3, 5], [-2, 8], [-11, 15], [-9, 11], [-13, 19], [-7, 7], [-7, 5], [-4, 5], [-3, 8], [0, 4], [3, 11], [3, 7], [4, 6], [1, 12], [-2, 17], [-1, 12], [3, 15], [1, 9], [-2, 8], [-6, 19], [-2, 3], [-10, 13], [-2, 7], [1, 13], [3, 15], [12, 39], [-1, 5], [-6, 17], [-7, 8], [-14, 9], [-8, 2], [-9, -1], [-5, 1], [-6, 7], [-3, 1], [-18, -2], [-27, 6], [-8, 0], [-18, 6], [-5, 0], [-4, -2], [-6, -1], [-9, 0], [-11, -1], [-12, -3], [-14, 0], [-17, 3], [-11, 1], [-5, -3], [-6, -5], [-12, -17], [0, 2], [12, 24], [-1, 6], [-5, 4], [-9, 6], [-6, 5], [-6, 7], [-9, 8], [-12, 6], [-12, 5], [-12, 2], [-18, 7], [-42, 17], [-28, 7], [-15, 5], [-6, 4], [-24, 34], [-16, 15], [-15, 8], [-13, 8], [-18, 14], [-11, 10], [-10, 5], [-20, 4], [-10, 3], [-35, 8], [-21, 8], [-21, 12], [-19, 9], [-24, 3], [-16, -1], [-70, -21], [-18, -7], [-8, -4], [-1, 0], [6, 6], [-1, 3], [-12, -1], [-4, -2], [-11, -8], [-5, -3], [-6, -1], [0, 1], [5, 3], [5, 4], [4, 6], [5, 8], [6, 11], [3, 9], [0, 5], [-1, 7], [-3, 10], [-16, 37], [-3, 8], [-8, 27], [-7, 17], [-13, 31], [-2, 9], [-2, 16], [-2, 3], [-8, 4], [-7, -2], [-9, 0], [-22, 4], [-22, -2], [-5, -2], [-6, -13], [-5, -30], [-5, -9], [0, 5], [4, 37], [0, 7], [-11, 4], [-9, 12], [-3, 2], [-3, 1], [-4, -1], [-3, 4], [-2, 9], [-3, 5], [-5, 3], [-9, 3], [7, 2], [4, 7], [2, 14], [1, 25], [-1, 56]], [[6554, 7053], [1, 5], [3, 13]], [[6558, 7071], [5, 23], [2, 13], [-2, 4], [-3, 3], [-4, 3], [7, 4], [8, 1], [5, 7], [6, 15], [9, 17], [12, 18], [7, 13], [1, 9], [-1, 6], [-2, 4], [2, 2], [9, -1], [4, 4], [0, 3], [-4, 9], [2, 4], [16, 15], [22, 12], [6, 4], [3, 3], [-1, 1], [-8, 4], [-5, 4], [-3, 3], [-4, 9], [16, -2], [3, 0], [5, 4], [-2, 2], [-6, 4], [2, 2], [14, 0], [7, 4], [4, 3], [0, 2], [-4, 2], [1, 3], [12, 8], [-1, 3], [-5, 5], [-15, 8], [-2, 1], [4, 1], [22, -8], [10, -5], [7, -6], [5, -4], [4, -1], [-1, 4], [-7, 9], [-5, 6], [-5, 3], [-1, 3], [5, 5], [9, 6], [2, 4], [1, 5], [4, 2], [8, -1], [6, 1], [4, 2], [5, 0], [9, -5], [6, 0], [5, 4], [0, 2], [-1, 6], [-15, 3], [-7, 2], [-5, 3], [-3, 3], [-2, 4], [-1, 4], [2, 2], [12, 1], [4, 1], [3, 3], [6, 0], [13, -3], [32, -4], [8, 2], [18, 12], [8, 2], [10, 1], [8, 3], [3, 5], [3, 6], [0, 5], [-1, 23], [-3, 5], [-17, 11], [-10, 4], [-18, 4], [-12, 6], [-15, 12], [-12, 4], [-9, -2], [-9, -5], [-17, -9], [-13, -1], [0, 2], [20, 10], [3, 4], [-3, 2], [-11, 3], [-14, 4], [-49, 16], [-7, 3], [-9, 3], [-3, -1], [2, -5], [4, -3], [5, -3], [0, -1], [-11, 0], [-11, 3], [-8, 9], [0, 3], [4, 4], [1, 4], [-8, 13], [21, -14], [35, -11], [32, -12], [17, -5], [11, -2], [7, 1], [5, 4], [4, 1], [4, -1], [1, -2], [-3, -1], [0, -2], [2, -2], [20, -2], [10, -2], [33, -12], [13, -4], [10, -1], [8, 2], [6, 3], [3, 5], [4, 0], [7, -3], [6, -2], [10, 3], [6, 5], [11, 14], [0, 3], [-3, 5], [-9, 12], [2, 1], [17, -5], [5, 2], [0, 2], [-7, 12], [-3, 2], [-7, 4], [3, 1], [18, 5], [1, -2], [3, -8], [2, -2], [9, -6], [5, -2], [4, 0], [3, 2], [4, 1], [15, -6], [6, -2], [2, 1], [-8, 15], [1, 1], [11, -9], [7, -3], [11, -2], [13, 2], [21, 9], [21, 11], [8, 7], [3, 7], [6, 12], [47, 50], [7, 12], [12, 16], [2, 5], [-2, 11], [-6, 3], [-22, 4], [-41, 2], [-21, 0], [-60, 8], [-11, 6], [-9, 6], [-28, 23], [-14, 11], [-11, 5], [-9, 3], [-9, 1], [-34, 8], [-29, 4], [-7, -2], [0, -2], [2, -6], [-18, 8], [-11, 6], [1, 1], [8, 1], [23, 0], [14, -1], [13, -3], [11, -1], [15, 0], [15, -5], [9, 0], [9, -2], [3, 1], [-7, 7], [4, 1], [11, -1], [12, -3], [13, -5], [26, -15], [10, -4], [11, -1], [5, -2], [5, -8], [7, -3], [15, -8], [21, -14], [12, -6], [22, -1], [17, 0], [13, 2], [7, 3], [9, 7], [10, 10], [19, 17], [51, 42], [2, 3], [4, 8], [0, 4], [-3, 4], [-9, 4], [-14, 6], [-22, 6], [-8, 4], [-4, 7], [0, 2], [3, 1], [1, 2], [-4, 6], [3, 1], [5, 1], [44, -1], [21, 2], [15, 4], [12, -4], [10, -11], [9, -9], [8, -5], [14, -6], [22, -8], [11, -3], [2, 1], [4, 8], [6, 4], [2, 0], [22, -7], [8, -2], [3, 0], [5, 3], [-2, 6], [-10, 15], [-10, 13], [-5, 7], [-2, 7], [-3, 4], [-8, 4], [-20, 17], [-1, 2], [-3, 3], [-8, 4], [-9, 1], [-3, -2], [-6, -5], [-3, -1], [-8, 2], [0, 2], [6, 5], [15, 9], [12, -1], [11, -6], [1, -1], [5, -4], [12, -8], [3, -2], [-7, -2], [0, -2], [3, -3], [9, -5], [4, -1], [8, 1], [4, -1], [6, -6], [14, -17], [7, -6], [1, -3], [3, -8], [6, -3], [9, 1], [8, 2], [8, 5], [19, 14], [23, 4], [7, 4], [14, 10], [9, 4], [4, 3], [7, 7], [8, 11], [6, 7], [4, 1], [12, 0], [7, 2], [13, 8], [2, 2], [4, 12], [6, 20], [3, 12], [-2, 4], [-9, 12], [-6, 4], [-17, 11], [-12, 9], [-8, 7], [-7, 8], [-3, 7], [0, 3], [1, 4], [6, 15], [0, 2], [-2, 0], [-3, -2], [-6, -4], [-3, -1], [-2, 1], [-13, 17], [-3, 1], [0, 1], [2, 4], [-1, 2], [-4, 3], [-7, 8], [1, 2], [3, 2], [8, -1], [15, 4], [8, -1], [7, -4], [7, -1], [7, 3], [14, 5], [9, 4], [19, 12], [2, 4], [0, 6], [-1, 4], [-5, 6], [-4, 2], [-7, 2], [-16, 2], [-21, 0], [0, 3], [15, 6], [12, 5], [21, 14], [1, 2], [-4, 6], [-3, 1], [-25, 7], [-17, 2], [-15, -3], [-6, 0], [-1, 2], [3, 4], [-2, 4], [-6, 9], [-26, 6], [-8, 4], [9, 3], [11, 1], [8, 9], [1, 4], [-19, 6], [-10, 1], [-19, -1], [-43, 2], [-8, -1], [-20, 6], [-25, 10], [-6, 1], [-25, 1], [-14, -2], [-15, -3], [-12, 0], [-9, 3], [-9, 2], [-7, -1], [-2, -1], [3, -2], [4, -5], [0, -3], [-5, -12], [2, -3], [4, -5], [1, -4], [-2, -3], [0, -4], [2, -4], [1, -3], [-1, -2], [-1, -7], [0, -4], [3, -10], [9, -7], [2, -1], [10, 0], [17, -8], [3, -2], [-3, -7], [0, -4], [-2, -3], [-7, -1], [-5, -5], [-2, -4], [0, -4], [18, -6], [0, -2], [-31, -4], [-5, 1], [-7, 3], [-5, 0], [-2, 0], [-3, -5], [-7, -3], [-3, -4], [-3, -5], [-1, -6], [1, -7], [0, -8], [-5, -14], [-12, -30], [-3, -3], [-29, -26], [-3, -3], [-6, -11], [-2, -7], [-2, -16], [-2, -4], [-4, -3], [-5, -4], [-6, -2], [-4, 0], [-9, -6], [-12, -10], [-14, -10], [-5, -1], [-3, 0], [-2, 2], [-2, 3], [-4, 13], [-2, 3], [-39, 32], [-15, 17], [-9, 21], [-1, 5], [1, 21], [-4, 11], [2, 3], [8, 6], [2, 0], [5, -9], [3, -1], [9, -1], [7, 0], [4, 3], [1, 3], [2, 6], [0, 7], [-2, 5], [-2, 3], [-4, 11], [-4, 17], [-6, 12], [-14, 12], [-7, 4], [-25, 13], [-13, 9], [-11, 10], [-8, 5], [-11, 1], [-6, -2], [-6, -3], [-15, -17], [-9, -8], [-4, -10], [-2, -14], [-3, -9], [-8, -13], [-1, -4], [2, -8], [-2, -4], [-8, -11], [-9, -7], [-3, -4], [-1, -4], [-2, -2], [-4, 2], [-3, 3], [-2, 4], [-3, 2], [-5, 6], [-8, 4], [-4, 5], [4, 16], [1, 9], [-2, 12], [0, 4], [5, 5], [0, 2], [-9, 8], [-12, 18], [-38, 21], [2, 2], [5, 0], [7, 2], [3, 0], [-2, -3], [3, -2], [9, -1], [5, 3], [2, 7], [4, 5], [5, 4], [6, 2], [7, 0], [7, 2], [-2, 2], [-8, 3], [-9, 1], [-9, 0], [-13, 0], [-26, 4], [-11, -2], [1, 1], [9, 5], [10, 5], [2, 2], [-4, 1], [-14, 0], [-11, -4], [-15, -8], [-15, -2], [-23, 9], [-3, 3], [2, 3], [-6, 2], [-14, 1], [-12, -3], [-12, -1], [-4, 1], [-3, 1], [11, 6], [36, 21], [16, 11], [7, 6], [-6, 4], [-5, 1], [-25, 0], [-5, 3], [5, 5], [7, 6], [3, 4], [2, 1], [7, -5], [6, -4], [10, -3], [10, -1], [19, 2], [4, 1], [0, 2], [-7, 7], [-5, 8], [-3, 3], [-5, 2], [-4, -2], [-4, -4], [-4, -1], [-5, 2], [-2, 2], [3, 6], [-1, 2], [-13, 12], [-8, 14], [-3, 3], [-2, 2], [-14, 5], [-6, 3], [-11, 9], [-13, 5], [-2, 2], [5, 4], [1, 3], [2, 18], [-1, 7], [-4, 16], [-3, 4], [-4, 5], [-17, 14], [-12, 7], [-13, 6], [-14, 8], [-1, 4], [2, 5], [0, 3], [-5, 3], [-21, 1], [-6, -2], [-11, 1], [-13, 10], [-1, 8], [-5, 7], [-4, 1], [-10, 0], [-11, -3], [-25, -7], [-24, -15], [-8, -10], [-17, -11], [-2, -2], [3, -6], [4, -1], [8, -2], [18, 1], [3, -2], [-4, -4], [-9, -14], [-5, -2], [-7, 1], [-10, 4], [-5, 3], [-6, 2], [-5, 1], [-6, -2], [-10, -7], [-10, -8], [-3, -4], [2, -7], [-2, -2], [-5, -2], [-1, -2], [4, -7], [-2, -8], [-4, -13], [0, -9], [14, -16], [8, -4], [6, 0], [21, 7], [2, -1], [-8, -9], [0, -3], [8, -5], [-13, 2], [-6, -1], [-8, -2], [-5, -3], [-3, -5], [-16, -17], [-1, -10], [0, -4], [5, -10], [17, -15], [8, -8], [4, -7], [5, -4], [7, -4], [8, -2], [11, -1], [10, -2], [7, -5], [15, -6], [36, -10], [3, 1], [6, 7], [3, 1], [3, -1], [9, -8], [7, -7], [7, -5], [5, -3], [8, -1], [12, 0], [7, 2], [10, 4], [11, 3], [9, -5], [0, -3], [-1, -5], [0, -3], [8, -1], [-1, -2], [-9, -7], [-14, -9], [-5, 4], [1, 3], [15, 14], [-1, 2], [-18, -5], [-23, -4], [-8, -3], [-2, -5], [0, -4], [2, -10], [3, -2], [10, 0], [0, -2], [-11, -8], [-19, -11], [-7, -6], [-2, -12], [1, -4], [8, -3], [20, 2], [12, 3], [5, 4], [4, 8], [4, 11], [1, 1], [2, 1], [3, -1], [4, -3], [4, -4], [2, -5], [2, -6], [0, -7], [-1, -11], [2, -6], [3, -1], [12, -1], [-3, -2], [-12, -7], [-22, -8], [-13, -9], [-12, -12], [-10, -8], [-8, -4], [-20, -14], [-9, -4], [-7, 1], [-13, 4], [-8, -3], [-12, 0], [-3, -1], [-3, -3], [-14, -34], [1, -4], [13, -11], [6, -11], [7, -18], [3, -12], [-5, -12], [-2, -21], [-4, -3], [-7, -3], [-10, -1], [-27, 4], [-5, -2], [-1, -1], [3, -7], [18, -17], [3, -4], [-4, -9], [-1, 0], [-1, 9], [-5, 6], [-37, 29], [-4, 6], [-1, 2], [2, 1], [4, 1], [10, -9], [10, 0], [5, 3], [5, -1], [8, -4], [7, -1], [12, 1], [4, -1], [4, 1], [4, 1], [3, 3], [1, 5], [0, 7], [-1, 5], [-9, 6], [-2, 1], [-5, -1], [-12, -3], [0, 1], [6, 13], [-2, 2], [-12, -2], [-10, -4], [-10, 1], [-2, 2], [-1, 10], [-2, 3], [-13, 13], [0, 5], [11, 14], [3, 5], [2, 13], [10, 39], [5, 11], [-8, -2], [-28, -10], [-2, -2], [-2, -6], [-3, -2], [-5, -2], [-10, -1], [0, 2], [19, 22], [3, 5], [1, 3], [-15, -7], [-27, 0], [-2, 1], [-6, 8], [-4, 6], [-10, 8], [-6, 4], [-6, 2], [-5, -1], [-5, -2], [-7, 1], [-15, 6], [-7, -1], [-1, -2], [2, -7], [9, -7], [-23, -5], [-10, -3], [-19, 7], [-4, 0], [-3, -1], [-7, -1], [1, -4], [11, -12], [4, -3], [5, -8], [-1, -2], [-8, 2], [0, -2], [4, -6], [19, -17], [7, -2], [15, 6], [13, 3], [15, -2], [16, -7], [10, -6], [4, -4], [1, -3], [-1, -5], [0, -3], [-3, -3], [-6, -4], [-14, -6], [-12, 2], [-25, 9], [-3, 3], [-7, 4], [-27, 26], [-9, 7], [-8, 3], [-4, -1], [-2, -3], [0, -5], [1, -4], [7, -6], [11, -7], [4, -3], [0, -2], [-3, -2], [-19, -2], [-8, -4], [-9, -2], [-8, -1], [-9, 1], [-11, 2], [-14, 5], [-23, 3], [-34, 3], [-19, 0], [-5, -2], [-7, -1], [-10, 0], [-9, -1], [-13, -4], [-5, -1], [-35, -8], [-11, 2], [-15, 4], [-13, 1], [-12, -2], [-8, 0], [-6, 3], [-23, 6], [-11, 5], [-14, 10], [-23, 15], [-12, 6], [-14, -6], [-19, -3], [-22, -1], [-12, 1], [-11, 3], [-11, 6], [-2, 4], [2, 7], [-2, 2], [-9, 3], [-8, -1], [-3, 1], [-1, 6], [-2, 1], [-3, -1], [-4, -2], [-4, 1], [-7, 4], [-14, 10], [-4, 5], [-2, 14], [-7, 17], [-5, 7], [-6, 6], [-9, 4], [-16, 5], [-12, 1], [-12, -2], [-30, -9], [-9, -2], [-47, -14], [-25, -4], [-42, -5], [-3, -1], [-22, -26], [-6, -10], [2, -2], [25, -12], [3, -3], [5, 0], [12, 2], [6, -1], [22, 2], [5, 3], [-5, 6], [0, 4], [9, 5], [9, -1], [16, -3], [12, 1], [7, 5], [8, 3], [7, 2], [6, 0], [13, -4], [3, 4], [2, 8], [7, 7], [16, 7], [17, 6], [7, 1], [12, -3], [2, -2], [-2, -8], [-6, -6], [-6, -4], [-8, -4], [-7, -2], [-11, -1], [-10, -7], [-2, -4], [0, -10], [-3, -1], [-5, 2], [-11, 1], [-3, -1], [-6, -8], [-4, -2], [-6, -1], [-6, -1], [-10, 0], [-7, -2], [-10, -4], [-7, -1], [-5, 1], [-20, -3], [-3, 1], [1, -3], [5, -7], [-3, -6], [-10, -6], [-5, -4], [-1, -10], [1, -4], [7, -7], [9, -6], [8, -9], [1, -5], [-1, -5], [0, -5], [6, -13], [7, -9], [12, -8], [3, -3], [-5, -6], [2, -4], [5, -5], [8, -11], [-4, 0], [-9, 6], [-6, 1], [-1, -2], [-3, 0], [-4, 1], [-9, 8], [-8, -2], [-2, -3], [1, -17], [1, -6], [1, -3], [11, -14], [22, -23], [2, -3], [-3, 0], [-6, 4], [-8, 7], [-17, 17], [-5, 5], [-15, 12], [-3, 4], [-4, 3], [-4, 2], [-4, 4], [-5, 6], [-18, 14], [-3, 3], [3, 0], [8, -4], [10, 0], [10, 2], [8, 3], [4, 3], [2, 5], [-2, 4], [-4, 7], [-28, 17], [-11, 9], [-8, 13], [-1, 1], [-5, 1], [-3, -3], [-8, -17], [-3, -2], [-3, 2], [-4, 7], [-2, 4], [-2, 13], [-3, 6], [-3, 2], [-11, 2], [-31, 1], [-5, 2], [-5, 8], [-6, 5], [-6, 1], [-2, 2], [-8, 11], [-3, 1], [-2, 0], [-9, -4], [-12, 0], [-33, -15], [-14, -4], [-8, -1], [-5, 1], [-3, 3], [-7, -1], [-13, -4], [-9, -2], [-11, 0], [-30, -3], [-10, 0], [-6, -2], [-9, -4], [-5, 0], [-29, 0], [-15, 0], [-11, 2], [-36, 0], [-16, 1], [-13, 2], [-9, 1], [-7, 0], [-13, 2], [-18, 5], [-14, 2], [-12, -1], [-10, 2], [-12, 6], [-11, 4], [1, 2], [17, 9], [3, 2], [-2, 8], [1, 2], [4, 4], [21, 7], [7, 9], [37, -3], [14, 3], [3, 2], [3, 2], [4, 11], [-2, 2], [-5, 0], [-3, 2], [-1, 5], [-8, 9], [-15, 13], [-16, 10], [-29, 12], [-19, 5], [-16, 6], [-14, 4], [-14, 1], [-5, 0], [-4, -3], [-18, 2], [-6, 0], [-1, -2], [15, -12], [0, -2], [-13, 1], [-8, 3], [-7, 1], [-10, 0], [-36, 4], [-8, -1], [-8, 1], [-6, 2], [-40, 8], [-21, 5], [-16, 6], [-14, 6], [-20, 10], [-9, 3], [-76, 10], [-22, 5], [-12, 4]], [[4592, 8166], [-30, 18]], [[7604, 6259], [-15, 2], [-40, -4], [-4, -3], [-4, -4], [-6, -9], [2, -5], [13, -6], [38, -13], [19, -8], [7, -2], [16, -4], [5, 2], [2, 5], [0, 5], [-4, 11], [-11, 13], [-18, 20]], [[8185, 8001], [-23, 1], [-18, 4], [-20, 4], [-25, -3], [-2, -2], [-2, -4], [1, -4], [5, -9], [8, -9], [7, -3], [8, -2], [21, 0], [37, 0], [17, 1], [0, 4], [0, 6], [-3, 11], [-1, 2], [-3, 1], [-7, 2]], [[7861, 7461], [-6, 5], [-5, 1], [-23, 2], [-14, -3], [-7, -2], [-2, -3], [5, -8], [17, -14], [17, -12], [7, -3], [11, 2], [11, 4], [5, 3], [4, 6], [1, 4], [-1, 2], [-9, 13], [-4, 4], [-7, -1]], [[7483, 7404], [-20, -8], [-4, 0], [-7, 1], [-13, 5], [-7, -2], [-28, -40], [-12, -11], [-1, -2], [0, -4], [0, -2], [11, -12], [3, -5], [1, -10], [1, -2], [25, 9], [9, 0], [10, -4], [4, 0], [5, 1], [34, 23], [6, 5], [8, 8], [21, 16], [7, 9], [2, 6], [2, 6], [2, 6], [-1, 5], [-3, 4], [-4, 2], [-6, 0], [-8, 0], [-18, -4], [-19, 0]], [[7733, 7339], [-6, 3], [-5, 0], [-4, -2], [-4, 0], [-12, 1], [-5, -1], [-7, -6], [-12, -16], [-5, -7], [-1, -5], [-2, -6], [0, -8], [0, -15], [1, -5], [5, -5], [8, -3], [7, -6], [9, -8], [6, -4], [4, 0], [4, 2], [3, 4], [4, 7], [6, 12], [6, 10], [7, 9], [4, 6], [1, 10], [3, 9], [-2, 8], [-3, 5], [-10, 11]], [[5786, 9107], [-14, 0], [-3, 4], [-1, 7], [-5, 4], [-17, 11], [-12, 7], [-11, 4], [-21, 3], [-13, -2], [-2, -2], [14, -11], [2, -8], [8, -4], [4, -8], [11, -13], [13, -11], [13, -9], [15, -2], [24, -4], [12, 2], [5, 2], [2, 7], [1, 3], [-4, 7], [-3, 4], [-18, 9]], [[6682, 9136], [-7, 5], [-7, 4], [-13, 3], [-8, 1], [-33, -2], [-51, 2], [-22, 0], [-21, -2], [-19, 4], [-16, -1], [-35, -8], [-4, -2], [-4, -3], [-2, -5], [1, -4], [16, -11], [5, -2], [121, -1], [44, -3], [8, 2], [15, 0], [2, 1], [14, 18], [16, 4]], [[6190, 9398], [15, -6], [2, -4], [0, -3], [6, -2], [21, -5], [5, 1], [3, 1], [4, 6], [2, 7], [-2, 16], [-2, 6], [-6, 5], [-9, 4], [-11, 1], [-21, 1], [-23, 2], [-6, -1], [-19, -5], [-2, -2], [-3, -6], [0, -3], [2, -10], [3, -2], [4, -2], [11, 0], [26, 1]], [[6909, 9128], [-19, 4], [-20, 2], [-12, 1], [-14, -1], [-7, -2], [-3, -3], [-3, -6], [0, -8], [1, -7], [2, -5], [12, -6], [59, -14], [10, 0], [20, 6], [7, 3], [3, 2], [1, 4], [0, 4], [-2, 8], [-9, 6], [-26, 12]], [[6653, 8839], [-7, 11], [-6, 5], [-6, 9], [-12, 8], [-27, 15], [-13, 5], [-17, 4], [-18, 1], [-13, -1], [-48, -11], [-14, -7], [-8, -3], [-13, -10], [1, -7], [-5, -7], [-9, -3], [-7, 0], [-14, -14], [-2, -5], [0, -3], [0, -3], [3, -1], [13, 1], [3, -2], [2, -5], [2, -2], [2, -1], [7, 4], [7, -2], [17, -12], [32, -4], [13, -1], [25, -11], [12, -4], [8, -2], [13, -1], [25, 1], [17, 0], [27, 2], [5, 1], [2, 2], [-1, 5], [1, 2], [2, 1], [2, 2], [2, 10], [0, 7], [-1, 4], [-5, 9], [-1, 3], [2, 5], [2, 5]], [[5384, 9256], [-11, 2]], [[5373, 9258], [-20, 7], [-16, 0], [-20, -3], [-15, -3], [-26, -8], [-9, -4], [-14, -4], [-39, -3], [-33, -6], [-17, -4], [-22, -6], [-10, -7], [-1, -2], [5, -4], [4, -2], [13, 1], [34, 6], [33, 3], [29, -10], [18, -1], [7, 1], [10, 6], [6, 5], [4, 1], [11, -2], [14, -6], [7, -1], [26, -2], [10, 1], [20, 3]], [[5382, 9214], [2, -1]], [[5384, 9213], [22, -2]], [[5406, 9211], [18, 2], [7, 2], [3, 4], [1, 5], [0, 5], [-2, 5], [-8, 8], [-3, 2], [-6, 1], [-4, 0], [-14, 7], [-9, 3]], [[5389, 9255], [-5, 1]], [[4797, 8936], [10, 13], [-1, 3], [-9, 2], [-9, 0], [-11, -5], [-19, -9], [-18, -5], [-19, -6], [-29, -16], [-18, -8], [-7, -4], [-6, -6], [1, -2], [6, -1], [17, -2], [21, -6], [16, -1], [22, 8], [7, 4], [26, 22], [14, 14], [6, 5]], [[5781, 8664], [-21, 11], [-16, 3], [-15, -1], [-65, -5], [-19, -3], [-7, -3], [-10, -8], [-9, -11], [2, -3], [13, -3], [17, -5], [27, -13], [5, -5], [3, -8], [21, -12], [18, -12], [10, -6], [8, -3], [3, 1], [7, 3], [10, 6], [5, 5], [3, 6], [14, 9], [13, 17], [3, 5], [2, 13], [0, 9], [-3, 4], [-4, 5], [-6, 2], [-9, 2]], [[6394, 8551], [-14, -6], [-6, -1], [-2, 1], [1, 4], [5, 5], [0, 5], [-12, 9], [-11, 3], [0, 2], [5, 2], [1, 3], [-2, 2], [-12, 6], [-12, 5], [-7, 1], [-16, 0], [-10, -2], [-9, -3], [-14, -7], [-4, 0], [-1, 2], [-1, 5], [2, 3], [3, 3], [16, 8], [29, 20], [24, 6], [16, 6], [3, 4], [-4, 4], [-5, 3], [-9, 1], [-5, -2], [-4, 0], [-2, 1], [-1, 2], [2, 5], [8, -2], [8, 5], [8, 1], [10, 2], [5, 3], [7, 6], [0, 1], [-1, 3], [-8, 10], [-4, 4], [-4, 3], [-8, 2], [-20, 3], [-7, 0], [-12, -1], [-7, -2], [-18, -5], [-28, -3], [-20, -4], [-20, -1], [-9, -3], [-65, 26], [-10, -2], [-7, -5], [0, -2], [5, -3], [9, 0], [8, 1], [2, -1], [-6, -6], [-15, 1], [-23, 5], [-33, -5], [-4, -2], [-2, -3], [1, -4], [2, -4], [4, -4], [9, -6], [10, -2], [4, -2], [5, -8], [2, -5], [-1, -2], [-12, 6], [-13, 9], [-10, 3], [-11, -1], [-16, -2], [-10, -5], [-5, -3], [0, -2], [3, -5], [3, -2], [43, -18], [10, 0], [13, 3], [7, 2], [10, 5], [8, -3], [20, -11], [14, -3], [-11, -2], [-8, 1], [-12, 5], [-9, 2], [-8, -2], [-3, -2], [-4, -4], [-1, -4], [1, -3], [7, -2], [9, 0], [3, 0], [4, -3], [4, -5], [7, -11], [0, -2], [-3, -5], [-4, -2], [-3, 1], [-11, 10], [-2, 0], [-4, -20], [-2, -4], [-1, 0], [-32, -6], [-14, -1], [-15, 1], [-6, 3], [-6, 8], [-8, 8], [-6, 3], [-11, 4], [-3, 3], [-3, 5], [-7, 5], [-7, 2], [-14, 0], [-10, -1], [-13, -7], [-4, -3], [-6, -8], [-4, -8], [-2, -7], [0, -2], [4, -6], [19, -14], [33, -13], [5, -7], [6, -2], [2, -3], [3, -5], [4, -3], [17, -5], [14, 5], [5, 1], [3, -1], [9, -4], [5, -6], [4, -3], [14, -1], [7, -1], [9, -4], [20, -18], [16, -11], [30, -18], [11, -12], [14, -11], [10, -16], [4, -4], [4, -3], [14, 1], [7, -2], [9, -5], [9, -1], [10, 1], [9, 4], [17, 11], [0, 3], [0, 3], [-3, 8], [-14, 18], [-3, 7], [5, 6], [5, 3], [2, 0], [0, -2], [-1, -3], [0, -5], [2, -6], [3, -4], [5, -2], [46, -4], [10, 1], [18, 4], [8, 5], [7, 6], [6, 3], [26, 5], [-1, 16], [-7, 7], [-3, 0], [-1, 2], [11, 12], [2, 3], [0, 4], [-1, 4], [-5, 4], [-9, 5], [-1, 1], [5, 0], [8, 2], [9, 6], [4, 5], [2, 6], [0, 8], [-4, 9], [-4, 8], [-4, 1], [-6, 1], [-15, -3]], [[7640, 7517], [7, 1], [1, 3], [-3, 4], [-7, 7], [-10, 7], [-7, 1], [-7, -7], [-1, -1], [-6, 1], [-18, 5], [-29, -7], [-14, 0], [-1, 1], [14, 11], [3, 3], [1, 3], [0, 5], [-9, 25], [-11, 15], [-9, 10], [-9, 6], [-8, 3], [-24, 5], [-7, 2], [-24, 15], [-17, 7], [-1, 1], [-15, 16], [-6, 3], [-18, 4], [-13, 2], [-15, 4], [-3, 3], [-4, 9], [-7, 5], [-18, 11], [-5, 0], [-3, -1], [-12, -17], [-6, -6], [-3, 0], [-3, 1], [-10, 21], [-14, 8], [-1, 2], [2, 2], [7, 6], [2, 4], [-2, 8], [-4, 6], [-5, 6], [-15, 6], [-4, 6], [-2, 2], [-3, 0], [-11, -4], [-9, -6], [-11, -15], [-4, -7], [-5, -13], [-3, -14], [-5, -47], [-3, -14], [-9, -27], [-3, -12], [0, -7], [2, -15], [6, -16], [2, -7], [0, -5], [-5, -5], [-8, -5], [-36, -15], [-4, -3], [-7, -8], [-10, -14], [-2, -5], [-1, -5], [2, -4], [1, -1], [8, -2], [11, 0], [5, 1], [21, 10], [21, -1], [38, 6], [3, -1], [2, -1], [2, -4], [11, -45], [6, -16], [8, -2], [12, 2], [21, 7], [13, 6], [12, 7], [6, 5], [4, 5], [9, 16], [6, 7], [4, 2], [9, 1], [9, 6], [23, 18], [5, 7], [2, 5], [1, 11], [3, 5], [7, 5], [14, 6], [10, 1], [9, 0], [4, -3], [-2, -8], [0, -5], [8, -2], [28, -5], [8, -4], [4, -22], [3, -4], [17, -2], [15, -3], [45, -15], [15, -6], [10, -3], [3, 0], [4, 3], [19, 13], [16, 9], [15, 11], [3, 4], [-14, 8], [-17, 4]], [[8024, 8037], [-15, 2], [-8, -1], [-9, -2], [-6, 0], [-15, 1], [-18, -5], [-7, -3], [-4, -3], [-15, -16], [-14, -17], [-8, -11], [-6, -17], [0, -3], [6, -21], [5, -11], [6, -11], [6, -6], [5, -2], [6, -1], [13, -1], [28, 3], [21, 0], [21, 3], [29, 10], [7, 4], [9, 7], [5, 9], [3, 11], [0, 14], [-3, 25], [1, 2], [4, 8], [0, 3], [0, 8], [-1, 4], [-2, 3], [-4, 4], [-40, 10]], [[7635, 8644], [-3, 0], [-2, -1], [-2, -5], [0, -6], [4, -5], [4, -4], [8, -4], [25, -3], [8, -2], [3, -8], [3, -9], [5, -12], [5, -10], [3, -4], [9, -4], [25, -8], [14, 0], [14, 2], [45, 10], [18, 2], [37, 2], [63, -6], [47, -3], [17, 3], [7, 4], [-3, 4], [-9, 6], [-5, 4], [2, 10], [-3, 2], [-10, 3], [-9, 4], [-4, 8], [-2, 3], [-8, 7], [-19, 5], [-3, 2], [-6, 9], [-7, 6], [-13, 5], [-52, 13], [-18, 2], [-83, -3], [-13, 1], [-27, 6], [-18, 1], [-22, 6], [-16, 1], [-11, -1], [-5, -2], [-2, -3], [-1, -6], [1, -9], [2, -7], [7, -6]], [[6318, 8909], [-3, 3], [16, 5], [7, 6], [0, 3], [-4, 7], [0, 5], [3, 8], [6, 7], [1, 4], [-1, 5], [-3, 5], [-10, 9], [-3, 4], [0, 3], [3, 3], [1, 4], [-1, 5], [-2, 4], [-6, 2], [-12, 2], [-21, 5], [-4, 3], [-19, 8], [-14, 3], [-17, -6], [-7, -3], [4, -10], [7, -8], [-22, -1], [-12, 8], [-26, 12], [-11, 1], [-20, 0], [-25, -3], [-14, -3], [-20, -7], [-4, -6], [1, -2], [4, -2], [13, -5], [37, -4], [7, -2], [8, -4], [0, -1], [-5, -1], [-24, -3], [-5, -2], [0, -1], [18, -6], [14, 0], [14, -3], [22, -3], [-20, -1], [-16, 1], [-6, -1], [-2, -2], [4, -6], [6, -4], [22, -8], [-1, -3], [-5, -1], [-7, -1], [-12, 2], [-7, 3], [-10, 5], [-51, 24], [-12, 4], [-3, 3], [1, 4], [-4, 5], [-16, 8], [-6, 1], [-20, 3], [-8, 0], [-6, -1], [-8, -5], [-11, -8], [-2, -5], [11, -5], [6, -1], [27, 0], [3, -2], [-19, -8], [-7, -6], [-1, -2], [4, -5], [8, -4], [22, -2], [-6, -5], [0, -3], [3, -5], [4, -4], [10, -7], [2, 0], [20, 2], [2, 0], [-11, -5], [-11, 0], [-12, 3], [-14, 6], [-26, 6], [-16, -1], [-9, -8], [1, -4], [-12, -7], [-25, -9], [-5, -5], [8, -6], [8, -4], [4, 0], [83, 11], [19, -2], [24, 4], [76, 7], [55, 2], [-1, -4], [-29, -1], [-13, -3], [-1, -2], [-15, -5], [-24, -13], [-34, -7], [1, -1], [-2, -6], [9, -3], [19, -3], [17, -5], [0, -2], [-24, -2], [-2, -3], [10, -15], [5, -4], [4, -2], [22, -1], [25, -2], [16, 7], [7, 0], [6, -2], [7, -2], [11, 1], [14, 0], [10, -2], [10, 1], [21, 2], [14, 0], [10, 2], [3, 2], [-3, 6], [-7, 5], [-1, 2], [2, 3], [2, 0], [5, -5], [10, -4], [3, -1], [10, 2], [1, 2], [-3, 5], [-12, 8], [-2, 19], [18, 10], [14, -5], [8, -5], [2, 0], [-6, 16], [1, 14], [-3, 1], [-32, 6]], [[5776, 9299], [12, -3], [1, -4], [-12, -12], [-6, -4], [0, -3], [6, -2], [6, -1], [6, 3], [26, 15], [19, 4], [3, -1], [8, -4], [9, -3], [1, -2], [-21, -11], [-3, -2], [0, -2], [14, -2], [38, 1], [8, -2], [3, -2], [-15, -5], [-28, -3], [-7, -4], [33, -1], [8, -4], [-6, -6], [-15, -2], [-35, 2], [-39, 5], [-8, -1], [-6, -2], [-7, -4], [1, -6], [8, -8], [9, -6], [19, -7], [15, -3], [29, -1], [20, 7], [73, 6], [-4, -5], [1, -2], [4, -4], [4, -2], [5, -1], [25, 3], [17, 0], [18, -1], [40, -8], [18, -1], [9, -7], [10, -5], [1, -2], [2, -9], [2, -2], [6, -6], [7, -4], [24, -7], [25, -5], [22, 4], [25, 2], [13, 2], [3, 2], [5, 11], [5, 3], [0, 2], [-4, 7], [-6, 5], [-33, 19], [-15, 3], [-2, 3], [1, 4], [0, 4], [-6, 5], [2, 2], [11, 5], [4, 6], [3, 2], [-2, 2], [-13, 4], [-18, 13], [-32, 11], [-37, -4], [-17, 2], [-1, 2], [2, 5], [7, 6], [-1, 3], [-3, 3], [-5, 1], [-12, 1], [-31, 11], [-13, 2], [-8, -1], [-16, -5], [-16, -4], [-1, -6], [-1, -3], [-6, -3], [-6, -3], [-1, 3], [1, 3], [-1, 1], [-11, 4], [4, 2], [3, 4], [0, 4], [0, 2], [-1, 2], [-20, 16], [-21, 8], [-18, 2], [-22, 5], [-20, -1], [-68, -4], [-41, 1], [-4, -2], [-6, -7], [-4, -9], [-1, -6], [1, -7], [2, -3], [18, 0], [31, 2]], [[6787, 9534], [-10, 10], [-8, 6], [-17, 7], [-15, 4], [-48, 8], [-23, 2], [-21, -1], [-35, 0], [-9, -2], [-3, -1], [-1, -1], [0, -3], [1, -3], [2, -3], [6, -2], [17, -1], [37, -1], [10, -3], [4, -3], [0, -3], [-4, -4], [-5, -2], [-7, 0], [-30, 3], [-30, -6], [-23, -3], [-36, 2], [-22, -5], [-18, -17], [-1, -3], [25, -3], [31, -7], [15, -13], [30, -3], [14, -3], [7, -3], [-43, -1], [-19, 2], [-12, 0], [-10, 9], [-16, 4], [-21, 1], [-16, 4], [-16, 0], [-16, -4], [-2, -16], [20, -10], [22, -9], [5, -3], [-16, 0], [-20, 2], [-8, 0], [-17, -4], [-4, -2], [0, -2], [2, -3], [4, -3], [7, -2], [15, -3], [9, -1], [20, 2], [19, -11], [16, 0], [46, 8], [26, -1], [-4, -2], [-21, -5], [-2, -1], [0, -3], [1, -4], [-1, -2], [-3, 0], [-57, 0], [-30, 2], [-77, 8], [11, -13], [2, -6], [2, -7], [9, -8], [36, -17], [11, -4], [9, -1], [14, -1], [20, 0], [25, 3], [30, 6], [14, 1], [-6, -6], [-3, -2], [-60, -12], [-21, -2], [-7, -3], [-6, -13], [6, -3], [26, -4], [17, -8], [4, 1], [16, 5], [34, 6], [9, 1], [13, 0], [12, 0], [1, -1], [-7, -4], [-6, -5], [2, -1], [4, -1], [8, -1], [29, 8], [13, 1], [27, 8], [16, 2], [14, 0], [12, -1], [18, -8], [43, -1], [30, 1], [-3, -1], [-40, -5], [-53, -5], [-10, -11], [-12, -3], [-18, -1], [-17, -1], [-50, -12], [-17, -5], [0, -3], [1, -3], [3, -2], [16, -7], [44, -7], [13, -4], [-3, -1], [-28, 1], [-4, -1], [-1, -2], [6, -5], [13, -8], [9, -4], [12, -1], [11, 2], [20, -1], [60, -5], [5, -3], [-33, -2], [-33, -4], [-10, -3], [3, -4], [10, -5], [26, -9], [34, -9], [38, -5], [38, -4], [23, -1], [18, 2], [4, 2], [-2, 4], [-4, 4], [-5, 2], [-14, 5], [3, 1], [12, 1], [13, 0], [12, -2], [8, -2], [5, -4], [7, -2], [17, -6], [6, -3], [4, -1], [3, 2], [-2, 3], [-8, 9], [-11, 11], [-19, 15], [-5, 6], [3, 7], [3, -1], [5, -3], [21, -16], [14, -8], [29, -19], [10, -3], [11, 0], [2, 1], [5, 6], [6, 11], [4, 7], [-2, 8], [-7, 10], [-2, 4], [3, 2], [10, 0], [22, -12], [11, -2], [8, 2], [5, 5], [0, 3], [0, 4], [-2, 2], [-3, 1], [-14, 3], [-2, 3], [5, 2], [1, 6], [-1, 15], [2, 7], [4, 5], [5, 3], [13, 5], [3, 1], [1, -2], [-8, -10], [-2, -4], [-1, -2], [1, -5], [2, -12], [5, -5], [19, -4], [10, 5], [18, 11], [13, 6], [5, 4], [4, 9], [4, 1], [15, -1], [7, 2], [13, 6], [28, 7], [62, 13], [8, 3], [11, 6], [-2, 5], [-9, 7], [-25, 17], [-11, 9], [-2, 1], [-6, -3], [-4, -2], [-11, -11], [-4, 0], [-7, 8], [-7, 6], [-4, 2], [-8, 2], [-24, 1], [-16, -6], [-5, 0], [-25, -3], [-4, 1], [6, 6], [6, 4], [7, 17], [6, 10], [-8, 9], [-9, 9], [-10, 0], [-25, 4], [-16, 1], [-5, 1], [4, 5], [19, 6], [1, 2], [-1, 11], [-2, 6], [-2, 3], [-22, 5], [-12, 1], [-23, 0], [-8, -1], [-9, -4], [-2, -4], [2, -7], [2, -4], [18, -4], [10, -7], [4, -4], [0, -2], [-10, 3], [-16, -1], [-25, 4], [-12, 4], [-14, 8], [-1, 3], [5, 8], [-1, 2], [-3, 2], [0, 2], [5, 4], [0, 2], [-3, 2], [-5, 4], [-7, 2], [-15, 1], [-12, -1], [-9, -3], [-5, -1], [-27, 6], [-25, 4], [-8, 2], [1, 5], [-1, 2], [-3, 4], [-29, 10], [-16, 9], [-38, 23]], [[7108, 8760], [17, 4], [29, -2], [17, -1], [8, 2], [-5, 8], [6, -1], [24, -5], [10, 3], [8, 1], [11, -5], [12, 0], [20, 4], [3, 1], [1, 3], [1, 4], [3, 0], [8, -7], [9, -3], [7, 0], [3, 1], [2, 9], [4, 0], [7, -4], [8, -3], [12, -3], [18, -1], [14, 1], [29, 5], [19, 1], [7, 2], [9, 8], [2, 2], [4, 11], [-2, 4], [-10, 4], [-1, 2], [-3, 5], [2, 1], [8, -2], [15, -7], [5, -1], [4, 0], [1, -3], [-2, -7], [0, -4], [4, -8], [6, -5], [4, -2], [15, -5], [7, 0], [18, 1], [26, -6], [10, -1], [10, 0], [16, 3], [20, 6], [9, 2], [73, 1], [1, 1], [4, 8], [0, 6], [0, 5], [2, 4], [3, 2], [-3, 3], [-10, 6], [-2, 3], [4, 1], [7, -2], [20, -7], [33, 6], [9, 4], [-5, 5], [-5, 3], [-11, 4], [-5, 0], [-23, -3], [-8, -1], [-10, 2], [-9, 4], [2, 2], [29, 8], [27, 9], [9, 7], [0, 4], [-6, 11], [-5, 8], [-6, 1], [-28, 1], [-12, 1], [-3, 1], [13, 6], [0, 3], [-3, 2], [-12, 6], [-16, 1], [-36, 0], [-10, 2], [-4, 1], [-1, 2], [3, 6], [-9, 3], [-29, 4], [-39, 4], [-16, 1], [-15, -2], [-41, -7], [-11, -1], [-39, 7], [-15, 1], [-6, -5], [-9, -2], [-37, -12], [-11, 0], [-18, -1], [-30, -9], [-16, 1], [-30, -6], [-7, -3], [12, -7], [-3, -6], [-22, 1], [-16, 4], [-8, 3], [-21, 3], [-34, 15], [-8, -3], [-13, -12], [-3, 1], [-5, 6], [-7, 3], [-36, -7], [-29, 16], [-5, 1], [-6, 0], [-5, -1], [-5, -3], [-1, -4], [3, -6], [1, -4], [-2, -5], [-3, -1], [-3, 0], [-28, 13], [-5, 1], [-23, -1], [1, 2], [21, 8], [8, 6], [4, 4], [0, 3], [-6, 4], [-18, 7], [-11, -1], [-3, 1], [-8, 8], [-9, 5], [-9, 1], [-11, 7], [-6, 3], [-35, 2], [-7, 4], [-17, 4], [-20, 2], [-10, 7], [11, 1], [34, -5], [39, -3], [70, 4], [9, 3], [4, 2], [1, 3], [-5, 5], [-120, 16], [-37, 1], [-6, 1], [-3, 5], [4, 1], [5, 0], [5, -1], [31, -2], [19, -2], [6, 4], [-1, 2], [-4, 3], [-10, 5], [-30, 9], [-14, 2], [-19, 1], [-18, -1], [-39, -7], [-33, -2], [-21, 2], [-33, -17], [-8, -3], [3, 5], [5, 4], [8, 6], [4, 6], [5, 5], [1, 2], [0, 3], [-2, 5], [-1, 2], [-4, 2], [-11, 3], [-14, 7], [-16, 5], [-10, 0], [-13, -1], [-14, 1], [-25, 5], [-39, 7], [-39, 5], [-17, 1], [-16, -2], [-24, -6], [-13, -2], [-11, 0], [-5, -1], [-1, -3], [-4, -4], [4, -3], [8, -4], [18, -5], [2, -2], [-4, -3], [-11, -1], [-6, 0], [-16, 5], [-1, -4], [0, -2], [1, -2], [3, -1], [16, -3], [51, -16], [8, 0], [17, 2], [-4, -2], [-7, -4], [-17, -2], [-2, -3], [6, -5], [9, -4], [30, -6], [14, -12], [21, 0], [20, 4], [12, 0], [15, -2], [41, -1], [14, 0], [2, 2], [7, 3], [19, 6], [9, 0], [8, -1], [16, -17], [13, -11], [18, -15], [13, -9], [9, -8], [4, -6], [3, -8], [2, -8], [-1, -3], [-2, -3], [-17, -15], [-6, -9], [-1, -2], [0, -5], [1, -5], [2, -4], [3, -4], [11, -6], [10, -7], [1, -2], [-8, -4], [-1, -2], [6, -12], [11, -19], [6, -6], [16, -5], [9, -5], [3, 0], [13, 2], [14, -3], [2, 0], [-2, 8], [2, 3], [20, 9], [0, -2], [-6, -6], [-1, -4], [14, -2], [18, -10], [15, 0], [26, -6], [13, -1], [22, 0], [9, 2], [14, 5], [5, 3], [1, 3], [-1, 4], [-2, 4], [3, 2], [5, -1], [5, 1], [3, 3], [3, 2], [3, 0], [2, -3], [2, -6], [3, -3], [5, 3], [8, 10], [11, 4], [4, 0], [4, -3], [7, -3], [-3, -4], [-7, -10], [-5, -6], [-2, -5], [0, -3], [5, -4], [6, -2], [32, 0], [32, -3]], [[6415, 9158], [9, 3], [36, 1], [26, 5], [17, 4], [6, 1], [13, 0], [9, 3], [12, 9], [0, 4], [-4, 4], [-9, 5], [-18, 5], [5, 4], [20, 6], [9, 4], [0, 2], [-2, 3], [-9, 5], [-29, 8], [-11, 2], [-32, -1], [-18, 3], [-4, 2], [1, 3], [-2, 3], [-16, 8], [-8, 2], [-27, 4], [-18, 5], [-16, 3], [-17, 1], [-34, 1], [-13, 0], [-10, -3], [0, -3], [4, -7], [15, -12], [2, -4], [-19, -5], [-1, -2], [1, -2], [3, -5], [1, -1], [11, -3], [4, -2], [1, -7], [16, -8], [2, -3], [38, -4], [23, -5], [6, -1], [-1, -1], [-6, -3], [-14, -1], [-34, -2], [1, -2], [2, -3], [15, -8], [26, -5], [6, -3], [-3, -3], [-1, -3], [2, -4], [4, -2], [12, 0], [18, 5]], [[5384, 9186], [-35, 2]], [[5349, 9188], [-21, -1], [-11, -2], [-26, 1], [-84, -10], [-39, -7], [-16, -3], [-13, -1], [-2, -1], [-4, -5], [-1, -3], [0, -2], [0, -5], [7, -7], [1, -5], [4, -5], [-1, -1], [-4, -3], [-2, -2], [1, -2], [3, -4], [9, -2], [9, -4], [22, -4], [21, -9], [15, -3], [17, 0], [56, 10], [13, 1], [12, -1], [17, 2], [24, 6], [13, 4], [4, 5], [2, 6], [0, 11], [-1, 3], [-2, 3], [-2, 1], [-8, 1], [-33, 0], [-7, 2], [-4, 2], [-1, 2], [9, 3], [43, 5]], [[5369, 9164], [15, 3]], [[5384, 9167], [18, 4]], [[5402, 9171], [7, 4], [4, 7], [0, 2], [-3, 1]], [[5410, 9185], [-26, 1]], [[4719, 8989], [-12, 6], [-1, 2], [2, 4], [4, 1], [13, 0], [8, 3], [3, 4], [1, 9], [3, 5], [6, 4], [7, 3], [10, 1], [15, 4], [5, 0], [3, -2], [-2, -6], [-8, -10], [-5, -9], [-3, -9], [-1, -6], [2, -5], [12, -7], [8, -3], [19, -6], [11, 0], [9, 1], [6, 2], [3, 3], [5, 6], [2, 4], [1, 7], [-1, 4], [2, 4], [41, 5], [19, 4], [11, 5], [7, 4], [3, 3], [0, 3], [-6, 6], [-16, 10], [-2, 3], [5, 2], [6, 0], [15, -1], [8, 4], [-3, 3], [-17, 7], [-16, 9], [-4, 4], [9, 5], [46, 10], [9, 3], [2, 2], [0, 2], [-6, 5], [-35, 11], [-15, 7], [-12, 3], [-12, 0], [-25, -2], [-8, -3], [-7, -4], [-1, -2], [1, -2], [21, -4], [4, -2], [-7, -4], [-3, -1], [-17, 1], [-7, 1], [-5, -3], [-5, -2], [-11, 0], [-45, 8], [-62, -6], [-21, -3], [-18, -8], [-13, -7], [-26, -12], [-28, -17], [-9, -3], [-4, -2], [-6, -9], [-3, -2], [-40, -12], [-8, -4], [-8, -5], [-27, -19], [-4, -2], [-58, -5], [-4, -1], [-8, -4], [-19, -15], [-8, -7], [-2, -4], [16, 3], [5, 1], [3, -1], [0, -1], [-2, -1], [0, -2], [5, -3], [0, -2], [-5, -5], [-3, -1], [1, -3], [4, -4], [4, -2], [10, -1], [8, 2], [19, 6], [11, 2], [17, -1], [20, -5], [8, 0], [9, 0], [15, 5], [9, 17], [4, 2], [2, -1], [4, -1], [3, -4], [7, -12], [6, -3], [3, -6], [5, -10], [3, -5], [4, 0], [16, 3], [19, 1], [13, 6], [6, 4], [4, 4], [5, 0], [1, 2], [0, 4], [-1, 2], [-14, 6], [-1, 2], [8, 5], [0, 4], [-1, 6], [0, 4], [5, 6], [5, 1], [2, -2], [3, -5], [7, -7], [9, -7], [6, -4], [7, 0], [6, 2], [3, 3], [8, 11], [3, 2], [13, 7], [2, 3], [-2, 6]], [[5384, 8886], [-35, 2]], [[5349, 8888], [-21, 0], [-12, -1], [-13, 0], [-8, 3], [-9, 5], [-8, 7], [-6, 10], [-4, 6], [-3, 1], [-12, 2], [-13, -1], [-14, 1], [-2, 1], [3, 3], [13, 5], [1, 3], [-36, 16], [-28, 15], [-22, 5], [-15, 2], [-15, -1], [-35, -5], [-8, 2], [-10, 9], [-3, 3], [-1, 2], [0, 6], [-2, 3], [-5, 3], [-8, 3], [-18, 3], [-18, 1], [-17, -1], [-45, -7], [-15, -9], [-4, -4], [0, -4], [0, -3], [2, -2], [10, -2], [50, -3], [11, -2], [8, -2], [-13, -1], [-64, 2], [-22, 2], [-12, -1], [-18, -6], [-11, -5], [-1, -3], [4, -7], [-2, -3], [-7, -4], [1, -3], [16, -5], [9, -1], [56, 2], [47, 0], [-14, -3], [-23, -3], [-28, 0], [-42, -4], [-32, -5], [-13, -3], [-5, -3], [-10, -8], [2, -4], [9, -2], [46, -1], [30, 2], [70, 12], [0, -2], [-1, -2], [-9, -4], [-6, -3], [-57, -14], [-11, -2], [-52, 0], [-20, -1], [-8, -1], [-6, -2], [-4, -3], [-10, -7], [-5, -5], [-1, -3], [0, -2], [2, -5], [5, -3], [38, -6], [13, -1], [28, 3], [26, -15], [31, -9], [4, 1], [6, 4], [2, 5], [3, 3], [5, 3], [5, 1], [5, 0], [5, -1], [8, -7], [4, -4], [4, -3], [4, -1], [12, 3], [32, 10], [7, 6], [0, 4], [-11, 10], [-1, 2], [2, 1], [4, 0], [11, -4], [9, -1], [4, 6], [4, 12], [1, 3], [3, 2], [3, -1], [5, -5], [3, -1], [22, 4], [9, 1], [-2, -3], [-20, -9], [-4, -2], [-3, -5], [-3, -6], [2, -2], [1, -7], [0, -2], [4, -3], [7, -2], [28, 3], [30, 1], [7, 2], [5, 2], [7, 6], [4, 2], [4, 1], [9, -2], [18, -8], [3, 0], [16, 1], [17, 3], [12, 0], [12, 3], [22, 8], [7, 0], [5, -4], [-4, -3], [-14, -8], [-19, -9], [-12, -4], [-9, -2], [-13, 0], [-19, 1], [-36, -2], [-13, -2], [-38, -12], [-41, -7], [-21, -6], [-14, -6], [-5, -5], [9, -8], [7, -3], [26, -10], [13, -5], [12, -2], [38, -4], [38, 2], [61, 10], [34, 10], [27, 6], [14, 6], [10, 8], [6, 3], [12, 4], [17, 3]], [[5371, 8804], [13, 1]], [[5384, 8969], [7, -3]], [[5391, 8966], [15, -1], [18, -8], [4, -4], [1, -4], [-1, -4], [-2, -2], [-32, -11], [6, -8], [65, -19], [2, -3], [2, -6], [-1, -4], [-3, -6], [-4, -3], [-7, -1]], [[5454, 8882], [-70, 4]], [[5384, 8997], [-21, -8]], [[5363, 8989], [-3, -2], [0, -3], [2, -5], [3, -3], [4, -2]], [[5369, 8974], [15, -5]], [[5384, 8805], [38, 4]], [[5422, 8809], [39, 15], [13, 6], [13, -3], [2, -2], [-3, -2], [-6, -1], [12, -4], [9, -2], [10, 0], [9, 1], [16, 4], [16, 2], [27, -6], [24, -3], [8, 0], [7, 2], [28, 8], [39, 9], [9, 5], [8, 7], [13, 26], [1, 11], [13, 15], [3, 8], [0, 5], [-7, 16], [-3, 6], [-2, 2], [-6, 2], [-15, 5], [-38, 6], [-10, 0], [-11, -4], [-10, -6], [-3, -2], [-2, -3], [4, -7], [4, -3], [6, -3], [-1, -1], [-15, -3], [-1, -5], [0, -6], [-1, -1], [-4, 11], [-6, 8], [-2, 2], [-5, 2], [-6, 2], [-15, 1], [-10, 0], [-12, -3], [-17, -9], [-2, -1], [-6, 1], [4, 4], [17, 12], [2, 2], [0, 2], [0, 3], [-4, 4], [-6, 3], [-13, 1], [-11, -1], [-10, 0], [-7, 1], [-1, 2], [2, 4], [5, 4], [10, 6], [4, 4], [-5, 11], [-12, 7], [-13, 6], [0, 1], [-2, 6], [-1, 4], [-5, 6], [-1, 3], [2, 2], [7, 6], [3, 4], [1, 3], [-2, 2], [-5, 0], [-21, 8], [-21, -1], [-9, -3], [-9, -3], [-13, -8], [-28, -20], [-9, -5]], [[5385, 8997], [-1, 0]], [[6839, 8706], [-42, 2], [-19, -1], [-27, -5], [-7, 2], [-13, 8], [-8, 3], [-14, 3], [-31, 6], [-18, 2], [-11, -1], [-18, -6], [-12, 1], [-41, -2], [-19, -3], [-19, -5], [-5, -2], [-7, -5], [-1, -3], [-1, -3], [0, -3], [1, -3], [6, -9], [13, -10], [7, -4], [9, -2], [-1, -1], [-9, 0], [-14, 3], [-30, 8], [-5, 0], [-9, -3], [-5, -4], [-1, -3], [0, -4], [0, -9], [3, -27], [1, -18], [0, -7], [-1, -14], [0, -6], [0, -7], [2, -6], [3, -6], [22, -33], [5, -19], [2, -19], [-2, -18], [14, -2], [31, 4], [8, 0], [28, -5], [8, 3], [5, 12], [8, 14], [24, 20], [1, 2], [1, 7], [-1, 4], [-2, 3], [-16, 13], [-11, 4], [-18, 4], [-5, 3], [49, 5], [18, 0], [73, -10], [12, 1], [9, 3], [17, 12], [9, 7], [13, 13], [12, 14], [3, 6], [-3, 3], [-7, 2], [20, 6], [3, 2], [14, 13], [5, 6], [2, 4], [4, 3], [13, 7], [15, 12], [8, 8], [6, 8], [2, 6], [-8, 4], [-13, 5], [-35, 7]], [[6493, 8097], [-13, 8], [-8, 6], [-7, 9], [-9, 12], [-9, 15], [-9, 10], [-31, 15], [-13, 5], [-9, 5], [-8, 7], [-11, 7], [-3, 1], [-3, -1], [-3, -4], [-4, -1], [-3, 1], [-2, 3], [1, 2], [5, 2], [1, 2], [-3, 3], [-15, 7], [-6, 5], [-8, 2], [-7, 0], [-15, -3], [-9, -4], [-3, -2], [-3, -3], [-2, -8], [0, -2], [1, -5], [10, -14], [9, -6], [-9, 1], [-5, 2], [-13, 9], [-7, 2], [-5, -1], [0, -3], [1, -2], [3, -3], [4, -3], [-7, 0], [-2, -2], [2, -4], [5, -6], [1, -3], [0, -2], [-4, -3], [-17, -10], [-14, -6], [-13, -3], [-29, -2], [-4, -3], [-4, -6], [0, -2], [5, -9], [4, -5], [10, -5], [5, -1], [12, 0], [2, 1], [-2, 3], [3, 2], [7, 2], [4, 0], [2, -2], [2, -3], [0, -3], [2, -3], [4, -3], [6, -1], [13, -1], [8, 2], [4, 3], [4, 0], [2, -1], [2, -3], [0, -4], [1, -3], [2, -1], [27, -8], [14, -5], [18, -10], [16, -2], [19, 2], [32, -10], [15, 2], [10, 4], [19, 12], [10, 2], [7, 7], [9, 6], [6, 1], [11, 0], [8, 4], [5, 3], [2, 2], [-4, 6], [-4, 2], [-5, -1], [-11, -6]], [[5384, 8585], [-1, 0]], [[5383, 8585], [-6, 1], [-32, 1], [-12, 1], [-2, -7], [10, -10], [22, -8], [6, -4], [-1, -9], [0, -2], [-18, -4], [-5, -4], [-21, -7], [-14, -12], [-14, -8], [-3, -1], [-6, 1], [-2, 1], [0, 3], [1, 3], [2, 3], [-1, 1], [-4, -1], [-10, -5], [-8, -7], [-10, -6], [-6, 4], [-11, 3], [6, 3], [16, 6], [20, 16], [8, 11], [-2, 6], [-14, 6], [-45, 14], [-32, 6], [-23, 6], [-24, 1], [-11, -2], [-6, -4], [-12, -10], [-3, -5], [-1, -8], [1, -3], [-1, -4], [-6, -5], [-4, 0], [-5, 3], [-20, -3], [-17, -3], [-13, -4], [-9, -1], [-5, 1], [2, 4], [17, 14], [8, 7], [5, 7], [3, 5], [1, 6], [1, 7], [-1, 7], [-3, 13], [-2, 6], [-3, 4], [-3, 3], [-8, 4], [-26, 5], [-70, -19], [-79, -18], [-30, -11], [-22, -6], [-23, -9], [-44, -24], [-19, -11], [-6, -8], [-2, -5], [2, -3], [5, -4], [11, -7], [3, -3], [-1, -2], [-12, -7], [-17, -5], [-27, -21], [-1, -2], [-2, -6], [-1, -13], [1, -5], [3, -4], [6, -5], [22, -5], [16, -1], [37, 3], [12, 0], [-11, -12], [-21, -4], [-6, -7], [1, -2], [2, -2], [20, -5], [16, 0], [30, 5], [43, 1], [91, 12], [19, -4], [3, -2], [-3, -2], [-10, -1], [-20, 2], [-19, -2], [-5, -1], [0, -4], [10, -3], [2, -2], [-12, -2], [-14, -1], [-15, -2], [-30, -7], [-38, -8], [-39, -6], [-9, -3], [-26, -12], [-6, -4], [-2, -4], [8, -9], [53, -30], [45, -4], [51, 3], [8, -1], [11, -3], [7, -1], [53, 2], [36, 3], [19, 2], [20, 4], [8, 0], [20, 2], [16, 0], [28, -5], [19, -4], [79, -20], [30, -11], [8, -5], [-6, -3], [-6, -1], [-31, 0], [-6, -3], [-20, -3], [-9, 0], [-44, 5], [-35, 0], [-19, 2], [-19, 3], [-33, 0], [-72, -6], [-79, -10], [-45, -9], [-2, -1], [-2, -4], [1, -8], [2, -12], [2, -7], [2, -3], [8, -10]], [[4846, 8201], [11, -8]], [[4857, 8193], [11, -8]], [[4868, 8185], [8, -8], [3, -6], [2, -3], [2, -1], [32, -11], [18, -3], [19, -3], [35, -2], [36, 1], [29, -1], [19, -2], [29, -6], [1, -2], [6, -18], [1, -8], [-2, -14], [5, -9], [16, -19], [9, -8], [8, -5], [8, -1], [12, -1], [15, 1], [28, 4], [60, 2], [16, 1], [14, 5], [13, 1], [9, -2], [29, 4], [39, 2], [38, 6], [40, 10], [17, 8], [13, 8], [15, 4], [38, 2], [33, 6], [7, 4], [17, 14], [7, 3], [14, 20], [7, 7], [8, 4], [9, 0], [9, -3], [5, -4], [1, -5], [-1, -7], [1, -4], [0, -3], [6, -3], [10, -4], [10, -2], [16, 1], [21, -2], [20, -5], [19, -2], [1, -1], [-1, -2], [-10, -9], [-1, -2], [5, -5], [41, -5], [8, 5], [8, 1], [22, -7], [19, -2], [27, -5], [24, 3], [21, -1], [4, 1], [8, 4], [19, 3], [10, 4], [29, 7], [9, 5], [6, 12], [0, 6], [-3, 4], [-4, 3], [-2, 1], [-7, -1], [-4, 2], [-2, 3], [0, 3], [0, 3], [1, 3], [6, 6], [0, 2], [-5, 5], [-8, 2], [-23, -1], [-8, -5], [-17, -7], [-9, -4], [-15, -15], [-3, -1], [1, 4], [5, 15], [1, 8], [-1, 4], [-1, 2], [-3, 0], [-3, 1], [-12, 9], [-10, 5], [-3, 4], [2, 2], [6, 2], [4, -1], [19, -9], [11, -4], [13, -2], [10, 1], [4, 2], [2, 2], [0, 4], [-1, 4], [-1, 4], [-2, 3], [5, 5], [14, 6], [8, 4], [4, 0], [7, -2], [18, -10], [10, -4], [7, -1], [3, 3], [3, 4], [4, 9], [2, 2], [2, -2], [5, -10], [4, -5], [10, -3], [13, -2], [5, 2], [3, 4], [3, 11], [-1, 7], [-5, 19], [-5, 10], [-4, 2], [-4, 2], [-7, 0], [-25, -2], [-4, 5], [-1, 9], [-1, 2], [-3, 1], [-4, 1], [-16, -1], [-4, 1], [-29, 15], [-17, 7], [-12, 6], [-23, 16], [-3, -1], [0, -4], [6, -9], [0, -2], [-1, -1], [-4, -1], [-3, 0], [-7, 5], [-7, 3], [-22, 7], [-21, 12], [-8, 3], [-16, 20], [-27, 16], [-4, 4], [0, 4], [6, 13], [4, 6], [4, 5], [2, 5], [1, 6], [-1, 5], [-1, 2], [-1, 10], [-10, 14], [-19, 20], [-4, 4], [-5, 9], [-27, 52], [-1, 5], [-4, 12], [-2, 8], [-3, 5], [-4, 4], [-1, 4], [1, 3], [0, 2], [-6, 8], [-10, 9], [-15, 10], [-21, 7], [-30, 14], [-27, 9], [-9, 1], [-7, -4], [-3, -5], [-3, -1], [-29, 11], [-18, 5], [-23, 2], [-5, -5], [1, -3], [7, -5], [4, -2], [-4, -1], [-10, -1], [-7, -2], [-2, -4], [0, -5], [18, -53], [5, -8], [1, -4], [-1, -4], [2, -4], [4, -2], [2, -3], [2, -11], [1, -6], [7, -18], [12, -14], [18, -16], [-5, -1], [-1, -1], [1, -2], [3, -3], [-1, -2], [-26, -12], [-5, -6], [-3, -4], [-2, 0], [-8, 1], [-8, 5], [-9, 3], [-4, 2], [-1, 3], [-5, 18], [-15, 28], [-8, 21], [-4, 11], [-6, 10], [-4, 7], [-3, 2], [-12, 1], [-4, 2], [1, 5], [1, 2], [-1, 2], [-4, 2], [-6, 4], [-18, 6], [-9, 4], [-11, 8]], [[5414, 8573], [-30, 12]], [[4413, 8760], [-65, -4], [-94, -10], [-1, -3], [5, -2], [1, -4], [4, -3], [24, -34], [6, -7], [7, -5], [17, -3], [6, -5], [0, -2], [-18, -15], [-7, -14], [-23, -12], [-13, -21], [-4, -5], [-13, -9], [-2, -6], [1, -2], [7, -4], [7, 0], [4, -2], [1, -2], [1, -3], [0, -2], [-2, -2], [-26, -8], [-3, -2], [-3, -4], [-1, -4], [0, -5], [-1, -10], [1, -4], [3, -1], [-1, -2], [-6, -4], [-8, -4], [-10, -8], [-6, -3], [-4, -5], [-6, -9], [-5, -5], [-4, -1], [-1, -2], [1, -4], [1, -2], [2, -1], [-13, -5], [-1, -1], [1, -6], [-1, -3], [-1, -3], [-5, -6], [2, -1], [4, -1], [37, 2], [6, -3], [7, -3], [28, -11], [58, -18], [4, -3], [15, -15], [6, -4], [6, -8], [16, -24], [6, -6], [8, -5], [9, -4], [12, -1], [7, 2], [10, 3], [13, 8], [30, 8], [31, 21], [4, 1], [6, 0], [6, -5], [6, -2], [24, 3], [17, 4], [24, 7], [8, 6], [4, 5], [2, 3], [6, 31], [4, 11], [9, 17], [1, 10], [7, 2], [25, 1], [19, 7], [8, 7], [22, 29], [4, 4], [9, 5], [75, 26], [40, 16], [31, 8], [44, 17], [19, 5], [19, 3], [36, 11], [6, 3], [3, 3], [1, 4], [-1, 5], [-4, 5], [-4, 4], [-9, 6], [-25, 9], [-59, 33], [-18, 9], [-19, 8], [-24, 7], [-15, 3], [-20, 1], [-18, 0], [-26, -2], [-6, -2], [-9, -4], [-22, -18], [-5, -2], [-2, -1], [-1, 1], [-1, 12], [-1, 5], [-1, 2], [-3, 1], [-8, 1], [-12, 0], [-20, -11], [-1, 0], [0, 2], [2, 3], [12, 9], [-30, 3], [-47, 12], [-25, 8], [-19, 8], [-14, 4], [-15, 2], [-19, 0], [-67, -9]], [[9519, 5501], [5, -1], [2, 1], [1, 2], [0, 3], [-1, 1], [-3, -1], [-2, -1], [-2, -4]], [[6557, 8935], [-7, 0], [-3, -1], [-2, -2], [1, -2], [3, -3], [2, -3], [1, -5], [2, -3], [3, -3], [10, -2], [7, 0], [15, 2], [2, 1], [1, 1], [-3, 5], [-6, 7], [-2, 4], [-3, 6], [-2, 2], [-3, 1], [-16, -5]], [[7719, 6118], [4, -4], [5, -3], [8, 0], [3, 0], [2, 2], [2, 3], [1, 4], [4, 5], [0, 2], [-5, 2], [-24, -11]], [[7794, 6639], [-4, 0], [-3, -2], [-3, -4], [-2, -5], [0, -6], [-3, -7], [-3, -8], [-1, -4], [3, 0], [4, 2], [5, 3], [4, 5], [3, 5], [1, 7], [-1, 14]], [[7710, 6565], [12, 8], [6, -1], [2, 1], [3, 2], [4, 5], [14, 28], [3, 5], [4, -4], [0, -3], [0, -2], [-11, -26], [4, -5], [4, 1], [7, 22], [5, 11], [6, 12], [3, 6], [-2, 1], [-1, 2], [0, 3], [-2, 6], [-2, 2], [-7, 2], [-1, 2], [-2, 6], [-2, 2], [-5, 1], [-2, 2], [-2, 4], [-3, -16], [-3, -10], [-4, -12], [-5, -8], [-6, -3], [-1, 1], [3, 7], [3, 7], [3, 16], [0, 8], [-1, 3], [-1, 0], [-1, -2], [0, -7], [-1, -7], [-2, -9], [-6, -10], [-15, -15], [-17, -21], [0, -3], [1, -2], [7, -3], [9, 9], [17, 21], [2, 1], [3, -2], [0, -2], [-20, -34]], [[7696, 6685], [0, -6], [4, -2], [2, 2], [2, 5], [1, 3], [-3, 2], [-3, 0], [-3, -4]], [[7709, 6766], [3, -1], [1, 1], [1, 5], [-1, 5], [-2, 1], [-2, -1], [-3, -5], [-1, -2], [1, -3], [-2, -4], [2, -5], [2, 1], [1, 8]], [[7716, 6626], [4, 7], [4, 9], [-2, 0], [-6, -7], [-5, -3], [-5, 0], [-3, -1], [-2, -2], [-10, -4], [-4, -4], [-2, -8], [4, -2], [4, 1], [23, 14]], [[7669, 7020], [-2, -5], [0, -1], [3, 0], [3, 2], [4, 5], [2, 3], [-4, 2], [-3, -6], [-3, 0]], [[7688, 6278], [-2, -3], [0, -2], [3, -3], [2, -2], [5, -1], [0, 3], [-3, 6], [-5, 2]], [[7695, 7036], [4, 5], [-4, 3], [-7, -1], [-3, -2], [-3, -4], [-3, -7], [8, 1], [8, 5]], [[7726, 6674], [-2, -11], [2, 0], [3, 2], [1, 1], [1, 2], [0, 4], [0, 3], [-2, 3], [-3, 4], [-1, -1], [-1, -2], [0, -1], [2, -4]], [[6388, 8579], [13, 1], [5, 1], [3, 2], [3, 3], [2, 6], [1, 4], [-4, 3], [-7, 3], [-2, 2], [-2, 3], [-5, 3], [-12, -4], [-4, -3], [-2, -4], [-1, -4], [1, -8], [11, -8]], [[6357, 8767], [4, 4], [1, 2], [-2, 2], [-8, 4], [-8, -3], [-18, -11], [2, -2], [6, -3], [23, 7]], [[6232, 8683], [12, 1], [4, 1], [6, 3], [10, -1], [22, 3], [10, 2], [22, 9], [8, 5], [6, 5], [1, 2], [0, 3], [-3, 2], [-8, 0], [-20, -1], [-40, -8], [-18, -2], [-7, -1], [-4, -2], [-3, -3], [-7, -2], [-19, -3], [-5, -1], [-1, -2], [3, -2], [6, -2], [25, -6]], [[6909, 8159], [-2, 7], [-2, 2], [-2, -2], [-5, 1], [-5, -1], [-6, -3], [-2, -3], [2, -4], [7, -8], [3, -1], [3, 0], [2, 2], [7, 10]], [[6876, 8160], [-5, 1], [-6, -2], [-2, -3], [0, -5], [7, 0], [1, -4], [3, -1], [4, -5], [6, 2], [1, 3], [-2, 5], [-2, 4], [-5, 5]], [[8123, 7375], [-8, 4], [-5, 1], [-5, -1], [0, -2], [10, -5], [19, -5], [15, -2], [4, 1], [1, 2], [-2, 3], [-7, 2], [-22, 2]], [[8092, 8053], [-2, 4], [-5, 6], [-8, 12], [-7, 5], [-9, 1], [-7, -1], [-6, -6], [-2, -6], [-1, -4], [0, -3], [7, -6], [19, -8], [8, -8], [6, 1], [3, 2], [2, 4], [1, 2], [-2, 1], [0, 2], [3, 2]], [[7798, 7149], [-4, -5], [0, -2], [10, 2], [13, 3], [7, 3], [3, 4], [-12, -1], [-17, -4]], [[7757, 8039], [-2, -9], [0, -3], [9, -6], [6, 1], [12, 9], [-3, 5], [-7, 5], [-15, -2]], [[7949, 7470], [-9, 8], [-28, 13], [-18, -11], [24, -16], [10, -5], [11, -3], [8, 1], [2, 13]], [[8886, 7356], [-3, 2], [-9, 0], [-15, 1], [-2, -2], [0, -2], [2, -2], [-2, -1], [-6, -2], [-2, -3], [4, -4], [5, -2], [14, -2], [8, 0], [5, 4], [5, 8], [-4, 5]], [[9028, 7902], [-3, -3], [-7, -4], [-5, -5], [-4, -1], [5, -4], [10, 3], [15, 9], [7, 6], [-2, 1], [-4, 0], [-12, -2]], [[8608, 7127], [-4, -3], [-8, -12], [-7, -11], [-3, -6], [0, -7], [4, -2], [7, 0], [17, 8], [6, 4], [7, 6], [1, 7], [-2, 5], [-5, 6], [-5, 3], [-8, 2]], [[8432, 7372], [-5, 8], [-18, 8], [-13, 4], [-13, 3], [-10, 2], [-6, -1], [6, -7], [12, -3], [10, -10], [1, -6], [6, -6], [6, -3], [11, -3], [10, -1], [6, 1], [3, 1], [2, 2], [-2, 5], [-6, 6]], [[8862, 7224], [4, 3], [3, 4], [0, 8], [2, 7], [-7, 5], [-3, 3], [-12, 2], [-14, 0], [-15, -2], [-8, -2], [0, -4], [3, -6], [23, -13], [3, -2], [8, -3], [6, -6], [1, -1], [2, 2], [4, 5]], [[8633, 8191], [-5, 5], [-6, 1], [-7, 0], [-8, -3], [-10, -5], [2, -4], [20, -5], [2, 0], [5, 6], [7, 5]], [[7726, 8191], [11, 6], [3, 4], [-2, 2], [-3, 7], [-12, 2], [-10, -2], [-11, -4], [-8, -2], [-7, 1], [-5, -1], [-3, 5], [-4, 2], [-6, -1], [-10, 1], [-1, -2], [-1, -5], [-1, 0], [-15, 1], [-5, -1], [-4, -4], [-2, -2], [2, -2], [25, -3], [4, -2], [5, -5], [2, -1], [3, 2], [2, 0], [1, -4], [5, -3], [9, -3], [5, 0], [2, 1], [-1, 4], [-6, 9], [13, -3], [25, 3]], [[7801, 8192], [-16, -14], [-5, -4], [4, -1], [10, 3], [9, -1], [6, 1], [13, 6], [20, 6], [6, 4], [-1, 3], [-4, 6], [-9, 3], [-4, 0], [-5, -3], [0, -3], [-2, -3], [-4, 1], [-5, -3], [-13, -1]], [[7867, 7522], [6, 6], [6, 3], [-1, 1], [-3, 1], [-7, 0], [-17, -3], [-3, -2], [1, -2], [2, -2], [16, -2]], [[7456, 7787], [4, 1], [6, 6], [-1, 2], [-6, 0], [-8, 1], [-7, 7], [-2, -1], [1, -3], [1, -3], [5, -6], [7, -4]], [[7780, 8107], [5, 4], [6, 7], [9, 8], [2, 3], [1, 3], [1, 2], [8, 6], [11, 8], [5, 5], [-1, 1], [-3, 5], [-2, 2], [-2, 1], [-10, 1], [-11, -3], [-4, -2], [-3, -3], [0, -3], [3, -3], [-1, -1], [-8, -1], [-2, -3], [-8, -11], [-2, -2], [-17, -4], [-7, -4], [-5, -7], [-4, -4], [-3, -2], [-1, -2], [1, -4], [3, -4], [6, -2], [5, 1], [12, 4], [16, 4]], [[7923, 8165], [-9, 3], [-6, 1], [-10, -3], [-2, -2], [-1, -3], [0, -8], [-2, -4], [5, -9], [3, -4], [5, -3], [7, -1], [10, 2], [6, 3], [3, 6], [5, 5], [9, 7], [2, 3], [0, 2], [-1, 2], [-5, 2], [-19, 1]], [[8833, 7270], [18, -8], [2, 0], [4, 3], [0, 2], [-1, 3], [-5, 3], [-2, 2], [-1, 3], [-1, 1], [-2, -1], [-2, 1], [-3, 6], [-4, 2], [-4, 0], [-3, -2], [-2, -4], [1, -4], [5, -7]], [[7154, 8236], [10, -3], [10, -1], [13, 2], [3, 2], [-1, 4], [-2, 4], [-4, 3], [-6, 1], [-8, -1], [-9, 1], [-15, 4], [-5, -3], [-12, -3], [0, -2], [5, -4], [5, -3], [12, -3], [4, 2]], [[7355, 7765], [8, -2], [4, -3], [2, -3], [4, -3], [1, -4], [1, -13], [8, -2], [6, 1], [9, 3], [2, -2], [7, -2], [-8, -5], [0, -4], [1, -1], [11, -1], [1, 0], [-1, 2], [1, 2], [2, 1], [2, -1], [3, -3], [4, -2], [8, -3], [4, 1], [6, 4], [2, 3], [0, 2], [-2, 1], [-16, 2], [-10, 2], [-10, 5], [-3, 4], [4, 4], [1, 3], [0, 4], [-7, 5], [-12, 7], [-14, 6], [-21, 6], [-4, -3], [-1, -2], [1, -2], [6, -7]], [[7176, 8035], [-10, -9], [-4, -4], [-1, -4], [-4, -5], [-1, -2], [2, -4], [7, -7], [-4, -8], [-1, -4], [0, -4], [1, -4], [3, -3], [12, -7], [5, -2], [3, 0], [4, 2], [4, 4], [4, 4], [4, 8], [1, 5], [-1, 7], [-2, 10], [-1, 8], [1, 5], [-3, 5], [-9, 7], [-10, 2]], [[7295, 7766], [-2, -5], [1, -4], [2, -7], [0, -7], [4, -8], [1, -2], [11, -6], [5, -4], [3, -6], [4, -4], [4, 0], [4, 1], [4, 3], [2, 4], [0, 3], [-7, 16], [-5, 7], [-9, 10], [-2, 4], [-2, 3], [-1, 1], [-7, 2], [-10, -1]], [[6686, 7286], [-9, -2], [1, -3], [7, -6], [3, 0], [4, 3], [0, 2], [-6, 6]], [[5955, 8914], [5, 4], [7, 7], [8, 3], [21, 4], [3, 1], [-1, 2], [-16, 7], [-15, 8], [-5, 1], [-4, 12], [-4, 10], [-11, 3], [-28, 1], [-36, -6], [-34, -4], [-26, -5], [-5, -9], [13, -7], [20, 0], [14, -1], [-14, -13], [16, -4], [44, 7], [12, -4], [-15, -12], [-6, -6], [29, -1], [28, 2]], [[6133, 9027], [-16, 2], [-12, 0], [-20, -1], [-6, -1], [-15, -8], [-27, -5], [-8, -3], [1, -2], [9, -1], [20, 0], [37, 9], [37, 10]], [[5886, 8979], [13, 2], [8, 3], [9, 4], [4, 3], [-1, 2], [-11, 3], [-30, 8], [-3, 3], [-8, 4], [-8, 0], [-11, -3], [-4, 1], [4, 4], [1, 2], [-3, 3], [-6, 1], [-10, 0], [-23, -5], [-6, -2], [-2, -3], [2, -5], [6, -7], [7, -14], [4, -3], [7, -1], [61, 0]], [[5914, 9204], [-12, -7], [-1, -2], [1, -2], [1, -1], [10, -2], [9, 3], [17, 8], [-3, 4], [-5, 2], [-6, 0], [-11, -3]], [[5993, 9139], [19, 0], [11, 1], [8, 2], [15, 2], [30, 0], [-1, 4], [-2, 1], [-6, 5], [-6, 2], [-9, 2], [-25, 5], [-21, 1], [-27, -1], [-14, -1], [-2, -1], [0, -5], [1, -7], [6, -5], [23, -5]], [[6926, 9039], [-14, 0], [-21, -3], [-9, -3], [-3, -4], [1, -2], [8, -9], [12, -10], [10, -6], [8, -3], [6, -1], [4, 1], [12, -1], [3, 2], [0, 2], [-3, 1], [-3, 3], [-1, 5], [0, 3], [3, 4], [6, 5], [1, 2], [0, 2], [-2, 3], [-4, 2], [-14, 7]], [[6428, 8894], [-7, -3], [-26, -5], [-10, -3], [-3, -5], [4, -7], [5, -4], [4, -1], [10, 1], [4, 2], [4, 4], [12, 8], [9, 1], [9, -3], [6, 0], [6, 4], [8, 3], [1, 2], [-6, 4], [-8, 3], [-17, 5], [-3, -1], [-1, -2], [-1, -3]], [[6499, 8780], [-12, 0], [-7, -3], [-2, -2], [-6, -1], [2, -2], [4, -2], [26, -5], [7, -1], [3, 0], [3, 2], [-1, 2], [-5, 6], [-12, 6]], [[5084, 9166], [-14, 7], [-8, 2], [-7, 1], [-2, 3], [1, 4], [-3, 2], [-21, -5], [-9, -3], [-5, -2], [-8, -2], [-11, -1], [4, -6], [29, -18], [24, -5], [14, 0], [21, 4], [9, 2], [8, 5], [0, 2], [-6, 4], [-2, 3], [-4, 1], [-10, 2]], [[4535, 8929], [2, -2], [0, -2], [-1, -3], [-15, -8], [-2, -3], [7, -1], [6, 0], [5, 3], [4, 2], [3, 3], [2, 4], [1, 7], [0, 3], [-2, 1], [-6, -1], [-4, -3]], [[5099, 9024], [11, 4], [6, 4], [-2, 5], [-6, 3], [-9, 3], [-14, 3], [-41, -3], [-17, -3], [-15, -6], [2, -3], [5, -1], [80, -6]], [[5865, 8852], [-1, 5], [-3, 6], [-4, 3], [-5, 3], [-12, 4], [-21, 0], [-10, -2], [-13, -7], [-4, -4], [-8, -13], [-4, -4], [-2, -1], [-1, -2], [5, -3], [15, -7], [25, -4], [14, 1], [24, 5], [8, 4], [3, 3], [2, 3], [0, 3], [-2, 2], [-6, 5]], [[9793, 5602], [-2, 8], [-1, 7], [-2, 2], [-3, 0], [-3, -5], [-4, -12], [-6, -8], [-8, -8], [-5, -4], [-3, 4], [-1, 3], [0, 17], [1, 9], [4, 9], [8, 20], [4, 6], [6, 8], [1, 2], [-2, 2], [-3, 2], [-12, -9], [-15, -9], [-17, -30], [-8, -9], [-5, -2], [-8, 0], [-3, 4], [-2, 5], [-2, 7], [0, 2], [13, 14], [3, 6], [1, 5], [-1, 2], [-11, -2], [-6, 1], [9, 2], [4, 2], [0, 1], [10, 3], [3, 2], [-2, 8], [-4, 4], [6, 3], [10, 7], [6, 7], [2, 2], [6, -1], [3, 2], [4, 3], [6, 9], [1, 4], [2, 7], [-1, 7], [-1, 3], [-1, 0], [-6, -3], [-7, -7], [-4, -1], [-7, 1], [-3, -1], [-11, -10], [-7, 4], [-8, -2], [-4, -6], [-4, 0], [-7, -1], [-13, -6], [0, 1], [3, 3], [14, 7], [7, 5], [2, 6], [5, 10], [1, 2], [-7, 2], [-8, -1], [-6, 3], [0, 2], [0, 1], [6, 4], [3, 3], [0, 2], [-8, -1], [-11, -2], [-5, 0], [20, 12], [4, 4], [2, 3], [1, 5], [4, 5], [7, 5], [7, 8], [1, 6], [-1, 8], [-3, 7], [-11, 7], [-8, 5], [-7, 2], [-24, -3], [-4, 1], [-3, -1], [-3, -3], [-4, -7], [-1, 1], [0, 7], [-2, 8], [1, 7], [-2, 0], [-6, -4], [-6, -6], [-5, -6], [-5, -4], [-5, -1], [-5, -4], [-5, -5], [-3, -1], [1, 4], [-2, 1], [-2, -1], [-9, -6], [-5, -5], [-1, -3], [0, -5], [0, -2], [-6, -5], [-2, 0], [2, 6], [-1, 5], [6, 12], [3, 7], [2, 9], [-1, 9], [-1, 3], [-3, 2], [-1, -1], [0, -12], [-1, -3], [-4, -2], [-1, 1], [0, 7], [-2, 4], [1, 2], [-9, -3], [-15, -4], [-10, 10], [-18, -7], [-3, -1], [15, 15], [1, 8], [1, 3], [-3, 1], [-6, 0], [-5, -2], [-6, -5], [-1, 0], [7, 10], [25, 15], [11, 7], [4, 5], [2, 6], [-2, 1], [-11, -3], [-7, -1], [-9, 6], [-4, 1], [-11, -4], [-7, -5], [0, 2], [3, 7], [0, 5], [-2, 5], [-2, 1], [-5, -3], [-6, -9], [-4, -9], [-4, -4], [-6, -4], [-8, -9], [-12, -16], [-5, -5], [1, 7], [0, 5], [-1, 1], [-2, 5], [1, 3], [4, 5], [3, 6], [0, 3], [0, 7], [1, 5], [3, 6], [12, 17], [4, 8], [3, 9], [0, 3], [5, 5], [14, 20], [5, 7], [1, 10], [1, 3], [2, 3], [9, 6], [7, 9], [6, 14], [1, 7], [0, 5], [-1, 4], [-1, 3], [-2, 2], [-3, 0], [-6, -2], [-3, 1], [-3, 3], [-1, 4], [1, 8], [7, 2], [16, 2], [8, 1], [4, 2], [3, 6], [6, 12], [1, 3], [-4, 3], [-2, 1], [-11, -3], [-2, -2], [3, -5], [-3, -5], [-13, 4], [-3, 7], [-9, 0], [-14, -9], [-24, -11], [-8, -4], [-5, -3], [-5, -7], [-4, -16], [-2, -2], [-12, -11], [-4, -2], [0, -2], [2, -3], [0, -4], [-4, -9], [-6, -9], [-8, -5], [-3, -2], [-1, -3], [0, -3], [2, -3], [7, -4], [-5, -1], [-7, 0], [-2, -3], [-6, -9], [-2, -5], [-11, -31], [-8, -20], [-17, -38], [0, -4], [3, -8], [7, -11], [1, -2], [-5, -2], [-8, 7], [-5, 1], [-7, -5], [-5, -8], [-3, -5], [0, -10], [2, -5], [7, -4], [9, 0], [-1, -2], [-4, -4], [-4, -12], [1, -4], [4, -4], [3, -2], [-1, -1], [-3, 1], [-11, 8], [-10, 3], [-3, 1], [-3, -1], [-7, -9], [-4, -13], [-8, -17], [-3, -15], [-2, -3], [-10, 1], [-3, 2], [-2, 3], [1, 5], [6, 6], [-2, 0], [-17, -14], [-8, -8], [0, -4], [17, 0], [17, 2], [9, -1], [9, -2], [12, 1], [0, -1], [-13, -9], [-8, -3], [-8, -10], [-19, -20], [-24, -19], [-5, -7], [-2, -6], [0, -2], [3, -15], [5, -13], [3, -3], [8, -4], [13, 1], [26, 6], [8, 3], [6, 3], [7, 6], [1, -1], [-1, -6], [7, 0], [24, 0], [4, -2], [17, -4], [14, 1], [40, -7], [14, -1], [4, 4], [20, 2], [10, 5], [5, 0], [3, 2], [6, 12], [2, 2], [3, -2], [5, -1], [8, 3], [4, 3], [1, -3], [-1, -23], [-17, -8], [-3, -3], [3, 0], [17, 3], [4, -1], [3, -3], [15, -3], [6, 1], [3, 1], [2, 2], [2, 6], [1, 10], [2, 3], [13, -2], [10, 1], [3, -3], [7, -1], [4, 1], [8, 4], [-1, -3], [-6, -8], [-8, -6], [-16, -8], [-13, -23], [-3, -4], [-7, -7], [-9, -5], [-13, -3], [-5, -3], [-6, -6], [-3, -5], [0, -2], [0, -4], [6, -4], [2, -2], [5, -1], [10, 2], [10, 4], [4, 0], [6, -2], [6, 1], [5, 4], [9, 12], [3, 7], [0, 8], [18, 25], [5, 2], [4, -1], [7, 2], [6, 6], [8, 10], [1, -1], [-6, -13], [-2, -6], [6, 3], [2, 3], [2, 4], [2, 11], [13, 26], [2, 8], [2, 2], [11, -6], [4, -6], [4, -13], [3, -16], [2, -6], [2, -2], [-2, -7], [-7, -14], [-10, -21], [-5, -14], [-1, -6], [0, -4], [3, -5], [2, -2], [2, 0], [6, 2], [11, 12], [7, 8], [6, 10], [4, 5], [3, 1], [2, -1], [0, -4], [-3, -11], [3, -6], [-2, -8], [-1, -25], [2, -5], [2, -1], [2, 1], [12, 9], [4, 1], [3, 0], [3, -3], [3, -4], [3, -2], [4, 1], [4, 3], [3, 5], [5, 12], [6, 18], [0, 4], [0, 4], [-2, 7], [18, 38], [1, 5], [1, 9], [-2, 9]], [[9568, 5974], [-2, -5], [0, -2], [2, -2], [3, 0], [2, 1], [3, 3], [2, 4], [0, 2], [-4, 1], [-6, -2]], [[9628, 5825], [3, -3], [3, 2], [3, 3], [12, 6], [1, 4], [-2, 1], [-4, -1], [-9, -7], [-2, 0], [-2, 1], [0, 3], [-2, 1], [-3, -1], [-1, -2], [3, -7]], [[9593, 6116], [-5, -1], [-4, -5], [-2, -5], [1, -1], [4, 0], [5, 5], [1, 7]], [[9693, 5842], [-3, 2], [-6, 8], [-3, 0], [-5, -7], [-5, 4], [-1, -1], [-1, -6], [0, -8], [2, -1], [2, 1], [20, 8]], [[9513, 5523], [3, 10], [-1, 4], [-6, 4], [-1, -2], [0, -2], [4, -16], [0, -2], [-4, -8], [0, -3], [2, -3], [7, 5], [1, 3], [-3, 4], [-2, 6]], [[9678, 5600], [-3, -2], [-1, -3], [0, -3], [-3, -5], [-4, -7], [-1, -3], [4, 0], [4, 4], [6, 15], [1, 9], [-3, -5]], [[6131, 8112], [-3, 5], [-7, 3], [-8, 1], [-3, -1], [-3, -2], [-1, -2], [1, -3], [0, -3], [-2, -9], [0, -6], [1, -4], [1, -2], [2, 0], [4, 3], [2, 0], [1, 0], [3, -5], [4, -3], [2, 1], [6, 4], [3, 1], [3, 4], [3, 12], [-3, 2], [-6, 4]], [[6141, 8132], [-2, -2], [-4, -5], [-1, -2], [1, -1], [3, -6], [4, -3], [10, -2], [2, 7], [-1, 4], [-2, 5], [-3, 3], [-7, 2]], [[6129, 8303], [3, 1], [-12, 7], [-8, 2], [-8, 0], [-3, -3], [1, -5], [1, -4], [2, -2], [7, -3], [17, -4], [1, 1], [0, 1], [-2, 4], [1, 5]], [[6462, 8163], [2, -3], [5, -2], [3, 4], [3, 7], [1, 7], [-1, 4], [1, 3], [3, 0], [5, -3], [1, -4], [2, -8], [-1, -4], [-2, -1], [-2, -5], [3, -2], [8, 0], [7, 2], [4, 3], [3, 5], [1, 7], [1, 3], [-11, 8], [-7, 4], [-8, 2], [-7, 0], [-6, -2], [-4, -4], [-3, -7], [-2, -5], [2, -4], [-1, -5]], [[6069, 8180], [-12, 4], [-5, 1], [-3, -2], [0, -3], [2, -2], [8, -3], [1, -2], [0, -2], [-6, -2], [2, -1], [0, -2], [0, -3], [1, 0], [6, 1], [4, 2], [3, 3], [3, 2], [3, 0], [-2, 4], [-5, 5]], [[5998, 8096], [-4, -6], [-6, -4], [-9, -4], [-3, -3], [3, -2], [25, -7], [4, -2], [3, 0], [4, 6], [5, 2], [1, 8], [-1, 4], [-2, 2], [-5, 3], [-15, 3]], [[5799, 8065], [-7, 2], [-16, 1], [-7, -1], [-4, -2], [0, -3], [6, -5], [9, -4], [12, -5], [8, -1], [4, 0], [6, 3], [2, 2], [-1, 3], [-2, 3], [-10, 7]], [[5540, 7920], [2, -1], [4, 10], [0, 4], [-1, 4], [-2, 1], [-4, 4], [1, 4], [-1, 9], [-5, 5], [-6, -4], [1, -8], [-3, -5], [0, -11], [6, -5], [6, -2], [2, -5]], [[5463, 7997], [-6, -1], [-9, 1], [-1, -1], [2, -2], [5, -4], [13, -5], [2, 1], [1, 1], [-2, 5], [-5, 5]], [[5542, 7865], [4, 1], [5, 6], [2, 9], [-13, 0], [-9, 1], [2, -7], [8, -7], [1, -3]], [[5425, 8011], [-2, -6], [10, -7], [3, 0], [0, 3], [-1, 4], [-10, 6]], [[9149, 6603], [-3, 1], [-5, -3], [-3, -3], [0, -3], [0, -2], [4, -2], [12, -2], [-1, 3], [3, 5], [0, 2], [-7, 4]], [[9081, 6770], [-4, 2], [-2, -1], [0, -1], [3, -7], [14, -8], [12, -1], [-2, 13], [-6, 3], [-8, 3], [-4, 0], [-3, -3]], [[8523, 6958], [-9, 0], [-2, -3], [2, -6], [-1, -5], [-3, -8], [2, 0], [2, 2], [7, -1], [4, 8], [1, 3], [-2, 1], [-1, 2], [0, 7]], [[8884, 7109], [-3, 1], [-9, 9], [-10, -1], [-4, -1], [0, -6], [2, -5], [5, -4], [14, -6], [9, -3], [3, 8], [-7, 8]], [[7755, 8951], [-16, -9], [-13, -7], [-11, -8], [4, -5], [8, -2], [14, 0], [18, 5], [5, 0], [0, 7], [0, 7], [9, 4], [7, 10], [-6, 3], [-6, 1], [-13, -6]], [[8300, 9113], [-12, -1], [-15, -1], [-4, -2], [0, -6], [9, -3], [27, -5], [27, 1], [16, 4], [2, 4], [-9, 1], [-14, 4], [-19, 3], [-8, 1]], [[9906, 8049], [-6, -1], [-13, -3], [-2, -1], [1, -2], [-13, -11], [-13, -2], [-30, -4], [-14, 0], [-25, 5], [-3, 2], [-3, 4], [-30, -1], [-9, -1], [-7, 0], [3, 7], [10, 7], [13, 23], [11, 6], [23, 6], [23, -1], [40, -18], [12, -2], [11, 2], [27, 6], [5, 2], [9, 8], [11, 12], [0, 3], [-17, -7], [-9, -2], [-8, 0], [7, 24], [3, 17], [3, 5], [22, -1], [31, 2], [7, 4], [0, 2], [-12, 2], [-6, 5], [-11, -2], [-13, -3], [-18, 0], [2, 8], [12, 16], [2, 8], [5, 14], [0, 8], [7, 7], [20, 5], [8, 4], [1, 3], [-12, 13], [3, 4], [9, 3], [4, 2], [-3, 2], [-9, 1], [-13, -3], [-15, -1], [-13, 4], [-10, 2], [-7, 0], [-17, -8], [-6, 0], [-8, 2], [-50, 6], [-7, 3], [-18, 11], [-15, 7], [-20, 8], [-25, 6], [-32, 4], [-19, 4], [-9, 6], [-16, 12], [-12, 10], [-2, 5], [7, 6], [7, 4], [14, 4], [23, -1], [13, -2], [14, -3], [10, -1], [22, 1], [22, -2], [14, -3], [17, -5], [48, -21], [20, -8], [8, -1], [36, -8], [6, 0], [15, 4], [1, 3], [-5, 2], [-16, 2], [-18, 9], [-12, 7], [-1, 11], [1, 7], [3, 3], [2, 10], [-10, 6], [-8, 2], [-20, 8], [-1, 2], [9, 1], [9, 0], [21, -5], [10, 0], [8, 1], [1, 2], [-13, 5], [-15, 9], [-32, 1], [-21, -1], [-13, 3], [-14, 5], [-9, 2], [-18, -3], [-10, -1], [-8, 1], [-9, 16], [2, 5], [7, 2], [5, 5], [4, 5], [11, 5], [60, 12], [15, 8], [-1, 1], [-10, -1], [-13, -4], [-9, -1], [-35, 5], [-6, -1], [-14, -7], [-20, -8], [-9, 1], [-12, 4], [-1, 3], [-1, 4], [13, 6], [4, 3], [9, 7], [-1, 4], [-14, -2], [-2, 4], [0, 6], [-1, 7], [-4, 8], [-12, 11], [-5, 3], [-4, 5], [-9, 15], [3, 4], [8, 3], [1, 1], [-19, -2], [-2, -3], [3, -4], [3, -5], [2, -7], [2, -5], [7, -7], [6, -3], [9, -9], [4, -10], [-1, -5], [-7, -5], [-12, -6], [-3, -4], [-2, -5], [-9, -4], [-5, 1], [-4, 0], [5, -7], [3, -8], [-4, -8], [-10, -5], [-6, 0], [-11, -4], [-29, -2], [-10, 1], [-18, 4], [-21, 2], [-9, 6], [-11, 9], [-6, 9], [0, 7], [3, 6], [6, 3], [8, 23], [10, 18], [27, 18], [7, 7], [3, 4], [0, 2], [-5, 2], [-32, -23], [-20, -2], [-6, 5], [2, 9], [3, 2], [16, -1], [7, 5], [-10, 8], [-11, 2], [-3, 2], [11, 6], [26, 0], [6, 4], [9, 4], [11, 8], [3, 7], [1, 6], [-2, 5], [0, 5], [2, 4], [-3, 6], [-7, 5], [-16, 6], [-5, -6], [-5, -3], [-7, 0], [-6, 2], [-7, 1], [-7, 3], [-6, 0], [-3, 2], [-2, 6], [0, 7], [8, 3], [11, 2], [7, 5], [5, 7], [0, 8], [-3, 8], [-9, 8], [-16, -8], [-6, -1], [-2, 5], [-2, 4], [-7, 5], [-9, 4], [-8, 2], [-1, 4], [2, 4], [4, 5], [5, 11], [5, -1], [5, 1], [-3, 8], [-4, 8], [-5, 4], [0, 2], [-2, 2], [-4, 5], [-4, 4], [-8, 10], [-5, 4], [-8, 2], [-7, 0], [-13, -2], [-23, -4], [-18, -1], [-3, 1], [9, 4], [13, 4], [18, 3], [5, 7], [-2, 6], [1, 5], [-5, 7], [5, 3], [16, 3], [8, 1], [7, 4], [-21, 10], [-21, 7], [-5, 3], [-5, 4], [-4, 7], [-7, 6], [-9, 6], [-13, 6], [-35, 11], [-12, 8], [-11, 12], [-5, 5], [-6, 3], [-24, 9], [-3, 4], [25, 10], [2, 4], [-10, 13], [-10, 9], [-11, 3], [-17, 2], [-16, 4], [-14, 6], [-13, 5], [-21, 4], [-35, 11], [-54, 13], [-25, 7], [-14, 2], [-19, 1], [-36, 7], [-31, 2], [-19, -1], [-6, 1], [-14, 7], [-22, 4], [-12, -2], [-14, -7], [-17, -7], [-9, -1], [-13, 6], [-7, 5], [-6, 2], [-7, -2], [-12, -6], [-11, -4], [-17, -5], [-13, -3], [-18, 0], [-4, -2], [-7, 0], [-9, 1], [-8, 4], [-8, 5], [-7, 2], [-6, 1], [-14, -3], [-17, -8], [-8, -1], [-7, 0], [-8, 3], [-17, 6], [-9, -1], [-7, -2], [2, -5], [16, -12], [13, -8], [-11, -1], [-102, 11], [-13, 3], [-19, 7], [-15, 4], [-27, 11], [-20, 6], [-7, 5], [-2, 3], [7, 5], [41, 14], [16, 3], [32, 4], [7, 2], [3, 2], [-9, 3], [-42, -1], [-37, 2], [-32, 6], [-6, 2], [-5, 3], [-6, 6], [2, 5], [7, 7], [5, 4], [1, 3], [-41, -16], [-16, -6], [-13, 2], [-10, 3], [-4, 3], [0, 3], [1, 2], [3, 2], [-21, 6], [-10, 5], [-1, 6], [8, 5], [7, 4], [7, 3], [20, 2], [73, 4], [52, -4], [18, 13], [12, 4], [35, 5], [54, 0], [38, -2], [18, -3], [24, -7], [2, 2], [-6, 6], [0, 5], [9, 9], [5, 6], [-3, 5], [-11, 6], [-19, 8], [-10, 1], [-11, -2], [-14, -4], [-28, -11], [-13, -2], [-23, -1], [-12, 1], [-12, 2], [-19, 6], [-6, 1], [-8, -3], [-10, -6], [-9, -4], [-9, -3], [-9, -1], [-11, 1], [-49, 9], [-10, 5], [-1, 6], [-15, 6], [-16, 1], [-3, 2], [22, 10], [15, 4], [-2, 1], [-23, 0], [-15, -5], [-9, -1], [-21, 0], [-21, 3], [-9, 2], [-10, 5], [-10, 3], [-32, 4], [-7, 2], [-7, 4], [-26, 12], [-16, 8], [-2, 4], [18, 10], [1, 3], [-8, 4], [-3, 3], [3, 5], [16, 9], [5, 3], [29, 6], [29, 8], [10, 2], [9, 0], [38, 0], [12, 2], [10, 4], [16, 4], [34, 6], [75, 9], [5, 2], [0, 1], [-6, 5], [-2, 2], [16, 5], [35, 7], [23, 3], [16, 0], [12, 2], [17, 4], [10, 1], [59, 1], [27, -2], [13, 0], [8, 2], [11, 5], [20, 12], [11, 7], [10, 12], [13, 17], [10, 18], [7, 17], [5, 10], [3, 4], [13, 5], [12, 3], [22, 4], [-2, 1], [-9, 3], [-9, 1], [-8, -1], [-14, -4], [-19, -2], [-19, 0], [-13, -1], [-12, -4], [-20, -2], [-13, 0], [-24, 5], [-12, 1], [-31, 0], [-8, 2], [-8, 3], [-6, 5], [-5, 7], [1, 7], [11, 12], [4, 3], [29, 14], [19, 7], [18, 4], [13, 3], [13, 1], [12, 3], [22, 10], [22, 8], [29, 15], [14, 4], [48, 7], [13, 0], [11, -1], [10, -4], [30, -15], [2, 0], [-5, 6], [-11, 17], [2, 7], [17, 8], [7, 1], [17, 0], [29, -3], [19, -2], [15, -4], [17, -3], [9, 0], [6, 2], [10, 7], [11, 11], [5, 13], [-2, 16], [-4, 12], [-4, 7], [2, 6], [15, 8], [13, 5], [32, 8], [25, 2], [15, -1], [20, -3], [28, -3], [25, -6], [41, -16], [27, -8], [22, -4], [22, -6], [32, -13], [17, -6], [10, -2], [9, 0], [-3, 4], [-16, 8], [-23, 8], [-53, 15], [-29, 11], [-26, 12], [-19, 8], [-35, 7], [1, 3], [41, 10], [77, 9], [87, 7], [29, -1], [51, 3], [5, 4], [11, 2], [48, 6], [13, 0], [21, -3], [22, -5], [11, -5], [14, -9], [6, -12], [-1, -38], [1, -7], [3, -3], [9, 4], [12, 8], [10, 6], [8, 8], [6, 11], [3, 9], [-14, 9], [0, 16], [7, 8], [19, 0], [78, -29], [31, -6], [35, -15], [41, 1], [28, -1]], [[9999, 9650], [-30, 10], [-25, 14], [-17, 17], [-4, 9], [13, 2], [60, 0], [3, -1]], [[9999, 7334], [-5, 2], [-9, 6], [-8, 6], [-3, 1], [3, 7], [2, 5], [0, 12], [-3, 5], [7, 11], [10, 11], [6, 4]], [[9999, 7411], [-7, -3], [-19, -17], [-4, -1], [-1, 3], [-8, 12], [-5, 3], [-2, 3], [-11, 6], [-5, 5], [-6, 9], [-10, 10], [-13, 21], [-22, 25], [-5, 13], [6, 17], [-7, 12], [21, 6], [29, 6], [15, 5], [9, 2], [19, 1], [6, 5], [-10, -1], [-7, 0], [-1, 3], [3, 4], [1, 5], [-3, -1], [-18, -9], [-27, -8], [-22, -4], [-3, 0], [-7, -2], [-5, -1], [-3, 1], [-7, 7], [-2, 5], [13, 13], [10, 17], [13, 12], [10, 1], [15, -1], [6, -1], [-2, 7], [1, 3], [12, 4], [15, 2], [11, -1], [6, -8], [9, -15], [11, -5], [0, 7], [-6, 9], [-2, 14], [-8, 6], [-6, 3], [-17, -1], [-10, 10], [-2, 4], [0, 5], [-10, 14], [-4, 8], [-5, 9], [-3, 1], [4, -12], [4, -9], [7, -19], [3, -8], [-4, -6], [-9, -6], [-7, -3], [-18, -4], [4, 9], [2, 9], [-9, -3], [-8, -7], [-3, -9], [-5, -8], [-16, -21], [-6, -11], [-6, -6], [-7, -2], [-6, 5], [-5, 10], [-2, 9], [0, 21], [0, 10], [-2, 13], [-9, 31], [-2, 12], [-14, 5], [0, 2], [-4, 7], [-3, 6], [2, 2], [4, 2], [21, 9], [16, 11], [20, 16], [7, 5], [29, 4], [12, 1], [0, 3], [-4, 1], [-19, -1], [-26, -7], [-4, -2], [-11, -10], [-9, -6], [-24, -12], [-15, 0], [-16, 15], [-18, -3], [-13, 1], [-3, 2], [-3, 21], [10, 24], [-10, 0], [-3, 1], [-5, 6], [-4, 2], [3, 3], [26, 11], [39, 23], [17, 8], [10, 4], [8, 5], [9, 9], [4, 4], [5, 4], [11, 3], [12, 6], [20, 13], [3, 5], [-5, 1], [-9, -5], [-19, -11], [-13, -7], [-46, -30], [-20, -10], [-10, -7], [-9, -7], [-8, -4], [-10, -3], [-19, -2], [-10, -3], [-6, 2], [-3, 14], [2, 8], [-1, 9], [5, 11], [8, 9], [3, 4], [1, 3], [15, 9], [8, 4], [6, 8], [34, 3], [8, 0], [5, 1], [3, 3], [-3, 2], [-10, 1], [-27, 0], [-24, 1], [-11, 2], [-6, -1], [-9, 2], [-10, 5], [-15, 18], [6, 22], [1, 11], [19, 9], [10, 3], [15, 7], [20, 12], [23, 8], [12, 1], [10, -1], [36, -10], [19, -2], [16, 2], [21, -4], [37, -15], [7, 3], [-2, 3], [-41, 16], [0, 5], [11, 1], [11, 5], [-7, 2], [-27, -2], [-7, -4], [-27, -2], [-14, 4], [-12, 1], [-18, 7], [-16, -2], [-10, -3], [-17, -2], [-6, -2], [-34, -22], [-14, -4], [-10, 1], [7, 14], [2, 6], [0, 6], [3, 8], [17, 16], [11, 17], [5, 11], [8, 1], [12, -2], [35, -7], [29, -8], [21, -2], [14, 0], [6, 2], [5, 5], [2, 3], [1, 7], [2, 2], [5, 3], [9, 9], [3, 8], [-3, 4]], [[7233, 1933], [1, -3], [6, 6], [2, 4], [1, 2], [-4, 1], [-3, -4], [-3, -6]], [[798, 6660], [-10, 2]], [[788, 6662], [-1, -3], [10, -7]], [[797, 6652], [4, -1]], [[801, 6651], [9, 6]], [[810, 6657], [-1, 3]], [[809, 6660], [-5, 1]], [[804, 6661], [-6, -1]], [[8241, 2364], [-4, -1], [-4, -3], [5, -4], [1, 0], [3, 1], [8, -4], [8, -2], [1, 3], [-3, 3], [-10, 7], [-5, 0]], [[204, 3319], [-2, 1], [-1, -2], [0, -1], [1, 0], [1, 1], [1, 1]], [[7554, 2887], [4, 1]], [[7558, 2888], [0, 3]], [[7558, 2891], [-2, 0]], [[7556, 2891], [-4, -2]], [[7552, 2889], [0, -2], [2, 0]], [[7174, 2501], [-1, 2], [-1, 0], [1, -5], [2, -5], [1, 0], [-1, 5], [-1, 3]], [[1090, 7803], [-16, -6]], [[1074, 7797], [-8, -5]], [[1066, 7792], [-1, -2]], [[1065, 7790], [3, 1]], [[1068, 7791], [13, 6]], [[1081, 7797], [9, 4]], [[1090, 7801], [0, 2]], [[7190, 3886], [0, -2], [5, -21], [1, -3]], [[7215, 3951], [-1, -3], [-9, -14]], [[7310, 3638], [6, -20], [0, -4]], [[7302, 3721], [-5, 17], [0, 12], [5, 20]], [[7398, 4042], [-20, -23]], [[5047, 4063], [1, -6], [5, -6], [11, -10], [3, -4], [0, -4], [-3, -4]], [[5031, 4159], [-4, 14], [-2, 8], [2, 10]], [[5019, 4236], [2, 9], [4, 4], [6, 3], [7, 1], [7, -1], [6, -3], [4, -7], [4, -3], [3, -1], [5, 6], [5, 11]], [[5072, 4255], [4, 3], [4, -4], [15, -18], [9, -21]], [[5356, 4458], [-3, -2], [-1, -6], [-1, -7], [-3, -8], [-5, -9], [-9, -12], [-3, -5], [-1, -3], [-1, -6], [-5, -8], [-10, -21], [-8, -4], [-22, -8], [-10, -6], [-3, -8]], [[5706, 4727], [-4, -2]], [[4763, 5725], [-8, -18], [-2, -13], [3, -20]], [[4467, 4997], [1, -7], [8, -20]], [[5299, 5265], [-1, 1], [-5, 1], [-4, 3], [-5, 7], [-1, 1]], [[6590, 5577], [9, 6], [5, 0], [3, -3]], [[6576, 5583], [-10, -2]], [[6202, 5175], [3, -12], [-2, -9], [-6, -10], [-1, -6], [4, -3], [3, -6], [3, -10], [9, -12], [15, -13], [8, -9], [1, -4], [7, -5], [18, -11]], [[5267, 5454], [-1, 12], [-4, 9], [-8, 14]], [[6093, 5495], [12, -12], [5, -11], [0, -15], [-1, -12], [-3, -11], [4, -18], [13, -26], [2, -13], [0, -4], [-3, -4], [-2, -3], [0, -6], [0, -3], [2, -4], [5, -7], [3, -15], [0, -22], [-1, -9], [-3, -2], [-2, 0], [-2, -5], [0, -9], [-3, -8], [-5, -6], [-6, -2], [-7, 2], [-1, -1], [0, -2], [3, -5], [1, -6], [1, -7], [1, -5], [4, -3], [0, -3], [0, -3], [2, -3], [9, -3], [2, -2]], [[6046, 5588], [-3, 6], [-9, 0], [-17, -2], [-16, 1], [-14, 6], [-8, 9], [-3, 10], [-3, 5], [-5, -2], [-6, 1], [-8, 4], [-2, 6], [3, 10], [-3, 9], [-8, 7], [-12, 5], [-14, 1], [-9, -1], [-4, -6], [-6, -4], [-8, -2], [-7, 2], [-4, 7], [-6, 0], [-11, -10]], [[5661, 5649], [-9, -16], [-6, -7], [-22, -14], [-12, -2], [-17, 2], [-10, -2], [-2, -4], [-5, -1], [-7, 1], [-9, -6], [-10, -11], [-5, -1], [-4, 9], [-7, 2], [-13, 1]], [[4454, 4798], [-21, -3]], [[6663, 5719], [9, 1]], [[6196, 3181], [0, -1]], [[6196, 3180], [5, -8]], [[6211, 3153], [1, -5]], [[6212, 3148], [1, 0]], [[5592, 3871], [1, 24]], [[5601, 3902], [1, 22]], [[4433, 4795], [-3, -5]], [[4638, 4353], [8, 7]], [[7492, 4002], [19, -21], [7, -11], [4, -10]], [[5129, 5018], [-12, 2]], [[5164, 5036], [17, 25]], [[5303, 5083], [-3, 3], [-7, 12]], [[7161, 4067], [-26, 21], [-13, 8], [-6, 1], [-7, -1], [-10, -5]], [[7078, 4228], [-2, 2], [1, 6], [3, 11], [-1, 13], [-5, 17], [-2, 15], [0, 12]], [[7072, 4304], [-5, 18], [-13, 33]], [[7448, 4070], [9, -10], [6, -4], [6, -2], [4, -3], [2, -8]], [[6549, 5795], [1, -17]], [[6550, 5778], [2, -9]], [[7196, 3860], [13, -22], [2, -12], [-5, -7], [-1, -5], [-1, -2], [-3, 2], [-4, -2], [-4, -4], [-5, -1], [-4, 4], [-4, 0], [-3, -3], [-2, 0], [-3, 2], [-2, -2], [-1, -2], [-1, 0], [-1, 1], [-2, 6], [-1, 1], [-1, 0], [-3, -5], [-3, -1], [-3, 3], [-3, 1], [-2, -2], [0, -2], [2, -3], [-2, -3], [-5, -3], [-2, -4], [3, -6], [-2, -4], [-6, -1], [-3, -3], [0, -6], [-3, -1], [-5, 5], [-4, -2], [-4, -8], [0, -7], [4, -4], [-1, -4], [-5, -1], [-1, -4], [4, -6], [-1, -6], [-5, -6], [-2, -7], [2, -7], [-1, -5], [-4, -2], [-1, -5], [1, -5], [-3, -5], [-7, -3], [-6, -8], [-4, -12], [-4, -7], [-2, 0], [-1, -2], [-1, -2], [0, -24], [-2, -10], [-2, -4], [-1, -5], [0, -7]], [[7205, 3934], [-3, -3], [-1, -4], [0, -7], [-2, -6], [-4, -5], [-2, -5], [1, -3], [-1, -6], [-3, -6], [0, -3]], [[7293, 4030], [-25, -6], [-12, 0], [-3, -1], [-2, -1], [0, -2], [2, -2], [0, -3], [-2, -2], [-3, -1], [-4, 1], [-1, -2], [-1, -2], [-1, -3], [-2, 0], [-1, 0], [0, -3], [-2, -2], [-2, 0], [-1, -2], [0, -3], [-2, -3], [-3, -2], [-2, -5], [0, -9], [-2, -6], [-6, -5], [-3, -6], [0, -6], [0, -3]], [[7856, 4921], [-11, -6], [-6, -1], [-9, 2], [-10, 10], [-3, 8], [1, 8], [-5, 6], [-12, 2], [-7, 4], [-4, 4], [-6, -2], [-7, -8], [-5, -9], [-2, -10], [-6, -6], [-11, -2], [-8, -5], [-6, -9], [-3, -8], [0, -9], [-2, -7], [-5, -3], [-7, -1], [-7, -3], [-7, -5], [-2, -4], [2, -3], [-1, -2], [-3, -4], [2, -4], [5, -5], [5, -8], [4, -10], [2, -7], [-1, -2], [2, -3], [5, -3], [1, -4], [-2, -10], [-12, -22], [-10, -13], [-9, -8], [-4, -1]], [[6840, 3976], [-4, 3], [-2, 5], [-1, 6], [-4, 4], [-6, 1], [-2, 2], [-1, 2], [-1, 1], [-3, 0], [-3, 3], [-1, 4], [-4, 3], [-6, 1], [-9, 7], [-12, 11], [-6, 2], [-2, 0], [-2, 1], [-4, 5], [-7, 15], [-3, 14], [0, 13], [-6, 10], [-11, 7], [-6, 7], [-2, 6], [-2, 5], [-4, 2], [-2, 5], [1, 8], [-2, 2], [-9, -3], [-9, 8], [-8, 3], [-9, 0], [-11, 7], [-13, 14], [-12, 6], [-13, -2], [-9, 3], [-5, 7], [-7, 1], [-9, -3], [-5, -4], [0, -4], [-2, -3], [-3, -1], [-5, 3], [-7, 5], [-5, -2], [-4, -9], [-4, -3], [-3, 2], [-3, -1], [-2, -3], [-3, 0], [-2, 4], [-7, 5], [-13, 6], [-9, 8], [-5, 9], [-4, 4], [-3, 0], [-1, 4], [1, 7], [-2, 6], [-3, 4], [-7, 2], [-12, -1], [-7, 3], [-1, 7], [-6, 6], [-11, 5], [-7, 7], [-3, 10], [-7, 5], [-8, 0], [-5, 2], [-1, 5], [-3, 5], [-8, 6], [-5, 2], [-3, -1], [-2, 2], [0, 6], [-3, 4], [-6, 2], [-4, 4], [-2, 8], [-2, 1], [-3, -2], [-5, -4], [-4, -3], [-3, 0], [-2, 2], [-1, 3], [2, 4], [2, 3], [1, 1], [-2, 1], [-3, 1], [-3, 3], [-1, 5], [4, 5], [11, 4], [2, 3], [-1, 2], [-3, 2], [-2, 8], [-2, 13], [-4, 9], [-7, 5], [-4, 11], [-3, 17], [-5, 19], [-8, 23], [-27, 27], [-46, 33], [-27, 17], [-7, 0], [-10, -5], [-12, -10], [-16, -19], [-19, -30], [-18, -13], [-22, 6], [-60, 22], [-27, 5], [-14, -6], [-25, 3], [-36, 14], [-36, 6], [-35, -1], [-23, -3], [-11, -5], [-10, 1], [-8, 6], [-11, 4], [-18, 5], [-13, 7], [-13, 3], [-18, 0], [-14, 4], [-9, 9], [-11, 6], [-15, 4], [-15, -1], [-14, -6], [-13, 3], [-14, 12], [-6, 10], [0, 8], [-4, 14], [-11, 33], [-8, 17]], [[6115, 3871], [4, 6], [10, 4], [15, 5], [9, 7], [3, 8], [0, 10], [-2, 11], [8, 11], [16, 12], [14, 5], [12, 1], [7, -4], [2, -7], [3, -3], [4, 0], [4, -4], [2, -6], [2, -3], [2, 2], [2, -2], [2, -6], [5, -7], [14, -13], [-1, -6], [2, -5], [3, -6], [3, -1], [3, 6], [4, 0], [7, -5], [2, -5], [-3, -4], [1, -4], [2, -4], [5, -1], [6, 2], [5, -2], [2, -6], [0, -4], [-2, -2], [1, -2], [2, -2], [14, -6], [10, -9], [4, -11], [6, -8], [6, -3], [1, -4], [-4, -2], [-2, -4], [2, -5], [3, -6], [7, -5], [4, -1], [2, 1], [0, -1], [0, -1], [0, -7], [4, -10], [8, -13], [4, -10], [0, -7], [5, -10], [10, -15], [7, -14], [5, -21], [20, -53], [11, -21], [9, -7], [5, -8], [3, -9], [3, -4], [5, 1], [4, -2], [3, -5], [-1, -8], [-4, -11], [0, -8], [3, -6], [3, -13], [1, -20], [3, -11], [3, -2], [6, -2], [12, -4], [7, -5], [5, -6], [2, -10], [0, -15], [-2, -10], [-2, -8], [4, -8], [10, -8], [5, -7], [-1, -6]], [[7316, 3614], [-1, -6], [-5, -11], [-9, -36], [-3, -12], [0, -11], [6, -10], [2, -10], [-2, -10], [1, -6], [4, -3]], [[7302, 3721], [1, -7], [-3, -33], [2, -20]], [[7302, 3661], [8, -23]], [[7378, 4019], [-2, -9], [-4, -5], [-6, -6], [-5, -2], [-3, 2], [-7, -8], [-11, -19], [-12, -17], [-15, -15], [-9, -11], [-3, -9], [-1, -8], [1, -8], [-1, -9], [-5, -10], [-2, -10], [2, -16]], [[7295, 3859], [11, -33], [3, -14], [-1, -5], [1, -5], [4, -4], [-1, -5], [-4, -5], [-4, -6], [-2, -12]], [[7403, 4084], [5, -2], [2, -5], [2, -8], [-3, -10], [-11, -17]], [[7452, 4126], [-1, -9]], [[7451, 4117], [-3, -8], [-12, -15], [-3, -11]], [[5021, 3850], [0, -2]], [[5064, 4029], [-13, -13], [-7, -10], [-7, -18], [1, -14], [0, -11], [-2, -10], [-2, -8], [-4, -8], [-4, -4], [-4, -2], [-1, -10], [1, -19], [5, -12], [7, -4], [5, -6], [1, -8], [0, -9], [-3, -8], [-5, -4]], [[5032, 3851], [-11, -1]], [[5031, 4159], [-2, -42]], [[5029, 4117], [0, -11]], [[5029, 4106], [5, -13], [12, -24], [1, -6]], [[5019, 4236], [1, -3], [4, -8], [0, -12], [0, -10], [2, -5], [1, -7]], [[5163, 4268], [-26, -19], [-6, -9], [-3, -6], [0, -18], [-2, -9], [-5, 0], [-12, 4], [-5, 4]], [[5271, 4345], [-9, -10], [-7, -12], [-8, -18], [-5, -13], [-2, -8], [1, -11], [3, -14], [-1, -9], [-4, -6], [-8, -1], [-12, 4], [-8, 6], [-6, 8], [-4, 3], [-3, -3], [-3, 1], [-3, 3], [0, 4], [2, 5], [0, 4], [-1, 3], [-4, 1], [-5, 0], [-7, -3], [-15, -11]], [[5702, 4725], [-9, -5], [-11, -4], [-12, 0], [-9, -3], [-6, -5], [-9, -12], [-3, -3], [-3, -1], [-4, 4], [-5, 1], [-7, -3], [-7, -9], [-8, -15], [-4, -6], [-8, -5], [-24, -3], [-15, 0], [-8, -3], [-8, -5], [-12, -12], [-11, -11], [-5, -16], [-9, -10], [-10, -1], [-19, 11], [-5, 2], [-6, 0], [-8, -7], [-11, -12], [-5, -12], [-4, -14], [-4, -9], [-5, -4], [-5, -6], [-5, -8], [-3, -4], [-1, 1], [-2, -2], [-1, -7], [-2, -4], [-2, 0], [-2, -4], [-1, -8], [-3, -8], [-9, -13], [-18, -24], [-9, -8], [-9, 0]], [[5704, 4753], [2, -22], [0, -4]], [[4450, 5363], [-4, 0], [-28, 4], [-7, 0], [-6, 6]], [[4405, 5373], [-1, 9], [-8, 27], [-7, 16], [-8, 5], [-6, 2], [-8, -3]], [[4756, 5674], [-5, -17], [-4, -6], [-5, -1], [-2, -4], [2, -8], [0, -6], [-2, -3], [-5, 1], [-7, 5], [-10, 3], [-15, 0], [-10, 6], [-5, 12], [-14, 2], [-21, -7], [-14, -1], [-4, 5], [-5, 1], [-5, -2], [-1, -4], [1, -6], [0, -6], [-2, -4], [-6, -9], [-5, -4], [-8, -2], [-6, -9], [-3, -13], [-1, -11], [2, -7], [4, -5], [10, -6], [0, -7], [1, -3], [3, -3], [1, -5], [-1, -7], [2, -16], [4, -24], [7, -12], [12, -1], [8, 1], [6, 4], [7, -4], [8, -13], [4, -13], [0, -14], [4, -8], [8, -4], [1, 0], [2, -2], [2, -1]], [[4689, 5436], [5, -9], [2, -3], [1, -2], [1, -1], [-1, -2], [-2, -3], [-2, -3], [-5, -4]], [[4688, 5409], [-10, -4], [-19, -2], [-39, -11], [-17, -9], [-15, -4], [-28, -4], [-25, -6], [-11, 0], [-5, 5], [-12, 3], [-20, 1], [-17, -3], [-15, -11], [-5, -1]], [[4792, 5763], [-1, -2], [-15, -14], [-9, -10], [-4, -12]], [[2795, 7349], [-1, 10], [-3, 6], [-5, 4], [-10, 4], [-23, 3], [-19, -5], [-21, -11], [-30, -19], [-17, -14], [-5, -8], [-1, -7], [3, -10], [16, -18], [18, -13], [24, -13], [13, -10], [2, -10], [-4, -9], [-8, -5], [-11, -5], [-8, -7], [-4, -8], [1, -10], [4, -13], [8, -11], [11, -11], [3, -3], [1, -2], [-1, -3], [-6, -9], [-2, -3], [-4, -2]], [[6025, 3945], [19, -16], [7, -16], [6, -7], [5, -5], [6, -3], [9, 0], [12, -4], [10, -7], [16, -16]], [[5288, 3894], [-16, -7], [-15, 1], [-19, 9], [-17, 11], [-15, 14], [-15, 4], [-14, -8], [-6, -8], [4, -10], [1, -8], [-1, -6], [-7, -1], [-13, 5], [-8, 0], [-3, -4], [-6, -4], [-9, -2], [-16, -9], [-23, -15], [-14, -6], [-5, 1], [-5, -1], [-4, -4], [-6, 2], [-6, 6], [-7, 2], [-11, -5]], [[5522, 3950], [7, -19], [-10, -28], [-8, -10], [-9, -2], [-7, -10], [-5, -19], [-7, -13], [-9, -7], [-9, 0], [-9, 6], [-7, 9], [-4, 11], [-4, 7], [-5, 3], [-6, -3], [-7, -7], [-7, -3], [-8, -1], [-12, 10], [-15, 20], [-14, 10], [-14, 1], [-12, -5], [-10, -11], [-10, -3], [-7, 7], [-10, 3], [-16, -1]], [[7656, 4262], [0, -3], [-1, -13], [1, -10], [5, -8], [2, -6], [-1, -4], [-2, -2], [-3, 0], [-1, -3], [0, -5], [3, -3], [5, -3], [7, -8], [11, -22], [4, -8], [1, -10], [0, -16], [2, -8], [5, 0], [4, -1], [2, -5], [0, -10], [-2, -15], [2, -13], [6, -10], [2, -8], [-1, -7], [1, -2], [2, 0], [3, -5], [3, -12], [4, -7], [3, -1], [3, -11], [6, -31], [11, -18], [7, -8], [6, -5], [4, -5], [0, -5], [-1, -7], [-5, -11], [-3, -3]], [[7582, 4354], [-21, -9], [-14, -10], [-20, -10], [-35, -18], [-5, -11], [-21, -14], [-7, -4], [-13, -5], [-15, -8], [-5, -7], [-23, -14], [-11, -12], [-6, -7], [0, -5]], [[8147, 5188], [1, -10], [-1, -4], [-3, -1], [-3, -4], [-2, -6], [0, -3], [3, -4], [13, -11], [7, -9], [3, -10], [1, -8], [-2, -7], [-1, -8], [2, -9], [5, -3], [8, 2], [4, -7], [0, -28], [-8, -33], [-3, -20], [0, -18], [-4, -15], [-6, -13], [-4, -16], [0, -30], [-3, -22], [0, -14], [2, -15], [-1, -6], [0, -4]], [[4324, 4856], [-4, 3], [-4, 7], [-4, 12], [-4, 7], [-6, 1], [-2, 1]], [[4476, 4970], [2, -4], [-2, -4], [-3, -5], [-4, -1], [-5, 2], [-7, -3], [-8, -10], [-8, -5], [-7, -1], [-7, -4], [-7, -8], [-8, -2], [-8, 3], [-5, 0], [-6, -3], [-9, -5], [-8, 1], [-9, 4], [-9, -7], [-9, -17], [-4, -15], [0, -15], [-2, -11], [-6, -8], [-5, -2], [-8, 6]], [[2244, 8004], [-5, -22], [-8, -13], [-3, -8], [-1, -8], [4, -7], [9, -7], [7, -7], [5, -8], [0, -10], [-5, -10], [-19, -12], [-32, -13], [-13, -7], [-4, -7], [-3, -14], [-5, -10], [-9, -5], [-11, -4], [-14, -3], [-14, 0], [-13, 2], [-13, -2], [-14, -7], [-12, -4], [-11, 0], [-6, -3], [-3, -5], [-6, 2], [-14, -3], [-24, -9], [-18, -13], [-10, -18], [-14, -14], [-17, -11], [-13, -4], [-11, 1], [-5, 3], [-3, 3], [-10, 3], [-4, 3], [-3, 3], [-4, -1], [-2, 1], [0, 4], [-4, 3], [-6, 1], [-10, -3], [-9, 1], [-8, 3], [-11, 2], [-14, -2], [-2, -3], [0, -2], [-2, -2], [-6, -2], [-2, -3], [1, -3], [-1, -6], [-10, -9], [-2, -3], [1, -4], [1, -2], [-1, -3], [2, -3], [3, -1], [3, 0], [2, -5], [1, -2], [1, -3], [-1, -2], [-2, -1], [-1, -2], [0, -3], [-3, -1], [-5, 0], [-6, 0], [-5, 2], [1, 2], [-4, 3], [-7, 4], [-7, 2], [-6, 0], [-7, -3], [-8, -6], [-12, -2], [-16, 2], [-9, 0], [-2, -2], [-4, -7], [1, -2], [3, -1], [1, -3], [0, -5], [-1, -3], [1, -4], [-2, -5], [-1, -6], [1, -10], [4, -3], [7, 3], [6, -1], [4, -4], [-1, -1], [-8, -1], [-4, -2], [1, -5], [-4, -5], [-12, -6], [-2, -2]], [[1944, 7420], [-9, -7], [-6, -1], [-7, 1], [-9, -1], [-11, -5], [-7, 0], [-4, 4], [-6, 0], [-8, -3], [-2, -3], [5, -2], [-4, -5], [-10, -9], [-6, -5], [-1, -4], [1, -2], [2, -1], [1, -4], [1, -6], [-1, -5], [-3, -3], [-16, -11], [-9, -10], [-7, -13], [-2, -12], [4, -12], [0, -7], [-4, -2], [-3, -4], [-2, -7], [-15, -9], [-27, -12], [-29, 1], [-30, 15], [-19, 5], [-9, -5], [-6, 0], [-3, 3], [-5, -7], [-7, -15], [-9, -9], [-12, -5], [-13, -1], [-13, 3], [-23, 1], [-47, -3], [-32, -3], [-16, -5], [-9, -7], [-5, -7], [-1, -6], [-3, -3], [-5, -1], [-3, -2], [-1, -5], [-6, -6], [-10, -8], [-7, -7], [-2, -8], [-6, -7], [-9, -4], [-7, -2], [-6, 2], [-2, -1], [1, -4], [-1, -4], [-2, -2], [-3, 1], [-6, 3], [-2, 0], [-1, -5], [-2, -2], [-4, 0], [-1, -2], [1, -3], [-2, -2], [-8, -2], [-5, -2]], [[5327, 5261], [-9, -6], [-6, -2], [-5, 1], [-4, 3], [-3, 7], [-1, 1]], [[5283, 5278], [-1, 0], [-6, -3], [-6, 1], [-5, 5], [-6, 11], [-5, 18], [-2, 15], [1, 10], [5, 12], [7, 14], [4, 17], [2, 22], [3, 14], [3, 7], [-1, 11], [-9, 22]], [[6970, 3409], [10, 21], [18, 5]], [[6979, 3430], [-2, 2]], [[6977, 3432], [8, 19]], [[6977, 3432], [-11, 14], [-7, 6], [-7, 5], [-4, 3], [0, 4], [-4, 5], [-3, 2], [-9, 12], [-8, 9], [-2, 8], [-1, 7], [2, 6], [3, 1], [2, 1], [0, 1], [0, 2], [0, 1], [-9, 2], [-16, 1], [-11, 4], [-5, 7], [-9, 2], [-13, -3], [-8, 2], [-2, 4], [-2, 3], [-3, 3], [-4, 0], [-2, 2], [-1, 4], [-3, 3], [-3, 1], [-2, 2], [1, 2], [-2, 3], [-2, 2], [-1, 2], [0, 3], [-1, 2], [-2, 2], [-1, 4], [2, 9], [-1, 3], [-2, 4], [0, 4], [-2, 5], [-5, 11], [-3, 5], [-3, 1], [-8, 0], [-2, 3], [0, 4], [2, 4], [0, 3], [-1, 4], [-6, 3], [-2, 2], [-1, 2], [0, 3], [0, 4]], [[6795, 3650], [3, 4], [2, 6], [-3, 12], [1, 7], [4, 2], [3, 6], [0, 12], [2, 9], [3, 4], [4, 10], [3, 16], [6, 13], [10, 10], [5, 9], [1, 7], [1, 3], [2, 1], [3, 5], [4, 7], [1, 4], [0, 3], [0, 3], [-4, 6], [-5, 5], [-4, 8], [1, 6], [-1, 5], [-3, 5], [1, 4], [3, 4], [0, 4], [-3, 3], [0, 6], [3, 9], [0, 7], [-4, 3]], [[6834, 3878], [0, 9]], [[6834, 3887], [3, 21], [-1, 2], [-3, 7], [0, 7], [2, 10], [-1, 7], [-3, 2], [-1, 3], [-1, 5], [3, 4], [0, 3], [-2, 3], [1, 5], [7, 6], [2, 4]], [[6840, 3976], [1, 3], [2, 9], [-1, 5], [-2, 4], [1, 1], [1, 2], [8, 3], [2, 2], [0, 3], [-2, 7], [2, 7], [8, 7], [3, 7], [1, 6], [3, 4], [7, 2], [3, 11], [2, 18], [2, 8], [3, 1], [2, 3], [0, 5], [4, 6], [8, 5], [3, 6], [-1, 3]], [[6900, 4114], [0, 4]], [[6900, 4118], [3, 5], [8, 8], [4, 10], [-1, 13], [3, 12], [8, 10], [3, 11], [-1, 10], [3, 9], [9, 6], [2, 3], [-1, 2], [0, 2], [0, 1], [1, 2], [2, 1], [2, 1], [1, 2], [0, 2], [-1, 5]], [[6945, 4233], [1, 5]], [[6946, 4238], [4, 9], [1, 8], [-4, 6], [2, 3], [2, 1], [3, 0], [0, 2], [0, 2], [-2, 4], [1, 3], [4, 3], [1, 6]], [[6958, 4285], [-2, 10]], [[6956, 4295], [1, 3], [0, 2], [1, 0], [1, 0], [2, -3]], [[6961, 4297], [1, -4]], [[6962, 4293], [1, -1], [2, 1]], [[6965, 4293], [4, 11]], [[6969, 4304], [3, 3], [2, 0], [4, -2], [2, 0], [4, 3], [3, 11], [2, 11], [1, 12], [-3, 10]], [[6987, 4352], [-5, 7], [-4, 3], [-1, -1], [0, -2], [0, -2], [0, -2], [0, -1], [-1, 0], [-4, 3], [-5, 8], [-5, 13], [-1, 10], [3, 8], [0, 8], [-3, 8], [-2, 8], [0, 8], [-8, 12], [-15, 17], [-11, 9], [-8, 4], [-8, 8], [-10, 13], [-5, 9], [0, 7], [4, 17], [13, 38], [1, 4]], [[6912, 4566], [1, 2], [-1, 2], [-1, 1], [-6, 5], [-10, 4], [-9, 2], [-8, -5], [-7, 9], [-5, 23], [-14, 24], [-23, 26], [-13, 17], [-3, 9], [-3, 14], [-3, 18], [0, 15], [4, 18]], [[6811, 4750], [4, 2], [1, 6], [-1, 11], [4, 7], [10, 6], [9, 12], [7, 18], [4, 13], [-1, 8], [-3, 7], [-5, 7], [-1, 9], [3, 10], [8, 7], [14, 3], [13, 5], [12, 8], [7, 8], [2, 10], [3, 7], [5, 5], [3, 8], [2, 12], [0, 8], [-1, 5], [-6, 7], [-8, 7], [-5, 7], [-1, 6], [-5, 7], [-8, 7]], [[6877, 4993], [-5, 9]], [[6872, 5002], [-3, 10], [-7, 8], [-13, 5], [-9, 8], [-4, 11], [-2, 13], [-1, 13], [1, 12], [3, 9], [-1, 6], [-4, 4], [-3, 4], [-2, 5]], [[6827, 5110], [-3, 42]], [[6824, 5152], [-3, 16], [-5, 7], [-10, 7], [-16, 9], [-9, 8], [-6, 11], [-11, 13], [-7, 5], [-7, 2], [-6, 5], [-4, 6], [-5, 4], [-8, 2], [-8, 5]], [[6719, 5252], [-14, 13], [-10, 4], [-6, 6], [-3, 8], [-3, 3], [-5, -3], [-4, 6], [-5, 15], [-4, 9], [-5, 4], [-40, 22], [-10, 6], [-6, 7], [-4, 9], [-2, 7], [1, 3], [-3, 7], [-6, 8], [-4, 8], [-1, 6], [0, 8], [2, 9], [0, 9], [-3, 8], [5, 11], [12, 13], [7, 10], [2, 7], [8, 3], [12, -1], [8, 3], [4, 8], [6, 5], [6, 4], [7, 9], [6, 16], [1, 10], [-4, 2], [-3, 5], [-4, 9], [-4, 6], [-7, 3], [-6, 1], [-7, -2], [-3, 1], [1, 6], [-3, 3], [-6, -1], [-5, 3], [-10, 12]], [[6536, 5547], [4, 19], [7, 16], [7, 4], [12, -5]], [[6576, 5583], [14, -6]], [[6264, 5065], [32, -24]], [[6296, 5041], [14, -6], [6, 6], [14, 3], [22, 0], [14, -2], [5, -6], [11, -6], [17, -7], [10, -7], [3, -8], [6, -6]], [[6418, 5002], [8, -2]], [[6426, 5000], [5, -10], [3, -19], [4, -15], [7, -11], [4, -8], [0, -6], [2, -5], [3, -6], [2, -11], [-1, -14], [1, -8], [4, -1], [2, -2], [-1, -3], [1, -2], [3, -2], [1, -4], [-1, -7], [1, -4], [3, -2], [1, -3], [-2, -5], [0, -4], [3, -3], [2, -13], [1, -22], [-1, -13], [-1, -4], [2, -8]], [[6474, 4785], [7, -12]], [[6481, 4773], [4, -13], [2, -12], [4, -8], [7, -3], [4, -8], [2, -12], [6, -10]], [[6510, 4707], [19, -17]], [[6529, 4690], [5, 0], [5, 3], [4, -4], [3, -9], [0, -5], [-4, 0], [-5, -6], [-5, -11], [1, -10], [8, -10], [6, -10], [3, -10], [5, -7], [12, -9]], [[6567, 4602], [9, 0], [6, 4], [7, 6], [7, 0], [8, -5], [9, -1], [8, 4], [11, 3], [13, 0], [7, 3], [1, 4], [3, 0], [3, -4], [6, 4], [8, 10], [10, 0], [13, -11], [5, -9], [-3, -6], [-1, -6], [2, -7], [7, -4], [12, -2], [12, -10], [16, -27], [13, -9], [11, 1], [14, 9], [14, 4], [14, 0], [15, -4], [16, -10], [12, -1], [10, 8], [7, 4], [4, 0], [7, 6], [8, 12], [7, 3], [5, -1], [9, -4]], [[6123, 5227], [2, -2], [2, -5], [9, -5], [16, -5], [7, -5], [-1, -5], [1, -4], [3, -3], [6, -3], [9, -1], [3, 2], [-3, 6], [1, 3], [4, 0], [3, -3], [1, -8], [5, -6], [11, -8]], [[5253, 5489], [-4, 1], [-4, 5], [-6, 9], [-2, 6], [1, 3], [-2, 6], [-4, 10], [-1, 7], [2, 4], [27, 29], [12, 9], [8, -1], [5, 4], [0, 6], [4, 6], [8, 3], [7, 7], [8, 12], [6, 6], [5, -1], [14, 8], [22, 17], [12, 7], [4, -3], [2, -6], [2, -10], [5, -8], [9, -7], [21, 0], [34, 4], [18, 0], [1, -4], [4, -5], [6, -4], [14, -5], [32, -4]], [[6046, 5588], [-2, -2], [1, -6], [4, -11], [8, -7], [14, -3], [7, -3], [-1, -4], [2, -6], [4, -7], [1, -6], [-2, -5], [3, -10], [8, -23]], [[5661, 5649], [3, 4], [11, 0], [20, -3], [18, 1], [14, 6], [15, 1], [17, -2], [11, 2], [6, 5], [12, 1], [17, -1], [19, -6], [22, -8], [12, -3], [5, 4]], [[2179, 7564], [-41, 3], [-15, -11], [-8, -8], [-5, -11], [-2, -7], [1, -5], [-16, -10], [-33, -16], [-18, -12], [-5, -8], [-7, -7], [-10, -6], [-9, -8], [-8, -8], [-4, -5], [1, -3], [-14, -6], [-42, -16]], [[7764, 5091], [-1, -20]], [[7293, 4552], [-2, 1], [-3, 3], [-5, 1], [-7, -1], [-3, -6], [1, -10], [-2, -7], [-7, -6], [-5, -7], [-3, -9], [-4, -5], [-7, -2], [-5, -9], [-4, -14], [-4, -9], [-5, -4], [-6, 1], [-8, 5], [-5, 6], [-1, 5], [-2, 4], [-2, 2], [-2, 0], [0, -1], [1, -1], [-1, -2], [-5, 0], [-2, -3], [1, -4], [-2, -3], [-2, -1], [-2, -4], [0, -6], [-2, -6], [-4, -5], [-6, 2], [-7, 8], [-8, 2], [-8, -4], [-5, -6], [-4, -8], [-4, -2], [-5, 6], [-13, 8], [-6, 2], [-5, -2], [-3, 1], [-1, 2], [-1, 1], [-1, -1], [0, -7], [-1, -5], [-2, -1], [-2, 1], [-1, 5], [-3, 1], [-4, -1], [-4, 0], [-4, 3], [-1, 0], [-2, -1], [1, -6], [-1, -4], [-2, -3], [-3, -1], [-3, 2], [-2, -2]], [[7073, 4445], [-2, -2], [-5, -8], [-1, -9], [3, -11], [-6, -8], [-16, -6], [-8, -6], [-1, -4], [1, -8], [3, -10], [1, -6], [-2, -4]], [[7040, 4363], [-11, 2], [-20, 9], [-13, 0], [-7, -9], [-2, -8], [0, -5]], [[7692, 4758], [-12, 9], [-6, 7], [-3, 9], [-6, 3], [-13, -4]], [[7652, 4782], [-9, -6], [-2, -4], [3, -6], [1, -8], [0, -10], [-5, -21], [-10, -32], [-6, -21], [0, -8], [-7, -11], [-13, -14], [-10, -8], [-5, -3], [-5, 1], [-3, 3], [-4, -2], [-5, -8], [-4, -4], [-4, 0], [-3, -5], [-3, -10], [-3, -6], [-2, -1], [0, -5], [2, -8], [0, -3], [-2, -1], [-1, 1], [-2, -2], [-3, -5], [-1, 0], [-1, 1], [-1, 6], [-2, 5], [-4, 2], [-6, -6], [-7, -13], [-3, -9], [1, -7], [1, -9], [-2, -4], [-4, -1], [-3, -5], [-2, -8], [-5, -6], [-9, -2]], [[7499, 4519], [-9, 4]], [[7490, 4523], [-11, 11], [-6, 10], [-3, 10], [-9, 0], [-16, -9], [-12, -1], [-8, 6], [-6, 2], [-6, -2], [-5, -3], [-5, 2], [-6, 7], [-9, 5], [-13, 3], [-9, 8], [-4, 13], [-6, 9], [-5, 4], [-6, 2], [-7, -2], [-5, 1], [-3, 4], [-5, -1]], [[7325, 4602], [-6, -4]], [[7319, 4598], [-1, -6], [2, -5], [0, -4], [-3, -3], [1, -2], [4, -2], [1, -4], [-1, -5], [-7, -6], [-15, -7], [-3, -2], [-3, -1], [-1, 1]], [[4820, 5763], [1, -4], [-1, -6], [-4, -8], [0, -10], [2, -13], [3, -9], [3, -5], [2, -9], [1, -14], [9, -10], [17, -7], [11, -1], [4, 7], [5, 2], [6, 0]], [[4582, 4906], [-2, -17], [-5, -7], [-12, -6], [-23, -6], [-11, -13], [0, -19], [-1, -12], [-3, -6], [-4, 0], [-5, 7], [-5, 1], [-5, -4], [-6, -1], [-8, 2], [-7, 0], [-6, -4], [-5, 0], [-4, 4], [-4, 0], [-2, -4], [-1, -4], [2, -6], [-2, -5], [-9, -8]], [[3000, 7931], [-8, -1], [-31, -10], [-9, -4], [-3, -5], [-2, -6], [-2, -3], [-2, -1], [-24, 0], [-5, 0], [-1, 1], [-1, 3], [-1, 1], [-2, 2], [-11, -7], [-19, -13], [-22, -7], [-27, 1], [-19, -2], [-12, -3], [-5, -3], [2, -3], [0, -2], [-1, -1], [-9, 2], [-6, -2], [-5, -6], [-9, -4], [-13, -3], [-9, 0], [-3, 3], [-2, 0], [-1, -3], [-5, -2], [-8, 0], [-9, -3], [-8, -7], [-9, -3], [-9, 2], [-13, -2], [-20, -7]], [[7820, 4663], [11, 5], [7, 0], [5, -6], [6, -3], [5, 1], [2, -2], [-1, -2], [2, -4], [4, -3], [1, -4], [-1, -3], [0, -2], [3, -2], [1, -3], [0, -6]], [[7865, 4629], [6, -5]], [[7871, 4624], [10, -5], [4, -5], [-1, -6], [-1, -3], [2, -4], [14, -7], [9, -10]], [[7908, 4584], [6, -4]], [[7914, 4580], [4, -2], [2, -3]], [[6663, 5719], [-7, -6]], [[6656, 5713], [-7, -3]], [[6648, 5710], [-1, -1]], [[6627, 5717], [-1, 1]], [[6619, 5721], [-9, 3]], [[6610, 5724], [-18, 4]], [[6578, 5731], [-11, 3]], [[6567, 5734], [-2, 1]], [[6560, 5742], [0, 6]], [[6257, 3089], [4, -3]], [[6261, 3086], [9, -1]], [[6270, 3085], [4, -3]], [[6281, 3077], [5, -5]], [[6286, 3072], [8, -3]], [[6314, 3064], [8, -1]], [[6322, 3063], [17, -6], [11, -11]], [[6350, 3046], [1, -1]], [[6356, 3043], [1, 1]], [[6359, 3049], [3, 3]], [[6362, 3052], [7, 1]], [[6103, 3419], [1, -11], [7, -18], [12, -24], [5, -13]], [[6127, 3347], [0, -3]], [[6127, 3344], [3, -10]], [[6132, 3329], [4, -7]], [[6136, 3322], [9, -8]], [[6145, 3314], [8, -15]], [[6153, 3299], [9, -21]], [[6162, 3278], [6, -9]], [[6186, 3249], [5, -8]], [[6191, 3241], [2, -10], [0, -8]], [[6193, 3223], [-1, -3]], [[6192, 3210], [3, -6]], [[6195, 3184], [0, -1]], [[5648, 3743], [10, -6]], [[5658, 3737], [7, -10]], [[5665, 3727], [4, -10]], [[5685, 3697], [5, -4]], [[5690, 3693], [16, -18], [23, -31], [18, -20]], [[5773, 3600], [2, -2]], [[5775, 3598], [4, -11]], [[5787, 3565], [6, -20]], [[5793, 3545], [0, -17]], [[5793, 3528], [3, -12]], [[5801, 3504], [6, -13]], [[5807, 3491], [8, -12], [7, -4], [7, -7]], [[5829, 3468], [4, -5]], [[5842, 3453], [4, -4]], [[5846, 3449], [11, -3], [14, -10]], [[5871, 3436], [8, -6]], [[5898, 3415], [5, -3]], [[5931, 3437], [2, 5]], [[5933, 3454], [2, 10]], [[5935, 3464], [8, 23], [9, 13]], [[5952, 3500], [2, 0]], [[5966, 3504], [4, 2]], [[5970, 3506], [3, 6]], [[5981, 3512], [6, -3]], [[5987, 3509], [13, -4]], [[6008, 3504], [10, -1]], [[6018, 3503], [11, 1]], [[6031, 3505], [2, 2]], [[6033, 3507], [1, -1], [0, -2]], [[6047, 3499], [3, -6]], [[5592, 3871], [4, -19], [6, -8], [7, -3], [8, -8], [6, -12], [4, -10], [0, -6], [5, -18], [16, -44]], [[5601, 3902], [-8, -6]], [[5582, 4444], [28, -4], [11, 8], [8, -2], [10, -9], [11, -5], [13, 0], [14, -3], [14, -8], [10, -7], [5, -6], [4, -8], [2, -8], [0, -11], [-1, -15], [1, -16], [3, -18], [0, -21], [-2, -22], [-3, -14], [-7, -11], [-12, -13], [-5, -10], [-2, -12], [-5, -11], [-8, -11], [-6, -13], [-3, -16], [-8, -17], [-11, -18], [-5, -15], [1, -12], [-2, -25], [-6, -37], [-5, -20], [-2, -3], [0, -14], [1, -26], [-5, -21], [-9, -16], [-6, -14], [-3, -16]], [[7725, 4519], [14, 26], [10, 31], [7, 12], [7, 2], [9, 12], [10, 23], [9, 15], [10, 10]], [[7801, 4650], [6, 1], [3, 2], [1, 6], [3, 3], [6, 1]], [[4493, 4548], [5, -5], [1, -5], [-2, -5], [0, -7], [1, -8], [0, -5], [-2, -3], [0, -3], [3, -5], [-3, -5], [-2, -4], [-9, -5], [-1, -3], [-2, -3], [-2, -2]], [[4430, 4790], [-2, -8], [4, -9], [10, -12], [5, -8], [0, -6], [0, -3], [3, -1], [-1, -4], [-2, -8], [0, -5], [3, -2], [3, -11], [3, -19], [4, -13], [4, -5], [0, -14], [-2, -22], [-1, -15], [1, -6], [4, -9], [6, -12], [4, -10], [1, -10], [2, -8], [5, -6], [4, -3], [3, 0], [2, -3], [0, -10]], [[8440, 5433], [4, 12]], [[8452, 5458], [3, 3], [5, 8], [4, 3], [5, -2], [2, 2], [0, 5], [11, 17], [21, 28], [14, 16], [8, 4], [5, 1], [4, -1], [7, 3], [10, 8]], [[8551, 5553], [12, 6]], [[8563, 5559], [11, 2]], [[8583, 5565], [3, 1]], [[8586, 5566], [0, 1]], [[8587, 5569], [4, 2], [5, -1]], [[8598, 5569], [9, -8]], [[8607, 5561], [15, -15]], [[4638, 4353], [-6, -13], [-12, -6], [-21, -4], [-14, 7], [-9, 18], [-8, 11], [-7, 4], [-12, 16], [-27, 43]], [[4712, 4364], [-5, 12], [-23, 25], [-4, 3], [-3, 2], [-2, -2], [-2, -4], [-6, -18], [-6, -10], [-7, -2], [-4, -3], [-4, -7]], [[4522, 4429], [-4, 6], [-2, 9], [-1, 14], [-4, 9], [-7, 4], [-2, 1], [-2, 3], [-2, 2]], [[7522, 3960], [15, -17], [6, -8], [0, -7], [2, -5], [3, -3], [3, -6], [4, -9], [6, -8], [9, -7], [7, -16], [4, -24], [5, -14], [6, -6], [9, -21], [3, -12], [0, -11], [5, -9], [15, -9]], [[7475, 4043], [17, -41]], [[5117, 5020], [-12, -11], [-12, -5], [-17, -2], [-21, 4], [-26, 9], [-15, 9], [-5, 7], [-3, 7], [1, 5], [-5, 4], [-8, 1], [-6, 3], [-2, 6], [-6, 0], [-10, -4], [-14, 0], [-18, 5], [-9, 0], [-4, -2], [-9, 5], [-13, 12], [-8, 10], [-2, 7], [-4, 4], [-6, 2], [-7, 5], [-8, 10], [-4, 8], [-2, 5], [-6, 7], [-11, 7], [-4, 7], [3, 8]], [[4844, 5153], [5, 19]], [[4849, 5172], [1, 8], [-1, 5], [1, 5], [2, 4], [0, 5], [-3, 6], [-6, 3], [-8, 2], [-5, 6], [0, 11], [7, 17], [13, 23], [7, 15], [1, 10], [8, 21], [14, 34], [6, 22], [-3, 10], [-8, 9], [-11, 8], [-8, 10]], [[4856, 5406], [-5, 12]], [[4851, 5418], [-1, 8], [2, 4], [-2, 9], [-5, 11], [-2, 8]], [[4843, 5458], [0, 4], [-3, 2], [-7, 0], [-4, 2], [-1, 6], [-4, 8], [-9, 9], [-8, 6], [-9, 0], [-6, -2], [-5, -5], [-9, -4], [-12, -4], [-13, -1], [-14, 2], [-12, -8], [-11, -16], [-11, -12], [-16, -9]], [[5164, 5036], [-12, -15], [-9, -4], [-14, 1]], [[5293, 5098], [-13, 13], [-4, 7], [-2, 6], [-12, 9], [-24, 11], [-11, -2], [1, -17], [-3, -14], [-7, -12], [-12, -14], [-25, -24]], [[5336, 5157], [-8, -26], [-4, -10], [-3, -2], [-1, -4], [1, -6], [2, -3], [4, -3], [1, -5], [-2, -8], [-6, -5], [-11, -4], [-6, 2]], [[4547, 4988], [-13, 5], [-30, 4], [-12, 5], [-6, 7], [-8, 0], [-8, -8], [-3, -4]], [[7505, 5059], [-5, -31]], [[7500, 5028], [-3, -11]], [[7489, 5007], [-1, -1]], [[8096, 5294], [-2, -1]], [[8094, 5293], [-3, 1]], [[8033, 5253], [-18, -19]], [[3703, 6671], [-2, -1], [-15, 1], [-10, -2], [-8, -6], [-4, -2]], [[2563, 7353], [-12, 8], [-4, 5], [0, 3], [-6, 4], [-11, 3], [-9, 0], [-9, -4], [-12, 2], [-16, 9], [-14, 6], [-18, 3], [-30, 1], [-13, -1], [-6, -3], [-8, 0], [-10, 4], [-15, 1], [-20, 0], [-19, -6], [-17, -12], [-10, -10], [-5, -12], [-4, -10], [0, -9], [2, -12], [0, -7], [-2, -3], [0, -3], [4, -4], [1, -7], [0, -9], [-6, -15], [-11, -20], [-8, -11], [-7, -4], [-4, -7], [0, -14], [0, -7]], [[2921, 7297], [-4, 4], [-1, 5], [0, 7], [3, 6], [8, 5], [10, 5], [6, 6], [4, 8], [6, 7], [9, 5], [5, 5], [2, 4], [-1, 9], [-5, 11], [-6, 7], [-6, 2], [-2, 3], [3, 4], [-9, 9], [-21, 14], [-14, 7], [-8, 0], [-9, 3], [-10, 5], [-7, 6], [-4, 7], [-4, 3], [-5, 0], [-6, 1], [-6, 5], [-6, 2], [-9, -3], [-36, 0], [-13, 1], [-2, 5], [-1, 5], [2, 5], [-2, 6], [-5, 6], [-16, 5], [-25, 4], [-19, 8], [-12, 12], [-5, 8], [1, 5], [-2, 4], [-9, 4], [-10, 2], [-11, -1], [-11, 3], [-12, 7], [-10, 2], [-8, -2], [-15, 3], [-22, 8], [-15, 2], [-13, -2], [-20, 15], [-6, 8], [0, 7], [-5, 8], [-9, 9], [-10, 6], [-10, 5], [-13, 4], [-16, 1], [-12, -1], [-13, -7], [-31, -12], [-18, -5], [-25, 0], [-1, 2], [0, 6], [1, 11], [-1, 5], [-2, 2], [0, 3], [2, 3], [0, 3], [-4, 3], [-9, 1], [-15, 0], [-14, -4], [-12, -8], [-19, 3], [-25, 11], [-20, 6], [-17, -1], [-15, -3], [-14, -6], [-11, 1], [-8, 7], [-3, 4], [1, 3], [-2, 1], [-4, -2], [-4, 2], [-2, 6], [-7, 5], [-20, 5]], [[7099, 4091], [-8, -2], [-12, 10], [-9, 10]], [[7070, 4109], [-8, 10]], [[7062, 4119], [-9, 13], [-1, 7], [3, 6], [4, 6], [3, 5], [0, 4], [4, 3], [8, 2], [3, 1], [-2, 2], [0, 4], [2, 5], [0, 8], [-2, 11], [0, 11], [4, 12], [2, 7], [-3, 2]], [[7386, 4220], [-20, -13], [-9, -3], [-4, 1], [-4, 3], [-7, 11], [-4, -7], [-3, -2], [-2, 2], [-3, -7], [-5, -14], [-4, -7], [-3, -1], [-3, -4], [0, -6], [-2, -3], [-3, 1], [-4, -8], [-6, -17], [-7, -13], [-8, -8], [-5, -2], [-1, 5], [-2, 1], [-3, -5], [-2, -1], [-4, 4], [-4, 0], [-6, -4], [-3, -5], [-1, -5], [-12, -19], [-21, -32], [-14, -15], [-6, 3], [-6, 6], [-7, 9], [-9, 4], [-18, -2]], [[7054, 4355], [-14, 8]], [[7433, 4083], [15, -13]], [[7326, 4748], [8, -1], [1, 2], [-7, 6], [-2, 2], [-1, 5], [0, 5], [-2, 5], [-7, 3], [-10, 7], [-11, 12], [-12, 9], [-14, 5], [-14, -1], [-16, -7], [-18, -5], [-20, -1], [-18, -11], [-16, -20], [-10, -9], [-6, 0], [-7, -3], [-8, -6], [-14, -14], [-3, -11], [0, -12], [2, -10], [2, -5], [1, -10], [-1, -15], [-2, -13], [-3, -12], [-4, -9], [-5, -6]], [[7109, 4628], [-3, -6], [1, -7], [-1, -4], [-3, -3], [2, -8], [4, -11], [3, -12], [1, -11], [-1, -9], [-4, -7], [-4, -16], [-3, -6], [-4, -3], [-4, -6], [-3, -9], [-4, -5], [-4, 0], [-2, -2], [-2, -4], [0, -4], [2, -4], [-1, -4], [-3, -4], [0, -1], [1, -1], [0, -2], [-3, -2], [-1, -5], [0, -9], [-1, -5], [-3, 0], [0, -2], [4, -11]], [[4454, 5108], [-10, 7], [-11, 4], [-5, 7], [0, 11], [-2, 9], [-5, 6], [-13, 11], [-21, 16], [-12, 15], [-5, 20], [-3, 22], [3, 10], [6, 6], [2, 4], [-2, 3], [-1, 5], [2, 6], [0, 4], [-4, 2], [2, 4], [5, 7], [2, 6], [-1, 5], [0, 5], [4, 4], [0, 5], [-2, 5], [0, 5], [3, 3], [6, 2], [14, 3], [7, 7], [2, 7], [-3, 9], [-3, 7], [-4, 4], [-1, 6], [1, 3]], [[7570, 4244], [4, -4], [5, 1], [9, 6], [34, 14], [18, 4], [14, -2], [2, -1]], [[3000, 7610], [-1, 0], [-4, 4], [0, 3], [-2, 3], [-6, 3], [-1, 4], [4, 4], [0, 2], [-4, 1], [-2, 2], [1, 4], [-3, 1], [-7, -1], [-4, 2], [0, 9], [-2, 5], [-10, 5], [-18, 7], [-12, 7], [-6, 7], [-12, 4], [-16, 1], [-11, 2], [-6, 3], [-6, 1], [-6, -4], [-15, -1], [-23, 1], [-17, 2], [-10, 6], [-7, 5], [-2, 5], [-6, 4], [-11, 3], [-8, 4], [-5, 5], [0, 8], [5, 11], [1, 7], [-3, 5], [-15, 13], [-29, 22], [-16, 14], [-3, 6], [-10, 8], [-17, 10], [-18, 6]], [[2657, 7833], [-29, 3]], [[1288, 7295], [1, -9], [0, -6], [-4, -5], [-7, -2], [-12, 1], [-8, -2], [-5, -4], [-5, -1], [-5, 3], [0, 4], [-2, 2], [-3, 0], [-4, -2], [-3, 2], [-2, 5], [-5, -2], [-7, -7], [-7, -3], [-5, 1], [-7, -2], [-10, -5], [-7, 0], [-8, 2], [-6, -2], [-4, -5], [1, -4], [8, -4], [2, -5], [-3, -4], [-9, -6], [-9, -3], [-6, -4], [-8, -6], [-1, -7], [0, -6], [-3, -2]], [[1288, 7295], [-14, 6], [-23, 3], [-13, 5], [-4, 6], [1, 8], [4, 9], [2, 7], [0, 7], [-5, 10], [-10, 13], [-12, 8], [-20, 4], [-4, -1], [-5, -3]], [[2628, 7836], [-64, -12], [-27, -8], [-11, -7], [-13, -4], [-14, -1], [-7, -2], [1, -5], [-12, -2], [-26, 0], [-22, -5], [-18, -8], [-10, -6], [-3, -4], [-2, -1], [-1, 3], [-5, 1], [-7, -2], [-4, -2], [1, -4], [-4, -2], [-9, -2], [-6, -4], [-2, -6], [-12, -2], [-22, 2], [-15, -2], [-11, -7], [1, -4], [12, 0], [5, -5], [-3, -9], [-5, -3], [-9, 2], [-6, -3], [-5, -9], [-10, -6], [-18, -1], [-18, -7], [-21, -12], [-26, -10], [-50, -11]], [[2150, 7666], [-23, -5], [-14, 1], [-13, 6], [-12, 2], [-12, -1], [-9, -3], [-7, -5], [-9, -2], [-11, 2], [-20, -5], [-30, -12], [-21, -6], [-14, 1], [-20, -4], [-22, -4], [-19, -9], [-15, -4], [-14, 0], [-19, -5], [-16, -1], [-17, -3], [-23, -2], [-2, 3], [-4, 3], [-9, 1], [-5, 3], [-4, 5], [-18, 4], [-9, -1], [-9, 3], [-20, 1]], [[1710, 7629], [-6, 2], [-5, -1], [-7, -7], [-13, -14], [-9, -5], [-16, -21], [-14, -14], [-1, -4], [2, -10], [-1, -10], [-4, -10], [-7, -7], [-15, -8], [-10, -5], [-4, -4], [-1, -6], [-3, -4], [-5, -3], [-5, -7], [-6, -12], [-2, -10], [3, -7], [-4, -7], [-11, -9], [-7, -10], [-2, -12], [-5, -9], [-9, -6], [-6, -8], [-2, -11], [-4, -8], [-4, -4], [-2, -7], [0, -9], [3, -7], [6, -6], [4, -7], [2, -5], [1, -6], [1, -5], [3, -3], [8, -2], [5, -2], [1, -4], [-1, -3], [-3, -5], [-5, -4], [-8, -1], [-6, -5], [-5, -7], [-19, -4], [-32, 0], [-24, -4], [-14, -6], [-7, -6], [1, -4], [-1, -6], [-4, -5], [-10, -4], [-18, -3], [-5, -2], [-7, -3], [-3, 3], [-1, 7], [-4, 10], [-9, 14], [-9, 7], [-9, 0], [-5, 3], [-3, 6], [-4, 1], [-7, -2], [-4, -4], [-2, -4], [-3, 0], [-6, 3], [-6, -1], [-5, -3], [-7, 0], [-7, 1], [-5, 5], [-4, 6]], [[2628, 7836], [0, 0]], [[2629, 7836], [-1, 0]], [[3230, 6993], [-1, -1], [-3, 0], [-10, -4], [-3, -4], [-3, -6], [1, -3], [2, -2], [1, -1], [1, -2], [1, -3], [0, -3], [0, -1], [-1, -1], [-2, -1], [-1, 0], [-15, 1], [-4, -1], [-2, -1]], [[8047, 4455], [12, 24]], [[8060, 4479], [-10, -22]], [[8401, 5051], [-4, -13]], [[8370, 4911], [-7, -8]], [[8325, 4862], [-13, 1]], [[8185, 4826], [-7, -7]], [[8160, 4823], [-1, -10]], [[8045, 4686], [-5, -6]], [[8073, 4529], [0, -9]], [[8049, 4478], [-19, -51]], [[8008, 4312], [10, -50]], [[8014, 4267], [-7, 35]], [[8002, 4302], [-2, -11]], [[7814, 3990], [-13, -5]], [[7119, 3575], [-5, -3]], [[7049, 3573], [-26, -2]], [[6965, 3555], [-11, -6]], [[6373, 3054], [-11, -2]], [[6362, 3052], [-4, -4]], [[6358, 3048], [-1, -3]], [[6357, 3045], [-1, -2]], [[6356, 3043], [-1, 0]], [[6355, 3043], [-5, 3]], [[6322, 3063], [-21, 3]], [[6301, 3066], [-15, 6]], [[6286, 3072], [-8, 8]], [[6278, 3080], [-8, 5]], [[6261, 3086], [-7, 5]], [[6254, 3091], [-6, 8], [-8, 4], [-11, 2], [-7, 6], [-5, 14]], [[6217, 3125], [-5, 23]], [[6212, 3148], [-5, 15]], [[6207, 3163], [-11, 17]], [[6196, 3180], [-1, 3]], [[6195, 3183], [0, 3]], [[6195, 3186], [1, 10], [-1, 8]], [[6192, 3210], [-1, 6]], [[6191, 3216], [2, 7]], [[6191, 3241], [-7, 10]], [[6184, 3251], [-12, 11]], [[6172, 3262], [-10, 16]], [[6136, 3322], [-6, 10]], [[6130, 3332], [-3, 12]], [[6127, 3344], [-1, 7]], [[6126, 3351], [2, 2]], [[6103, 3419], [-8, 14]], [[6095, 3433], [-13, 15], [-7, 11], [-2, 7]], [[6073, 3466], [-20, 20]], [[6053, 3486], [-6, 13]], [[6047, 3499], [-5, 4], [-5, -1], [-3, 2]], [[6033, 3507], [-4, -3]], [[6018, 3503], [-18, 2]], [[5987, 3509], [-8, 5]], [[5979, 3514], [-6, -1], [-3, -7]], [[5970, 3506], [-7, -4]], [[5963, 3502], [-11, -2]], [[5935, 3464], [-3, -15]], [[5932, 3449], [1, -7]], [[5931, 3437], [-5, -3], [-5, -7], [-5, -11], [-6, -5]], [[5910, 3411], [-7, 1]], [[5903, 3412], [-13, 8]], [[5890, 3420], [-19, 16]], [[5846, 3449], [-9, 8]], [[5837, 3457], [-8, 11]], [[5807, 3491], [-9, 21]], [[5798, 3512], [-5, 16]], [[5793, 3545], [-12, 37]], [[5781, 3582], [-6, 16]], [[5775, 3598], [-5, 7]], [[5770, 3605], [-9, 9]], [[5761, 3614], [-14, 10]], [[5690, 3693], [-10, 7]], [[5680, 3700], [-8, 11], [-7, 16]], [[5658, 3737], [-1, 0]], [[5657, 3737], [-17, 0], [-17, 1]], [[5623, 3738], [-17, 0]], [[5606, 3738], [-17, 0]], [[5589, 3738], [-17, 0]], [[5572, 3738], [-17, 0]], [[5555, 3738], [-16, 0], [-17, 0]], [[5522, 3738], [0, -13]], [[5522, 3725], [0, -13], [-1, -13], [0, -14], [-27, 0], [-27, 0]], [[5467, 3685], [-27, 0]], [[5440, 3685], [-27, 0]], [[5413, 3685], [-28, 0]], [[5385, 3685], [-27, 0]], [[5358, 3685], [-27, 0]], [[5331, 3685], [-27, 0]], [[5304, 3685], [-37, 17]], [[5267, 3702], [-36, 18]], [[5231, 3720], [-36, 17]], [[5195, 3737], [-37, 17]], [[5158, 3754], [-36, 18]], [[5122, 3772], [-37, 17], [-36, 18]], [[5049, 3807], [-37, 17]], [[5012, 3824], [4, 7], [5, 17]], [[5021, 3848], [-9, -1]], [[5012, 3847], [-22, -2]], [[4990, 3845], [-22, -3]], [[4968, 3842], [-22, -2]], [[4946, 3840], [-22, -3]], [[4924, 3837], [-22, -2]], [[4902, 3835], [-22, -3]], [[4880, 3832], [-22, -2]], [[4858, 3830], [-22, -3]], [[4291, 4939], [-11, 16]], [[4400, 5763], [8, 0], [33, 0], [33, 0], [33, 0], [32, 0], [33, 0], [33, 0], [33, 0]], [[4638, 5763], [33, 0]], [[4671, 5763], [32, 0]], [[4703, 5763], [33, 0], [33, 0], [33, 0], [33, 0]], [[4835, 5763], [32, 0]], [[4867, 5763], [33, 0]], [[4900, 5763], [33, 0]], [[4933, 5763], [33, 0], [33, 0], [32, 0], [33, 0], [33, 0], [33, 0], [33, 0], [32, 0], [33, 0], [33, 0], [33, 0], [33, 0], [32, 0], [33, 0], [33, 0]], [[5425, 5763], [33, 0]], [[5458, 5763], [33, 0]], [[5491, 5763], [32, 0], [33, 0], [33, 0], [33, 0], [33, 0], [32, 0], [33, 0], [33, 0], [33, 0], [32, 0]], [[5818, 5763], [33, 0]], [[5851, 5763], [33, 0], [33, 0], [33, 0], [32, 0], [33, 0], [33, 0], [33, 0], [33, 0], [32, 0], [33, 0], [33, 0]], [[6212, 5763], [33, 0]], [[6245, 5763], [33, 0]], [[6278, 5763], [32, 0], [33, 0]], [[6343, 5763], [33, 0]], [[6376, 5763], [33, 0], [33, 0], [32, 0], [33, 0], [18, 0]], [[6525, 5763], [1, 25]], [[6526, 5788], [0, 20]], [[6526, 5808], [16, -3]], [[6542, 5805], [5, -3]], [[6547, 5802], [2, -2]], [[6549, 5800], [0, -5]], [[6550, 5778], [3, -14]], [[6553, 5764], [7, -16]], [[6560, 5742], [3, -4]], [[6563, 5738], [4, -4]], [[6567, 5734], [16, -5]], [[6583, 5729], [27, -5]], [[6610, 5724], [16, -6]], [[6626, 5718], [4, -7]], [[6630, 5711], [7, -3]], [[6637, 5708], [11, 2]], [[6648, 5710], [8, 3]], [[6672, 5720], [8, 0], [8, -1]], [[6688, 5719], [4, -1]], [[6692, 5718], [12, -5]], [[6704, 5713], [8, -4]], [[6712, 5709], [12, -8]], [[6724, 5701], [6, -3]], [[6730, 5698], [3, -8]], [[6733, 5690], [4, -11]], [[6737, 5679], [5, 0], [4, 6]], [[6746, 5685], [9, 1]], [[6755, 5686], [13, -4]], [[6768, 5682], [11, -12]], [[6779, 5670], [17, -11], [10, -6]], [[6806, 5653], [10, 0]], [[6816, 5653], [12, 6], [14, 10]], [[6842, 5669], [10, 2]], [[6852, 5671], [6, -1]], [[6858, 5670], [3, -8], [4, -3]], [[6865, 5659], [11, 1]], [[6876, 5660], [22, -2]], [[6898, 5658], [17, 2]], [[6915, 5660], [4, -4], [4, -8]], [[6923, 5648], [7, -2]], [[6930, 5646], [10, 2]], [[6940, 5648], [17, -2]], [[6957, 5646], [7, 0], [14, 3], [7, 3], [9, 6], [13, 7], [22, 13], [18, 4], [17, -9], [13, -8], [5, -3], [14, -8], [19, -12], [22, -13], [22, -14], [19, -11], [14, -8], [5, -4], [15, -9], [15, -9], [15, -10], [15, -9], [15, -10], [15, -9], [15, -10], [15, -9], [3, -16], [4, -15], [9, -11], [8, -10], [4, 1], [5, 4], [3, 2]], [[7353, 5472], [5, 0]], [[7358, 5472], [11, 4], [3, -1], [2, -2], [0, -3], [0, -2], [-2, -4], [3, -9], [1, -10], [1, -7], [5, -9], [4, -8], [5, -1], [11, 4], [7, 2], [4, -1], [7, -7], [4, -4], [1, -3], [-10, -21], [15, -11], [17, -11], [20, -13], [12, -9], [16, -12], [3, -16], [2, -15], [3, -19], [3, -21], [3, -20], [4, -21], [3, -23], [3, -21], [4, -23], [4, -30], [-4, -11], [-9, -25], [-8, -22], [0, -7]], [[7506, 5060], [-6, -32]], [[7500, 5028], [-4, -14]], [[7496, 5014], [-8, -8]], [[7488, 5006], [-8, -7], [-9, -13], [-10, -6], [-6, -4]], [[7455, 4976], [-3, -6]], [[7452, 4970], [-3, -13], [1, -19], [8, -17], [13, -9], [14, -10], [19, 0], [17, 13], [19, 13], [16, 11], [20, 14], [17, 12], [19, 5], [27, 6], [33, 8], [17, 9], [21, 11], [24, 13], [21, 12], [11, 6], [7, 7], [2, 6], [0, 3], [-2, 3], [-3, 2], [-2, 2], [-2, 2], [0, 6], [-1, 3], [-2, 2]], [[7764, 5091], [-2, 6]], [[7762, 5097], [-7, 16], [13, 7], [12, 7], [10, 5], [20, 1], [19, 0], [26, -1], [21, 0], [26, 0], [15, 0], [19, 0], [10, 19], [8, 16], [9, 16], [17, 18], [5, 3], [3, 8], [10, 7], [11, 6], [4, 6], [2, 3]], [[8015, 5234], [30, 33]], [[8045, 5267], [17, 15], [14, 8], [11, 4]], [[8087, 5294], [7, -1]], [[8094, 5293], [5, 1]], [[8099, 5294], [3, 0]], [[8102, 5294], [18, 0]], [[8120, 5294], [32, 0]], [[8152, 5294], [32, 0]], [[8184, 5294], [32, 0]], [[8216, 5294], [32, 0], [32, 0]], [[8280, 5294], [32, 0]], [[8312, 5294], [32, 0]], [[8344, 5294], [8, 23]], [[8352, 5317], [7, 11]], [[8359, 5328], [9, -4]], [[8368, 5324], [5, 1]], [[8373, 5325], [6, 5]], [[8379, 5330], [5, 3], [3, 0], [2, -5]], [[8389, 5328], [3, -4]], [[8392, 5324], [2, 1]], [[8394, 5325], [2, 5], [0, 7], [3, 4]], [[8399, 5341], [4, 1]], [[8403, 5342], [3, 0], [2, 2]], [[8408, 5344], [-1, 3]], [[8407, 5347], [-1, 5]], [[8406, 5352], [1, 6], [8, 11], [10, 8], [3, 3]], [[8428, 5380], [1, 8]], [[8429, 5388], [6, 8]], [[8435, 5396], [3, 4]], [[8438, 5400], [1, 4]], [[8439, 5404], [-2, 5]], [[8437, 5409], [0, 9]], [[8437, 5418], [2, 11]], [[8439, 5429], [3, 12], [5, 10]], [[8447, 5451], [9, 12]], [[8456, 5463], [2, 15]], [[8458, 5478], [2, 17]], [[8460, 5495], [11, 15]], [[8471, 5510], [11, 18]], [[8482, 5528], [7, 10]], [[8489, 5538], [12, 19], [9, 13]], [[8510, 5570], [4, 6]], [[8514, 5576], [5, 7]], [[8519, 5583], [7, -2]], [[8526, 5581], [8, -2]], [[8534, 5579], [-1, -10]], [[8533, 5569], [1, -8]], [[8534, 5561], [3, -4], [5, -3], [4, -1]], [[8546, 5553], [5, 0]], [[8563, 5559], [15, 3]], [[8578, 5562], [8, 4]], [[8586, 5566], [1, 3]], [[8596, 5570], [11, -9]], [[8607, 5561], [13, -12]], [[8620, 5549], [9, -10]], [[8629, 5539], [1, -18], [0, -18]], [[8630, 5503], [0, -19]], [[8630, 5484], [0, -14]], [[8630, 5470], [0, -19]], [[8630, 5451], [1, -15], [0, -20]], [[8631, 5416], [0, -10]], [[8631, 5406], [1, -3]], [[8632, 5403], [0, -5]], [[8632, 5398], [-1, -2]], [[8631, 5396], [0, -1], [1, -2], [0, -3]], [[8632, 5390], [-1, -3]], [[8631, 5387], [-1, -3]], [[8630, 5384], [0, -5]], [[8630, 5379], [1, -3]], [[8631, 5376], [2, -2]], [[8633, 5374], [2, 0], [3, -1], [3, -4], [5, -2], [5, -1], [3, 0], [4, -1]], [[8658, 5365], [2, -5]], [[8660, 5360], [-1, -4]], [[8659, 5356], [-3, -2]], [[8656, 5354], [-2, -1], [-1, -4]], [[8653, 5349], [2, -3]], [[8655, 5346], [2, -3]], [[8657, 5343], [2, -5]], [[8659, 5338], [-1, -4]], [[8658, 5334], [-2, -4]], [[8656, 5330], [-1, -4]], [[8655, 5326], [2, -3], [4, -5], [2, -4], [4, -2], [2, 1]], [[8669, 5313], [2, 3]], [[8671, 5316], [1, 1]], [[8672, 5317], [3, -1], [3, -1], [4, -1]], [[3829, 6473], [-7, -9]], [[3822, 6464], [-14, -14], [-6, -7]], [[3000, 8193], [0, -34], [0, -34], [0, -35], [0, -34], [0, -34], [0, -35], [0, -34], [0, -35], [0, -34], [0, -34], [0, -35], [0, -34], [0, -34], [0, -35], [0, -34], [0, -35], [0, -34], [0, -34], [0, -35], [0, -34], [0, -34], [0, -35], [0, -34], [0, -35], [0, -34], [0, -34], [0, -35], [0, -34], [0, -34], [0, -35], [0, -34], [0, -35]], [[3000, 7093], [18, -4]], [[3018, 7089], [18, -5]], [[3036, 7084], [6, 9]], [[3042, 7093], [19, -7]], [[3061, 7086], [18, -6]], [[3079, 7080], [11, 8]], [[3090, 7088], [12, 9], [16, 0]], [[3118, 7097], [17, 1]], [[3135, 7098], [12, 1]], [[3147, 7099], [0, -8], [-4, -13], [-4, -10]], [[3139, 7068], [11, -11]], [[3150, 7057], [14, -5]], [[3164, 7052], [12, -5]], [[3176, 7047], [6, -15]], [[3182, 7032], [14, -11]], [[3196, 7021], [10, -9]], [[3206, 7012], [10, -8]], [[3216, 7004], [14, -11]], [[3230, 6993], [10, -9]], [[3240, 6984], [14, -10], [8, -7]], [[3262, 6967], [4, -12], [4, -16], [-3, -8]], [[3267, 6931], [7, -2]], [[3274, 6929], [12, 10]], [[3286, 6939], [12, 6]], [[3298, 6945], [14, 8]], [[3312, 6953], [10, 5]], [[3322, 6958], [18, 0]], [[3340, 6958], [8, 15], [0, 21]], [[3348, 6994], [10, 0]], [[3358, 6994], [5, 3]], [[3363, 6997], [2, 6], [-5, 9]], [[3360, 7012], [17, 4], [12, 2], [18, 8]], [[3407, 7026], [18, 8]], [[3425, 7034], [8, -6]], [[3433, 7028], [8, -6]], [[3441, 7022], [16, -13]], [[3457, 7009], [1, -4]], [[3458, 7005], [-1, -6]], [[3457, 6999], [-1, -7], [10, -18]], [[3466, 6974], [2, -2]], [[3468, 6972], [8, -2]], [[3476, 6970], [10, -6]], [[3486, 6964], [4, -5]], [[3490, 6959], [14, -8], [3, -4], [1, -5]], [[3508, 6942], [2, -5]], [[3510, 6937], [3, -4]], [[3513, 6933], [2, -4], [6, -6]], [[3521, 6923], [12, -7]], [[3533, 6916], [8, -4]], [[3541, 6912], [11, -6]], [[3552, 6906], [11, -13]], [[3563, 6893], [10, -11]], [[3573, 6882], [11, -11]], [[3584, 6871], [-1, -9]], [[3583, 6862], [11, -13], [12, -17]], [[3606, 6832], [9, -15]], [[3615, 6817], [6, -9]], [[3621, 6808], [8, -12], [10, -15]], [[3639, 6781], [11, -17]], [[3650, 6764], [8, -11]], [[3658, 6753], [11, -15]], [[3669, 6738], [5, -9]], [[3674, 6729], [-4, -7]], [[3670, 6722], [-4, -7]], [[3666, 6715], [14, -4]], [[3680, 6711], [10, -3]], [[3690, 6708], [-3, -8]], [[3687, 6700], [-3, -12]], [[3684, 6688], [11, -4]], [[3695, 6684], [7, -3]], [[3702, 6681], [-1, -6]], [[3701, 6675], [4, -7]], [[3705, 6668], [0, -11]], [[3705, 6657], [14, 1], [6, 0], [8, -5]], [[3733, 6653], [10, -6]], [[3743, 6647], [11, -7]], [[3754, 6640], [8, -5]], [[3762, 6635], [12, -3]], [[3774, 6632], [15, -4]], [[3789, 6628], [7, -9]], [[3796, 6619], [13, -4]], [[3809, 6615], [5, -13]], [[3814, 6602], [15, -5], [9, 3]], [[3838, 6600], [3, -5]], [[3841, 6595], [3, -6]], [[3844, 6589], [1, -7]], [[3845, 6582], [-1, -8]], [[5371, 8804], [51, 5]], [[5385, 8997], [-22, -8]], [[5369, 8974], [22, -8]], [[5454, 8882], [-105, 6]], [[5369, 9164], [33, 7]], [[5410, 9185], [-61, 3]], [[5382, 9214], [24, -3]], [[5389, 9255], [-16, 3]], [[5414, 8573], [-31, 12]], [[4846, 8201], [22, -16]], [[0, 8115], [0, -461]], [[0, 8415], [0, -64]], [[9999, 9650], [0, -16]], [[9999, 7411], [0, -7]], [[9999, 7334], [0, -7135]], [[8932, 5406], [-14, 8]], [[8125, 5318], [20, 14]], [[9438, 6056], [15, -5]], [[8884, 7090], [-16, 8]], [[7720, 6005], [14, -15]], [[7733, 5985], [-15, 15]], [[6554, 7053], [4, 18]], [[4592, 8166], [-40, 23]], [[3375, 8103], [-29, 2]], [[7682, 0], [-856, 0]], [[6800, 0], [-6800, 0], [0, 9999], [9999, 0], [0, -298]], [[9999, 127], [0, -96]], [[9995, 0], [-30, 0]], [[9963, 0], [-47, 0]], [[7453, 4974], [1, 0]], [[7454, 4974], [-2, -4]], [[7763, 5071], [13, -4], [1, -2], [-1, -3], [-2, -4], [1, -6], [2, -7], [1, -6], [0, -4], [-3, -4], [-6, -4], [-5, -6], [-5, -8], [-7, -7], [-8, -5], [-8, -7], [-7, -7], [-15, -11]], [[7714, 4976], [-28, -18]], [[7686, 4958], [-4, -2], [-3, -2], [-7, -4], [-6, -5]], [[7666, 4945], [-41, -17]], [[7625, 4928], [-28, -14], [-17, -12], [-14, -14], [-7, -5], [-7, -1], [-12, 1], [-12, -2], [-12, -4], [-8, -4], [-4, -2], [-3, -1], [-4, 1], [-9, 7], [0, -1], [-2, -1], [-5, 2], [-7, 0], [-5, -3], [-5, 0], [-5, 2], [4, 2], [21, 5], [0, 1], [-8, 3], [-2, 3], [-2, 0], [-2, -3], [-2, -2], [-4, 1], [-4, 2], [-5, 5], [-13, 7], [-4, 4], [-4, 2], [-7, 1], [-1, 2]], [[7426, 4910], [1, 3]], [[7427, 4913], [6, 14], [3, 5], [2, 1], [2, 0], [1, 1], [-1, 3], [1, 2], [4, 5], [2, 2], [-1, 4], [1, 8], [3, 9], [3, 7]], [[7769, 5057], [2, 0], [3, 4], [1, 2], [0, 2], [-6, 1], [-2, -2], [0, -3], [1, -2], [1, -2]], [[7764, 5092], [-2, 5]], [[8015, 5234], [1, -1], [0, -3], [-1, -3], [-3, -4], [-31, -25], [-9, -10], [3, -5], [1, -2], [2, 0], [6, 5], [2, -1], [1, -1], [0, -3], [-3, -5], [2, -1], [5, 1], [3, 0], [-2, -4], [-3, -2], [-1, -3], [-4, -6], [-2, -1], [-1, 1], [0, 3], [-4, -3], [0, -3], [2, -2], [2, -4], [1, -5], [1, -10], [1, -2], [0, -1], [0, -1], [-1, -2], [1, -3], [0, -2], [-3, -3], [-4, -1], [-7, -1], [-7, -3], [-8, -6], [-6, -6], [-3, -5], [-2, -2], [-2, 0], [-11, -4], [-4, -3], [-1, -2], [-1, -1], [-1, 2], [-8, 1], [-15, 1], [-11, -1], [-8, -2], [-8, 1], [-6, 5], [-10, 4], [-26, 5], [-22, -1], [-18, -3], [-28, -9]], [[7978, 5177], [0, -3], [1, 1], [2, 3], [0, 1], [-2, 0], [-1, -2]], [[7993, 5211], [0, 1], [-3, 0], [-2, -2], [0, -1], [0, -1], [3, 1], [2, 2]], [[7453, 4974], [3, 5], [4, 3], [6, 3], [2, 2], [2, 6], [1, 9], [2, 6], [3, 2], [1, 3], [-1, 3], [1, 3], [4, 3], [4, 1], [3, -2], [1, -3], [0, -7], [0, -3]], [[7489, 5008], [-1, -2]], [[7455, 4976], [-1, -2]], [[7643, 3163], [-3, -7], [-3, -10], [-3, -5], [-3, -1], [-3, 2], [-2, 2], [-3, 3], [-3, 7], [-4, 5], [-3, 3], [-4, 4], [-1, 5], [2, 4], [5, 5], [4, 5], [6, 8], [5, 5], [3, 0], [4, -6], [2, -3], [1, -3], [1, -4], [1, -5], [2, -7], [-1, -7]], [[5232, 4823], [2, -4], [-1, -3], [-7, -5], [-3, -2], [-7, -13], [-6, -4], [-5, -4], [-3, 0], [-2, 6], [-3, 7], [-1, 7], [-3, 8], [-1, 0], [-1, -3], [-1, -4], [-2, -5], [-2, 1], [-4, 10], [-5, 6], [-3, 4], [-1, 5], [-2, 6], [-1, 7], [-2, 6], [-3, 5], [-3, 3], [-2, 5], [-4, 5], [-2, 6], [0, 8], [-1, 5], [1, 3], [0, 2], [-1, 1], [-1, 1], [1, 2], [3, 0], [4, 3], [5, 2], [4, 2], [2, -1], [2, 0], [2, -3], [1, -4], [-2, -5], [0, -3], [2, -5], [3, -3], [5, -4], [4, 0], [2, -1], [1, -5], [2, -9], [2, -7], [2, -3], [1, 0], [1, 0], [1, 0], [1, 1], [1, 2], [-1, 9], [-1, 7], [-1, 6], [1, 2], [2, 1], [3, -2], [3, -4], [4, -2], [5, 1], [6, 1], [2, -1], [0, -1], [-1, -5], [-3, -1], [-4, 0], [-5, 1], [-2, -2], [-2, -2], [-1, -4], [1, -1], [4, -6], [2, -3], [2, -4], [3, -3], [5, -8], [8, -8]], [[5217, 4808], [1, 10], [-3, 12], [-3, 1], [-2, -1], [1, -5], [0, -6], [3, -5], [3, -6]], [[7325, 5387], [-3, -2], [-5, -1], [-6, 1], [-3, -1], [2, -3], [-1, -4], [-6, -6], [-3, -6], [-1, -6], [1, -6], [3, -5], [3, -3], [5, -1], [1, -2], [-3, -2], [-5, -2], [-7, 0], [-7, -3], [-8, -4], [-4, -5], [-1, -4], [0, -5], [1, -14], [-1, -6], [-3, -9], [-4, -13], [-5, -8], [-1, 1], [1, 4], [4, 13], [0, 5], [0, 3], [-1, 0], [-2, -2], [-2, -6], [0, -2], [1, -1], [-1, -3], [-3, -7], [-2, -1], [-2, 3], [0, 6], [3, 10], [-1, 2], [2, 7], [1, 4], [-1, 5], [1, 5], [2, 4], [-1, 1], [-3, -2], [-6, -7], [-7, -14], [-6, -6], [-4, 2], [-3, -1], [-3, -4], [-2, -2], [-3, 0], [-2, -5], [-1, -10], [-3, -5], [-8, -3], [-2, -3], [0, -3], [1, -5], [0, -9], [-1, -14], [-3, -12], [-4, -11], [-5, -8], [-8, -11], [0, -3], [4, -8], [2, -7], [0, -13], [0, -2], [-8, -16], [0, -3], [9, -27], [7, -19], [1, -2], [2, -8], [4, -15], [2, -15], [0, -15], [-1, -15], [-2, -14], [-3, -13], [-4, -13], [-4, -10], [-9, -17], [-4, -11], [-5, -9], [-4, -7], [-8, -7]], [[7169, 4916], [-21, -14]], [[7148, 4902], [-7, -3], [-7, -1], [-8, 1], [-5, 1], [-1, 3], [-1, 0], [-3, 2], [-2, 3]], [[7114, 4908], [-4, 5]], [[7110, 4913], [-3, 11], [-4, 17], [-3, 10], [-3, 3], [-3, 5], [-2, 7], [-1, 15]], [[7091, 4981], [0, 24]], [[7091, 5005], [0, 16], [2, 8], [0, 7], [-3, 4], [-1, 6], [-1, 7], [-1, 4], [-2, 3], [-1, 3], [2, 4], [0, 3], [-2, 2], [0, 2], [1, 4], [-1, 9], [0, 6], [1, 6], [5, 11], [1, 6], [1, 6], [2, 7], [5, 14], [0, 4], [-2, 6], [0, 5], [0, 7], [2, 8], [3, 10], [5, 8], [5, 5], [2, 7], [-2, 7], [1, 11], [3, 15], [3, 11], [4, 7], [3, 7], [1, 6], [0, 3], [-3, 3], [-3, 5], [-5, 1], [-6, -3], [-3, 0], [-6, -7], [-5, -12], [-3, -4], [-5, -4], [-4, -3], [-2, -4], [-3, -1], [-4, 2], [-1, 6], [2, 8], [4, 9], [7, 14], [0, 3], [2, 4], [12, 6], [4, 4], [1, 6]], [[7106, 5303], [1, 9]], [[7107, 5312], [1, 3], [1, 1], [5, 7], [8, 13], [8, 15], [6, 14], [5, 8], [3, 2], [3, 4], [3, 14], [3, 6], [1, 2], [1, -1], [-1, -7], [0, -7], [1, -7], [3, -3], [4, 2], [4, 6], [3, 7], [3, 4], [4, -2], [3, 1], [3, 2], [5, 1], [1, -3], [-1, -6], [-2, -2], [-2, 0], [-3, -3], [-3, -7], [0, -5], [3, -4], [2, 0], [0, 3], [2, 4], [5, 6], [0, 2], [10, 6], [4, 3], [1, 4], [5, 9], [5, 2], [7, 2], [6, -1], [6, -3], [4, 0], [1, 3], [5, 2], [10, 1], [7, 4], [4, 6], [6, 4], [7, 0], [10, -2], [12, -5], [10, -7], [13, -14], [4, -1]], [[7326, 5395], [-1, -8]], [[7269, 5383], [-3, 2], [-3, -5], [-3, -9], [0, -6], [2, -1], [3, 0], [3, 3], [1, 5], [-1, 10], [1, 1]], [[7226, 5312], [-1, -1], [2, -5], [2, -2], [2, 0], [1, 2], [0, 3], [-2, 2], [-4, 1]], [[7159, 5342], [-1, 2], [-2, -7], [1, -3], [4, 0], [3, 1], [0, 5], [0, 2], [-2, 0], [-3, 0]], [[7149, 5329], [-3, -3], [-2, -6], [-4, -4], [-4, -2], [-3, -4], [-1, -7], [-3, -6], [-6, -9], [-1, -2], [0, -4], [2, -4], [2, -3], [3, -2], [3, 1], [7, 10], [0, 3], [-1, 1], [1, 4], [3, 6], [2, 4], [2, -1], [1, 2], [2, 3], [-1, 2], [-1, 3], [0, 1], [2, -1], [1, 3], [-1, 5], [1, 2], [2, -1], [2, 2], [0, 4], [-1, 2], [-4, 1]], [[7357, 5472], [-1, -1]], [[7356, 5471], [-2, -1], [-3, -1], [-3, -3], [-5, -3], [-5, 0], [-3, 5], [-5, 1], [-6, -3], [-6, 0], [-4, 3], [-10, 2], [-1, 2], [0, 2], [2, 2], [1, 6], [0, 9], [1, 6], [2, 4], [-3, 1], [-10, 0], [-11, -3], [-12, -6], [-13, -2], [-15, 2], [-11, 0], [-12, -3], [-2, 1], [-8, -4], [-13, -8], [-10, -7], [-8, -8], [-1, -1], [-2, 2], [-4, 3], [-5, 0], [-4, -2], [-5, 1], [-5, 6], [-4, 1], [-4, -2], [-7, -1], [-14, 1], [-2, 2], [-1, 6], [-2, 2], [-3, 2], [-4, 7], [-10, 16], [-3, 2], [-8, 6], [-7, 2], [-20, 3], [-4, 3], [-2, -1], [-13, -9], [-1, -2], [-1, -2], [-2, -3], [-1, -1], [-1, 1], [-1, 3], [0, 5], [1, 9], [-1, 6], [-3, 3], [-1, 3], [1, 4], [-3, 3], [-4, 2], [-2, 3], [0, 4], [-1, 3], [-1, 1], [-5, -3], [-9, -7], [-6, -6], [-3, -6], [-4, -3], [-7, -1], [-5, -2], [-8, -9], [-7, -4], [-11, -3], [-15, -2], [-13, -6], [-10, -8], [-12, -8], [-15, -6]], [[6896, 5482], [-10, -2]], [[6886, 5480], [-5, 2], [-6, 3], [-9, 7], [-2, 0], [1, -2], [0, -1], [-2, -2], [-12, -5], [-1, 2], [4, 7], [2, 5], [0, 5], [2, 6], [4, 6], [1, 5], [-1, 2], [-4, 4], [-4, 1], [-4, -2], [-14, -10], [-2, 1], [-4, 2], [-1, -1], [-11, -8], [-11, -6], [-26, -7], [-6, -1], [-5, 0], [-7, 5], [-3, 3]], [[6760, 5501], [1, 3], [3, 4], [32, 27], [20, 19], [33, 36], [8, 8], [19, 12], [31, 11], [15, 7], [14, 7], [8, 5], [4, 4], [3, 2], [2, 0], [2, 1]], [[6955, 5647], [2, -1]], [[7353, 5472], [4, 0]], [[6864, 5525], [-1, -2], [1, -2], [3, -2], [1, 1], [0, 2], [-2, 2], [-2, 1]], [[6872, 5509], [5, 3], [0, 1], [-2, 1], [-10, -7], [-3, -3], [-1, -2], [2, 0], [5, 1], [2, 2], [0, 2], [2, 2]], [[6880, 5519], [1, 4], [0, 1], [-3, 0], [-6, -3], [0, -1], [6, -1], [2, 0]], [[6891, 5534], [0, 2], [-1, 1], [-2, 0], [-1, -2], [-1, -4], [1, -1], [3, 2], [1, 2]], [[7006, 5639], [22, 12], [3, 4], [-1, 2], [2, 3], [7, 6], [-1, 1], [-7, -2], [-13, -6], [-30, -18], [-4, -3], [0, -1], [2, 0], [0, -2], [-2, -4], [0, -2], [3, -1], [6, 1], [9, 4], [3, 1], [0, 1], [-3, 1], [1, 2], [3, 1]], [[7092, 5577], [4, 3], [-4, 4], [-6, 2], [-9, 0], [-9, -2], [-16, -6], [-6, -3], [-13, -14], [-4, -7], [0, -5], [3, -3], [11, -3], [0, 2], [0, 1], [-2, 1], [3, 4], [1, -1], [0, -6], [-3, -3], [-2, -3], [0, -4], [2, -4], [3, 1], [4, 6], [9, 11], [0, 2], [1, 4], [16, 13], [3, 4], [0, 1], [-4, 2], [1, 1], [17, 2]], [[7506, 5060], [-2, 3], [-2, 5], [-4, 23], [-6, 42], [-5, 26], [-4, 10], [-6, 7], [-7, 6], [-5, 2], [-4, -1], [-6, -4], [-10, -4], [-5, -2], [-2, -4], [-3, -1], [-4, -1], [0, -1], [3, -1], [0, -3], [-8, -14], [-1, -4], [-5, -1], [-3, -3], [-4, -7], [-4, -2], [-5, 1], [-8, 4], [-6, 5], [-1, 4], [0, 5], [1, 6], [0, 5], [3, 11], [5, 3], [6, 1], [4, 2], [0, 2], [2, 2], [4, 1], [2, 6], [3, 12], [2, 7], [2, 1], [2, 0], [0, -2], [8, 8], [2, 5], [1, 6], [0, 15], [1, 11], [1, 4], [0, 12], [-2, 10], [-9, 11], [-1, 4], [0, 3], [2, 5], [4, 0], [6, -3], [1, 1], [-1, 2], [-2, 4], [0, 5], [-1, 3], [-1, 0], [-4, 7], [0, 3], [0, 3], [-1, 3], [-5, 4], [-9, 5], [-21, 9], [-4, 4], [-5, 2], [-7, 0], [-4, 3], [-2, 5], [-3, 4], [-4, 4], [-6, 3], [-9, 1], [-9, 4], [-10, 8], [-6, 2]], [[7326, 5395], [2, 0], [1, 1], [0, 1], [-1, 2], [1, 6], [-1, 1], [4, 9], [3, 2], [4, 0], [3, -2], [1, -3], [1, -1], [2, -1], [1, 0], [1, 0], [1, 2], [9, -1], [28, -4], [3, 0], [2, 2], [1, 2], [-2, 3], [-7, 4], [-2, 4], [-1, 3], [1, 2], [-1, 3], [-4, 2], [-10, 1], [-1, 2], [1, 2], [2, 3], [0, 5], [-1, 2], [-1, 7], [-2, 5], [-4, 7], [-1, 3], [-3, 2]], [[7357, 5472], [1, 0]], [[7420, 5414], [-4, 7], [-2, 2], [-6, 1], [-1, -2], [1, -2], [0, -2], [-2, -2], [-5, -3], [-3, -1], [-3, 1], [-1, 0], [-1, -3], [1, -1], [3, -3], [8, -1], [11, -1], [5, 2], [2, 4], [-1, 3], [-2, 1]], [[7371, 5449], [-2, 0], [0, -2], [1, -3], [4, -4], [1, 0], [0, 2], [0, 3], [-2, 2], [-2, 2]], [[7374, 5451], [0, 3], [-3, 5], [0, 6], [2, 5], [0, 4], [-3, 1], [-3, -1], [-4, -5], [3, -5], [2, -8], [1, -2], [2, -2], [3, -1]], [[7351, 5388], [-10, 2], [0, -2], [5, -6], [4, -2], [2, 0], [2, 1], [1, 3], [0, 2], [-3, 3], [-1, -1]], [[6560, 5748], [-8, 1], [-8, 6], [-6, 5], [-3, -1], [-11, -7], [-7, -1], [-3, 5], [0, 5], [1, 2], [10, 0]], [[6525, 5763], [1, 21], [5, 1], [8, 2], [4, 5], [-1, 10], [-5, 2], [0, 2], [5, -1]], [[8018, 5085], [2, -2], [-5, -3], [-13, -1], [-10, 7], [0, 2], [3, 1], [15, -2], [8, -2]], [[2002, 7031], [-11, -6], [-9, -4], [-11, -2], [-12, -5], [-2, -4], [0, -5], [-2, -5], [0, -4], [-2, -2], [-6, -1], [-17, -2], [-64, -11], [-11, 5], [-2, 9], [5, 10], [45, 14], [20, 5], [52, 11], [10, -2], [9, 0], [8, -1]], [[1849, 6808], [-5, -4], [0, -3], [2, -2], [1, -4], [-4, -7], [-7, -8], [-2, 0], [2, 6], [1, 7], [-3, 6], [-16, 5], [-19, 5], [-5, 1], [-12, 5], [-4, 8], [5, 8], [14, 2], [28, -9], [23, -12], [1, -4]], [[4619, 4591], [-1, -8], [-6, -2], [-3, 3], [-3, 11], [-2, 3], [1, 7], [5, 9], [6, 2], [2, 2], [2, 0], [1, -6], [-2, -21]], [[7051, 5184], [0, -15], [-4, -9], [-4, -7], [-3, 1], [-2, 15], [-2, 10], [3, 10], [5, 10], [5, 0], [3, -2], [-1, -13]], [[8203, 5294], [2, 0], [1, -6], [1, -5], [1, 0], [2, 4], [3, 7], [3, 0]], [[8216, 5294], [5, 0], [-4, -2], [-2, -4], [2, -4], [-1, -5], [-1, -20], [-1, -14], [-2, -3], [-2, -2], [0, -4], [2, -2], [0, -6], [0, -1], [-1, 0], [-2, -4], [-1, -9], [-3, -11], [-4, -10], [-2, -11], [3, -21], [1, -11], [-1, -3], [-2, 7], [-1, 6], [-2, 17], [-1, 3], [-1, 6], [1, 13], [7, 10], [-2, 13], [-2, 5], [-3, 24], [3, 16], [1, 19], [3, 8]], [[4952, 3914], [0, -6], [-6, -10], [-7, 1], [-10, 19], [-11, 15], [0, 8], [4, 2], [5, 0], [5, -8], [17, -15], [3, -6]], [[7918, 5016], [-4, -14], [-6, -11], [-1, 1], [2, 9], [2, 7], [3, 0], [2, 9], [2, -1]], [[7932, 4990], [-3, 5], [0, 15], [-3, 17], [-1, 17], [1, -1], [5, -22], [0, -17], [1, -11], [0, -3]], [[7959, 5000], [0, -2], [-5, 7], [-6, 7], [-8, 20], [1, 18], [2, 2], [2, -22], [3, -11], [5, -8], [6, -11]], [[6890, 3561], [-4, -9], [-5, -2], [-3, 2], [0, 5], [2, 5], [4, 8], [5, -3], [1, -6]], [[1804, 6795], [-5, -8], [-13, -15], [0, -4], [1, -3], [-2, -5], [-3, -3], [-7, 4], [-7, 4], [-2, 5], [3, 2], [4, 0], [4, 1], [4, 4], [1, 5], [9, 9], [2, 3], [8, 4], [3, -3]], [[1564, 7820], [0, -5], [-10, 0], [-11, 1], [-4, 3], [-11, 6], [1, 3], [15, -1], [15, -3], [5, -4]], [[5369, 5215], [-3, -1], [-3, -1], [-1, 3], [-3, 2], [-1, 3], [-4, 1], [0, 4], [-3, 3], [-4, -2], [-4, -2], [-2, 4], [2, 2], [3, 1], [3, 0], [3, 3], [3, 6], [4, 2], [4, -3], [1, -11], [5, -7], [0, -7]], [[2081, 8301], [-25, -6], [-30, -4], [-5, 2], [-5, -1], [-10, 3], [-8, 3], [-6, 7], [-6, 4], [-4, 1], [-2, 2], [7, 2], [10, 1], [5, -1], [3, 3], [9, 3], [16, 1], [46, -16], [5, -4]], [[5064, 5639], [-1, 10], [-2, 2], [1, 4], [4, 2], [7, -9], [2, -9], [1, -18], [-1, -3], [-3, -5], [-5, 0], [-2, 0], [-1, 3], [3, 5], [-2, 3], [-3, 2], [-5, 1], [0, 3], [4, 4], [3, 5]], [[5104, 4215], [-2, 1], [-5, 10], [-5, 10], [-7, 8], [-5, 6], [-3, 4], [-4, 0], [-5, -15], [-8, -3], [-6, 2], [-2, 6], [-4, 3], [-3, -1], [-3, -2], [-4, 3], [-11, 0], [-6, -9], [-2, -2]], [[5019, 4236], [-2, 1], [-3, 7], [0, 5], [12, 4], [11, 5], [6, 5], [2, 13], [2, 16], [1, 3], [3, 1], [0, -9], [-2, -13], [-1, -15], [6, -11], [5, -7], [5, 3], [5, 9], [5, 11], [3, 0], [5, -8], [5, -8], [7, -7], [6, -11], [5, -12], [-1, -3]], [[7902, 5037], [-4, -6], [-3, -9], [-1, 1], [1, 4], [2, 6], [3, 6], [1, 7], [2, -2], [-1, -7]], [[7962, 5031], [-3, 8], [-2, 7], [1, 3], [5, -16], [0, -2], [-1, 0]], [[7978, 5033], [-7, 8], [-5, 7], [0, 6], [5, -9], [6, -6], [1, -6]], [[1272, 7107], [-5, -3], [-2, -3], [-2, -3], [2, -8], [-6, -7], [-11, -5], [0, 1], [-2, 4], [1, 6], [1, 3], [-1, 1], [-4, 0], [-8, -1], [-8, -2], [0, 3], [2, 1], [9, 3], [4, 2], [-2, 3], [-5, 2], [4, 2], [2, 3], [1, 0], [4, -2], [5, 2], [1, 1], [5, -1], [12, -1], [3, -1]], [[5285, 4945], [-2, -10], [-1, -7], [-2, -2], [-2, 1], [-1, 6], [-1, 6], [0, 9], [1, 8], [4, 3], [2, -1], [2, -4], [0, -6], [0, -3]], [[5250, 4728], [-6, -4], [-2, -7], [-2, -3], [-1, -1], [-1, 2], [1, 4], [-1, 5], [-1, 2], [5, 9], [-1, 8], [-2, 7], [7, -1], [2, -4], [3, -6], [2, -6], [2, -1], [-1, -2], [-4, -2]], [[6596, 5551], [-1, -4], [-3, -7], [-4, -2], [-4, -1], [-3, 1], [-2, 4], [-4, 4], [-4, -3], [0, -5], [-1, -2], [0, 2], [-2, 5], [-1, 5], [1, 5], [1, 0], [7, 2], [5, 1], [5, 1], [6, 5], [1, -1], [-1, -6], [-1, -5], [2, 1], [3, 0]], [[4590, 4939], [-2, -7], [-2, -11], [-2, -5], [-3, -1], [-2, 3], [-1, 7], [-1, 14], [1, 9], [2, 3], [3, 2], [3, -2], [2, -3], [2, -3], [0, -6]], [[4659, 4694], [0, -2], [-1, -2], [-4, 1], [-4, 6], [-7, 12], [-5, 11], [1, 3], [4, 5], [4, 2], [4, -3], [1, -12], [3, -13], [2, -4], [2, -4]], [[4645, 4359], [1, -1], [-1, -2], [-2, -3], [-4, -1], [1, 4], [4, 2], [1, 1]], [[6651, 5440], [1, -4], [0, -4], [-2, -3], [-4, -2], [-6, 0], [-4, 3], [-4, 5], [-2, 6], [3, 6], [6, 5], [6, 2], [3, -1], [1, -2], [1, -4], [0, -4], [1, -3]], [[8361, 5133], [3, -3], [3, -3], [0, -2], [-1, -3], [-4, -1], [-7, 8], [-5, 3], [0, 3], [1, 3], [1, 2], [1, 2], [0, 2], [2, -1], [3, -2], [2, -4], [1, -4]], [[4716, 5092], [-3, 1], [-3, -4], [-4, -2], [-4, 2], [-3, 5], [-2, 6], [-1, 5], [1, 2], [11, 1], [10, -2], [2, -7], [2, -6], [-1, -2], [-2, 0], [-1, 1], [-2, 0]], [[1124, 7670], [1, -1], [-2, -3], [-6, -4], [-9, -4], [-9, -2], [-6, 1], [-8, 5], [0, 4], [3, 2], [3, 1], [12, -1], [16, 1], [5, 1]], [[1246, 7193], [1, -2], [0, -3], [-1, -3], [-1, -3], [-2, 0], [-1, 3], [-3, 4], [-3, 4], [0, 3], [3, 3], [5, 2], [5, 1], [10, 0], [6, 1], [-3, -2], [-3, -2], [-6, -3], [-7, -3]], [[1345, 7152], [1, -3], [-2, -1], [-4, -1], [-7, 2], [-6, 3], [-2, 2], [1, 2], [4, 1], [4, 0], [6, -2], [5, -3]], [[1626, 6964], [-1, -4], [-3, 0], [-2, 2], [-1, 3], [-1, 4], [-3, 6], [-4, 5], [8, -3], [6, -7], [1, -3], [0, -3]], [[1675, 7051], [-3, -1], [-9, 2], [-12, 2], [-13, -4], [-9, -2], [-12, -2], [-4, 0], [-5, 0], [-1, 2], [-1, 2], [-1, 2], [3, 1], [7, -1], [19, 4], [16, 4], [5, 0], [5, -4], [9, 0], [5, -2], [1, -3]], [[1603, 7081], [2, 3], [6, 3], [10, 3], [6, 1], [-2, -2], [3, -2], [4, -2], [1, -1], [0, -2], [-1, -2], [-10, 2], [-13, 1], [-5, -2], [-1, 0]], [[1638, 7007], [-9, -3], [-1, -3], [2, -3], [11, -3], [5, -2], [1, -3], [-4, 1], [-9, 1], [-4, 1], [-5, 4], [-9, 4], [-4, 4], [-3, 5], [-4, 7], [3, 0], [11, -8], [5, 1], [7, 1], [11, 0], [-4, -4]], [[6715, 5707], [9, -6]], [[6733, 5690], [1, -2], [-1, 0], [-1, 2], [-3, 3], [-7, 3], [-12, 1], [-12, 0], [-6, 2], [-4, 4], [-2, 3], [1, 1], [6, -3], [9, -3], [6, -1], [3, 3], [3, 3], [1, 1]], [[6715, 5707], [-9, 3], [-9, 3], [-2, 1], [1, 1], [-4, 1], [-6, 0], [-3, -1], [1, -2], [-1, -1], [-4, 0], [-2, 3], [-7, 3], [-4, -1], [-4, 2], [1, 0]], [[6712, 5709], [3, -2]], [[5523, 5600], [2, 1], [9, 0], [7, -1], [2, -2], [3, -6], [4, -1], [5, 5], [5, 5], [2, 4], [0, 3], [-2, 2], [8, -2], [12, -1], [2, 2], [4, 4], [3, 1], [3, 0], [5, 0], [9, 0], [10, -1], [4, 1], [0, 3], [0, 2], [4, 1], [5, 1], [4, 1], [2, 2], [2, 2], [4, 2], [5, 2], [1, 2], [1, 4], [0, 3], [-2, 3], [4, 2], [8, 6], [3, -1]], [[5661, 5649], [3, -1], [3, -3], [-1, -4], [-2, -3], [1, -6], [5, -5], [2, -2], [-1, -3], [1, -3], [1, -3], [1, -6], [1, -7], [0, -5], [-3, -4], [-1, -1], [1, 6], [0, 6], [-2, 6], [-3, 7], [-1, 3], [-2, 4], [-2, 4], [-3, 3], [-2, 0], [-2, -2], [-3, -4], [-3, -5], [0, -4], [-3, 0], [-7, -1], [-6, -2], [-4, -1], [-2, -3], [-1, -5], [-1, -2], [-1, 2], [-2, 3], [-3, -1], [-4, -2], [-1, 0], [-1, 2], [-12, 2], [-10, 0], [-2, 0], [-4, -3], [-3, -2], [-6, -2], [-8, 0], [-8, -7], [-10, -11], [-5, -9], [-1, 2], [1, 7], [-1, 5], [-1, 4], [-1, 1], [-3, 2], [-5, 1], [-9, 1], [-2, 2]], [[5863, 5650], [1, 5], [3, 4], [4, 4], [7, 2], [4, -6], [3, -3], [3, -2], [7, 1], [11, 9], [7, 3], [7, 1], [9, -1], [10, -3], [9, -5], [5, -6], [4, -10], [-2, -7], [0, -5], [0, -3], [4, -2], [5, -1], [4, -1], [2, 1], [-1, 7], [-2, 7], [4, 2], [4, 1], [3, 2], [2, 0], [-1, -2], [-1, -3], [-1, -1], [0, -2], [1, -6], [2, -8], [1, -3], [4, -2], [1, -2], [-3, -6], [4, -7], [5, -2], [6, -2], [10, -3], [13, 1], [7, 1], [2, 1], [1, 1], [10, 1], [14, 3], [6, 2], [3, -2], [2, -3], [-4, -2], [-5, -2], [-6, -4], [-5, -4]], [[6046, 5588], [-15, 1], [-7, -2], [-7, -1], [-9, -2], [-2, -1], [-3, 3], [-6, 5], [-5, 3], [-4, 2], [-7, 2], [-7, 0], [-6, -3], [-2, 0], [1, 3], [5, 4], [4, 4], [0, 6], [-2, 6], [-7, 1], [-11, 2], [-4, 4], [-3, 12], [3, 4], [0, 5], [-3, 5], [-8, 5], [-11, 5], [-12, 0], [-10, -3], [-4, -4], [-8, -4], [-9, -2], [-5, 2], [-2, 5], [-2, 3], [-7, -4], [-6, -5], [-2, 1]], [[4879, 5676], [3, 4], [2, 2], [5, 0], [6, -2], [4, -6], [6, -6], [1, -3], [-7, -2], [-8, -4], [-2, -4], [-1, -5], [0, -5], [-3, -2], [-3, -1], [-2, 1], [1, 3], [1, 6], [2, 8], [3, 6], [4, 3], [3, 2], [-1, 2], [-1, 3], [-3, 0], [-2, 1], [-5, -2], [-3, 1]], [[4614, 5629], [-3, 1], [-5, 2], [-4, 4], [-3, 6], [-10, 9], [-15, 15], [-7, 11], [0, 2], [7, -9], [16, -16], [12, -12], [8, -9], [4, -4]], [[2563, 7353], [1, 0], [1, -3], [1, -3], [2, -5], [6, -6], [5, -4], [-1, -2], [-3, -3], [-3, 1], [-7, 8], [-4, 6], [0, 4], [1, 4], [1, 3]], [[6540, 5650], [8, -3], [6, -4], [1, -4], [-1, -3], [-8, -2], [-17, -1], [-10, 3], [-2, 4], [1, 5], [5, 5], [8, 4], [6, 0], [3, -4]], [[6536, 5661], [4, 3], [10, 4], [12, 2], [6, 0], [4, -3], [0, -4], [-3, -4], [-5, -3], [-7, -2], [-11, 0], [-9, 1], [-2, 3], [1, 3]], [[6053, 3486], [2, 2], [5, -6], [2, -2], [3, -3], [3, -3], [3, -3], [3, 2], [2, 3], [1, -2], [1, 3], [2, 6], [1, 0], [-1, -5], [0, -4], [1, -3], [2, 0], [-1, -1], [-1, -1], [-3, -1], [-5, -2]], [[4679, 5763], [-8, 0]], [[4671, 5763], [-12, 0]], [[3844, 6579], [1, 3]], [[3845, 6582], [-1, 2]], [[3842, 6595], [-1, 0]], [[3814, 6602], [-3, 8]], [[3814, 6602], [0, 0]], [[3801, 6617], [-5, 2]], [[3796, 6619], [-1, 0]], [[3782, 6630], [-8, 2]], [[3762, 6635], [-7, 5]], [[3741, 6648], [-8, 5]], [[3705, 6657], [0, 3]], [[3704, 6670], [-3, 5]], [[3696, 6683], [-1, 1]], [[3695, 6684], [-5, 2]], [[3685, 6693], [2, 7]], [[3687, 6700], [1, 4]], [[3690, 6708], [-8, 2]], [[3690, 6708], [0, 0]], [[3670, 6722], [1, 2]], [[3665, 6743], [-7, 10]], [[3658, 6753], [-3, 4]], [[3643, 6775], [-4, 6]], [[3621, 6808], [-5, 7]], [[3610, 6824], [-4, 8]], [[3583, 6862], [0, 4]], [[3569, 6886], [-6, 7]], [[3563, 6893], [-5, 6]], [[3543, 6911], [-2, 1]], [[3541, 6912], [-2, 1]], [[3525, 6921], [-4, 2]], [[3513, 6934], [-3, 3]], [[3490, 6959], [-2, 2]], [[3482, 6966], [-6, 4]], [[3476, 6970], [-5, 1]], [[3467, 6973], [-1, 1]], [[3457, 6999], [1, 5]], [[3457, 7008], [0, 1]], [[3441, 7022], [-7, 5]], [[3408, 7027], [-1, -1]], [[3362, 6997], [-4, -3]], [[3358, 6994], [-8, 0]], [[3336, 6958], [-14, 0]], [[3322, 6958], [-8, -4]], [[3309, 6952], [-11, -7]], [[3298, 6945], [-5, -2]], [[3274, 6929], [0, 0]], [[3240, 6984], [-4, 4]], [[3214, 7006], [-8, 6]], [[3196, 7021], [-8, 6]], [[3166, 7051], [-2, 1]], [[3150, 7057], [-1, 1]], [[3829, 6473], [0, 0]], [[3833, 6486], [4, 9]], [[3843, 6509], [0, 1]], [[3837, 6532], [-1, 3]], [[3835, 6553], [0, 2]], [[3837, 6562], [2, 3]], [[3829, 6473], [-5, -7]], [[6526, 5793], [0, -5]], [[6252, 5763], [-7, 0]], [[6245, 5763], [-6, 0]], [[8672, 5317], [0, 0]], [[8670, 5315], [-1, -2]], [[8670, 5315], [0, 0]], [[8655, 5328], [1, 2]], [[8656, 5330], [1, 3]], [[8658, 5335], [1, 3]], [[8659, 5338], [-1, 1]], [[8654, 5349], [-1, 0]], [[8659, 5356], [0, 0]], [[8633, 5374], [-1, 1]], [[8630, 5378], [0, 1]], [[8630, 5378], [0, 0]], [[8630, 5381], [0, 3]], [[8631, 5387], [0, 1]], [[8631, 5396], [0, 1]], [[8631, 5396], [0, 0]], [[8632, 5402], [0, 1]], [[8632, 5403], [0, 1]], [[8631, 5416], [0, 0]], [[8630, 5456], [0, 14]], [[8630, 5470], [0, 10]], [[8630, 5495], [0, 8]], [[8629, 5539], [-1, 1]], [[8616, 5552], [-9, 9]], [[8586, 5568], [0, -2]], [[8586, 5566], [-5, -2]], [[8575, 5562], [-12, -3]], [[8563, 5559], [-11, -6]], [[8550, 5553], [-4, 0]], [[8550, 5553], [0, 0]], [[8097, 5294], [-3, -1]], [[8042, 5264], [-27, -30]], [[8042, 5264], [0, 0]], [[7764, 5091], [0, 0]], [[7488, 5006], [0, 0]], [[7496, 5016], [4, 12]], [[7500, 5028], [4, 25]], [[6953, 5647], [-13, 1]], [[6940, 5648], [-7, -1]], [[6928, 5647], [-5, 1]], [[6915, 5660], [-10, -1]], [[6883, 5659], [-7, 1]], [[6876, 5660], [-1, 0]], [[6858, 5670], [-3, 1]], [[6858, 5670], [0, 0]], [[6843, 5670], [-1, -1]], [[6843, 5670], [0, 0]], [[6816, 5653], [-3, 0]], [[6816, 5653], [0, 0]], [[6772, 5678], [-4, 4]], [[6768, 5682], [-7, 2]], [[6751, 5686], [-5, -1]], [[6737, 5679], [-1, 2]], [[6730, 5698], [-3, 1]], [[6730, 5698], [0, 0]], [[6717, 5706], [-5, 3]], [[6712, 5709], [-7, 4]], [[6701, 5715], [-9, 3]], [[6692, 5718], [-2, 1]], [[6670, 5720], [-7, -1]], [[6656, 5713], [-6, -3]], [[6648, 5710], [0, 0]], [[6627, 5717], [0, 0]], [[6621, 5720], [-11, 4]], [[6610, 5724], [-16, 3]], [[6560, 5742], [0, 0]], [[6552, 5768], [-2, 10]], [[6549, 5795], [0, 1]], [[6547, 5802], [-1, 1]], [[6547, 5802], [0, 0]], [[6541, 5805], [-15, 3]], [[6541, 5805], [0, 0]], [[8533, 5566], [0, 3]], [[8533, 5579], [-7, 2]], [[8526, 5581], [-5, 2]], [[8517, 5580], [-3, -4]], [[8514, 5576], [-2, -3]], [[8482, 5528], [-10, -16]], [[8470, 5509], [-10, -14]], [[8458, 5478], [-1, -5]], [[8455, 5462], [-8, -11]], [[8439, 5429], [-1, -5]], [[8437, 5412], [0, -3]], [[8439, 5404], [-1, -3]], [[8435, 5396], [-1, -2]], [[8406, 5352], [1, -4]], [[8407, 5347], [0, 0]], [[8403, 5342], [0, 0]], [[8394, 5325], [-2, 0]], [[8394, 5325], [0, 0]], [[8391, 5325], [-2, 3]], [[8379, 5330], [-2, -2]], [[8377, 5328], [-4, -3]], [[8368, 5324], [-4, 2]], [[8356, 5324], [-4, -7]], [[8312, 5294], [-16, 0]], [[8210, 5294], [-7, 0]], [[8203, 5294], [-19, 0]], [[8184, 5294], [-24, 0]], [[8184, 5294], [26, 0]], [[8145, 5294], [-25, 0]], [[8120, 5294], [-10, 0]], [[5468, 5763], [-10, 0]], [[5458, 5763], [-13, 0]], [[3149, 7058], [-10, 10]], [[3144, 7098], [-9, 0]], [[3135, 7098], [-8, 0]], [[3090, 7088], [0, 0]], [[3078, 7080], [-17, 6]], [[3061, 7086], [-4, 2]], [[3041, 7092], [-5, -8]], [[3018, 7089], [-16, 4]], [[3166, 7051], [10, -4]], [[3182, 7032], [6, -5]], [[3214, 7006], [2, -2]], [[3230, 6993], [6, -5]], [[3286, 6939], [7, 4]], [[3309, 6952], [3, 1]], [[3312, 6953], [2, 1]], [[3336, 6958], [4, 0]], [[3348, 6994], [2, 0]], [[3362, 6997], [1, 0]], [[3408, 7027], [17, 7]], [[3433, 7028], [1, -1]], [[3457, 7008], [1, -3]], [[3458, 7005], [0, -1]], [[3467, 6973], [1, -1]], [[3468, 6972], [3, -1]], [[3482, 6966], [4, -2]], [[3486, 6964], [2, -3]], [[3513, 6934], [0, -1]], [[3525, 6921], [8, -5]], [[3533, 6916], [6, -3]], [[3543, 6911], [9, -5]], [[3552, 6906], [6, -7]], [[3569, 6886], [4, -4]], [[3584, 6871], [-1, -5]], [[3610, 6824], [5, -7]], [[3615, 6817], [1, -2]], [[3643, 6775], [7, -11]], [[3650, 6764], [5, -7]], [[3665, 6743], [4, -5]], [[3674, 6729], [-3, -5]], [[3680, 6711], [2, -1]], [[3690, 6708], [-2, -4]], [[3685, 6693], [-1, -5]], [[3684, 6688], [6, -2]], [[3696, 6683], [6, -2]], [[3704, 6670], [1, -2]], [[3705, 6668], [0, -8]], [[3741, 6648], [2, -1]], [[3754, 6640], [1, 0]], [[3782, 6630], [7, -2]], [[3789, 6628], [6, -9]], [[3801, 6617], [8, -2]], [[3809, 6615], [2, -5]], [[3842, 6595], [2, -6]], [[3844, 6589], [0, -5]], [[3844, 6579], [0, -5]], [[3840, 6568], [-1, -3]], [[3835, 6553], [-1, -6]], [[3835, 6539], [1, -4]], [[3843, 6509], [0, -4]], [[3843, 6505], [-6, -10]], [[3824, 6466], [-2, -2]], [[3798, 6445], [-5, 1], [-13, 1]], [[3780, 6447], [0, 2], [-6, 15]], [[3774, 6464], [-2, 6], [-2, 7]], [[3765, 6491], [2, 2], [3, 6]], [[3771, 6565], [-10, 15], [-2, 3]], [[3763, 6593], [2, 2], [1, 3]], [[3766, 6598], [-6, -3], [-13, -6]], [[3747, 6589], [-16, -5], [-11, -4]], [[3720, 6580], [-6, -4], [-5, -3]], [[3707, 6562], [0, -1], [0, -1]], [[3707, 6560], [-1, -1], [-1, -3]], [[3696, 6535], [-2, -2], [-1, 0]], [[3680, 6540], [-3, 9], [-2, 6]], [[3691, 6579], [6, 11], [7, 16]], [[3712, 6607], [7, 2], [7, 3]], [[3704, 6615], [-1, 0], [-2, 1]], [[3698, 6620], [-1, 1], [-2, 5]], [[3668, 6649], [-1, 4], [-1, 0]], [[3666, 6653], [0, 5], [0, 1]], [[3666, 6659], [-1, 1], [-1, 1]], [[3664, 6661], [-3, 1], [-2, 0]], [[3659, 6662], [-3, 2], [-1, 0]], [[3643, 6681], [-3, 2], [-2, 1]], [[3638, 6684], [-3, 4], [-5, 5]], [[3630, 6693], [0, 1], [-2, 3]], [[3629, 6712], [-4, -1], [-3, 0]], [[3565, 6781], [1, 1], [1, 3]], [[3569, 6787], [1, 0], [2, 0]], [[3572, 6787], [11, -5], [6, -3]], [[3589, 6779], [2, -1], [13, -7]], [[3606, 6772], [-19, 15], [-5, 4]], [[3582, 6791], [-3, 2], [-4, 3]], [[3574, 6803], [0, 1], [0, 3]], [[3576, 6809], [9, -1], [14, -1]], [[3577, 6814], [-1, 0], [-4, 0]], [[3572, 6814], [-4, -6], [-1, -2]], [[3564, 6804], [-3, 0], [-2, 1]], [[3552, 6815], [-3, 3], [-3, 4]], [[3546, 6822], [-7, 6], [-3, 3]], [[3534, 6838], [-1, 7], [0, 3]], [[3533, 6848], [1, 2], [1, 7]], [[3543, 6878], [2, 2], [2, 2]], [[3513, 6858], [-7, 4], [-5, 4]], [[3501, 6866], [-9, 1], [-5, 1]], [[3478, 6876], [-6, 11], [-6, 12]], [[3455, 6917], [-1, 1], [-3, 4]], [[3451, 6922], [-5, 22], [-2, 5]], [[3430, 6965], [-1, 0], [-1, 0]], [[3423, 6964], [7, -10], [1, -3]], [[3431, 6951], [2, -8], [2, -13]], [[3441, 6914], [3, -8], [1, -4]], [[3445, 6902], [1, -5], [1, -5]], [[3447, 6892], [1, -3], [2, -6]], [[3438, 6853], [-2, 2], [-3, 3]], [[3433, 6858], [-4, 6], [-2, 3]], [[3427, 6867], [-4, 2], [-6, 2]], [[3394, 6869], [-1, 1], [-1, 0]], [[3392, 6870], [0, 5], [0, 2]], [[3395, 6891], [-1, 2], [-1, 3]], [[3381, 6919], [3, 2], [14, 7]], [[3398, 6928], [-3, 1], [-5, 0]], [[3390, 6929], [-6, -3], [-1, 0]], [[3383, 6926], [-1, 0], [-2, 2]], [[3380, 6928], [-2, 6], [-1, 6]], [[3377, 6940], [-2, 3], [-1, 2]], [[3373, 6946], [-1, -7], [0, -5]], [[3373, 6916], [-1, -1], [-2, -2]], [[3370, 6913], [-2, 0], [-1, 0]], [[3361, 6915], [-4, 4], [-2, 1]], [[3355, 6920], [-2, 1], [-3, 1]], [[3348, 6924], [-1, 2], [-2, 4]], [[3345, 6930], [-3, 2], [-1, 2]], [[3320, 6939], [-6, 3], [-6, 3]], [[3311, 6937], [0, -1], [0, -3]], [[3311, 6933], [-2, -1], [-1, 0]], [[3308, 6932], [-1, -2], [-4, -4]], [[3317, 6927], [5, -2], [5, -2]], [[3327, 6923], [9, -4], [1, -1]], [[3337, 6918], [1, 0], [3, -3]], [[3341, 6915], [0, -2], [1, -2]], [[3343, 6910], [1, -1], [9, -4]], [[3353, 6903], [-5, -7], [-1, 0]], [[3347, 6896], [3, 0], [10, 0]], [[3367, 6894], [5, -7], [4, -5]], [[3338, 6852], [-2, 0], [-5, 2]], [[3331, 6854], [-7, 4], [-6, 4]], [[3265, 6892], [-1, 2], [0, 2]], [[3261, 6899], [-2, 1], [-2, 0]], [[3257, 6900], [-4, 3], [-3, 3]], [[3250, 6906], [-2, 2], [-7, 7]], [[3241, 6915], [-4, 5], [-1, 2]], [[3236, 6922], [-2, 5], [-1, 1]], [[3233, 6928], [-3, 4], [-2, 2]], [[3212, 6946], [-8, 4], [-1, 1]], [[3196, 6953], [-5, 1], [-2, 0]], [[3162, 6968], [-15, 7], [-20, 10]], [[3127, 6985], [-9, 5], [-9, 5]], [[3109, 6995], [-7, 3], [-3, 2]], [[3095, 7007], [3, 1], [4, 2]], [[3102, 7010], [2, 1], [2, 1]], [[3115, 7026], [-3, 4], [-1, 3]], [[3109, 7044], [0, 2], [1, 2]], [[3111, 7050], [2, 2], [1, 1]], [[3114, 7053], [2, 0], [0, 1]], [[3134, 7028], [0, 2], [1, 2]], [[3135, 7032], [1, 3], [1, 2]], [[3135, 7046], [-4, 4], [-8, 8]], [[3123, 7058], [-2, 1], [-1, 1]], [[3120, 7060], [-1, 0], [-5, 0]], [[3114, 7060], [-7, -5], [-1, 0]], [[3088, 7038], [-2, -1], [-3, -2]], [[3083, 7035], [-4, -1], [-19, -8]], [[2974, 7043], [-1, 1], [-5, 3]], [[2968, 7047], [3, 2], [6, 7]], [[2977, 7056], [0, 1], [0, 2]], [[2974, 7068], [-1, 1], [-1, 2]], [[2967, 7071], [0, -2], [0, -1]], [[2968, 7063], [-2, -2], [-1, -1]], [[2948, 7055], [-10, 2], [-23, 5]], [[2915, 7062], [-17, 3], [-17, 3]], [[2881, 7068], [-10, 1], [-21, 1]], [[2850, 7070], [-26, -3], [-17, -2]], [[2758, 7060], [-1, 1], [0, 3]], [[2757, 7064], [5, 1], [0, 1]], [[2755, 7076], [-7, 2], [-5, 3]], [[2743, 7081], [-7, 0], [-8, 1]], [[2728, 7082], [-5, 2], [-3, 1], [-3, 2]], [[2706, 7121], [2, 5], [0, 1]], [[2708, 7127], [2, 1], [6, 9]], [[2703, 7129], [-9, -7], [-1, -1]], [[2693, 7121], [-1, -1], [-7, -9]], [[2673, 7103], [-6, 0], [-4, 1]], [[2649, 7110], [-11, 3], [-1, 0]], [[2630, 7120], [0, 1], [4, 3]], [[2639, 7131], [0, 1], [1, 3]], [[2602, 7142], [0, 1], [-1, 2]], [[2588, 7145], [-2, -1], [-10, -3]], [[2566, 7175], [3, 6], [0, 1]], [[2570, 7191], [-7, -4], [-3, -2]], [[2560, 7185], [-3, -2], [-9, -7]], [[2540, 7173], [-3, 1], [-1, 1]], [[2530, 7176], [-1, 0], [-6, -1]], [[2519, 7173], [-3, -3], [0, -1]], [[2508, 7166], [-2, 2], [-1, 2]], [[2505, 7170], [-3, 1], [-4, 1]], [[2461, 7172], [2, 4], [1, 2]], [[2480, 7202], [0, -1], [-1, 0]], [[2479, 7201], [-2, -1], [-4, -2]], [[2473, 7198], [-8, -8], [-3, -2]], [[2457, 7185], [-5, 0], [-3, 0]], [[2445, 7186], [-3, 0], [-1, -1]], [[2431, 7177], [1, 0], [7, 3]], [[2435, 7159], [-2, -2], [-2, -1]], [[2425, 7156], [-3, 0], [-3, -1]], [[2419, 7155], [-1, -1], [0, -1]], [[2431, 7144], [2, 0], [2, -1]], [[2435, 7143], [3, -1], [3, -2]], [[2441, 7140], [0, -1], [1, -1]], [[2438, 7127], [-1, -1], [-2, -1]], [[2435, 7125], [-9, 0], [-1, 0]], [[2415, 7120], [-1, -2], [-2, -2]], [[2427, 7120], [7, 1], [4, 0]], [[2447, 7122], [0, 1], [5, 3]], [[2462, 7111], [-1, -1], [-3, -1]], [[2458, 7109], [-5, -2], [-2, 0]], [[2451, 7107], [-2, -2], [-2, -1]], [[2444, 7090], [1, -6], [1, -6]], [[2445, 7076], [-1, 0], [-2, -1]], [[2439, 7075], [-3, -2], [0, -1]], [[2436, 7072], [-1, 0], [-7, -15]], [[2428, 7057], [-1, -1], [-1, -1]], [[2404, 7052], [-6, 0], [-2, 0]], [[2396, 7052], [-11, 3], [-1, 1]], [[2379, 7058], [-1, 1], [-3, 3]], [[2375, 7062], [-6, -2], [-5, -2]], [[2354, 7071], [-1, -1], [-1, 0]], [[2352, 7070], [0, -7], [-1, -5]], [[2351, 7058], [-1, -1], [-1, -3]], [[2342, 7046], [-3, -14], [-1, -1]], [[2324, 7029], [0, -1], [-1, -1]], [[2310, 7033], [-2, 0], [-1, 0]], [[2307, 7033], [0, -1], [-1, 0]], [[2311, 7024], [-1, 0], [0, -2]], [[2292, 7007], [-1, 0], [-3, 1]], [[2285, 7009], [-2, 0], [-2, 0]], [[2263, 7007], [-1, 0], [-1, 0]], [[2260, 7003], [0, -4], [0, -2]], [[2260, 6997], [-2, -3], [-3, -3]], [[2242, 6981], [-1, -1], [-3, -4]], [[2215, 6975], [0, -1], [3, -3]], [[2218, 6971], [0, -2], [-1, -1]], [[2217, 6968], [-1, 0], [-3, -1]], [[2213, 6967], [-1, 1], [-4, 0]], [[2183, 6963], [-7, 0], [-2, 0]], [[2174, 6963], [-11, 7], [-2, 1]], [[2158, 6972], [-1, 0], [-1, 2]], [[2156, 6974], [1, 1], [2, 6]], [[2165, 6988], [3, 2], [9, 5]], [[2191, 6997], [8, 4], [1, 0]], [[2200, 7001], [1, 1], [9, 7]], [[2216, 7015], [1, 2], [10, 14]], [[2226, 7032], [-1, 1], [-1, 0]], [[2224, 7033], [-17, -10], [-8, -5]], [[2165, 7032], [2, 6], [3, 11]], [[2170, 7049], [3, 5], [1, 3]], [[2174, 7057], [5, 6], [4, 6]], [[2196, 7082], [2, 4], [2, 4]], [[2206, 7123], [0, 2], [-3, 11]], [[2203, 7143], [2, 2], [1, 1]], [[2234, 7157], [4, 2], [10, 7]], [[2248, 7166], [6, 3], [20, 10]], [[2281, 7178], [3, -3], [2, -1]], [[2299, 7168], [2, 0], [7, 0]], [[2308, 7168], [4, 1], [7, 2]], [[2319, 7171], [8, 0], [9, -1]], [[2336, 7170], [24, -6], [11, -2]], [[2371, 7162], [4, -1], [4, 0]], [[2373, 7168], [-13, 2], [-11, 2]], [[2349, 7172], [-3, 1], [-7, 2]], [[2339, 7175], [-19, 10], [-10, 5]], [[2303, 7196], [1, 0], [2, 3]], [[2306, 7199], [2, 0], [5, 2]], [[2317, 7207], [3, 4], [1, 1]], [[2321, 7212], [5, 5], [2, 1]], [[2328, 7218], [1, 1], [10, 6]], [[2339, 7225], [6, 3], [14, 6]], [[2351, 7235], [-6, -1], [-9, -1]], [[2336, 7233], [-3, -1], [-2, -1]], [[2331, 7231], [-4, -2], [-6, -5]], [[2321, 7224], [-3, -4], [-1, 0]], [[2317, 7220], [-5, -8], [0, -2]], [[2309, 7209], [-5, -1], [-5, -1]], [[2299, 7207], [-11, 0], [-17, -1]], [[2271, 7206], [-3, 3], [-2, 2]], [[2260, 7211], [-10, -5], [-15, -7]], [[2235, 7199], [-5, -3], [-4, -3]], [[2226, 7193], [-6, -6], [-1, -1]], [[2219, 7186], [-5, -3], [-5, -2]], [[2209, 7181], [-9, -3], [-5, -1]], [[2195, 7177], [-6, -2], [-4, -2]], [[2185, 7173], [-1, 0], [-10, -8]], [[2174, 7165], [-3, -5], [-1, -1]], [[2170, 7156], [2, -5], [1, -4]], [[2164, 7144], [-4, -2], [-6, -4]], [[2130, 7111], [3, -4], [1, -1]], [[2125, 7098], [-6, -5], [-7, -4]], [[2112, 7089], [-4, -1], [-5, -2]], [[2092, 7087], [-8, 5], [-1, 1]], [[2085, 7086], [4, -3], [7, -4]], [[2096, 7079], [2, -2], [5, -4]], [[2103, 7058], [-1, -1], [-7, -8]], [[2095, 7049], [-6, -3], [-1, 0]], [[2069, 7043], [-1, 0], [-6, -2]], [[2060, 7040], [8, -3], [6, -1]], [[2074, 7036], [0, -1], [1, -1]], [[2059, 7019], [-6, 0], [-4, -1]], [[2047, 7019], [1, 2], [1, 4]], [[2049, 7026], [-3, 1], [-1, 1]], [[2045, 7028], [-3, -1], [-2, -2]], [[2040, 7025], [-5, -3], [-8, -5]], [[2027, 7017], [-1, -1], [-1, -1]], [[2022, 7005], [-3, -4], [0, -1]], [[2014, 6996], [-5, -3], [-16, -10]], [[1993, 6983], [1, -3], [1, 0]], [[1995, 6980], [-5, -9], [-1, -2]], [[1989, 6969], [-1, -3], [-2, -7]], [[1986, 6959], [3, -3], [1, -1]], [[1990, 6955], [7, -2], [11, -3]], [[2045, 6936], [1, -2], [5, -4]], [[2051, 6930], [0, -1], [1, -2]], [[2052, 6927], [-1, -2], [0, -1]], [[2051, 6924], [0, -1], [-2, -3]], [[2049, 6920], [-4, -5], [-2, -3]], [[2043, 6912], [-5, -5], [-9, -7]], [[2023, 6897], [-5, -2], [-4, -1]], [[2010, 6892], [0, -1], [-12, -10]], [[1978, 6857], [2, 0], [4, 0]], [[1982, 6850], [-1, -6], [0, -2]], [[1978, 6840], [-4, 0], [-6, 0]], [[1956, 6837], [0, -1], [-1, 0]], [[1955, 6829], [-16, -2], [-17, -2]], [[1891, 6797], [-5, -2], [-4, -1]], [[1877, 6792], [0, -3], [0, -1]], [[1875, 6784], [-6, -3], [-2, -2]], [[1867, 6779], [-3, -5], [-1, -2]], [[1863, 6772], [-2, -1], [-1, 0]], [[1843, 6767], [-1, -4], [0, -5]], [[1834, 6760], [-3, -1], [-4, -1]], [[1809, 6744], [0, -1], [1, -1]], [[1810, 6742], [1, -2], [1, 0]], [[1812, 6740], [1, -2], [2, -4]], [[1815, 6734], [0, -4], [0, -1]], [[1800, 6713], [-2, -5], [0, -2]], [[1798, 6706], [-5, 1], [-2, 0]], [[1791, 6707], [-3, -1], [-2, 0]], [[1786, 6706], [0, -1], [-3, -3]], [[1770, 6695], [-1, -1], [-5, -5]], [[1758, 6685], [-3, -1], [-2, -1]], [[1753, 6683], [-3, 0], [-2, 0]], [[1748, 6683], [-1, 0], [-4, 1]], [[1743, 6684], [-3, -1], [-1, 0]], [[1731, 6677], [0, -3], [-3, -7]], [[1728, 6667], [-1, -1], [-2, -4]], [[1725, 6662], [-2, 0], [-1, -1]], [[1694, 6648], [-4, 0], [-3, 0]], [[1687, 6648], [-1, 0], [-3, -1]], [[1683, 6647], [-3, -2], [-2, -1]], [[1651, 6627], [-1, -2], [0, -1]], [[1666, 6621], [1, -2], [1, -2]], [[1671, 6611], [-8, -7], [-4, -3]], [[1656, 6596], [-1, 0], [-2, -1]], [[1652, 6596], [-1, 7], [-1, 4]], [[1649, 6609], [-1, 0], [-1, 0]], [[1582, 6569], [-3, -2], [-4, -2]], [[1575, 6565], [-1, -5], [0, -2]], [[1574, 6558], [-1, -2], [-1, -4]], [[1572, 6552], [-1, -2], [-3, -3]], [[1568, 6547], [-2, -2], [-2, -1]], [[1541, 6563], [-3, -2], [-3, -1]], [[1520, 6548], [-8, -3], [-2, 0]], [[1510, 6545], [-2, -2], [-1, -1]], [[1496, 6533], [-1, 0], [-5, 2]], [[1486, 6534], [-3, -5], [-1, 0]], [[1479, 6527], [-1, 0], [-9, 3]], [[1465, 6528], [-4, -4], [-1, -2]], [[1432, 6514], [-3, 0], [-3, 1]], [[1424, 6517], [1, 4], [0, 4]], [[1425, 6525], [0, 1], [2, 4]], [[1433, 6535], [1, 0], [3, 1]], [[1415, 6540], [0, -1], [-4, -3]], [[1404, 6516], [-6, -8], [-12, -15]], [[1368, 6484], [-1, -1], [-4, -4]], [[1359, 6476], [-3, 0], [-1, 0]], [[1349, 6479], [1, 1], [0, 1]], [[1353, 6483], [-1, 1], [0, 3]], [[1352, 6487], [-1, 2], [-5, 5]], [[1336, 6499], [-1, -2], [0, -1]], [[1333, 6470], [-3, -2], [-9, -4]], [[1308, 6475], [-9, 2], [-1, 1]], [[1298, 6474], [1, -4], [1, -3]], [[1291, 6454], [-1, -1], [-5, -2]], [[1285, 6451], [-1, 0], [-2, 0]], [[1282, 6455], [0, 3], [3, 6]], [[1284, 6481], [1, 1], [1, 2]], [[1286, 6484], [3, 2], [10, 7]], [[1304, 6494], [1, -1], [2, -1]], [[1315, 6493], [2, 2], [0, 1]], [[1317, 6496], [1, 3], [1, 1]], [[1319, 6500], [2, 2], [3, 3]], [[1345, 6523], [7, 9], [6, 8]], [[1389, 6567], [13, 7], [6, 3]], [[1445, 6590], [2, 0], [1, -1]], [[1444, 6585], [1, -1], [2, -2]], [[1450, 6582], [7, 1], [7, 1]], [[1469, 6581], [-5, -2], [-3, -2]], [[1461, 6574], [4, -5], [8, -8]], [[1473, 6561], [2, -1], [2, -1]], [[1477, 6559], [2, 0], [1, 0]], [[1481, 6561], [0, 4], [-1, 5]], [[1505, 6566], [2, -1], [5, -1]], [[1512, 6564], [3, 0], [1, 1]], [[1497, 6588], [0, 2], [2, 5]], [[1535, 6639], [14, 10], [6, 5]], [[1593, 6669], [5, 4], [5, 5]], [[1603, 6678], [6, 5], [7, 6]], [[1622, 6687], [1, -2], [0, -1]], [[1640, 6705], [1, 2], [0, 3]], [[1656, 6729], [7, 7], [4, 4]], [[1675, 6746], [4, 1], [3, 1]], [[1682, 6748], [4, 4], [3, 1]], [[1704, 6767], [3, 1], [5, 2]], [[1715, 6769], [1, -2], [1, -1]], [[1728, 6762], [5, 3], [1, 0]], [[1734, 6765], [-1, 0], [0, 1]], [[1733, 6766], [-3, 1], [-2, 0]], [[1725, 6769], [-1, 2], [-2, 5]], [[1715, 6785], [1, 4], [0, 4]], [[1716, 6793], [2, 7], [3, 11]], [[1722, 6829], [3, 6], [1, 4]], [[1735, 6843], [11, 2], [8, 1]], [[1754, 6846], [-6, 2], [-5, 2]], [[1739, 6850], [-2, 1], [-6, 2]], [[1729, 6864], [0, 4], [0, 4]], [[1751, 6898], [10, 6], [10, 5]], [[1771, 6909], [-1, 1], [-2, 0]], [[1766, 6914], [5, 12], [4, 10]], [[1784, 6956], [-3, -4], [-9, -13]], [[1772, 6939], [-12, -11], [-2, -2]], [[1690, 6898], [-5, -1], [-8, -2]], [[1662, 6911], [-3, 3], [-1, 1]], [[1658, 6915], [-4, 8], [-1, 0]], [[1653, 6923], [2, 8], [1, 2]], [[1668, 6942], [5, -2], [5, -2]], [[1678, 6938], [5, 0], [3, 0]], [[1647, 6939], [-1, -1], [-6, -6]], [[1640, 6932], [-3, 1], [-3, 2]], [[1634, 6935], [-3, 2], [-1, 0]], [[1631, 6875], [-5, -2], [-7, -3]], [[1619, 6870], [-1, 1], [-9, 7]], [[1564, 6930], [-5, -2], [-1, 0]], [[1544, 6919], [0, 1], [-17, 9]], [[1510, 6947], [-1, -1], [-11, -5]], [[1488, 6935], [-6, -4], [-7, -6]], [[1467, 6925], [-13, -4], [-9, -4]], [[1439, 6912], [-2, -1], [-1, -1]], [[1436, 6910], [-1, -5], [-1, -3]], [[1403, 6895], [-16, 2], [-14, 2]], [[1396, 6907], [3, 3], [7, 6]], [[1401, 6928], [0, 4], [0, 3]], [[1401, 6942], [5, 6], [6, 5]], [[1412, 6953], [-9, 0], [-3, 0]], [[1400, 6953], [-5, -2], [-2, -2]], [[1386, 6958], [-2, 8], [-1, 8]], [[1397, 7001], [0, 2], [1, 7]], [[1374, 7056], [-2, 5], [-6, 18]], [[1352, 7092], [1, 2], [9, 18]], [[1374, 7130], [8, 5], [7, 4]], [[1379, 7140], [-3, -1], [-2, 0]], [[1364, 7128], [-8, -11], [-8, -12]], [[1332, 7090], [5, -3], [5, -2]], [[1344, 7082], [2, -8], [0, -1]], [[1343, 7063], [-1, -4], [-1, -2]], [[1328, 7057], [-1, -1], [-10, -7]], [[1317, 7049], [-12, -4], [-14, -5]], [[1291, 7040], [-17, -2], [-19, -3]], [[1238, 7035], [-3, 2], [-15, 9]], [[1220, 7052], [0, 1], [1, 4]], [[1180, 7094], [-6, 0], [-5, 0]], [[1169, 7094], [-3, 2], [-6, 3]], [[1160, 7099], [-6, 4], [-5, 4]], [[1150, 7113], [1, 3], [1, 1]], [[1152, 7117], [-6, 2], [-1, 1]], [[1136, 7120], [-7, 1], [-2, 1]], [[1127, 7122], [9, 8], [17, 15], [8, 16]], [[1178, 7161], [2, -1], [11, -6]], [[1191, 7154], [7, -1], [4, -1]], [[1202, 7152], [1, -1], [3, -2]], [[1206, 7149], [2, -3], [2, -3]], [[1210, 7143], [-1, -3], [-3, -6]], [[1206, 7134], [-3, -4], [-1, -2]], [[1202, 7128], [1, 0], [4, 1]], [[1231, 7148], [3, 0], [2, -1]], [[1244, 7137], [3, -4], [4, -5]], [[1251, 7128], [9, 4], [7, 4]], [[1267, 7136], [1, 2], [7, 9]], [[1275, 7147], [-5, 4], [-2, 1]], [[1260, 7155], [-16, 3], [-6, 1]], [[1238, 7159], [4, 2], [1, 1]], [[1243, 7162], [2, 0], [12, -1]], [[1262, 7164], [-1, 1], [-4, 3]], [[1250, 7172], [-12, -8], [-7, -4]], [[1154, 7181], [7, 4], [2, 1]], [[1158, 7189], [-7, -1], [-3, -1]], [[1148, 7187], [-3, 2], [-2, 2]], [[1143, 7191], [1, 5], [0, 2]], [[1144, 7198], [-1, 0], [-1, 0]], [[1138, 7194], [-1, 0], [-5, 2]], [[1132, 7196], [-3, 2], [-2, 1]], [[1124, 7201], [0, -1], [1, -4]], [[1125, 7196], [0, -4], [-1, -4]], [[1105, 7195], [-2, 9], [-2, 7]], [[1101, 7211], [-10, 3], [-4, 1]], [[1084, 7223], [5, 7], [3, 4]], [[1089, 7239], [-7, 1], [-2, 1]], [[1080, 7241], [-9, -5], [-1, -1]], [[1069, 7252], [13, 2], [8, 2]], [[1071, 7270], [6, 3], [0, 1]], [[1077, 7274], [4, 0], [9, 0]], [[1090, 7274], [9, 2], [8, 2]], [[1107, 7278], [-5, 5], [-3, 2]], [[1099, 7285], [0, 1], [-1, 3]], [[1098, 7289], [-1, 5], [0, 1]], [[1145, 7354], [5, 1], [4, 1]], [[1166, 7362], [4, 1], [8, 2]], [[1185, 7374], [0, 1], [0, 2]], [[1185, 7377], [-5, -3], [-2, -1]], [[1169, 7401], [1, 2], [2, 5]], [[1198, 7416], [2, -1], [1, 0]], [[1201, 7415], [0, 1], [1, 1]], [[1190, 7426], [3, 4], [2, 3]], [[1199, 7436], [22, 5], [1, 1]], [[1222, 7442], [2, 0], [11, -2]], [[1251, 7434], [2, -2], [7, -6]], [[1259, 7422], [-2, -2], [0, -1]], [[1257, 7419], [-1, 0], [-4, -2]], [[1252, 7417], [-1, -1], [-1, -1]], [[1251, 7413], [3, 2], [4, 3]], [[1258, 7418], [7, 3], [4, 2]], [[1280, 7416], [4, 0], [1, 0]], [[1303, 7420], [4, 2], [5, 3]], [[1312, 7425], [6, 6], [4, 4]], [[1363, 7473], [2, 1], [5, 1]], [[1380, 7466], [3, -1], [3, -1]], [[1467, 7489], [4, 4], [4, 4]], [[1474, 7521], [-2, 4], [-3, 7]], [[1469, 7532], [-4, 15], [-3, 11]], [[1462, 7558], [-9, 9], [-9, 8]], [[1444, 7575], [-11, 5], [-1, 0]], [[1424, 7580], [4, 8], [2, 3]], [[1447, 7589], [7, 2], [4, 1]], [[1458, 7592], [1, 0], [8, 5]], [[1470, 7601], [3, 6], [1, 2]], [[1474, 7609], [-1, 7], [-1, 2]], [[1470, 7622], [-4, 4], [-2, 1]], [[1464, 7627], [-2, 2], [-5, 6]], [[1457, 7635], [-4, 2], [-2, 1]], [[1447, 7637], [-16, -11], [-5, -4]], [[1403, 7625], [-6, -7], [-2, -2]], [[1371, 7608], [-5, -3], [-7, -4]], [[1336, 7582], [-1, -1], [-5, -8]], [[1322, 7573], [-3, 10], [-2, 6]], [[1292, 7605], [-3, -2], [-5, -3]], [[1284, 7600], [4, -4], [0, -1]], [[1288, 7595], [3, -2], [3, -2]], [[1294, 7591], [3, 0], [7, -1]], [[1287, 7585], [-14, 7], [-3, 1]], [[1253, 7598], [-22, 0], [-24, -1]], [[1207, 7597], [-18, -5], [-12, -4]], [[1165, 7585], [-2, -1], [-2, -1]], [[1161, 7583], [-5, -1], [-1, 0]], [[1143, 7583], [-14, 4], [-10, 2]], [[1066, 7597], [-7, 3], [-7, 2]], [[1052, 7602], [-7, 7], [-5, 5]], [[1045, 7626], [0, 11], [0, 1]], [[1034, 7641], [-11, 9], [-10, 8]], [[1005, 7665], [2, 0], [0, 1]], [[1055, 7679], [4, -1], [8, -1]], [[1065, 7680], [-2, 1], [-1, 1]], [[1026, 7686], [-23, 4], [-34, 6]], [[969, 7696], [-19, 7], [-26, 10]], [[916, 7724], [4, 4], [2, 3]], [[929, 7730], [0, -1], [1, -3]], [[930, 7726], [13, 5], [12, 4]], [[1008, 7759], [6, 6], [5, 5]], [[1019, 7770], [15, 5], [1, 1]], [[1046, 7781], [2, 0], [12, 3]], [[1060, 7784], [7, -3], [5, -2]], [[1108, 7781], [2, 2], [1, 1]], [[1089, 7793], [1, 3], [1, 2]], [[1094, 7801], [3, 2], [22, 9]], [[1179, 7829], [9, 2], [7, 2]], [[1226, 7836], [22, 0], [4, 0]], [[1258, 7832], [-5, 0], [-8, 1]], [[1245, 7833], [-4, -1], [-2, 0]], [[1243, 7830], [1, 0], [4, -4]], [[1248, 7826], [-1, -1], [0, -3]], [[1247, 7822], [-6, -8], [-3, -6]], [[1238, 7808], [0, -5], [1, -5]], [[1239, 7798], [-2, -1], [-4, -3]], [[1233, 7794], [-3, -3], [-2, -2]], [[1228, 7789], [11, -6], [15, -9]], [[1254, 7774], [25, -1], [15, 0]], [[1316, 7776], [2, -1], [11, -4]], [[1329, 7771], [5, -1], [5, 0]], [[1390, 7769], [5, 1], [4, 0]], [[1399, 7770], [15, 18], [5, 5]], [[1419, 7793], [1, 1], [6, 3]], [[1426, 7797], [7, -3], [2, -1]], [[1446, 7790], [3, 1], [4, 1]], [[1453, 7792], [2, -2], [4, -4]], [[1398, 7808], [-3, 1], [-4, 3]], [[1391, 7812], [1, 6], [1, 4]], [[1360, 7850], [-9, 0], [-2, 0]], [[1349, 7850], [-5, 7], [-1, 2]], [[1343, 7859], [-3, 6], [-2, 4]], [[1338, 7869], [5, 3], [5, 1]], [[1357, 7875], [1, 0], [7, -3]], [[1383, 7856], [-1, -8], [-1, -6]], [[1416, 7818], [6, 1], [14, 3]], [[1436, 7822], [7, 0], [6, 0]], [[1449, 7822], [4, -1], [5, -1]], [[1458, 7820], [15, -8], [5, -3]], [[1478, 7809], [2, 0], [8, -1]], [[1520, 7813], [1, 6], [0, 5]], [[1521, 7824], [-1, 2], [-2, 5]], [[1518, 7831], [-3, 2], [-4, 3]], [[1472, 7843], [-13, -2], [-2, 0]], [[1417, 7834], [-2, 1], [-6, 5]], [[1409, 7840], [-6, 3], [-7, 3], [-2, 13]], [[1406, 7884], [-2, 0], [-17, 3]], [[1387, 7887], [-11, -1], [-22, -2]], [[1354, 7884], [-1, 4], [0, 1]], [[1353, 7889], [0, 1], [0, 4]], [[1353, 7894], [-8, -6], [-5, -4]], [[1340, 7884], [-13, 2], [-1, 0]], [[1246, 7913], [-2, 6], [-9, 19]], [[1235, 7938], [-11, 11], [-3, 4]], [[1080, 8018], [-11, 7], [-10, 7]], [[1045, 8035], [-7, 1], [-5, 1]], [[1016, 8042], [5, 2], [6, 3]], [[1027, 8047], [4, 1], [4, 1]], [[1033, 8042], [5, 1], [4, 2]], [[1042, 8045], [4, 3], [2, 1]], [[1048, 8049], [2, 6], [5, 12]], [[1055, 8067], [6, 20], [2, 6]], [[1063, 8093], [-2, 7], [0, 3]], [[1115, 8101], [8, 1], [27, 1]], [[1150, 8103], [5, 1], [7, 1]], [[1207, 8109], [6, 2], [6, 1]], [[1266, 8137], [15, 13], [7, 7]], [[1295, 8162], [1, 3], [1, 5]], [[1311, 8206], [26, 22], [21, 18]], [[1386, 8268], [0, 1], [8, 4]], [[1394, 8273], [3, -2], [2, -2]], [[1399, 8269], [0, -1], [2, -1]], [[1401, 8267], [1, -1], [1, -1]], [[1398, 8264], [-5, -4], [-2, -1]], [[1391, 8259], [-5, -2], [-5, -2]], [[1379, 8254], [4, 0], [2, 0]], [[1385, 8254], [6, 1], [11, 3]], [[1402, 8258], [6, 2], [4, 2]], [[1412, 8262], [25, 4], [25, 4]], [[1462, 8270], [8, 5], [18, 9]], [[1488, 8284], [1, 1], [1, 2]], [[1490, 8287], [8, 3], [31, 14]], [[1529, 8304], [3, 0], [3, -1]], [[1535, 8303], [2, 0], [4, -2]], [[1541, 8301], [-4, -4], [-7, -7]], [[1530, 8290], [4, -1], [4, -2]], [[1531, 8274], [7, 0], [7, -1]], [[1545, 8273], [2, -3], [2, -3]], [[1549, 8273], [0, 2], [0, 5]], [[1549, 8280], [0, 1], [1, 7]], [[1585, 8296], [-13, 1], [-14, 0]], [[1541, 8309], [-8, 0], [-1, 0]], [[1600, 8336], [1, -3], [0, -1]], [[1601, 8332], [-3, -1], [-3, -1]], [[1595, 8330], [-4, -2], [-2, -2]], [[1592, 8323], [1, 0], [3, 0]], [[1596, 8323], [9, 1], [4, 1]], [[1616, 8329], [18, 0], [10, -1]], [[1653, 8331], [1, 1], [1, 1]], [[1692, 8334], [5, 1], [2, 1]], [[1699, 8336], [14, 5], [9, 4]], [[1722, 8345], [7, 3], [15, 9]], [[1754, 8363], [15, 15], [2, 1]], [[1771, 8379], [12, 9], [3, 2]], [[1802, 8392], [6, -4], [2, -2]], [[1810, 8386], [22, -5], [28, -7]], [[1860, 8374], [3, 0], [10, 0]], [[1873, 8374], [5, -7], [0, -1]], [[1878, 8366], [-1, -2], [-3, -5]], [[1861, 8352], [-16, -5], [-10, -3]], [[1856, 8333], [11, 2], [1, 0]], [[1868, 8335], [2, 1], [8, 4]], [[1878, 8340], [17, 11], [4, 3]], [[1899, 8354], [1, 2], [5, 6]], [[1905, 8362], [2, 0], [3, 2]], [[1910, 8364], [7, -1], [10, -1]], [[1944, 8344], [-2, -2], [-3, -2]], [[1939, 8340], [7, -3], [8, -3]], [[2093, 8338], [4, 0], [5, 1]], [[2116, 8338], [8, -2], [7, -2]], [[2120, 8321], [0, -1], [-3, -9]], [[2134, 8301], [2, 0], [4, -1]], [[2140, 8300], [12, 0], [19, 0]], [[2169, 8298], [0, -1], [-1, -2]], [[2168, 8295], [-9, -6], [-1, -1]], [[2221, 8288], [1, 0], [10, 1]], [[2256, 8294], [6, -1], [4, -1]], [[2276, 8287], [6, -1], [4, 0]], [[2286, 8286], [6, 0], [4, 1]], [[2296, 8287], [10, 3], [11, 4]], [[2317, 8294], [13, 1], [13, 0]], [[2343, 8295], [7, -2], [3, -1]], [[2364, 8293], [17, -4], [15, -5]], [[2424, 8272], [7, 0], [2, 0]], [[2450, 8276], [5, -2], [3, -2]], [[2458, 8272], [8, -1], [5, -1]], [[2471, 8270], [3, -3], [3, -4]], [[2477, 8263], [3, -2], [4, -1]], [[2558, 8257], [6, 0], [29, -1]], [[2593, 8256], [11, -2], [7, -1]], [[2629, 8253], [4, -2], [25, -11]], [[2658, 8240], [7, 0], [9, -2]], [[2674, 8238], [2, -1], [1, -2]], [[2818, 8245], [4, 0], [3, 1]], [[2829, 8248], [23, -5], [16, -5]], [[2890, 8227], [1, 0], [9, -8]], [[2993, 8194], [2, 0], [5, -1]], [[3000, 7093], [2, 0]], [[3041, 7092], [1, 1]], [[3042, 7093], [15, -5]], [[3078, 7080], [1, 0]], [[3118, 7097], [9, 1]], [[3144, 7098], [3, 1]], [[1068, 7791], [-2, -1], [-1, 0]], [[1066, 7792], [1, 1], [7, 4]], [[1074, 7797], [12, 4], [4, 2]], [[1090, 7801], [-2, -1], [-7, -3]], [[657, 7486], [1, -2], [0, -1]], [[666, 7484], [2, 0], [9, -1]], [[702, 7481], [8, 4], [7, 5]], [[727, 7492], [3, 0], [6, 1]], [[736, 7493], [8, -1], [2, -1]], [[760, 7484], [1, -3], [2, -2]], [[765, 7473], [0, -1], [3, -3]], [[768, 7469], [9, -3], [9, -2]], [[786, 7464], [6, -1], [6, -1]], [[798, 7462], [1, -2], [2, -1]], [[803, 7455], [5, -1], [5, -2]], [[813, 7452], [6, 1], [4, 0]], [[829, 7452], [3, 0], [14, 0]], [[846, 7452], [13, -3], [9, -1]], [[868, 7448], [-1, -4], [-3, -8]], [[864, 7436], [-1, -1], [-6, -4]], [[857, 7431], [-4, 0], [-15, 2]], [[838, 7433], [-10, -1], [-10, -1]], [[810, 7425], [-5, -4], [-2, -3]], [[798, 7407], [-3, -1], [-1, 0]], [[791, 7410], [-3, 7], [-2, 5]], [[783, 7425], [-2, 2], [-1, 1]], [[780, 7428], [-3, 0], [-7, 3]], [[770, 7431], [-5, 2], [-5, 1]], [[704, 7463], [-4, 1], [-4, 0]], [[696, 7464], [-7, 0], [-1, -1]], [[661, 7451], [-4, -1], [-5, 0]], [[652, 7450], [-4, 1], [-4, 1]], [[635, 7494], [2, 1], [6, 2]], [[2457, 7154], [-3, 1], [-2, 1]], [[2452, 7157], [1, 4], [1, 2]], [[2454, 7163], [0, 2], [-1, 1]], [[2464, 7164], [1, -2], [1, -1]], [[2467, 7155], [-4, 0], [-6, -1]], [[594, 7099], [0, -1], [3, -5]], [[585, 7097], [-16, 0], [-3, 0]], [[533, 7116], [0, 2], [2, 7]], [[535, 7125], [7, 4], [2, 1]], [[544, 7130], [3, -8], [2, -4]], [[575, 7104], [10, 1], [1, 0]], [[2148, 7100], [3, 3], [2, 3]], [[2161, 7116], [1, -1], [0, -1]], [[2488, 7111], [0, -2], [0, -1]], [[2485, 7105], [1, -2], [1, -3]], [[2483, 7090], [0, -1], [-1, -5]], [[2475, 7080], [0, 1], [0, 1]], [[2471, 7085], [-1, 4], [0, 4]], [[2470, 7093], [1, 1], [1, 2]], [[2472, 7096], [1, 2], [0, 1]], [[2473, 7099], [1, 1], [0, 2]], [[2478, 7113], [1, -2], [0, -1]], [[2587, 7108], [12, 0], [2, 1]], [[2601, 7109], [1, -1], [4, 0]], [[2606, 7108], [0, -1], [1, -1]], [[2605, 7104], [-2, -1], [-3, -2]], [[2565, 7098], [-1, 0], [-1, 3]], [[2569, 7113], [1, 1], [2, 1]], [[2585, 7111], [0, -1], [2, -2]], [[2680, 7095], [-2, 0], [-4, 1]], [[2674, 7096], [-1, 0], [-3, 2]], [[2682, 7105], [1, 0], [1, -1]], [[2684, 7104], [0, -1], [-1, -5]], [[1077, 7097], [4, 1], [8, 1]], [[1093, 7098], [2, -2], [3, -1]], [[1099, 7078], [0, -6], [0, -2]], [[1105, 7061], [1, -3], [1, -3]], [[1107, 7055], [1, -6], [0, -1]], [[1108, 7048], [-3, -1], [-10, -1]], [[1067, 7037], [0, -1], [2, -4]], [[1065, 7030], [-2, 1], [-1, 1]], [[1062, 7032], [-1, 1], [-4, 3]], [[989, 7059], [-3, 3], [-9, 7]], [[973, 7085], [1, 0], [7, 1]], [[981, 7086], [15, -1], [16, -1]], [[1016, 7093], [4, 2], [1, 0]], [[1021, 7095], [0, 1], [10, 2]], [[1031, 7098], [6, 3], [3, 2]], [[1040, 7103], [3, 0], [1, 0]], [[1058, 7104], [4, 1], [1, 0]], [[1073, 7097], [3, 0], [1, 0]], [[2482, 7036], [-8, -1], [-1, -1]], [[2471, 7038], [4, 8], [1, 1]], [[2476, 7047], [1, 1], [2, 3], [3, 2]], [[2492, 7062], [8, 6], [2, 2]], [[2502, 7070], [3, 2], [7, 8]], [[2509, 7057], [-1, -1], [-1, -1]], [[2507, 7055], [-1, -1], [-2, -1]], [[2501, 7050], [-1, -4], [0, -1]], [[2497, 7043], [-3, -1], [-2, 0]], [[2440, 7064], [1, 2], [3, 5]], [[2444, 7071], [9, 4], [2, 1]], [[2455, 7076], [2, -1], [11, -6]], [[2468, 7069], [-5, -2], [-3, -1]], [[2725, 7037], [-1, -1], [-2, 0]], [[2722, 7036], [0, 1], [5, 7]], [[2727, 7044], [3, 3], [5, 5]], [[2751, 7060], [0, -1], [-1, -3]], [[2750, 7056], [-5, -3], [-7, -4]], [[1456, 6890], [-3, 7], [-2, 4]], [[1462, 6909], [4, 2], [13, 5]], [[1483, 6916], [-9, -14], [-6, -11]], [[1468, 6891], [-2, -1], [-4, -1]], [[2108, 6883], [-3, 3], [0, 1]], [[2105, 6887], [1, 1], [1, 1]], [[2107, 6889], [1, 1], [10, 6]], [[2125, 6895], [1, -1], [0, -1]], [[2126, 6890], [-1, -2], [-2, -2]], [[2114, 6879], [-2, 1], [-4, 3]], [[2138, 6866], [5, 1], [1, 0]], [[2144, 6867], [1, -2], [3, -5]], [[2151, 6859], [2, 0], [1, 1]], [[2156, 6859], [-1, -4], [0, -3]], [[2155, 6852], [-1, -1], [-6, -7]], [[2148, 6844], [0, -1], [-3, -1]], [[2145, 6842], [-2, 1], [-2, 1]], [[2139, 6845], [-1, 2], [-2, 1]], [[2136, 6848], [0, 2], [-2, 2]], [[2133, 6852], [-2, -2], [-3, -3]], [[2124, 6837], [-1, 1], [-4, 0]], [[2108, 6842], [-2, -4], [-2, -3]], [[2104, 6835], [-2, -4], [-1, -1]], [[2094, 6825], [-3, 0], [-2, -1]], [[2089, 6824], [-4, -1], [-3, -1]], [[2082, 6822], [-2, 0], [-2, 1]], [[2053, 6830], [-5, 3], [-1, 0]], [[2079, 6858], [3, 0], [3, -1]], [[2085, 6857], [2, 0], [4, -2]], [[2094, 6856], [0, 6], [1, 2]], [[2095, 6864], [-3, 2], [-3, 3]], [[2089, 6872], [8, 2], [5, 2]], [[2102, 6876], [2, 0], [3, -1]], [[2107, 6875], [3, -1], [2, -1]], [[2117, 6870], [3, -3], [2, -2]], [[2122, 6865], [1, 0], [1, -1]], [[3486, 6842], [3, -1], [16, -2]], [[3505, 6839], [11, 1], [4, 0]], [[3520, 6840], [4, -6], [9, -12]], [[3541, 6808], [1, -3], [3, -7]], [[3548, 6788], [1, -1], [3, -8]], [[3552, 6779], [0, -1], [-1, 0]], [[3544, 6784], [-3, 6], [-3, 7]], [[3538, 6797], [-1, 3], [-1, 2]], [[3536, 6802], [-1, 0], [-2, 2]], [[3530, 6809], [-5, 10], [-1, 2]], [[3524, 6824], [-1, 1], [-1, 2]], [[3519, 6829], [-2, -1], [-1, 0]], [[3516, 6828], [-1, 0], [0, -1]], [[3518, 6809], [0, -1], [14, -19]], [[3532, 6789], [4, -6], [5, -6]], [[3541, 6777], [1, -1], [1, -2]], [[3544, 6763], [-1, -2], [-3, -3]], [[3540, 6758], [2, -3], [3, -6]], [[3545, 6749], [0, -1], [0, -1]], [[3544, 6745], [-3, -1], [-11, -3]], [[3518, 6722], [-12, -9], [-1, -1]], [[3491, 6721], [0, 4], [-1, 3]], [[3494, 6732], [4, 15], [2, 8]], [[3500, 6755], [1, 5], [0, 2]], [[3492, 6772], [0, 1], [-5, 7]], [[3485, 6792], [-3, 21], [-2, 9]], [[3475, 6840], [-2, 3], [-2, 4]], [[3471, 6847], [-2, 6], [-1, 1]], [[3468, 6854], [0, 2], [-2, 6]], [[3467, 6865], [3, -2], [4, -2]], [[3474, 6861], [3, -6], [5, -6]], [[3498, 6862], [4, -2], [5, -3]], [[3507, 6857], [6, -6], [1, -1]], [[3487, 6857], [1, 1], [0, 2]], [[3414, 6830], [-5, -2], [-1, -1]], [[3417, 6824], [4, 4], [8, 7]], [[3429, 6835], [1, 1], [2, 1]], [[3432, 6837], [2, 0], [1, 0]], [[3449, 6834], [8, -3], [4, -2]], [[3461, 6829], [2, -3], [2, -1]], [[3465, 6825], [0, -2], [2, -6]], [[3449, 6799], [-2, 0], [-4, 2]], [[3443, 6801], [-4, -3], [-4, -2]], [[3435, 6796], [5, -3], [2, -2]], [[3469, 6781], [1, -3], [1, -3]], [[3471, 6775], [-4, -12], [-1, -1]], [[3455, 6765], [-6, 4], [-5, 4]], [[3413, 6775], [1, -6], [0, -7]], [[3378, 6785], [-15, 12], [-8, 7]], [[3349, 6808], [-7, 9], [-1, 3]], [[3341, 6820], [1, 4], [2, 5]], [[3360, 6849], [3, -4], [2, -4]], [[3373, 6834], [0, 4], [0, 3]], [[3377, 6846], [5, 0], [3, 0]], [[3401, 6854], [2, -1], [2, -1]], [[3416, 6840], [0, -1], [0, -2]], [[2085, 6802], [0, -5], [0, -1]], [[2085, 6796], [2, 0], [1, 0]], [[2121, 6797], [-3, -3], [-2, -2]], [[2122, 6781], [3, -1], [10, -2]], [[2137, 6776], [0, -1], [0, -2]], [[2137, 6773], [-8, -10], [-1, -1]], [[2122, 6759], [-3, 0], [-14, 2]], [[2090, 6764], [-2, 1], [-5, 0]], [[2081, 6764], [-3, -3], [-1, -1]], [[2080, 6759], [5, 0], [9, 0]], [[2094, 6759], [2, -3], [2, -2]], [[2100, 6750], [1, -1], [0, -3]], [[2099, 6744], [-2, 0], [-4, -1]], [[2093, 6743], [-4, 0], [-3, 0]], [[2077, 6738], [-2, -2], [-2, -3]], [[2056, 6732], [-7, -4], [-6, -3]], [[2038, 6721], [-1, -1], [-1, -3]], [[2031, 6714], [-7, -2], [-4, -1]], [[2020, 6711], [6, -2], [1, 0]], [[2002, 6679], [-3, 0], [-1, 0]], [[1994, 6682], [0, 1], [0, 1]], [[1994, 6684], [7, 6], [15, 14]], [[2016, 6704], [-1, 0], [0, 1]], [[1997, 6720], [-1, 0], [-1, 1]], [[1989, 6722], [-5, 0], [-3, 0]], [[1970, 6717], [8, 0], [1, 0]], [[1981, 6715], [2, -1], [1, -1]], [[1984, 6713], [0, -2], [1, -2]], [[1984, 6701], [-2, -3], [-2, -3]], [[1974, 6696], [-11, 12], [-2, 2]], [[1961, 6710], [0, 3], [-5, 16]], [[1959, 6771], [6, 2], [5, 2]], [[1978, 6780], [3, 1], [5, 1]], [[1986, 6782], [2, 0], [3, 0]], [[2009, 6757], [4, -6], [2, -4]], [[2015, 6747], [1, -1], [3, -3]], [[2019, 6743], [4, -2], [1, 0]], [[2024, 6741], [-5, 7], [-1, 0]], [[2018, 6748], [-1, 5], [-2, 4]], [[2015, 6757], [-1, 15], [0, 3]], [[2014, 6775], [-1, 3], [-1, 2]], [[2012, 6780], [2, 1], [1, 0]], [[2024, 6781], [0, 1], [-1, 1]], [[2015, 6789], [-3, 3], [-3, 2]], [[2007, 6798], [0, 1], [0, 3]], [[2015, 6808], [1, 0], [2, 1]], [[2033, 6795], [2, -3], [1, -1]], [[2036, 6791], [2, 0], [1, 0]], [[2042, 6793], [1, 1], [1, 3]], [[2044, 6797], [2, 1], [1, 1]], [[2047, 6799], [1, 1], [1, 0]], [[2060, 6799], [1, 1], [0, 2]], [[2061, 6807], [1, 1], [1, 1]], [[2064, 6812], [-2, 2], [-3, 4]], [[2088, 6811], [-3, -8], [0, -1]], [[2050, 6807], [-6, 3], [-3, 2]], [[2041, 6812], [-3, 3], [-1, 1]], [[2050, 6815], [3, -3], [1, -1]], [[2054, 6811], [1, -1], [3, -5]], [[3470, 6734], [3, -6], [2, -4]], [[3475, 6724], [2, -7], [2, -5]], [[3479, 6712], [4, -14], [3, -11]], [[3486, 6687], [2, -5], [1, -5]], [[3490, 6656], [-1, -2], [-1, -1]], [[3480, 6616], [-3, 4], [-1, 1]], [[3476, 6621], [-1, 1], [-2, 4]], [[3473, 6626], [-2, 4], [-6, 11]], [[3465, 6641], [-1, 4], [-1, 4]], [[3466, 6660], [4, 6], [1, 0]], [[3470, 6667], [-2, 0], [-1, -1]], [[3454, 6670], [-3, 2], [-2, 1]], [[3450, 6682], [-1, 1], [0, 2]], [[3439, 6682], [-1, 1], [-2, 1]], [[3436, 6684], [0, 1], [-1, 3]], [[3435, 6688], [0, 4], [0, 1]], [[3441, 6711], [-1, 0], [-5, 4]], [[3435, 6715], [-2, 11], [-1, 1]], [[3423, 6734], [-1, -1], [-8, -19]], [[3414, 6714], [-1, -2], [-3, -3]], [[3410, 6709], [-4, -1], [-7, -2]], [[3401, 6712], [0, 2], [1, 3]], [[3398, 6738], [0, 1], [3, 4]], [[3401, 6743], [1, 0], [7, 1]], [[3413, 6747], [1, 1], [3, 3]], [[3427, 6768], [1, 0], [7, 0]], [[3451, 6756], [1, 0], [4, -2]], [[3464, 6747], [3, -7], [3, -6]], [[742, 6723], [-2, 4], [-1, 0]], [[2065, 6716], [-4, -4], [-2, -3]], [[2059, 6709], [-2, -2], [-3, -2]], [[2054, 6705], [-1, 1], [-5, 5]], [[2076, 6720], [-7, -3], [-3, -1]], [[3587, 6706], [4, -3], [1, -1]], [[3619, 6691], [0, -1], [0, -2]], [[3619, 6688], [-1, -5], [0, -3]], [[3617, 6664], [-2, -2], [0, -1]], [[3596, 6681], [-2, 2], [-4, 2]], [[3591, 6680], [4, -5], [2, -2]], [[3601, 6663], [1, -2], [1, -2]], [[3604, 6654], [0, -3], [0, -1]], [[3603, 6646], [-1, -1], [-1, -2]], [[3586, 6643], [-5, -1], [-3, -1]], [[3569, 6642], [-2, 2], [-1, 1]], [[3566, 6645], [0, 1], [-1, 2]], [[3564, 6658], [-1, 6], [-1, 7]], [[3562, 6671], [0, 5], [1, 5]], [[3563, 6681], [-6, 9], [0, 1]], [[3557, 6691], [-4, 4], [-1, 1]], [[3552, 6696], [-1, 1], [-7, 4]], [[3544, 6701], [-3, 4], [-1, 1]], [[3541, 6710], [2, 1], [5, 2]], [[3548, 6713], [6, 0], [7, 0]], [[3539, 6687], [1, -2], [4, -6]], [[3544, 6679], [6, 0], [1, 0]], [[3555, 6673], [2, -5], [1, -4]], [[3558, 6664], [-1, -3], [-1, -3]], [[3533, 6603], [-1, -2], [-3, -4]], [[3519, 6611], [3, 16], [1, 9]], [[3523, 6636], [4, 3], [5, 2]], [[3532, 6641], [-3, 4], [-2, 3]], [[3527, 6648], [-5, 4], [-6, 4]], [[3509, 6686], [4, 4], [4, 6]], [[3527, 6697], [4, -2], [3, -2]], [[3634, 6648], [-6, 0], [-4, 0]], [[3624, 6648], [-3, 1], [-1, 0]], [[3619, 6654], [0, 3], [1, 2]], [[3622, 6663], [1, 1], [2, 5]], [[3643, 6653], [-3, -1], [-3, -1]], [[788, 6662], [6, -1], [4, -1]], [[798, 6660], [3, 1], [3, 0]], [[804, 6661], [2, 0], [3, -1]], [[809, 6660], [0, -2], [1, -1]], [[801, 6651], [-2, 1], [-2, 0]], [[1984, 6648], [-1, 0], [-3, 0]], [[1980, 6648], [-2, 1], [-4, 2]], [[1975, 6655], [5, 2], [3, 2]], [[1991, 6659], [1, -2], [0, -1]], [[1992, 6656], [0, -2], [0, -1]], [[1940, 6639], [1, 3], [0, 1]], [[1960, 6658], [2, 0], [2, 0]], [[1964, 6658], [1, -2], [1, -1]], [[1966, 6655], [-2, -2], [-4, -4]], [[1942, 6636], [-1, 1], [-1, 0]], [[3670, 6579], [-3, 1], [-3, 0]], [[3664, 6580], [-1, 1], [-2, 2]], [[3661, 6583], [-1, 1], [0, 1]], [[3660, 6585], [1, 5], [0, 1]], [[3657, 6594], [-9, 1], [-2, 0]], [[3646, 6595], [-2, 1], [-3, 1]], [[3641, 6597], [0, 1], [-2, 5]], [[3646, 6616], [3, 7], [1, 3]], [[3676, 6582], [-4, -2], [-2, -1]], [[3628, 6616], [-3, 2], [-2, 0]], [[3614, 6634], [2, 2], [4, 4]], [[3620, 6640], [2, 1], [1, 0]], [[3642, 6639], [1, -2], [1, -1]], [[3644, 6636], [-1, -1], [0, -2]], [[3643, 6633], [-1, -2], [0, -1]], [[3641, 6627], [1, -1], [0, -2]], [[3586, 6625], [6, 0], [7, 0]], [[3607, 6615], [1, -4], [1, -1]], [[3609, 6610], [0, -2], [0, -2]], [[3608, 6598], [9, -4], [17, -7]], [[3634, 6587], [6, -6], [6, -6]], [[3651, 6569], [2, -5], [1, 0]], [[3654, 6564], [1, -3], [4, -10]], [[3677, 6526], [-3, 2], [-5, 2]], [[3669, 6530], [-6, 3], [-10, 7]], [[3650, 6534], [-1, -1], [-2, -4]], [[3647, 6529], [-2, -2], [-2, -1]], [[3643, 6526], [1, -1], [2, 0]], [[3675, 6498], [1, -1], [0, -1]], [[3692, 6497], [1, -1], [1, -2]], [[3694, 6494], [-2, -16], [0, -4]], [[3692, 6474], [1, -3], [1, -5]], [[3692, 6454], [1, -1], [0, -2]], [[3690, 6438], [-1, -1], [-2, -1]], [[3687, 6436], [-3, 0], [-2, 0]], [[3682, 6436], [-3, 2], [-2, 1]], [[3644, 6486], [-2, 1], [-1, 0]], [[3641, 6487], [-1, -1], [-2, -1]], [[3638, 6473], [-2, 1], [-4, 2]], [[3622, 6492], [-11, 12], [-1, 1]], [[3610, 6505], [0, 1], [-4, 2]], [[3607, 6512], [4, 2], [2, 0]], [[3613, 6514], [1, 0], [3, 0]], [[3618, 6516], [-5, 7], [-4, 6]], [[3608, 6542], [-9, -2], [-3, 0]], [[3592, 6541], [-3, 5], [0, 1]], [[3589, 6547], [-1, 0], [-1, 4]], [[3576, 6552], [-2, -1], [-2, 0]], [[3574, 6568], [3, -2], [4, -2]], [[3581, 6564], [1, 0], [1, 0]], [[3590, 6569], [1, 2], [0, 3]], [[3591, 6574], [3, 2], [2, 2]], [[3593, 6590], [-2, 0], [-5, 2]], [[3558, 6583], [0, 1], [-1, 4]], [[3557, 6588], [4, 3], [8, 8]], [[3569, 6599], [4, 4], [1, 2]], [[3749, 6496], [-4, 5], [0, 1]], [[3736, 6502], [-1, 3], [-1, 2]], [[3734, 6507], [1, 2], [1, 4]], [[3736, 6513], [0, 2], [-2, 3]], [[3734, 6518], [-1, -2], [-1, -2]], [[3732, 6514], [-2, -3], [-1, -1]], [[3720, 6505], [-1, 0], [-6, -9]], [[3713, 6496], [-2, -4], [-1, -3]], [[3710, 6489], [-2, 5], [-2, 2]], [[3704, 6512], [0, 3], [0, 4]], [[3710, 6529], [2, 2], [7, 8]], [[3751, 6582], [5, -7], [4, -4]], [[3760, 6571], [3, -6], [6, -9]], [[3754, 6494], [-3, 1], [-2, 1]], [[1876, 6563], [-4, 0], [-1, 0]], [[1877, 6579], [1, -1], [1, 0]], [[1880, 6574], [-1, -4], [0, -3]], [[1879, 6567], [0, -1], [-3, -3]], [[3592, 6534], [1, -2], [0, -1]], [[3582, 6521], [0, -1], [0, -1]], [[3582, 6519], [-2, -4], [-1, -1]], [[3573, 6507], [-5, -4], [-3, -1]], [[3558, 6528], [4, 3], [4, 2]], [[3566, 6533], [4, -1], [1, 0]], [[3571, 6532], [2, 0], [7, -1]], [[3580, 6531], [5, 3], [3, 2]], [[3588, 6536], [2, -1], [2, -1]], [[1487, 6507], [1, 0], [1, 1]], [[1489, 6508], [4, 5], [1, 1]], [[1496, 6513], [-2, -5], [-1, -2]], [[1493, 6506], [1, 0], [3, -9]], [[1497, 6497], [2, -1], [2, -3]], [[1484, 6491], [-2, -1], [-1, 0]], [[1477, 6487], [-1, 1], [-1, 2]], [[1487, 6515], [0, -1], [0, -2]], [[1487, 6512], [-1, -4], [0, -1]], [[1501, 6506], [-1, 2], [-1, 1]], [[3629, 6457], [2, 2], [2, 2]], [[3630, 6436], [-4, 3], [-2, 3]], [[3614, 6453], [-2, 4], [-3, 4]], [[3609, 6461], [0, 2], [0, 1]], [[3596, 6491], [-2, 0], [-4, 1]], [[3583, 6495], [-2, 4], [-1, 2]], [[3616, 6474], [1, -1], [2, -3]], [[1535, 6475], [-9, -11], [-1, -1]], [[1526, 6474], [1, 1], [0, 2]], [[1528, 6484], [2, 1], [0, 1]], [[1548, 6501], [0, -2], [-2, -3]], [[1550, 6487], [-1, -1], [-1, -1]], [[1548, 6485], [-3, -1], [-2, -1]], [[3735, 6459], [-1, 2], [0, 1]], [[3736, 6470], [-3, 3], [-1, 1]], [[3723, 6481], [2, 3], [1, 2]], [[3724, 6494], [0, 1], [0, 4]], [[3725, 6501], [2, 0], [2, 0]], [[3743, 6479], [6, -12], [2, -3]], [[1572, 6479], [-3, -2], [-1, 0]], [[1568, 6477], [-1, 0], [-2, 2]], [[1569, 6485], [1, 2], [0, 2]], [[1571, 6496], [1, 0], [1, 1]], [[1573, 6497], [1, -4], [2, -5]], [[1583, 6467], [-1, 3], [-2, 4]], [[1585, 6475], [2, -4], [0, -1]], [[1587, 6470], [0, -1], [0, -2]], [[1278, 6448], [2, -1], [2, -3]], [[1293, 6440], [3, -2], [1, -1]], [[1301, 6431], [-14, 5], [-7, 3]], [[1263, 6426], [-26, 0], [-12, -1]], [[1225, 6425], [-4, -1], [-4, -1]], [[1217, 6423], [-3, -2], [-1, -1]], [[1213, 6420], [-6, -7], [-3, -4]], [[1185, 6400], [-8, 0], [-4, 0]], [[1173, 6400], [-1, 1], [-5, 1]], [[1164, 6407], [-1, 4], [-2, 5]], [[1161, 6419], [0, 1], [1, 4]], [[1162, 6424], [7, 4], [4, 2]], [[1261, 6476], [3, -1], [1, -1]], [[1268, 6472], [2, -3], [1, -1]], [[1360, 6451], [-2, 2], [-4, 2]], [[1362, 6468], [1, 0], [1, 0]], [[1366, 6465], [0, -2], [0, -1]], [[1328, 6400], [-5, 4], [-1, 1]], [[1321, 6410], [13, -3], [1, 0]], [[1338, 6405], [2, -3], [2, -2]], [[1335, 6397], [-4, 1], [-3, 2]], [[1106, 6369], [0, 2], [0, 3]], [[1108, 6385], [2, 0], [1, 1]], [[1118, 6374], [-5, -4], [-2, -2]], [[1089, 6361], [-2, -2], [-1, 0]], [[1086, 6359], [0, -1], [-2, 0]], [[1082, 6359], [-5, -1], [-3, 0]], [[1074, 6358], [-1, 0], [-1, 1]], [[1070, 6372], [1, 1], [3, 2]], [[1100, 6366], [0, -1], [0, -1]], [[1097, 6362], [-7, -1], [-1, 0]], [[1033, 6335], [5, 4], [1, 0]], [[1039, 6339], [1, 1], [3, 3]], [[1051, 6349], [4, -3], [4, -2]], [[1039, 6327], [-1, -2], [-3, -5]], [[1035, 6320], [0, -1], [0, -2]], [[1035, 6317], [7, 2], [5, 0]], [[1047, 6319], [2, 0], [2, 0]], [[1050, 6314], [-6, -2], [-1, -1]], [[1037, 6306], [-12, -7], [-2, -1]], [[1023, 6298], [-2, -2], [-3, -5]], [[1011, 6288], [-4, 0], [-4, -1]], [[1003, 6287], [-8, -2], [-7, -2]], [[979, 6278], [-1, -1], [-1, -1]], [[963, 6269], [-1, 0], [-2, -2]], [[952, 6265], [-2, 0], [-2, 0]], [[948, 6265], [-1, 1], [-7, 4]], [[945, 6278], [1, 1], [5, 1]], [[997, 6312], [1, 3], [1, 2]], [[999, 6317], [5, 0], [4, 0]], [[1008, 6317], [2, -2], [2, -4]], [[1012, 6311], [1, 0], [1, -1]], [[1020, 6319], [-2, 1], [-1, 1]], [[1008, 6324], [-3, 1], [-3, 0]], [[1002, 6325], [-1, 0], [-3, 0]], [[991, 6337], [1, 2], [1, 2]], [[997, 6346], [4, 1], [1, 1]], [[1002, 6348], [1, 0], [9, 2]], [[1012, 6350], [4, 1], [4, 2]], [[1029, 6352], [0, -1], [0, -10]], [[1060, 6319], [0, 1], [-2, 2]], [[1069, 6333], [-2, -3], [-1, -3]], [[1063, 6324], [-1, -2], [-1, -2]], [[902, 6263], [-6, -7], [-1, -3]], [[895, 6253], [-2, -2], [-4, -6]], [[889, 6245], [-3, -3], [-2, -2]], [[881, 6239], [-2, -1], [-2, -1]], [[877, 6237], [-7, -6], [-1, -1]], [[869, 6230], [-1, 0], [-2, 0]], [[866, 6230], [-19, -11], [-6, -4]], [[839, 6215], [1, 2], [0, 2]], [[853, 6229], [0, 1], [6, 7]], [[863, 6252], [1, 3], [1, 0]], [[870, 6261], [1, 2], [3, 2]], [[889, 6265], [4, 2], [1, 1]], [[895, 6270], [-2, 2], [-1, 1]], [[892, 6273], [0, 1], [0, 2]], [[896, 6288], [3, 4], [2, 2]], [[917, 6300], [2, 0], [5, 0]], [[935, 6286], [-1, -2], [-1, -4]], [[793, 6217], [-1, -2], [0, -3]], [[792, 6212], [-1, -2], [-1, 0]], [[779, 6213], [-4, -1], [-4, 0]], [[770, 6217], [7, 2], [6, 2]], [[783, 6221], [3, 0], [2, 0]], [[788, 6221], [1, 0], [2, -2]], [[791, 6219], [1, -1], [1, -1]], [[706, 6183], [0, 2], [0, 3]], [[706, 6188], [1, 2], [1, 1]], [[368, 6116], [-2, 2], [-4, 2]], [[410, 6133], [7, 3], [8, 3]], [[425, 6139], [6, 1], [7, 2]], [[442, 6149], [-7, 1], [-5, 2]], [[433, 6157], [4, 4], [1, 0]], [[458, 6160], [1, -1], [1, -3]], [[460, 6156], [0, -3], [-1, -2]], [[457, 6146], [-4, -1], [-4, -2]], [[448, 6141], [0, -1], [4, -7]], [[452, 6133], [-8, -3], [-9, -4]], [[435, 6126], [-22, -4], [-4, -1]], [[574, 6147], [-2, 1], [-4, 1]], [[574, 6159], [1, 1], [4, 3]], [[591, 6156], [-5, -3], [-6, -4]], [[537, 6128], [0, -1], [0, -1]], [[537, 6126], [-5, 0], [-7, -2]], [[510, 6122], [-4, 0], [-3, 0]], [[503, 6122], [-6, 1], [-10, 1]], [[471, 6123], [-1, 1], [-3, 2]], [[462, 6129], [0, 1], [0, 2]], [[466, 6132], [7, -1], [4, -2]], [[478, 6131], [7, 2], [3, 1]], [[488, 6134], [2, 0], [6, -1]], [[496, 6133], [8, -2], [7, -3]], [[293, 6124], [2, 4], [1, 1]], [[296, 6129], [2, 0], [4, 0]], [[302, 6129], [1, -1], [2, -1]], [[305, 6127], [2, -2], [2, -2]], [[310, 6120], [-3, -2], [-1, -1]], [[274, 6096], [0, -4], [0, -4]], [[268, 6087], [-2, -3], [-1, -1]], [[265, 6083], [-9, -5], [-8, -4]], [[248, 6074], [-1, 1], [-4, 4]], [[243, 6079], [-4, -4], [-5, -5]], [[240, 6092], [5, 2], [3, 2]], [[251, 6098], [0, 3], [-1, 4]], [[250, 6105], [1, 1], [3, 9]], [[213, 6081], [-7, 1], [-5, 0]], [[180, 6079], [0, 1], [-1, 2]], [[205, 6091], [2, 1], [4, 2]], [[222, 6109], [3, -3], [1, -1]], [[221, 6098], [0, -3], [-1, -1]], [[156, 6086], [-1, 1], [0, 2]], [[157, 6107], [5, -4], [2, -2]], [[164, 6101], [4, -2], [1, -1]], [[169, 6098], [2, 0], [10, -1]], [[171, 6091], [0, -2], [-4, -9]], [[148, 6081], [1, 0], [5, 3]], [[292, 6098], [0, 3], [1, 3]], [[293, 6104], [4, -2], [5, -3]], [[302, 6099], [1, -1], [4, -3]], [[276, 6098], [2, 2], [1, 1]], [[279, 6101], [5, -5], [2, -2]], [[7114, 3572], [-4, -5], [-5, -7]], [[7077, 3559], [6, 0], [1, 1]], [[7084, 3578], [-1, 3], [-1, 1]], [[7080, 3603], [-4, 7], [-1, 1]], [[7045, 3573], [-1, 24], [0, 22], [-1, 22], [-1, 22], [-1, 22], [-1, 22], [0, 22], [-1, 22], [2, 22], [2, 22], [1, 22], [2, 21], [2, 22], [2, 22], [2, 22], [2, 21], [2, 22], [2, 22], [1, 21], [2, 22], [2, 22], [2, 22], [2, 21], [2, 22], [0, 6], [0, 2], [1, 2]], [[7070, 4109], [-7, 8]], [[7063, 4117], [-1, 2]], [[7062, 4119], [-1, 1], [1, 0], [12, 0], [12, 0], [13, 0], [12, -1], [12, 0], [13, 0], [12, 0], [12, 0], [13, 0], [12, -1], [12, 0], [13, 0], [12, 0], [12, 0], [13, 0], [12, -1]], [[7259, 4117], [2, -15], [2, -15], [2, -15], [2, -15], [2, -15], [2, -15], [2, -15], [2, -15], [2, -15], [2, -15], [2, -15], [2, -15], [2, -15], [2, -15], [2, -15], [3, -15], [1, -7], [2, -11]], [[7302, 3661], [4, -14]], [[7306, 3647], [-24, 0], [-25, 0], [-25, 0], [-25, 0], [-25, 0], [-25, 0], [-25, 0], [-25, 0], [-1, -9], [1, -8], [3, -7], [11, -15], [1, -3], [-2, -12], [0, -8], [-1, -4], [-3, -4], [-1, -4]], [[7052, 3557], [2, 1], [2, 1]], [[7068, 3561], [1, -1], [2, -1]], [[7071, 3559], [-3, -1], [-4, -2]], [[6945, 4235], [0, -2]], [[6900, 4118], [0, -1]], [[6900, 4117], [0, -3]], [[6834, 3887], [0, -3]], [[6834, 3884], [-15, 0], [-14, 0], [-14, 0], [-14, 0], [-13, 0], [-14, 0], [-14, 0], [-14, 0], [-14, 0], [-14, 0], [-13, 0], [-14, -1], [-14, 0], [-14, 0], [-14, 0], [-13, 0]], [[6612, 3883], [-1, 16], [0, 16], [0, 16], [0, 16], [-4, 3], [-7, 1], [-4, -1], [-4, 1], [-3, -3], [-2, 0], [-1, 1], [-1, 2], [-4, 2], [-4, 5]], [[6577, 3958], [1, 13], [0, 13], [0, 13], [0, 12], [1, 13], [0, 13], [0, 13], [0, 13], [0, 12], [1, 13], [0, 13], [0, 13], [0, 12], [1, 13], [0, 13], [0, 13], [-2, 16], [-2, 17], [-1, 16], [-2, 16], [-2, 17], [-1, 16], [-2, 16], [-2, 17]], [[6567, 4294], [22, 0], [21, 0], [21, 0], [22, 0], [21, 0], [22, 0], [21, 0], [22, 0], [21, 0], [21, 0], [22, 0], [21, 0], [22, 0], [21, 0], [21, 0], [22, 0], [3, -9], [4, -6], [0, -5], [0, -4], [-6, -10], [-5, -4], [-3, -5], [-4, -4], [-6, -13], [13, 0], [12, 1], [13, 0], [14, 0]], [[5457, 3685], [-17, 0]], [[5413, 3685], [-14, 0], [-14, 0]], [[5358, 3685], [-18, 0], [-9, 0]], [[5331, 3685], [-9, 0], [-18, 0]], [[5267, 3702], [-19, 9], [-17, 9]], [[5231, 3720], [-24, 11], [-12, 6]], [[5195, 3737], [-18, 8], [-19, 9]], [[5158, 3754], [-15, 8], [-21, 10]], [[5049, 3807], [-9, 4], [-28, 13]], [[5029, 4106], [0, 10]], [[5029, 4116], [0, 1]], [[5072, 4255], [1, 1], [0, 51], [0, 46]], [[5073, 4353], [24, 0], [24, 0], [24, 0], [24, 0], [24, 0], [24, 0], [24, 0], [24, 0], [24, 0], [24, 0], [24, 0], [24, 0], [24, 0], [24, 0], [24, 0], [24, 0]], [[5457, 4353], [0, -42], [0, -42], [0, -42], [0, -41], [0, -42], [0, -42], [0, -41], [0, -42], [0, -42], [0, -41], [0, -42], [0, -42], [0, -42], [0, -41], [0, -42], [0, -42]], [[5021, 3848], [-4, 0], [-5, -1]], [[5012, 3847], [-16, -2], [-6, 0]], [[4968, 3842], [-11, -1], [-11, -1]], [[4946, 3840], [-19, -3], [-3, 0]], [[4902, 3835], [-10, -2], [-12, -1]], [[4880, 3832], [-18, -2], [-4, 0]], [[4835, 3841], [-1, 1], [-2, 3]], [[4832, 3845], [-2, -1], [-3, -2]], [[4827, 3842], [-1, 10], [-1, 7]], [[4821, 3894], [-9, 18], [-2, 5]], [[4810, 3917], [-20, 22], [-5, 6]], [[4785, 3945], [0, 1], [-13, 9]], [[4763, 3967], [-3, 1], [-4, 2]], [[4748, 3971], [-1, -2], [-1, -3]], [[4746, 3966], [-6, 2], [-3, 1]], [[4705, 4002], [-5, 2], [-19, 9]], [[4671, 4030], [-8, 7], [-3, 2]], [[4645, 4049], [-7, -2], [-1, 0]], [[4637, 4047], [-10, 1], [-1, 0]], [[4626, 4048], [-2, 1], [-13, 6]], [[4584, 4054], [-3, 0], [-3, 1]], [[4578, 4055], [-3, 4], [-3, 5]], [[4564, 4111], [1, 8], [1, 7]], [[4561, 4136], [-6, 3], [-6, 3]], [[4549, 4142], [0, 2], [-2, 6]], [[4547, 4150], [1, 8], [1, 2]], [[4536, 4174], [-7, 11], [-2, 4]], [[4527, 4189], [-2, 1], [-9, 7]], [[4516, 4197], [-2, 6], [-2, 7]], [[4468, 4286], [-1, 14], [0, 2]], [[4474, 4312], [2, 5], [1, 4]], [[4477, 4321], [0, 6], [0, 2]], [[4477, 4329], [0, 1], [-1, 5]], [[4476, 4335], [-3, 5], [-3, 5]], [[4448, 4351], [-2, 4], [-15, 22]], [[4431, 4377], [-1, 10], [0, 9]], [[4432, 4445], [-1, -4], [0, -1]], [[4438, 4422], [4, -2], [2, -1]], [[4444, 4419], [1, -2], [3, -5]], [[4452, 4416], [-2, 7], [-1, 3]], [[4433, 4461], [-1, 3], [-1, 2]], [[4431, 4466], [4, 4], [2, 1]], [[4454, 4476], [4, 0], [25, -2]], [[4489, 4477], [3, 0], [2, -1]], [[4480, 4480], [-3, -1], [-7, 0]], [[4462, 4484], [-2, 0], [-1, 1]], [[4459, 4485], [-5, -3], [-5, -4]], [[4431, 4487], [-6, -4], [-1, 0]], [[4424, 4483], [0, -2], [-1, -16]], [[4423, 4465], [2, -11], [0, -3]], [[4425, 4451], [-3, -1], [-1, 0]], [[4416, 4455], [-6, 3], [-1, 1]], [[4403, 4464], [-3, 3], [-6, 6]], [[4384, 4469], [0, 2], [0, 1]], [[4387, 4482], [-1, 6], [0, 9]], [[4386, 4497], [5, -8], [3, -4]], [[4391, 4493], [-1, 2], [-5, 8]], [[4330, 4577], [0, 2], [-1, 22]], [[4321, 4631], [2, 12], [1, 5]], [[4324, 4661], [-1, 3], [-3, 15]], [[4316, 4689], [-1, 2], [-16, 26]], [[4299, 4717], [-13, 14], [-4, 4]], [[4282, 4735], [-2, 11], [0, 3]], [[4279, 4763], [3, 10], [0, 3]], [[4282, 4776], [1, 3], [2, 10]], [[4285, 4789], [2, 2], [1, 1]], [[4291, 4793], [1, 2], [1, 1]], [[4290, 4798], [1, 2], [1, 2]], [[4296, 4841], [5, 21], [1, 6]], [[4302, 4868], [0, 4], [0, 5]], [[4302, 4877], [-3, 15], [-1, 4]], [[4295, 4908], [-1, 0], [-6, 8]], [[4291, 4928], [0, 4], [0, 7]], [[4290, 4941], [21, 0], [20, 0], [20, 0], [21, 0], [20, 0], [20, 0], [20, 0], [21, 0], [20, 0], [20, 0], [20, 0], [21, 0], [20, 0], [20, 0], [21, 0], [20, 0]], [[4615, 4941], [0, -22], [0, -22], [0, -22], [0, -22], [0, -23], [0, -22], [0, -22], [0, -22], [0, -22], [0, -22], [0, -22], [0, -22], [0, -22], [0, -22], [0, -22], [0, -22], [18, -19], [18, -20], [18, -19], [18, -19], [18, -19], [18, -20], [18, -19], [18, -19], [14, -16], [14, -16], [14, -17], [14, -16], [15, -16], [14, -16], [14, -17], [14, -16], [19, -23], [20, -23], [19, -24], [20, -23], [20, -23], [19, -24], [20, -23], [20, -25]], [[4624, 3999], [-1, 3], [0, 1]], [[4588, 4007], [2, -3], [1, -1]], [[4612, 3990], [-2, -1], [-4, -1]], [[4606, 3988], [-2, 1], [-2, 1]], [[4602, 3990], [-3, 5], [-3, 6]], [[4609, 4003], [4, -3], [2, -2]], [[4746, 3919], [-3, 0], [-3, 1]], [[4733, 3924], [-1, 3], [-1, 4]], [[4730, 3932], [-1, 0], [-3, 1]], [[4726, 3938], [11, -6], [2, -1]], [[4650, 3908], [-1, 4], [-1, 3]], [[4732, 3863], [-2, 6], [-2, 5]], [[4728, 3874], [-4, 8], [-1, 1]], [[4723, 3883], [2, 2], [1, 1]], [[4730, 3877], [6, -8], [3, -5]], [[5457, 4353], [0, 29], [0, 29], [0, 30], [0, 29], [0, 30], [0, 29], [0, 29], [0, 30], [0, 29], [0, 30], [0, 29], [0, 30], [0, 29], [0, 29], [0, 30], [0, 29]], [[5457, 4823], [25, 0], [24, 0], [24, 0], [24, 0], [24, 0], [24, 0], [25, 0], [24, 0], [24, 0], [24, 0], [24, 0], [24, 0], [24, 0], [25, 0], [24, 0], [24, 0]], [[5844, 4823], [19, 0], [19, 0], [19, 0], [20, 0], [19, 0], [19, 0], [19, 0], [19, 0], [0, -15], [0, -14], [0, -15], [0, -15], [1, -14], [0, -15], [0, -15], [0, -14]], [[5998, 4706], [0, -23], [0, -22], [0, -22], [0, -22], [0, -22], [0, -22], [0, -22], [0, -22], [0, -22], [0, -22], [0, -22], [0, -22], [0, -22], [0, -22], [0, -22], [0, -22]], [[5998, 4353], [-19, 0], [-19, 0], [-19, 0], [-19, 0]], [[5922, 4353], [-29, 0], [-29, 0], [-29, 0], [-29, 0], [-29, 0], [-29, 0], [-29, 0], [-29, 0], [-29, 0], [-29, 0], [-29, 0], [-29, 0], [-29, 0], [-29, 0], [-30, 0], [-29, 0]], [[8322, 4942], [0, -6], [0, -9], [0, -6], [0, -9], [0, -10], [1, -10], [0, -8], [-1, -5], [0, -7], [-2, -3], [-1, -6]], [[8301, 4861], [-7, -1], [-8, -3]], [[8286, 4857], [-6, 2], [-2, 1]], [[8181, 4822], [-4, 7], [-3, 6], [7, 5], [4, 3], [5, 4], [3, 2], [-3, 4], [-2, 5], [1, 11], [0, 11], [1, 11], [1, 12], [0, 11], [1, 11], [0, 11], [1, 11]], [[8193, 4947], [6, 0], [7, 0], [6, -1], [7, 0], [6, 0], [7, -1], [6, 0], [7, 0], [0, -3], [3, 0], [1, 3], [9, 0], [9, -1], [9, 0], [9, 0], [10, 0], [9, 0], [9, 0], [9, -1], [0, -1]], [[7914, 4580], [-1, 1]], [[7913, 4581], [6, 8], [4, -7], [5, -7], [-7, -10], [-1, 10]], [[8073, 4524], [-8, 0], [-17, 0], [-17, 0], [-8, 0], [-1, 0], [-1, 18], [-1, 19], [-1, 19], [0, 18], [-1, 19], [-1, 18], [0, 19], [-1, 19]], [[8016, 4673], [6, 9], [2, 3], [3, 2], [10, 0], [7, -3]], [[8044, 4684], [-2, -2], [-2, -2]], [[8037, 4672], [-2, -3], [-4, -6]], [[8031, 4663], [0, -2], [0, -4]], [[8032, 4653], [0, -7], [0, -2]], [[8032, 4644], [2, -5], [2, -4]], [[8036, 4635], [2, -3], [6, -11]], [[8044, 4621], [1, -9], [1, -13]], [[8052, 4584], [8, -14], [2, -3]], [[8062, 4567], [1, -1], [6, -4]], [[8071, 4538], [1, -5], [1, -4]], [[7306, 3647], [4, -9]], [[7316, 3614], [7, -1], [12, -1], [13, -1], [12, -1], [12, -1], [12, -1], [13, -1], [12, -2], [12, -1], [12, -1], [13, -1], [12, -1], [12, -1], [12, -1], [13, -1], [12, -1], [12, -1], [0, -4], [2, -6], [1, -7], [1, -4], [2, -2], [4, 0], [4, 4], [2, 8], [1, 10], [-1, 11], [0, 9], [2, 6], [2, 1], [2, 3], [2, 0], [4, -1], [16, -8], [13, -1]], [[7576, 3615], [1, -3], [2, -8]], [[7579, 3604], [3, -20], [3, -23]], [[7622, 3417], [18, -39], [8, -19]], [[7651, 3351], [-1, -2], [-2, -5]], [[7656, 3282], [-2, 3], [-1, 1]], [[7645, 3355], [-1, 0], [-1, 0]], [[7643, 3355], [-1, -3], [-1, -5]], [[7641, 3347], [0, -7], [-1, -2]], [[7640, 3338], [0, -1], [-2, -3]], [[7638, 3334], [-2, 10], [-1, 4]], [[7635, 3348], [0, 6], [0, 1]], [[7635, 3355], [1, 1], [3, 6]], [[7638, 3364], [-3, 2], [-2, 2]], [[7632, 3380], [-1, 2], [-2, 1]], [[7629, 3383], [-1, 0], [-1, 0]], [[7631, 3360], [1, -6], [2, -15]], [[7639, 3326], [1, -6], [2, -5]], [[7642, 3315], [13, -46], [19, -68]], [[7683, 3074], [-1, -18], [0, -17]], [[7681, 3040], [0, 2], [0, 2]], [[7668, 3014], [0, -7], [-2, -16]], [[7666, 2991], [-1, -4], [-2, -7]], [[7649, 2968], [-8, -5], [-6, -4]], [[7635, 2959], [-3, 1], [-7, 2]], [[7614, 2957], [-2, 0], [-6, 0]], [[7606, 2957], [-4, 10], [0, 1]], [[7602, 2968], [0, 3], [0, 2]], [[7604, 2977], [2, 1], [1, 0]], [[7607, 2978], [8, -8], [2, -3]], [[7616, 2978], [-4, 2], [-2, 1]], [[7597, 3010], [-4, 8], [-5, 9]], [[7588, 3027], [-1, 7], [0, 5]], [[7546, 3129], [1, 1], [4, 9]], [[7544, 3133], [-2, -5], [-2, -5]], [[7537, 3122], [-1, 1], [-1, 0]], [[7535, 3123], [-3, 15], [0, 3]], [[7530, 3172], [-4, -2], [-2, -1]], [[7524, 3169], [0, -1], [1, -7]], [[7524, 3157], [-4, 1], [-1, 0]], [[7515, 3161], [-2, 4], [-3, 4]], [[7490, 3223], [0, 1], [-3, 5]], [[7487, 3229], [-2, 3], [-2, 3]], [[7485, 3237], [2, 0], [2, 1]], [[7489, 3238], [5, 10], [4, 8]], [[7507, 3274], [0, 3], [-1, 1]], [[7503, 3282], [-2, -2], [-2, -2]], [[7493, 3289], [-1, 1], [-3, 2]], [[7483, 3262], [-1, 0], [-1, -2]], [[7476, 3268], [-2, 5], [-1, 3]], [[7473, 3276], [5, 34], [2, 12]], [[7487, 3351], [0, 11], [1, 22]], [[7488, 3384], [0, 3], [0, 2]], [[7488, 3389], [0, 5], [0, 4]], [[7479, 3417], [-39, 46], [-2, 2]], [[7407, 3520], [-17, 14], [-10, 7]], [[7380, 3541], [-10, -2], [-10, -2]], [[7357, 3532], [-1, -3], [-1, -2]], [[7355, 3527], [1, -3], [0, -3]], [[7356, 3521], [0, -1], [-2, -2]], [[7354, 3518], [-3, 0], [-2, 0]], [[7322, 3502], [-3, 1], [-3, 0]], [[7278, 3493], [0, 1], [-3, 8]], [[7283, 3501], [0, 1], [0, 3]], [[7268, 3526], [-10, 13], [-3, 4]], [[7255, 3543], [1, 0], [3, 0]], [[7259, 3543], [0, 1], [1, 3]], [[7260, 3547], [-2, 2], [-2, 2]], [[7258, 3557], [0, 1], [3, 5]], [[7261, 3563], [-5, -1], [-1, 0]], [[7255, 3562], [-2, -1], [-3, -3]], [[7241, 3554], [-11, 7], [-13, 7]], [[7217, 3568], [-5, 2], [-17, 6]], [[7195, 3576], [10, 2], [7, 2]], [[7210, 3587], [-1, 0], [-8, -1]], [[7201, 3586], [-4, 1], [-1, 0]], [[7190, 3584], [-3, -3], [-4, -2]], [[7183, 3579], [-2, -1], [-3, -2]], [[7178, 3576], [-13, -2], [-9, -1]], [[7151, 3594], [-3, -5], [0, -1]], [[7148, 3588], [-2, 2], [-2, 2]], [[7144, 3592], [-1, 0], [-3, 0]], [[7307, 3485], [5, 3], [4, 3]], [[7327, 3498], [-10, -9], [-3, -2]], [[7678, 3200], [-2, 6], [-5, 14]], [[7671, 3220], [-5, 20], [-3, 12]], [[7525, 3141], [1, -1], [2, -3]], [[7531, 3123], [0, -4], [0, -2]], [[7529, 3113], [3, -1], [3, 0]], [[7649, 2941], [5, 10], [1, 2]], [[7655, 2953], [0, 1], [1, 4]], [[7656, 2958], [3, 3], [1, 1]], [[7660, 2962], [1, 0], [3, 6]], [[7662, 2958], [-3, -6], [-12, -16]], [[7640, 2929], [1, 2], [2, 3]], [[7608, 2905], [-3, 1], [-1, 1]], [[7620, 2913], [-4, -3], [-1, -1]], [[7582, 2909], [0, 1], [0, 1]], [[7582, 2911], [4, -3], [4, -4]], [[7590, 2904], [0, -1], [0, -1]], [[7590, 2902], [0, -1], [-1, -1]], [[7589, 2900], [-1, -2], [-1, -1]], [[7571, 2904], [2, -3], [1, -2]], [[7574, 2899], [0, -2], [0, -2]], [[7552, 2889], [2, 1], [2, 1]], [[7558, 2891], [0, -2], [0, -1]], [[7259, 4117], [25, 0], [25, 0], [25, -1], [25, 0]], [[7359, 4116], [23, 0], [23, 1], [24, 0], [23, 0]], [[7452, 4117], [-1, 0]], [[7599, 3730], [1, -1], [0, -4]], [[7596, 3714], [-1, -2], [0, -2]], [[7601, 3709], [-1, -2], [-2, -5]], [[7592, 3690], [-4, -1], [-3, -1]], [[7585, 3688], [0, -1], [1, -1]], [[7589, 3684], [1, -1], [2, -5]], [[7578, 3648], [-1, -6], [-1, -5]], [[7576, 3637], [-1, -2], [0, -3]], [[7575, 3632], [0, -3], [0, -6]], [[7575, 3623], [0, -5], [1, -3]], [[7580, 3641], [1, 1], [1, 1]], [[7582, 3643], [-1, -11], [-2, -17]], [[1580, 2573], [-2, 2], [-2, 1]], [[1592, 2606], [0, -1], [0, -5]], [[1592, 2600], [-1, -4], [-1, -2]], [[1590, 2594], [-1, -4], [0, -5]], [[1589, 2585], [-1, -3], [-1, -2]], [[1520, 2570], [0, 3], [1, 3]], [[1521, 2576], [3, 3], [2, 2]], [[1526, 2581], [3, 5], [2, 4]], [[1535, 2589], [-1, -3], [-2, -3]], [[1524, 2569], [-1, -4], [0, -1]], [[1714, 2524], [0, -3], [1, -6]], [[1704, 2505], [-3, 3], [-1, 2]], [[1700, 2510], [-3, -1], [-2, 0]], [[1695, 2514], [-1, 0], [0, 1]], [[1694, 2515], [-2, -1], [-1, 0]], [[1691, 2514], [2, -4], [1, -2]], [[1694, 2508], [-1, -1], [-7, 0]], [[1674, 2528], [0, 3], [0, 2]], [[1674, 2533], [-1, 3], [-2, 3]], [[1671, 2539], [5, 1], [7, 1]], [[1683, 2541], [7, 9], [1, 2]], [[1691, 2552], [2, 0], [2, 1]], [[1710, 2523], [2, 1], [2, 0]], [[1773, 2494], [2, -2], [0, -1]], [[1775, 2491], [8, -1], [6, 0]], [[1789, 2490], [1, -1], [1, 0]], [[1788, 2483], [-2, -2], [-6, -4]], [[1748, 2488], [1, 3], [1, 1]], [[1750, 2497], [1, 0], [2, -1]], [[1769, 2492], [2, 1], [2, 1]], [[1811, 2460], [4, 2], [4, 1]], [[1825, 2465], [6, -6], [4, -2]], [[1838, 2429], [-8, -2], [-2, 0]], [[1828, 2427], [-2, -1], [-4, -3]], [[1804, 2446], [-4, 3], [-1, 0]], [[1799, 2449], [-3, 5], [-3, 5]], [[1793, 2459], [0, 2], [-1, 3]], [[1792, 2464], [1, 3], [2, 6]], [[1795, 2473], [4, 1], [2, 0]], [[1801, 2474], [2, -2], [2, -3]], [[1805, 2469], [3, -5], [1, -2]], [[1770, 2450], [-1, 1], [-4, 9]], [[1774, 2462], [3, -2], [1, -1]], [[1781, 2456], [1, -2], [2, -4]], [[1784, 2450], [-1, 0], [-2, -6]], [[1776, 2440], [-1, 0], [-4, 2]], [[1855, 2243], [0, 1], [-2, 6]], [[1853, 2250], [1, 17], [0, 13]], [[1842, 2323], [3, 6], [2, 4]], [[1853, 2340], [6, 12], [1, 2]], [[1854, 2372], [1, 8], [1, 3]], [[1916, 2338], [0, -1], [2, -14]], [[1918, 2323], [2, -1], [4, -1]], [[1924, 2321], [1, -6], [2, -4]], [[1935, 2302], [2, -3], [1, -2]], [[1919, 2273], [-5, -2], [-15, -5]], [[1899, 2266], [-2, -3], [-15, -15]], [[1875, 2231], [-3, 0], [-1, 0]], [[6811, 4750], [-6, 6], [-1, 5], [-6, 4], [-1, 3], [-4, 2], [-4, 9], [-19, 0], [-20, 0], [-19, -1], [-19, 0], [-20, 0], [-19, 0], [-20, 0], [-19, 0], [-19, -1], [-20, 0], [-19, 0], [-20, 0], [-19, 0], [-19, 0], [-20, -1], [-19, 0]], [[6479, 4776], [-5, 9]], [[6426, 5000], [-2, 1]], [[6424, 5001], [-2, 10], [-2, 4], [-1, 3], [-6, 9], [0, 3], [1, 4], [4, 8], [3, 13], [1, 8], [3, 7], [0, 4], [0, 4], [-2, 4], [-5, 4], [0, 1], [-1, 5], [2, 3], [1, 3], [-1, 4], [-3, 7], [-1, 8], [11, 0]], [[6426, 5117], [25, 0], [25, 0], [25, 0], [25, 0], [25, 0], [25, 0], [25, 0], [25, 0], [25, 0], [25, 0], [25, 0], [25, 0], [25, 0], [25, 0], [25, 0], [26, 0]], [[6827, 5117], [0, -7]], [[6872, 5002], [0, -1]], [[6872, 5001], [5, -8]], [[4919, 5763], [0, -29], [0, -29], [0, -29], [0, -29], [8, -12], [6, -7], [4, -8], [6, -8], [1, -3], [2, -9], [-1, -6], [3, -7], [-1, -3], [-2, -1], [0, -1], [0, -1], [0, -1], [9, -6], [8, -8], [11, -6], [3, -4], [14, -17], [7, -11], [7, -8], [4, -9], [8, -8], [2, -4], [0, -1], [1, 0], [5, 1], [2, 0], [2, -3], [0, -5], [2, -2], [3, -1], [4, -1], [10, 3], [2, 0], [2, -1], [0, -3], [0, -4], [-1, -6], [-3, -6], [0, -5], [-2, -13], [-3, -9], [0, -8], [-3, -5], [2, -6], [-1, -9], [1, -2], [3, -4], [2, -11], [-1, -2], [-6, -4], [-3, -3], [-1, -3], [3, -12], [-3, -6], [0, -5], [1, -2], [6, -3], [7, -6], [3, -1], [2, 0], [3, 2], [3, 4], [7, 5], [9, 9], [0, 3], [1, 1], [1, 0], [3, -1], [5, -6], [5, -3], [1, -1], [0, -1], [0, -6], [2, -4], [1, -8], [3, -7], [2, -9], [7, -11], [3, -8], [5, -4], [2, -4], [2, -5], [1, -4], [-3, -8], [1, -3], [2, -3], [7, -8], [2, -1], [6, 1], [4, -1], [4, -4], [3, -6], [3, -6], [0, -7], [4, -8], [0, -7], [3, -3], [4, -4], [5, -2], [1, 0], [2, 1], [0, 3], [2, 3], [5, 5], [5, -1], [20, -3], [1, 0], [1, 1], [4, 7], [2, 2], [4, 1], [13, -2], [14, 1], [7, -2], [10, 3], [13, -2], [1, 1], [1, 1], [-1, 3], [-1, 4], [3, 6], [1, 5], [2, 2], [4, 2], [2, 0], [3, -3], [3, -4], [5, -12], [3, -4], [3, -3], [5, -3]], [[5303, 5235], [0, -19], [0, -18], [0, -18], [0, -19], [0, -18], [0, -19], [0, -18], [0, -18], [0, -19], [0, -18], [0, -18], [0, -19], [0, -18], [0, -18], [0, -19], [0, -18]], [[5303, 4941], [-14, 0], [-14, 0], [-15, 0], [-14, 0], [-15, 0], [-14, 0], [-14, 0], [-15, 0], [-14, 0], [-15, 0], [-14, 0], [-14, 0], [-15, 0], [-14, 0], [-14, 0], [-15, 0]], [[5073, 4941], [-28, 0], [-29, 0], [-29, 0], [-28, 0], [-29, 0], [-28, 0], [-29, 0], [-29, 0]], [[4844, 4941], [0, 26], [0, 26], [0, 27], [0, 26], [0, 26], [0, 26], [0, 27], [0, 28]], [[4844, 5153], [0, 1], [5, 18]], [[4856, 5406], [-2, 5]], [[4854, 5411], [-3, 7]], [[4843, 5458], [0, 3], [1, 1], [0, 19], [0, 18], [0, 19], [-1, 19], [0, 19], [0, 19], [0, 19], [0, 19], [0, 18], [0, 19], [0, 19], [0, 19], [0, 19], [0, 19], [0, 19], [0, 18]], [[4843, 5763], [24, 0]], [[4900, 5763], [19, 0]], [[6872, 5001], [36, 0], [34, -1], [35, 0], [35, 0], [34, 0], [35, -1], [10, 0]], [[7091, 4999], [0, -18]], [[7110, 4913], [3, -4]], [[7113, 4909], [0, -32], [0, -35], [0, -35], [0, -36], [-1, -35], [0, -35], [0, -35], [0, -34], [-3, -4]], [[7113, 4909], [1, -1]], [[7148, 4902], [16, 11]], [[7164, 4913], [19, 0], [23, -1], [23, 0], [24, 0], [23, 0], [23, 0], [24, 0], [0, -6]], [[7323, 4906], [-1, -39], [0, -38], [0, -38], [0, -39], [-1, -38], [0, -38], [0, -38], [0, -39]], [[7321, 4599], [-2, -1]], [[5998, 4706], [16, 0], [16, 0], [16, 0], [16, 0], [16, 0], [16, 0], [16, 0], [16, 0], [17, 0], [16, 0], [16, 0], [16, 0], [16, 0], [16, 0], [16, 0], [16, 0], [17, 0], [16, 0], [16, 0], [16, 0], [16, 0], [16, 0], [16, 0], [16, 0], [17, 0], [16, 0], [16, 0], [16, 0], [16, 0], [16, 0], [16, 0], [14, 0]], [[6511, 4706], [18, -16]], [[6567, 4602], [0, -4], [0, -15], [0, -16], [0, -15], [0, -15], [0, -16], [0, -15], [0, -15], [0, -16], [0, -15], [0, -15], [0, -16], [0, -15], [0, -15], [0, -16], [0, -15], [0, -15]], [[6567, 4353], [-18, 0], [-17, 0], [-18, 0], [-18, 0], [-18, 0], [-17, 0], [-18, 0], [-18, 0], [-18, 0], [-18, 0], [-17, 0], [-18, 0], [-18, 0], [-18, 0], [-17, 0], [-18, 0], [-18, 0], [-18, 0], [-18, 0], [-17, 0], [-18, 0], [-18, 0], [-18, 0], [-17, 0], [-18, 0], [-18, 0], [-18, 0], [-17, 0], [-18, 0], [-18, 0], [-18, 0], [-18, 0]], [[7321, 4599], [4, 3]], [[7490, 4523], [1, 0]], [[7491, 4523], [1, -3], [1, -18], [0, -3], [-3, -7], [0, -4], [2, -6], [9, -14], [0, -5], [4, -5], [2, -8], [9, -17], [14, -13], [10, -4]], [[7540, 4416], [-29, -29], [-13, -10], [-13, -8], [-1, -2], [-3, -9], [-7, -8], [-5, -9], [-12, -7], [-8, -9], [-20, -10], [-12, -3], [-8, -6]], [[7409, 4306], [-1, 0], [0, -1], [-1, 0], [-1, 0], [0, -1], [-1, 0], [-27, 1], [-27, 1], [-26, 1], [-27, 1], [-27, 2], [-27, 1], [-26, 1], [-27, 1], [-26, -1], [-25, 0], [-26, -1], [-25, 0], [-3, 3], [-6, 1], [-11, 1], [3, -12]], [[7072, 4304], [0, -10], [-12, 0], [-14, 0], [-13, 0], [-13, 0], [-13, 0], [-14, 0], [-13, 0], [-15, 0]], [[6965, 4294], [4, 10]], [[6962, 4294], [-6, 0]], [[6956, 4294], [0, 1]], [[6961, 4297], [1, -3]], [[6834, 3884], [0, -6]], [[6795, 3650], [0, -3], [18, 0], [19, 0], [18, 0], [19, 0], [18, 0], [19, 0], [18, 0], [19, 0], [-3, -14], [-5, -18], [0, -5], [1, -6], [0, -3], [2, -5], [5, -5], [4, -8], [3, -13], [1, -6], [3, -9], [1, -1], [2, -1], [2, -1]], [[6919, 3570], [-3, 1], [-3, 1]], [[6913, 3572], [-5, 1], [-3, 1]], [[6905, 3574], [-4, -7], [-4, -5]], [[6931, 3544], [3, 0], [3, 0]], [[6940, 3545], [1, 2], [2, 2]], [[6943, 3549], [2, -1], [3, -2]], [[6948, 3543], [-2, -3], [-2, -2]], [[6944, 3538], [-3, -2], [-1, -2]], [[6940, 3534], [-3, -3], [0, -1]], [[6937, 3530], [3, -6], [2, -3]], [[6968, 3534], [0, -1], [-1, -3]], [[6972, 3520], [0, -5], [-1, -5]], [[6964, 3504], [-5, -2], [-1, 0]], [[6956, 3494], [-3, -2], [-1, -1]], [[6952, 3491], [-2, 0], [-2, 1]], [[6947, 3491], [-1, -3], [0, -1]], [[6947, 3475], [2, -1], [6, -5]], [[6955, 3469], [1, -3], [4, -5]], [[6980, 3451], [1, 0], [4, 0]], [[6998, 3435], [0, -5], [0, -2]], [[6989, 3417], [-1, -2], [-1, -2]], [[6987, 3413], [-2, 4], [-1, 1]], [[6984, 3418], [-1, 1], [-2, 2]], [[6974, 3411], [-3, -1], [-1, -1]], [[6969, 3424], [-2, 6], [-2, 4]], [[6959, 3441], [-2, 1], [-2, 1]], [[6952, 3447], [-2, 1], [-2, 1]], [[6948, 3449], [-1, 0], [-3, -1]], [[6938, 3456], [-1, 3], [-1, 1]], [[6932, 3465], [-9, 4], [-13, 6]], [[6912, 3468], [1, -1], [2, -1]], [[6915, 3466], [2, -2], [1, -2]], [[6918, 3462], [0, -4], [0, -7]], [[6903, 3427], [-3, 13], [-1, 2]], [[6899, 3442], [-1, 0], [-5, 4]], [[6893, 3446], [-6, 1], [-3, 0]], [[6870, 3429], [-1, -1], [-4, -1]], [[6865, 3427], [-18, 7], [-2, 0]], [[6845, 3434], [-3, 2], [-19, 9]], [[6823, 3445], [1, 3], [0, 1]], [[6824, 3449], [1, 1], [2, 0]], [[6827, 3450], [3, 0], [4, -1]], [[6834, 3449], [0, 2], [0, 2]], [[6834, 3453], [-3, 3], [-4, 9]], [[6825, 3471], [1, 4], [0, 3]], [[6802, 3483], [-3, 5], [-5, 11]], [[6794, 3499], [-4, 0], [-8, 1]], [[6782, 3500], [-3, 6], [-2, 4]], [[6777, 3510], [-9, -4], [-1, 0]], [[6762, 3481], [-2, 0], [-12, -4]], [[6717, 3482], [-1, 0], [-8, 4]], [[6708, 3486], [-7, 5], [-6, 4]], [[6678, 3503], [-3, 1], [-5, 0]], [[6670, 3504], [-3, 0], [-5, -1]], [[6662, 3503], [-20, -1], [-4, 0]], [[6633, 3500], [-3, -2], [-2, -1]], [[6625, 3500], [-1, 4], [0, 3]], [[6626, 3508], [3, 3], [0, 1]], [[6629, 3512], [1, 2], [2, 5]], [[6632, 3519], [0, 3], [0, 1]], [[6631, 3526], [5, 12], [1, 4], [-1, 20], [-2, 8], [0, 4], [3, 11], [-1, 10], [4, 12], [4, 6], [3, 13], [1, 8], [1, 7], [-1, 6], [3, 5], [-2, 7], [0, 9], [-3, 3], [-6, 14], [1, 5], [-6, 13], [0, 4], [-5, 7], [-1, 4], [-1, 17], [-1, 5], [-5, 10], [-11, 14], [0, 15], [0, 15], [0, 15], [0, 15], [0, 15], [1, 15], [0, 14], [0, 15]], [[6982, 3533], [-1, 1], [-2, 2]], [[6979, 3536], [-5, 0], [-1, 0]], [[6977, 3540], [1, 1], [0, 1]], [[6978, 3542], [1, 1], [6, 6]], [[6985, 3549], [-1, -3], [-1, -2]], [[7013, 3506], [-1, -1], [-2, -2]], [[7010, 3503], [2, 10], [1, 8]], [[7013, 3521], [-2, 5], [-1, 10]], [[7010, 3536], [1, -2], [2, -5]], [[7013, 3529], [0, -3], [1, -5]], [[7014, 3521], [-1, -5], [0, -10]], [[7009, 3500], [0, -1], [-1, -4]], [[6769, 3479], [-1, 2], [0, 2]], [[6774, 3487], [2, 0], [2, 0]], [[6778, 3487], [3, -2], [3, -3]], [[6787, 3478], [0, -1], [0, -2]], [[8193, 4947], [-2, 3], [2, 10], [3, 10], [2, 10], [3, 10], [2, 10], [3, 9], [2, 10], [2, 10]], [[8210, 5029], [8, 0], [8, 0], [7, -1], [8, 0], [7, 0], [8, -1], [7, 0], [8, 0]], [[8271, 5027], [11, -1], [11, 0], [11, -1], [11, 0], [11, 0], [11, -1], [10, 0], [11, -1], [7, 4], [8, 9], [5, 2], [8, 5], [4, 1], [9, 0]], [[8413, 5020], [0, -2], [1, -1]], [[8414, 5017], [0, -1], [0, -2]], [[8410, 5013], [-2, -1], [-5, -4]], [[8397, 5006], [-1, -3], [-2, -4]], [[8380, 4980], [2, -3], [2, -1]], [[8384, 4976], [5, -2], [9, -2]], [[8404, 4968], [5, -13], [4, -10]], [[8411, 4943], [-1, -3], [0, -1]], [[8410, 4939], [5, -3], [3, -3]], [[8428, 4912], [9, -3], [1, 0]], [[8438, 4909], [2, 0], [10, 5]], [[8450, 4914], [7, 4], [4, 2]], [[8461, 4920], [-1, 4], [0, 2]], [[8446, 4945], [-1, 1], [-2, 3]], [[8442, 4951], [2, 1], [1, 1]], [[8448, 4952], [1, 0], [3, -2]], [[8462, 4936], [2, -10], [1, -8]], [[8465, 4918], [1, -8], [0, -3]], [[8466, 4907], [-1, -3], [0, -1]], [[8462, 4904], [-4, -1], [-2, 0]], [[8456, 4903], [-8, -2], [-18, -4]], [[8424, 4892], [-11, -5], [-3, -1]], [[8386, 4888], [-5, -1], [-3, -1]], [[8378, 4886], [-5, -4], [-2, -1]], [[8368, 4898], [2, 7], [2, 6]], [[8366, 4906], [-3, 5], [-3, 3], [-2, 3], [0, 4], [0, 7], [-3, 1], [-1, 8], [0, 6], [-6, 0], [-5, -1], [-7, 0], [-8, 0], [-6, 0]], [[8402, 4867], [2, 2], [5, 7]], [[8420, 4872], [1, -2], [0, -3]], [[8443, 4857], [5, 1], [6, 2]], [[8454, 4860], [1, 1], [1, 1]], [[8457, 4867], [0, 1], [1, 2]], [[8458, 4870], [2, -8], [2, -4]], [[7913, 4581], [-5, 3]], [[7871, 4624], [-5, 5]], [[7866, 4629], [-1, 0]], [[7801, 4650], [-1, 1], [-7, 1], [-5, 4], [-1, 1], [1, 3], [-1, 1], [-1, 0], [-2, -1], [-1, -5], [-12, -14], [-6, 3], [-2, 0], [-17, -20], [-5, -3], [-10, -8], [0, 15], [0, 15], [1, 15], [0, 15]], [[7732, 4673], [17, 0], [18, 0], [18, 0], [18, 0], [17, 0], [18, 0], [18, 0], [18, 0], [17, 0], [18, 0], [18, 0], [18, 0], [18, 0], [17, 0], [18, 0], [18, 0]], [[8072, 4515], [-1, -1], [-1, -1]], [[8070, 4513], [0, 3], [0, 2]], [[8070, 4518], [0, 1], [-1, 1]], [[8066, 4515], [0, -3], [-1, -7]], [[8047, 4473], [-7, -1], [-12, -2], [-3, -5]], [[8025, 4465], [-2, 1], [-3, 1]], [[8012, 4474], [1, 2], [2, 4]], [[8010, 4487], [-1, 0], [-1, 0]], [[8002, 4504], [-1, 2], [-1, 3]], [[7998, 4505], [-2, -2], [-1, 0]], [[7990, 4508], [-7, 4], [0, 1]], [[7979, 4521], [0, 1], [-2, 6]], [[7983, 4559], [-3, 1], [-2, 1]], [[7973, 4554], [1, 3], [2, 9]], [[7985, 4578], [-3, 2], [-1, 1]], [[7975, 4589], [3, 0], [2, 0]], [[7985, 4587], [2, 6], [2, 4]], [[7983, 4595], [-1, 9], [-1, 6]], [[7993, 4631], [3, 0], [5, 0]], [[8001, 4631], [5, 1], [3, 0]], [[8009, 4632], [-4, 2], [-1, 1]], [[8004, 4635], [-1, 0], [-4, 1]], [[8009, 4648], [-1, 0], [-7, -1]], [[7993, 4643], [-1, -2], [0, -2]], [[7992, 4639], [-2, -2], [-2, -2]], [[7982, 4637], [-1, 1], [-1, 1]], [[7980, 4639], [0, -1], [-1, -9]], [[7972, 4633], [1, -1], [0, -1]], [[7972, 4626], [-2, -1], [-2, -1]], [[7967, 4614], [-4, 2], [-8, 3]], [[7955, 4618], [3, -5], [4, -7]], [[7962, 4606], [3, -2], [1, -1]], [[7966, 4603], [1, -4], [0, -3]], [[7967, 4596], [-1, -1], [-3, -4]], [[7956, 4595], [2, -3], [1, -4]], [[7959, 4588], [1, -3], [1, -4]], [[7961, 4581], [-1, -2], [-1, -3]], [[7959, 4576], [0, -3], [1, -4]], [[7959, 4563], [-1, -6], [2, -19], [1, -5]], [[7967, 4520], [1, -3], [1, -3]], [[7969, 4514], [-3, -1], [-1, 0]], [[7965, 4513], [-4, 4], [-1, 1]], [[7974, 4487], [-1, -2], [0, -5]], [[7968, 4485], [-2, 3], [-2, 3]], [[7964, 4491], [-9, 5], [-1, 1]], [[7940, 4501], [-5, 10], [-3, 5]], [[7931, 4505], [-3, 3], [-2, 3]], [[7926, 4511], [0, 1], [-3, 4]], [[7922, 4523], [-3, -1], [-3, 0]], [[7916, 4522], [-2, -2], [-4, -3]], [[7904, 4518], [0, 1], [0, 9]], [[7904, 4528], [1, 4], [0, 2]], [[7912, 4547], [4, 4], [2, 2]], [[7918, 4553], [1, 3], [2, 6]], [[7921, 4562], [-1, 9], [0, 4]], [[8059, 4475], [-2, 0]], [[8057, 4475], [1, 3], [1, 1]], [[8059, 4479], [5, 14], [2, 5]], [[8069, 4505], [-3, -10], [0, -4]], [[8066, 4491], [-2, -4], [-4, -8]], [[8550, 5553], [1, 0]], [[8551, 5553], [1, 0]], [[8575, 5562], [3, 0]], [[8578, 5562], [3, 2]], [[8586, 5568], [1, 1]], [[8616, 5552], [4, -3]], [[8620, 5549], [8, -9]], [[8630, 5495], [0, -11]], [[8630, 5484], [0, -4]], [[8630, 5456], [0, -5]], [[8631, 5406], [1, -2]], [[8632, 5402], [0, -4]], [[8632, 5398], [-1, -1]], [[8632, 5390], [-1, -2]], [[8630, 5381], [0, -2]], [[8630, 5378], [1, -2]], [[8631, 5376], [1, -1]], [[8654, 5349], [1, -3]], [[8657, 5343], [1, -4]], [[8658, 5335], [0, -1]], [[8658, 5334], [-1, -1]], [[8655, 5328], [0, -2]], [[8670, 5315], [1, 1]], [[8684, 5304], [0, -4], [1, -8]], [[8685, 5292], [-1, -1], [-1, -4]], [[8683, 5287], [0, -5], [0, -2]], [[8692, 5273], [-7, -8], [-8, -10]], [[8613, 5223], [-1, -1], [-2, -1]], [[8610, 5221], [-3, 6], [0, 1]], [[8607, 5228], [0, 2], [-1, 4]], [[8599, 5237], [-1, 0], [-2, 0]], [[8593, 5236], [-2, -3], [-1, -1]], [[8586, 5228], [-1, 1], [-3, 2]], [[8578, 5228], [-3, -5], [-1, -2]], [[8574, 5221], [1, -5], [0, -4]], [[8574, 5206], [-3, 2], [-1, 0]], [[8567, 5212], [-5, 3], [-3, 1]], [[8552, 5216], [1, 2], [0, 3]], [[8553, 5229], [2, 6], [1, 1]], [[8556, 5236], [0, 3], [0, 4]], [[8553, 5241], [-2, -5], [-2, -3]], [[8549, 5233], [-4, -3], [-4, -3]], [[8541, 5227], [0, -1], [0, -9]], [[8541, 5217], [-3, -8], [-5, -13]], [[8533, 5196], [-1, -8], [0, -1]], [[8532, 5187], [-1, -1], [-4, -6]], [[8501, 5165], [-2, -1], [-1, 0]], [[8496, 5172], [-1, 1], [0, 1]], [[8495, 5174], [-2, -7], [-1, -4]], [[8492, 5163], [0, -1], [-2, 0]], [[8490, 5162], [-1, 3], [0, 5]], [[8489, 5170], [-1, 5], [-1, 0]], [[8476, 5165], [0, -2], [0, -3]], [[8477, 5153], [-1, -1], [-1, -3]], [[8463, 5159], [-7, -2], [-9, -9]], [[8447, 5148], [-5, -7], [-2, -4]], [[8445, 5132], [-4, -5], [-8, -12]], [[8433, 5115], [-9, -12], [-3, -4]], [[8411, 5074], [-1, -1], [-2, -2]], [[8404, 5067], [-6, 11], [-1, 8], [-7, 11], [-3, 7], [-1, 8], [1, 9], [-1, 13], [-1, 13], [0, 13], [-1, 13], [0, 13], [-1, 13], [0, 13], [-1, 13], [-1, 13], [0, 13], [-1, 13], [0, 13], [-1, 13], [-1, 13], [0, 14], [-1, 11]], [[8391, 5325], [1, -1]], [[8392, 5324], [0, 1]], [[8407, 5347], [0, 1]], [[8429, 5388], [5, 6]], [[8438, 5400], [0, 1]], [[8437, 5412], [0, 6]], [[8437, 5418], [1, 6]], [[8455, 5462], [1, 1]], [[8456, 5463], [1, 10]], [[8470, 5509], [1, 1]], [[8471, 5510], [1, 2]], [[8510, 5570], [2, 3]], [[8517, 5580], [2, 3]], [[8519, 5583], [2, 0]], [[8533, 5579], [1, 0]], [[8533, 5566], [1, -5]], [[8591, 5214], [0, -4], [0, -2]], [[8590, 5205], [-3, 2], [-2, 2]], [[8583, 5211], [0, 3], [0, 5]], [[8562, 5206], [1, -1], [1, -1]], [[8564, 5204], [1, -2], [2, -3]], [[7496, 5016], [0, -2]], [[7496, 5014], [-7, -6]], [[7427, 4913], [-1, -2]], [[7426, 4911], [-1, -1], [-26, -1], [-25, -1], [-26, -1], [-25, -1]], [[7164, 4913], [5, 3]], [[7506, 5060], [-2, -7]], [[7107, 5306], [-4, 2], [-3, 5], [-2, 4], [0, 5], [4, 12], [1, 2], [-1, 1], [-2, 2], [-6, -3], [-6, 0], [-1, 1], [-1, 2], [0, 3], [1, 4], [3, 7], [0, 7], [2, 4], [-1, 7], [0, 3], [-2, 4], [-7, 5], [-15, 7], [2, 7], [-1, 3], [-3, 5], [-16, 5], [-11, 3], [-10, 0], [-8, 2], [-8, 1], [-6, 3], [-5, 3], [-5, 3], [-6, 4], [-9, 3], [-10, 2], [-10, 3], [-9, 3], [-10, 3], [-10, 3], [-9, 3], [-10, 2], [-2, 5], [-2, 5], [-2, 5], [-2, 5], [-5, 2], [-2, 1], [-2, 3], [-4, -1], [-1, 4], [0, 1]], [[6891, 5481], [5, 1]], [[7107, 5312], [0, -6]], [[6526, 5793], [0, 15]], [[6541, 5805], [1, 0]], [[6542, 5805], [4, -2]], [[6549, 5800], [0, -4]], [[6552, 5768], [1, -4]], [[6563, 5738], [2, -3]], [[6578, 5731], [5, -2]], [[6583, 5729], [11, -2]], [[6621, 5720], [5, -2]], [[6627, 5717], [3, -6]], [[6637, 5708], [10, 1]], [[6648, 5710], [2, 0]], [[6670, 5720], [2, 0]], [[6688, 5719], [2, 0]], [[6701, 5715], [3, -2]], [[6704, 5713], [1, 0]], [[6717, 5706], [7, -5]], [[6724, 5701], [3, -2]], [[6733, 5690], [3, -9]], [[6751, 5686], [4, 0]], [[6755, 5686], [6, -2]], [[6772, 5678], [7, -8]], [[6806, 5653], [7, 0]], [[6843, 5670], [9, 1]], [[6852, 5671], [3, 0]], [[6865, 5659], [10, 1]], [[6883, 5659], [15, -1]], [[6898, 5658], [7, 1]], [[6928, 5647], [2, -1]], [[6930, 5646], [3, 1]], [[6953, 5647], [2, 0]], [[6760, 5501], [-1, 0], [-5, -7], [-3, -4], [-5, -1], [0, -17], [0, -17], [0, -17], [0, -17], [-2, -2], [-4, -4], [-4, -1], [-21, -13], [-3, -5], [-4, -9], [-7, -10], [-1, -6], [0, -7], [10, -7], [4, -5], [2, -7], [0, -7], [-5, -12], [-1, -5], [0, -14], [-2, -5], [2, -12], [0, -4], [-2, -13], [-1, -4], [2, -7], [0, -1], [10, -9]], [[6824, 5152], [3, -35]], [[6426, 5117], [0, 27], [0, 26], [0, 27], [0, 26], [0, 26], [0, 27], [0, 26], [0, 27], [-6, 8], [-13, 7], [-3, 5], [-6, 10], [-2, 5], [-1, 3], [3, 4], [12, 11], [4, 6], [1, 5], [3, 11]], [[6418, 5404], [-1, 9], [1, 14], [-3, 11], [0, 5], [-2, 7], [-8, 17], [-2, 14], [-3, 6], [0, 18], [0, 5], [2, 9], [-4, 7], [-1, 5], [-1, 42], [-1, 6], [0, 19], [-7, 20], [-3, 7], [-2, 9], [0, 4], [-5, 14], [-4, 15], [1, 13], [-2, 10], [1, 8], [-1, 12], [1, 6], [0, 11], [-7, 36]], [[6367, 5763], [9, 0]], [[6567, 4294], [0, 14], [0, 15], [0, 15], [0, 15]], [[6511, 4706], [-1, 1]], [[6481, 4773], [-2, 3]], [[6965, 4294], [0, -1]], [[6962, 4293], [0, 1]], [[6956, 4294], [2, -9]], [[6946, 4238], [-1, -3]], [[7023, 3571], [-6, 4], [-4, 2]], [[7007, 3578], [-3, -1], [-9, -5]], [[6979, 3569], [-3, 1], [-1, 0]], [[6900, 4117], [19, 0], [21, 0], [20, 0], [21, 0], [20, 0], [21, 0], [20, 0], [21, 0]], [[7026, 3556], [-4, 1], [-1, 1]], [[7021, 3560], [7, -3], [4, -1]], [[5468, 5763], [23, 0]], [[5818, 5763], [25, 0]], [[5843, 5763], [0, -22], [0, -22], [0, -23], [0, -22], [1, -23], [0, -22], [0, -23], [0, -22], [0, -22], [0, -23], [0, -22], [0, -23], [0, -22], [1, -23], [0, -22], [0, -22]], [[5845, 5405], [0, -28], [0, -27], [0, -27], [0, -28], [0, -1], [-1, 0], [-1, 0]], [[5843, 5294], [-17, 0], [-17, 0], [-17, 0], [-17, 0], [-17, 0], [-16, 0], [-17, 0], [-17, 0], [-17, 0], [-17, 0], [-17, 0], [-17, 0], [-16, 0], [-17, 0], [-17, 0], [-17, 0], [-17, 0], [-17, 0], [-17, 0], [-16, 0], [-17, 0], [-17, 0], [-17, 0], [-17, 0], [-17, 0], [-17, 0], [-16, 0], [-17, 0], [-17, 0], [-17, 0], [-17, 0], [-17, 0], [0, -15], [0, -15], [0, -15], [0, -14]], [[4919, 5763], [14, 0]], [[5425, 5763], [20, 0]], [[7359, 4116], [3, 25], [2, 6], [2, 1], [8, 0], [5, 3], [5, 15], [10, 12], [4, 3], [7, 2], [16, 3], [18, 11], [13, 11], [10, 3], [3, 4], [2, 6], [1, 7], [1, 1], [6, 0], [5, 6], [4, 3], [4, 2], [3, 0], [1, -1], [0, -6], [1, -1], [2, -1], [3, 2], [3, 3], [8, 11], [6, 4], [9, 2], [6, -6], [4, 2], [11, 22], [4, 4], [3, 1], [6, 1], [0, 6], [2, 9], [0, 6], [4, 9]], [[7564, 4307], [1, -2], [28, 0], [28, -1], [28, 0], [28, 0], [27, -1], [28, 0], [28, 0], [28, -1], [28, 0], [28, 0], [27, -1], [28, 0], [28, 0], [28, -1], [28, 0], [19, 0]], [[8002, 4300], [0, -1], [-2, -8]], [[8000, 4291], [0, -2], [1, -4]], [[8005, 4280], [3, -8], [1, -6]], [[8009, 4266], [3, -12], [1, -6]], [[8013, 4248], [-4, 6], [-1, 2]], [[8008, 4256], [-3, 2], [-2, 1]], [[8003, 4259], [-4, 2], [-4, 1]], [[7982, 4249], [-4, 1], [-4, 1]], [[7970, 4251], [-2, -5], [-1, -3]], [[7944, 4252], [-1, 5], [0, 5]], [[7943, 4262], [0, -3], [-1, -8]], [[7944, 4239], [-1, -7], [0, -2]], [[7960, 4230], [5, 0], [7, -1]], [[7995, 4221], [-1, -6], [-1, -5]], [[7993, 4210], [0, -7], [0, -5]], [[7993, 4198], [2, 1], [1, 0]], [[8001, 4223], [5, 4], [4, 3]], [[8017, 4193], [-10, -10], [-5, -6]], [[8002, 4177], [-5, -7], [-6, -8]], [[7986, 4159], [-2, 0], [-6, 2]], [[7978, 4161], [-4, 1], [-5, 2]], [[7965, 4165], [-1, 0], [-2, -1]], [[7962, 4164], [-2, 3], [0, 2]], [[7952, 4180], [-1, -7], [-1, -2]], [[7942, 4168], [-2, 1], [-9, 3]], [[7931, 4172], [-11, 7], [-1, 0]], [[7952, 4090], [-5, -1], [-2, -1]], [[7895, 4073], [-2, 7], [0, 1]], [[7891, 4065], [1, -2], [1, -1]], [[7869, 4039], [-2, -3], [-2, -3]], [[7865, 4033], [-8, -15], [-1, -1]], [[7856, 4017], [-1, -4], [-1, -8]], [[7851, 3993], [0, 3], [0, 2]], [[7851, 3998], [0, 2], [0, 8]], [[7849, 4019], [-1, -10], [0, -10]], [[7802, 3985], [-11, 14], [-10, 14], [-11, 13], [-10, 14], [-11, 13], [-10, 13], [-10, 14], [-11, 13], [-11, 1], [-11, 0], [-10, 1], [-11, 0], [-11, 1], [-11, 0], [-11, 1], [-11, 0], [0, 9], [0, 7], [-7, 11], [-4, 7], [-9, -6], [0, 3], [0, 5], [-1, 1], [-2, 2], [-12, 0], [-12, 1], [-13, 1], [-12, 0], [-12, 1], [-12, 1], [-12, 0], [-13, 1], [-2, 1], [-6, -4], [-14, -6], [-8, -5], [-2, 0], [-12, -5], [-13, -5], [-1, 0]], [[8008, 4300], [2, 0]], [[8010, 4300], [3, -14], [5, -24]], [[8018, 4262], [10, -28], [5, -13]], [[8033, 4221], [1, -2], [1, -5]], [[8035, 4150], [2, 16], [2, 8]], [[8037, 4208], [1, -1], [1, -5]], [[8039, 4202], [1, -9], [1, -9]], [[8041, 4184], [-1, -13], [0, -1]], [[8037, 4150], [-2, -3], [-1, -2]], [[8016, 4140], [-13, -8], [-1, -1]], [[7979, 4107], [3, 2], [1, 1]], [[7983, 4110], [-7, -10], [-4, -6]], [[7972, 4094], [-1, -1], [-11, -18]], [[7948, 4080], [0, 1], [-1, 1]], [[7951, 4081], [3, -2], [3, -2]], [[7957, 4077], [-1, -1], [-1, 0]], [[6252, 5763], [26, 0]], [[6343, 5763], [24, 0]], [[6418, 5404], [-18, 0], [-18, 0], [-18, 0], [-18, 0], [-17, 0], [-18, 0], [-18, 0], [-18, 1], [-18, 0], [-18, 0], [-18, 0], [-18, 0], [-18, 0], [-18, 0], [-18, 0], [-18, 0], [-18, 0], [-17, 0], [-18, 0], [-18, 0], [-18, 0], [-18, 0], [-18, 0], [-18, 0], [-18, 0], [-18, 0], [-18, 0], [-18, 0], [-18, 0], [-17, 0], [-18, 0], [-18, 0]], [[5843, 5763], [8, 0]], [[6212, 5763], [27, 0]], [[5844, 4823], [0, 30], [0, 29], [0, 29], [0, 30], [0, 29], [-1, 30], [0, 29], [0, 29]], [[5843, 5058], [27, 0], [27, 0], [26, 0], [27, 0], [26, 0], [27, 0], [27, 0], [26, 0], [27, 0], [27, 0], [26, 0], [27, 0], [26, 0], [27, 0], [27, 0], [30, 0], [23, -17]], [[6418, 5002], [6, -1]], [[8356, 5324], [3, 4]], [[8359, 5328], [5, -2]], [[8404, 5067], [-3, -14], [0, -2]], [[8271, 5027], [-1, 4], [-5, 7], [-1, 3], [1, 4], [2, 9], [2, 3], [1, 6], [3, 22], [3, 12], [1, 24], [2, 6], [2, 4], [3, 11], [6, 9], [3, 11], [5, 10], [1, 5], [3, 13], [2, 21], [3, 4], [10, 3], [3, 2], [11, 9], [4, 4], [2, 3], [2, 6], [1, 2], [0, 3], [-4, 15], [0, 5], [9, 16], [-2, 9], [1, 2]], [[8070, 4703], [4, 4], [9, 8], [14, 9], [-19, 29], [-5, 2], [-4, 14], [-6, 4], [-1, 3], [-1, 11], [1, 6], [1, 4], [5, 3], [2, 6], [0, 3], [-3, 11], [0, 3], [7, 6], [9, 12], [5, 14], [3, 4], [2, 2], [6, 4]], [[8099, 4865], [8, -5], [7, -6], [8, -5], [8, -5], [7, -6], [8, -5], [8, -6], [7, -5]], [[8151, 4794], [-2, -2], [-1, -2]], [[8148, 4790], [-1, 0], [-3, -4]], [[8144, 4786], [-1, -1], [-2, 0]], [[8141, 4785], [-1, -2], [-1, -1]], [[8133, 4768], [1, -7], [0, -2]], [[8149, 4756], [2, 1], [2, 2]], [[8153, 4759], [1, -4], [1, -2]], [[8156, 4744], [0, -6], [-1, -3]], [[8153, 4726], [0, -1], [-2, -11]], [[8151, 4714], [-1, -13], [-1, -5]], [[8147, 4680], [0, 2], [0, 3]], [[8147, 4685], [1, 13], [0, 7]], [[8148, 4705], [-1, -1], [-1, -1]], [[8133, 4660], [-3, -6], [-2, -3]], [[8122, 4652], [0, -3], [1, -4]], [[8107, 4617], [-3, -4], [-1, -1]], [[8078, 4610], [-4, 2], [-2, 1]], [[8072, 4613], [-3, -1], [-3, 0]], [[8036, 4659], [1, 4], [3, 9]], [[8040, 4672], [2, 4], [2, 5]], [[8049, 4685], [2, 1], [13, 4]], [[8068, 4697], [1, 4], [1, 2]], [[8143, 4668], [-8, -16], [-1, -2]], [[8134, 4650], [0, 2], [0, 1]], [[8134, 4653], [4, 10], [7, 13]], [[5922, 4353], [0, -15], [0, -15], [0, -14], [0, -15]], [[5922, 4294], [-3, 0], [0, -33], [0, -33], [0, -33], [0, -34], [0, -33], [0, -33], [-1, -33], [0, -33], [0, -33], [0, -33], [0, -33], [0, -33], [0, -33], [0, -33], [0, -33], [-1, -33], [-17, 0], [-17, 0], [-17, 0], [-18, 0], [-17, 0], [-17, 0], [-18, 0], [-17, 0], [-17, 0], [-17, 0], [-18, 0], [-17, 0], [-17, 0], [-18, 0], [-17, -1], [-17, 0], [-1, 0], [8, -21]], [[5648, 3743], [9, -6]], [[5623, 3738], [-8, 0], [-9, 0]], [[5589, 3738], [-6, 0], [-11, 0]], [[5572, 3738], [-5, 0], [-12, 0]], [[5522, 3738], [0, -8], [0, -5]], [[5467, 3685], [-10, 0]], [[4615, 4941], [29, 0], [28, 0], [29, 0], [29, 0], [28, 0], [29, 0], [29, 0], [28, 0]], [[5073, 4941], [0, -19], [0, -18], [0, -18], [0, -19], [0, -18], [0, -18], [0, -19], [0, -18], [0, -19], [0, -18], [0, -18], [0, -19], [0, -18], [0, -18], [0, -19], [0, -18], [0, -18], [0, -19], [0, -18], [0, -19], [0, -18], [0, -18], [0, -19], [0, -18], [0, -18], [0, -19], [0, -18], [0, -19], [0, -18], [0, -18], [0, -19], [0, -18]], [[7764, 5091], [0, 1]], [[8042, 5264], [3, 3]], [[8087, 5294], [4, 0]], [[8097, 5294], [2, 0]], [[8102, 5294], [8, 0]], [[8145, 5294], [7, 0]], [[8152, 5294], [8, 0]], [[8203, 5294], [0, -8], [-1, -9], [1, -10], [-2, -21], [5, -16], [-1, -11], [0, -11], [-1, -3], [-3, -7], [-3, -7], [-1, -6], [0, -5], [3, -19], [1, -8], [0, -5], [-3, -16], [0, -4], [1, -2], [1, 0], [3, 5], [2, 1], [1, -1], [5, -8], [0, -13], [0, -10], [-1, -10], [0, -17], [0, -11], [0, -11], [-1, -9], [-1, -6], [2, -7]], [[8178, 4819], [-4, -6], [-4, -4]], [[8164, 4803], [-1, 0], [-3, -2]], [[8157, 4797], [-2, -2], [-1, -1]], [[8157, 4804], [1, 4], [2, 5]], [[8160, 4813], [2, 10], [1, 7]], [[8163, 4830], [0, 1], [-1, 12]], [[8162, 4843], [-3, 5], [0, 1]], [[8155, 4852], [2, -6], [2, -7]], [[8159, 4839], [1, -14], [0, -2]], [[8099, 4865], [-1, 4], [-3, 4], [-14, 6], [-3, 4], [-4, 5], [-2, 7], [-1, 6], [0, 6], [1, 6], [-3, 4], [0, 4], [-2, 2], [-9, 5], [-3, 6], [-6, 6], [-11, 1], [-10, 0], [-11, 0], [-10, 0], [-11, 0], [-11, 0], [-10, 0], [-11, 0], [-10, 0], [-11, 0], [-11, 0], [-10, 0], [-11, 0], [-10, 0], [-11, 0], [-11, 0], [-10, 0], [-11, 0], [-10, 0], [-11, 0], [-11, 0], [-10, 0], [-11, 0], [-10, 0], [-11, 0], [-11, 0], [-10, 0], [-11, 0], [-10, 0], [-11, 0], [-11, 0], [-10, 0], [0, 16], [0, 15], [0, 1]], [[7710, 4973], [4, 3]], [[8262, 4814], [2, 0], [3, -1]], [[8267, 4813], [2, 1], [2, 1]], [[8271, 4815], [3, 3], [1, 2]], [[8275, 4820], [3, 2], [7, 4]], [[8295, 4829], [3, -3], [1, -1]], [[8307, 4828], [6, 2], [1, 0]], [[8281, 4811], [-5, -2], [-2, -1]], [[8264, 4803], [-3, -2], [-6, -3]], [[8171, 4775], [-4, -1], [-6, -1]], [[8168, 4781], [-1, 1], [0, 1]], [[8158, 4776], [-3, -1], [-3, -1]], [[8151, 4781], [1, 4], [1, 1]], [[8153, 4786], [0, 1], [3, 4]], [[8162, 4799], [5, 2], [5, 2]], [[8172, 4803], [3, 4], [1, 1]], [[8176, 4808], [2, -2], [2, -2]], [[8180, 4804], [0, 3], [1, 2]], [[8192, 4814], [2, 0], [2, 0]], [[8196, 4814], [2, 1], [1, 1]], [[8201, 4816], [2, 0], [6, -2]], [[8209, 4814], [3, 0], [4, 1]], [[8221, 4818], [2, 0], [4, 1]], [[8227, 4819], [6, 0], [10, 1]], [[8145, 4783], [1, 0], [2, -1]], [[8147, 4774], [-1, -1], [-4, -4]], [[7652, 4939], [0, -9], [0, -13], [0, -12], [0, -12], [0, -13], [0, -12], [0, -12], [0, -13], [0, -12], [0, -12], [0, -13], [0, -12], [0, -12]], [[7499, 4519], [-8, 4]], [[7426, 4911], [0, -1]], [[7625, 4928], [27, 11]], [[6577, 3958], [-17, 6], [-4, 4], [-8, 4], [-7, 9], [-14, 11], [-6, 3], [-2, -1], [-2, -5], [-8, -4], [-4, 0], [-7, 1], [-2, 2], [-1, 3], [-3, 2], [-2, -2], [-11, -4], [-1, -3], [-7, 0], [-6, 2], [-15, -7], [-5, -6], [-6, -2], [-2, -5], [-2, 0], [-6, 7], [-6, 2], [-8, 7], [0, 6], [-1, 0], [-1, 1], [-2, 0], [-3, -5], [-3, -2], [-2, 0], [-7, 4], [-4, 6], [-1, 2], [-2, 0], [-3, -2], [-2, -6], [-2, -2], [-3, -1], [0, -4], [-3, -9], [-1, -2], [-2, 1], [-2, 2], [-2, 3], [0, 3], [1, 7], [-1, 3], [-2, 0], [-3, -3], [-4, 0], [-4, -3], [-3, -1], [-2, 1], [-4, 5], [-7, 4], [-3, 6], [-1, 2], [-1, 1], [-2, -1], [-2, -2], [-8, -8], [-4, -3], [-3, -1], [-3, 1], [-2, 2], [0, 9], [-2, 3], [-7, 5], [-2, 5], [-1, 8], [-8, -1], [-11, 0], [-5, -6], [-3, -1], [-3, 1], [-9, 9], [-9, -2], [-5, 1], [-13, 6], [-11, 1], [-4, 1], [-2, 1], [-1, 10], [-4, 8], [-2, 3], [-7, 5], [-1, 0], [0, -1], [-2, -6], [-2, -1], [-8, 2], [-7, -2], [-5, 4], [-14, 15], [-5, 3], [-5, 2], [0, 14], [0, 14], [0, 14], [0, 14], [0, 14], [0, 14], [0, 14], [0, 14], [0, 14], [0, 14], [0, 14], [0, 14], [0, 15], [0, 14], [0, 14], [0, 14], [-14, 0], [-15, 0], [-14, 0], [-15, 0], [-14, 0], [-14, 0], [-15, 0], [-14, 0], [-15, 0], [-14, 0], [-14, 0], [-15, 0], [-14, 0], [-15, 0], [-14, 0], [-15, 0]], [[4290, 4941], [-6, 9], [-4, 5]], [[4280, 4955], [-1, 7], [-3, 14]], [[4276, 4976], [-1, 6], [0, 4]], [[4276, 5009], [-3, 10], [0, 1]], [[4273, 5020], [-1, 3], [-6, 13]], [[4269, 5051], [2, 6], [1, 3]], [[4286, 5102], [2, 1], [4, 5]], [[4300, 5215], [1, 5], [3, 6]], [[4304, 5226], [0, 1], [-2, 10]], [[4313, 5361], [-1, 11], [-2, 21]], [[4310, 5393], [1, 18], [0, 17]], [[4314, 5433], [11, 0], [7, 0]], [[4332, 5433], [10, 4], [2, 0]], [[4348, 5436], [3, -4], [2, -3]], [[4353, 5429], [3, 0], [3, -1]], [[4688, 5409], [2, 2], [2, 0], [20, 0], [20, 0], [21, 0], [20, 0], [21, 0], [20, 0], [21, 0], [19, 0]], [[8045, 4686], [0, -1], [-1, -1]], [[7732, 4673], [-10, 0], [-10, 0], [-10, 0], [-10, 0], [-10, 0], [-10, 0], [-10, 0], [-10, 0], [0, 13], [0, 14], [0, 14], [0, 13], [0, 14], [0, 13], [0, 14], [0, 14]], [[7652, 4939], [14, 6]], [[7686, 4958], [24, 15]], [[8366, 4906], [-2, -1], [-1, -2]], [[8360, 4908], [-1, 2], [-1, 3]], [[8356, 4906], [-1, -1], [-4, -7]], [[8351, 4898], [-1, -20], [0, -2]], [[8350, 4876], [-3, -3], [-3, -5]], [[8325, 4862], [-1, 0], [-5, 1]], [[8357, 4878], [1, 2], [1, 3]], [[8366, 4900], [0, -8], [-1, -11]], [[8354, 4878], [-1, 1], [0, 5]], [[8356, 4890], [0, -1], [1, -2]], [[8357, 4887], [-1, -3], [0, -4]], [[7801, 3985], [-5, -5], [-15, -13]], [[7781, 3967], [-3, -3], [-3, -5]], [[7775, 3959], [-9, -14], [-8, -15]], [[7754, 3911], [-2, 4], [-2, 4]], [[7751, 3925], [0, 3], [0, 2]], [[7751, 3930], [-2, -5], [-2, -6]], [[7747, 3919], [3, -13], [1, -2]], [[7747, 3898], [-9, -9], [-2, -2]], [[7723, 3882], [-1, -4], [-1, -7]], [[7697, 3860], [1, -5], [2, -5]], [[7700, 3850], [-2, -4], [-2, -3]], [[7696, 3843], [-2, -2], [-4, -4]], [[7682, 3834], [-2, 0], [-2, 0]], [[7678, 3834], [-3, -1], [-1, -1]], [[7664, 3823], [-4, 1], [-4, 2]], [[7643, 3825], [5, -3], [3, -2]], [[7655, 3814], [0, -4], [-1, -4]], [[7654, 3806], [-2, -3], [0, -1]], [[7652, 3802], [-1, 0], [-4, -4]], [[7647, 3798], [-1, 0], [-1, 1]], [[7642, 3811], [-1, -1], [-2, -1]], [[7630, 3817], [0, -5], [0, -5]], [[7630, 3807], [2, -6], [1, -2]], [[7635, 3796], [1, -1], [2, -2]], [[7626, 3778], [-1, -4], [-1, -2]], [[5843, 5058], [0, 30], [0, 29], [0, 30], [0, 29], [0, 30], [0, 29], [0, 29], [0, 30]], [[7409, 4306], [18, 0], [37, 0], [27, 0], [21, -1], [13, 0], [16, 0], [4, 3], [8, -1], [11, 0]], [[6607, 3490], [-31, -18], [-5, -4]], [[6571, 3468], [-2, -1], [-13, -10]], [[6557, 3461], [2, 1], [15, 14]], [[6574, 3476], [-2, 1], [-4, 1]], [[6568, 3478], [-8, -3], [-2, -1]], [[6558, 3474], [-1, 1], [-2, 1]], [[6558, 3500], [-3, 0], [-4, 0]], [[6539, 3495], [-1, 0], [-2, -1]], [[6538, 3474], [4, -7], [1, -1]], [[6546, 3455], [-4, -5], [-6, -8]], [[6526, 3421], [-5, -8], [-4, -6]], [[6517, 3407], [-6, -5], [-3, -3]], [[6481, 3378], [0, -1], [-9, -8]], [[6452, 3367], [-9, 3], [-3, 1]], [[6440, 3371], [-2, 0], [-6, 0]], [[6432, 3371], [0, -1], [0, -2]], [[6432, 3368], [-5, -4], [-1, 0]], [[6426, 3364], [-4, 5], [-2, 1]], [[6417, 3378], [-2, 0], [-1, 1]], [[6414, 3379], [-1, -1], [-1, -1]], [[6412, 3377], [7, -20], [2, -6]], [[6424, 3350], [2, -1], [3, -1]], [[6418, 3337], [-4, -1], [-5, -2]], [[6409, 3334], [-2, 2], [-6, 7]], [[6399, 3320], [-1, -2], [-2, -1]], [[6396, 3317], [-2, -3], [-2, -2]], [[6383, 3313], [-5, 0], [-1, -1]], [[6372, 3306], [1, -3], [0, -2]], [[6381, 3305], [0, -3], [-3, -10]], [[6365, 3277], [-7, 1], [-3, 0]], [[6355, 3278], [-1, 0], [-1, -1]], [[6353, 3277], [-1, -1], [-1, -1]], [[6362, 3255], [-7, -27], [0, -3]], [[6350, 3215], [-1, -1], [-2, -1]], [[6344, 3213], [-6, 4], [-6, 6]], [[6331, 3210], [12, -5], [4, -1]], [[6347, 3204], [0, -5], [0, -3]], [[6347, 3190], [0, -2], [-3, -6]], [[6341, 3172], [1, -4], [1, -3]], [[6343, 3165], [2, -10], [1, -7]], [[6351, 3116], [1, -5], [1, -6]], [[6353, 3105], [9, -23], [6, -15]], [[6372, 3066], [1, -3], [0, -1]], [[6373, 3062], [0, -2], [0, -6]], [[6373, 3054], [-4, -1]], [[6362, 3052], [-2, -2], [-2, -2]], [[6357, 3045], [0, -1]], [[6355, 3043], [-4, 2]], [[6322, 3063], [-5, 1], [-16, 2]], [[6301, 3066], [-8, 3], [-7, 3]], [[6281, 3077], [-3, 3]], [[6278, 3080], [-3, 2], [-5, 3]], [[6257, 3089], [-3, 2]], [[6217, 3125], [-1, 7], [-4, 16]], [[6211, 3153], [-4, 10]], [[6207, 3163], [-6, 9]], [[6196, 3181], [-1, 2]], [[6195, 3183], [0, 2], [0, 1]], [[6191, 3216], [1, 4]], [[6191, 3241], [-2, 3], [-5, 7]], [[6172, 3262], [-4, 7]], [[6153, 3299], [0, 1], [-8, 14]], [[6136, 3322], [-4, 8], [-2, 2]], [[6130, 3332], [0, 2]], [[6127, 3347], [-1, 4]], [[6103, 3419], [-1, 1], [-7, 13]], [[6053, 3486], [-3, 7]], [[6033, 3507], [-3, -2], [-1, -1]], [[5987, 3509], [-5, 2], [-3, 3]], [[5966, 3504], [-3, -2]], [[5933, 3454], [-1, -5]], [[5910, 3411], [-6, 0], [-1, 1]], [[5903, 3412], [-6, 4], [-7, 4]], [[5890, 3420], [-10, 8], [-9, 8]], [[5842, 3453], [-5, 4]], [[5837, 3457], [-5, 7], [-3, 4]], [[5807, 3491], [-5, 11], [-4, 10]], [[5798, 3512], [-1, 4], [-4, 12]], [[5787, 3565], [-6, 17]], [[5781, 3582], [-1, 4], [-5, 12]], [[5775, 3598], [-2, 3], [-3, 4]], [[5761, 3614], [-10, 7], [-4, 3]], [[5690, 3693], [-6, 4], [-4, 3]], [[5665, 3727], [-2, 2], [-5, 8]], [[6535, 3429], [-2, -1], [-2, -1]], [[6531, 3427], [14, 15], [3, 4]], [[6556, 3451], [-5, -6], [-3, -4]], [[6403, 3318], [2, 1], [3, 2]], [[6426, 3334], [2, 2], [1, 2]], [[6430, 3339], [0, -2], [-1, -3]], [[6419, 3326], [-2, -2], [-15, -12]], [[6385, 3292], [0, 3], [1, 0]], [[6395, 3309], [0, -1], [1, -4]], [[6355, 3215], [2, 8], [4, 15]], [[6361, 3238], [2, 3], [11, 27]], [[6374, 3268], [4, 4], [1, 1]], [[6357, 3153], [5, -28], [6, -31]], [[5303, 4941], [0, -15], [0, -15], [0, -14], [0, -15], [0, -15], [0, -14], [0, -15], [0, -15], [20, 0], [19, 0], [19, 0], [19, 0], [20, 0], [19, 0], [19, 0], [19, 0]], [[7910, 4550], [-6, -7], [-2, -2]], [[7902, 4541], [0, -3], [-2, -6]], [[7904, 4510], [3, 1], [7, 3]], [[7914, 4514], [3, -2], [2, 0]], [[7919, 4512], [4, -7], [6, -12]], [[7929, 4493], [11, -3], [10, -4]], [[7979, 4458], [0, -4], [0, -2]], [[7979, 4452], [-2, -5], [0, -1]], [[7973, 4432], [-2, 0], [-5, -1]], [[7966, 4431], [-3, 1], [-2, 1]], [[7935, 4466], [-6, 11], [-2, 4]], [[7928, 4474], [5, -9], [1, -2]], [[7971, 4415], [3, -2], [5, -2]], [[7979, 4395], [-5, 1], [-6, 2]], [[7968, 4398], [0, -3], [0, -3]], [[7969, 4388], [-3, -2], [-2, -1]], [[7958, 4389], [-8, 11], [-9, 12]], [[7941, 4412], [0, -2], [0, -1]], [[7942, 4405], [8, -11], [2, -4]], [[7952, 4390], [3, -3], [6, -5]], [[7973, 4354], [-2, -1], [-3, -2]], [[7964, 4356], [-2, 3], [-2, 2]], [[7953, 4369], [-2, 9], [0, 1]], [[7940, 4374], [7, -1], [1, 0]], [[7950, 4358], [8, -7], [2, -3]], [[7960, 4348], [1, -1], [1, -7]], [[7962, 4340], [3, 0], [3, 0]], [[7988, 4344], [4, 0], [7, -2]], [[8002, 4336], [1, -8], [1, -3]], [[8008, 4312], [0, -3], [2, -9]], [[7540, 4416], [1, -4], [-1, -4], [2, -5], [2, -7], [3, -5], [4, -3], [6, -4], [7, -5], [5, -2], [2, 0], [6, 6], [5, 3], [6, 6], [9, -9], [4, 0], [9, 4], [9, 1], [2, 1], [3, 4], [0, 6], [1, 2], [2, 1], [1, 0], [4, -2], [4, 0], [17, 8], [1, -1], [2, -4], [7, 5], [4, 4], [2, 7], [4, 7], [-1, 2], [-2, 4], [3, 9], [4, 8], [17, 27], [5, 15], [7, 11], [2, 8], [5, 8], [4, 16], [2, 3], [3, 1], [4, -2], [2, -3], [2, -5], [8, -4], [7, -1], [5, 3], [3, 6], [4, 11], [3, 6], [3, 8], [2, 6], [4, 4], [10, -1], [4, 5], [4, 6], [2, 2], [3, 0], [9, 9], [11, 19], [2, 12], [3, 8], [1, 9], [3, 5], [10, -10], [5, -5], [13, -13], [9, -8], [3, 9], [5, 15]], [[8047, 4473], [-7, -20], [-10, -26]], [[8031, 4418], [-2, -1], [-1, -2]], [[8028, 4415], [-3, -1], [-3, -1]], [[8022, 4413], [-3, -3], [-2, -2]], [[8017, 4408], [-2, -3], [-1, -2]], [[8010, 4387], [-2, -6], [-4, -11]], [[8004, 4370], [-1, 3], [-3, 4]], [[8000, 4377], [0, 5], [-1, 2]], [[7999, 4384], [2, 14], [0, 1]], [[8008, 4425], [5, 12], [2, 4]], [[8015, 4441], [3, 4], [3, 4]], [[8021, 4449], [2, 7], [2, 9]], [[8047, 4455], [2, 4], [8, 16]], [[8210, 5294], [6, 0]], [[8280, 5294], [16, 0]], [[4679, 5763], [24, 0]], [[4835, 5763], [8, 0]], [[4367, 5429], [-1, 1], [-1, 1]], [[4361, 5431], [-5, 4], [-3, 2]], [[4353, 5437], [-2, 3], [-3, 3]], [[4348, 5443], [-5, 0], [-9, 0]], [[4331, 5446], [-12, -2], [-4, -1]], [[4315, 5443], [-1, 1], [-4, 3]], [[4304, 5455], [0, 6], [-1, 8]], [[4303, 5469], [1, 7], [0, 6]], [[4306, 5473], [3, -6], [2, -5]], [[4316, 5489], [-4, 4], [-1, 2]], [[4299, 5513], [20, 11], [0, 1]], [[4294, 5539], [0, 4], [-2, 10]], [[4292, 5553], [-7, 19], [-2, 4]], [[4256, 5643], [-1, 9], [-2, 12]], [[4255, 5680], [0, 1], [-2, 10]], [[4253, 5691], [1, 0], [5, 0]], [[4294, 5675], [5, -4], [1, -1]], [[4362, 5661], [1, 0], [2, 0]], [[4365, 5661], [4, 2], [3, 2]], [[4372, 5665], [1, -1], [2, 0]], [[4386, 5655], [2, 0], [3, 1]], [[4409, 5638], [1, -3], [1, -2]], [[4411, 5633], [-3, -5], [-7, -12]], [[4399, 5615], [0, 5], [0, 1]], [[4373, 5574], [1, -1], [0, -2]], [[4374, 5571], [0, -1], [2, 0]], [[4391, 5578], [-5, -1], [-3, -1]], [[4398, 5606], [3, 3], [2, 2]], [[4403, 5611], [3, 7], [0, 1]], [[4414, 5627], [2, 2], [0, 1]], [[4416, 5630], [0, 5], [0, 2]], [[4416, 5638], [1, 0], [3, -1]], [[4420, 5637], [1, -4], [1, -8]], [[4422, 5625], [-1, -4], [0, -2]], [[4421, 5619], [-2, -1], [-5, -5]], [[4414, 5613], [-1, -2], [0, -1]], [[4414, 5601], [-1, 0], [0, -1]], [[4409, 5601], [1, -1], [6, -9]], [[4416, 5591], [1, -4], [1, -4]], [[4418, 5583], [0, -4], [1, -3]], [[4419, 5576], [-2, -8], [0, -5]], [[4406, 5565], [-2, -5], [-2, -5]], [[4399, 5568], [-1, 0], [-1, 0]], [[4390, 5563], [0, -1], [-2, -4]], [[4391, 5544], [2, 1], [6, 1]], [[4399, 5546], [4, -3], [2, -1]], [[4407, 5542], [3, 2], [3, 2]], [[4415, 5549], [1, 3], [3, 9]], [[4422, 5564], [2, 0], [1, 0]], [[4425, 5564], [3, 1], [1, 1]], [[4429, 5566], [4, 5], [1, 2]], [[4432, 5591], [0, 4], [1, 5]], [[4431, 5625], [1, 2], [4, 8]], [[4436, 5635], [0, 2], [1, 2]], [[4442, 5648], [-1, 2], [0, 1]], [[4428, 5667], [1, -3], [2, -7]], [[4431, 5656], [-5, 4], [-3, 2]], [[4421, 5673], [1, 2], [3, 2]], [[4419, 5695], [-2, 2], [-1, 0]], [[4410, 5701], [0, 2], [1, 1]], [[4413, 5705], [3, -1], [3, 0]], [[4423, 5706], [0, 2], [0, 2]], [[4421, 5712], [1, 6], [0, 7]], [[4422, 5725], [-2, 8], [-1, 3]], [[4418, 5738], [-1, 0], [-1, 0]], [[4411, 5736], [-2, 2], [-1, 2]], [[4638, 5763], [21, 0]], [[4401, 5726], [0, -2], [1, -1]], [[4402, 5723], [-2, -1], [-1, -1]], [[4399, 5721], [-1, -1], [-1, 0]], [[4397, 5720], [-3, 3], [-1, 1]], [[4392, 5725], [1, -5], [0, -1]], [[4392, 5716], [-2, 2], [-5, 2]], [[4392, 5730], [2, -1], [7, -3]], [[4383, 5705], [2, -2], [0, -1]], [[4385, 5702], [-3, 0], [-5, 2]], [[4377, 5704], [-3, 2], [-1, 0]], [[4375, 5719], [6, -8], [1, -1]], [[4382, 5710], [1, -3], [0, -2]], [[4390, 5700], [0, 2], [-1, 2]], [[4391, 5710], [1, 1], [1, 0]], [[4394, 5711], [1, -3], [0, -2]], [[4395, 5706], [1, -2], [2, -4]], [[4427, 5638], [0, 2], [-1, 2]], [[4426, 5642], [-1, 1], [-2, 1]], [[4423, 5644], [-2, 1], [-3, 1]], [[4416, 5650], [-1, 2], [0, 1]], [[4411, 5665], [-1, 0], [-2, 2]], [[4404, 5673], [0, 1], [0, 1]], [[4404, 5675], [1, 2], [0, 3]], [[4405, 5680], [2, 3], [3, 5]], [[4410, 5688], [1, 2], [2, 2]], [[4408, 5675], [0, -1], [-1, -1]], [[4407, 5673], [4, -1], [2, 0]], [[4422, 5597], [-1, 0], [-3, 2]], [[4417, 5602], [0, 1], [0, 4]], [[4419, 5612], [1, -1], [1, -1]], [[4421, 5610], [1, 0], [0, -1]], [[4427, 5581], [3, -4], [1, -2]], [[4394, 5551], [-1, 1], [-2, 3]], [[4391, 5555], [0, 2], [0, 2]], [[4393, 5561], [0, -1], [3, -6]], [[6886, 5480], [5, 1]], [[7107, 5306], [-1, -3]], [[7091, 5005], [0, -6]]], "transform": {"scale": [0.013001300130013002, 0.008500850085008501], "translate": [-180, 0]}, "bbox": [-180, 0, -50, 85]}