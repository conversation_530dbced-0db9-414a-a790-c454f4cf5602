import React from 'react';
import { Line } from 'react-chartjs-2';
import { Chart as ChartJS, LineElement, PointElement, LinearScale, CategoryScale, Tooltip, Legend } from 'chart.js';

ChartJS.register(LineElement, PointElement, LinearScale, CategoryScale, Tooltip, Legend);

const MissionStatistics: React.FC = () => {
  const data = {
    labels: ['Week 1', 'Week 2', 'Week 3', 'Week 4'],
    datasets: [{
      label: 'Mission Progress (%)',
      data: [70, 80, 85, 90],
      borderColor: 'hsl(var(--accent))',
      backgroundColor: 'hsl(var(--accent) / 0.2)',
      tension: 0.4,
      fill: true,
      pointBackgroundColor: 'hsl(var(--accent))',
      pointBorderColor: 'hsl(var(--accent))',
      pointRadius: 6,
    }],
  };

  const options = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: { position: 'top' as const },
      title: { display: true, text: 'Mission Progress Tracking' }
    },
    scales: { y: { beginAtZero: true, max: 100 } },
  };

  return (
    <div className="p-6">
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-foreground">Mission Statistics</h1>
            <p className="text-muted-foreground">Progress tracking and mission analytics</p>
          </div>
          <div className="bg-warning text-warning-foreground px-3 py-1 rounded text-sm font-medium">
            IN PROGRESS
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          <div className="lg:col-span-2 bg-card p-6 rounded-lg border border-border">
            <h3 className="text-lg font-semibold text-card-foreground mb-4 flex items-center gap-2">
              📈 Mission Progress Trend
            </h3>
            <div className="h-64">
              <Line data={data} options={options} />
            </div>
          </div>

          <div className="bg-card p-6 rounded-lg border border-border">
            <h3 className="text-lg font-semibold text-card-foreground mb-4 flex items-center gap-2">
              🎯 Mission Metrics
            </h3>
            <div className="space-y-4">
              <div className="p-3 bg-primary/10 rounded-lg">
                <div className="text-sm text-muted-foreground">Active Missions</div>
                <div className="text-2xl font-bold text-primary">12</div>
              </div>
              <div className="p-3 bg-success/10 rounded-lg">
                <div className="text-sm text-muted-foreground">Success Rate</div>
                <div className="text-2xl font-bold text-success">90%</div>
              </div>
              <div className="p-3 bg-warning/10 rounded-lg">
                <div className="text-sm text-muted-foreground">In Progress</div>
                <div className="text-2xl font-bold text-warning">8</div>
              </div>
              <div className="p-3 bg-accent/10 rounded-lg">
                <div className="text-sm text-muted-foreground">Completed</div>
                <div className="text-2xl font-bold text-accent">4</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default MissionStatistics;
