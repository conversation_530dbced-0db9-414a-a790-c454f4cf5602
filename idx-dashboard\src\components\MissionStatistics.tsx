import React from 'react';
import { Line } from 'react-chartjs-2';
import { Chart as ChartJS, LineElement, PointElement, LinearScale, CategoryScale, Tooltip, Legend } from 'chart.js';

ChartJS.register(LineElement, PointElement, LinearScale, CategoryScale, Tooltip, Legend);

const MissionStatistics: React.FC = () => {
  const data = {
    labels: ['Week 1', 'Week 2', 'Week 3', 'Week 4'],
    datasets: [{
      label: 'Progress (%)',
      data: [70, 80, 85, 90],
      borderColor: '#FF9933',
      backgroundColor: 'rgba(255, 153, 51, 0.2)',
      tension: 0.4,
    }],
  };

  const options = {
    responsive: true,
    plugins: { 
      legend: { position: 'top' as const }, 
      title: { display: true, text: 'Mission Progress' } 
    },
  };

  return (
    <div className="space-y-6">
      <h2 className="text-3xl font-semibold">Mission Statistics</h2>
      <div className="bg-white p-6 rounded-lg shadow-lg">
        <Line data={data} options={options} />
      </div>
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-white p-6 rounded-lg shadow-lg text-center">
          <div className="text-3xl font-bold text-drdo-navy">12</div>
          <div className="text-sm text-gray-600">Active Missions</div>
        </div>
        <div className="bg-white p-6 rounded-lg shadow-lg text-center">
          <div className="text-3xl font-bold text-green-600">90%</div>
          <div className="text-sm text-gray-600">Success Rate</div>
        </div>
        <div className="bg-white p-6 rounded-lg shadow-lg text-center">
          <div className="text-3xl font-bold text-drdo-saffron">8</div>
          <div className="text-sm text-gray-600">In Progress</div>
        </div>
        <div className="bg-white p-6 rounded-lg shadow-lg text-center">
          <div className="text-3xl font-bold text-blue-600">4</div>
          <div className="text-sm text-gray-600">Completed</div>
        </div>
      </div>
    </div>
  );
};

export default MissionStatistics;
