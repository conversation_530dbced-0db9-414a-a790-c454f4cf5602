import React from 'react';
import { Line } from 'react-chartjs-2';
import { Chart as ChartJS, LineElement, PointElement, LinearScale, CategoryScale, Tooltip, Legend } from 'chart.js';

ChartJS.register(LineElement, PointElement, LinearScale, CategoryScale, Tooltip, Legend);

const MissionStatistics: React.FC = () => {
  const data = {
    labels: ['Day 1', 'Day 2', 'Day 3', 'Day 4'],
    datasets: [{
      label: 'Progress (%)',
      data: [70, 80, 85, 90],
      borderColor: 'rgba(255, 153, 51, 1)',
      backgroundColor: 'rgba(255, 153, 51, 0.2)',
      tension: 0.3,
      fill: true,
    }],
  };

  const options = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: { position: 'top' as const },
      title: { display: true, text: 'Mission Progress' }
    },
    scales: { y: { beginAtZero: true, max: 100 } },
  };

  return (
    <div className="space-y-4">
      <h2 className="text-2xl font-semibold">Mission Progress</h2>
      <div className="bg-white p-4 rounded-lg shadow-md h-64">
        <Line data={data} options={options} />
      </div>
    </div>
  );
};

export default MissionStatistics;
