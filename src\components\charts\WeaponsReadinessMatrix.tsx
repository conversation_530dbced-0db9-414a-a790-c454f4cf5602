// DRDO IDX - Weapons Readiness Matrix

import React from 'react';
import { MilitaryAsset } from '../../types/military';

interface WeaponsReadinessMatrixProps {
  weapons: MilitaryAsset[];
}

export const WeaponsReadinessMatrix: React.FC<WeaponsReadinessMatrixProps> = ({ weapons }) => {
  const getReadinessLevel = (status: string): number => {
    switch (status) {
      case 'OPERATIONAL': return 95;
      case 'DEPLOYED': return 100;
      case 'STANDBY': return 85;
      case 'MAINTENANCE': return 40;
      case 'OFFLINE': return 0;
      default: return 0;
    }
  };

  const getReadinessColor = (level: number): string => {
    if (level >= 90) return 'bg-success';
    if (level >= 70) return 'bg-warning';
    if (level >= 50) return 'bg-destructive';
    return 'bg-muted';
  };

  const getStatusBadgeColor = (status: string): string => {
    switch (status) {
      case 'OPERATIONAL': return 'bg-success text-success-foreground';
      case 'DEPLOYED': return 'bg-primary text-primary-foreground';
      case 'STANDBY': return 'bg-warning text-warning-foreground';
      case 'MAINTENANCE': return 'bg-destructive text-destructive-foreground';
      case 'OFFLINE': return 'bg-muted text-muted-foreground';
      default: return 'bg-muted text-muted-foreground';
    }
  };

  return (
    <div className="bg-card p-6 rounded-lg border border-border">
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-lg font-bold text-card-foreground">Combat Readiness Matrix</h3>
        <div className="flex items-center gap-2">
          <div className="w-3 h-3 bg-success rounded-full"></div>
          <span className="text-sm text-muted-foreground">Systems Online</span>
        </div>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {weapons.map((weapon) => {
          const readiness = weapon.readiness || getReadinessLevel(weapon.status);
          return (
            <div key={weapon.id} className="p-4 border border-border rounded-lg bg-muted/20 hover:bg-muted/30 transition-colors">
              <div className="flex items-center justify-between mb-2">
                <span className="font-medium text-card-foreground">{weapon.name}</span>
                <div className={`px-2 py-1 rounded text-xs font-medium ${getStatusBadgeColor(weapon.status)}`}>
                  {readiness}%
                </div>
              </div>
              
              <div className="text-sm text-muted-foreground mb-3">
                {weapon.type} • {weapon.location}
              </div>
              
              {/* Readiness Progress Bar */}
              <div className="mb-3">
                <div className="flex justify-between text-xs text-muted-foreground mb-1">
                  <span>Readiness Level</span>
                  <span>{readiness}%</span>
                </div>
                <div className="h-2 bg-muted rounded-full overflow-hidden">
                  <div 
                    className={`h-full transition-all duration-500 ${getReadinessColor(readiness)}`}
                    style={{ width: `${readiness}%` }}
                  />
                </div>
              </div>
              
              {/* Additional Info */}
              <div className="space-y-1 text-xs text-muted-foreground">
                <div className="flex justify-between">
                  <span>Status:</span>
                  <span className="font-medium">{weapon.status}</span>
                </div>
                {weapon.specifications && (
                  <>
                    <div className="flex justify-between">
                      <span>Range:</span>
                      <span>{weapon.specifications.range} km</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Accuracy:</span>
                      <span>{weapon.specifications.accuracy}%</span>
                    </div>
                  </>
                )}
                <div className="flex justify-between">
                  <span>Last Maintenance:</span>
                  <span>{new Date(weapon.lastMaintenance).toLocaleDateString()}</span>
                </div>
              </div>
            </div>
          );
        })}
      </div>
      
      {/* Summary Statistics */}
      <div className="mt-6 grid grid-cols-2 md:grid-cols-4 gap-4">
        <div className="text-center p-3 bg-success/10 rounded-lg">
          <div className="text-2xl font-bold text-success">
            {weapons.filter(w => w.status === 'OPERATIONAL' || w.status === 'DEPLOYED').length}
          </div>
          <div className="text-xs text-muted-foreground">Ready for Deployment</div>
        </div>
        
        <div className="text-center p-3 bg-warning/10 rounded-lg">
          <div className="text-2xl font-bold text-warning">
            {weapons.filter(w => w.status === 'STANDBY').length}
          </div>
          <div className="text-xs text-muted-foreground">On Standby</div>
        </div>
        
        <div className="text-center p-3 bg-destructive/10 rounded-lg">
          <div className="text-2xl font-bold text-destructive">
            {weapons.filter(w => w.status === 'MAINTENANCE').length}
          </div>
          <div className="text-xs text-muted-foreground">Under Maintenance</div>
        </div>
        
        <div className="text-center p-3 bg-primary/10 rounded-lg">
          <div className="text-2xl font-bold text-primary">
            {Math.round(weapons.reduce((acc, w) => acc + (w.readiness || getReadinessLevel(w.status)), 0) / weapons.length)}%
          </div>
          <div className="text-xs text-muted-foreground">Average Readiness</div>
        </div>
      </div>
    </div>
  );
};
