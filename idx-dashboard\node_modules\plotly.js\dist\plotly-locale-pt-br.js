var locale={moduleType:"locale",name:"pt-BR",dictionary:{Autoscale:"Escala autom\xe1tica","Box Select":"<PERSON><PERSON>\xe7\xe3o retangular","Click to enter Colorscale title":"Clique para editar o t\xedtulo da escala de cor","Click to enter Component A title":"Clique para editar o t\xedtulo do Componente A","Click to enter Component B title":"Clique para editar o t\xedtulo do Componente B","Click to enter Component C title":"Clique para editar o t\xedtulo do Componente C","Click to enter Plot title":"Clique para editar o t\xedtulo do Gr\xe1fico","Click to enter X axis title":"Clique para editar o t\xedtulo do eixo X","Click to enter Y axis title":"Clique para editar o t\xedtulo do eixo Y","Click to enter radial axis title":"Clique para editar o t\xedtulo do eixo radial","Compare data on hover":"Comparar dados ao pairar","Double-click on legend to isolate one trace":"Duplo clique na legenda para isolar uma s\xe9rie","Double-click to zoom back out":"Duplo clique para reverter zoom","Download plot as a png":"Fazer download do gr\xe1fico como imagem (png)","Download plot":"Fazer download do gr\xe1fico","Edit in Chart Studio":"Editar no Chart Studio","IE only supports svg.  Changing format to svg.":"IE suporta apenas svg. Alterando formato para svg","Lasso Select":"Sele\xe7\xe3o de la\xe7o","Orbital rotation":"Rota\xe7\xe3o orbital",Pan:"Mover","Produced with Plotly.js":"Criado com o Plotly.js",Reset:"Restaurar","Reset axes":"Restaurar eixos","Reset camera to default":"Restaurar c\xe2mera para padr\xe3o","Reset camera to last save":"Restaurar c\xe2mera para \xfaltima salva","Reset view":"Restaurar vis\xe3o","Reset views":"Restaurar vis\xf5es","Show closest data on hover":"Exibir dado mais pr\xf3ximo ao pairar","Snapshot succeeded":"Captura instant\xe2nea completa","Sorry, there was a problem downloading your snapshot!":"Desculpe, houve um problema no download de sua captura instant\xe2nea!","Taking snapshot - this may take a few seconds":"Efetuando captura instant\xe2nea - isso pode levar alguns instantes","Toggle Spike Lines":"Habilitar/desabilitar triangula\xe7\xe3o de linhas","Toggle show closest data on hover":"Habilitar/desabilitar exibi\xe7\xe3o de dado mais pr\xf3ximo ao pairar","Turntable rotation":"Rota\xe7\xe3o de mesa",Zoom:"Zoom","Zoom in":"Ampliar zoom","Zoom out":"Reduzir zoom",close:"fechamento",high:"alta","incoming flow count":"contagem de fluxo de entrada",kde:"kde",lat:"latitude",lon:"longitude",low:"baixa","lower fence":"limite inferior",max:"m\xe1ximo","mean \xb1 \u03c3":"m\xe9dia \xb1 \u03c3",mean:"m\xe9dia",median:"mediana",min:"m\xednimo","new text":"novo texto",open:"abertura","outgoing flow count":"contagem de fluxo de sa\xedda",q1:"q1",q3:"q3",source:"origem",target:"destino",trace:"s\xe9rie","upper fence":"limite superior"},format:{days:["Domingo","Segunda-feira","Ter\xe7a-feira","Quarta-feira","Quinta-feira","Sexta-feira","S\xe1bado"],shortDays:["Dom","Seg","Ter","Qua","Qui","Sex","S\xe1b"],months:["Janeiro","Fevereiro","Mar\xe7o","Abril","Maio","Junho","Julho","Agosto","Setembro","Outubro","Novembro","Dezembro"],shortMonths:["Jan","Fev","Mar","Abr","Mai","Jun","Jul","Ago","Set","Out","Nov","Dez"],date:"%d/%m/%Y",decimal:",",thousands:"."}};"undefined"==typeof Plotly?(window.PlotlyLocales=window.PlotlyLocales||[],window.PlotlyLocales.push(locale)):Plotly.register(locale);