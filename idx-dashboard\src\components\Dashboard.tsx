import React, { useEffect, useRef } from 'react';
import { drawEquipmentChart, drawLineChart, drawBarChart } from '../utils/d3Charts';
import type { ChartData } from '../utils/d3Charts';

const Dashboard: React.FC = () => {
  const systemHealthRef = useRef<SVGSVGElement>(null);
  const performanceRef = useRef<SVGSVGElement>(null);
  const resourceRef = useRef<SVGSVGElement>(null);

  useEffect(() => {
    if (systemHealthRef.current) {
      drawEquipmentChart(systemHealthRef.current, [95, 88, 92], ['Systems', 'Network', 'Security']);
    }

    if (performanceRef.current) {
      const performanceData = Array.from({ length: 10 }, (_, i) => ({
        x: i,
        y: Math.random() * 100 + 50
      }));
      drawLineChart(performanceRef.current, performanceData);
    }

    if (resourceRef.current) {
      const resourceData: ChartData[] = [
        { label: 'CPU', value: 75, color: '#003087' },
        { label: 'Memory', value: 60, color: '#FF9933' },
        { label: 'Storage', value: 45, color: '#4CAF50' },
        { label: 'Network', value: 85, color: '#2196F3' }
      ];
      drawBarChart(resourceRef.current, resourceData);
    }
  }, []);

  const kpiData = [
    { title: 'Active Systems', value: '52', change: '+2', trend: 'up' },
    { title: 'Mission Success Rate', value: '98.5%', change: '+0.3%', trend: 'up' },
    { title: 'Security Alerts', value: '0', change: '0', trend: 'neutral' },
    { title: 'Data Processed', value: '2.4TB', change: '+15%', trend: 'up' }
  ];

  const recentActivities = [
    { time: '14:30', activity: 'BrahMos telemetry data received', status: 'success' },
    { time: '14:25', activity: 'Agni-V simulation completed', status: 'success' },
    { time: '14:20', activity: 'Security scan completed', status: 'info' },
    { time: '14:15', activity: 'Data backup initiated', status: 'warning' },
    { time: '14:10', activity: 'System health check passed', status: 'success' }
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-drdo-navy">Dashboard Overview</h1>
          <p className="text-gray-600 mt-1">Real-time system monitoring and analytics</p>
        </div>
        <div className="flex space-x-2">
          <button className="px-4 py-2 bg-drdo-navy text-white rounded-lg hover:bg-opacity-90 transition-colors">
            Export Report
          </button>
          <button className="px-4 py-2 bg-drdo-saffron text-white rounded-lg hover:bg-opacity-90 transition-colors">
            Refresh Data
          </button>
        </div>
      </div>

      {/* KPI Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {kpiData.map((kpi, index) => (
          <div key={index} className="liquid-card p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600 mb-1">{kpi.title}</p>
                <p className="text-2xl font-bold text-drdo-navy">{kpi.value}</p>
              </div>
              <div className={`flex items-center space-x-1 text-sm ${
                kpi.trend === 'up' ? 'text-green-600' : 
                kpi.trend === 'down' ? 'text-red-600' : 'text-gray-600'
              }`}>
                {kpi.trend === 'up' && <span>↗</span>}
                {kpi.trend === 'down' && <span>↘</span>}
                {kpi.trend === 'neutral' && <span>→</span>}
                <span>{kpi.change}</span>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Charts Section */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* System Health */}
        <div className="liquid-card p-6">
          <h3 className="text-lg font-semibold text-drdo-navy mb-4">System Health</h3>
          <svg ref={systemHealthRef} className="w-full"></svg>
        </div>

        {/* Performance Metrics */}
        <div className="liquid-card p-6">
          <h3 className="text-lg font-semibold text-drdo-navy mb-4">Performance Trends</h3>
          <svg ref={performanceRef} className="w-full"></svg>
        </div>

        {/* Resource Utilization */}
        <div className="liquid-card p-6">
          <h3 className="text-lg font-semibold text-drdo-navy mb-4">Resource Utilization</h3>
          <svg ref={resourceRef} className="w-full"></svg>
        </div>
      </div>

      {/* Recent Activities */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="liquid-card p-6">
          <h3 className="text-lg font-semibold text-drdo-navy mb-4">Recent Activities</h3>
          <div className="space-y-3">
            {recentActivities.map((activity, index) => (
              <div key={index} className="flex items-center space-x-3 p-3 rounded-lg bg-gray-50">
                <div className={`w-3 h-3 rounded-full ${
                  activity.status === 'success' ? 'bg-green-400' :
                  activity.status === 'warning' ? 'bg-yellow-400' :
                  activity.status === 'info' ? 'bg-blue-400' : 'bg-gray-400'
                }`}></div>
                <div className="flex-1">
                  <p className="text-sm font-medium text-drdo-navy">{activity.activity}</p>
                  <p className="text-xs text-gray-500">{activity.time}</p>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Quick Actions */}
        <div className="liquid-card p-6">
          <h3 className="text-lg font-semibold text-drdo-navy mb-4">Quick Actions</h3>
          <div className="grid grid-cols-2 gap-3">
            <button className="p-4 bg-drdo-navy text-white rounded-lg hover:bg-opacity-90 transition-colors text-center">
              <div className="text-2xl mb-2">🚀</div>
              <div className="text-sm">Launch Mission</div>
            </button>
            <button className="p-4 bg-drdo-saffron text-white rounded-lg hover:bg-opacity-90 transition-colors text-center">
              <div className="text-2xl mb-2">📊</div>
              <div className="text-sm">View Analytics</div>
            </button>
            <button className="p-4 bg-green-600 text-white rounded-lg hover:bg-opacity-90 transition-colors text-center">
              <div className="text-2xl mb-2">🔒</div>
              <div className="text-sm">Security Check</div>
            </button>
            <button className="p-4 bg-blue-600 text-white rounded-lg hover:bg-opacity-90 transition-colors text-center">
              <div className="text-2xl mb-2">⚙️</div>
              <div className="text-sm">System Config</div>
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;
