import React from 'react';
import { Bar } from 'react-chartjs-2';
import { Chart as ChartJS, CategoryScale, LinearScale, BarElement, Title, Tooltip, Legend } from 'chart.js';

ChartJS.register(CategoryScale, LinearScale, BarElement, Title, Tooltip, Legend);

const Dashboard: React.FC = () => {
  const data = {
    labels: ['Equipment', 'Mission', 'Simulation'],
    datasets: [{
      label: 'Status (%)',
      data: [95, 80, 90],
      backgroundColor: '#003087',
      borderColor: '#FF9933',
      borderWidth: 1,
    }],
  };

  const options = {
    responsive: true,
    plugins: {
      legend: { position: 'top' as const },
      title: { display: true, text: 'Overview' }
    },
  };



  return (
    <div className="space-y-6">
      <h2 className="text-3xl font-semibold">Dashboard Overview</h2>
      <div className="bg-white p-6 rounded-lg shadow-lg">
        <Bar data={data} options={options} />
      </div>
    </div>
  );
};

export default Dashboard;
