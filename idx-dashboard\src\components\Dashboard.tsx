// DRDO IDX - Main Command Dashboard

import React from 'react';
import { useTranslation } from 'react-i18next';
import { Bar } from 'react-chartjs-2';
import { Chart as ChartJS, CategoryScale, LinearScale, BarElement, Title, Tooltip, Legend } from 'chart.js';

ChartJS.register(CategoryScale, LinearScale, BarElement, Title, Tooltip, Legend);

const Dashboard: React.FC = () => {
  const { t } = useTranslation();
  const data = {
    labels: ['Equipment', 'Mission', 'Simulation'],
    datasets: [{
      label: 'Performance (%)',
      data: [95, 80, 90],
      backgroundColor: 'hsl(var(--primary) / 0.7)',
      borderColor: 'hsl(var(--accent))',
      borderWidth: 2,
    }],
  };

  const options = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: { position: 'top' as const },
      title: { display: true, text: t('dashboard.systemOverview') }
    },
    scales: { y: { beginAtZero: true, max: 100 } },
  };



  return (
    <div className="space-y-6">
        {/* DRDO Status Bar */}
        <div className="bg-gradient-to-r from-primary/10 to-primary/5 p-4 rounded-lg border border-primary/20">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <div className="w-3 h-3 bg-success rounded-full animate-pulse"></div>
              <span className="font-medium">{t('dashboard.allSystemsOperational')}</span>
              <div className="bg-success text-success-foreground px-2 py-1 rounded text-xs font-medium">{t('common.secure')}</div>
            </div>
            <div className="text-sm text-muted-foreground">
              {t('dashboard.lastUpdated')}: {new Date().toLocaleTimeString('en-IN')} IST
            </div>
          </div>
        </div>

        {/* Status Overview */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <div className="bg-card p-6 rounded-lg border border-border border-l-4 border-l-primary">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">{t('dashboard.activeAssets')}</p>
                <p className="text-3xl font-bold text-primary">247</p>
                <p className="text-xs text-success">+12 {t('status.fromYesterday')}</p>
              </div>
              <div className="h-10 w-10 text-primary">🛡️</div>
            </div>
          </div>

          <div className="bg-card p-6 rounded-lg border border-border border-l-4 border-l-success">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">{t('dashboard.activeMissions')}</p>
                <p className="text-3xl font-bold text-success">89%</p>
                <p className="text-xs text-success">{t('status.excellentStatus')}</p>
              </div>
              <div className="h-10 w-10 text-success">🎯</div>
            </div>
          </div>

          <div className="bg-card p-6 rounded-lg border border-border border-l-4 border-l-warning">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">{t('dashboard.threatAlerts')}</p>
                <p className="text-3xl font-bold text-warning">3</p>
                <p className="text-xs text-muted-foreground">{t('status.mediumPriority')}</p>
              </div>
              <div className="h-10 w-10 text-warning">⚠️</div>
            </div>
          </div>

          <div className="bg-card p-6 rounded-lg border border-border border-l-4 border-l-primary">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">{t('dashboard.systemHealth')}</p>
                <p className="text-3xl font-bold text-primary">98.2%</p>
                <p className="text-xs text-success">{t('status.aboveTarget')}</p>
              </div>
              <div className="h-10 w-10 text-primary">📈</div>
            </div>
          </div>
        </div>

        {/* Charts Section */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <div className="bg-card p-6 rounded-lg border border-border">
            <h3 className="text-lg font-semibold text-card-foreground mb-4 flex items-center gap-2">
              📊 {t('dashboard.systemOverview')}
            </h3>
            <div className="h-64">
              <Bar data={data} options={options} />
            </div>
          </div>

          <div className="bg-card p-6 rounded-lg border border-border">
            <h3 className="text-lg font-semibold text-card-foreground mb-4 flex items-center gap-2">
              ⚡ {t('dashboard.quickActions')}
            </h3>
            <div className="space-y-3">
              <button className="w-full p-3 text-left bg-secondary hover:bg-secondary/80 rounded-lg transition-colors flex items-center gap-2">
                📊 {t('dashboard.systemHealthCheck')}
              </button>
              <button className="w-full p-3 text-left bg-secondary hover:bg-secondary/80 rounded-lg transition-colors flex items-center gap-2">
                🎯 {t('dashboard.missionPlanning')}
              </button>
              <button className="w-full p-3 text-left bg-secondary hover:bg-secondary/80 rounded-lg transition-colors flex items-center gap-2">
                🛡️ {t('dashboard.securityAudit')}
              </button>
              <button className="w-full p-3 text-left bg-secondary hover:bg-secondary/80 rounded-lg transition-colors flex items-center gap-2">
                📈 {t('dashboard.generateReport')}
              </button>
            </div>
          </div>
        </div>
    </div>
  );
};

export default Dashboard;
