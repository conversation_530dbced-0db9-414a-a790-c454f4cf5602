import React from 'react';
import { BrowserRouter as Router, Route, Routes } from 'react-router-dom';
import Header from './components/Header';
import Sidebar from './components/Sidebar';
import Dashboard from './components/Dashboard';
import EquipmentStatus from './components/EquipmentStatus';
import MissionStatistics from './components/MissionStatistics';
import SimulationResults from './components/SimulationResults';
import Weapons from './components/Weapons';
import Missions from './components/Missions';
import Analytics from './components/Analytics';
import Geospatial from './components/Geospatial';
import Settings from './components/Settings';
import Profile from './components/Profile';
import Roles from './components/Roles';
import Users from './components/Users';
import DataTransfer from './components/DataTransfer';
import AuditLog from './components/AuditLog';
import VisualSandbox from './components/VisualSandbox';
import Chat from './components/Chat';

const App: React.FC = () => {
  return (
    <Router>
      <div className="flex h-screen bg-white text-drdo-navy font-inter overflow-hidden">
        <Sidebar />
        <div className="flex-1 ml-64">
          <Header />
          <main className="p-6 overflow-auto h-[calc(100vh-64px)] relative">
            <div className="liquid-effect"></div>
            <Routes>
              <Route path="/" element={<Dashboard />} />
              <Route path="/equipment-status" element={<EquipmentStatus />} />
              <Route path="/mission-statistics" element={<MissionStatistics />} />
              <Route path="/simulation-results" element={<SimulationResults />} />
              <Route path="/weapons" element={<Weapons />} />
              <Route path="/missions" element={<Missions />} />
              <Route path="/analytics" element={<Analytics />} />
              <Route path="/geospatial" element={<Geospatial />} />
              <Route path="/settings" element={<Settings />} />
              <Route path="/profile" element={<Profile />} />
              <Route path="/roles" element={<Roles />} />
              <Route path="/users" element={<Users />} />
              <Route path="/data-transfer" element={<DataTransfer />} />
              <Route path="/audit-log" element={<AuditLog />} />
              <Route path="/visual-sandbox" element={<VisualSandbox />} />
              <Route path="/chat" element={<Chat />} />
            </Routes>
          </main>
        </div>
      </div>
    </Router>
  );
};

export default App;
