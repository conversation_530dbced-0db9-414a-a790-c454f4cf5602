import React from 'react';
import { BrowserRouter as Router, Route, Routes } from 'react-router-dom';
import Header from './components/Header';
import Sidebar from './components/Sidebar';
import Dashboard from './components/Dashboard';
import EquipmentStatus from './components/EquipmentStatus';
import MissionStatistics from './components/MissionStatistics';
import SimulationResults from './components/SimulationResults';
import Settings from './components/Settings';
import { Profile } from './components/Profile';

const App: React.FC = () => {
  return (
    <Router>
      <div className="flex h-screen bg-drdo-white text-drdo-navy font-inter overflow-hidden">
        <Sidebar />
        <div className="flex-1 ml-60">
          <Header />
          <main className="p-4 overflow-auto h-[calc(100vh-60px)] relative">
            <div className="liquid-effect"></div>
            <Routes>
              <Route path="/" element={<Dashboard />} />
              <Route path="/equipment-status" element={<EquipmentStatus />} />
              <Route path="/mission-statistics" element={<MissionStatistics />} />
              <Route path="/simulation-results" element={<SimulationResults />} />
              <Route path="/settings" element={<Settings />} />
              <Route path="/profile" element={<Profile />} />
            </Routes>
          </main>
        </div>
      </div>
    </Router>
  );
};

export default App;
