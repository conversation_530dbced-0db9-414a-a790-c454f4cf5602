{"version": 3, "names": ["_objectDestructuringEmpty", "obj", "TypeError"], "sources": ["../../src/helpers/objectDestructuringEmpty.ts"], "sourcesContent": ["/* @minVersion 7.0.0-beta.0 */\n\nexport default function _objectDestructuringEmpty<T>(\n  obj: T | null | undefined,\n): asserts obj is T {\n  if (obj == null) throw new TypeError(\"Cannot destructure \" + obj);\n}\n"], "mappings": ";;;;;;AAEe,SAASA,yBAAyBA,CAC/CC,GAAyB,EACP;EAClB,IAAIA,GAAG,IAAI,IAAI,EAAE,MAAM,IAAIC,SAAS,CAAC,qBAAqB,GAAGD,GAAG,CAAC;AACnE", "ignoreList": []}