import React from 'react';
import { useTranslation } from 'react-i18next';
import LanguageSwitcher from './LanguageSwitcher';
import DRD<PERSON>ogo from './DRDOLogo';

const Header: React.FC = () => {
  const { t } = useTranslation();
  return (
    <header className="bg-primary text-primary-foreground p-4 flex items-center justify-between fixed h-16 z-20 shadow-lg border-b border-border top-0 left-60 header-width" style={{ width: 'calc(100% - 240px)' }}>
      <div className="flex items-center gap-4">
        <div className="flex items-center gap-3">
          {/* DRDO Logo */}
          <div className="w-10 h-10 bg-primary-foreground rounded-lg flex items-center justify-center">
            <DRDOLogo className="text-primary" size={28} />
          </div>
          <div>
            <h1 className="text-lg font-semibold">{t('dashboard.title')}</h1>
            <p className="text-xs text-primary-foreground/70">{t('dashboard.subtitle')}</p>
          </div>
        </div>
        <div className="bg-success text-success-foreground px-2 py-1 rounded text-xs font-medium animate-tactical-pulse">
          {t('common.classified')}
        </div>
      </div>
      <div className="flex items-center gap-4">
        <div className="flex items-center gap-2">
          <div className="w-2 h-2 bg-success rounded-full animate-pulse"></div>
          <span className="text-sm">{t('common.online')}</span>
        </div>
        <div className="text-sm font-mono">
          {new Date().toLocaleTimeString('en-IN')}
        </div>
        <LanguageSwitcher />
      </div>
    </header>
  );
};

export default Header;
