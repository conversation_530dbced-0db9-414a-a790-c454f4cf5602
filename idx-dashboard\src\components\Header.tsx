import React from 'react';

const Header: React.FC = () => {
  return (
    <header className="bg-primary text-primary-foreground p-4 flex items-center justify-between fixed w-[calc(100%-240px)] z-20 shadow-lg border-b border-border">
      <div className="flex items-center gap-4">
        <div className="flex items-center gap-2">
          <div className="w-8 h-8 bg-primary-foreground text-primary rounded-full flex items-center justify-center font-bold text-sm">
            IDX
          </div>
          <div>
            <h1 className="text-lg font-semibold">DRDO IDX Dashboard</h1>
            <p className="text-xs text-primary-foreground/70">Interactive Defense eXcellence</p>
          </div>
        </div>
        <div className="bg-success text-success-foreground px-2 py-1 rounded text-xs font-medium animate-tactical-pulse">
          CLASSIFIED
        </div>
      </div>
      <div className="flex items-center gap-4">
        <div className="flex items-center gap-2">
          <div className="w-2 h-2 bg-success rounded-full animate-pulse"></div>
          <span className="text-sm">System Online</span>
        </div>
        <div className="text-sm font-mono">
          {new Date().toLocaleTimeString('en-IN')}
        </div>
        <div className="flex gap-2">
          <button className="px-3 py-1 bg-accent text-accent-foreground rounded text-sm hover:bg-accent/80 transition-colors">
            EN
          </button>
          <button className="px-3 py-1 bg-accent text-accent-foreground rounded text-sm hover:bg-accent/80 transition-colors">
            हिं
          </button>
        </div>
      </div>
    </header>
  );
};

export default Header;
