// DRDO IDX - Military Data Types

export interface SystemHealth {
  component: string;
  performance: number;
  status: 'HEALTHY' | 'WARNING' | 'CRITICAL' | 'OFFLINE';
  lastCheck: Date;
  issues?: string[];
  location?: string;
  priority?: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
}

export interface ThreatAlert {
  id: string;
  type: 'CYBER' | 'PHYSICAL' | 'INTELLIGENCE' | 'COMMUNICATION';
  severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  description: string;
  timestamp: Date;
  location?: string;
  status: 'ACTIVE' | 'INVESTIGATING' | 'RESOLVED' | 'DISMISSED';
  assignedTo?: string;
}

export interface MilitaryAsset {
  id: string;
  name: string;
  type: 'MISSILE' | 'AIRCRAFT' | 'RADAR' | 'COMMUNICATION' | 'SATELLITE' | 'GROUND_VEHICLE';
  status: 'OPERATIONAL' | 'MAINTENANCE' | 'DEPLOYED' | 'STANDBY' | 'OFFLINE';
  location: string;
  readiness: number; // 0-100
  lastMaintenance: Date;
  nextMaintenance: Date;
  specifications?: {
    range?: number;
    speed?: number;
    payload?: number;
    accuracy?: number;
  };
}

export interface Mission {
  id: string;
  name: string;
  type: 'TRAINING' | 'OPERATION' | 'SURVEILLANCE' | 'DEFENSE' | 'RESEARCH';
  status: 'PLANNING' | 'ACTIVE' | 'COMPLETED' | 'CANCELLED' | 'ON_HOLD';
  priority: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  startDate: Date;
  endDate?: Date;
  progress: number; // 0-100
  assignedAssets: string[];
  location: string;
  commander?: string;
  objectives?: string[];
}

export interface SimulationResult {
  id: string;
  name: string;
  type: 'MISSILE_TRAJECTORY' | 'WEATHER_IMPACT' | 'SATELLITE_ORBIT' | 'COMBAT_SCENARIO';
  accuracy: number; // 0-100
  timestamp: Date;
  parameters: Record<string, any>;
  results: Record<string, any>;
  status: 'RUNNING' | 'COMPLETED' | 'FAILED' | 'CANCELLED';
}

export interface WeaponSystem {
  id: string;
  name: string;
  type: 'BRAHMOS' | 'AGNI' | 'PRITHVI' | 'TEJAS' | 'AKASH' | 'NAG';
  status: 'READY' | 'MAINTENANCE' | 'TESTING' | 'DEPLOYED' | 'OFFLINE';
  readiness: number; // 0-100
  location: string;
  lastTest: Date;
  nextTest: Date;
  specifications: {
    range: number;
    speed: number;
    accuracy: number;
    payload: number;
  };
  testResults?: {
    date: Date;
    accuracy: number;
    success: boolean;
    notes?: string;
  }[];
}

export interface Laboratory {
  id: string;
  name: string;
  location: string;
  type: 'RESEARCH' | 'TESTING' | 'DEVELOPMENT' | 'MANUFACTURING';
  status: 'ACTIVE' | 'MAINTENANCE' | 'OFFLINE' | 'RESTRICTED';
  capacity: number;
  currentProjects: number;
  securityLevel: 'PUBLIC' | 'RESTRICTED' | 'CONFIDENTIAL' | 'SECRET' | 'TOP_SECRET';
  personnel: number;
}

export interface User {
  id: string;
  username: string;
  rank: string;
  clearance: 'PUBLIC' | 'RESTRICTED' | 'CONFIDENTIAL' | 'SECRET' | 'TOP_SECRET';
  division: string;
  lastLogin: Date;
  permissions: string[];
  isActive: boolean;
}

export interface AuditLog {
  id: string;
  userId: string;
  action: string;
  resource: string;
  timestamp: Date;
  ipAddress: string;
  userAgent: string;
  success: boolean;
  details?: Record<string, any>;
}

export interface DataClassification {
  level: 'PUBLIC' | 'RESTRICTED' | 'CONFIDENTIAL' | 'SECRET' | 'TOP_SECRET';
  color: string;
  description: string;
  accessRequirements: string[];
}

// Utility types
export type SystemStatus = 'OPERATIONAL' | 'WARNING' | 'CRITICAL' | 'OFFLINE';
export type SecurityLevel = 'PUBLIC' | 'RESTRICTED' | 'CONFIDENTIAL' | 'SECRET' | 'TOP_SECRET';
export type Priority = 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';

// Chart data interfaces
export interface ChartDataPoint {
  label: string;
  value: number;
  color?: string;
  metadata?: Record<string, any>;
}

export interface TimeSeriesData {
  timestamp: Date;
  value: number;
  category?: string;
}

export interface GeospatialData {
  id: string;
  name: string;
  latitude: number;
  longitude: number;
  type: string;
  status: SystemStatus;
  metadata?: Record<string, any>;
}
