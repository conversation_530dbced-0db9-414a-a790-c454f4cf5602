import React, { useEffect, useRef } from 'react';
import * as d3 from 'd3';

const SimulationResults: React.FC = () => {
  const chartRef = useRef<SVGSVGElement>(null);

  const drawBarChart = (svgRef: SVGSVGElement, data: number[], labels: string[]) => {
    const svg = d3.select(svgRef);
    const width = 500;
    const height = 300;
    const margin = { top: 20, right: 20, bottom: 30, left: 40 };

    svg.selectAll('*').remove();
    svg.attr('width', width).attr('height', height);

    const x = d3.scaleBand().domain(labels).range([margin.left, width - margin.right]).padding(0.1);
    const y = d3.scaleLinear().domain([0, d3.max(data) || 100]).nice().range([height - margin.bottom, margin.top]);

    svg.append('g')
      .attr('transform', `translate(0,${height - margin.bottom})`)
      .call(d3.axisBottom(x));

    svg.append('g')
      .attr('transform', `translate(${margin.left},0)`)
      .call(d3.axisLeft(y));

    svg.selectAll('.bar')
      .data(data)
      .enter()
      .append('rect')
      .attr('class', 'bar')
      .attr('x', (d, i) => x(labels[i]) || 0)
      .attr('y', (d) => y(d))
      .attr('width', x.bandwidth())
      .attr('height', (d) => height - margin.bottom - y(d))
      .attr('fill', '#003087')
      .attr('stroke', '#FF9933')
      .attr('stroke-width', '1px')
      .style('transition', 'all 0.3s');
  };

  useEffect(() => {
    if (chartRef.current) {
      drawBarChart(chartRef.current, [90, 85, 95], ['Run 1', 'Run 2', 'Run 3']);
    }
  }, []);

  return (
    <div className="space-y-6">
      <h2 className="text-3xl font-semibold">Simulation Results</h2>
      <div className="bg-white p-6 rounded-lg shadow-lg">
        <svg ref={chartRef} className="w-full h-64"></svg>
      </div>
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-white p-6 rounded-lg shadow-lg text-center">
          <div className="text-3xl font-bold text-drdo-navy">90%</div>
          <div className="text-sm text-gray-600">Run 1 Accuracy</div>
        </div>
        <div className="bg-white p-6 rounded-lg shadow-lg text-center">
          <div className="text-3xl font-bold text-drdo-saffron">85%</div>
          <div className="text-sm text-gray-600">Run 2 Accuracy</div>
        </div>
        <div className="bg-white p-6 rounded-lg shadow-lg text-center">
          <div className="text-3xl font-bold text-green-600">95%</div>
          <div className="text-sm text-gray-600">Run 3 Accuracy</div>
        </div>
      </div>
    </div>
  );
};

export default SimulationResults;
