import React from 'react';
import { Bar } from 'react-chartjs-2';
import { Chart as ChartJS, CategoryScale, LinearScale, BarElement, Title, Tooltip, Legend } from 'chart.js';

ChartJS.register(CategoryScale, LinearScale, BarElement, Title, Tooltip, Legend);

const SimulationResults: React.FC = () => {
  const data = {
    labels: ['BrahMos Test', 'Agni-V Test', 'Prithvi Test'],
    datasets: [{
      label: 'Accuracy (%)',
      data: [90, 85, 95],
      backgroundColor: 'hsl(var(--primary) / 0.8)',
      borderColor: 'hsl(var(--primary))',
      borderWidth: 2,
    }],
  };

  const options = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: { position: 'top' as const },
      title: { display: true, text: 'Simulation Accuracy Results' }
    },
    scales: { y: { beginAtZero: true, max: 100 } },
  };

  return (
    <div className="p-6">
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-foreground">Simulation Results</h1>
            <p className="text-muted-foreground">3D trajectory analysis and accuracy testing</p>
          </div>
          <div className="bg-primary text-primary-foreground px-3 py-1 rounded text-sm font-medium">
            COMPLETED
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <div className="bg-card p-6 rounded-lg border border-border">
            <h3 className="text-lg font-semibold text-card-foreground mb-4 flex items-center gap-2">
              🎯 Accuracy Comparison
            </h3>
            <div className="h-64">
              <Bar data={data} options={options} />
            </div>
          </div>

          <div className="bg-card p-6 rounded-lg border border-border">
            <h3 className="text-lg font-semibold text-card-foreground mb-4 flex items-center gap-2">
              📊 Test Results
            </h3>
            <div className="space-y-4">
              <div className="p-4 bg-primary/10 rounded-lg border-l-4 border-l-primary">
                <div className="flex justify-between items-center">
                  <div>
                    <div className="font-medium">BrahMos Test</div>
                    <div className="text-sm text-muted-foreground">Supersonic cruise missile</div>
                  </div>
                  <div className="text-2xl font-bold text-primary">90%</div>
                </div>
              </div>
              <div className="p-4 bg-warning/10 rounded-lg border-l-4 border-l-warning">
                <div className="flex justify-between items-center">
                  <div>
                    <div className="font-medium">Agni-V Test</div>
                    <div className="text-sm text-muted-foreground">Intercontinental ballistic missile</div>
                  </div>
                  <div className="text-2xl font-bold text-warning">85%</div>
                </div>
              </div>
              <div className="p-4 bg-success/10 rounded-lg border-l-4 border-l-success">
                <div className="flex justify-between items-center">
                  <div>
                    <div className="font-medium">Prithvi Test</div>
                    <div className="text-sm text-muted-foreground">Surface-to-surface missile</div>
                  </div>
                  <div className="text-2xl font-bold text-success">95%</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SimulationResults;
