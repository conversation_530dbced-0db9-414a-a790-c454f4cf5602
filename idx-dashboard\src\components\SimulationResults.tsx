import React from 'react';
import { Bar } from 'react-chartjs-2';
import { Chart as ChartJS, CategoryScale, LinearScale, BarElement, Title, Tooltip, Legend } from 'chart.js';

ChartJS.register(CategoryScale, LinearScale, BarElement, Title, Tooltip, Legend);

const SimulationResults: React.FC = () => {
  const data = {
    labels: ['Run 1', 'Run 2', 'Run 3'],
    datasets: [{
      label: 'Accuracy (%)',
      data: [90, 85, 95],
      backgroundColor: 'rgba(0, 48, 135, 0.7)',
      borderColor: 'rgba(255, 153, 51, 1)',
      borderWidth: 1,
    }],
  };

  const options = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: { position: 'top' as const },
      title: { display: true, text: 'Simulation Accuracy' }
    },
    scales: { y: { beginAtZero: true, max: 100 } },
  };

  return (
    <div className="space-y-4">
      <h2 className="text-2xl font-semibold">Simulation Accuracy</h2>
      <div className="bg-white p-4 rounded-lg shadow-md h-64">
        <Bar data={data} options={options} />
      </div>
    </div>
  );
};

export default SimulationResults;
