/**
 * Easing functions adapted from <PERSON>'s easing equations.
 * @namespace Chart.helpers.easing.effects
 * @see http://www.robertpenner.com/easing/
 */
declare const effects: {
    readonly linear: (t: number) => number;
    readonly easeInQuad: (t: number) => number;
    readonly easeOutQuad: (t: number) => number;
    readonly easeInOutQuad: (t: number) => number;
    readonly easeInCubic: (t: number) => number;
    readonly easeOutCubic: (t: number) => number;
    readonly easeInOutCubic: (t: number) => number;
    readonly easeInQuart: (t: number) => number;
    readonly easeOutQuart: (t: number) => number;
    readonly easeInOutQuart: (t: number) => number;
    readonly easeInQuint: (t: number) => number;
    readonly easeOutQuint: (t: number) => number;
    readonly easeInOutQuint: (t: number) => number;
    readonly easeInSine: (t: number) => number;
    readonly easeOutSine: (t: number) => number;
    readonly easeInOutSine: (t: number) => number;
    readonly easeInExpo: (t: number) => number;
    readonly easeOutExpo: (t: number) => number;
    readonly easeInOutExpo: (t: number) => number;
    readonly easeInCirc: (t: number) => number;
    readonly easeOutCirc: (t: number) => number;
    readonly easeInOutCirc: (t: number) => number;
    readonly easeInElastic: (t: number) => number;
    readonly easeOutElastic: (t: number) => number;
    readonly easeInOutElastic: (t: number) => number;
    readonly easeInBack: (t: number) => number;
    readonly easeOutBack: (t: number) => number;
    readonly easeInOutBack: (t: number) => number;
    readonly easeInBounce: (t: number) => number;
    readonly easeOutBounce: (t: number) => number;
    readonly easeInOutBounce: (t: number) => number;
};
export type EasingFunction = keyof typeof effects;
export default effects;
