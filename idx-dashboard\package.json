{"name": "idx-dashboard", "private": true, "version": "1.0.0", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"@types/plotly.js": "^3.0.2", "chart.js": "^4.5.0", "d3": "^7.9.0", "i18next": "^25.3.1", "i18next-browser-languagedetector": "^8.2.0", "plotly.js": "^3.0.1", "react": "^18.3.0", "react-chartjs-2": "^5.3.0", "react-dom": "^18.3.0", "react-i18next": "^15.6.0", "react-plotly.js": "^2.6.0", "react-router-dom": "^6.22.0"}, "devDependencies": {"@types/d3": "^7.4.3", "@types/react": "^18.3.0", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.16", "postcss": "^8.4.31", "tailwindcss": "^3.4.0", "typescript": "^5.4.0", "vite": "^5.0.0"}}