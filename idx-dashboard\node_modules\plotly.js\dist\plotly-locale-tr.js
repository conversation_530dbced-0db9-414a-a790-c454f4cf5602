var locale={moduleType:"locale",name:"tr",dictionary:{Autoscale:"Otomatik \xd6l\xe7eklendir","Box Select":"Dikd\xf6rtgen Kutu Se\xe7imi","Click to enter Colorscale title":"Renk \xd6l\xe7e\u011fi ba\u015fl\u0131\u011f\u0131n\u0131 girmek i\xe7in t\u0131klay\u0131n","Click to enter Component A title":"A bile\u015feni ba\u015fl\u0131\u011f\u0131n\u0131 girmek i\xe7in t\u0131klay\u0131n","Click to enter Component B title":"B bile\u015feni ba\u015fl\u0131\u011f\u0131n\u0131 girmek i\xe7in t\u0131klay\u0131n","Click to enter Component C title":"C bile\u015feni ba\u015fl\u0131\u011f\u0131n\u0131 girmek i\xe7in t\u0131klay\u0131n","Click to enter Plot title":"Grafik ba\u015fl\u0131\u011f\u0131n\u0131 girmek i\xe7in t\u0131klay\u0131n","Click to enter X axis title":"X ekseni ba\u015fl\u0131\u011f\u0131n\u0131 girmek i\xe7in t\u0131klay\u0131n","Click to enter Y axis title":"Y ekseni ba\u015fl\u0131\u011f\u0131n\u0131 girmek i\xe7in t\u0131klay\u0131n","Compare data on hover":"\xdczerine gelince verileri kar\u015f\u0131la\u015ft\u0131r","Double-click on legend to isolate one trace":"Bir izi izole etmek i\xe7in g\xf6stergeye (legend) \xe7ift t\u0131klay\u0131n","Double-click to zoom back out":"Geri uzakla\u015ft\u0131rmak i\xe7in \xe7ift t\u0131klay\u0131n","Download plot as a png":"Grafi\u011fi PNG olarak indir","Download plot":"Grafi\u011fi indir","Edit in Chart Studio":"Chart Studio'da d\xfczenle","IE only supports svg.  Changing format to svg.":"IE yaln\u0131zca svg'yi destekler. Format svg'ye d\xf6n\xfc\u015ft\xfcr\xfcl\xfcyor.","Lasso Select":"Serbest se\xe7im","Orbital rotation":"Y\xf6r\xfcngesel d\xf6nd\xfcrme",Pan:"Kayd\u0131r","Produced with Plotly.js":"Plotly.js ile \xfcretilmi\u015ftir",Reset:"S\u0131f\u0131rla","Reset axes":"Eksenleri s\u0131f\u0131rla","Reset camera to default":"Kameray\u0131 varsay\u0131lana s\u0131f\u0131rla","Reset camera to last save":"Kameray\u0131 son kaydedilene s\u0131f\u0131rla","Reset view":"G\xf6r\xfcn\xfcm\xfc s\u0131f\u0131rla","Reset views":"G\xf6r\xfcn\xfcmleri s\u0131f\u0131rla","Show closest data on hover":"\xdczerine gelince en yak\u0131n veriyi g\xf6ster","Snapshot succeeded":"Anl\u0131k g\xf6r\xfcnt\xfc al\u0131nd\u0131","Sorry, there was a problem downloading your snapshot!":"\xdczg\xfcn\xfcz, anl\u0131k g\xf6r\xfcnt\xfcn\xfcz indirilirken bir sorun olu\u015ftu!","Taking snapshot - this may take a few seconds":"Anl\u0131k g\xf6r\xfcnt\xfc al\u0131n\u0131yor - bu i\u015flem birka\xe7 saniye s\xfcrebilir",Zoom:"Yak\u0131nla\u015ft\u0131r","Zoom in":"Yak\u0131nla\u015f","Zoom out":"Uzakla\u015f","close:":"kapan\u0131\u015f:",trace:"iz","lat:":"enlem:","lon:":"boylam:","q1:":"\xe71:","q3:":"\xe73:","source:":"kaynak:","target:":"hedef:","lower fence:":"alt s\u0131n\u0131r:","upper fence:":"\xfcst s\u0131n\u0131r:","max:":"maks:","mean \xb1 \u03c3:":"ortalama \xb1 \u03c3:","mean:":"ortalama:","median:":"medyan:","min:":"min:","Turntable rotation":"D\xf6ner tabla (turntable) d\xf6n\xfc\u015f\xfc","Toggle Spike Lines":"Yard\u0131mc\u0131 \xc7izgileri A\xe7/Kapat","open:":"a\xe7\u0131l\u0131\u015f:","high:":"y\xfcksek:","low:":"d\xfc\u015f\xfck:","Toggle show closest data on hover":"\xdczerine gelince en yak\u0131n veriyi g\xf6sterme se\xe7ene\u011fini a\xe7/kapat","incoming flow count:":"gelen ak\u0131\u015f say\u0131s\u0131:","outgoing flow count:":"giden ak\u0131\u015f say\u0131s\u0131:","kde:":"\xe7ekirdek yo\u011funluk tahmini (kde):"},format:{days:["Pazar","Pazartesi","Sal\u0131","\xc7ar\u015famba","Per\u015fembe","Cuma","Cumartesi"],shortDays:["Pz","Pt","Sa","\xc7a","Pe","Cu","Ct"],months:["Ocak","\u015eubat","Mart","Nisan","May\u0131s","Haziran","Temmuz","A\u011fustos","Eyl\xfcl","Ekim","Kas\u0131m","Aral\u0131k"],shortMonths:["Oca","\u015eub","Mar","Nis","May","Haz","Tem","A\u011fu","Eyl","Eki","Kas","Ara"],date:"%d.%m.%Y",decimal:",",thousands:"."}};"undefined"==typeof Plotly?(window.PlotlyLocales=window.PlotlyLocales||[],window.PlotlyLocales.push(locale)):Plotly.register(locale);