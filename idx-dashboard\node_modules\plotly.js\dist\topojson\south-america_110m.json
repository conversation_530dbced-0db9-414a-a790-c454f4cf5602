{"type": "Topology", "objects": {"coastlines": {"type": "GeometryCollection", "geometries": [{"type": "LineString", "arcs": [0]}, {"type": "LineString", "arcs": [1]}, {"type": "LineString", "arcs": [2]}, {"type": "LineString", "arcs": [3]}, {"type": "LineString", "arcs": [4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104, 105, 106, 107]}, {"type": "LineString", "arcs": [108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 123, 124, 125, 126, 127, 128, 129, 130, 131, 132, 133, 134, 135, 136, 137, 138, 139, 140, 141, 142, 143, 144, 145, 146, 147, 148, 149, 150, 151, 152, 153, 154, 155, 156, 157, 158, 159]}, {"type": "LineString", "arcs": [160, 161, 162, 163, 164]}, {"type": "MultiLineString", "arcs": [[165, 166, 167, 168, 169, 170, 171], [172, 173, 174, 175, 176, 177, 178]]}, {"type": "LineString", "arcs": [179, 180, 181]}, {"type": "LineString", "arcs": [182, 183, 184]}, {"type": "LineString", "arcs": [185]}, {"type": "LineString", "arcs": [186]}, {"type": "LineString", "arcs": [187]}, {"type": "LineString", "arcs": [188]}]}, "land": {"type": "GeometryCollection", "geometries": [{"type": "Polygon", "arcs": [[196, 164, 160, 161, 162]]}, {"type": "Polygon", "arcs": [[186]]}, {"type": "Polygon", "arcs": [[4, 197, 6, 198, 199, 9, 200, 201, 202, 13, 203, 204, 205, 17, 206, 19, 207, 208, 22, 23, 209, 210, 26, 27, 211, 212, 213, 214, 215, 216, 34, 217, 218, 219, 220, 221, 38, 222, 40, 223, 224, 43, 225, 45, 226, 47, 227, 228, 50, 229, 230, 53, 231, 55, 232, 57, 233, 59, 234, 235, 236, 63, 237, 65, 238, 239, 240, 241, 242, 243, 72, 244, 245, 246, 247, 77, 78, 248, 80, 249, 82, 250, 251, 85, 252, 253, 88, 254, 90, 255, 92, 256, 257, 258, 259, 260, 98, 261, 262, 101, 263, 103, 104, 264, 265, 107, -160, 266, -158, 267, -156, 268, -154, 269, 270, 271, 272, -150, 273, 274, -147, 275, -145, 276, 277, 278, -141, 279, 280, 281, 282, 283, 284, 285, -133, 286, 287, 288, -129, 289, -127, 290, -125, 291, -123, 292, 293, -120, 294, -118, 295, -116, 296, 297, 298, -112, 299, 300, 301, 302]]}]}, "ocean": {"type": "GeometryCollection", "geometries": [{"type": "Polygon", "arcs": [[-186, 303, -4, 304, -172, 305, -170, 306, -168, 307, -166, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 123, 124, 125, 126, 127, 128, 129, 130, 131, 132, 133, 134, 135, 136, 137, 138, 139, 140, 141, 142, 143, 144, 145, 146, 147, 148, 149, 150, -272, 308, 152, 153, 154, 155, 156, 157, 158, 159, -108, -107, -106, -105, -104, -103, -102, -101, -100, -99, -98, -97, -96, -95, -94, -93, -92, -91, -90, -89, -88, -87, -86, -85, -84, -83, -82, -81, -80, -79, -78, -77, -76, -75, -74, -73, -72, -71, -70, -69, -68, -67, -66, -65, -64, -63, -62, -61, -60, -59, -58, -57, -56, -55, -54, -53, -52, -51, -50, -49, -48, -47, -46, -45, -44, -43, -42, -41, -40, -39, -222, 309, -37, -36, -35, -34, -33, -32, -31, -30, -29, -28, -27, -26, -25, -24, 310, -22, -21, -20, -19, -18, -17, -16, -15, -14, -13, -12, -11, -10, -9, -8, -7, -6, -5, -182, 311, -180, -179, 312, -177, 313, -175, 314, -173, 315, -189, 316], [-3], [317, -183, -185], [-1], [-2], [-188], [-187], [-161, -165, -197, -163, -162]]}]}, "lakes": {"type": "GeometryCollection", "geometries": [{"type": "Polygon", "arcs": [[318]]}]}, "rivers": {"type": "GeometryCollection", "geometries": [{"type": "LineString", "arcs": [189, 190, 191, 192, 193, 194]}, {"type": "LineString", "arcs": [195]}]}, "countries": {"type": "GeometryCollection", "geometries": [{"type": "MultiPolygon", "properties": {"ct": [-65.15, -35.22]}, "id": "ARG", "arcs": [[[161, 319]], [[320, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 321, 322, 323, 324, 325, 326]]]}, {"type": "MultiPolygon", "properties": {"ct": [-71.67, -37.34]}, "id": "CHL", "arcs": [[[-320, 162, 163, 164, 160]], [[327, -322, 104, 105, 106, 107, -160, -159, -158, -157, -156, -155, -154, -153, -152, -151, -150, -149, -148, -147, -146, -145, -144, -143, -142, 328]]]}, {"type": "Polygon", "properties": {"ct": [-59.42, -51.71]}, "id": "FLK", "arcs": [[186]]}, {"type": "Polygon", "properties": {"ct": [-56, -32.78]}, "id": "URY", "arcs": [[329, 72, 73, 74, 75, 76, 77, -321]]}, {"type": "Polygon", "properties": {"ct": [-53.05, -10.81]}, "id": "BRA", "arcs": [[-330, -327, 330, 331, -191, 332, 333, 334, 335, 336, 337, 219, -310, 221, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71]]}, {"type": "Polygon", "properties": {"ct": [-64.64, -16.73]}, "id": "BOL", "arcs": [[-333, 338, -323, -328, 339]]}, {"type": "Polygon", "properties": {"ct": [-74.39, -9.19]}, "id": "PER", "arcs": [[-334, -340, -329, -141, -140, -139, -138, -137, -136, -135, -134, -133, -132, -131, -130, -129, -128, 340, 341]]}, {"type": "Polygon", "properties": {"ct": [-73.08, 3.93]}, "id": "COL", "arcs": [[-335, -342, 342, -120, -119, -118, -117, -116, -115, -114, -113, -112, -111, -110, -109, 302, 4, 5, 6, 7, 8, 9, 10, 343]]}, {"type": "Polygon", "properties": {"ct": [-66.16, 7.16]}, "id": "VEN", "arcs": [[-336, -344, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 344]]}, {"type": "Polygon", "properties": {"ct": [-58.97, 4.79]}, "id": "GUY", "arcs": [[-337, -345, 27, 28, 29, 30, 31, 345]]}, {"type": "Polygon", "properties": {"ct": [-55.91, 4.12]}, "id": "SUR", "arcs": [[-338, -346, 32, 33, 34, 35, 218]]}, {"type": "Polygon", "properties": {"ct": [-78.38, -1.45]}, "id": "ECU", "arcs": [[-341, -127, -126, -125, -124, -123, -122, -121, -343]]}, {"type": "Polygon", "properties": {"ct": [-58.39, -23.25]}, "id": "PRY", "arcs": [[190, -332, -331, -326, -325, -324, -339]]}]}, "subunits": {"type": "GeometryCollection", "geometries": []}}, "arcs": [[[4816, 9316], [73, -9], [26, -21], [-37, -26], [-107, 0], [-83, -3], [-9, 45], [20, 15], [117, -1]], [[3204, 9314], [96, -10], [76, -25], [24, -29], [-101, -2], [-43, -17], [-80, 17], [-82, 38], [17, 24], [60, 7], [33, -3]], [[2533, 9808], [123, -7], [112, -1], [135, -36], [56, -39], [134, 12], [51, -25], [120, -65], [89, -47], [47, 1], [86, -21], [-11, -30], [106, -4], [107, -43], [-16, -25], [-95, -13], [-97, -6], [-98, 9], [-205, -10], [96, 58], [-58, 28], [-92, 7], [-49, 30], [-34, 60], [-81, -4], [-133, 28], [-43, 22], [-186, 16], [-50, 21], [53, 26], [-140, 6], [-102, -55], [-59, -1], [-21, -26], [-71, -12], [-61, 10], [76, 33], [31, 38], [64, 23], [73, 21], [108, 10], [35, 11]], [[4038, 0], [-2, 52], [82, 50], [131, 16], [76, -39], [33, -39], [52, -40]], [[3235, 8280], [74, -3]], [[3309, 8277], [107, 73]], [[3416, 8350], [59, 12]], [[3475, 8362], [1, 34]], [[3476, 8396], [26, 89]], [[3502, 8485], [82, 49], [90, 2], [12, 22], [112, -9], [112, 54], [56, 23], [69, 51], [50, -7], [38, -27]], [[4123, 8643], [-28, -36]], [[4095, 8607], [-4, -25]], [[4091, 8582], [-84, -12]], [[4007, 8570], [47, -48], [-2, -55], [-63, -61], [54, -83], [62, 6], [32, 76]], [[4137, 8405], [-45, 38]], [[4092, 8443], [-7, 79]], [[4085, 8522], [178, 43]], [[4263, 8565], [-20, 50]], [[4243, 8615], [50, 33]], [[4293, 8648], [52, -74], [100, -2]], [[4445, 8572], [93, -59]], [[4538, 8513], [5, -34]], [[4543, 8479], [128, -1], [153, 10]], [[4824, 8488], [82, -47], [109, -13], [80, 33], [2, 27]], [[5097, 8488], [177, 6]], [[5274, 8494], [171, 2]], [[5445, 8496], [-121, -32], [49, -49], [114, -8], [108, -52], [23, -84], [74, 2], [56, -25]], [[5748, 8248], [94, -38], [88, -69]], [[5930, 8141], [4, -54]], [[5934, 8087], [54, -3]], [[5988, 8084], [77, -51]], [[6065, 8033], [56, -37]], [[6121, 7996], [171, -21]], [[6292, 7975], [16, 19]], [[6308, 7994], [115, 8]], [[6423, 8002], [154, -28]], [[6577, 7974], [48, -12], [105, -25], [152, -89], [23, -43]], [[6905, 7805], [51, -4], [33, -49]], [[6989, 7752], [80, -184]], [[7069, 7568], [77, -18]], [[7146, 7550], [4, -72], [-108, -87], [45, -32], [252, -16], [5, -106], [109, 69], [180, -37], [237, -65], [70, -61]], [[7940, 7143], [-24, -59]], [[7916, 7084], [166, 33]], [[8082, 7117], [278, -56], [214, 4], [211, -87], [182, -118], [110, -30], [122, -4], [52, -34]], [[9251, 6792], [49, -134]], [[9300, 6658], [23, -63], [-57, -174], [-72, -69]], [[9194, 6352], [-202, -146]], [[8992, 6206], [-91, -119]], [[8901, 6087], [-105, -92]], [[8796, 5995], [-36, -2]], [[8760, 5993], [-40, -77]], [[8720, 5916], [10, -197]], [[8730, 5719], [-40, -163]], [[8690, 5556], [-15, -69]], [[8675, 5487], [-45, -41]], [[8630, 5446], [-25, -141]], [[8605, 5305], [-145, -138]], [[8460, 5167], [-24, -108]], [[8436, 5059], [-116, -46]], [[8320, 5013], [-33, -63], [-156, 0], [-224, -40]], [[7907, 4910], [-101, -47]], [[7806, 4863], [-160, -31]], [[7646, 4832], [-168, -84]], [[7478, 4748], [-121, -104]], [[7357, 4644], [-21, -79]], [[7336, 4565], [24, -58], [-27, -106], [-32, -51]], [[7301, 4350], [-100, -58]], [[7201, 4292], [-158, -186]], [[7043, 4106], [-126, -83]], [[6917, 4023], [-97, -49]], [[6820, 3974], [-65, -100]], [[6755, 3874], [-95, -61]], [[6660, 3813], [-62, -66], [-161, -58]], [[6437, 3689], [-105, 21]], [[6332, 3710], [-78, -11]], [[6254, 3699], [-132, 45]], [[6122, 3744], [-97, -4]], [[6025, 3740], [-87, 59]], [[5938, 3799], [-9, -55]], [[5929, 3744], [181, -90]], [[6110, 3654], [-20, -73], [90, -46]], [[6180, 3535], [-8, -51]], [[6172, 3484], [-137, -135], [-212, -57], [-286, -22], [-157, 11]], [[5380, 3281], [30, -63]], [[5410, 3218], [-29, -79]], [[5381, 3139], [26, -53], [-86, -37]], [[5321, 3049], [-146, -14]], [[5175, 3035], [-137, 38]], [[5038, 3073], [-55, -27]], [[4983, 3046], [20, -105]], [[5003, 2941], [96, -32]], [[5099, 2909], [78, 33]], [[5177, 2942], [43, -54], [-132, -33], [-114, -65]], [[4974, 2790], [-21, -106]], [[4953, 2684], [-34, -57]], [[4919, 2627], [-135, 0]], [[4784, 2627], [-112, -54]], [[4672, 2573], [-41, -79]], [[4631, 2494], [140, -77], [137, -21]], [[4908, 2396], [-49, -94]], [[4859, 2302], [-169, -60]], [[4690, 2242], [-93, -123], [-130, -42], [-59, -49], [46, -109]], [[4454, 1919], [96, -61]], [[4550, 1858], [-61, 5]], [[4489, 1863], [-127, 1]], [[4362, 1864], [-69, -26]], [[4293, 1838], [-128, -38]], [[4165, 1800], [-23, -98], [-61, -3], [-161, 34], [-164, 74], [-177, 60]], [[3159, 8128], [58, -56]], [[3217, 8072], [23, -89]], [[3240, 7983], [-31, -28]], [[3209, 7955], [32, -96]], [[3241, 7859], [-27, -61], [53, -25]], [[3267, 7773], [-55, -55]], [[3212, 7718], [-60, -66]], [[3152, 7652], [-71, -8]], [[3081, 7644], [-33, -38]], [[3048, 7606], [6, -52]], [[3054, 7554], [-53, -8]], [[3001, 7546], [19, -33]], [[3020, 7513], [-98, -42]], [[2922, 7471], [-78, -22]], [[2844, 7449], [10, -43], [-54, -68], [-26, -66]], [[2774, 7272], [-51, -16], [25, -95]], [[2748, 7161], [-29, -30], [85, -46]], [[2804, 7085], [55, 49]], [[2859, 7134], [31, -46], [-76, -79]], [[2814, 7009], [-114, -66]], [[2700, 6943], [-45, -74], [70, -100]], [[2725, 6769], [-47, -47]], [[2678, 6722], [102, -43]], [[2780, 6679], [111, -69]], [[2891, 6610], [45, -77], [58, -48], [135, -210]], [[3129, 6275], [141, -194]], [[3270, 6081], [121, -138]], [[3391, 5943], [-23, -30]], [[3368, 5913], [59, -87]], [[3427, 5826], [110, -65]], [[3537, 5761], [256, -115]], [[3793, 5646], [283, -106]], [[4076, 5540], [13, -43], [143, -60]], [[4232, 5437], [30, -149]], [[4262, 5288], [10, -172]], [[4272, 5116], [-44, -235]], [[4228, 4881], [-46, -219]], [[4182, 4662], [-26, -204]], [[4156, 4458], [-84, -128], [18, -130], [-43, -87]], [[4047, 4113], [33, -157]], [[4080, 3956], [-61, -157]], [[4019, 3799], [-98, -169], [-88, -170], [-60, -3], [12, -119], [41, -102], [-66, -72]], [[3760, 3164], [-49, -195]], [[3711, 2969], [-45, -151], [91, -15]], [[3757, 2803], [44, 132]], [[3801, 2935], [96, -28], [-75, -218], [-158, 37], [-49, -175]], [[3615, 2551], [-136, -93]], [[3479, 2458], [217, -31]], [[3696, 2427], [-151, -81]], [[3545, 2346], [-61, -101], [19, -180], [71, -70]], [[3574, 1995], [-40, -61]], [[3534, 1934], [45, -67]], [[3619, 1806], [118, -22], [201, -70], [189, -38], [74, 48], [46, 73], [132, 43], [101, -12]], [[4480, 1828], [55, -49], [72, -79], [185, -63], [200, -27], [-64, -52], [-136, -6], [-72, 38]], [[4720, 1590], [-48, -43], [-122, -33]], [[4550, 1514], [-70, 4], [-85, 8]], [[4395, 1526], [-104, 32], [-149, 15], [-180, 59], [-146, 57], [-197, 117]], [[3159, 8128], [-47, 30], [-31, 57], [36, 28], [-37, 8], [-26, 34], [-71, 30], [-63, -7], [-29, -37], [-58, -26], [-31, -4], [-14, -22], [68, -57], [-39, -13], [-20, -16], [-67, -5], [-24, 63], [-19, -18], [-47, 6], [-29, 42], [-59, 7], [-37, 12]], [[2515, 8240], [-30, 0], [-31, 0]], [[2454, 8240], [-4, -23], [-17, 16], [-77, 24], [-29, 22], [16, 18], [-5, 23], [-40, 26], [-56, 20], [-49, 14], [-9, 31], [-38, 18], [9, -30], [-28, -25], [-33, 29], [-46, 10], [-19, 21], [1, 32], [18, 34]], [[2048, 8500], [-20, 7], [-20, 7]], [[2008, 8514], [33, 21], [-50, 33], [-66, 42], [-32, 36], [-60, 33], [-72, 47], [16, 17], [24, -16], [11, 7], [-25, 33]], [[1787, 8767], [-22, 5], [-21, 4]], [[1744, 8776], [-16, -24], [-83, 1], [-51, 10], [-59, 21], [-80, 7], [-40, 22], [-74, 19], [-89, 2], [-65, 21], [-77, 43], [-161, 113], [-74, 34], [-117, 28], [-80, -8], [-114, -39], [-72, -11], [-101, 28], [-107, 20], [-134, 48], [-107, 15], [-43, 13]], [[354, 9999], [-1, -1], [-25, -76], [-10, -141], [-14, -51], [25, -57], [44, -52], [28, -81], [95, -79], [34, -60], [56, -52], [151, -28], [59, -44], [126, 30], [108, 10], [107, 19], [90, 18], [91, 43], [34, 62], [12, 88], [25, 31], [96, 28], [151, 24], [127, -4], [87, 9], [34, -22], [-5, -51], [-77, -62], [-34, -64], [27, -19], [-22, -45], [-36, -82], [-36, 27], [-30, -2], [1, -16], [27, 0], [-3, -29], [-23, -45]], [[1673, 9225], [13, -17], [-15, -37]], [[1671, 9171], [9, -10], [-17, -53], [-28, -28], [-26, -4], [-28, -36], [47, -19], [12, 16]], [[1640, 9037], [47, -15], [10, -3]], [[1697, 9019], [31, 18], [41, 2], [13, -9], [22, 6], [67, -10], [66, 3], [46, 12], [17, 11], [45, -5], [34, -7], [38, 2], [28, 9], [65, -14], [23, -3], [43, -19], [42, -24], [52, -16], [37, -29], [-12, -10], [-7, -24], [14, -38], [-33, -36], [-15, -42], [-5, -47], [8, -27], [4, -47], [-22, -11], [-13, -45], [9, -27], [-29, -27], [7, -29], [22, -17]], [[2335, 8519], [9, -16], [27, -41]], [[2371, 8462], [55, -43], [67, -44]], [[2493, 8375], [51, -38]], [[2544, 8337], [-2, -23], [57, -4]], [[2599, 8310], [13, 8], [39, -26], [71, 8], [60, 27], [87, 21], [49, 31], [79, -6], [-6, -10], [80, -4], [64, -18], [46, -32], [54, -29]], [[4041, 9443], [18, 18]], [[4059, 9461], [29, 2], [82, -3]], [[4170, 9460], [85, -27], [37, 3], [26, -38], [78, 2], [-4, -31], [63, -4], [71, -38], [-53, -43], [-68, 23], [-66, -5], [-47, 5], [-26, -19], [-55, -6], [-21, 25], [-48, -15], [-57, -72], [-36, 17], [-8, 30], [-95, 18], [-67, -7], [-87, 7], [-67, -20], [-77, 33], [13, 34], [132, -14], [107, -9], [52, 24], [-65, 45], [1, 40], [-91, 17], [33, 29], [87, -5], [124, -16]], [[4499, 0], [-6, 30], [14, 41], [67, 39], [56, 43], [23, 42], [-28, 45], [-17, 41], [70, 48], [78, 31], [93, 39], [97, 33], [115, 31], [56, 45], [78, 29], [90, 27], [137, 6], [90, 33], [100, 21], [118, 12], [103, 27], [81, 33], [112, 12], [84, -27], [-53, -35], [-145, -31], [-62, -22], [-106, 16], [-118, -10], [-98, -25], [-103, -27], [-70, -31], [-20, -41], [9, -39], [67, -35], [-98, -25], [-134, -8], [-79, -35], [-84, -33], [-89, -45], [-23, -40], [51, -43], [75, -33], [118, -25], [109, -33], [59, -41], [31, -39], [0, -1]], [[5542, 1910], [172, 63], [121, -26], [86, 42], [114, -47], [-43, -37], [-193, -32], [-64, 37], [-121, -47], [-72, 47]], [[5474, 8500], [82, 14], [30, -4], [-6, -78], [-119, -12], [-26, 10], [42, 29], [-3, 41]], [[3168, 9999], [40, -69], [1, -62], [-35, -5], [-36, 61], [-54, 30], [21, 45]], [[6417, 5861], [-51, -4], [-98, -35], [-31, -18], [-8, -22], [44, -82], [21, -25], [1, -14], [-10, -13], [-61, -33], [-16, -16], [-7, -28], [-56, -55], [-28, -15], [-20, -20], [-18, -57], [-22, -43], [37, -35], [-7, -20], [-10, -9], [-22, -21], [-22, -21], [-11, -9], [-13, -6], [-11, -5], [-22, -11]], [[5976, 5244], [42, -58]], [[6018, 5186], [-3, -39], [-7, -104]], [[6008, 5043], [-5, -8], [20, -41], [1, -28], [69, -93], [21, -36], [11, -31], [-1, -32], [-20, -35], [-44, -30], [-8, -17], [0, -9], [0, -10]], [[6052, 4673], [-10, -12], [-27, -30], [-34, -38], [-33, -39], [-26, -29], [-11, -12]], [[5911, 4513], [-33, -51], [-6, -38], [-26, -40], [-22, -75], [-24, -14], [-18, -22], [-10, -32], [-9, -106], [-58, -59], [-46, -27], [-30, -26], [-15, -29], [-4, -30], [2, -23], [10, -24], [22, -25], [34, -23], [54, -27], [96, -33], [76, -19], [33, 0]], [[4047, 5753], [24, 58], [-8, 53], [-15, 22], [-28, 18], [-40, 18], [-88, 26], [-67, 7], [-49, 43], [-42, 54], [-17, 32], [-24, 78], [0, 19], [49, 9], [10, 17], [-8, 24], [-29, 43], [-8, 22], [6, 34], [-23, 19], [-44, 68], [1, 31], [19, 24], [-21, 13], [-25, 33], [-36, 25], [-16, 39], [-28, 30], [1, 10], [11, 8], [-4, 51], [8, 30], [24, 18], [27, 10], [63, 35], [35, 53], [13, 10], [24, 7], [45, 56], [19, 28], [20, 15], [11, 37], [15, 14], [23, 6], [57, 1], [49, 7], [38, -2], [46, -31], [39, -14], [53, -10], [38, 7], [38, -2], [36, -17], [35, -26], [29, 5], [23, 14], [35, 54], [44, 19], [126, 16], [16, 8], [4, 12], [39, 25], [40, 17], [76, 5], [43, 18], [78, -1], [34, -11], [20, -1], [26, -8], [123, -75], [81, -36], [119, -31], [63, 19], [46, 7], [86, -7], [45, 9], [129, 44], [95, 15], [14, 7], [25, 2], [138, -23], [22, 12], [31, 2], [79, 58], [41, 17], [111, 2], [83, 36], [42, 13], [44, 6], [52, -19], [50, -4], [40, -28], [48, 13], [56, 35], [43, 16], [151, 42], [93, 36], [112, 64], [64, 57]], [[4550, 1514], [-155, 12]], [[3309, 8277], [94, 65], [13, 8]], [[3475, 8362], [0, 15], [1, 19]], [[3476, 8396], [23, 78], [3, 11]], [[4123, 8643], [-3, -4], [-25, -32]], [[4095, 8607], [-3, -19], [-1, -6]], [[4091, 8582], [-77, -11], [-7, -1]], [[4137, 8405], [-35, 30], [-10, 8]], [[4092, 8443], [-1, 16], [-6, 63]], [[4085, 8522], [153, 37], [25, 6]], [[4243, 8615], [27, 17], [23, 16]], [[4445, 8572], [55, -35], [38, -24]], [[4538, 8513], [1, -8], [4, -26]], [[5097, 8488], [163, 6], [14, 0]], [[5274, 8494], [8, 0], [163, 2]], [[5930, 8141], [4, -43], [0, -11]], [[5934, 8087], [48, -2], [6, -1]], [[5988, 8084], [27, -17], [50, -34]], [[6065, 8033], [36, -23], [20, -14]], [[6121, 7996], [134, -16], [37, -5]], [[6292, 7975], [10, 13], [6, 6]], [[6423, 8002], [132, -24], [22, -4]], [[6577, 7974], [-75, -91], [12, -72], [56, -62], [-25, -46], [-13, -48], [-36, -44]], [[6496, 7611], [62, -22], [44, 29], [32, -5], [20, -29], [68, 7], [55, 40], [44, 78], [84, 96]], [[6905, 7805], [44, 5], [5, 0]], [[6954, 7810], [35, -58]], [[7069, 7568], [37, -9], [40, -9]], [[7940, 7143], [-1, -1], [-23, -58]], [[7916, 7084], [79, 16], [87, 17]], [[9251, 6792], [18, -48], [31, -86]], [[9194, 6352], [-199, -145], [-3, -1]], [[8901, 6087], [-40, -36], [-65, -56]], [[8796, 5995], [-21, -1], [-15, -1]], [[8720, 5916], [8, -156], [2, -41]], [[8730, 5719], [-13, -54], [-27, -109]], [[8675, 5487], [-8, -7], [-37, -34]], [[8605, 5305], [-129, -123], [-16, -15]], [[8436, 5059], [-57, -23], [-59, -23]], [[7907, 4910], [-63, -29], [-38, -18]], [[7806, 4863], [-89, -17], [-71, -14]], [[7646, 4832], [-70, -35], [-98, -49]], [[7357, 4644], [-13, -48], [-8, -31]], [[7301, 4350], [-25, -15], [-75, -43]], [[7201, 4292], [-67, -79], [-91, -107]], [[7043, 4106], [-82, -54], [-44, -29]], [[6917, 4023], [-36, -18], [-61, -31]], [[6820, 3974], [-55, -84], [-10, -16]], [[6755, 3874], [-60, -39], [-35, -22]], [[6437, 3689], [-99, 20], [-6, 1]], [[6332, 3710], [-56, -8], [-22, -3]], [[6254, 3699], [-88, 30], [-44, 15]], [[6122, 3744], [-52, -2], [-45, -2]], [[5929, 3744], [48, -24], [133, -66]], [[6180, 3535], [-1, -3], [-7, -48]], [[5380, 3281], [25, -51], [5, -12]], [[5410, 3218], [-16, -44], [-13, -35]], [[5321, 3049], [-117, -11], [-29, -3]], [[5175, 3035], [-43, 12], [-94, 26]], [[4983, 3046], [16, -86], [4, -19]], [[5099, 2909], [13, 6], [65, 27]], [[4974, 2790], [-15, -73], [-6, -33]], [[4953, 2684], [-4, -6], [-30, -51]], [[4919, 2627], [-74, 0], [-61, 0]], [[4784, 2627], [-56, -27], [-56, -27]], [[4672, 2573], [-31, -59], [-10, -20]], [[4908, 2396], [-26, -49], [-23, -45]], [[4859, 2302], [-19, -7], [-150, -53]], [[4454, 1919], [70, -45], [26, -16]], [[4362, 1864], [-57, -22], [-12, -4]], [[4293, 1838], [-31, -9], [-97, -29]], [[3534, 1934], [29, 44], [11, 17]], [[3545, 2346], [84, 45], [67, 36]], [[3479, 2458], [69, 47], [67, 46]], [[3801, 2935], [-16, -49], [-28, -83]], [[3757, 2803], [-16, 3], [-74, 12]], [[3667, 2818], [44, 151]], [[3711, 2969], [36, 141], [13, 54]], [[4019, 3799], [37, 93], [24, 64]], [[4080, 3956], [-15, 73], [-18, 84]], [[4156, 4458], [14, 114], [12, 90]], [[4228, 4881], [17, 93], [27, 142]], [[4272, 5116], [-9, 156], [-1, 16]], [[4262, 5288], [0, 1], [-30, 148]], [[4076, 5540], [-267, 100], [-16, 6]], [[3793, 5646], [-25, 11], [-231, 104]], [[3537, 5761], [-91, 53], [-19, 12]], [[3427, 5826], [-10, 14], [-49, 73]], [[3368, 5913], [19, 25], [4, 5]], [[3391, 5943], [-106, 121], [-15, 17]], [[3270, 6081], [-17, 25], [-124, 169]], [[2891, 6610], [-100, 62], [-11, 7]], [[2780, 6679], [-24, 10], [-78, 33]], [[2678, 6722], [28, 28], [19, 19]], [[2700, 6943], [75, 44], [39, 22]], [[2859, 7134], [-13, -12], [-42, -37]], [[2748, 7161], [-24, 95], [28, 10], [22, 6]], [[2844, 7449], [58, 16], [20, 6]], [[2922, 7471], [52, 22], [46, 20]], [[3001, 7546], [30, 4], [23, 4]], [[3048, 7606], [13, 15], [20, 23]], [[3152, 7652], [45, 48], [15, 18]], [[3212, 7718], [23, 22], [32, 33]], [[3267, 7773], [-52, 25], [26, 61]], [[3209, 7955], [10, 9], [21, 19]], [[3240, 7983], [-8, 30], [-15, 59]], [[3217, 8072], [-21, 21], [-37, 35]], [[3159, 8128], [19, 51], [46, -7], [27, 31], [-33, 62], [17, 15]], [[4499, 0], [-89, 0]], [[4038, 0], [-4038, 0], [0, 9139]], [[1744, 8776], [43, -9]], [[2008, 8514], [40, -14]], [[2454, 8240], [61, 0]], [[3667, 2818], [90, -15]], [[6954, 7810], [-49, -5]], [[4824, 8488], [-152, -10], [-129, 1]], [[2599, 8310], [-57, 5], [2, 22]], [[2371, 8462], [-36, 57]], [[1697, 9019], [-15, 4], [-42, 14]], [[1671, 9171], [15, 38], [-13, 16]], [[354, 9999], [2751, 0]], [[3168, 9999], [6831, 0], [0, -9999], [-4652, 0]], [[4170, 9460], [-111, 1]], [[4370, 5670], [-46, 21], [-36, 20], [15, 7], [1, 13], [-2, 21], [41, -6], [87, -50], [4, -3], [30, -47], [-22, -16], [-14, -3], [-13, 8], [-13, 6], [-10, 19], [-22, 10]], [[4720, 1590], [-87, 2], [-152, 1], [-1, 235]], [[6053, 4187], [-36, -84], [-38, -108], [1, -105], [-31, -23], [-11, -68]], [[4489, 1863], [-132, 17], [-345, 14], [-59, 61], [2, 79], [-95, -7], [-50, 38], [-13, 112], [110, 46], [45, 67], [-16, 53], [75, 90], [53, 139], [-16, 62], [63, 20], [-16, 40], [-66, 21], [47, 44], [-64, 40], [-34, 121], [58, 22], [-24, 128], [33, 108], [38, 94], [86, 38], [-44, 103], [0, 96], [108, 69], [-3, 88], [81, 103], [1, 97], [-37, 19], [-66, 181], [88, 109], [-14, 102], [51, 95], [94, 99], [101, 65], [-43, 42], [30, 34], [-5, 175], [156, 52], [49, 109], [-17, 27]], [[4699, 4975], [119, 95], [187, -26], [83, -76], [56, 85], [163, -5], [23, -22]], [[5330, 5026], [263, -172], [117, -16], [174, -78], [147, -41], [21, -46]], [[6052, 4673], [-141, -160]], [[5911, 4513], [144, -29], [161, -16], [113, 17], [129, 81], [23, 93]], [[6481, 4659], [71, 20], [72, -61], [-3, -84], [-120, -58], [-96, -43], [-161, -102], [-191, -144]], [[4344, 5517], [70, -71], [19, -76], [75, -45], [-45, -102], [77, -118], [56, -145], [103, 15]], [[4232, 5437], [74, 26], [38, 54]], [[6053, 4187], [93, 12], [143, -82], [53, 3], [147, -67], [112, -58], [83, -72], [-63, -50], [39, -60]], [[6481, 4659], [28, 60], [20, 63], [0, 57], [-51, 20], [-54, -18], [-53, 5], [-17, 41], [-13, 96], [-27, 32], [-97, 28], [-58, -21], [-151, 21]], [[6008, 5043], [10, 143]], [[5976, 5244], [44, 22], [-13, 60], [39, 46], [25, 83], [-34, 65], [-78, 30], [-15, 41], [21, 61], [-274, 4], [-55, 123], [42, 2], [-2, 45], [-28, 31], [-6, 61], [-83, 31], [-90, -1], [-59, 30], [-97, 21], [-56, 40], [-160, 17], [-155, 94], [12, 71], [-18, 40], [15, 79], [-187, -18], [-75, -39], [-125, -43], [-32, -32], [-73, -2], [-107, 9]], [[4352, 6215], [-80, -18], [-65, 12], [9, 160], [-117, -62], [-126, 3], [-54, 56], [-95, 6], [31, 45], [-80, 64], [-59, 95], [37, 19], [0, 44], [87, 31], [-15, 57], [37, 36], [10, 50], [164, 71], [117, 20], [19, 16], [128, -5]], [[4300, 6915], [65, 289], [3, 46], [-22, 60], [-64, 38], [1, 77], [81, 17], [28, -11], [5, 40], [-84, 11], [-2, 66], [279, -2], [47, 36], [40, -33], [28, -62], [26, 13]], [[4731, 7500], [79, -56], [111, 7], [28, 32], [106, 25], [59, 17], [16, 44], [103, 30], [-8, 22], [-121, 10], [-20, 66], [6, 70], [-64, 28], [27, 9], [105, -13], [114, -26], [41, 24], [103, 17], [160, 39], [52, 40], [-19, 30]], [[5609, 7915], [74, 5], [33, -25], [-18, -46], [49, -16], [33, -49], [-40, -37], [-23, -89], [37, -54], [10, -48], [88, -50], [70, -5], [16, 21], [45, 4], [65, 19], [46, 28], [79, -9], [35, 4]], [[6208, 7568], [78, -9], [13, 22], [-24, 20], [14, 31], [58, -9], [67, 10], [82, -22]], [[5976, 5244], [-3, 32], [-133, 54], [-133, 2], [-248, -31], [-69, -92], [-4, -57], [-56, -126]], [[4344, 5517], [90, 114], [-62, 88], [33, 36], [-25, 39], [55, 52], [3, 90], [7, 74], [31, 36], [-124, 169]], [[2814, 7009], [17, -44], [-41, -25], [4, -38], [59, 8], [57, -11], [60, -53], [81, 43], [27, 71], [88, 92], [171, 41], [156, 110], [45, 69], [-20, 80]], [[3518, 7352], [38, 10], [95, -50], [45, -50], [67, -27], [84, -110], [106, -14], [79, 28], [51, -18], [86, 9], [109, -49], [-92, -107], [43, -3], [71, -56]], [[3518, 7352], [-61, 25], [-71, 34], [-40, -16], [-121, 14], [-35, 46], [-27, -2], [-143, 60]], [[4095, 8607], [-92, -17], [-36, -53], [-55, -30], [-42, -39], [-17, -75], [-40, -62], [74, -7], [18, -48], [32, -24], [11, -42], [-17, -39], [5, -22], [35, -9], [34, -36], [184, 10], [83, -14], [100, -90], [58, 11], [103, -6], [82, 12], [50, -18], [-26, -56], [-32, -36], [-11, -75], [29, -70], [41, -31], [5, -24], [-73, -52], [52, -24], [38, -36], [43, -105]], [[5748, 8248], [-113, -61], [-12, -39], [48, -39], [-35, -20], [-88, -17], [3, -48], [-39, -29], [97, -80]], [[6121, 7996], [-23, -94], [-86, -28], [7, -25], [-26, -54], [63, -76], [46, 0], [19, -60], [87, -91]]], "transform": {"scale": [0.007000700070007, 0.009500950095009501], "translate": [-100, -70]}, "bbox": [-100, -70, -30, 25]}