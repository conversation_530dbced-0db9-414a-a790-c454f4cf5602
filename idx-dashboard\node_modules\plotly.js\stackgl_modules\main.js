module.exports = {
    alpha_shape: require('alpha-shape'),
    convex_hull: require('convex-hull'),
    delaunay_triangulate: require('delaunay-triangulate'),
    gl_cone3d: require('gl-cone3d'),
    gl_error3d: require('gl-error3d'),
    gl_line3d: require('gl-line3d'),
    gl_mesh3d: require('gl-mesh3d'),
    gl_plot3d: require('gl-plot3d'),
    gl_scatter3d: require('gl-scatter3d'),
    gl_streamtube3d: require('gl-streamtube3d'),
    gl_surface3d: require('gl-surface3d'),
    ndarray: require('ndarray'),
    ndarray_linear_interpolate: require('ndarray-linear-interpolate'),
};
