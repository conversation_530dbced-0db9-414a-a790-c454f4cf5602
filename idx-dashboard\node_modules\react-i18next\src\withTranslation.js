import { createElement, forwardRef as forwardRefReact } from 'react';
import { useTranslation } from './useTranslation.js';
import { getDisplayName } from './utils.js';

export const withTranslation = (ns, options = {}) =>
  function Extend(WrappedComponent) {
    function I18nextWithTranslation({ forwardedRef, ...rest }) {
      const [t, i18n, ready] = useTranslation(ns, { ...rest, keyPrefix: options.keyPrefix });

      const passDownProps = {
        ...rest,
        t,
        i18n,
        tReady: ready,
      };
      if (options.withRef && forwardedRef) {
        passDownProps.ref = forwardedRef;
      } else if (!options.withRef && forwardedRef) {
        passDownProps.forwardedRef = forwardedRef;
      }
      return createElement(WrappedComponent, passDownProps);
    }

    I18nextWithTranslation.displayName = `withI18nextTranslation(${getDisplayName(
      WrappedComponent,
    )})`;

    I18nextWithTranslation.WrappedComponent = WrappedComponent;

    const forwardRef = (props, ref) =>
      // eslint-disable-next-line prefer-object-spread
      createElement(I18nextWithTranslation, Object.assign({}, props, { forwardedRef: ref }));

    return options.withRef ? forwardRefReact(forwardRef) : I18nextWithTranslation;
  };
