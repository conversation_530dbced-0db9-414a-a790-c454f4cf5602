import React from 'react';
import { useTranslation } from 'react-i18next';

const LanguageSwitcher: React.FC = () => {
  const { i18n } = useTranslation();

  const languages = [
    { code: 'en', name: 'EN', fullName: 'English' },
    { code: 'hi', name: 'हिं', fullName: 'हिंदी' }
  ];

  const changeLanguage = (languageCode: string) => {
    i18n.changeLanguage(languageCode);
  };

  return (
    <div className="flex gap-2">
      {languages.map((language) => (
        <button
          key={language.code}
          onClick={() => changeLanguage(language.code)}
          className={`px-3 py-1 rounded text-sm transition-colors ${
            i18n.language === language.code
              ? 'bg-accent text-accent-foreground font-medium'
              : 'bg-accent/20 text-accent hover:bg-accent/40'
          }`}
          title={language.fullName}
        >
          {language.name}
        </button>
      ))}
    </div>
  );
};

export default LanguageSwitcher;
