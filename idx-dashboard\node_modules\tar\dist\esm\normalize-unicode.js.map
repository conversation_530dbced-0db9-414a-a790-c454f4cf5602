{"version": 3, "file": "normalize-unicode.js", "sourceRoot": "", "sources": ["../../src/normalize-unicode.ts"], "names": [], "mappings": "AAAA,oCAAoC;AACpC,+CAA+C;AAC/C,6CAA6C;AAC7C,4CAA4C;AAC5C,MAAM,cAAc,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA;AAC1C,MAAM,EAAE,cAAc,EAAE,GAAG,MAAM,CAAC,SAAS,CAAA;AAC3C,MAAM,CAAC,MAAM,gBAAgB,GAAG,CAAC,CAAS,EAAE,EAAE;IAC5C,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC,EAAE,CAAC;QAC5C,cAAc,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,SAAS,CAAC,KAAK,CAAC,CAAA;IACxC,CAAC;IACD,OAAO,cAAc,CAAC,CAAC,CAAC,CAAA;AAC1B,CAAC,CAAA", "sourcesContent": ["// warning: extremely hot code path.\n// This has been meticulously optimized for use\n// within npm install on large package trees.\n// Do not edit without careful benchmarking.\nconst normalizeCache = Object.create(null)\nconst { hasOwnProperty } = Object.prototype\nexport const normalizeUnicode = (s: string) => {\n  if (!hasOwnProperty.call(normalizeCache, s)) {\n    normalizeCache[s] = s.normalize('NFD')\n  }\n  return normalizeCache[s]\n}\n"]}