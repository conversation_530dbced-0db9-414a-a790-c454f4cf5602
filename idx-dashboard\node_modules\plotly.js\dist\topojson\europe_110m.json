{"type": "Topology", "objects": {"coastlines": {"type": "GeometryCollection", "geometries": [{"type": "LineString", "arcs": [0, 1]}, {"type": "LineString", "arcs": [2, 3, 4, 5, 6]}, {"type": "LineString", "arcs": [7]}, {"type": "LineString", "arcs": [8]}, {"type": "LineString", "arcs": [9]}, {"type": "LineString", "arcs": [10]}, {"type": "LineString", "arcs": [11, 12, 13, 14, 15, 16, 17]}, {"type": "LineString", "arcs": [18]}, {"type": "LineString", "arcs": [19]}, {"type": "MultiLineString", "arcs": [[20], [21]]}, {"type": "LineString", "arcs": [22]}, {"type": "LineString", "arcs": [23]}, {"type": "LineString", "arcs": [24]}, {"type": "LineString", "arcs": [25]}, {"type": "LineString", "arcs": [26]}, {"type": "LineString", "arcs": [27, 28, 29, -27, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 123, 124, 125, 126, 127, 128, 129, 130, 131, 132, 133, 134, 135, 136, 137, 138, 139, 140, 141, 142, 143, 144, 145, 146, 147, 148, 149, 150, 151, 152, 153, 154, 155, 156, 157, 158, 159, 160, 161, 162, 163, 164, 165, 166, 167, 168, 169, 170, 171, 172, 173, 174, 175, 176, 177, 178, 179, 180, 181, 182, 183, 184, 185, 186, 187, 188, 189, 190, 191, 192, 193, 194, 195, 196, 197, 198, 199, 200, 201, 202, 203, 204, 205, 206, 207, 208, 209, 210, 211, 212, 213, 214, 215, 216, 217, 218, 219, 220, 221, 222, 223, 224, 225, 226, 227]}, {"type": "MultiLineString", "arcs": [[228, 229, 230, 231, 232, 233, 234, 235, 236, 237, 238, 239, 240], [241]]}, {"type": "LineString", "arcs": [242, 243, 244]}, {"type": "MultiLineString", "arcs": [[245], [246]]}]}, "land": {"type": "GeometryCollection", "geometries": [{"type": "Polygon", "arcs": [[7]]}, {"type": "Polygon", "arcs": [[25]]}, {"type": "Polygon", "arcs": [[8]]}, {"type": "Polygon", "arcs": [[9]]}, {"type": "Polygon", "arcs": [[1, 0]]}, {"type": "Polygon", "arcs": [[10]]}, {"type": "Polygon", "arcs": [[17, 11, 12, 13, 14, 15, 255]]}, {"type": "Polygon", "arcs": [[18]]}, {"type": "Polygon", "arcs": [[21, 20, 256]]}, {"type": "Polygon", "arcs": [[-27, 30, 257, 32, 258, 34, 35, 259, 260, 261, 262, 39, 263, 41, 264, 43, 44, 45, 46, 47, 265, 266, 267, 50, 268, 269, 53, 270, 271, 272, 57, 273, 59, 60, 61, 274, 275, 64, 276, 277, 278, 68, 279, 70, 280, 72, 281, 74, 282, 76, 283, 284, 79, 285, 286, 287, 83, 288, 289, 86, 290, 291, 292, 90, 293, 92, 294, 94, 295, 296, 97, 297, 298, 299, 101, 102, 103, 300, 105, 301, 302, 303, 109, 304, 305, 112, 306, 114, 307, 116, 308, 309, 119, 310, 121, 311, 123, 124, 312, 313, 127, 314, 315, 130, 316, 317, 133, 318, 135, 319, 137, 138, 320, 140, 141, 321, 322, 144, 145, 323, 324, 325, 326, 150, 327, 328, 329, 330, 155, 331, 332, 158, 333, 334, 335, 162, 163, 164, 165, 166, 336, 168, 337, 170, 338, 172, 339, 174, 175, 176, 340, 178, 341, 180, 342, 343, 183, 344, 185, 345, 346, 188, 347, 190, 348, 349, 350, 351, 352, 196, 353, 198, 354, 200, 201, 355, 203, 204, 205, 356, 357, 358, 359, 360, 361, 211, 212, 213, 362, 363, 364, 216, 365, 218, 366, 367, 368, 222, 369, 370, 371, 225, 226, 372, 373, 374, -243, -245, 375]]}, {"type": "Polygon", "arcs": [[23]]}, {"type": "Polygon", "arcs": [[24]]}, {"type": "Polygon", "arcs": [[22]]}, {"type": "Polygon", "arcs": [[19]]}]}, "ocean": {"type": "GeometryCollection", "geometries": [{"type": "Polygon", "arcs": [[244, 242, 243]]}, {"type": "MultiPolygon", "arcs": [[[-24], [-25], [-23], [-20], [-19], [-18, -17, -16, -15, -14, -13, -12], [-2, -1], [-26], [-8], [-7, 376, -5, 377, -3], [-9], [-10], [-11], [-21, -22, 378, -374, 379, -226, -372, 380, -224, -223, -369, -221, -220, -219, -218, -217, 381, 382, -215, -214, 383, -212, -362, 384, -209, -208, -207, -206, 385, -204, -203, -202, -201, -200, -199, -198, -197, -196, -195, -194, -193, -192, -191, -190, -189, -188, -187, -186, -185, -184, -183, -182, -181, -180, -179, -178, -177, -176, -175, -174, -173, -172, -171, -170, -169, -168, -167, -166, -165, -164, -163, -162, -161, -160, -159, -158, -157, -156, -155, -154, -153, -152, -151, -150, -149, -148, -147, -146, -145, -323, -143, -142, -141, -140, 386, -138, -137, -136, -135, -134, -133, -132, -131, -130, -129, -128, -127, -126, -125, -124, -123, -122, -121, -120, -119, -118, -117, -116, -115, -114, -113, -112, -111, -110, -109, -108, -107, -106, -105, -104, -103, -102, -101, -100, -99, -98, -97, -96, -95, -94, -93, -92, -91, -90, -89, -88, -87, -86, -85, -84, -83, -82, -81, -80, -79, -78, -77, -76, -75, -74, -73, -72, -71, -70, -69, -68, -67, -66, -65, -64, -63, -62, -61, -60, -59, -58, -57, -56, -55, -54, -53, -52, -51, -50, -49, -48, -47, -46, -45, -44, -43, -42, -41, -40, -39, -38, -37, -36, -35, -34, -33, -258, -31, 26, -30, 387, -28, -242, 388, -247, 389, -246, 390]], [[-241, 391, -239, 392, -237, 393, -235, 394, -233, 395, -231, 396, -229, 397]]]}]}, "lakes": {"type": "GeometryCollection", "geometries": [{"type": "Polygon", "arcs": [[398]]}, {"type": "Polygon", "arcs": [[399]]}, {"type": "Polygon", "arcs": [[400]]}]}, "rivers": {"type": "GeometryCollection", "geometries": [{"type": "LineString", "arcs": [247, 248, 249, 250, 251, 252, 253, 254]}]}, "countries": {"type": "GeometryCollection", "geometries": [{"type": "MultiPolygon", "properties": {"ct": [44.69, 58.03]}, "id": "RUS", "arcs": [[[-243, -245, 375, -27, 30, 31, 32, 33, 34, 401, 402, 403, 404, 175, 405, 406, 201, 202, 203, 204, 205, 206, 207, 208, 209, 210, 211, 212, 213, 214, -383, -382, 216, 217, 218, 219, 220, 368, 222, 223, -381, 371, 225, 226, 227, 374]], [[19]], [[407, 164, 408]], [[20, 256, 21]], [[409, 262, 39, 40, 41, 42, 43, 410]]]}, {"type": "MultiPolygon", "properties": {"ct": [14.24, 64.54]}, "id": "NOR", "arcs": [[[24]], [[-407, 411, 412, 192, 193, 194, 195, 196, 197, 198, 199, 200]], [[22]], [[23]]]}, {"type": "MultiPolygon", "properties": {"ct": [2.34, 46.61]}, "id": "FRA", "arcs": [[[413, 414, 415, 102, 416, 130, 131, 132, 133, 134, 135, 136, 137, 138, 139, 140, 417, 418]], [[9]]]}, {"type": "Polygon", "properties": {"ct": [16.6, 62.81]}, "id": "SWE", "arcs": [[-413, 419, 184, 185, 186, 187, 188, 189, 190, 191]]}, {"type": "Polygon", "properties": {"ct": [27.98, 53.51]}, "id": "BLR", "arcs": [[-403, 420, 421, 422, 423]]}, {"type": "Polygon", "properties": {"ct": [31.23, 49.15]}, "id": "UKR", "arcs": [[-402, 35, 36, 37, 424, -410, 425, 45, -255, 426, 427, 428, 429, 430, 431, -421]]}, {"type": "Polygon", "properties": {"ct": [19.31, 52.15]}, "id": "POL", "arcs": [[-422, -432, 432, 433, 434, 163, -408, 435]]}, {"type": "Polygon", "properties": {"ct": [14.08, 47.61]}, "id": "AUT", "arcs": [[436, 437, 438, 439, 440, 441, 442]]}, {"type": "Polygon", "properties": {"ct": [19.36, 47.2]}, "id": "HUN", "arcs": [[-430, 443, 444, 445, 446, -437, 447, 248, 448]]}, {"type": "Polygon", "properties": {"ct": [28.41, 47.2]}, "id": "MDA", "arcs": [[-428, 449]]}, {"type": "Polygon", "properties": {"ct": [24.94, 45.86]}, "id": "ROU", "arcs": [[-427, 254, 46, 450, -253, -252, 451, -444, -429, -450]]}, {"type": "Polygon", "properties": {"ct": [23.88, 55.28]}, "id": "LTU", "arcs": [[-423, -436, -409, 165, 452]]}, {"type": "Polygon", "properties": {"ct": [24.83, 56.81]}, "id": "LVA", "arcs": [[-404, -424, -453, 166, 167, 168, 169, 453]]}, {"type": "Polygon", "properties": {"ct": [25.82, 58.64]}, "id": "EST", "arcs": [[-405, -454, 170, 171, 172, 173, 174]]}, {"type": "Polygon", "properties": {"ct": [10.29, 51.13]}, "id": "DEU", "arcs": [[-435, 454, -441, 455, -414, 456, 457, 458, 145, 146, 147, 459, 157, 158, 159, 160, 161, 162]]}, {"type": "Polygon", "properties": {"ct": [25.2, 42.75]}, "id": "BGR", "arcs": [[252, -451, 47, 48, 266, 460, 461, 462]]}, {"type": "MultiPolygon", "properties": {"ct": [22.56, 39.34]}, "id": "GRC", "arcs": [[[7]], [[-461, 267, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 463, 464]]]}, {"type": "Polygon", "properties": {"ct": [20.03, 41.14]}, "id": "ALB", "arcs": [[-464, 60, 465, 466, 467]]}, {"type": "Polygon", "properties": {"ct": [16.57, 45.02]}, "id": "HRV", "arcs": [[-446, 468, 469, 470, 471, 64, 65, 66, 67, 68, 69, 70, 472]]}, {"type": "Polygon", "properties": {"ct": [8.12, 46.79]}, "id": "CHE", "arcs": [[-440, 473, -415, -456]]}, {"type": "Polygon", "properties": {"ct": [5.97, 49.77]}, "id": "LUX", "arcs": [[-457, -419, 474]]}, {"type": "Polygon", "properties": {"ct": [4.58, 50.65]}, "id": "BEL", "arcs": [[-458, -475, -418, 141, 475]]}, {"type": "Polygon", "properties": {"ct": [5.51, 52.3]}, "id": "NLD", "arcs": [[-459, -476, 142, 143, 144]]}, {"type": "Polygon", "properties": {"ct": [-8.06, 39.63]}, "id": "PRT", "arcs": [[476, 116, 117, 118, 119, 120, 121, 122, 123]]}, {"type": "Polygon", "properties": {"ct": [-3.62, 40.35]}, "id": "ESP", "arcs": [[-477, 124, 125, 126, 127, 128, 129, -417, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115]]}, {"type": "Polygon", "properties": {"ct": [-8.01, 53.18]}, "id": "IRL", "arcs": [[0, 477]]}, {"type": "MultiPolygon", "properties": {"ct": [12.22, 43.47]}, "id": "ITA", "arcs": [[[-439, 478, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, -416, -474]], [[25]], [[8]]]}, {"type": "MultiPolygon", "properties": {"ct": [9.31, 56.22]}, "id": "DNK", "arcs": [[[-460, 148, 149, 150, 151, 152, 153, 154, 155, 156]], [[10]]]}, {"type": "MultiPolygon", "properties": {"ct": [-2.66, 53.88]}, "id": "GBR", "arcs": [[[-478, 1]], [[255, 17, 11, 479, 13, 480, 15]]]}, {"type": "Polygon", "properties": {"ct": [-18.76, 65.07]}, "id": "ISL", "arcs": [[18]]}, {"type": "Polygon", "properties": {"ct": [14.94, 46.13]}, "id": "SVN", "arcs": [[-438, -447, -473, 71, -479]]}, {"type": "Polygon", "properties": {"ct": [26.21, 64.5]}, "id": "FIN", "arcs": [[-406, 176, 177, 178, 179, 180, 181, 182, 183, -420, -412]]}, {"type": "Polygon", "properties": {"ct": [19.51, 48.73]}, "id": "SVK", "arcs": [[-431, -449, -249, -448, -443, 481, -433]]}, {"type": "Polygon", "properties": {"ct": [15.33, 49.78]}, "id": "CZE", "arcs": [[-434, -482, -442, -455]]}, {"type": "Polygon", "properties": {"ct": [17.82, 44.18]}, "id": "BIH", "arcs": [[-471, 482, 483]]}, {"type": "Polygon", "properties": {"ct": [21.7, 41.61]}, "id": "MKD", "arcs": [[-462, -465, -468, 484, 485]]}, {"type": "Polygon", "properties": {"ct": [20.82, 44.23]}, "id": "SRB", "arcs": [[-445, -452, 251, -463, -486, 486, 487, -483, -470, -469]]}, {"type": "Polygon", "properties": {"ct": [19.29, 42.79]}, "id": "MNE", "arcs": [[-466, 61, 62, 63, -472, -484, -488, 488]]}, {"type": "Polygon", "arcs": [[-467, -489, -487, -485]]}]}, "subunits": {"type": "GeometryCollection", "geometries": []}}, "arcs": [[[2644, 5985], [19, -80], [-84, -99], [-197, -66], [-157, 17], [90, 116], [-58, 113], [151, 87], [84, 52]], [[2492, 6125], [93, 5], [119, -69], [-60, -76]], [[6993, 3931], [80, -1], [101, 33], [-75, -47]], [[7099, 3916], [12, -30]], [[7111, 3886], [-114, -45], [-54, 14], [-26, 45]], [[6917, 3900], [60, 5]], [[6977, 3905], [16, 26]], [[6254, 3922], [-14, -33], [-160, -9], [1, 18], [-135, 22], [20, 47], [61, -38], [86, 7], [83, -8], [-3, -20], [61, 14]], [[4301, 4544], [55, 34], [67, -78], [-16, -147], [-50, 7], [-46, -37], [-42, 29], [-4, 134], [-25, 64], [61, -6]], [[4305, 4736], [71, 42], [19, -95], [-37, -86], [-50, 23], [-26, 75], [23, 41]], [[4707, 6234], [36, -56], [-67, -90], [-116, 63], [-16, 46], [163, 37]], [[2865, 6505], [134, 9], [-119, -120], [114, 15], [121, 0], [-29, -91], [-100, -99], [115, -7]], [[3101, 6212], [108, -143]], [[3209, 6069], [76, -18], [69, -127], [31, -43], [135, -22], [-14, -71], [-56, -32], [44, -58], [-100, -58], [-148, 1], [-190, -30], [-51, 21], [-74, -52], [-103, 13], [-78, -42], [-59, 22], [163, 116], [100, 24]], [[2954, 5713], [-175, 19]], [[2779, 5732], [-31, 44], [116, 35], [-61, 60], [21, 72], [165, -10], [17, 65]], [[3006, 5998], [-76, 70]], [[2930, 6068], [-135, 19], [-27, 30], [41, 50], [-37, 31], [-60, -53], [-6, 107], [-56, 57], [40, 115], [86, 90], [89, -9]], [[1721, 7383], [-26, -72], [126, -75], [-144, -85], [-321, -76], [-96, -21], [-146, 17], [-310, 35], [109, 49], [-241, 54], [196, 22], [-4, 33], [-234, 25], [75, 73], [169, 16], [173, -75], [169, 60], [140, -31], [181, 59], [184, -8]], [[8315, 8954], [217, 20], [169, 1], [23, -30], [64, 27], [104, 18], [165, -24], [-43, -17], [-149, -15], [-100, -8], [-15, -19], [-130, -18], [-121, 26], [64, 35], [-248, 4]], [[9278, 8194], [266, 97], [-30, 51], [248, 58], [237, 46]], [[9999, 8308], [-169, -52], [-166, -109], [-174, -107], [23, -92], [212, -91], [-65, -10], [-363, 15], [-30, 49], [-201, 30], [-16, 60], [113, 24], [-3, 60], [220, 95], [-102, 14]], [[6378, 8894], [-165, -60], [-322, -13], [-328, 19], [-19, 30], [-160, 2], [-121, 51], [343, 31], [161, -26], [112, 33], [281, -28], [218, -39]], [[6080, 8650], [-248, -46], [-196, 26], [76, 29], [-67, 35], [230, 22], [45, -41], [160, -25]], [[5015, 8852], [43, 38], [163, 4], [140, -39], [366, -83], [-280, -44], [-62, -81], [-97, -21], [-53, -92], [-134, -5], [-239, 68], [101, 39], [-167, 33], [-216, 93], [-87, 87], [303, 40], [61, -39], [158, 2]], [[4973, 4238], [84, 9], [-40, -87], [17, -34], [-23, -58], [-85, 42], [-57, 12], [-155, 57], [16, 57], [130, -10], [113, 12]], [[7504, 4961], [126, -41], [142, -94]], [[1527, 2122], [-14, 55], [12, 55], [-29, 53], [-59, 48], [5, 47]], [[1442, 2380], [5, 52], [43, 30]], [[1490, 2462], [36, 58], [-7, 37], [38, 79], [62, 70], [38, 18], [29, 65], [3, 59], [40, 69], [74, 40], [70, 114], [58, 44], [103, 12], [88, 76], [56, 30], [92, 93], [-27, 138], [42, 95], [15, 59], [71, 75], [112, 51], [82, 46], [74, 115], [35, 68], [82, -1], [67, -47], [106, 8], [115, -25], [48, -1], [107, 61], [120, 19], [70, 46], [107, 34], [188, 20], [184, 9], [56, -17], [105, 44], [118, 1], [46, -26], [76, 7], [121, 45], [77, -14], [-3, -56], [94, 41], [8, -21], [-55, -55], [-1, -51], [38, -28], [-14, -96], [-73, -56], [21, -60], [57, -2], [28, -53], [42, -17], [131, -39], [47, 10], [92, -19], [148, -49], [52, -99], [100, -22], [156, -46], [119, -55], [54, 28], [53, 52], [-26, 85], [35, 54], [80, 52], [76, 15], [151, -23], [38, -50], [41, 0], [35, -19], [111, -13], [27, -37], [148, 2], [107, -29], [110, -33], [51, -17], [86, 35], [46, 32], [98, 9], [79, -14], [30, -55], [26, 36], [89, -26], [86, -7], [55, 28], [32, 37], [-7, 6], [29, 52], [23, 84], [15, 28], [4, 1], [39, 91], [55, 78], [2, 4], [-10, 85], [27, 46], [-41, 50], [42, 42], [-67, -10], [-93, 26], [-77, -64], [-168, -12], [-90, 59], [-120, 4], [-26, -46], [-76, -13], [-108, 59], [-121, -2], [-66, 110], [-81, 62], [54, 86], [-70, 53], [123, 107], [171, 4], [47, 85], [211, -15], [134, 72], [129, 31], [184, 3], [194, -79], [159, -43], [130, 18], [95, -10], [132, 58], [16, 47], [-28, 76], [-64, 41], [-61, 13], [-41, 34]], [[7504, 4961], [-96, 66]], [[7408, 5027], [80, 17], [93, 93]], [[7581, 5137], [-62, 44]], [[7519, 5181], [163, 46]], [[7682, 5227], [-3, 24], [-99, -18]], [[7580, 5233], [-89, -9], [-74, -36]], [[7417, 5188], [-104, -6]], [[7313, 5182], [-96, -41]], [[7217, 5141], [7, -69]], [[7224, 5072], [54, -27], [113, 7]], [[7391, 5052], [-21, -40]], [[7370, 5012], [-122, -19], [-151, -64], [-61, 22]], [[7036, 4951], [24, 52]], [[7060, 5003], [-121, 33], [19, 21], [107, 37]], [[7065, 5094], [-33, 26]], [[7032, 5120], [-172, 28], [-8, 41], [-103, -14], [-41, -61], [-86, -82]], [[6622, 5032], [3, -29], [-54, -23], [-34, 10], [-31, -134]], [[6506, 4856], [-58, -46], [-40, -80]], [[6408, 4730], [35, -63]], [[6443, 4667], [14, -43], [97, -36], [-21, -27], [-132, -6], [-47, -34], [-93, -60], [-35, 52], [2, 23]], [[6228, 4536], [-68, 3]], [[6160, 4539], [-58, 10]], [[6102, 4549], [-134, -29]], [[5968, 4520], [77, -62], [-57, -18], [-62, 0], [-58, 57], [-21, -24], [25, -67]], [[5872, 4406], [55, -52]], [[5927, 4354], [-42, -24]], [[5885, 4330], [62, -52]], [[5947, 4278], [55, -32], [2, -63], [-103, 30], [33, -57], [-71, -11], [42, -98]], [[5905, 4047], [-73, -2]], [[5832, 4045], [-91, 48], [-42, 89], [-20, 74], [-43, 51], [-57, 64], [-7, 31]], [[5572, 4402], [-19, 8], [-2, 25], [-62, 37], [-10, 53], [10, 76], [15, 34], [-19, 18]], [[5485, 4653], [-23, 8]], [[5462, 4661], [-31, 36]], [[5431, 4697], [-48, 23]], [[5383, 4720], [-105, 41], [-64, 40], [-102, 33], [-93, 81]], [[5019, 4915], [22, 9]], [[5041, 4924], [-50, 46]], [[4991, 4970], [-2, 38]], [[4989, 5008], [-72, 17]], [[4917, 5025], [-34, -47]], [[4883, 4978], [-33, 37], [3, 38], [4, 2]], [[4857, 5055], [24, 10]], [[4881, 5065], [-88, 16]], [[4793, 5081], [-90, -39]], [[4703, 5042], [6, -55]], [[4709, 4987], [-14, -32]], [[4695, 4955], [37, -56]], [[4732, 4899], [104, -56]], [[4836, 4843], [56, -92]], [[4892, 4751], [123, -90], [87, 1], [27, -25]], [[5129, 4637], [-31, -22]], [[5098, 4615], [100, -40]], [[5198, 4575], [81, -34]], [[5279, 4541], [96, -57], [11, -21], [-21, -40], [-61, 52], [-97, 18]], [[5207, 4493], [-47, -72]], [[5160, 4421], [81, -41]], [[5241, 4380], [-13, -58], [-47, -6]], [[5181, 4316], [-59, -96]], [[5122, 4220], [-46, -8]], [[5076, 4212], [0, 34]], [[5076, 4246], [23, 59], [24, 24]], [[5123, 4329], [-44, 64]], [[5079, 4393], [-34, 56]], [[5045, 4449], [-46, 14]], [[4999, 4463], [-32, 48], [-72, 20], [-48, 45], [-82, 7]], [[4765, 4583], [-87, 50]], [[4678, 4633], [-102, 73]], [[4576, 4706], [-75, 64], [-35, 110], [-55, 12]], [[4411, 4892], [-90, 37]], [[4321, 4929], [-52, -15]], [[4269, 4914], [-64, -51]], [[4205, 4863], [-46, -9]], [[4159, 4854], [-101, -62], [-219, 30], [-162, -36], [-12, -67]], [[3665, 4719], [6, -65], [-106, -74], [-142, -23], [-10, -38]], [[3413, 4519], [-68, -61]], [[3345, 4458], [-43, -91], [43, -63]], [[3345, 4304], [-64, -50]], [[3281, 4254], [-24, -72]], [[3257, 4182], [-84, -22]], [[3173, 4160], [-78, -86], [-141, -1]], [[2954, 4073], [-106, 2]], [[2848, 4075], [-70, -39]], [[2778, 4036], [-42, -42], [-55, 9]], [[2681, 4003], [-41, 37]], [[2640, 4040], [-31, 64]], [[2609, 4104], [-104, 18]], [[2505, 4122], [-45, -29], [-58, 15], [-58, -12]], [[2344, 4096], [17, 87]], [[2361, 4183], [-10, 68]], [[2351, 4251], [-50, 11], [-26, 42], [8, 72]], [[2283, 4376], [45, 41]], [[2328, 4417], [8, 45]], [[2336, 4462], [23, 67]], [[2359, 4529], [-3, 47], [-22, 39], [-5, 38]], [[2329, 4653], [6, 79], [-46, 48], [158, 80]], [[2447, 4860], [136, -20]], [[2583, 4840], [149, 1]], [[2732, 4841], [118, -19]], [[2850, 4822], [92, 6]], [[2942, 4828], [180, -4]], [[3122, 4824], [57, 67]], [[3179, 4891], [21, 221]], [[3200, 5112], [-114, 117]], [[3086, 5229], [-82, 56], [-170, 43]], [[2834, 5328], [-11, 81]], [[2823, 5409], [144, 24]], [[2967, 5433], [186, -29]], [[3153, 5404], [-35, 126]], [[3118, 5530], [105, -48], [259, 87]], [[3482, 5569], [33, 91]], [[3515, 5660], [97, 23]], [[3612, 5683], [89, 22]], [[3701, 5705], [58, 30]], [[3759, 5735], [97, 163], [152, 47]], [[4008, 5945], [92, -3]], [[4100, 5942], [22, 23], [93, 6], [20, -24], [76, 55]], [[4311, 6002], [-26, 41]], [[4285, 6043], [-5, 63]], [[4280, 6106], [-45, 62]], [[4235, 6168], [-3, 114]], [[4232, 6282], [18, 30], [32, 33], [98, 7]], [[4380, 6352], [39, 30]], [[4419, 6382], [89, 32]], [[4508, 6414], [-3, -57]], [[4505, 6357], [-33, -37]], [[4472, 6320], [13, -31], [60, -16], [-27, -42], [-33, 12], [-80, -80]], [[4405, 6163], [30, -54]], [[4435, 6109], [2, -43]], [[4437, 6066], [113, -26]], [[4550, 6040], [-2, -40]], [[4548, 6000], [113, 21]], [[4661, 6021], [63, 31]], [[4724, 6052], [125, -44], [53, -36]], [[4902, 5972], [76, 33], [173, 51], [140, 38], [111, -19], [8, -27], [107, -1]], [[5517, 6047], [26, 49], [153, 36]], [[5696, 6132], [-24, 93]], [[5672, 6225], [4, 84]], [[5676, 6309], [55, 69]], [[5731, 6378], [104, 38], [89, -83], [89, 3]], [[6013, 6336], [21, 85]], [[6034, 6421], [13, 65], [-41, -14]], [[6006, 6472], [-70, 40]], [[5936, 6512], [-10, 64], [141, 31]], [[6067, 6607], [140, 16]], [[6207, 6623], [120, -19], [115, 4]], [[6442, 6608], [126, 61], [-116, 53]], [[6452, 6722], [-202, -9], [-195, -41]], [[6055, 6672], [-181, -23]], [[5874, 6649], [-64, 61], [-108, 36], [25, 109], [-54, 101]], [[5673, 6956], [53, 64]], [[5726, 7020], [100, 70]], [[5826, 7090], [255, 121]], [[6081, 7211], [74, 23]], [[6155, 7234], [-12, 47], [-154, 52]], [[5989, 7333], [-191, -31]], [[5798, 7302], [-108, -78], [17, -68], [-177, -89], [-214, -96], [-81, -156], [79, -78]], [[5314, 6737], [106, -62]], [[5420, 6675], [-102, -125]], [[5318, 6550], [-115, -26]], [[5203, 6524], [-43, -187]], [[5160, 6337], [-63, -104], [-135, 11], [-62, -88], [-129, -5], [-35, 105], [-93, 126]], [[4643, 6382], [-85, 157]], [[4558, 6539], [-74, 68]], [[4484, 6607], [-220, -128]], [[4264, 6479], [-148, -26]], [[4116, 6453], [-154, 56]], [[3962, 6509], [-39, 120], [-35, 256], [102, 71], [293, 94], [220, 114], [203, 155]], [[4706, 7319], [267, 215]], [[4973, 7534], [186, 83], [305, 140], [244, 48], [183, -6]], [[5891, 7799], [169, 92]], [[6060, 7891], [203, -4], [199, 22], [348, -82], [-143, -29], [121, -70]], [[6788, 7728], [115, 39]], [[6903, 7767], [182, -68]], [[7085, 7699], [305, -26], [419, -126]], [[7809, 7547], [86, -53], [7, -73]], [[7902, 7421], [-123, -59]], [[7779, 7362], [-182, -29]], [[7597, 7333], [-496, 84]], [[7101, 7417], [-81, -14]], [[7020, 7403], [181, -81]], [[7201, 7322], [7, -52], [7, -114]], [[7215, 7156], [143, -33], [87, -29], [14, 54]], [[7459, 7148], [-66, 47], [70, 42]], [[7463, 7237], [269, -69], [93, 27], [-74, 82], [259, 109], [102, -7], [104, -39], [65, 77]], [[8281, 7417], [-93, 66]], [[8188, 7483], [53, 68], [-80, 67]], [[8161, 7618], [310, -35]], [[8471, 7583], [64, -63]], [[8535, 7520], [-141, -13], [1, -62], [87, -38]], [[8482, 7407], [172, 24]], [[8654, 7431], [27, 71]], [[8681, 7502], [620, 148]], [[9301, 7650], [84, -5]], [[9385, 7645], [-110, -68]], [[9275, 7577], [141, -13], [77, 40]], [[9493, 7604], [208, 3], [165, 46]], [[9866, 7653], [126, -67]], [[9992, 7586], [7, 4]], [[9999, 2813], [-43, 7], [-121, 25], [-125, 15], [-48, 136], [-53, 20], [-85, -20], [-112, -54], [-136, 37], [-112, 85], [-107, 32], [-74, 105], [-82, 148], [-60, -18], [-71, 37], [-41, -43], [-66, 5], [23, -49], [-10, -25], [36, -84], [44, -96], [54, -25], [19, -39], [76, -47], [7, -46], [-11, -37], [14, -37], [32, -31], [14, -37], [17, -27], [-7, 81], [30, 58], [30, 12], [34, -34], [2, -66], [-25, -65], [21, -42], [20, 5], [4, -30], [87, 17], [92, -3], [67, -3], [76, 75], [83, 71], [70, 69], [33, 38], [14, -10], [-11, -46], [-14, -20], [15, -88]], [[9599, 2769], [49, -76], [63, -40]], [[9711, 2653], [81, -15], [66, -20], [50, -64], [30, -36], [40, -15], [-1, -24], [-40, -67], [-18, -31], [-47, -35], [-41, -76], [-50, 5], [-23, -26], [-18, -56], [13, -75], [-10, -13], [-51, 0], [-70, -41], [-10, -55], [-26, -23], [-69, 1]], [[9517, 1987], [-44, -28], [1, -45]], [[9474, 1914], [-54, -31], [-61, 11], [-74, -38], [-52, -6], [-80, -30]], [[9153, 1820], [-21, -49], [-3, -38]], [[9129, 1733], [-111, -47], [-177, -52], [-100, -78], [-49, -6], [-33, 6], [-65, -46], [-71, -21], [-93, -6], [-28, -6], [-24, -30], [-29, -8], [-18, -28], [-55, 2], [-35, -15], [-77, 6], [-29, 65], [3, 61], [-18, 32], [-22, 83], [-32, 45], [23, 6], [-12, 50], [14, 22], [-5, 48], [-15, 48], [-33, 33], [-9, 44], [-57, 40], [-59, 93], [-32, 91], [-76, 76], [-50, 19], [-74, 106], [-12, 77], [4, 66], [-63, 123], [-53, 43], [-60, 23], [-36, 64], [6, 25]], [[7467, 2787], [-31, 57], [-32, 25]], [[7404, 2869], [-44, 83], [-67, 90], [-57, 76], [-55, -1], [17, 61], [5, 39], [14, 45], [-4, 16], [-31, -45], [-24, -84], [-30, -58], [-26, -19], [-37, 36]], [[7065, 3108], [-51, 49], [-79, 159]], [[6935, 3316], [-11, -10], [46, -117], [68, -112], [84, -173], [41, -60], [36, -63], [99, -123], [-22, -19], [4, -72], [129, -100], [20, -23], [36, -109], [-25, -20], [16, -114], [41, -133], [43, -27], [60, -41], [65, -129], [30, -102], [61, -54], [152, -105], [62, -63], [60, -64], [35, -39], [54, -33], [27, -34], [-4, -47], [-63, -26], [47, -31], [37, -20], [21, -46]], [[8184, 1207], [50, -47], [56, 0]], [[8290, 1160], [104, 29], [121, 13], [98, 34], [55, 8], [40, 20], [63, 4], [36, 2], [51, 16], [59, 12], [52, 38], [42, 0], [3, -31], [-10, -64], [0, -59], [-23, -40], [-32, -120], [-53, -124], [-69, -142], [-95, -163], [-95, -124], [-131, -152], [-111, -90], [-166, -110], [-104, -85], [-29, -32]], [[4355, 0], [10, 30], [23, 82], [-21, 17], [38, 125], [16, 87], [-43, 74], [-51, 19], [-22, 50], [-29, 15], [1, 31], [-115, -40], [-42, 6], [-43, -25], [-89, 3], [-59, 69], [-37, 80], [-78, 74], [-84, -2], [-98, 0], [-92, -13], [-89, -23], [-174, -65], [-62, -38], [-100, -33], [-99, 32], [-51, -1], [-77, 21], [-72, -1], [-131, -19], [-77, -32], [-110, -41], [-22, 3], [-29, -1], [-114, 53], [-101, 84], [-95, 61], [-75, 72], [-30, 8], [-80, 45], [-58, 59], [-19, 41], [-14, 82], [-48, 66], [-44, 43], [-28, 15], [-28, 22], [-12, 49], [-17, 24], [-32, 19], [-59, 46], [-47, 7], [-26, 32], [1, 17], [-34, 23], [-7, 24], [-18, 85], [14, 49], [-46, 87], [-55, 39], [49, 22], [54, 78], [26, 57], [-10, 59], [31, 55], [14, 105], [-12, 110]], [[8519, 4956], [111, 115], [108, 18], [50, 66]], [[8788, 5155], [104, 23], [128, 49], [95, -27], [111, 5], [20, -68], [-20, -109], [-97, 17], [-95, -18], [-4, -81], [-108, 10], [4, -36], [61, -28], [50, -100], [129, -38], [21, -38], [-27, -47], [6, -27], [35, -72], [11, 82], [89, 28], [32, -64], [81, -66], [-97, -36], [-105, 27], [-25, -93], [74, -7], [-28, -76], [86, -37], [-16, -117], [21, -78], [-11, -26], [-173, -30], [-158, 20], [-78, 55], [-105, 23], [-35, 82], [-3, 55], [41, 26], [19, 39], [19, 87], [92, 9], [-35, 29], [-51, 6], [-57, 78], [-58, 59]], [[8731, 4645], [-122, 131], [11, 75], [-101, 105]], [[0, 9284], [322, -5], [695, -88], [-205, -43], [-425, -5], [-387, -7]], [[0, 9117], [238, 8], [335, -38], [215, 34], [93, -40], [-122, -65], [283, 41], [539, 43], [333, -21], [63, -48], [-453, -79], [-63, -25], [-355, -19], [257, -6], [-130, -81], [-89, -72], [3, -123], [134, -73], [-174, -4], [-183, -36], [205, -59], [27, -94], [-119, -10], [144, -96], [-247, -8], [129, -45], [-37, -39], [-156, -17], [-155, -1], [139, -75], [2, -49], [-220, 46], [-57, -30], [149, -28], [146, -68], [42, -89], [-198, -22], [-86, 43], [-137, 64], [38, -75], [-129, -59], [293, -5], [153, -6], [-298, -96], [-302, -88], [-250, -29]], [[4246, 5338], [37, -7], [96, 17], [145, 57], [107, 16], [39, 26], [14, -3], [25, -5], [13, -3], [17, -6], [38, -13], [37, -13], [17, -7], [135, -38], [33, -3], [71, 18], [34, -2], [104, -20], [68, -34]], [[5276, 5318], [41, -12], [93, 14]], [[5410, 5320], [24, -9], [13, -15], [-11, -55], [3, -44], [-8, -55], [-13, -28], [2, -7], [3, -7], [8, -11], [14, -20], [7, -12]], [[5452, 5057], [9, -8], [17, -15], [9, -7], [14, -2], [29, -4], [29, -5], [14, -2], [42, -37], [25, -6], [66, 9], [23, -6]], [[5729, 4974], [64, -32], [35, 24], [28, -13], [-26, -19], [20, -20]], [[5850, 4914], [32, -45], [43, 8], [86, -17], [163, -6], [55, 28], [131, 26]], [[6360, 4908], [4, -3], [2, -2], [9, 4], [24, 8], [25, 7], [12, 3], [14, 9], [-1, 9], [-19, 28], [12, 53], [7, 7], [12, 12], [8, 9], [13, -4], [24, -9], [13, -6]], [[6519, 5033], [53, 18], [50, -19]], [[3006, 5998], [-75, 68], [-1, 2]], [[9999, 8446], [0, -138]], [[7408, 5027], [81, 17], [92, 93]], [[7519, 5181], [106, 30], [57, 16]], [[7417, 5188], [-24, -1], [-80, -5]], [[7313, 5182], [-41, -17], [-55, -24]], [[7217, 5141], [3, -24], [3, -36]], [[7223, 5081], [1, -9]], [[7391, 5052], [-11, -21], [-10, -19]], [[7036, 4951], [7, 17], [17, 35]], [[6408, 4730], [6, -11], [29, -52]], [[6443, 4667], [-95, 15], [-113, -35]], [[6235, 4647], [54, -29], [-35, -70], [-26, -12]], [[6160, 4539], [-39, 7], [-19, 3]], [[6102, 4549], [-14, -3], [-120, -26]], [[5872, 4406], [17, -16], [38, -36]], [[5927, 4354], [-38, -22], [-4, -2]], [[5885, 4330], [12, -10], [50, -42]], [[5905, 4047], [-14, -1], [-59, -1]], [[5462, 4661], [-15, 17], [-16, 19]], [[5431, 4697], [-12, 6], [-36, 17]], [[5019, 4915], [13, 5], [9, 4]], [[5041, 4924], [-21, 19], [-29, 27]], [[4991, 4970], [-1, 8], [-1, 30]], [[4917, 5025], [-29, -41], [-5, -6]], [[4857, 5055], [6, 3], [18, 7]], [[4793, 5081], [-35, -15], [-55, -24]], [[4709, 4987], [-12, -28], [-2, -4]], [[4732, 4899], [95, -52], [9, -4]], [[4836, 4843], [0, -1], [56, -91]], [[5129, 4637], [-9, -6], [-22, -16]], [[5098, 4615], [21, -8], [79, -32]], [[5198, 4575], [70, -29], [11, -5]], [[5207, 4493], [-18, -28], [-29, -44]], [[5160, 4421], [72, -37], [9, -4]], [[5181, 4316], [-26, -43], [-33, -53]], [[5122, 4220], [-20, -3], [-26, -5]], [[5076, 4212], [0, 1], [0, 33]], [[5123, 4329], [-18, 26], [-26, 38]], [[5045, 4449], [-12, 4], [-34, 10]], [[4765, 4583], [-83, 48], [-4, 2]], [[4678, 4633], [-1, 1], [-101, 72]], [[4411, 4892], [-35, 15], [-55, 22]], [[4321, 4929], [-27, -8], [-25, -7]], [[4269, 4914], [-32, -26], [-32, -25]], [[3413, 4519], [-7, -6], [-61, -55]], [[3345, 4304], [-9, -8], [-55, -42]], [[3281, 4254], [-18, -53], [-6, -19]], [[3257, 4182], [-61, -16], [-23, -6]], [[2954, 4073], [-25, 0], [-81, 2]], [[2848, 4075], [-53, -30], [-17, -9]], [[2681, 4003], [-40, 37], [-1, 0]], [[2609, 4104], [-28, 5], [-76, 13]], [[2344, 4096], [11, 56], [6, 31]], [[2361, 4183], [-6, 40], [-4, 28]], [[2283, 4376], [38, 35], [7, 6]], [[2336, 4462], [20, 59], [3, 8]], [[2447, 4860], [103, -15], [33, -5]], [[2583, 4840], [73, 1], [76, 0]], [[2850, 4822], [58, 4], [34, 2]], [[2942, 4828], [37, -1], [143, -3]], [[3179, 4891], [0, 2], [21, 219]], [[3200, 5112], [-8, 9], [-106, 108]], [[2834, 5328], [-1, 7], [-10, 74]], [[2967, 5433], [43, -7], [143, -22]], [[3482, 5569], [23, 63], [10, 28]], [[3701, 5705], [22, 11], [36, 19]], [[3759, 5735], [97, 164], [152, 46]], [[4311, 6002], [-21, 34], [-5, 7]], [[4285, 6043], [-1, 17], [-4, 46]], [[4280, 6106], [-39, 54], [-6, 8]], [[4235, 6168], [0, 10], [-3, 104]], [[4380, 6352], [6, 4], [33, 26]], [[4419, 6382], [58, 21], [31, 11]], [[4508, 6414], [-2, -38], [-1, -19]], [[4505, 6357], [-23, -26], [-10, -11]], [[4405, 6163], [24, -44], [6, -10]], [[4435, 6109], [1, -9], [1, -34]], [[4550, 6040], [-1, -19], [-1, -21]], [[4548, 6000], [62, 12], [51, 9]], [[4661, 6021], [14, 7], [49, 24]], [[5676, 6309], [54, 69], [1, 0]], [[6013, 6336], [1, 5], [20, 80]], [[6006, 6472], [-68, 39], [-2, 1]], [[6067, 6607], [61, 7], [79, 9]], [[6055, 6672], [-20, -2], [-161, -21]], [[5673, 6956], [18, 22], [35, 42]], [[5826, 7090], [142, 67], [113, 54]], [[6081, 7211], [32, 10], [42, 13]], [[5989, 7333], [-58, -9], [-133, -22]], [[5314, 6737], [103, -60], [3, -2]], [[5420, 6675], [-8, -10], [-94, -115]], [[5203, 6524], [-39, -171], [-4, -16]], [[4643, 6382], [-51, 94], [-34, 63]], [[4558, 6539], [-30, 27], [-44, 41]], [[4484, 6607], [-82, -48], [-138, -80]], [[4264, 6479], [-72, -13], [-76, -13]], [[4116, 6453], [-90, 33], [-64, 23]], [[4706, 7319], [138, 111], [129, 104]], [[5891, 7799], [76, 42], [93, 50]], [[6903, 7767], [75, -28], [107, -40]], [[7779, 7362], [-172, -28], [-10, -1]], [[7597, 7333], [-124, 21], [-372, 63]], [[7101, 7417], [-60, -10], [-21, -4]], [[7020, 7403], [65, -29], [116, -52]], [[7201, 7322], [0, -1]], [[7201, 7321], [14, -165]], [[8281, 7417], [-24, 17], [-69, 49]], [[8188, 7483], [45, 55], [9, 11]], [[8242, 7549], [-11, 10], [-70, 59]], [[8471, 7583], [41, -40], [23, -23]], [[8482, 7407], [96, 13], [76, 11]], [[8654, 7431], [13, 35], [14, 36]], [[8681, 7502], [232, 53], [388, 95]], [[9385, 7645], [-77, -48], [-33, -20]], [[9275, 7577], [13, -1], [125, -10]], [[9413, 7566], [80, 38]], [[9992, 7586], [1, 0]], [[9993, 7586], [6, 4]], [[9999, 7590], [0, -1815], [-4, -2], [4, -2], [0, -123], [-7, 1], [-33, -33], [-142, 57], [-176, -2], [-118, -47], [-131, 45], [-245, 77], [-174, -3], [-229, -121], [-14, -81], [-114, 65], [-89, -123], [33, -22], [-65, -84], [95, -76], [82, 3], [71, -74], [-11, -57], [56, -18]], [[8731, 4645], [-67, -45], [-19, -28], [-49, 8], [-76, 67], [-31, 4], [-70, 25], [-34, 46], [-104, 23], [-67, -17], [-20, 21], [-151, 53], [-164, 18], [-93, 19], [-14, -13]], [[6977, 3905], [-7, -1], [-53, -4]], [[7111, 3886], [-4, 9], [-8, 21]], [[9999, 8308], [0, -718]], [[9993, 7586], [-127, 67]], [[9413, 7566], [-138, 11]], [[8161, 7618], [81, -69]], [[8242, 7549], [-54, -66]], [[7463, 7237], [-73, -40], [69, -49]], [[7201, 7321], [-181, 82]], [[7902, 7421], [-7, 74], [-86, 52]], [[3482, 5569], [-259, -86], [-105, 47]], [[1490, 2462], [-43, -31], [-5, -51]], [[4355, 0], [-4355, 0], [0, 7578]], [[0, 9117], [0, 19]], [[0, 9284], [0, 715], [9999, -1553]], [[8290, 1160], [-55, 1], [-51, 46]], [[6935, 3316], [80, -159], [50, -49]], [[7404, 2869], [32, -24], [31, -58]], [[9129, 1733], [2, 38], [22, 49]], [[9474, 1914], [0, 45], [43, 28]], [[9711, 2653], [-62, 40], [-50, 76]], [[9999, 2813], [0, -2813], [-1903, 0]], [[6648, 6802], [113, 61], [186, -73], [46, -53], [-14, -17], [-24, 5], [-2, -36], [-98, 3], [-21, -35], [-45, 1], [0, 24], [-64, 54], [-3, 24], [-74, 42]], [[4886, 6578], [1, -14], [-8, -20], [-70, -33], [-51, -11], [-41, 0], [9, 30], [-2, 12], [20, 8], [36, 4], [19, 15], [44, 23], [43, -14]], [[7301, 6919], [38, -62], [37, -49], [-31, -29], [-85, -8], [-53, 19], [38, 0], [41, -3], [-47, 34], [-33, 17], [-42, 35], [-24, 40], [3, 8], [41, -7], [-4, 24], [23, -17], [27, -17], [16, 6], [27, 7], [-36, 25], [-58, 31], [42, -2], [26, -8], [54, -44]], [[7580, 5233], [3, 49], [57, 31], [108, 9], [17, 37], [-24, 61], [45, 58], [-1, 33], [-164, 36], [-65, -1], [-69, 52], [-85, -18], [-141, 39], [2, 22], [-39, 48], [-89, 6], [-9, 34], [28, 23], [-71, 62], [-115, -10], [-34, 5], [-28, -25], [-42, 4]], [[6864, 5788], [-27, 72], [-26, 37], [21, 10], [90, -4], [43, 24], [-32, 30], [-75, 20], [7, 20], [-45, 20], [-70, 73], [24, 30], [-11, 52], [-109, 26], [-58, -13], [-16, 28], [-117, 27]], [[6463, 6240], [-35, 66], [-10, 54], [-53, 25]], [[6365, 6385], [47, 36], [-33, 103], [79, 64], [-16, 20]], [[6452, 6722], [237, 142], [104, 64], [41, 57], [-164, 76], [45, 72], [-100, 83], [75, 95], [-129, 127], [102, 83], [-170, 74], [17, 78]], [[6510, 7673], [89, 10], [189, 45]], [[5858, 6036], [-204, -2], [-137, 13]], [[5696, 6132], [116, -20], [49, -17], [-11, -31], [8, -28]], [[7048, 5107], [29, 28], [79, -24], [36, -4], [14, -22], [17, -4]], [[7065, 5094], [-17, 13]], [[6510, 7673], [47, 78], [-143, 44], [-172, -37], [-55, -82], [-106, -49], [-119, 27], [-145, -6], [-124, 59], [-66, -29]], [[5627, 7678], [-69, -5], [-16, -73], [-210, 18], [-29, -62], [-107, 0], [-73, -79], [-111, -123], [-173, -156], [41, -38], [-39, -44], [-110, 2], [-73, -104], [7, -148], [71, -56], [-36, -131], [-93, -76], [-49, -64]], [[4020, 5495], [53, -29], [160, -20], [-56, -76], [-14, -79]], [[4163, 5291], [-31, -19], [-51, 10], [4, -28], [-81, -63], [-2, -50], [53, 17], [38, -48]], [[4093, 5110], [-4, -32], [32, -41], [-38, -34], [29, -86], [60, -14], [-13, -49]], [[3665, 4719], [-129, -15], [-125, 51], [-40, -24], [-205, 50], [-44, 43]], [[3612, 5683], [16, -39], [52, -2], [52, -45], [77, -52], [57, 8], [97, -50]], [[3963, 5503], [25, -10], [32, 2]], [[5627, 7678], [148, -55], [173, -75], [3, -171], [38, -44]], [[6864, 5788], [-95, -6], [-34, -24], [-7, -56], [-45, 10], [-100, -5], [-29, 26], [-42, -19], [-41, 16], [-88, 2], [-124, 27], [-112, 8], [-86, -2], [-61, -30], [-53, -5]], [[5947, 5730], [-2, 50], [-35, 51], [67, 23], [1, 44], [-31, 43], [-5, 49]], [[5942, 5990], [107, -1], [121, 42], [26, 62], [91, 36], [-10, 50]], [[6277, 6179], [67, 19], [119, 42]], [[7217, 5141], [6, -60]], [[7048, 5107], [-16, 13]], [[6519, 5033], [-49, 21]], [[6470, 5054], [28, 12], [19, 38], [31, 35], [-8, 20], [23, 9], [11, -15], [65, -4], [30, 9], [-21, 11], [8, 17], [-39, 28], [-16, 46], [-40, 18], [8, 38], [-51, 30], [-45, 4], [-82, 35], [-74, -11], [-27, -17]], [[6290, 5357], [-46, 0], [-28, -26], [-82, -10], [-38, -17], [-52, 27], [-71, 0], [-69, 13], [-48, -24]], [[5856, 5320], [-8, 29], [-61, 31]], [[5787, 5380], [21, 45], [31, 28]], [[5839, 5453], [24, -6], [-28, 50], [101, 92], [55, 13], [12, 31], [-56, 97]], [[5839, 5453], [-105, 43], [-80, -16], [-53, 12], [-65, -24], [-56, 39], [-46, -15], [-6, 7]], [[5428, 5499], [-52, 55], [-82, 6], [-11, 35], [-76, 13], [-16, -29], [-61, 23], [7, 31], [-83, 9], [-53, 36]], [[5001, 5678], [-45, 71], [9, 38], [-28, 60], [-40, 39], [31, 30], [-26, 56]], [[5858, 6036], [57, -12], [27, -34]], [[5219, 5347], [-8, -46], [-63, 0], [22, -24], [-37, -72]], [[5133, 5205], [-21, -18], [-97, -3], [-56, -25], [-92, 8]], [[4867, 5167], [-159, 29], [-25, 39], [-110, -20], [-13, -21], [-67, 16]], [[4493, 5210], [-57, 3], [-50, 20], [17, 27], [-4, 20]], [[4399, 5280], [33, 6], [57, -31], [15, 30], [98, -5], [80, 20], [53, -4], [35, -22], [10, 19], [-16, 72], [40, 14], [40, 51]], [[4844, 5430], [82, -36], [63, 46], [39, 8], [86, -34], [52, 6], [51, -21]], [[5217, 5399], [-9, -14], [11, -38]], [[5856, 5320], [-68, -24], [-52, -75], [-67, -75], [-90, -21]], [[5579, 5125], [-69, 5], [-85, -30]], [[5425, 5100], [-42, -16], [-91, 21], [-83, 48], [-36, 14]], [[5173, 5167], [-21, 37], [-19, 1]], [[5219, 5347], [57, -29]], [[5410, 5320], [9, 22], [44, 3], [54, 17], [12, -7], [53, 14], [26, 26], [36, 7], [119, -34], [24, 12]], [[6470, 5054], [-20, 50], [12, 48], [-4, 49], [-64, 66], [-35, 47], [-35, 32], [-34, 11]], [[6506, 4856], [-66, 12], [-80, 40]], [[5729, 4974], [-9, 46], [-68, 26], [-12, 35], [-61, 44]], [[5672, 6225], [128, 34], [186, -7], [109, 11], [16, -23], [59, -7], [107, -54]], [[6034, 6421], [95, 19], [48, -13], [96, -41], [92, -1]], [[5001, 5678], [-49, -12], [-29, 13], [-28, -21], [-80, -22], [-41, -27], [-81, -24], [19, -33], [12, -47], [57, -27], [63, -48]], [[4399, 5280], [-119, 34], [-23, -24], [-94, 1]], [[4020, 5495], [7, 49], [-23, 25]], [[4004, 5569], [13, 75]], [[4017, 5644], [-19, 117], [67, 0], [28, 42], [28, 101], [-21, 38]], [[4280, 6106], [84, -14], [71, 17]], [[6235, 4647], [-2, -55], [-101, -11], [-78, 39], [-89, -31], [-82, 4]], [[5883, 4593], [-8, 73], [-56, 36]], [[5819, 4702], [19, 15], [-12, 14], [18, 35], [43, 35], [-54, 48], [-10, 40], [27, 25]], [[5572, 4402], [51, 54], [7, 36], [36, 16], [2, 30]], [[5668, 4538], [73, 9], [42, 25], [61, -2], [18, 19], [21, 4]], [[5485, 4653], [-7, 35], [48, 55], [7, -21], [30, 10]], [[5563, 4732], [24, -30], [26, -12], [8, -40]], [[5621, 4650], [-15, -38], [16, -47], [46, -27]], [[5425, 5100], [27, -43]], [[5452, 5057], [35, -31], [-42, -42]], [[5445, 4984], [-51, 25], [-77, -2], [-95, 18], [-52, -2], [-24, -23], [-40, 25], [-23, -46], [54, -52], [24, -34], [51, -42], [43, -24], [42, -47], [98, -42]], [[5395, 4738], [-12, -18]], [[4857, 5055], [77, -4], [21, 19], [37, -18], [44, -2], [-1, 31], [39, 11], [11, 45], [88, 30]], [[4493, 5210], [-9, -46], [-49, -18], [-82, 14], [-24, -45], [-53, -4], [-19, 18], [-62, -38], [-54, -5], [-48, 24]], [[3963, 5503], [12, 62], [29, 4]], [[3701, 5705], [82, -9], [103, 23], [70, -49], [61, -26]], [[2329, 4653], [41, 28], [45, 16], [28, -54], [65, 0], [19, 14], [65, -4], [31, -55], [-51, -31], [-2, -86], [-18, -16], [-4, -53], [-48, -9], [44, -67], [-30, -73], [38, -33], [-15, -30], [-41, -42], [9, -36]], [[2492, 6125], [23, -59], [-23, -60], [68, 2], [84, -23]], [[4867, 5167], [-12, -55], [26, -47]], [[3101, 6212], [9, -12], [99, -131]], [[2954, 5713], [-1, 1], [-174, 18]], [[5217, 5399], [16, 25], [49, -2], [38, 11], [3, 11], [21, 5], [8, 25], [25, 5], [17, 20], [34, 0]], [[5445, 4984], [40, 0], [-28, -49], [54, -42], [-17, -53], [-26, -5]], [[5468, 4835], [-21, -10], [-36, -25], [-16, -62]], [[5621, 4650], [14, -1], [5, 23], [65, 17], [25, 4]], [[5730, 4693], [38, 7], [51, 2]], [[5730, 4693], [-4, 9], [14, 13], [12, 27], [-16, -1], [-21, 21], [-18, 5], [-15, 18], [-21, 7], [-16, 16], [-19, -7], [-16, -37], [-26, -8]], [[5584, 4756], [9, 10], [-43, 23], [-36, 12], [-16, 15], [-30, 19]], [[5584, 4756], [-21, -24]]], "transform": {"scale": [0.009000900090009001, 0.009000900090009001], "translate": [-30, 0]}, "bbox": [-30, 0, 60, 90]}