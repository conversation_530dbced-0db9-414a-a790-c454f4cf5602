{"version": 3, "names": ["_index", "require", "getQualifiedName", "node", "isIdentifier", "name", "id", "qualification", "removeTypeDuplicates", "nodesIn", "nodes", "Array", "from", "generics", "Map", "bases", "typeGroups", "Set", "types", "i", "length", "includes", "isAnyTypeAnnotation", "isFlowBaseAnnotation", "set", "type", "isUnionTypeAnnotation", "has", "push", "add", "isGenericTypeAnnotation", "existing", "get", "typeParameters", "params", "baseType", "genericName"], "sources": ["../../../src/modifications/flow/removeTypeDuplicates.ts"], "sourcesContent": ["import {\n  isAnyTypeAnnotation,\n  isGenericTypeAnnotation,\n  isUnionTypeAnnotation,\n  isFlowBaseAnnotation,\n  isIdentifier,\n} from \"../../validators/generated/index.ts\";\nimport type * as t from \"../../index.ts\";\n\nfunction getQualifiedName(node: t.GenericTypeAnnotation[\"id\"]): string {\n  return isIdentifier(node)\n    ? node.name\n    : `${node.id.name}.${getQualifiedName(node.qualification)}`;\n}\n\n/**\n * Dedupe type annotations.\n */\nexport default function removeTypeDuplicates(\n  nodesIn: ReadonlyArray<t.FlowType | false | null | undefined>,\n): t.FlowType[] {\n  const nodes = Array.from(nodesIn);\n\n  const generics = new Map<string, t.GenericTypeAnnotation>();\n  const bases = new Map<t.FlowBaseAnnotation[\"type\"], t.FlowBaseAnnotation>();\n\n  // store union type groups to circular references\n  const typeGroups = new Set<t.FlowType[]>();\n\n  const types: t.FlowType[] = [];\n\n  for (let i = 0; i < nodes.length; i++) {\n    const node = nodes[i];\n    if (!node) continue;\n\n    // detect duplicates\n    if (types.includes(node)) {\n      continue;\n    }\n\n    // this type matches anything\n    if (isAnyTypeAnnotation(node)) {\n      return [node];\n    }\n\n    if (isFlowBaseAnnotation(node)) {\n      bases.set(node.type, node);\n      continue;\n    }\n\n    if (isUnionTypeAnnotation(node)) {\n      if (!typeGroups.has(node.types)) {\n        nodes.push(...node.types);\n        typeGroups.add(node.types);\n      }\n      continue;\n    }\n\n    // find a matching generic type and merge and deduplicate the type parameters\n    if (isGenericTypeAnnotation(node)) {\n      const name = getQualifiedName(node.id);\n\n      if (generics.has(name)) {\n        let existing: t.Flow = generics.get(name);\n        if (existing.typeParameters) {\n          if (node.typeParameters) {\n            existing.typeParameters.params.push(...node.typeParameters.params);\n            existing.typeParameters.params = removeTypeDuplicates(\n              existing.typeParameters.params,\n            );\n          }\n        } else {\n          existing = node.typeParameters;\n        }\n      } else {\n        generics.set(name, node);\n      }\n\n      continue;\n    }\n\n    types.push(node);\n  }\n\n  // add back in bases\n  for (const [, baseType] of bases) {\n    types.push(baseType);\n  }\n\n  // add back in generics\n  for (const [, genericName] of generics) {\n    types.push(genericName);\n  }\n\n  return types;\n}\n"], "mappings": ";;;;;;AAAA,IAAAA,MAAA,GAAAC,OAAA;AASA,SAASC,gBAAgBA,CAACC,IAAmC,EAAU;EACrE,OAAO,IAAAC,mBAAY,EAACD,IAAI,CAAC,GACrBA,IAAI,CAACE,IAAI,GACT,GAAGF,IAAI,CAACG,EAAE,CAACD,IAAI,IAAIH,gBAAgB,CAACC,IAAI,CAACI,aAAa,CAAC,EAAE;AAC/D;AAKe,SAASC,oBAAoBA,CAC1CC,OAA6D,EAC/C;EACd,MAAMC,KAAK,GAAGC,KAAK,CAACC,IAAI,CAACH,OAAO,CAAC;EAEjC,MAAMI,QAAQ,GAAG,IAAIC,GAAG,CAAkC,CAAC;EAC3D,MAAMC,KAAK,GAAG,IAAID,GAAG,CAAqD,CAAC;EAG3E,MAAME,UAAU,GAAG,IAAIC,GAAG,CAAe,CAAC;EAE1C,MAAMC,KAAmB,GAAG,EAAE;EAE9B,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGT,KAAK,CAACU,MAAM,EAAED,CAAC,EAAE,EAAE;IACrC,MAAMhB,IAAI,GAAGO,KAAK,CAACS,CAAC,CAAC;IACrB,IAAI,CAAChB,IAAI,EAAE;IAGX,IAAIe,KAAK,CAACG,QAAQ,CAAClB,IAAI,CAAC,EAAE;MACxB;IACF;IAGA,IAAI,IAAAmB,0BAAmB,EAACnB,IAAI,CAAC,EAAE;MAC7B,OAAO,CAACA,IAAI,CAAC;IACf;IAEA,IAAI,IAAAoB,2BAAoB,EAACpB,IAAI,CAAC,EAAE;MAC9BY,KAAK,CAACS,GAAG,CAACrB,IAAI,CAACsB,IAAI,EAAEtB,IAAI,CAAC;MAC1B;IACF;IAEA,IAAI,IAAAuB,4BAAqB,EAACvB,IAAI,CAAC,EAAE;MAC/B,IAAI,CAACa,UAAU,CAACW,GAAG,CAACxB,IAAI,CAACe,KAAK,CAAC,EAAE;QAC/BR,KAAK,CAACkB,IAAI,CAAC,GAAGzB,IAAI,CAACe,KAAK,CAAC;QACzBF,UAAU,CAACa,GAAG,CAAC1B,IAAI,CAACe,KAAK,CAAC;MAC5B;MACA;IACF;IAGA,IAAI,IAAAY,8BAAuB,EAAC3B,IAAI,CAAC,EAAE;MACjC,MAAME,IAAI,GAAGH,gBAAgB,CAACC,IAAI,CAACG,EAAE,CAAC;MAEtC,IAAIO,QAAQ,CAACc,GAAG,CAACtB,IAAI,CAAC,EAAE;QACtB,IAAI0B,QAAgB,GAAGlB,QAAQ,CAACmB,GAAG,CAAC3B,IAAI,CAAC;QACzC,IAAI0B,QAAQ,CAACE,cAAc,EAAE;UAC3B,IAAI9B,IAAI,CAAC8B,cAAc,EAAE;YACvBF,QAAQ,CAACE,cAAc,CAACC,MAAM,CAACN,IAAI,CAAC,GAAGzB,IAAI,CAAC8B,cAAc,CAACC,MAAM,CAAC;YAClEH,QAAQ,CAACE,cAAc,CAACC,MAAM,GAAG1B,oBAAoB,CACnDuB,QAAQ,CAACE,cAAc,CAACC,MAC1B,CAAC;UACH;QACF,CAAC,MAAM;UACLH,QAAQ,GAAG5B,IAAI,CAAC8B,cAAc;QAChC;MACF,CAAC,MAAM;QACLpB,QAAQ,CAACW,GAAG,CAACnB,IAAI,EAAEF,IAAI,CAAC;MAC1B;MAEA;IACF;IAEAe,KAAK,CAACU,IAAI,CAACzB,IAAI,CAAC;EAClB;EAGA,KAAK,MAAM,GAAGgC,QAAQ,CAAC,IAAIpB,KAAK,EAAE;IAChCG,KAAK,CAACU,IAAI,CAACO,QAAQ,CAAC;EACtB;EAGA,KAAK,MAAM,GAAGC,WAAW,CAAC,IAAIvB,QAAQ,EAAE;IACtCK,KAAK,CAACU,IAAI,CAACQ,WAAW,CAAC;EACzB;EAEA,OAAOlB,KAAK;AACd", "ignoreList": []}