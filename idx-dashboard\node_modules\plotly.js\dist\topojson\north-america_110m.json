{"type": "Topology", "objects": {"coastlines": {"type": "GeometryCollection", "geometries": [{"type": "LineString", "arcs": [0]}, {"type": "LineString", "arcs": [1]}, {"type": "LineString", "arcs": [2]}, {"type": "LineString", "arcs": [3]}, {"type": "LineString", "arcs": [4]}, {"type": "LineString", "arcs": [5]}, {"type": "LineString", "arcs": [6]}, {"type": "LineString", "arcs": [7]}, {"type": "LineString", "arcs": [8]}, {"type": "LineString", "arcs": [9]}, {"type": "LineString", "arcs": [10]}, {"type": "LineString", "arcs": [11]}, {"type": "LineString", "arcs": [12]}, {"type": "LineString", "arcs": [13, 14, 15]}, {"type": "LineString", "arcs": [16]}, {"type": "LineString", "arcs": [17]}, {"type": "LineString", "arcs": [18, 19, 20, 21, 22, 23, 24, 25]}, {"type": "LineString", "arcs": [26]}, {"type": "LineString", "arcs": [27]}, {"type": "LineString", "arcs": [28]}, {"type": "LineString", "arcs": [29]}, {"type": "LineString", "arcs": [30]}, {"type": "LineString", "arcs": [31]}, {"type": "LineString", "arcs": [32, 33, 34]}, {"type": "LineString", "arcs": [35]}, {"type": "LineString", "arcs": [36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 123, 124, 125, 126, 127, 128, 129, 130, 131, 132, 133, 134, 135, 136, 137, 138, 139, 140, 141, 142, 143, 144, 145, 146, 147, 148, 149, 150, 151, 152, 153, 154, 155, 156, 157, 158, 159, 160, 161, 162, 163, 164, 165, 166, 167, 168, 169, 170, 171, 172, 173, 174, 175, 176, 177, 178, 179, 180, 181, 182, 183, 184, 185, 186, 187, 188, 189, 190, 191, 192, 193, 194, 195, 196, 197, 198, 199, 200, 201, 202, 203, 204, 205, 206, 207, 208, 209, 210, 211, 212, 213, 214, 215, 216, 217, 218, 219, 220, 221, 222, 223, 224, 225, 226, 227, 228, 229, 230, 231, 232, 233, 234, 235, 236, 237, 238, 239, 240, 241, 242, 243, 244, 245, 246, 247, 248, 249, 250, 251, 252, 253, 254, 255, 256, 257, 258, 259, 260, 261, 262, 263, 264, 265, 266, 267, 268, 269, 270, 271, 272, 273, 274, 275, 276, 277, 278, 279, 280, 281, 282, 283, 284, 285, 286, 287, 288, 289, 290, 291, 292, 293, 294, 295, 296, 297, 298, 299, 300, 301, 302, 303, 304, 305, 306, 307, 308, 309, 310, 311, 312, 313, 314, 315, 316, 317, 318, 319, 320, 321, 322, 323, 324, 325, 326, 327, 328, 329, 330, 331, 332, 333, 334, 335, 336, 337, 338, 339, 340, 341, 342, 343, 344, 345, 346, 347, 348, 349, 350, 351, 352, 353, 354, 355, 356, 357, 358, 359, 360, 361, 362, 363, 364, 365, 366, 367, 368, 369, 370, 371, 372, 373, 374, 375, 376, 377, 378, 379, 380, 381, 382, 383, 384, 385, 386, 387, 388, 389, 390, 391, 392, 393, 394, 395, 396, 397, 398, 399, 400, 401, 402, 403, 404, 405, 406, 407, 408, 409, 410, 411, 412, 413, 414, 415, 416, 417, 418, 419, 420, 421, 422, 423, 424, 425, 426, 427, 428, 429, 430, 431, 432, 433, 434, 435, 436, 437, 438, 439, 440, 441, 442, 443, 444, 445, 446, 447, 448, 449, 450, 451, 452, 453, 454, 455, 456, 457, 458, 459, 460, 461, 462, 463, 464, 465, 466, 467, 468, 469, 470, 471, 472, 473, 474, 475, 476, 477, 478, 479]}, {"type": "LineString", "arcs": [480, 481, 482, 483, 484, 485, 486, 487, 488, 489, 490]}, {"type": "LineString", "arcs": [491, 492, 493, 494]}, {"type": "LineString", "arcs": [495]}, {"type": "LineString", "arcs": [-496, 496]}, {"type": "LineString", "arcs": [497]}, {"type": "LineString", "arcs": [498]}, {"type": "LineString", "arcs": [499]}, {"type": "LineString", "arcs": [500]}, {"type": "LineString", "arcs": [501]}, {"type": "LineString", "arcs": [502]}, {"type": "LineString", "arcs": [503]}, {"type": "LineString", "arcs": [504]}, {"type": "LineString", "arcs": [505]}, {"type": "LineString", "arcs": [506]}, {"type": "LineString", "arcs": [507]}, {"type": "LineString", "arcs": [508]}, {"type": "LineString", "arcs": [509]}, {"type": "LineString", "arcs": [510]}, {"type": "LineString", "arcs": [511]}, {"type": "LineString", "arcs": [512, 513]}, {"type": "LineString", "arcs": [514, 515, 516]}, {"type": "LineString", "arcs": [517]}, {"type": "LineString", "arcs": [518]}, {"type": "LineString", "arcs": [519, 520, 521]}, {"type": "LineString", "arcs": [522]}, {"type": "MultiLineString", "arcs": [[523], [524], [525]]}, {"type": "LineString", "arcs": [526]}]}, "land": {"type": "GeometryCollection", "geometries": [{"type": "Polygon", "arcs": [[498]]}, {"type": "Polygon", "arcs": [[6]]}, {"type": "Polygon", "arcs": [[7]]}, {"type": "Polygon", "arcs": [[491, 544, 493, 494]]}, {"type": "Polygon", "arcs": [[499]]}, {"type": "Polygon", "arcs": [[500]]}, {"type": "Polygon", "arcs": [[501]]}, {"type": "Polygon", "arcs": [[502]]}, {"type": "Polygon", "arcs": [[503]]}, {"type": "Polygon", "arcs": [[8]]}, {"type": "Polygon", "arcs": [[504]]}, {"type": "Polygon", "arcs": [[505]]}, {"type": "Polygon", "arcs": [[506]]}, {"type": "Polygon", "arcs": [[507]]}, {"type": "Polygon", "arcs": [[508]]}, {"type": "Polygon", "arcs": [[29]]}, {"type": "Polygon", "arcs": [[9]]}, {"type": "Polygon", "arcs": [[28]]}, {"type": "Polygon", "arcs": [[27]]}, {"type": "Polygon", "arcs": [[26]]}, {"type": "Polygon", "arcs": [[509]]}, {"type": "Polygon", "arcs": [[510]]}, {"type": "Polygon", "arcs": [[30]]}, {"type": "Polygon", "arcs": [[10]]}, {"type": "Polygon", "arcs": [[511]]}, {"type": "Polygon", "arcs": [[513, 545]]}, {"type": "Polygon", "arcs": [[546, 243, 547, 245, 548, 549, 248, 550, 250, 551, 552, 253, 553, 255, 554, 555, 258, 556, 260, 557, 558, 559, 264, 560, 266, 561, 562, 269, 563, 564, 565, 566, 274, 567, 276, 568, 569, 279, 570, 571, 572, 573, 284, 574, 286, 575, 288, 576, 290, 577, 578, 579, 580, 295, 581, 582, 298, 583, 584, 301, 585, 586, 304, 587, 588, 589, 308, 590, 310, 591, 312, 592, 314, 593, 594, 317, 595, 596, 320, 321, 597, 323, 598, 325, 599, 600, 328, 601, 602, 331, 603, 604, 605, 335, 336, 606, 338, 339, 340, 607, 342, 343, 344, 345, 608, 347, 348, 349, 609, 610, 352, 353, 354, 611, 612, 357, 613, 614, 360, 361, 615, 616, 617, 366, 367, 368, 369, 618, 619, 371, 372, 373, 374, 375, 620, 621, 378, 622, 380, 623, 624, 383, 625, 626, 627, 628, 629, 388, 389, 630, 391, 392, 393, 394, 395, 631, 397, 632, 633, 399, 634, 401, 402, 635, 636, 637, 638, 407, 639, 640, 641, 411, 642, 413, 414, 643, 644, 417, 645, 646, 647, 648, -649, 421, 649, 423, 650, 425, 651, 652, 653, 429, 654, 431, 655, 433, 656, 657, 436, 658, 438, 659, 440, 660, 442, 661, 444, 445, 662, 447, 663, 449, 664, 451, 452, 665, 666, 454, 667, 668, 457, 669, 670, 671, 672, 673, 674, 675, 465, 676, 467, 677, 469, 678, 471, 679, 473, 680, 681, 682, 477, 683, 479, 480, 684, 482, 685, 686, 687, 688, 487, 689, 690, 490, 691, 36, 692, 693, 694, 40, 695, 42, 696, 44, 697, 46, 698, 699, 700, 50, 701, 52, 702, 703, 55, 704, 705, 58, 706, 60, 707, 61, 62, 63, 708, 709, 66, 710, 68, 711, 70, 71, 72, 712, 74, 713, 76, 77, 78, 714, 80, 715, 82, 716, 717, 718, 719, 87, 720, 89, 721, 722, 723, 93, 724, 725, 96, 726, 98, 727, 100, 728, 102, 729, 104, 730, 106, 731, 108, 732, 110, 733, 112, 734, 114, 735, 736, 117, 737, 119, 120, 738, 739, 123, 124, 125, 126, 127, 128, 129, 130, 131, 132, 133, 740, 135, 136, 741, 742, 139, 743, 141, 142, 143, 744, 745, 145, 746, 747, 148, 748, 749, 151, 750, 153, 154, 155, 751, 752, 753, 159, 754, 755, 162, 756, 757, 165, 758, 167, 759, 169, 760, 761, 172, 762, 174, 763, 764, 177, 765, 179, 766, 767, 182, 768, 184, 769, 770, 771, 187, 772, 773, 190, 774, 775, 193, 776, 777, 778, 197, 779, 780, 200, 781, 202, 782, 783, 205, 206, 784, 785, 209, 786, 787, 788, 213, 789, 215, 790, 217, 791, 219, 792, 221, 793, 223, 794, 795, 226, 796, 228, 797, 230, 798, 232, 799, 234, 800, 801, 237, 802, 239, 803, 241]]}, {"type": "Polygon", "arcs": [[515, 516, 514]]}, {"type": "Polygon", "arcs": [[526]]}, {"type": "Polygon", "arcs": [[517]]}, {"type": "Polygon", "arcs": [[11]]}, {"type": "Polygon", "arcs": [[804, 15, 13]]}, {"type": "Polygon", "arcs": [[12]]}, {"type": "Polygon", "arcs": [[22, 805, 806, 24, 25, 18, 807, 20, 808]]}, {"type": "Polygon", "arcs": [[0]]}, {"type": "Polygon", "arcs": [[518]]}, {"type": "Polygon", "arcs": [[16]]}, {"type": "Polygon", "arcs": [[3]]}, {"type": "Polygon", "arcs": [[17]]}, {"type": "Polygon", "arcs": [[1]]}, {"type": "Polygon", "arcs": [[4]]}, {"type": "Polygon", "arcs": [[5]]}, {"type": "Polygon", "arcs": [[2]]}, {"type": "Polygon", "arcs": [[31]]}, {"type": "Polygon", "arcs": [[519, 809, 521]]}, {"type": "Polygon", "arcs": [[522]]}, {"type": "MultiPolygon", "arcs": [[[810, 524]], [[523, 811, 525]]]}]}, "ocean": {"type": "GeometryCollection", "geometries": [{"type": "Polygon", "arcs": [[-12], [-518], [-511], [-510], [-11], [-512], [-16, -805, -14], [-13], [-21, -808, -19, -26, 812, -806, -23, -809], [813, -515, -517], [-527], [-546, -514], [-17], [-519], [-4], [-523], [-18], [-5], [-32], [-6], [-810, -520, -522], [-3], [-2], [-1], [-10], [-508], [-509], [-507], [-506], [-505], [-9], [-494, -545, -492, -495], [-7], [-8], [-499], [-500], [-501], [-502], [-503], [-504], [-30], [-29], [-28], [-27], [-31], [-497, 495, 814], [-498, 815], [-524, -526, 816, -525, 817, -35, 818, -33, -491, -490, -489, -488, -487, -486, -485, -484, -483, -482, -481, -480, -684, -478, -477, -476, -475, -474, -473, -472, -471, -470, -469, -468, -467, -466, -465, -464, -463, -462, -461, -460, -459, -458, -457, -456, -455, -667, -666, -453, -452, -451, -450, -449, -448, -447, -446, -445, -444, -443, -442, -441, -440, -439, -438, -437, -436, -435, -434, -433, -432, -431, -430, -429, -428, -427, -426, -425, -424, -423, -422, -421, -420, -419, -418, -417, -416, -415, -414, -413, -412, -411, -410, -409, -408, 819, -638, -405, -404, -403, -402, -401, -400, -634, 820, -397, -396, -395, -394, -393, -392, -391, -390, 821, 822, -387, -386, -385, -384, -383, -382, -381, -380, -379, -378, -377, -376, -375, -374, 823, -619, -370, -369, -368, -367, -618, -364, -363, -362, -361, -360, -359, -358, -357, -356, -355, -354, -353, -352, -351, -350, -349, -348, -347, -346, -345, -344, -343, -342, 824, -339, -338, -337, -336, -335, -334, -333, -332, -331, -330, -329, -328, -327, -326, -325, -324, 825, -321, -320, -319, -318, -317, -316, -315, -314, -313, -312, -311, -310, -309, -308, -307, -306, -305, -304, -303, -302, -301, -300, -299, -298, -297, -296, -295, -294, -293, -292, -291, -577, -289, -288, -287, -286, -285, -284, -283, -282, -281, -280, -279, -278, -277, -276, -275, -274, -273, -272, -271, -270, -269, -268, -267, -266, -265, -264, -263, -262, -261, -260, -259, -258, -257, -256, -255, -254, -253, -252, -251, -250, -249, -248, -247, -246, -245, -244, -243, -242, -241, -240, -239, -238, -237, -236, -235, -234, -233, -232, -231, -230, -229, -228, -227, -226, -225, -224, -223, -222, -221, -220, -219, -218, -217, -216, -215, -214, -213, -212, -211, -210, -209, -208, -207, 826, -205, -204, -203, -202, -201, -200, -199, -198, -197, -196, -195, -194, -193, -192, -191, -190, -189, -188, -187, 827, -770, -185, -184, -183, -182, -181, -180, -179, -178, -177, -176, -175, -174, -173, -172, -171, -170, -169, -168, -167, -166, -165, -164, -163, -162, -161, -160, -159, -158, -157, -156, 828, -154, -153, -152, -151, -150, -149, -148, -147, -146, -746, 829, -144, -143, -142, -141, -140, -139, -138, -137, -136, 830, -134, -133, -132, -131, -130, -129, -128, -127, -126, -125, -124, -123, -122, -121, -120, -119, -118, -117, -116, -115, -114, -113, -112, -111, -110, -109, -108, -107, -106, 831, -104, -103, -102, -101, -100, -99, -98, -97, -96, -95, -94, -93, -92, -91, -90, -721, -88, -87, -86, -85, -84, -83, -82, -81, -80, -79, -78, -77, -76, -75, -74, -73, 832, -71, -70, -69, -68, -67, -66, -65, -64, -63, 833, -61, -60, -59, -58, -57, -56, -55, -54, -53, -702, -51, -50, -49, -48, -47, -46, -45, -44, -43, -42, -41, -40, -39, -38, -37, 35, 834]]}]}, "lakes": {"type": "GeometryCollection", "geometries": [{"type": "Polygon", "arcs": [[835]]}, {"type": "Polygon", "arcs": [[836]]}, {"type": "Polygon", "arcs": [[837, 838]]}, {"type": "Polygon", "arcs": [[839, 840, 841, 842, 843, 844]]}, {"type": "Polygon", "arcs": [[845, 846, 847, 848, 849]]}, {"type": "Polygon", "arcs": [[850]]}, {"type": "Polygon", "arcs": [[851]]}, {"type": "Polygon", "arcs": [[852]]}, {"type": "Polygon", "arcs": [[853]]}, {"type": "Polygon", "arcs": [[854]]}, {"type": "Polygon", "arcs": [[855]]}, {"type": "Polygon", "arcs": [[856]]}, {"type": "Polygon", "arcs": [[857, 858, 859, 860]]}, {"type": "Polygon", "arcs": [[-858, 861, 862, 863, 864, 865, 866]]}]}, "rivers": {"type": "GeometryCollection", "geometries": [{"type": "LineString", "arcs": [527]}, {"type": "LineString", "arcs": [528, 529, 530, 531, 532, 533, 534, 535, 536, 537, 538, 539, 540, 541, 542, 543]}]}, "countries": {"type": "GeometryCollection", "geometries": [{"type": "MultiPolygon", "properties": {"ct": [-101.57, 57.75]}, "id": "CAN", "arcs": [[[142, 867, 209, 210, 211, 212, 213, 214, 215, 216, 217, 218, 219, 220, 221, 222, 223, 224, 225, 226, 227, 228, 229, 230, 231, 232, 233, 868, 235, 236, 237, 238, 239, 240, 241, 242, 243, 244, 245, 246, 247, 248, 249, 250, 251, 252, 253, 254, 255, 256, 257, 258, 259, 260, 261, 262, 263, 264, 265, 266, 267, 268, 269, 270, 271, 272, 273, 274, 275, 276, 277, 278, 279, 280, 281, 282, 283, 284, 285, 286, 287, 288, 289, 290, 291, 292, 293, 294, 295, 296, 297, 298, 299, 300, 301, 302, 303, 304, 305, 306, 307, 308, 309, 310, 311, 312, 313, 314, 315, 316, 317, 318, 319, 320, 321, 322, 323, 324, 325, 326, 327, 328, 329, 330, 331, 332, 333, 334, 335, 869, 870, 871, 872, 873, 874, 875, 876, 877, 878, 879, 880, 881, 882, 883, 884]], [[510]], [[517]], [[509]], [[0]], [[1]], [[2]], [[3]], [[4]], [[5]], [[9]], [[10]], [[11]], [[12]], [[17]], [[28]], [[31]], [[29]], [[18, 807, 20, 21, 22, 23, 24, 25]], [[16]], [[514, 515, 516]], [[13, 804, 15]], [[526]], [[518]], [[519, 809, 521]], [[522]], [[511]], [[513, 545]], [[508]], [[507]]]}, {"type": "MultiPolygon", "properties": {"ct": [-99.06, 39.5]}, "id": "USA", "arcs": [[[-885, -884, -883, -882, -881, -880, -879, -878, -877, -876, -875, -874, -873, -872, -871, -870, 336, 337, 338, 339, 340, 341, 342, 343, 344, 345, 346, 347, 348, 349, 350, 351, 352, 353, 354, 355, 356, 357, 358, 359, 360, 361, 362, 363, 364, 365, 366, 367, 368, 369, 370, 371, 372, 373, 374, 375, 376, 377, 378, 379, 380, 381, 382, 383, 384, 385, 386, 387, 388, 389, 390, 391, 392, 393, 394, 395, 396, 397, 398, 399, 400, 401, 402, 403, 404, 405, 406, 407, 408, 409, 410, 411, 412, 413, 414, 415, 416, 417, 418, 419, 420, 648, 885, 886, 887, 888, 889, 120, 121, 122, 123, 124, 125, 126, 127, 128, 129, 130, 131, 132, 133, 134, 135, 136, 137, 138, 139, 140, 141]], [[499]], [[500]], [[501]], [[502]], [[503]], [[26]], [[27]], [[-868, 143, 144, 145, 146, 147, 148, 149, 150, 151, 152, 153, 154, 155, 156, 157, 158, 159, 160, 161, 162, 163, 164, 165, 166, 167, 168, 169, 170, 171, 172, 173, 174, 175, 176, 177, 178, 179, 180, 181, 182, 183, 184, 185, 186, 187, 188, 189, 190, 191, 192, 193, 194, 195, 196, 197, 198, 199, 200, 201, 202, 203, 204, 205, 206, 207, 208]], [[30]]]}, {"type": "Polygon", "properties": {"ct": [-72.66, 18.9]}, "id": "HTI", "arcs": [[890, 494]]}, {"type": "Polygon", "properties": {"ct": [-70.46, 18.88]}, "id": "DOM", "arcs": [[-891, 491, 544, 493]]}, {"type": "MultiPolygon", "properties": {"ct": [-77.92, 24.51]}, "id": "BHS", "arcs": [[[505]], [[506]], [[504]]]}, {"type": "MultiPolygon", "properties": {"ct": [-53.28, 74.4]}, "id": "GRL", "arcs": [[[523, 811, 525]], [[524, 810]]]}, {"type": "Polygon", "properties": {"ct": [-102.58, 23.94]}, "id": "MEX", "arcs": [[-890, -889, -888, -887, -886, -649, 421, 422, 423, 424, 425, 426, 427, 428, 429, 430, 431, 432, 433, 434, 435, 436, 437, 438, 439, 440, 441, 442, 443, 444, 891, 892, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119]]}, {"type": "Polygon", "properties": {"ct": [-80.11, 8.53]}, "id": "PAN", "arcs": [[691, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 701, 52, 893, 480, 481, 482, 483, 484, 485, 486, 487, 488, 489, 490]]}, {"type": "Polygon", "properties": {"ct": [-84.18, 9.97]}, "id": "CRI", "arcs": [[-894, 53, 54, 55, 56, 57, 58, 59, 60, -834, 62, 894, 683, 479]]}, {"type": "Polygon", "properties": {"ct": [-85.02, 12.85]}, "id": "NIC", "arcs": [[-895, 63, 64, 65, 66, 67, 68, 69, 895, 467, 468, 469, 470, 471, 472, 473, 474, 475, 476, 477]]}, {"type": "Polygon", "properties": {"ct": [-86.59, 14.82]}, "id": "HND", "arcs": [[-896, 70, -833, 896, 897, 666, 454, 455, 456, 457, 458, 459, 460, 461, 462, 463, 464, 465, 466]]}, {"type": "Polygon", "properties": {"ct": [-88.87, 13.73]}, "id": "SLV", "arcs": [[-897, 72, 73, 74, 75, 76, 898]]}, {"type": "Polygon", "properties": {"ct": [-90.37, 15.7]}, "id": "GTM", "arcs": [[-893, 899, 452, 665, -898, -899, 77]]}, {"type": "Polygon", "properties": {"ct": [-88.7, 17.2]}, "id": "BLZ", "arcs": [[-892, 445, 446, 447, 448, 449, 450, 451, -900]]}, {"type": "Polygon", "properties": {"ct": [-66.48, 18.24]}, "id": "PRI", "arcs": [[6]]}, {"type": "Polygon", "properties": {"ct": [-77.32, 18.14]}, "id": "JAM", "arcs": [[7]]}, {"type": "Polygon", "properties": {"ct": [-78.96, 21.63]}, "id": "CUB", "arcs": [[8]]}, {"type": "Polygon", "properties": {"ct": [-61.33, 10.43]}, "id": "TTO", "arcs": [[498]]}]}, "subunits": {"type": "GeometryCollection", "geometries": [{"type": "Polygon", "properties": {"ct": [-94.3, 46.32], "gu": "USA"}, "id": "MN", "arcs": [[900, 901, 902, 903, -882, -850]]}, {"type": "Polygon", "properties": {"ct": [-109.66, 47.05], "gu": "USA"}, "id": "MT", "arcs": [[904, 905, 906, 907, 908]]}, {"type": "Polygon", "properties": {"ct": [-100.48, 47.46], "gu": "USA"}, "id": "ND", "arcs": [[-904, 909, -906, 910]]}, {"type": "MultiPolygon", "properties": {"ct": [-155.52, 19.6], "gu": "USA"}, "id": "HI", "arcs": [[[499]], [[500]], [[501]], [[502]], [[503]]]}, {"type": "Polygon", "properties": {"ct": [-114.65, 44.39], "gu": "USA"}, "id": "ID", "arcs": [[-909, 911, 912, 913, 914, 915, -884]]}, {"type": "Polygon", "properties": {"ct": [-120.4, 47.38], "gu": "USA"}, "id": "WA", "arcs": [[-916, 916, 917, 136, 137, 138, 139, 918, 141, -885]]}, {"type": "Polygon", "properties": {"ct": [-111.66, 34.3], "gu": "USA"}, "id": "AZ", "arcs": [[919, 920, 888, 921, 922, 923]]}, {"type": "Polygon", "properties": {"ct": [-119.64, 37.26], "gu": "USA"}, "id": "CA", "arcs": [[-922, 889, 120, 121, 122, 924, 925, 125, 926, 927, 928, 129, 929, 930, 132, 931, 932]]}, {"type": "Polygon", "properties": {"ct": [-105.55, 39], "gu": "USA"}, "id": "CO", "arcs": [[933, 934, 935, 936, 937, 938]]}, {"type": "Polygon", "properties": {"ct": [-116.65, 39.35], "gu": "USA"}, "id": "NV", "arcs": [[-914, 939, -923, -933, 940]]}, {"type": "Polygon", "properties": {"ct": [-106.09, 34.42], "gu": "USA"}, "id": "NM", "arcs": [[-920, -936, 941, 942, 886, 943]]}, {"type": "Polygon", "properties": {"ct": [-120.54, 43.94], "gu": "USA"}, "id": "OR", "arcs": [[-915, -941, -932, 133, 134, 944, -917]]}, {"type": "Polygon", "properties": {"ct": [-111.67, 39.33], "gu": "USA"}, "id": "UT", "arcs": [[-913, 945, -937, -924, -940]]}, {"type": "Polygon", "properties": {"ct": [-107.55, 43.03], "gu": "USA"}, "id": "WY", "arcs": [[-908, 946, 947, -938, -946, -912]]}, {"type": "Polygon", "properties": {"ct": [-92.44, 34.92], "gu": "USA"}, "id": "AR", "arcs": [[948, 540, 541, 949, 950, 951, 952]]}, {"type": "Polygon", "properties": {"ct": [-93.51, 42.08], "gu": "USA"}, "id": "IA", "arcs": [[-902, 953, 954, 955, -533, 956]]}, {"type": "Polygon", "properties": {"ct": [-98.38, 38.48], "gu": "USA"}, "id": "KS", "arcs": [[-934, 957, 534, 958, 959]]}, {"type": "Polygon", "properties": {"ct": [-92.48, 38.38], "gu": "USA"}, "id": "MO", "arcs": [[-953, 960, -959, -535, -534, -956, 961, 536, 537, 538]]}, {"type": "Polygon", "properties": {"ct": [-99.82, 41.53], "gu": "USA"}, "id": "NE", "arcs": [[-939, -948, 962, 529, 963, 531, 532, 533, -958]]}, {"type": "Polygon", "properties": {"ct": [-97.5, 35.58], "gu": "USA"}, "id": "OK", "arcs": [[-935, -960, -961, -952, 964, -942]]}, {"type": "Polygon", "properties": {"ct": [-100.23, 44.45], "gu": "USA"}, "id": "SD", "arcs": [[-903, -957, -532, -964, -530, -963, -947, -907, -910]]}, {"type": "Polygon", "properties": {"ct": [-91.96, 31.05], "gu": "USA"}, "id": "LA", "arcs": [[-950, 542, 965, 966, 406, 407, 408, 409, 410, 411, 412, 413, 967]]}, {"type": "Polygon", "properties": {"ct": [-99.33, 31.46], "gu": "USA"}, "id": "TX", "arcs": [[-943, -965, -951, -968, 414, 415, 968, 417, 969, 419, 970, 648, -649, 648, 885]]}, {"type": "Polygon", "properties": {"ct": [-72.74, 41.61], "gu": "USA"}, "id": "CT", "arcs": [[971, 972, 349, 973, 974]]}, {"type": "Polygon", "properties": {"ct": [-71.74, 42.24], "gu": "USA"}, "id": "MA", "arcs": [[-972, 975, 976, 977, 341, 342, 978, 344, 979, 980, 347, 981]]}, {"type": "Polygon", "properties": {"ct": [-71.56, 43.69], "gu": "USA"}, "id": "NH", "arcs": [[-978, 982, -871, 983, 340]]}, {"type": "Polygon", "properties": {"ct": [-71.53, 41.68], "gu": "USA"}, "id": "RI", "arcs": [[-973, -982, 984]]}, {"type": "Polygon", "properties": {"ct": [-72.66, 44.07], "gu": "USA"}, "id": "VT", "arcs": [[-977, 985, -872, -983]]}, {"type": "Polygon", "properties": {"ct": [-86.83, 32.77], "gu": "USA"}, "id": "AL", "arcs": [[986, 987, 988, 403, 989]]}, {"type": "Polygon", "properties": {"ct": [-82.5, 28.62], "gu": "USA"}, "id": "FL", "arcs": [[-989, 990, 384, 385, 386, -823, -822, 389, 390, 391, 991, 393, 992, 395, 396, -821, 633, 399, 400, 401, 993]]}, {"type": "Polygon", "properties": {"ct": [-83.45, 32.65], "gu": "USA"}, "id": "GA", "arcs": [[-988, 994, 995, 996, 383, -991]]}, {"type": "Polygon", "properties": {"ct": [-89.66, 32.77], "gu": "USA"}, "id": "MS", "arcs": [[-542, 997, -990, 404, 405, -967, -966, -543]]}, {"type": "Polygon", "properties": {"ct": [-80.88, 33.9], "gu": "USA"}, "id": "SC", "arcs": [[-997, 998, 379, 380, 381, 382]]}, {"type": "Polygon", "properties": {"ct": [-89.2, 40.06], "gu": "USA"}, "id": "IL", "arcs": [[-955, 999, -864, 1000, 1001, -537, -962]]}, {"type": "Polygon", "properties": {"ct": [-86.28, 39.9], "gu": "USA"}, "id": "IN", "arcs": [[-1001, -863, 1002, 1003, 1004]]}, {"type": "Polygon", "properties": {"ct": [-85.28, 37.51], "gu": "USA"}, "id": "KY", "arcs": [[-538, -1002, -1005, 1005, 1006, 1007, 1008, 1009]]}, {"type": "Polygon", "properties": {"ct": [-79.25, 35.54], "gu": "USA"}, "id": "NC", "arcs": [[-996, 1010, 1011, -1012, 1012, 1013, 1014, 376, 377, 378, -999]]}, {"type": "Polygon", "properties": {"ct": [-82.79, 40.28], "gu": "USA"}, "id": "OH", "arcs": [[-1004, 1015, -844, 1016, 1017, -1006]]}, {"type": "Polygon", "properties": {"ct": [-86.32, 35.84], "gu": "USA"}, "id": "TN", "arcs": [[-541, -949, -539, -1010, -1009, 1008, 1018, -1012, -1011, -995, -987, -998]]}, {"type": "MultiPolygon", "properties": {"ct": [-78.86, 37.5], "gu": "USA"}, "id": "VA", "arcs": [[[-1008, 1019, 1020, 1021, 1022, 1023, 372, 373, -1013, 1011, -1019, -1009]], [[1024, 1025, 367]]]}, {"type": "Polygon", "properties": {"ct": [-90, 44.65], "gu": "USA"}, "id": "WI", "arcs": [[-901, -849, 1026, 1027, 1028, 1029, -865, -1000, -954]]}, {"type": "Polygon", "properties": {"ct": [-80.61, 38.64], "gu": "USA"}, "id": "WV", "arcs": [[-1007, -1018, 1030, 1031, -1020]]}, {"type": "Polygon", "properties": {"ct": [-75.5, 38.99], "gu": "USA"}, "id": "DE", "arcs": [[1032, 1033, 1034, 363, 364, 1035]]}, {"type": "Polygon", "properties": {"ct": [-77.02, 38.9], "gu": "USA"}, "id": "DC", "arcs": [[-1022, 1036]]}, {"type": "Polygon", "properties": {"ct": [-76.77, 39.03], "gu": "USA"}, "id": "MD", "arcs": [[-1026, 369, 370, -1023, -1037, -1021, -1032, 1037, -1033, 1038, 366]]}, {"type": "Polygon", "properties": {"ct": [-74.67, 40.21], "gu": "USA"}, "id": "NJ", "arcs": [[-1035, 1039, 1040, 356, 357, 358, 1041, 360, 1042, 1043]]}, {"type": "Polygon", "properties": {"ct": [-75.5, 42.93], "gu": "USA"}, "id": "NY", "arcs": [[-975, 1044, 351, 352, 1045, 1046, 355, -1041, 1047, 1048, -841, -875, 1049, -838, 1050, -873, -986, -976]]}, {"type": "Polygon", "properties": {"ct": [-77.81, 40.87], "gu": "USA"}, "id": "PA", "arcs": [[-1017, -843, 1051, -1048, -1040, -1034, -1038, -1031]]}, {"type": "Polygon", "properties": {"ct": [-69.22, 45.34], "gu": "USA"}, "id": "ME", "arcs": [[-984, -870, 336, 337, 338, 339]]}, {"type": "MultiPolygon", "properties": {"ct": [-84.61, 43.48], "gu": "USA"}, "id": "MI", "arcs": [[[-1029, -1028, 1027, -847, 879, -880, -879, -859, -867, 1052]], [[-1016, -1003, -862, -861, -877, -845]]]}, {"type": "MultiPolygon", "properties": {"ct": [-152.72, 64.44], "gu": "USA"}, "id": "AK", "arcs": [[[27]], [[26]], [[30]], [[146, 147, 148, 149, 150, 151, 152, 153, 154, 155, 156, 157, 158, 159, 160, 161, 162, 163, 164, 165, 166, 167, 168, 169, 170, 171, 172, 173, 174, 175, 176, 177, 178, 179, 180, 181, 182, 183, 184, 185, 186, 187, 188, 189, 190, 191, 192, 193, 194, 195, 196, 197, 198, 199, 200, 201, 202, 203, 204, 205, 206, 207, 208, -868, 143, 144, 145]]]}]}}, "arcs": [[[6398, 8820], [-40, -45], [-107, 8], [-90, 31], [39, 53], [107, 32], [64, -41], [27, -38]], [[6382, 9119], [-34, -3], [-139, 7], [-20, 33], [149, -2], [52, -21], [-8, -14]], [[6166, 9266], [88, -41], [-20, -43], [-109, -24], [-61, 27], [-31, 44], [-6, 49], [96, -5], [43, -7]], [[6803, 8751], [-120, 15], [-196, 38], [-26, 64], [-9, 58], [-74, 52], [-154, 14], [-85, 37], [27, 48], [153, -8], [82, -37], [146, 0], [64, -39], [-17, -44], [85, -27], [47, -28], [100, -5], [108, -10], [118, 26], [151, 10], [120, -8], [79, -45], [17, -48], [-46, -32], [-111, -25], [-94, 14], [-213, -18], [-152, -2]], [[5091, 9194], [104, -19], [-24, -35], [-138, -34], [-110, 38], [60, 38], [108, 12]], [[5113, 9270], [97, -24], [-91, -23], [-123, 1], [2, 16], [76, 36], [39, -6]], [[8423, 2178], [38, -10], [13, -24], [-19, -29], [-56, 0], [-43, -4], [-4, 50], [10, 18], [61, -1]], [[7587, 2175], [50, -10], [39, -29], [12, -32], [-52, -2], [-22, -20], [-42, 19], [-42, 43], [9, 27], [31, 8], [17, -4]], [[7239, 2728], [64, -9], [58, -1], [69, -40], [30, -43], [69, 13], [26, -27], [63, -73], [46, -53], [25, 1], [44, -24], [-6, -33], [55, -5], [56, -48], [-9, -27], [-49, -15], [-50, -6], [-51, 9], [-106, -11], [50, 65], [-31, 31], [-47, 8], [-26, 34], [-18, 67], [-41, -5], [-69, 32], [-23, 24], [-96, 19], [-26, 23], [28, 29], [-73, 6], [-53, -61], [-31, -2], [-10, -29], [-37, -12], [-32, 11], [39, 36], [16, 43], [34, 26], [38, 23], [56, 11], [18, 13]], [[9214, 6037], [-40, -74], [-49, -103], [49, 39], [49, -25], [-26, -41], [66, -32], [34, 29], [74, -37], [-23, -86], [52, 20], [10, -62], [23, -73], [-32, -104], [-33, -4], [-49, 22], [16, 96], [-20, 15], [-86, -102], [-45, 4], [53, 56], [-71, 28], [-80, -7], [-144, 4], [-11, 35], [46, 41], [-32, 32], [62, 71], [77, 187], [46, 67], [64, 41], [34, -5], [-14, -32]], [[7119, 7659], [81, -40], [85, -37], [7, -56], [54, 9], [53, -39], [-66, -37], [-115, 29], [-42, 53], [-73, -63], [-105, -61], [-26, 69], [-100, -11], [64, 58], [10, 92], [25, 108], [53, -9], [14, -52], [38, 18], [43, -31]], [[7498, 8511], [70, 47], [164, -60], [102, -56], [10, -51], [137, 27], [77, -75], [179, -47], [65, -47], [70, -110], [-136, -55], [174, -77], [118, -26], [106, -108], [117, -8], [-23, -82], [-130, -137], [-91, 50], [-117, 114], [-96, -15], [-9, -68], [78, -68], [100, -54], [31, -31], [48, -117], [-25, -84], [-94, 32], [-186, 94], [105, -102], [77, -71], [12, -41], [-201, 47], [-158, 69], [-90, 57], [26, 33], [-111, 60], [-108, 58], [1, -35], [-214, -18], [-62, 40], [48, 87], [140, 2], [152, 15], [-25, 42], [26, 58], [96, 115], [-20, 52], [-29, 40], [-113, 57], [-150, 40], [47, 30], [-78, 73], [-66, 7], [-58, 40], [-40, -35], [-134, -15], [-270, 27], [-156, 34], [-120, 18], [-62, 41], [77, 54], [-105, 0], [-23, 119], [57, 106], [76, 48], [191, 31], [-54, -76], [58, -73], [68, 94], [188, 49], [127, -122], [-11, -77], [147, 34]], [[6332, 8721], [155, -4], [141, -29], [-110, -105], [-89, -22], [-79, -88], [-85, 4], [-46, 103], [1, 59], [39, 50], [73, 32]], [[5893, 8553], [-82, 77], [88, 57], [88, -25], [132, 15], [20, -34], [-70, -57], [113, -50], [-14, -106], [-121, -46], [-72, 10], [-51, 45]], [[5924, 8439], [-182, 88], [0, 40]], [[5742, 8567], [151, -14]], [[5346, 8922], [66, 20], [78, -5], [13, -58], [-45, -56], [-251, -18], [-187, -51], [-113, -3], [-9, 39], [154, 52], [-335, -14], [-103, 21], [101, 115], [69, 33], [209, -40], [131, -70], [130, -9], [-106, 113], [68, 43], [76, -14], [25, -56], [29, -42]], [[4233, 8954], [125, 88], [152, 76], [114, -1], [102, 17], [-11, -91], [-57, -40], [-69, -6], [-138, -50], [-118, -18], [-100, 25]], [[4330, 8758], [106, -25], [189, -6], [72, -34]], [[4697, 8693], [80, -50], [-94, -29]], [[4683, 8614], [-181, -83], [-92, -82]], [[4410, 8449], [0, -52], [-195, -56]], [[4215, 8341], [-39, 51], [-171, 62]], [[4005, 8454], [32, 50]], [[4037, 8504], [51, 86]], [[4088, 8590], [64, 77], [-72, 72], [250, 19]], [[1002, 7103], [59, -10], [7, -45], [-45, -19], [-49, 22], [-45, 32], [73, 20]], [[1983, 6819], [49, -8], [31, -36], [-64, -56], [-74, -45], [-38, 30], [-11, 55], [67, 42], [40, 18]], [[3503, 6357], [71, 9], [-22, -133], [64, -95], [-30, 0], [-44, 54], [-28, 54], [-37, 37], [-14, 52], [5, 37], [35, -15]], [[4043, 5876], [37, -56], [73, -49], [31, -65], [-37, -16], [-122, 54], [-22, 41], [-66, 41], [-14, 34], [-76, 21], [-28, 64], [6, 27], [78, -25], [45, -18], [70, -13], [25, -40]], [[612, 7503], [46, -22], [46, 12], [60, -31], [74, -16], [-6, -13], [-56, -25], [-57, 26], [-28, 21], [-65, -7], [-18, 11], [4, 44]], [[5519, 9329], [145, -16], [200, -43], [57, -56], [29, -49], [-121, 13], [-122, 38], [-165, 4], [71, 35], [-89, 29], [-5, 45]], [[7603, 1020], [38, -4], [56, 82], [30, 13], [1, 39], [13, 99], [43, 55], [47, 2], [5, 25], [58, -10], [59, 59], [29, 26], [35, 57], [27, -7], [19, -31], [-14, -40], [-2, -27], [-44, -14], [24, -54], [-1, -61], [-32, -68], [28, -94], [32, 8], [16, 85], [-23, 41], [-3, 89], [92, 48], [-10, 56], [26, 37], [26, -83], [52, -2], [48, -65], [3, -39], [67, -1], [79, 12], [42, -53], [57, -15], [41, 37], [1, 30], [92, 7], [89, 2], [-63, -35], [25, -56], [59, -9], [56, -57], [12, -95], [39, 3], [29, -28], [49, -43], [45, -77], [2, -60], [28, -3], [40, -57], [29, -41], [89, -24], [8, 21], [60, 9], [80, -32], [25, -13], [54, -28], [79, -99], [12, -48]], [[9506, 489], [26, -4], [17, -56]], [[9549, 429], [42, -205], [40, -20], [2, -81], [-56, -97], [17, -26]], [[7564, 850], [30, -63], [11, -99], [-16, -31], [17, -108], [-14, -68], [27, -28], [-28, -62], [-31, -74], [-37, -8], [-17, -42], [3, -59], [-28, -9], [10, -37], [-50, -46], [-41, -26], [5, -48], [-16, -42]], [[7564, 850], [-25, 34], [-16, 63], [18, 32]], [[7541, 979], [-18, 8]], [[7523, 987], [-14, 39]], [[7509, 1026], [-37, 32]], [[7472, 1058], [-33, -7], [-15, -41]], [[7424, 1010], [-30, -30]], [[7394, 980], [-16, -4], [-7, -24]], [[7371, 952], [35, -64]], [[7406, 888], [-20, -15], [-11, -18], [-34, -6]], [[7341, 849], [-13, 71]], [[7328, 920], [-9, -20]], [[7319, 900], [-25, 7]], [[7294, 907], [-15, 47]], [[7279, 954], [-30, 8]], [[7249, 962], [-19, 13]], [[7230, 975], [-16, 0], [-16, 0]], [[7198, 975], [-2, -25], [-9, 18]], [[7187, 968], [-40, 26]], [[7147, 994], [-15, 24]], [[7132, 1018], [8, 21], [-2, 26], [-21, 28], [-29, 23]], [[7088, 1116], [-26, 15]], [[7062, 1131], [-4, 35]], [[7058, 1166], [-20, 21], [5, -35], [-15, -28]], [[7028, 1124], [-17, 33]], [[7011, 1157], [-24, 12], [-10, 23], [1, 36], [10, 37]], [[6988, 1265], [-11, 8], [-10, 9]], [[6967, 1282], [17, 22]], [[6984, 1304], [-26, 37]], [[6958, 1341], [-35, 48]], [[6923, 1389], [-16, 40]], [[6907, 1429], [-31, 37]], [[6876, 1466], [-37, 53]], [[6839, 1519], [8, 18], [12, -18]], [[6859, 1519], [6, 8]], [[6865, 1527], [-13, 37]], [[6852, 1564], [-11, 5], [-12, 5]], [[6829, 1574], [-8, -27]], [[6821, 1547], [-43, 2]], [[6778, 1549], [-26, 11]], [[6752, 1560], [-31, 23]], [[6721, 1583], [-41, 8], [-21, 25]], [[6659, 1616], [-38, 20], [-46, 2], [-34, 24], [-40, 48]], [[6501, 1710], [-84, 127], [-38, 38], [-60, 31]], [[6319, 1906], [-42, -9]], [[6277, 1897], [-59, -44], [-38, -12], [-52, 31]], [[6128, 1872], [-55, 23]], [[6073, 1895], [-70, 54], [-55, 16]], [[5948, 1965], [-84, 55]], [[5864, 2020], [-62, 56]], [[5802, 2076], [-19, 32]], [[5783, 2108], [-41, 7]], [[5742, 2115], [-76, 37]], [[5666, 2152], [-31, 53], [-79, 67]], [[5556, 2272], [-38, 74], [-17, 58], [25, 11], [-8, 34], [17, 30], [0, 41], [-25, 53]], [[5510, 2573], [-6, 47]], [[5504, 2620], [-25, 59]], [[5479, 2679], [-65, 117]], [[5414, 2796], [-75, 92]], [[5339, 2888], [-36, 73]], [[5303, 2961], [-64, 48]], [[5239, 3009], [-13, 29]], [[5226, 3038], [11, 73]], [[5237, 3111], [-38, 27]], [[5199, 3138], [-43, 57]], [[5156, 3195], [-19, 82], [-40, 10], [-43, 62], [-34, 57], [-4, 37], [-39, 89], [-27, 90], [1, 45], [-53, 46], [-25, -5], [-42, 33], [-12, -48]], [[4819, 3693], [12, -56]], [[4831, 3637], [7, -89], [26, -48], [55, -81], [12, -28], [11, -8], [10, -41], [13, 2], [15, -76], [23, -30], [15, -42], [47, -60], [25, -109], [22, -51], [20, -56]], [[5132, 2920], [4, -62]], [[5136, 2858], [36, -4], [30, -53]], [[5202, 2801], [26, -52]], [[5228, 2749], [-1, -22]], [[5227, 2727], [-32, -43]], [[5195, 2684], [-13, 1], [-19, 71], [-49, 67], [-53, 57], [-38, 30]], [[5023, 2910], [2, 86]], [[5025, 2996], [-11, 64], [-35, 36], [-51, 53], [-10, -15], [-18, 30], [-46, 29], [-44, 68]], [[4810, 3261], [6, 9]], [[4816, 3270], [30, -7]], [[4846, 3263], [28, 44]], [[4874, 3307], [2, 53], [-57, 84]], [[4819, 3444], [-43, 33]], [[4776, 3477], [-27, 73]], [[4749, 3550], [-28, 77]], [[4721, 3627], [-34, 94]], [[4687, 3721], [-30, 106]], [[4657, 3827], [-13, 60], [-48, 68], [-34, 14], [-8, 34], [-42, 6], [-26, 32]], [[4486, 4041], [-69, 11]], [[4417, 4052], [-19, 19]], [[4398, 4071], [-9, 65]], [[4389, 4136], [-72, 118]], [[4317, 4254], [-62, 163]], [[4255, 4417], [3, 28]], [[4258, 4445], [-33, 39]], [[4225, 4484], [-57, 98]], [[4168, 4582], [-10, 96]], [[4158, 4678], [-40, 64]], [[4118, 4742], [16, 98]], [[4134, 4840], [-2, 101]], [[4132, 4941], [-24, 90], [29, 111]], [[4137, 5142], [9, 106], [9, 107]], [[4155, 5355], [-13, 158]], [[4142, 5513], [-24, 101], [-21, 54]], [[4097, 5668], [9, 23]], [[4106, 5691], [107, -40]], [[4213, 5651], [39, -111], [19, 31]], [[4271, 5571], [-12, 97]], [[4259, 5668], [-25, 96]], [[4234, 5764], [-10, 0], [-144, 116], [-53, 51], [-134, 48], [-41, 105], [11, 72], [-95, 50], [-13, 95], [-90, 85], [-1, 61]], [[3664, 6447], [-41, 44]], [[3623, 6491], [-65, 37], [-21, 103]], [[3537, 6631], [-96, 95], [-40, 111]], [[3401, 6837], [-71, 8]], [[3330, 6845], [-118, 3]], [[3212, 6848], [-86, 34], [-154, 122]], [[2972, 7004], [-70, 22]], [[2902, 7026], [-130, 42]], [[2772, 7068], [-103, -10], [-145, 54], [-88, 50], [-82, -25], [15, -81]], [[2369, 7056], [-41, -8]], [[2328, 7048], [-86, -25], [-65, -39], [-82, -25], [-11, 69]], [[2084, 7028], [34, 115], [78, 37]], [[2196, 7180], [-20, 29], [-94, -65], [-51, -79]], [[2031, 7065], [-107, -83]], [[1924, 6982], [55, -57]], [[1979, 6925], [-70, -85]], [[1909, 6840], [-80, -49], [-74, -36], [-19, -52], [-115, -61]], [[1621, 6642], [-24, -55]], [[1597, 6587], [-86, -50]], [[1511, 6537], [-51, 9]], [[1460, 6546], [-69, -33]], [[1391, 6513], [-75, -40]], [[1316, 6473], [-62, -40], [-127, -33], [-12, 20], [81, 55], [73, 36], [79, 64], [92, 14]], [[1440, 6589], [36, 48]], [[1476, 6637], [103, 70]], [[1579, 6707], [16, 24]], [[1595, 6731], [55, 41], [13, 89], [37, 70], [-85, -36], [-24, 21], [-40, -43], [-48, 59], [-20, -42]], [[1483, 6890], [-28, 59]], [[1455, 6949], [-74, -47]], [[1381, 6902], [-45, 0]], [[1336, 6902], [-7, 70]], [[1329, 6972], [14, 43]], [[1343, 7015], [-48, 42]], [[1295, 7057], [-96, -23]], [[1199, 7034], [-63, 56], [-51, 28], [0, 66], [-57, 51]], [[1028, 7235], [29, 67]], [[1057, 7302], [60, 66], [26, 60], [60, 9], [51, -19]], [[1254, 7418], [60, 57]], [[1314, 7475], [54, -10]], [[1368, 7465], [56, 36]], [[1424, 7501], [-14, 54]], [[1410, 7555], [-41, 21], [55, 45], [-46, -1], [-78, -26]], [[1300, 7594], [-23, -26], [-58, 26]], [[1219, 7594], [-105, -13]], [[1114, 7581], [-109, 28], [-31, 48], [-93, 68]], [[881, 7725], [104, 49]], [[985, 7774], [165, 58]], [[1150, 7832], [61, 0]], [[1211, 7832], [-10, -59]], [[1201, 7773], [156, 5]], [[1357, 7778], [-60, 72]], [[1297, 7850], [-91, 45]], [[1206, 7895], [-53, 59]], [[1153, 7954], [-71, 50]], [[1082, 8004], [-102, 37]], [[980, 8041], [42, 62]], [[1022, 8103], [131, 4]], [[1153, 8107], [94, 53], [17, 58], [76, 56]], [[1340, 8274], [72, 13]], [[1412, 8287], [140, 52]], [[1552, 8339], [69, -8]], [[1621, 8331], [114, 63]], [[1735, 8394], [112, -25], [53, -53]], [[1900, 8316], [33, 23], [125, -7], [-4, -27], [113, -20], [76, 12], [156, -37], [142, -11], [57, -16], [99, 19]], [[2697, 8252], [112, -35]], [[2809, 8217], [81, -16]], [[2890, 8201], [138, -29], [116, -56], [78, -11]], [[3222, 8105], [65, 49]], [[3287, 8154], [89, 37]], [[3376, 8191], [110, -15]], [[3486, 8176], [111, 52]], [[3597, 8228], [122, 29]], [[3719, 8257], [50, -48], [56, 27]], [[3825, 8236], [16, 55]], [[3841, 8291], [51, -12], [126, -106], [98, 80], [10, -89], [91, 19]], [[4217, 8183], [28, 34]], [[4245, 8217], [90, -6], [113, -50], [174, -43]], [[4622, 8118], [101, -20]], [[4723, 8098], [73, 8]], [[4796, 8106], [100, -60]], [[4896, 8046], [-104, -58], [134, -25]], [[4926, 7963], [200, 13]], [[5126, 7976], [63, 21]], [[5189, 7997], [79, -71], [80, 60], [-75, 50]], [[5273, 8036], [47, 40]], [[5320, 8076], [91, 6], [59, 11], [60, -28], [74, -64], [83, 10], [131, -53], [115, 18], [108, -2], [-9, 73]], [[6032, 8047], [66, 20]], [[6098, 8067], [115, -40], [-1, -111]], [[6212, 7916], [47, 94]], [[6259, 8010], [60, -3]], [[6319, 8007], [33, 118]], [[6352, 8125], [-79, 73], [-86, 47]], [[6187, 8245], [6, 130]], [[6193, 8375], [87, 85]], [[6280, 8460], [98, -18]], [[6378, 8442], [75, -52]], [[6453, 8390], [100, -133]], [[6553, 8257], [-65, -58]], [[6488, 8199], [137, -24], [0, -120]], [[6625, 8055], [99, 92]], [[6724, 8147], [89, -75]], [[6813, 8072], [-22, -88]], [[6791, 7984], [71, -79]], [[6862, 7905], [78, 85]], [[6940, 7990], [54, 101]], [[6994, 8091], [4, 130]], [[6998, 8221], [105, -9]], [[7103, 8212], [109, -18], [100, -58]], [[7312, 8136], [4, -58]], [[7316, 8078], [-55, -63]], [[7261, 8015], [52, -63]], [[7313, 7952], [-9, -57]], [[7304, 7895], [-145, -83], [-103, -18]], [[7056, 7794], [-77, 36]], [[6979, 7830], [-22, -59]], [[6957, 7771], [-71, -100]], [[6886, 7671], [-22, -51]], [[6864, 7620], [-86, -80]], [[6778, 7540], [-106, -7]], [[6672, 7533], [-58, -50]], [[6614, 7483], [-5, -77]], [[6609, 7406], [-86, -14]], [[6523, 7392], [-91, -96]], [[6432, 7296], [-80, -132], [-29, -93], [-4, -137]], [[6319, 6934], [109, -19]], [[6428, 6915], [33, -110]], [[6461, 6805], [35, -90], [104, 24]], [[6600, 6739], [137, -51]], [[6737, 6688], [74, -45]], [[6811, 6643], [53, -56]], [[6864, 6587], [93, -32]], [[6957, 6555], [78, -49]], [[7035, 6506], [123, -7]], [[7158, 6499], [80, -12]], [[7238, 6487], [-12, -101]], [[7226, 6386], [23, -119]], [[7249, 6267], [54, -131], [110, -112]], [[7413, 6024], [57, 38]], [[7470, 6062], [40, 121]], [[7510, 6183], [-38, 186]], [[7472, 6369], [-53, 62]], [[7419, 6431], [119, 55]], [[7538, 6486], [84, 82]], [[7622, 6568], [41, 82]], [[7663, 6650], [-6, 79]], [[7657, 6729], [-51, 100]], [[7606, 6829], [-89, 88], [87, 124]], [[7604, 7041], [-32, 106], [-25, 184]], [[7547, 7331], [51, 27]], [[7598, 7358], [127, -32]], [[7725, 7326], [77, -11]], [[7802, 7315], [61, 31]], [[7863, 7346], [69, -40], [91, -68]], [[8023, 7238], [23, -46]], [[8046, 7192], [132, -9]], [[8178, 7183], [-3, -99]], [[8175, 7084], [25, -149]], [[8200, 6935], [68, -18]], [[8268, 6917], [53, -69]], [[8321, 6848], [108, 65]], [[8429, 6913], [71, 130]], [[8500, 7043], [49, 55], [57, -105], [97, -150], [82, -142], [-30, -73], [98, -67], [67, -67], [118, -30]], [[9038, 6464], [48, -38]], [[9086, 6426], [29, -100]], [[9115, 6326], [58, -15]], [[9173, 6311], [29, -45], [6, -132], [-54, -44]], [[9154, 6090], [-53, -41]], [[9101, 6049], [-122, -42]], [[8979, 6007], [-93, -97]], [[8886, 5910], [-126, -19], [-158, 25], [-111, 1]], [[8491, 5917], [-77, -8]], [[8414, 5909], [-62, -85]], [[8352, 5824], [-94, -52]], [[8258, 5772], [-107, -156]], [[8151, 5616], [-85, -108]], [[8066, 5508], [62, 19]], [[8128, 5527], [119, 155]], [[8247, 5682], [156, 98]], [[8403, 5780], [110, 12]], [[8513, 5792], [66, -58]], [[8579, 5734], [-70, -79]], [[8509, 5655], [24, -127]], [[8533, 5528], [24, -89], [96, -58], [122, 17]], [[8775, 5398], [75, 132]], [[8850, 5530], [5, -86]], [[8855, 5444], [48, -42], [-92, -77], [-164, -70]], [[8647, 5255], [-74, -48]], [[8573, 5207], [-82, -85]], [[8491, 5122], [-57, 9]], [[8434, 5131], [-2, 100]], [[8432, 5231], [128, 97]], [[8560, 5328], [-118, -4]], [[8442, 5324], [-83, -14]], [[8359, 5310], [13, -39]], [[8372, 5271], [-79, -57]], [[8293, 5214], [-76, -40], [-78, -35]], [[8139, 5139], [-39, -70]], [[8100, 5069], [-13, -27]], [[8087, 5042], [-1, -62]], [[8086, 4980], [25, -62], [30, -3]], [[8141, 4915], [-7, 43]], [[8134, 4958], [22, -26]], [[8156, 4932], [-6, -34]], [[8150, 4898], [-50, -19]], [[8100, 4879], [-36, 2]], [[8064, 4881], [-54, -20]], [[8010, 4861], [-33, -6], [-43, -6]], [[7934, 4849], [-61, -34]], [[7873, 4815], [108, 22]], [[7981, 4837], [22, -22]], [[8003, 4815], [-103, -35]], [[7900, 4780], [-48, -1]], [[7852, 4779], [3, 15]], [[7855, 4794], [-23, -33]], [[7832, 4761], [22, -5]], [[7854, 4756], [-16, -85]], [[7838, 4671], [-54, -90]], [[7784, 4581], [-6, 30]], [[7778, 4611], [-16, 6]], [[7762, 4617], [-24, 29]], [[7738, 4646], [15, -63]], [[7753, 4583], [19, -21]], [[7772, 4562], [1, -44]], [[7773, 4518], [-24, -46]], [[7749, 4472], [-42, -94], [-6, 5]], [[7701, 4383], [23, 80]], [[7724, 4463], [-38, 45], [-9, 97], [-14, -50], [16, -75]], [[7679, 4480], [-49, 18]], [[7630, 4498], [51, -38]], [[7681, 4460], [3, -111]], [[7684, 4349], [21, -9], [8, -40]], [[7713, 4300], [10, -118]], [[7723, 4182], [-47, -87]], [[7676, 4095], [-77, -35]], [[7599, 4060], [-48, -69]], [[7551, 3991], [-37, -8]], [[7514, 3983], [-38, -43]], [[7476, 3940], [-10, -39]], [[7466, 3901], [-82, -77]], [[7384, 3824], [-41, -56]], [[7343, 3768], [-35, -69], [-12, -84]], [[7296, 3615], [13, -82]], [[7309, 3533], [25, -100]], [[7334, 3433], [33, -84]], [[7367, 3349], [0, -51]], [[7367, 3298], [35, -136]], [[7402, 3162], [-2, -79], [-3, -46]], [[7397, 3037], [-19, -72]], [[7378, 2965], [-22, -15], [-36, 15]], [[7320, 2965], [-12, 51]], [[7308, 3016], [-28, 27]], [[7280, 3043], [-39, 101]], [[7241, 3144], [-35, 90]], [[7206, 3234], [-11, 46]], [[7195, 3280], [15, 78]], [[7210, 3358], [-20, 65]], [[7190, 3423], [-58, 99], [-29, 18], [-75, -54]], [[7028, 3486], [-13, 6]], [[7015, 3492], [-36, 55], [-46, 29]], [[6933, 3576], [-84, -15]], [[6849, 3561], [-66, 13]], [[6783, 3574], [-56, -8]], [[6727, 3566], [-31, -18]], [[6696, 3548], [13, -31]], [[6709, 3517], [-1, -48], [16, -23]], [[6724, 3446], [-14, -16]], [[6710, 3430], [-28, 18]], [[6682, 3448], [-27, -23]], [[6655, 3425], [-54, 4], [-55, 62], [-65, -15]], [[6481, 3476], [-54, 28]], [[6427, 3504], [-46, -9]], [[6381, 3495], [-62, -27], [-68, -87]], [[6251, 3381], [-73, -51]], [[6178, 3330], [-41, -56]], [[6137, 3274], [-17, -53]], [[6120, 3221], [-1, -81]], [[6119, 3140], [4, -57]], [[6123, 3083], [14, -40]], [[6137, 3043], [-29, -103], [-13, -85], [-5, -157]], [[6090, 2698], [-7, -58]], [[6083, 2640], [13, -64], [23, -57]], [[6119, 2519], [15, -92]], [[6134, 2427], [49, -87], [17, -67], [29, -58]], [[6229, 2215], [79, -31]], [[6308, 2184], [30, -50]], [[6338, 2134], [65, 33]], [[6403, 2167], [57, 12], [55, 21], [47, 20]], [[6562, 2220], [47, 48]], [[6609, 2268], [17, 69]], [[6626, 2337], [7, 99]], [[6633, 2436], [12, 34]], [[6645, 2470], [51, 31]], [[6696, 2501], [78, 27]], [[6774, 2528], [65, -4]], [[6839, 2524], [45, 10]], [[6884, 2534], [18, -25]], [[6902, 2509], [-2, -56]], [[6900, 2453], [-40, -70]], [[6860, 2383], [-18, -72]], [[6842, 2311], [14, -20], [-11, -51]], [[6845, 2240], [-19, -92]], [[6826, 2148], [-19, 30], [-15, -2]], [[6792, 2176], [0, -17]], [[6792, 2159], [14, -1]], [[6806, 2158], [-1, -32], [-12, -50], [6, -19]], [[6799, 2057], [-7, -42]], [[6792, 2015], [4, -11]], [[6796, 2004], [-8, -59]], [[6788, 1945], [-15, -32], [-13, -3], [-15, -41]], [[6745, 1869], [24, -21], [7, 17]], [[6776, 1865], [24, -16], [5, -3]], [[6805, 1846], [16, 20], [22, 2], [6, -10]], [[6849, 1858], [12, 6]], [[6861, 1864], [34, -10]], [[6895, 1854], [35, 3], [23, 13], [9, 13], [24, -6]], [[6986, 1877], [17, -8]], [[7003, 1869], [20, 2]], [[7023, 1871], [15, 11]], [[7038, 1882], [33, -17]], [[7071, 1865], [12, -2]], [[7083, 1863], [23, -22]], [[7106, 1841], [21, -27]], [[7127, 1814], [27, -18]], [[7154, 1796], [20, -32]], [[7174, 1764], [-7, -11]], [[7167, 1753], [-4, -27]], [[7163, 1726], [8, -43]], [[7171, 1683], [-17, -40]], [[7154, 1643], [-8, -47], [-2, -52], [4, -30]], [[7148, 1514], [1, -53]], [[7149, 1461], [-11, -12]], [[7138, 1449], [-7, -50]], [[7131, 1399], [5, -31]], [[7136, 1368], [-15, -30]], [[7121, 1338], [4, -32], [11, -19]], [[7136, 1287], [5, -18], [14, -46]], [[7155, 1223], [28, -47], [35, -51]], [[7218, 1125], [27, -42]], [[7245, 1083], [-2, -25]], [[7243, 1058], [30, -5], [7, 9], [20, -28], [37, 8], [31, 30]], [[7368, 1072], [45, 24]], [[7413, 1096], [25, 35]], [[7438, 1131], [41, -7]], [[7479, 1124], [-3, -12]], [[7476, 1112], [42, -4]], [[7518, 1108], [33, -20]], [[7551, 1088], [24, -36]], [[7575, 1052], [28, -32]], [[8021, 2319], [9, 20]], [[8030, 2339], [15, 3], [43, -3]], [[8088, 2339], [43, -31], [20, 3], [13, -41], [41, 2], [-2, -35], [33, -4], [36, -44], [-28, -47], [-35, 25], [-34, -5], [-24, 6], [-14, -22], [-28, -7], [-11, 29], [-25, -17], [-29, -81], [-19, 19], [-4, 34]], [[8021, 2123], [-49, 20], [-35, -8], [-46, 8], [-34, -22], [-40, 37], [7, 38], [68, -17], [56, -9], [26, 26], [-33, 51], [0, 45], [-47, 18], [17, 33], [45, -5], [65, -19]], [[181, 8023], [-181, 90]], [[181, 8023], [195, -117], [-7, -73], [50, -30], [-17, 86], [201, -18], [145, -110], [-73, -51], [-122, -12], [-2, -115], [-29, -25], [-70, 4], [-56, 41], [-99, 34], [-16, 51], [-75, 19], [-85, -15], [-40, 41], [16, 44], [-88, -28], [33, -55], [-42, -50]], [[0, 8413], [9, 5], [63, -1], [107, -33], [-6, -16], [-76, -28], [-97, -8]], [[8764, 1266], [42, 15], [16, -4], [-3, -88], [-62, -13], [-13, 11], [21, 32], [-1, 47]], [[1822, 2362], [13, -10], [12, -16], [19, -41], [-2, -7], [-29, -25], [-23, -18], [-11, -20], [-19, 17], [2, 33], [-12, 43], [4, 13], [13, 19], [-5, 23], [4, 11], [6, -2], [28, -20]], [[1778, 2443], [-6, -15], [-25, -8], [-13, 25], [-8, 9], [-1, 8], [7, 10], [27, -11], [19, -18]], [[1721, 2491], [-2, -13], [-40, 4], [6, 14], [36, -5]], [[1628, 2555], [6, -8], [21, -39], [-4, -7], [-5, 2], [-26, 4], [-9, 27], [-3, 4], [20, 17]], [[1528, 2613], [2, -27], [-9, -12], [-25, 22], [4, 8], [11, 12], [17, -3]], [[7541, 2966], [22, -5], [26, -98], [0, -68], [-18, -6], [-19, 68], [-27, 34], [16, 75]], [[7482, 3151], [35, 10], [49, -4], [2, -30], [-81, -19], [-5, 43]], [[7570, 3181], [59, -53], [-13, -84], [-14, 15], [2, 62], [-33, 46], [-1, 14]], [[8591, 5533], [26, -57], [53, -16], [69, 3], [-36, -48], [-28, -8], [-94, 50], [-18, 40], [28, 36]], [[8553, 5867], [26, 10], [97, -30], [76, -49], [2, -22], [-36, -2], [-96, 37], [-69, 56]], [[7383, 7303], [29, 36], [30, -3], [19, -24], [-29, -62], [-33, 10], [-19, 35], [3, 8]], [[7111, 7347], [55, 54], [102, -1], [-2, -23], [-87, -65], [-52, 3], [-16, 32]], [[7761, 7934], [-48, -35], [-83, -6], [-19, 58], [32, 66], [68, 16], [58, -33], [0, -50], [-8, -16]], [[6180, 8197], [68, -67]], [[6248, 8130], [-46, -42], [-100, 36], [-61, -13], [-101, 53], [66, 36], [51, 51], [79, -33], [44, -21]], [[5442, 8596], [83, -47], [47, -115], [23, -83], [124, -58], [134, -56], [-8, -51], [-122, -10], [47, -45], [-25, -43], [-134, 18], [-127, 32], [-86, -7], [-139, -40]], [[5259, 8091], [-188, -18], [-132, -11]], [[4939, 8062], [-40, 56], [-101, 32], [-66, -13], [-91, 93], [49, 12], [115, 20], [104, -5], [97, 21], [-144, 27], [-158, -9], [-105, 2], [-39, 43], [172, 47], [-114, -1], [-130, 31], [62, 88], [52, 47], [198, 71], [76, -22], [-37, -55], [165, 35], [103, -59], [84, 60], [67, -39], [61, -115], [37, 48], [-53, 121], [66, 17], [73, -19]], [[7423, 8564], [-81, 63], [3, 42], [36, 8], [169, -13], [128, -65], [6, -32], [-78, 3], [-80, 3], [-81, -16], [-22, 7]], [[6031, 9010], [5, 15], [57, -55], [2, -60], [-33, -87], [-123, -12], [-79, 18], [1, 69], [-121, -9], [-5, 91], [80, -4], [112, 40], [104, -6]], [[6220, 9482], [52, 36], [76, 8], [-33, 27], [173, 6], [94, -63]], [[6582, 9496], [246, -48]], [[6828, 9448], [59, -77], [89, -38], [-102, -35], [-137, -89], [-131, -8], [-153, 15], [-80, 48], [1, 43], [59, 31], [-135, -1], [-82, 39], [-47, 53], [51, 53]], [[6548, 9634], [111, 22], [86, 4], [145, 19], [109, 44], [92, -6], [80, -33], [56, 63], [98, 19], [133, 13], [226, 5], [40, -13], [214, 20], [160, -7], [160, -8], [198, -9], [159, -15], [136, -32], [-3, -31], [-181, -51], [-179, -24], [-67, -27], [161, 1], [-175, -71], [-120, -34], [-127, -96], [-153, -19], [-47, -24], [-224, -13], [102, -15], [-51, -21], [61, -58], [-71, -40], [-114, -33], [-35, -46], [-104, -35], [11, -27], [126, 5], [2, -29], [-198, -71], [-193, 33], [-218, -19], [-110, 15], [-140, 6], [-10, 56], [137, 27], [-36, 85], [45, 8], [198, -51], [-101, 76], [-120, 22], [60, 46], [131, 28], [21, 41], [-104, 46], [-32, 60], [203, -5], [58, -12], [116, 42], [-167, 14], [-259, -7], [-131, 39], [-62, 48], [-86, 35], [-17, 40]], [[9868, 9720], [131, 37]], [[9999, 7076], [-94, 82], [-148, 1], [-72, 65], [-49, 114], [-128, 147], [-38, 76], [-10, 106], [-103, 109], [27, 86], [-49, 42], [73, 137], [111, 44], [30, 49], [15, 92], [-84, -42], [-41, -17], [-66, -17], [-91, 39], [-5, 80], [29, 62], [69, 2], [151, -32], [-128, 75], [-66, 40], [-74, -16], [-61, 29], [82, 110], [-45, 44], [-59, 81], [-89, 125], [-94, 45], [1, 49], [-199, 69], [-157, 9], [-198, -5], [-181, -8], [-86, 37], [-129, 74], [195, 37], [149, 6], [-317, 31], [-167, 48], [10, 45], [281, 57], [271, 57], [29, 42], [-200, 43], [64, 47], [257, 82], [108, 12], [-31, 53], [175, 31], [228, 19], [227, 1], [81, -37], [197, 65], [177, -44], [104, -10], [118, -29]], [[9999, 9619], [-141, 51], [10, 50]], [[5436, 8658], [100, 5], [56, -26], [-65, -78], [-116, 82], [25, 17]], [[4086, 6677], [47, -61], [23, -18], [26, -5], [60, 6], [37, -9], [19, 0], [45, 20], [83, -5], [54, 12], [24, -1], [38, -27], [24, -4], [42, 9], [21, 12], [22, 30], [2, 16], [-6, 36], [9, 50], [2, 44], [5, 33], [14, 16], [13, 7], [10, 19], [16, 9], [25, 6], [75, -1], [27, 8], [9, 4], [21, 9], [21, 9], [12, 3], [13, 4], [21, 10], [11, 5], [38, 29], [24, 3], [19, -5], [10, -13], [38, 4], [-2, 63], [-10, 36], [-60, 44], [-8, 11], [1, 16], [-7, 11], [-43, 36], [-9, 38], [-14, 8], [-9, 0], [-9, 1], [-10, 1], [-22, 2], [-22, 3], [-10, 1], [-130, -26], [-27, 4], [-72, 22], [-36, 13], [-68, -14], [-52, 4], [-41, 15], [-19, 13], [-17, 25], [-58, 22], [-106, 32], [-17, 12], [-3, 19], [5, 42], [-7, 23], [-47, 80], [-28, 31], [-12, 26], [-20, 24], [-19, 31], [-21, 13], [-65, 29], [-55, 29], [-82, 37], [-47, 13], [-18, 17], [-5, 19], [10, 35], [-13, 16], [-56, 32], [-20, 18], [-22, 39], [-14, 11], [-17, 5], [-33, 12], [-107, -19], [-27, 0], [-26, 8], [-34, 19], [-27, 26], [-16, 36], [0, 16], [18, 31], [-6, 30], [-44, 31], [-35, 48]], [[5130, 5261], [-14, -5], [-13, 9], [-8, 7], [-7, 6], [-9, 4], [-9, 5], [-7, 16], [-1, 27], [15, 84], [0, 23], [-2, 9], [-3, 8], [-3, 9], [-7, 17], [-3, 9], [-6, 9], [-7, 9], [-4, 15], [5, 20], [16, 21], [35, 35], [49, 38], [21, 6], [30, -18], [65, -5], [9, -3], [19, -7], [19, -6], [8, -3], [7, -2], [12, -3], [6, -2], [72, 24], [10, 8], [17, 16], [9, 8], [13, 1], [30, 3], [36, 4], [31, 3], [13, 2], [72, -12], [39, 7], [26, -4], [56, -48], [25, -11], [8, -2], [14, -2], [8, -2], [3, -9], [3, -9], [23, -21], [4, -14], [8, -26], [5, -14], [6, -13], [6, -12], [3, -42], [16, -93], [-1, -31], [-7, -21], [-13, -19], [1, -13], [5, -12], [6, -6], [6, -6], [9, -7], [17, -14], [9, -7], [22, -3], [5, -6], [9, -10], [5, -5], [-1, -9], [-1, -17], [-1, -9], [2, -12], [10, -10], [20, -22], [20, -22], [8, -12], [-1, -3], [1, -1]], [[6029, 5058], [20, -15], [27, -9], [6, 5], [18, 0], [26, 1], [19, -15], [21, -10]], [[6166, 5015], [3, -9], [6, -6]], [[6175, 5000], [13, -2]], [[6188, 4998], [3, -12], [5, -19], [0, -10], [13, -22], [5, -19], [0, -27], [6, -4], [5, -14], [7, -34], [2, -21], [-2, -21], [5, -21]], [[6237, 4774], [1, -10], [13, -18], [11, -15], [10, -25]], [[6272, 4706], [17, -16], [10, 0], [2, -17], [-10, -22], [5, -11], [9, -25], [20, -11]], [[6325, 4604], [105, 21], [14, -5], [11, -28], [23, -19], [18, -25], [18, -6], [44, 7], [42, -4], [34, 19], [6, 0], [10, 1], [5, -1], [-3, -14], [-2, -11]], [[6650, 4539], [-7, -17], [-4, -21], [10, -18], [15, -16], [8, -1], [20, -25], [7, -4], [6, -28], [-3, -18], [9, -29], [8, 3], [13, -18]], [[6732, 4347], [-2, -12], [2, -18], [-12, -10], [-17, -13]], [[6703, 4294], [-2, -11], [-4, -17], [-6, -28]], [[6691, 4238], [-2, 0], [1, -5], [0, -5]], [[6690, 4228], [-7, -17], [-13, -11], [-3, -20], [-12, -15], [1, -34], [-8, -11]], [[6648, 4120], [-2, -10], [-13, -8], [0, -17], [-10, -32], [-9, -7], [-13, -16], [-8, -24], [-16, -41], [-2, -28], [9, -31], [-4, -23]], [[6580, 3883], [6, -7], [-7, -17], [11, -24], [-3, -14], [9, -21], [-10, -12], [-4, -22], [-14, -18], [-7, -25], [-6, -28], [-9, -13], [3, -30]], [[6549, 3652], [4, -28], [14, -19], [16, -44], [10, -16], [15, -10], [55, -18], [7, -11], [4, -18], [36, -58]], [[8030, 2339], [58, 0]], [[6180, 8197], [23, -23], [45, -44]], [[6625, 8055], [73, 68], [26, 24]], [[6813, 8072], [-12, -45], [-10, -43]], [[6862, 7905], [30, 33], [48, 52]], [[6940, 7990], [20, 38], [34, 63]], [[6998, 8221], [23, -2], [82, -7]], [[7312, 8136], [3, -48], [1, -10]], [[7316, 8078], [-23, -27], [-32, -36]], [[7313, 7952], [-5, -33], [-4, -24]], [[7056, 7794], [-33, 15], [-44, 21]], [[6979, 7830], [-11, -31], [-11, -28]], [[6886, 7671], [-9, -22], [-13, -29]], [[6778, 7540], [-96, -7], [-10, 0]], [[6672, 7533], [-8, -8], [-50, -42]], [[6614, 7483], [-3, -44], [-2, -33]], [[6523, 7392], [-9, -9], [-82, -87]], [[6319, 6934], [94, -16], [15, -3]], [[6428, 6915], [11, -36], [22, -74]], [[6600, 6739], [87, -33], [50, -18]], [[6737, 6688], [20, -12], [54, -33]], [[6811, 6643], [24, -25], [29, -31]], [[6864, 6587], [53, -18], [40, -14]], [[7035, 6506], [103, -6], [20, -1]], [[7238, 6487], [-6, -53], [-6, -48]], [[7226, 6386], [6, -28], [17, -91]], [[7413, 6024], [34, 22], [23, 16]], [[7470, 6062], [28, 84], [12, 37]], [[7510, 6183], [-24, 114], [-14, 72]], [[7472, 6369], [-49, 57], [-4, 5]], [[7538, 6486], [1, 1], [83, 81]], [[7663, 6650], [0, 3], [-6, 76]], [[7606, 6829], [-89, 89], [87, 123]], [[7547, 7331], [17, 9], [34, 18]], [[7598, 7358], [37, -9], [90, -23]], [[7725, 7326], [64, -9], [13, -2]], [[7802, 7315], [6, 3], [55, 28]], [[8023, 7238], [9, -19], [14, -27]], [[8046, 7192], [56, -4], [76, -5]], [[8175, 7084], [16, -96], [9, -53]], [[8200, 6935], [49, -13], [19, -5]], [[8321, 6848], [60, 36], [48, 29]], [[8429, 6913], [39, 73], [32, 57]], [[9038, 6464], [4, -4], [44, -34]], [[9086, 6426], [14, -49], [15, -51]], [[9115, 6326], [32, -8], [26, -7]], [[9154, 6090], [-1, 0], [-52, -41]], [[8979, 6007], [-26, -27], [-67, -70]], [[8491, 5917], [-35, -4], [-42, -4]], [[8352, 5824], [-39, -21], [-55, -31]], [[8258, 5772], [-94, -136], [-13, -20]], [[8066, 5508], [23, 7], [39, 12]], [[8128, 5527], [2, 3], [117, 152]], [[8513, 5792], [64, -57], [2, -1]], [[8509, 5655], [3, -14], [21, -113]], [[8775, 5398], [43, 75], [32, 57]], [[8850, 5530], [4, -80], [1, -6]], [[8647, 5255], [-65, -42], [-9, -6]], [[8573, 5207], [-13, -13], [-69, -72]], [[8434, 5131], [0, 19], [-2, 81]], [[8432, 5231], [57, 43], [71, 54]], [[8560, 5328], [-81, -3], [-37, -1]], [[8372, 5271], [-21, -15], [-58, -42]], [[8087, 5042], [0, -25], [-1, -37]], [[8150, 4898], [-23, -9], [-27, -10]], [[7934, 4849], [-57, -32], [-4, -2]], [[7873, 4815], [31, 6], [77, 16]], [[7852, 4779], [1, 4], [2, 11]], [[7855, 4794], [-22, -32], [-1, -1]], [[7854, 4756], [-7, -35], [-9, -50]], [[7838, 4671], [-26, -43], [-28, -47]], [[7762, 4617], [-9, 12], [-15, 17]], [[7738, 4646], [9, -37], [6, -26]], [[7753, 4583], [18, -21], [2, -44]], [[7679, 4480], [-47, 18]], [[7632, 4498], [-2, 0]], [[7676, 4095], [-46, -21], [-31, -14]], [[7599, 4060], [-13, -19], [-35, -50]], [[7514, 3983], [-8, -9], [-30, -34]], [[7466, 3901], [-32, -30], [-50, -47]], [[7384, 3824], [-32, -44], [-9, -12]], [[7296, 3615], [10, -62], [3, -20]], [[7309, 3533], [4, -13], [21, -87]], [[7334, 3433], [29, -73], [4, -11]], [[7367, 3349], [0, -2], [0, -48]], [[7367, 3299], [0, -1]], [[7397, 3037], [-16, -60], [-3, -12]], [[7206, 3234], [-10, 44], [-1, 2]], [[7210, 3358], [0, 1]], [[7210, 3359], [-20, 64]], [[7028, 3486], [-12, 6], [-1, 0]], [[6849, 3561], [-63, 13], [-3, 0]], [[6783, 3574], [-6, -1], [-50, -7]], [[6727, 3566], [-32, -16]], [[6695, 3550], [2, -5], [12, -28]], [[6724, 3446], [-5, -6], [-9, -10]], [[6710, 3430], [-24, 15], [-4, 3]], [[6682, 3448], [-1, -2], [-26, -21]], [[6481, 3476], [0, 1], [-54, 27]], [[6251, 3381], [-70, -48], [-3, -3]], [[6178, 3330], [-9, -12], [-32, -44]], [[6120, 3221], [0, -26], [-1, -55]], [[6119, 3140], [2, -28], [2, -29]], [[6123, 3083], [1, -2], [13, -38]], [[6137, 3043], [-29, -3]], [[6090, 2698], [-4, -29], [-3, -29]], [[6119, 2519], [0, -5], [15, -87]], [[6229, 2215], [68, -27], [11, -4]], [[6308, 2184], [10, -18], [20, -32]], [[6338, 2134], [48, 25], [17, 8]], [[6562, 2220], [8, 9], [39, 39]], [[6626, 2337], [6, 98], [1, 1]], [[6645, 2470], [38, 24], [13, 7]], [[6696, 2501], [12, 4], [66, 23]], [[6839, 2524], [33, 8], [12, 2]], [[6902, 2509], [-1, -33], [-1, -23]], [[6860, 2383], [-5, -21], [-13, -51]], [[6845, 2240], [-12, -57], [-7, -35]], [[6792, 2159], [1, 0], [13, -1]], [[6799, 2057], [-3, -18], [-4, -24]], [[6796, 2004], [-6, -45], [-2, -14]], [[6776, 1865], [21, -15]], [[6797, 1850], [8, -4]], [[6849, 1858], [3, 2], [9, 4]], [[6861, 1864], [20, -6], [14, -4]], [[6986, 1877], [6, -3], [11, -5]], [[7003, 1869], [7, 1], [13, 1]], [[7023, 1871], [12, 9], [3, 2]], [[7038, 1882], [2, -2], [31, -15]], [[7071, 1865], [6, -1], [6, -1]], [[7083, 1863], [8, -8], [15, -14]], [[7106, 1841], [20, -25], [1, -2]], [[7154, 1796], [7, -12], [13, -20]], [[7167, 1753], [-1, -6], [-3, -21]], [[7171, 1683], [-10, -23], [-7, -17]], [[7148, 1514], [1, -50], [0, -3]], [[7138, 1449], [-4, -28], [-3, -22]], [[7131, 1399], [3, -18], [2, -13]], [[7136, 1368], [-7, -14], [-8, -16]], [[7136, 1287], [19, -64]], [[7245, 1083], [-2, -21], [0, -4]], [[7368, 1072], [7, 4], [38, 20]], [[7413, 1096], [12, 16], [13, 19]], [[7438, 1131], [36, -6], [5, -1]], [[7479, 1124], [-1, -6], [-2, -6]], [[7518, 1108], [1, -1], [32, -19]], [[7551, 1088], [7, -11], [17, -25]], [[7603, 1020], [-9, -17], [17, -70], [-14, -34], [-24, 8], [-9, -57]], [[7541, 979], [-16, 7], [-2, 1]], [[7523, 987], [-8, 20], [-6, 19]], [[7509, 1026], [-28, 24], [-9, 8]], [[7424, 1010], [-29, -29], [-1, -1]], [[7371, 952], [18, -32], [17, -32]], [[7341, 849], [-6, 35], [-7, 36]], [[7319, 900], [-3, 0], [-22, 7]], [[7294, 907], [-11, 34], [-4, 13]], [[7279, 954], [-14, 4], [-16, 4]], [[7230, 975], [-32, 0]], [[7187, 968], [-33, 21], [-7, 5]], [[7147, 994], [-2, 3], [-13, 21]], [[7088, 1116], [-20, 12], [-6, 3]], [[7062, 1131], [-3, 24], [-1, 11]], [[7028, 1124], [-11, 22], [-6, 11]], [[6988, 1265], [-1, 0], [1, 0]], [[6958, 1341], [-31, 43], [-4, 5]], [[6923, 1389], [-2, 5], [-14, 35]], [[6876, 1466], [-30, 43], [-7, 10]], [[6859, 1519], [4, 5], [2, 3]], [[6821, 1547], [-24, 1], [-19, 1]], [[6752, 1560], [-3, 2], [-28, 21]], [[6319, 1906], [-34, -7], [-8, -2]], [[6128, 1872], [-50, 21], [-5, 2]], [[5948, 1965], [-83, 54], [-1, 1]], [[5864, 2020], [-4, 4], [-58, 52]], [[5802, 2076], [-18, 31], [-1, 1]], [[5783, 2108], [-3, 0], [-38, 7]], [[5666, 2152], [-31, 54], [-79, 66]], [[5510, 2573], [-6, 46], [0, 1]], [[5504, 2620], [-9, 20], [-16, 39]], [[5479, 2679], [-11, 19], [-54, 98]], [[5339, 2888], [-19, 38], [-17, 35]], [[5303, 2961], [-11, 9], [-53, 39]], [[5226, 3038], [1, 8], [10, 65]], [[5199, 3138], [-42, 56], [-1, 1]], [[4819, 3693], [1, -2], [11, -54]], [[5132, 2920], [1, -7], [3, -55]], [[5202, 2801], [26, -51], [0, -1]], [[5227, 2727], [-31, -42], [-1, -1]], [[5023, 2910], [2, 72], [0, 14]], [[4810, 3261], [5, 9], [1, 0]], [[4846, 3263], [8, 12], [20, 32]], [[4819, 3444], [-3, 3], [-40, 30]], [[4776, 3477], [-17, 47], [-10, 26]], [[4721, 3627], [-31, 86], [-3, 8]], [[4486, 4041], [-40, 6], [-29, 5]], [[4417, 4052], [-16, 16], [-3, 3]], [[4137, 5142], [15, 175], [3, 38]], [[4097, 5668], [7, 18], [2, 5]], [[4106, 5691], [14, -5], [93, -35]], [[4271, 5571], [-3, 20], [-9, 77]], [[3623, 6491], [-5, 3], [-60, 35]], [[3558, 6529], [-21, 102]], [[3401, 6837], [-41, 5], [-30, 3]], [[3330, 6845], [-36, 1], [-82, 2]], [[2972, 7004], [-60, 19], [-10, 3]], [[2902, 7026], [-53, 17], [-77, 25]], [[2369, 7056], [-14, -3], [-27, -5]], [[2031, 7065], [-58, -45], [-49, -38]], [[1924, 6982], [39, -41], [16, -16]], [[1979, 6925], [-29, -34], [-41, -51]], [[1621, 6642], [-5, -12], [-19, -43]], [[1597, 6587], [-82, -48], [-4, -2]], [[1460, 6546], [-41, -20], [-28, -13]], [[1391, 6513], [-26, -14], [-49, -26]], [[1440, 6589], [27, 36], [9, 12]], [[1579, 6707], [1, 2], [15, 22]], [[1483, 6890], [-25, 52], [-3, 7]], [[1455, 6949], [-14, -9], [-60, -38]], [[1336, 6902], [-6, 63], [-1, 7]], [[1343, 7015], [-46, 40], [-2, 2]], [[1295, 7057], [-56, -13], [-40, -10]], [[1028, 7235], [22, 52], [7, 15]], [[1254, 7418], [16, 15], [44, 42]], [[1314, 7475], [27, -5], [27, -5]], [[1424, 7501], [-2, 9], [-12, 45]], [[1300, 7594], [-23, -25]], [[1277, 7569], [-28, 12], [-30, 13]], [[1219, 7594], [-39, -4], [-66, -9]], [[881, 7725], [82, 39], [22, 10]], [[985, 7774], [37, 13], [128, 45]], [[1211, 7832], [-3, -18], [-7, -41]], [[1201, 7773], [104, 3], [52, 2]], [[1297, 7850], [-37, 19], [-54, 26]], [[1206, 7895], [-50, 55], [-3, 4]], [[1153, 7954], [-10, 7], [-61, 43]], [[980, 8041], [9, 13], [33, 49]], [[1022, 8103], [119, 4], [12, 0]], [[1340, 8274], [57, 10], [15, 3]], [[1552, 8339], [60, -7], [9, -1]], [[1621, 8331], [17, 10], [97, 53]], [[2697, 8252], [75, -23], [37, -12]], [[2809, 8217], [6, -1], [75, -15]], [[3222, 8105], [34, 26], [31, 23]], [[3287, 8154], [19, 8], [70, 29]], [[3376, 8191], [82, -11], [28, -4]], [[3597, 8228], [14, 3], [108, 26]], [[3825, 8236], [8, 29], [8, 26]], [[4217, 8183], [5, 6], [23, 28]], [[4622, 8118], [65, -13], [36, -7]], [[4796, 8106], [32, -19], [68, -41]], [[4926, 7963], [162, 11], [38, 2]], [[5126, 7976], [43, 14], [20, 7]], [[5273, 8036], [42, 36], [5, 4]], [[6032, 8047], [15, 4], [51, 16]], [[6212, 7916], [15, 30], [32, 64]], [[6319, 8007], [30, 104], [3, 14]], [[6187, 8245], [1, 40], [5, 90]], [[6193, 8375], [37, 36], [50, 49]], [[6378, 8442], [31, -22], [44, -30]], [[6553, 8257], [-15, -14], [-50, -44]], [[5924, 8439], [-184, 91], [2, 37]], [[4005, 8454], [25, 39]], [[4030, 8493], [7, 11]], [[4697, 8693], [79, -50], [-93, -29]], [[4410, 8449], [0, -50], [-195, -58]], [[6582, 9496], [125, -25], [121, -23]], [[9999, 9615], [0, -2539]], [[9999, 9757], [0, -138]], [[4088, 8590], [-58, -97]], [[4939, 8062], [100, 8], [220, 21]], [[0, 8113], [0, -469]], [[0, 8413], [0, -81]], [[9999, 9619], [0, -4]], [[9999, 7076], [0, -7076], [-405, 0]], [[9549, 429], [-18, 65], [-25, -5]], [[6709, 3517], [-14, 33]], [[7210, 3359], [-15, -79]], [[7402, 3162], [-35, 137]], [[7367, 3299], [0, 50]], [[7684, 4349], [-3, 112], [-49, 37]], [[8087, 5042], [9, 20], [43, 77]], [[8579, 5734], [-65, 58], [-111, -12]], [[1900, 8316], [-53, 54], [-112, 24]], [[1219, 7594], [58, -25]], [[2196, 7180], [-78, -36], [-34, -116]], [[3558, 6529], [65, -38]], [[4155, 5355], [-18, -213]], [[5202, 2801], [-30, 54], [-36, 3]], [[6829, 1574], [23, -10]], [[6967, 1282], [21, -17]], [[7389, 0], [-7389, 0], [0, 9999], [9999, 0], [0, -242]], [[6003, 6344], [74, 48], [11, -33], [12, -74], [93, -239], [11, -83], [-36, -28], [-15, 35], [-23, 88], [-71, 81], [-77, 121], [21, 84]], [[4814, 7290], [-59, 67], [-19, 28], [16, -3], [103, -38], [81, -45], [117, 47], [55, 56], [62, 18], [59, -23], [23, -8], [-2, -14], [-73, -21], [-87, -20], [-100, -92], [-75, -57], [-126, -24], [-81, -2], [-120, 53], [-22, 6], [-2, 19], [15, 4], [102, -27], [83, 43], [50, 33]], [[7685, 5174], [5, -46], [-56, -39], [-61, 10], [-58, 4]], [[7515, 5103], [-38, -15], [-23, -6], [-30, 11], [23, 41], [22, 13], [52, 18], [63, 16], [33, -23], [21, 26], [23, 16], [16, -8], [8, -18]], [[7176, 4950], [40, -7], [55, 37], [33, 33], [22, 5], [40, -11], [20, 18], [36, 15], [65, 14]], [[7487, 5054], [1, -11]], [[7488, 5043], [-64, -71]], [[7424, 4972], [-56, -34]], [[7368, 4938], [-38, -15], [-44, -33], [-53, -16], [-37, 6], [-46, 25]], [[7150, 4905], [26, 45]], [[6696, 5648], [30, 46], [42, 19], [16, 28], [17, 16], [69, -24], [51, -3], [17, -16], [25, -66], [83, -18], [-13, -33], [30, -35], [-13, -45], [31, -14], [-15, -40]], [[7066, 5463], [-23, 5], [-16, 33], [-72, -11], [-66, -26], [-52, 45], [-42, 15], [24, 62], [-66, -39], [-59, -38], [-57, -30]], [[6637, 5479], [-46, 40]], [[6591, 5519], [-74, -24]], [[6517, 5495], [0, 17], [50, 50], [53, 47], [76, 39]], [[7354, 3151], [-16, 4], [1, 29], [16, -4], [-1, -29]], [[7047, 1311], [-32, 4], [-37, 39], [-7, 46], [23, 5], [40, -50], [13, -44]], [[5969, 6249], [-10, 0], [-19, 2], [-47, 17], [-13, 29], [21, 25], [-7, 19], [1, 5], [6, 17], [7, 16], [18, -1], [-4, -14], [-10, -20], [23, 0], [-3, -13], [-11, -44], [29, -18], [20, -3], [-1, -17]], [[5023, 4863], [3, -23], [-2, -34], [-38, 32], [-2, 5], [-13, 54], [22, -22], [14, -12], [13, 10], [3, -10]], [[4610, 7790], [-16, -43], [-10, -10], [-119, -4], [-2, -10], [6, -15], [-3, -18], [-122, -58], [1, 14], [29, 45], [-8, 8], [-112, -49], [-49, 18], [3, 16], [64, 56], [-3, 12], [-192, 18], [4, 12], [401, 96], [10, -11], [-61, -48], [190, 11], [0, -17], [-11, -23]], [[5210, 6945], [-106, -56], [-8, 14], [3, 9], [153, 93], [189, -27], [-1, -3], [-230, -30]], [[5785, 6824], [26, -17], [-32, -61], [3, -14], [-94, -104], [-6, 8], [10, 34], [5, 1], [5, -17], [32, 44], [-17, 41], [0, 21], [51, 65], [17, -1]], [[7042, 5387], [13, 15]], [[7055, 5402], [2, 0], [65, 10], [-37, 47]], [[7085, 5459], [14, 18], [15, -26], [55, -15], [57, -1], [60, -12], [66, -23], [24, -37], [48, -90], [-24, -39], [-60, 16], [-37, 73], [9, -74], [-35, -65], [4, -56], [-6, -33], [-48, -39]], [[7227, 5056], [-4, 48], [-13, 68], [-28, 12], [-46, -52], [-14, 1], [-4, 30], [41, 47], [6, 54], [-6, 53], [-55, 46], [-62, 24]], [[7042, 5387], [-11, -45], [-16, -12], [-13, -58], [-7, 39], [-30, -28], [-18, -39], [-20, -58], [-3, -50], [24, -73], [-2, -77], [-30, -58], [-15, -16]], [[6901, 4912], [-20, -13], [-25, -1], [-7, 8]], [[6849, 4906], [-20, 62], [0, 31]], [[6829, 4999], [2, 29], [-10, 57], [14, 67], [17, 82], [39, 91], [-12, -1], [-54, -76], [-10, 14], [29, 43]], [[6844, 5305], [44, 76]], [[6888, 5381], [51, 10], [57, 24], [59, -13]], [[3664, 6447], [41, 56], [-2, 75], [-126, 75], [-76, 134], [-46, 84], [-68, 53], [-50, 48], [-39, 61], [-75, -38], [-72, -66], [-65, 78], [-52, 51], [-72, 33], [-73, 3], [0, 670], [1, 437]], [[6352, 8125], [-79, 72], [-86, 48]], [[8359, 5310], [-48, 66], [0, 161], [-33, 34], [-50, -20], [-24, 31], [-57, -89], [-22, -92], [-27, -53], [-31, -19]], [[8067, 5329], [-24, -5], [-7, -29]], [[8036, 5295], [-137, -1]], [[7899, 5294], [-112, 0], [-34, -22]], [[7753, 5272], [-78, -85], [-9, -9], [-24, -46], [-68, 0], [-73, 0], [-33, -19], [12, -23]], [[7480, 5090], [7, -36]], [[7487, 5054], [-2, -12], [-97, -58], [-76, -19], [-86, -63], [-19, 0], [-25, 19], [-8, 17], [2, 12]], [[7176, 4950], [16, 41], [35, 65]], [[7227, 5056], [21, 69], [-14, 103], [-16, 106], [-77, 56], [9, 21], [-11, 14], [-21, 0], [-14, 19], [-4, 27], [-15, -12]], [[7085, 5459], [-19, 4]], [[7066, 5463], [4, 12]], [[7070, 5475], [-17, 11], [-7, 31], [-58, 38], [-60, 39], [-72, 45], [-70, 43], [-66, -33], [-24, -1]], [[6696, 5648], [-91, 30], [-60, -15], [-72, 36], [-76, 19], [-52, 7], [-23, 20], [-13, 65], [-25, -1], [0, -45], [-153, 0]], [[6131, 5764], [-254, 0], [-252, 0], [-222, 0], [-222, 0], [-219, 0], [-225, 0]], [[4737, 5764], [-73, 0]], [[4664, 5764], [-220, 0], [-210, 0]], [[6108, 3040], [-52, 26], [-58, 36], [-21, 55], [-16, 83], [-44, 67], [-25, 69], [-38, 80], [-52, 47], [-60, -2], [-47, -93], [-61, 35], [-39, 36], [-18, 64], [-25, 62], [-44, 52], [-38, 37], [-27, 41]], [[5443, 3735], [-128, 0], [0, -48]], [[5315, 3687], [-59, 0], [-147, -1]], [[5109, 3686], [-169, 83], [-112, 57], [7, 23]], [[4835, 3849], [-94, -13], [-84, -9]], [[8021, 2319], [6, -64], [-6, -45], [-18, -20], [19, -35], [-1, -32]], [[6792, 2176], [-14, -1], [-27, -71], [-13, 14], [-9, -6], [0, -17]], [[6729, 2095], [-68, 1], [-69, 0], [0, -66], [-34, -1], [28, -39], [27, -27], [8, -25], [12, -8], [-1, -40], [-95, 0], [-36, -96], [10, -22], [-8, -27], [-2, -35]], [[7187, 968], [4, 23], [6, 24], [-3, 21], [11, 14], [-15, 17], [-1, 48], [29, 10]], [[6984, 1304], [11, 16], [49, -32], [17, 16], [23, -10], [12, -24], [22, -8], [18, 25]], [[6865, 1527], [23, 5], [9, 27], [11, 1], [-2, 58], [18, 3], [15, -1], [16, 31], [22, -23], [8, 14], [13, 14], [26, 33], [1, 24], [7, -1], [10, 28], [8, 3], [12, -18], [15, -5], [16, 15], [19, 0], [26, 15], [10, 16], [26, -2]], [[6829, 1574], [6, 48], [-10, 12], [-16, 9], [-32, -14], [-3, 16], [-22, 18], [-16, 24], [-22, 10]], [[6714, 1697], [15, 30], [-6, 23], [6, 22], [35, 33], [33, 45]], [[6659, 1616], [2, 17], [26, 30], [13, 13], [-3, 14], [17, 7]], [[6729, 2095], [0, -93], [-6, -133], [22, 0]], [[6517, 5495], [-19, -7], [0, -66], [-2, 1], [-18, -13], [-16, -12], [-11, -21], [16, -22], [-6, -30], [0, -32], [-2, -26], [21, -22], [9, -1], [24, -17], [9, -8], [5, -13], [18, -20], [25, -17], [3, -10], [0, -28], [2, -14]], [[6575, 5117], [-97, 2], [-108, 0], [-101, -2], [-81, 0]], [[6188, 5117], [1, 110], [-9, 113], [-13, 9], [-7, 18], [4, 16], [16, 13], [2, 17]], [[6182, 5413], [0, 22], [-5, 18], [-6, 19], [-4, 24], [-1, 26], [-2, 7], [-3, 34], [0, 16], [-2, 14], [-4, 24], [-9, 24], [-8, 22], [-1, 21], [-1, 23], [2, 15], [1, 14], [-7, 17], [-1, 11]], [[4737, 5764], [221, 0], [221, 0], [222, 1], [221, 0]], [[5622, 5765], [1, -216], [4, -143]], [[5627, 5406], [-4, -108]], [[5623, 5298], [-123, 2], [-134, -1], [-115, 1], [-146, -1], [1, -59], [-2, -5]], [[5104, 5235], [-8, 7], [-7, 16], [-8, 4], [-11, -24], [-17, -3], [-41, 7], [-2, -12], [-25, 5], [-13, -17], [-13, 31], [-9, 17], [-16, 3], [-4, 8], [-5, 31], [-13, 15], [-8, 37], [-9, 16], [-9, 3], [-8, -16], [-14, -14], [-14, 12], [0, 30], [8, 7], [-6, 31], [7, 30], [8, 27], [-22, 1], [-19, 17], [-21, 36], [-12, 18], [-17, 11], [-14, 19], [0, 21], [-19, 32], [-6, 123]], [[6182, 5413], [-141, -6], [-121, 0], [-151, 0], [-142, -1]], [[5622, 5765], [257, -1], [252, 0]], [[5104, 5235], [3, -2], [0, -292]], [[5107, 4941], [-221, -1]], [[4886, 4940], [-222, 1]], [[4664, 4941], [1, 211], [7, 34], [-6, 15], [-14, 8], [0, 19], [10, 26], [16, 24], [11, 38], [10, 30], [7, 15], [-4, 18], [-12, 10], [-18, 22]], [[4672, 5411], [1, 21], [-7, 18], [-2, 164], [0, 150]], [[4672, 5411], [-152, -1], [-27, -12], [-19, 5], [-14, -9], [-28, -13], [-33, 1], [-17, -9], [-16, -4], [-11, 6], [-27, 5], [-16, -4], [-29, -12], [-18, -1], [-17, 5], [-6, 16], [-2, 20], [-14, 22], [-21, 7], [-18, 11], [-23, 0], [-16, 1]], [[4148, 5445], [-6, 68]], [[4271, 5571], [-2, 13], [-10, 84]], [[5255, 4352], [0, -665]], [[5255, 3687], [-146, -1]], [[4835, 3849], [10, 0], [8, 23], [-13, 16], [-3, 18], [-4, 21], [15, 27], [6, 54], [22, 24], [-14, 22], [-9, 22], [-7, 21], [-4, 16], [-1, 10]], [[4841, 4123], [4, 24], [-3, 23], [-2, 22], [0, 26], [-6, 16], [5, 14], [15, 0], [14, -8], [11, -5], [4, 18], [4, 4], [-1, 95]], [[4886, 4352], [120, 2], [142, -1], [107, -1]], [[4398, 4071], [0, 2], [-9, 63]], [[4389, 4136], [-43, 71], [-29, 47]], [[4255, 4417], [1, 9], [2, 19]], [[4258, 4445], [-32, 37], [-1, 2]], [[4225, 4484], [-24, 41], [-33, 57]], [[4158, 4678], [-15, 24], [-25, 40]], [[4118, 4742], [11, 67], [5, 31]], [[4132, 4941], [312, -1]], [[4444, 4940], [0, -353], [140, -159], [133, -155], [124, -150]], [[5773, 4706], [1, -182], [0, -172]], [[5774, 4352], [-71, 0]], [[5703, 4352], [-89, 0], [-126, 0], [-117, 0], [-116, 0]], [[5255, 4352], [0, 471]], [[5255, 4823], [74, 0], [74, 0], [149, 0], [74, 1]], [[5626, 4824], [148, 0], [-1, -115], [0, -3]], [[4886, 4940], [0, -588]], [[4444, 4940], [220, 0], [0, 1]], [[5703, 4352], [0, -58]], [[5703, 4294], [0, -309], [0, -221], [-69, 0], [-133, 0], [-67, 0], [1, -10], [8, -19]], [[5315, 3687], [-60, 0]], [[4155, 5355], [-7, 90]], [[5107, 4941], [0, -115], [148, -3]], [[5623, 5298], [2, -240]], [[5625, 5058], [1, -234]], [[6691, 4238], [-1, -10]], [[6580, 3883], [-62, 4], [-81, -4], [-72, 0]], [[6365, 3883], [5, 67], [-18, 1], [-14, -2], [-4, 8]], [[6334, 3957], [2, 103], [2, 114], [-15, 124]], [[6323, 4298], [90, -1], [82, -1], [78, 0], [85, -7], [6, -14], [-9, -13], [-8, -13], [-4, -11], [48, 0]], [[6575, 5117], [1, -6], [10, -19], [-7, -9], [0, -24], [3, -11], [5, -19], [24, -11], [8, -18]], [[6619, 5000], [4, -9], [9, -6], [3, -13], [12, -9], [7, -10], [-4, -32], [-13, -26], [-5, -9], [-17, -7], [-26, -5], [-6, -21], [9, -9], [3, -18], [-10, -20], [-4, -18], [-19, -18], [-2, -21]], [[6560, 4749], [-10, 10], [-14, 19], [-81, -3], [-85, -1], [-67, 0], [-66, 0]], [[6188, 4998], [0, 11], [-12, 13], [6, 19], [4, 19], [2, 13], [-10, 16], [0, 28], [10, 0]], [[5773, 4706], [1, 0], [129, 0], [96, 0], [171, 0], [102, 0]], [[6325, 4604], [-1, -126], [0, -126]], [[6324, 4352], [-65, 0], [-134, 0], [-133, 0], [-74, 0], [-74, 0], [-70, 0]], [[6323, 4298], [1, 54]], [[6560, 4749], [-6, -29], [6, -36], [12, -24], [15, -20], [17, -16], [7, -5], [6, -22], [1, -20], [8, -5], [14, 8], [14, -19], [-4, -22]], [[5625, 5058], [145, 0], [111, 0], [148, 0]], [[6166, 5015], [3, -10], [6, -5]], [[6334, 3957], [-32, 23], [-20, 12], [-17, -8], [-26, 2], [-16, -1], [-12, -9], [-13, -5], [-11, 6], [-24, -7], [-12, 20], [-11, -17], [-20, 8], [-21, 18], [-22, -12], [-10, 28], [-35, -2], [-22, 6], [-24, 8], [-11, 25], [-20, -8], [-12, 9], [-18, 13], [0, 112], [0, 116], [-74, 0], [-74, 0], [-74, 0]], [[6549, 3652], [65, -3], [70, -1], [-2, -19], [-5, -20], [5, -14], [9, -14], [3, -20], [1, -11]], [[6695, 3550], [1, -2]], [[6381, 3495], [-5, 13], [7, 17], [11, 16], [1, 23], [-6, 8], [7, 28], [4, 13], [8, 43], [-7, 16], [-9, 27], [-6, 27], [-4, 18], [-12, 14], [-5, 125]], [[6178, 3330], [-39, -54], [-2, -2]], [[6120, 3221], [0, -40], [-1, -41]], [[6123, 3083], [8, -24], [6, -16]], [[7888, 4947], [57, -2], [69, -3]], [[8014, 4942], [1, -64], [-5, -17]], [[7934, 4849], [-14, -8], [-43, -23]], [[7877, 4818], [-1, 3], [-2, 15], [16, 11], [-6, 10], [4, 90]], [[7888, 4947], [16, 81]], [[7904, 5028], [61, -2]], [[7965, 5026], [90, -1], [7, 12], [16, 8], [9, -3]], [[8141, 4915], [-2, 15], [-5, 28]], [[8156, 4932], [-4, -21], [-2, -13]], [[8150, 4898], [-38, -15], [-12, -4]], [[8064, 4881], [-2, 18], [-11, 14], [-6, 31], [-31, -2]], [[7965, 5026], [-6, 12], [6, 16], [2, 31], [2, 7], [3, 28], [8, 23], [6, 10], [9, 28], [2, 19], [2, 12], [14, 5], [17, 14], [3, 15], [-6, 17], [9, 32]], [[8067, 5329], [10, -216], [-2, -11], [13, -18], [4, -16], [8, 1]], [[8064, 4881], [-6, -2], [-48, -18]], [[7904, 5028], [3, 97], [-10, 1], [-2, 4], [5, 18], [-7, 30], [8, 25], [-4, 18], [-2, 34], [3, 16], [1, 23]], [[6802, 4117], [93, 0], [95, -1]], [[6990, 4116], [19, -146], [18, -117], [10, -37], [7, -21], [-12, -21], [-5, -37], [4, -22], [-1, -21], [-2, -20], [4, -15], [4, -13]], [[7036, 3646], [-151, -1], [-43, -7], [-1, -9], [17, -28], [-4, -24], [-5, -16]], [[6783, 3574], [-2, 180], [13, 188], [13, 152], [-5, 23]], [[7036, 3646], [11, -32], [75, -6], [120, -17], [5, -21], [10, 11], [0, 41], [9, 4], [15, -9], [15, -2]], [[7320, 2965], [-5, 22], [-7, 29]], [[7280, 3043], [-18, 47], [-21, 54]], [[6933, 3576], [-48, -8], [-36, -7]], [[6990, 4116], [57, -1], [40, 1]], [[7087, 4116], [92, -1]], [[7179, 4115], [-8, -10], [-12, -22], [20, -20], [13, -7], [13, -37], [9, -21], [26, -28], [5, -15], [18, -19], [8, -28], [24, -23], [5, -27], [5, -13], [-3, -9], [14, -13], [7, -22], [0, -22], [7, -5], [13, -6]], [[6648, 4120], [73, 0], [81, -3]], [[7179, 4115], [7, 3], [40, 20], [69, -1], [34, -5], [1, -10], [7, 7], [12, -20], [0, -13], [82, -1], [83, -112]], [[6619, 5000], [75, 0], [77, 0], [57, -1], [1, 0]], [[6849, 4906], [0, -136], [0, -136], [-8, -33], [6, -8], [4, -21], [-1, -15], [-6, -8], [-6, -19], [-15, -26], [-10, -31], [-3, -24]], [[6810, 4449], [1, -9], [-9, -16], [7, -11], [-13, -9], [-17, -10], [3, -25], [-10, -10], [-18, 11], [-19, 6], [-5, -11], [2, -18]], [[6901, 4912], [80, -1], [70, 1], [0, -9]], [[7051, 4903], [-1, -107], [0, -114], [-1, -82]], [[7049, 4600], [-4, -5], [6, -24], [-3, -9], [-13, 0], [-11, -11], [-18, 5], [-1, -23], [-11, -9], [-9, -20], [-11, -3], [-16, -35], [-15, 10], [-5, 14], [-13, -23], [-8, -13], [-16, 13], [-17, -11], [-6, -11], [-22, 18], [-16, -13], [-19, 9], [-1, -13], [-10, 3]], [[7049, 4600], [26, -2], [13, -12], [20, -26], [15, -9], [12, -9], [17, 3], [13, -7], [16, 7], [14, 2], [6, -17], [14, -11]], [[7215, 4519], [1, -11], [0, -25], [8, -18], [4, -18], [11, -15], [7, -14], [15, -2]], [[7261, 4416], [-8, -9], [-22, -26], [-23, -14], [-2, -10], [-8, -12], [-19, -13], [-1, -1], [-1, -1], [-6, -11], [-11, -5]], [[7160, 4314], [-4, -2], [-21, -7]], [[7135, 4305], [-51, -3], [-65, 5], [-21, -2], [-43, 3], [-43, 1], [-40, 1], [-46, -3], [-3, 5], [-14, 0], [0, -19], [-106, 1]], [[7087, 4116], [1, 25], [16, 7], [5, 13], [11, 14], [15, 3], [17, 5], [17, 11], [7, 10], [14, 9], [0, 9], [19, 16], [6, -11], [27, 23], [13, -3], [11, 20], [15, 5], [-1, 17], [2, 15]], [[7282, 4304], [-29, -2]], [[7282, 4304], [125, -5], [148, -1], [78, 1], [70, 1], [10, 0]], [[7713, 4300], [2, -28], [8, -90]], [[7723, 4182], [-27, -51], [-20, -36]], [[7051, 4903], [38, 1], [33, 0], [28, 1]], [[7368, 4938], [0, -157]], [[7368, 4781], [-10, -6], [3, -15], [-3, -27], [-8, -31], [-7, -25], [-1, -12], [-21, -27], [-8, -6], [-10, -3], [-9, 2], [-17, -20], [-3, -21], [-2, -11], [-6, -5], [-1, 13], [-10, 3], [-11, -26], [-1, -26], [-10, -16], [-18, -3]], [[7135, 4305], [110, -4], [8, 1]], [[7261, 4416], [3, -20], [6, -9], [2, -2], [11, -10], [17, 11], [7, 4], [9, -9], [23, 9], [4, 1], [1, 7], [1, 4], [8, -4], [9, 8], [1, 0], [10, -2], [12, 10], [0, 6], [1, 4], [-1, 14], [10, 21], [14, 15], [4, 17], [7, 12], [6, 9], [7, 26], [10, -9], [11, -9], [10, 5], [4, 11], [7, 14], [5, 11], [3, 6], [6, -5], [11, 15], [8, 9], [6, 6], [3, 4], [6, 8], [5, 25], [1, 6], [35, -29], [3, -2], [8, 22]], [[7575, 4626], [2, -1], [9, -3], [10, -9], [-4, -10], [-1, -3], [15, -7], [14, -13]], [[7620, 4580], [6, -10], [0, -7]], [[7626, 4563], [-2, -10], [-2, -2], [-10, -9], [-9, -26], [10, -6], [12, 5], [4, -13], [1, -4]], [[7630, 4498], [50, -37], [1, -1]], [[7701, 4383], [22, 80], [1, 0]], [[7724, 4463], [8, 7], [17, 2]], [[6591, 5519], [45, -39]], [[6636, 5480], [1, -1]], [[6637, 5479], [4, 2], [12, -4], [6, -21], [65, -21], [42, -21], [21, 0], [15, -2], [4, -19], [17, -8], [7, -16], [-5, -10], [-3, -19], [16, -1], [-5, -19], [10, -14], [0, 1], [2, -1]], [[6845, 5306], [-1, -1]], [[7368, 4781], [0, -108], [77, 0]], [[7445, 4673], [0, -60], [11, 10], [13, 14], [14, 5], [10, 12], [22, -5], [8, 9], [15, 9], [22, -9], [9, -17], [6, -15]], [[7773, 4523], [-49, 0], [-5, 150]], [[7719, 4673], [5, 9], [7, 5], [16, -6]], [[7747, 4681], [-11, -12], [2, -23]], [[7772, 4562], [1, -39]], [[7620, 4580], [6, 6], [9, -13], [-9, -10]], [[7445, 4673], [69, -1], [24, 1], [52, 0], [63, 0], [66, 0]], [[7773, 4523], [0, -5]], [[7747, 4681], [15, 11], [5, 7], [18, 16], [10, 13], [-24, 30], [-1, 13], [-8, 3], [0, 19], [9, 15], [-4, 15], [12, 10], [13, 27], [9, 5]], [[7801, 4865], [57, -47], [-3, -24]], [[7838, 4671], [-11, -18], [-43, -72]], [[7778, 4611], [-3, 1], [-13, 5]], [[7762, 4617], [-11, 14], [-13, 15]], [[7877, 4818], [-4, -3]], [[8003, 4815], [-73, -25], [-30, -10]], [[7900, 4780], [-1, 0], [-47, -1]], [[7801, 4865], [-12, 8], [-13, 8], [-5, 17], [2, 13], [-8, 11], [-17, 19], [-100, 0], [-108, 0], [-116, 0], [0, 32]], [[7424, 4973], [64, 70]], [[7480, 5090], [35, 13]], [[7685, 5174], [-7, 16], [75, 82]], [[7424, 4972], [0, 1]], [[6888, 5381], [-43, -75]]], "transform": {"scale": [0.013501350135013503, 0.008500850085008501], "translate": [-180, 0]}, "bbox": [-180, 0, -45, 85]}