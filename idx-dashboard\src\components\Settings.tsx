import React, { useState, useEffect } from 'react';

const Settings: React.FC = () => {
  const [theme, setTheme] = useState('drdo-formal');

  useEffect(() => {
    document.body.className = `bg-${theme === 'drdo-formal' ? 'drdo-white' : theme}-theme text-drdo-navy`;
  }, [theme]);

  const handleThemeChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setTheme(e.target.value);
  };

  return (
    <div className="space-y-6">
      <h2 className="text-3xl font-semibold">Configuration</h2>
      <div className="bg-white p-6 rounded-lg shadow-lg">
        <label className="block text-lg mb-2">Theme</label>
        <select
          value={theme}
          onChange={handleThemeChange}
          className="p-2 border rounded-lg bg-white text-drdo-navy w-full"
        >
          <option value="drdo-formal">DRDO Formal</option>
          <option value="light">Light</option>
          <option value="dark">Dark</option>
        </select>
      </div>
    </div>
  );
};

export default Settings;
