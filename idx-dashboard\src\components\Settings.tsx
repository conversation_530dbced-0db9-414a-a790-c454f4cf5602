import React, { useState, useEffect } from 'react';

const Settings: React.FC = () => {
  const [theme, setTheme] = useState('drdo-formal');

  useEffect(() => {
    document.body.className = `bg-${theme === 'drdo-formal' ? 'drdo-white' : theme}-theme text-drdo-navy`;
  }, [theme]);

  const handleThemeChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setTheme(e.target.value);
  };

  return (
    <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-foreground">System Configuration</h1>
            <p className="text-muted-foreground">Dashboard settings and preferences</p>
          </div>
          <div className="bg-accent text-accent-foreground px-3 py-1 rounded text-sm font-medium">
            ADMIN ACCESS
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <div className="bg-card p-6 rounded-lg border border-border">
            <h3 className="text-lg font-semibold text-card-foreground mb-4 flex items-center gap-2">
              🎨 Theme Configuration
            </h3>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium mb-2">Dashboard Theme</label>
                <select
                  value={theme}
                  onChange={handleThemeChange}
                  className="w-full p-3 border border-border rounded-lg bg-background text-foreground focus:ring-2 focus:ring-primary focus:border-transparent"
                >
                  <option value="drdo-formal">DRDO Formal (Default)</option>
                  <option value="light">Light Mode</option>
                  <option value="dark">Dark Mode</option>
                  <option value="tactical">Tactical Mode</option>
                </select>
              </div>
              <div className="p-4 bg-muted rounded-lg">
                <div className="text-sm text-muted-foreground">
                  Current theme: <span className="font-medium text-foreground">{theme}</span>
                </div>
                <div className="text-xs text-muted-foreground mt-1">
                  Changes apply immediately across all modules
                </div>
              </div>
            </div>
          </div>

          <div className="bg-card p-6 rounded-lg border border-border">
            <h3 className="text-lg font-semibold text-card-foreground mb-4 flex items-center gap-2">
              🔒 Security Settings
            </h3>
            <div className="space-y-4">
              <div className="p-4 bg-success/10 rounded-lg border border-success/20">
                <div className="flex items-center gap-2 mb-2">
                  <div className="w-3 h-3 bg-success rounded-full"></div>
                  <span className="font-medium text-success">AES-256 Encryption</span>
                </div>
                <div className="text-sm text-muted-foreground">All data transmissions encrypted</div>
              </div>
              <div className="p-4 bg-primary/10 rounded-lg border border-primary/20">
                <div className="flex items-center gap-2 mb-2">
                  <div className="w-3 h-3 bg-primary rounded-full"></div>
                  <span className="font-medium text-primary">Session Timeout</span>
                </div>
                <div className="text-sm text-muted-foreground">15 minutes idle timeout</div>
              </div>
              <div className="p-4 bg-warning/10 rounded-lg border border-warning/20">
                <div className="flex items-center gap-2 mb-2">
                  <div className="w-3 h-3 bg-warning rounded-full"></div>
                  <span className="font-medium text-warning">Audit Logging</span>
                </div>
                <div className="text-sm text-muted-foreground">All actions logged and monitored</div>
              </div>
            </div>
          </div>
        </div>
    </div>
  );
};

export default Settings;
